{**
 * Template rendering mail messages sent via <PERSON><PERSON>ail<PERSON>
 *
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * @licence New BSD
 *}
<style>
	.nette-panel[id~=MailPanel],
	.tracy-panel[id~=MailPanel]{
		text-align: left;
		overflow: auto;
		max-height: 600px;
	}
	.mail-panel-container {
		margin-bottom: 10px !important;
		width: 540px;
	}
	.mail-panel-body {
		border: 1px solid #A7A7A7 !important;
		padding: 10px !important;
		background: #FFF !important;
		width: 96%;
		height: auto;
	}
	.mail-panel-container a.delete-link {
		float: right;
	}
	a.mail-panel-delete-all-link {
		float: right;
		margin: 15px 5px 0 0 !important;
	}
	.mail-panel-container a.visibility-toggle-link {
		float: right;
		padding-left: 5px;
		padding-right: 5px;
		margin: 0px 20px 0 20px !important;
	}
</style>

<a n:if="count($messages)" href="{$baseUrl}?mail-panel=delete" class="mail-panel-delete-all-link">Delete all</a>
<h1>Sent emails</h1>
{var $allMessages = count($messages)}
{if count($messages) > 0}
	{var $hash = Nette\Utils\Strings::random()}
	{* Get newest at top *}
	{foreach $messages as $index => $mail}
		{* The message itself *}
		<table class="mail-panel-container">
			<tr>
				<th style="width: 30%"><strong>Subject</strong></th>
				<td>
					{$mail->getSubject()}
					<a class="delete-link" href="{$baseUrl}?mail-panel={$index}">Delete</a>
					<a class="visibility-toggle-link" href="#" id="mail-panel-mail-{$index}">Show</a>
				</td>
			</tr>
			{var headers => $mail->getHeaders()}
			<tbody class="mail-panel-collapsable" id="mail-panel-mail-{$index}-body">
				<tr>
					<th>From</th>
					<td>
						{if isset($headers["From"])}
							{foreach $headers["From"] as $key => $value}
								<a href="mailto:{$key}">{if isset($value)}{$value}{else}{$key}{/if}</a>{sep}, {/sep}
							{/foreach}
						{/if}
					</td>
				</tr>
				<tr>
					<th>To</th>
					<td>
						{if isset($headers["To"])}
							{foreach $headers["To"] as $key => $value}
								<a href="mailto:{$key}">{if isset($value)}{$value}{else}{$key}{/if}</a>{sep}, {/sep}
							{/foreach}
						{/if}
					</td>
				</tr>
				{if isset($headers["Cc"])}
					<tr>
						<th>CC</th>
						<td>
							{foreach $headers["Cc"] as $key => $value}
								<a href="mailto:{$key}">{if isset($value)}{$value}{else}{$key}{/if}</a>{sep}, {/sep}
							{/foreach}
						</td>
					</tr>
				{/if}
				{if isset($headers["Bcc"])}
					<tr>
						<th>BCC</th>
						<td>
							{foreach $headers["Bcc"] as $key => $value}
								<a href="mailto:{$key}">{if isset($value)}{$value}{else}{$key}{/if}</a>{sep}, {/sep}
							{/foreach}
						</td>
					</tr>
				{/if}
				{* Little magic here. Create iframe and then render message into it (needed because HTML messages) *}
				{var $mailContent = $mail->getHtmlBody()}
				{if $mailContent == NULL}
					{var $mailContent = '<pre style="font-family:serif">' . $mail->getBody() . '</pre>'}
					<tr><th colspan="2">Plaintext e-mail</th></tr>
				{else}
					<tr><th colspan="2">HTML e-mail</th></tr>
				{/if}
				<tr>
					<td colspan="2" class="mail-panel-body-outter">
						<iframe class="mail-panel-body" id="mail-body-{$hash}-{$index}"></iframe>
						<script type="text/javascript">
							var mailContent = '<base target="_parent" />' + {$mailContent};
							document.getElementById('mail-body-' + {$hash} + '-{$index}').contentWindow.document.write(mailContent);
						</script>
					</td>
				</tr>
			</tbody>
		</table>
	{/foreach}
{else}
	<p>No emails.</p>
{/if}

<script type="text/javascript">
	function mailPanelToggleVisibility(element) {
		if(typeof element.id == "undefined") return;
		var object = document.getElementById(element.id + '-body');
		if(element.innerHTML == "Hide") {
			object.style.display = "none";
			element.innerHTML = "Show";
		} else {
			object.style.display = "";
			element.innerHTML = "Hide";
		}
	}

	function mailPanelInit() {
		// Append toogle to hide/show links
		var elements = document.getElementsByClassName('visibility-toggle-link');
		var elements = [].slice.call(elements);
		elements.forEach(function(item) {
			item.onclick = function(arg) {
				return function() {
					mailPanelToggleVisibility(arg);
				}
			}(item);
		});
		// Collapse all (except last one) by default
		var elements = document.getElementsByClassName('mail-panel-collapsable');
		for(var i = 0, len = elements.length; i < len; i++) {
			if(i == 0) {
				document.getElementById('mail-panel-mail-0').innerHTML = "Hide";
				continue;
			}
			elements[i].style.display = "none";
		}
	}
	mailPanelInit();
</script>
