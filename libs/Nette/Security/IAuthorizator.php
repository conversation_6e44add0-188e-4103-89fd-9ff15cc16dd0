<?php

/**
 * This file is part of the Nette Framework (http://nette.org)
 * Copyright (c) 2004 <PERSON> (http://davidgrudl.com)
 */

namespace Nette\Security;

use Nette;


/**
 * Authorizator checks if a given role has authorization
 * to access a given resource.
 *
 * <AUTHOR>
 */
interface IAuthorizator
{
	/** Set type: all */
	const ALL = NULL;

	/** Permission type: allow */
	const ALLOW = TRUE;

	/** Permission type: deny */
	const DENY = FALSE;


	/**
	 * Performs a role-based authorization.
	 * @param  string  role
	 * @param  string  resource
	 * @param  string  privilege
	 * @return bool
	 */
	function isAllowed($role, $resource, $privilege);

}
