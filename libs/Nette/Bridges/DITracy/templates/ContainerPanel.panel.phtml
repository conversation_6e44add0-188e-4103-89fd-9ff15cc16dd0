<?php

namespace Nette\Bridges\DITracy;

use <PERSON><PERSON>,
	<PERSON>\Dumper;

?>
<style class="tracy-debug">#tracy-debug .nette-ContainerPanel .tracy-inner{width:700px}#tracy-debug .nette-ContainerPanel table{width:100%;white-space:nowrap}#tracy-debug .nette-ContainerPanel .created{font-weight:bold}#tracy-debug .nette-ContainerPanel .yes{color:green;font-weight:bold}</style>

<div class="nette-ContainerPanel">
<h1><?php echo get_class($container) ?></h1>

<div class="tracy-inner">
	<h2>Services</h2>

	<table>
		<thead>
		<tr>
			<th>Name</th>
			<th>Autowired</th>
			<th>Service</th>
			<th>Tags</th>
		</tr>
		</thead>
		<tbody>
		<?php foreach ($services as $name => $class): ?>
		<?php $autowired = in_array($name, $container->findByType($class)); ?>
		<tr>
			<td class="<?php echo isset($registry[$name]) ? 'created' : '' ?>"><?php echo htmlSpecialChars($name) ?></td>
			<td class="<?php echo $autowired ? 'yes' : '' ?>"><?php echo $autowired ? 'yes' : 'no' ?></td>
			<td>
				<?php if (isset($registry[$name]) && !$registry[$name] instanceof Nette\DI\Container): ?>
					<?php echo Dumper::toHtml($registry[$name], array(Dumper::COLLAPSE => TRUE)); ?>
				<?php elseif (isset($registry[$name])): ?>
					<code><?php echo get_class($registry[$name]) ?></code>
				<?php elseif (is_string($class)): ?>
					<code><?php echo htmlSpecialChars($class) ?></code>
				<?php endif ?>
			</td>
			<td><?php if (isset($tags[$name])) { echo Dumper::toHtml($tags[$name], array(Dumper::COLLAPSE => TRUE)); } ?></td>
		</tr>
		<?php endforeach ?>
		</tbody>
	</table>

	<h2>Parameters</h2>

	<div class="nette-ContainerPanel-parameters">
		<?php echo Dumper::toHtml($container->parameters); ?>
	</div>
</div>
</div>
