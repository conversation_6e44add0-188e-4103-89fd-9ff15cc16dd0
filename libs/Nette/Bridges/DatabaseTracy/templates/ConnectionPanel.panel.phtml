<?php

namespace Nette\Bridges\DatabaseTracy;

use <PERSON><PERSON>,
	<PERSON>,
	<PERSON>te\Database\Helpers;

$uniq = uniqid();
static $counter;

?>
<style class="tracy-debug">#tracy-debug td.nette-DbConnectionPanel-sql{background:white!important}#tracy-debug .nette-DbConnectionPanel-source{color:#BBB!important}</style>

<h1 title="<?php echo htmlSpecialChars($connection->getDsn(), ENT_QUOTES, 'UTF-8') ?>">Queries: <?php
	echo $count, ($totalTime ? sprintf(', time: %0.3f ms', $totalTime * 1000) : ''), ', ', htmlSpecialChars($name, ENT_NOQUOTES, 'UTF-8') ?></h1>

<div class="tracy-inner nette-DbConnectionPanel">
	<table>
		<tr><th>Time&nbsp;ms</th><th>SQL Query</th><th>Rows</th></tr>
		<?php
		foreach ($queries as $query):
			list($connection, $sql, $params, $source, $time, $rows, $error, $explain) = $query;
		?>
		<tr>
		<td>
		<?php if ($error): ?>
			<span title="<?php echo htmlSpecialChars($error, ENT_IGNORE | ENT_QUOTES, 'UTF-8') ?>">ERROR</span>
		<?php elseif ($time !== NULL): echo sprintf('%0.3f', $time * 1000); endif ?>
		<?php if ($explain): ?>
			<br /><a class="tracy-toggle tracy-collapsed" href="#nette-DbConnectionPanel-row-<?php echo $uniq, ++$counter ?>">explain</a>
		<?php endif ?>
        </td>
        <td class="nette-DbConnectionPanel-sql"><?php echo Helpers::dumpSql($sql, $params, $connection) ?>
		<?php if ($explain): ?>
			<table id="nette-DbConnectionPanel-row-<?php echo $uniq, $counter ?>" class="tracy-collapsed">
			<tr>
			<?php foreach ($explain[0] as $col => $foo): ?>
				<th><?php echo htmlSpecialChars($col, ENT_NOQUOTES, 'UTF-8') ?></th>
			<?php endforeach ?>
			</tr>
			<?php foreach ($explain as $row): ?>
			<tr>
			<?php foreach ($row as $col): ?>
				<td><?php echo htmlSpecialChars($col, ENT_NOQUOTES, 'UTF-8') ?></td>
			<?php endforeach ?>
			</tr>
			<?php endforeach ?>
			</table>
		<?php endif ?>
		<?php if ($source) echo substr_replace(Tracy\Helpers::editorLink($source[0], $source[1]), ' class="nette-DbConnectionPanel-source"', 2, 0); ?>
		</td>
		<td><?php echo $rows ?></td>
		</tr>
		<?php endforeach ?>
	</table>
	<?php if (count($queries) < $count): ?><p>...and more</p><?php endif ?>
</div>
