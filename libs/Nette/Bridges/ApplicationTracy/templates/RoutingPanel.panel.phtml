<?php

namespace Nette\Bridges\ApplicationTracy;

use Nette;
use Nette\Application\UI\Presenter;
use <PERSON>;
use <PERSON>\Dumper;

?>
<style class="tracy-debug">#tracy-debug .nette-RoutingPanel table{font:9pt/1.5 Consolas,monospace}#tracy-debug .nette-RoutingPanel .yes td{color:green}#tracy-debug .nette-RoutingPanel .may td{color:#67F}#tracy-debug .nette-RoutingPanel pre,#tracy-debug .nette-RoutingPanel code{display:inline}</style>

<div class="nette-RoutingPanel">
<h1>
<?php if (empty($request)): ?>
	no route
<?php else: ?>
	<?php echo htmlSpecialChars($request->getPresenterName() . ':' . (isset($request->parameters[Presenter::ACTION_KEY]) ? $request->parameters[Presenter::ACTION_KEY] : Presenter::DEFAULT_ACTION) . (isset($request->parameters[Presenter::SIGNAL_KEY]) ? " {$request->parameters[Presenter::SIGNAL_KEY]}!" : ''), ENT_NOQUOTES, 'UTF-8') ?>
<?php endif ?>
</h1>

<div class="tracy-inner">
	<p><code><?php echo htmlSpecialChars($url, ENT_IGNORE, 'UTF-8') ?></code></p>

<?php if (empty($routers)): ?>
	<p>No routers defined.</p>

<?php else: ?>
	<table>
	<thead>
	<tr>
		<th>Matched?</th>
		<th>Class</th>
		<th>Mask</th>
		<th>Defaults</th>
		<th>Module</th>
		<th>Request</th>
	</tr>
	</thead>

	<tbody>
	<?php foreach ($routers as $router): ?>
	<tr class="<?php echo $router['matched'] ?>">
		<td><?php echo $router['matched'] ?></td>

		<td><code title="<?php echo htmlSpecialChars($router['class'], ENT_QUOTES, 'UTF-8') ?>"><?php echo preg_replace('#.+\\\\#', '', htmlSpecialChars($router['class'], ENT_NOQUOTES, 'UTF-8')) ?></code></td>

		<td><code><strong><?php echo htmlSpecialChars($router['mask'], ENT_NOQUOTES, 'UTF-8') ?></strong></code></td>

		<td><code>
		<?php foreach ($router['defaults'] as $key => $value): ?>
			<?php echo htmlSpecialChars($key, ENT_IGNORE, 'UTF-8'), "&nbsp;=&nbsp;", is_string($value) ? htmlSpecialChars($value) . '<br />' : Dumper::toHtml($value, array(Dumper::COLLAPSE => TRUE)) ?>
		<?php endforeach ?>
		</code></td>

		<td><code><?php echo htmlSpecialChars($router['module'], ENT_NOQUOTES, 'UTF-8') ?></code></td>

		<td><?php if ($router['request']): ?><code>
		<?php $params = $router['request']->getParameters(); ?>
		<strong><?php echo htmlSpecialChars($router['request']->getPresenterName() . ':' . (isset($params[Presenter::ACTION_KEY]) ? $params[Presenter::ACTION_KEY] : Presenter::DEFAULT_ACTION), ENT_NOQUOTES, 'UTF-8') ?></strong><br />
		<?php unset($params[Presenter::ACTION_KEY]) ?>
		<?php foreach ($params as $key => $value): ?>
			<?php echo htmlSpecialChars($key, ENT_IGNORE, 'UTF-8'), "&nbsp;=&nbsp;", is_string($value) ? htmlSpecialChars($value) . '<br />' : Dumper::toHtml($value, array(Dumper::COLLAPSE => TRUE)) ?>
		<?php endforeach ?>
		</code><?php endif ?></td>
	</tr>
	<?php endforeach ?>
	</tbody>
	</table>
<?php endif ?>

	<?php if ($source): ?><p><?php echo $source ?> in <?php echo Tracy\Helpers::editorLink($source->getFileName(), $source->getStartLine()) ?></p><?php endif ?>
</div>
</div>
