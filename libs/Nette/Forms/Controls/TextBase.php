<?php

/**
 * This file is part of the Nette Framework (https://nette.org)
 * Copyright (c) 2004 <PERSON> (https://davidgrudl.com)
 */

namespace Nette\Forms\Controls;

use Nette;
use Nette\Forms\Form;
use Nette\Utils\Strings;
use Nette\Utils\Validators;


/**
 * Implements the basic functionality common to text input controls.
 *
 * <AUTHOR>
 *
 * @property   string $emptyValue
 */
abstract class TextBase extends BaseControl
{
	/** @var string */
	protected $emptyValue = '';

	/** @var array */
	protected $filters = array();

	/** @var mixed unfiltered submitted value */
	protected $rawValue = '';


	/**
	 * Sets control's value.
	 * @param  string
	 * @return self
	 */
	public function setValue($value)
	{
		if ($value === NULL) {
			$value = '';
		} elseif (!is_scalar($value) && !method_exists($value, '__toString')) {
			throw new Nette\InvalidArgumentException(sprintf("Value must be scalar or NULL, %s given in field '%s'.", gettype($value), $this->name));
		}
		$this->rawValue = $this->value = $value;
		return $this;
	}


	/**
	 * Returns control's value.
	 * @return string
	 */
	public function getValue()
	{
		$value = $this->value;
		if (!empty($this->control->maxlength)) {
			$value = Strings::substring($value, 0, $this->control->maxlength);
		}
		foreach ($this->filters as $filter) {
			$value = (string) call_user_func($filter, $value);
		}
		return $value === Strings::trim($this->translate($this->emptyValue)) ? '' : $value;
	}


	/**
	 * Sets the special value which is treated as empty string.
	 * @param  string
	 * @return self
	 */
	public function setEmptyValue($value)
	{
		$this->emptyValue = (string) $value;
		return $this;
	}


	/**
	 * Returns the special value which is treated as empty string.
	 * @return string
	 */
	public function getEmptyValue()
	{
		return $this->emptyValue;
	}


	/**
	 * Sets the maximum number of allowed characters.
	 * @param  int
	 * @return self
	 */
	public function setMaxLength($length)
	{
		$this->control->maxlength = $length;
		return $this;
	}


	/**
	 * Appends input string filter callback.
	 * @param  callable
	 * @return self
	 * @deprecated
	 */
	public function addFilter($filter)
	{
		$this->filters[] = Nette\Utils\Callback::check($filter);
		return $this;
	}


	public function getControl()
	{
		$el = parent::getControl();
		if ($this->emptyValue !== '') {
			$el->attrs['data-nette-empty-value'] = Strings::trim($this->translate($this->emptyValue));
		}
		if (isset($el->placeholder)) {
			$el->placeholder = $this->translate($el->placeholder);
		}
		return $el;
	}


	public function addRule($validator, $message = NULL, $arg = NULL)
	{
		if ($validator === Form::LENGTH || $validator === Form::MAX_LENGTH) {
			$tmp = is_array($arg) ? $arg[1] : $arg;
			if (is_scalar($tmp)) {
				$this->control->maxlength = isset($this->control->maxlength) ? min($this->control->maxlength, $tmp) : $tmp;
			}
		}
		return parent::addRule($validator, $message, $arg);
	}


	/********************* validators ****************d*g**/


	/**
	 * Is control's value valid email address?
	 * @return bool
	 * @internal
	 */
	public static function validateEmail(TextBase $control)
	{
		return Validators::isEmail($control->getValue());
	}


	/**
	 * Is control's value valid URL?
	 * @return bool
	 * @internal
	 */
	public static function validateUrl(TextBase $control)
	{
		if (Validators::isUrl($value = $control->getValue())) {
			return TRUE;

		} elseif (Validators::isUrl($value = "http://$value")) {
			$control->setValue($value);
			return TRUE;
		}
		return FALSE;
	}


	/** @deprecated */
	public static function validateRegexp(TextBase $control, $regexp)
	{
		trigger_error('Validator REGEXP is deprecated; use PATTERN instead (which is matched against the entire value and is case sensitive).', E_USER_DEPRECATED);
		return (bool) Strings::match($control->getValue(), $regexp);
	}


	/**
	 * Matches control's value regular expression?
	 * @return bool
	 * @internal
	 */
	public static function validatePattern(TextBase $control, $pattern)
	{
		return (bool) Strings::match($control->getValue(), "\x01^($pattern)\\z\x01u");
	}


	/**
	 * Is a control's value decimal number?
	 * @return bool
	 * @internal
	 */
	public static function validateInteger(TextBase $control)
	{
		if (Validators::isNumericInt($value = $control->getValue())) {
			if (!is_float($tmp = $value * 1)) { // bigint leave as string
				$control->setValue($tmp);
			}
			return TRUE;
		}
		return FALSE;
	}


	/**
	 * Is a control's value float number?
	 * @return bool
	 * @internal
	 */
	public static function validateFloat(TextBase $control)
	{
		$value = self::filterFloat($control->getValue());
		if (Validators::isNumeric($value)) {
			$control->setValue((float) $value);
			return TRUE;
		}
		return FALSE;
	}


	/**
	 * Float string cleanup.
	 * @param  string
	 * @return string
	 * @internal
	 */
	public static function filterFloat($s)
	{
		return str_replace(array(' ', ','), array('', '.'), $s);
	}

}
