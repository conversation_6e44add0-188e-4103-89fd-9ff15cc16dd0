<?php

/**
 * Debug Bar: panel "error" template.
 *
 * This file is part of the Tracy (https://tracy.nette.org)
 * Copyright (c) 2004 <PERSON> (https://davidgrudl.com)
 */

namespace <PERSON>;

use <PERSON>;
?>
<h1>Errors</h1>

<div class="tracy-inner">
<table>
<?php foreach ($data as $item => $count): list($file, $line, $message) = explode('|', $item, 3) ?>
<tr>
	<td class="tracy-right"><?php echo $count ? "$count\xC3\x97" : '' ?></td>
	<td><pre><?php echo htmlspecialchars($message, ENT_IGNORE, 'UTF-8'), ' in ', Helpers::editorLink($file, $line) ?></pre></td>
</tr>
<?php endforeach ?>
</table>
</div>
