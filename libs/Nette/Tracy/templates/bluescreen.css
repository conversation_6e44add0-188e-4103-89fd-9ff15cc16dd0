/**
 * This file is part of the Tracy (https://tracy.nette.org)
 */

html {
	overflow-y: scroll;
}

#tracyBluescreen {
	font: 9pt/1.5 Verdana, sans-serif;
	background: white;
	color: #333;
	position: absolute;
	z-index: 20000;
	left: 0;
	top: 0;
	width: 100%;
	text-align: left;
}

#tracyBluescreen * {
	font: inherit;
	color: inherit;
	background: transparent;
	border: none;
	margin: 0;
	padding: 0;
	text-align: inherit;
	text-indent: 0;
}

#tracyBluescreen b {
	font-weight: bold;
}

#tracyBluescreen i {
	font-style: italic;
}

#tracyBluescreen a {
	text-decoration: none;
	color: #328ADC;
	padding: 2px 4px;
	margin: -2px -4px;
}

#tracyBluescreen a:hover, #tracyBluescreen a:active, #tracyBluescreen a:focus {
	color: #085AA3;
}

#tracyBluescreenIcon {
	position: absolute;
	right: .5em;
	top: .5em;
	text-decoration: none;
	background: #CD1818;
	color: white !important;
	padding: 3px;
}

#tracyBluescreenError {
	background: #CD1818;
	color: white;
	font: 13pt/1.5 Verdana, sans-serif !important;
	display: block;
}

#tracyBluescreenError #tracyBsSearch {
	color: #CD1818;
	font-size: .7em;
}

#tracyBluescreenError:hover #tracyBsSearch {
	color: #ED8383;
}

#tracyBluescreen h1 {
	font-size: 18pt;
	font-weight: normal;
	text-shadow: 1px 1px 0 rgba(0, 0, 0, .4);
	margin: .7em 0;
}

#tracyBluescreen h2 {
	font: 14pt/1.5 sans-serif !important;
	color: #888;
	margin: .6em 0;
}

#tracyBluescreen h3 {
	font: bold 10pt/1.5 Verdana, sans-serif !important;
	margin: 1em 0;
	padding: 0;
}

#tracyBluescreen p, #tracyBluescreen pre {
	margin: .8em 0
}

#tracyBluescreen pre, #tracyBluescreen code, #tracyBluescreen table {
	font: 9pt/1.5 Consolas, monospace !important;
}

#tracyBluescreen pre, #tracyBluescreen table {
	background: #FDF5CE;
	padding: .4em .7em;
	border: 1px dotted silver;
	overflow: auto;
}

#tracyBluescreen table pre {
	padding: 0;
	margin: 0;
	border: none;
}

#tracyBluescreen table {
	border-collapse: collapse;
	width: 100%;
}

#tracyBluescreen td, #tracyBluescreen th {
	vertical-align: top;
	text-align: left;
	padding: 2px 6px;
	border: 1px solid #e6dfbf;
}

#tracyBluescreen th {
	font-weight: bold;
}

#tracyBluescreen tr > :first-child {
	width: 20%;
}

#tracyBluescreen tr:nth-child(2n), #tracyBluescreen tr:nth-child(2n) pre {
	background-color: #F7F0CB;
}

#tracyBluescreen ol {
	margin: 1em 0;
	padding-left: 2.5em;
}

#tracyBluescreen ul {
	font: 7pt/1.5 Verdana, sans-serif !important;
	padding: 2em 4em;
	margin: 1em 0 0;
	color: #777;
	background: #F6F5F3;
	border-top: 1px solid #DDD;
}

#tracyBluescreenLogo a {
	position: absolute;
	bottom: 0;
	right: 0;
	width: 100px;
	height: 50px;
	background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAUBAMAAAD/1DctAAAAMFBMVEWupZzj39rEvbTy8O3X0sz9/PvGwLu8tavQysHq6OS0rKP5+Pbd2dT29fPMxbzPx8DKErMJAAAACXBIWXMAAAsTAAALEwEAmpwYAAACGUlEQVQoFX3TQWgTQRQA0MWLIJJDYehBTykhG5ERTx56K1u8eEhCYtomE7x5L4iLh0ViF7egewuFFqSIYE6hIHsIYQ6CQSg9CDKn4QsNCRlB59C74J/ZNHW1+An5+bOPyf6/s46oz2P+A0yIeZZ2ieEHi6TOnLKTxvWq+b52mxlVO3xnM1s7xLX1504XQH65OnW2dBqn7cCkYsFsfYsWpyY/2salmFTpEyzeR8zosYqMdiPDXdyU52K1wgEa/SjGpdEwUAxqvRfckQCDOyFearsEHe2grvkh/cFAHKvdtI3lcVceKQIOFpv+FOZaNPQBwJZLPp+hfrvT5JZXaUFsR8zqQc9qSgAharkfS5M/5F6nGJJAtXq/eLr3ucZpHccSxOOIPaQhtHohpCH2Xu6rLmQ0djnr4/+J3C6v+AW8/XWYxwYNdlhWj/P5fPSTQwVr0T9lGxdaBCqErNZaqYnEwbkjEB3NasGF3lPdrHa1nnxNOMgj0+neePUPjd2v/qVvUv29ifvc19huQ48qwXShy/9o8o3OSk0cs37mOFd0Ydgvsf/oZEnPVtggfd66lORn9mDyyzXU13SRtH2L6aR5T/snGAcZPfAXz5J1YlJWBEuxdMYqQecpBrlM49xAbmqyHA+xlA1FxBtqT2xmJoNXZlIt74ZBLeJ9ZGDqByNI7p543idzJ23vXEv7IgnsxiS+eNtwNbFdLq7+Bi4wQ0I4SVb9AAAAAElFTkSuQmCC') no-repeat;
	opacity: .6;
	padding: 0;
	margin: 0;
}

#tracyBluescreenLogo a:hover, #tracyBluescreenLogo a:active, #tracyBluescreenLogo a:focus {
	opacity: 1;
	transition: opacity 0.1s;
}


#tracyBluescreen div.panel {
	padding: 1px 25px;
}

#tracyBluescreen div.inner {
	background: #F4F3F1;
	padding: .1em 1em 1em;
	border-radius: 8px;
}

#tracyBluescreen .outer {
	overflow: auto;
}


/* source code */
#tracyBluescreen pre.php div {
	min-width: 100%;
	float: left;
	white-space: pre;
}

#tracyBluescreen .highlight {
	background: #CD1818;
	color: white;
	font-weight: bold;
	font-style: normal;
	display: block;
	padding: 0 .4em;
	margin: 0 -.4em;
}

#tracyBluescreen .line {
	color: #9F9C7F;
	font-weight: normal;
	font-style: normal;
}

#tracyBluescreen pre:hover span[title] {
	border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}

#tracyBluescreen a[href^=editor\:] {
	color: inherit;
	border-bottom: 1px dotted rgba(0, 0, 0, .2);
}

#tracyBluescreen span[data-tracy-href] {
	border-bottom: 1px dotted rgba(0, 0, 0, .2);
}


/* toggle */
html.tracy-js #tracyBluescreen .tracy-collapsed {
	display: none;
}

html.tracy-js #tracyBluescreen .tracy-toggle.tracy-collapsed {
	display: inline;
}

#tracyBluescreen .tracy-toggle {
	cursor: pointer;
}

#tracyBluescreen .tracy-toggle:after {
	content: " ▼";
	opacity: .4;
}

#tracyBluescreen .tracy-toggle.tracy-collapsed:after {
	content: " ►";
	opacity: .4;
}


/* dump */
#tracyBluescreen .tracy-dump-array, #tracyBluescreen .tracy-dump-object {
	color: #C22;
}

#tracyBluescreen .tracy-dump-string {
	color: #35D;
}

#tracyBluescreen .tracy-dump-number {
	color: #090;
}

#tracyBluescreen .tracy-dump-null, #tracyBluescreen .tracy-dump-bool {
	color: #850;
}

#tracyBluescreen .tracy-dump-visibility, #tracyBluescreen .tracy-dump-hash {
	font-size: 85%;
	color: #998;
}

#tracyBluescreen .tracy-dump-indent {
	display: none;
}

#tracyBluescreen pre.tracy-dump div {
	padding-left: 3ex;
}

#tracyBluescreen pre.tracy-dump div div {
	border-left: 1px solid rgba(0, 0, 0, .1);
	margin-left: .5ex;
}

#tracyBluescreen .caused {
	float: right;
	padding: .3em .6em;
	background: #df8075;
	border-radius: 0 0 0 8px;
	white-space: nowrap;
}

#tracyBluescreen .caused a {
	color: white;
}
