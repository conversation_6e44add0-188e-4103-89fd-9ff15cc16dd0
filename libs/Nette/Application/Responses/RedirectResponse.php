<?php

/**
 * This file is part of the Nette Framework (https://nette.org)
 * Copyright (c) 2004 <PERSON> (https://davidgrudl.com)
 */

namespace Nette\Application\Responses;

use Nette;
use Nette\Http;


/**
 * Redirects to new URI.
 *
 * <AUTHOR>
 */
class RedirectResponse extends Nette\Object implements Nette\Application\IResponse
{
	/** @var string */
	private $url;

	/** @var int */
	private $code;


	/**
	 * @param  string  URI
	 * @param  int     HTTP code 3xx
	 */
	public function __construct($url, $code = Http\IResponse::S302_FOUND)
	{
		$this->url = (string) $url;
		$this->code = (int) $code;
	}


	/**
	 * @return string
	 */
	public function getUrl()
	{
		return $this->url;
	}


	/**
	 * @return int
	 */
	public function getCode()
	{
		return $this->code;
	}


	/**
	 * Sends response to output.
	 * @return void
	 */
	public function send(Http\IRequest $httpRequest, Http\IResponse $httpResponse)
	{
		$httpResponse->redirect($this->url, $this->code);
	}

}
