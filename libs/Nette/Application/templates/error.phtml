<?php

/**
 * Default error page.
 * @param  int  $code
 */

namespace NetteModule;

$messages = array(
	0 => array('Oops...', 'Your browser sent a request that this server could not understand or process.'),
	403 => array('Access Denied', 'You do not have permission to view this page. Please try contact the web site administrator if you believe you should be able to view this page.'),
	404 => array('Page Not Found', 'The page you requested could not be found. It is possible that the address is incorrect, or that the page no longer exists. Please use a search engine to find what you are looking for.'),
	405 => array('Method Not Allowed', 'The requested method is not allowed for the URL.'),
	410 => array('Page Not Found', 'The page you requested has been taken off the site. We apologize for the inconvenience.'),
	500 => array('Server Error', 'We\'re sorry! The server encountered an internal error and was unable to complete your request. Please try again later.'),
);
$message = isset($messages[$code]) ? $messages[$code] : $messages[0];

?>
<!DOCTYPE html>
<meta charset="utf-8">
<meta name=robots content=noindex>
<meta name=generator content="Nette Framework">
<style>body{color:#333;background:white;width:500px;margin:100px auto}h1{font:bold 47px/1.5 sans-serif;margin:.6em 0}p{font:21px/1.5 Georgia,serif;margin:1.5em 0}small{font-size:70%;color:gray}</style>

<title><?php echo $message[0] ?></title>

<h1><?php echo $message[0] ?></h1>

<p><?php echo $message[1] ?></p>

<?php if ($code): ?><p><small>error <?php echo $code ?></small></p><?php endif ?>
