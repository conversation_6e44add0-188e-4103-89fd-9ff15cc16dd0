<?php

/**
 * This file is part of the Nette Framework (https://nette.org)
 * Copyright (c) 2004 <PERSON> (https://davidgrudl.com)
 */

if (!function_exists('dlog')) {
	/**
	 * <PERSON>\Debugger::log() shortcut.
	 */
	function dlog($var = NULL)
	{
		if (func_num_args() === 0) {
			<PERSON>\Debugger::log(new Exception, 'dlog');
		}
		foreach (func_get_args() as $arg) {
			<PERSON>\Debugger::log($arg, 'dlog');
		}
		return $var;
	}
}


if (!function_exists('callback')) {
	/**
	 * Nette\Callback factory.
	 * @param  mixed   class, object, callable
	 * @param  string  method
	 * @return Nette\Callback
	 */
	function callback($callback, $m = NULL)
	{
		return new Nette\Callback($callback, $m);
	}
}


/**
 * This file is part of the Tracy (https://tracy.nette.org)
 * Copyright (c) 2004 <PERSON> (https://davidgrudl.com)
 */

if (!function_exists('dump')) {
	/**
	 * <PERSON>\Debugger::dump() shortcut.
	 * @tracySkipLocation
	 */
	function dump($var)
	{
		foreach (func_get_args() as $arg) {
			<PERSON>\Debugger::dump($arg);
		}
		return $var;
	}
}
