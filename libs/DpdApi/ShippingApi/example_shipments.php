<?php
include 'class_DpdShippingApi.php';


// klíč od DPD (asi testovací)
$DPD = new DpdShippingApi('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJWYmtSZHpob0dpd2VGWEhwdWgxYUpTYzhpaWk0bzNlbCJ9.FeVUcWPbJ0YeyvGNJN2S1sr3KaB_hQjxpUlzmIzASTU');


/**
* Toto níže mělo označení: DPD12 s COD MPS
*
* Další parametry buď podle dokumentace:
* https://nst-preprod.dpsin.dpdgroup.com/api/docs/#nst-shipment-api-shipment-webservices-updated
*
* Nebo podle příkladových requestů:
* dokumentace_postman_shipments.txt
*
* Nebo po debatě s DPD, protože vůbec nevím jaké jsou dostupné služby (ID services).
*/
$res = $DPD->curl('shipments', [
   'buCode' => '015', 
   'customerId' => '5017678', 
   'shipments' => [
         [
			'numOrder' => 58, 
			'senderAddressId' => '23', 
			'receiver' => [
				'additionalAddressInfo' => 'test shipping api', 
				'address2' => '', 
				'address3' => '', 
				'city' => 'Praha', 
				'companyName' => 'DPD internal test1', 
				'companyName2' => 'DPD 2', 
				'contactEmail' => '<EMAIL>', 
				'contactFax' => '', 
				'contactFaxPrefix' => '', 
				'contactInterphoneName' => '', 
				'contactMobile' => '*********', 
				'contactMobilePrefix' => '', 
				'contactName' => 'Tomas Horcik', 
				'contactPhone' => '*********', 
				'contactPhonePrefix' => '', 
				'countryCode' => 'CZ', 
				'department' => 'CT5555', 
				'doorCode' => '310', 
				'flatNo' => 'CT4', 
				'floor' => '3', 
				'houseNo' => 'C', 
				'name' => 'Tomas Horcik API1', 
				'name2' => 'Petr Novák', 
				'street' => 'Táborská 12', 
				'zipCode' => '14000' 
            ],
            'parcels' => [
				// JEDNOTLIVÉ BALÍKY V ZÁSILCE
				[
					'dimensionHeight' => 25, 
					'dimensionLength' => 14, 
					'dimensionWidth' => 13, 
					'limitedQuantity' => false, 
					'reference1' => 'REF BALIKU414', 
					'reference2' => 're2 22', 
					'reference3' => 're3 33', 
					'reference4' => 're4 44', 
					'weight' => 5, 
					'insCurrency' => '', 
					'codCurrency' => '' 
				], 
				[
					'dimensionHeight' => 25, 
					'dimensionLength' => 14, 
					'dimensionWidth' => 13, 
					'limitedQuantity' => false, 
					'reference1' => 'REF BALIKU414', 
					'reference2' => 're2 22', 
					'reference3' => 're3 33', 
					'reference4' => 're4 44', 
					'weight' => 5, 
					'insCurrency' => '', 
					'codCurrency' => '' 
				], 
				[
					'dimensionHeight' => 25, 
					'dimensionLength' => 14, 
					'dimensionWidth' => 13, 
					'limitedQuantity' => false, 
					'reference1' => 'REF BALIKU414', 
					'reference2' => 're2 22', 
					'reference3' => 're3 33', 
					'reference4' => 're4 44', 
					'weight' => 5, 
					'insCurrency' => '', 
					'codCurrency' => '' 
				] 
			],
            'service' => [
				'additionalService' => [
					// DOBÍRKA
					'cod' => [
						'amount' => '2700', 
						'currency' => 'CZK', 
						'paymentType' => 'Cash', 
						'reference' => '12555', 
						'split' => 'First parcel' 
					],
					/*
					// SWAP zásilky
					"swap" => [
						"swappedParcelNumber" => 1
					 ],
					 // extra pojištění
					 "highInsurance" => [
						"amount" => "120000", 
						"currency" => "CZK", 
						"split" => "Even" 
					],
					// toto taky nevím, co je, ale patří to ke SWAP příkladu
					"predicts" => [
						[
							"destination" => "+420*********", 
							"destinationType" => "Consignee", 
							"language" => "CS", 
							"trigger" => "Pick-up", 
							"type" => "SMS" 
						], 
						[
							"destination" => "<EMAIL>", 
							"destinationType" => "Consignee", 
							"language" => "CS", 
							"trigger" => "Pick-up", 
							"type" => "email" 
						] 
					],
					*/
				],
				'mainServiceCode' => '',
				'mainServiceElementCodes' => [
					// pro soupis služeb se obraťte na DPD
					// někdy použili "022" a někdy "010"
					// v případě COD PRIVATE použili více služeb: "010", "013"
					// DPD PRIVATE mělo "001", "013"
					'023'
				]
			], 
            'pickupDate' => $DPD->getNextPossiblePickup(), // vrátí další pracovní den, pokud je dnešek po 9. hodině
            'fromTime' => '09:00', 
            'toTime' => '12:00', 
            'reference1' => 'ref1', 
            'reference2' => 'ref2', 
            'reference3' => 'ref3', 
            'reference4' => 'ref4', 
            'saveMode' => 'printed', 
            'printFormat' => 'PDF', // PDF nebo ZPL
            'labelSize' => 'A4',  // A4 nebo A6
            'extendShipmentData' => true, 
            'printRef1AsBarcode' => false 
		]
	]
]);


$parcelNumbers = [];

foreach ($res->shipmentResults as $shipment)
{
	$prefix = 'data:application/pdf;base64,';
	
	// OBSAHUJE 3 ŠTÍTKY = 1 ZÁSILKA A 3 BALÍKY
	$pdf_label = $DPD->parseLabelFile($shipment->labelFile);
	
	# To znamená, že pokud posíláte více balíků, tak každý štítek bude mít samostatný soubor
	# A to i v případě použití funkce: $DPD->curl('labels', ....)
	header('Content-type: application/pdf');
	echo $pdf_label;
	exit;
	
	
	$shipment->shipment->shipmentId; // ID zásilky
	
	// tady jsou 3 čísla všech balíků v zásilce
	foreach ($shipment->shipment->parcels as $parcel)
	{
		# echo $parcel->parcelNumber.'<br>\n';
		
		$parcelNumbers[] = $parcel->parcelNumber;
	}
}




/*
VÝSTUP PROMĚNNÁ $res
---------------------------
stdClass Object
(
    [transactionId] => 4770228
    [shipmentResults] => Array
        (
            [0] => stdClass Object
                (
                    [numOrder] => 58
                    [labelFile] => data:appliTTlkJw................base64
                    [pickupOrderId] => 1057915
                    [shipment] => stdClass Object
                        (
                            [shipmentId] => 13032268
                            [shpParcelCounts] => 3
                            [shpWeight] => 15
                            [mpsidCckey] => A
                            [customerId] => 5017678
                            [createDate] => 20231105
                            [createTime] => 161806
                            [printDate] => 20231105
                            [printTime] => 161806
                            [sendingBuCode] => 015
                            [updateDate] => 20231105
                            [updateTime] => 161806
                            [sender] => stdClass Object
                                (
                                    [addressId] => 23
                                    [name] => Tomáš Horčík DPD CZ
                                    [name2] => TECHNICKÁ PODPORA
                                    [countryCode] => CZ
                                    [zipCode] => 12000
                                    [city] => Praha
                                    [street] => Nad Petruskou 63/1
                                    [contactName] => Tomáš Horčík
                                    [contactPhonePrefix] => +420
                                    [contactPhone] => *********
                                    [contactEmail] => <EMAIL>
                                    [senderCustId] => 5017678
                                )

                            [receiver] => stdClass Object
                                (
                                    [name] => Tomas Horcik API1
                                    [name2] => Petr Novák
                                    [companyName] => DPD internal test1
                                    [companyName2] => DPD 2
                                    [countryCode] => CZ
                                    [zipCode] => 14000
                                    [city] => Praha
                                    [street] => Táborská 12
                                    [houseNo] => C
                                    [flatNo] => CT4
                                    [department] => CT5555
                                    [floor] => 3
                                    [doorCode] => 310
                                    [contactName] => Tomas Horcik
                                    [contactMobile] => *********
                                    [contactPhone] => *********
                                    [contactEmail] => <EMAIL>
                                    [additionalAddressInfo] => test shipping api
                                )

                            [routing] => stdClass Object
                                (
                                    [buCode] => 015
                                    [buAlphaString] => DPD
                                    [networkCode] => 203
                                    [dcountry] => CZ
                                    [barcodeId] => %
                                    [serviceText] => AM2-COD
                                    [version] => ********
                                    [dDepotCountry] => CZ
                                    [dDepot] => 0151381
                                    [dDepotStr] => 1381
                                    [dSort] => 107A
                                )

                            [parcels] => Array
                                (
                                    [0] => stdClass Object
                                        (
                                            [parcelId] => ********
                                            [parcelNumber] => 13925023570840
                                            [parcelNumberCckey] => A
                                            [weight] => 5
                                            [dimensionWidth] => 13
                                            [dimensionHeight] => 25
                                            [dimensionLength] => 14
                                            [limitedQuantity] => 0
                                            [barcodeText] => 0014000139250235708402372031
                                            [reference1] => REF BALIKU414
                                            [reference2] => re2 22
                                            [reference3] => re3 33
                                            [reference4] => re4 44
                                            [soCode] => 237
                                            [parcelCod] => stdClass Object
                                                (
                                                    [codAmount] => 2700
                                                    [currency] => CZK
                                                    [paymentType] => Cash
                                                    [reference] => 12555
                                                )

                                        )

                                    [1] => stdClass Object
                                        (
                                            [parcelId] => 15387389
                                            [parcelNumber] => 13925023570841
                                            [parcelNumberCckey] => 8
                                            [weight] => 5
                                            [dimensionWidth] => 13
                                            [dimensionHeight] => 25
                                            [dimensionLength] => 14
                                            [limitedQuantity] => 0
                                            [barcodeText] => 001400013925023570841237203L
                                            [reference1] => REF BALIKU414
                                            [reference2] => re2 22
                                            [reference3] => re3 33
                                            [reference4] => re4 44
                                            [soCode] => 237
                                            [parcelCod] => stdClass Object
                                                (
                                                    [codAmount] => 0
                                                    [currency] => CZK
                                                    [paymentType] => Cash
                                                    [reference] => 12555
                                                )

                                        )

                                    [2] => stdClass Object
                                        (
                                            [parcelId] => 15387390
                                            [parcelNumber] => 13925023570842
                                            [parcelNumberCckey] => 6
                                            [weight] => 5
                                            [dimensionWidth] => 13
                                            [dimensionHeight] => 25
                                            [dimensionLength] => 14
                                            [limitedQuantity] => 0
                                            [barcodeText] => 001400013925023570842237203P
                                            [reference1] => REF BALIKU414
                                            [reference2] => re2 22
                                            [reference3] => re3 33
                                            [reference4] => re4 44
                                            [soCode] => 237
                                            [parcelCod] => stdClass Object
                                                (
                                                    [codAmount] => 0
                                                    [currency] => CZK
                                                    [paymentType] => Cash
                                                    [reference] => 12555
                                                )

                                        )

                                )

                            [shpReference1] => ref1
                            [shpReference2] => ref2
                            [shpReference3] => ref3
                            [shpReference4] => ref4
                            [pickupDate] => 20231106
                            [status] => 1
                            [mpsId] => 13925023570840
                            [printRef1AsBarcode] => 
                            [service] => stdClass Object
                                (
                                    [mainServiceElementCodes] => Array
                                        (
                                            [0] => 023
                                        )

                                    [additionalService] => stdClass Object
                                        (
                                            [cod] => stdClass Object
                                                (
                                                    [split] => first parcel
                                                    [amount] => 2700
                                                    [currency] => CZK
                                                    [paymentType] => Cash
                                                    [reference] => 12555
                                                )

                                            [additionalProductList] => Array
                                                (
                                                    [0] => stdClass Object
                                                        (
                                                            [name] => COD
                                                            [elements] => Array
                                                                (
                                                                    [0] => 100
                                                                )

                                                            [nonopsElements] => Array
                                                                (
                                                                )

                                                        )

                                                )

                                        )

                                )

                            [sDepot] => 0151392
                        )

                )

        )

)

*/

