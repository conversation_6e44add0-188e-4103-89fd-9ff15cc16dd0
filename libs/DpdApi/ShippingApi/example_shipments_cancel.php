<?php
include 'class_DpdShippingApi.php';


// klíč od DPD (asi testovací)
$DPD = new DpdShippingApi('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJWYmtSZHpob0dpd2VGWEhwdWgxYUpTYzhpaWk0bzNlbCJ9.FeVUcWPbJ0YeyvGNJN2S1sr3KaB_hQjxpUlzmIzASTU');


/**
* Zruš<PERSON><PERSON> z<PERSON>il<PERSON>
* https://nst-preprod.dpsin.dpdgroup.com/api/docs/#cancel-shipment
*/
$res = $DPD->curl('shipments/cancellation', [
	'buCode' => '015', 
	'customerId' => '5017678', 
	'shipmentIdList' => [
		// zásilka musí být jen jako "draft"
		13032268
	]
], 'PUT');

print_r($res);



/*
PŘÍKLAD VÝSTUPU:

stdClass Object
(
    [transactionId] => 4772176
    [resultList] => Array
        (
            [0] => stdClass Object
                (
                    [errors] => Array
                        (
                            [0] => stdClass Object
                                (
                                    [errorCode] => MSG44
                                    [errorContent] => The shipment is cancelled but included pickup order is not allowed to cancel. Please contact DPD!
                                )

                        )

                    [shipmentId] => 13032268
                )

        )

)
*/