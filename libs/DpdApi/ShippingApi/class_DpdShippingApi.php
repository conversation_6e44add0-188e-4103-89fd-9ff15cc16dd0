<?php
/**
* DPD Shipping API
*
* DOKUMENTACE:
* https://nst-preprod.dpsin.dpdgroup.com/api/docs/#nst-shipment-api
*
* <AUTHOR> | zvarik.cz
* @version 2023-10-25
*/
class DpdShippingApi
{
	private $apiKey;
	private $endpointBaseUrl;
	
	// testovací endpoint asi neexistuje, žádná zmínka nikde...
	// toto je zmíněno v dokumentaci
	// const URL_PRODUCTION = 'https://nst-preprod.dpsin.dpdgroup.com/api/v1.1';
	// toto dodalo DPD
	const URL_PRODUCTION = 'https://shipping.dpdgroup.com/api/v1.1';
	
	
	/**
	* __construct
	*
	* @param (string) $apiKey - Přístupový token, dostanete dva (jeden pro testovací a druhý pro produkční prostředí)
	*/
	function __construct($apiKey)
	{
		$this->apiKey = $apiKey;
		$this->endpointBaseUrl = self::URL_PRODUCTION;
	}
	
	
	/**
	* CURL požadavek
	*
	* @param (string) $urlPath
	* @param (mixed) $postData - POST
	* @param (string) $postType = null ... např. někde je nutné PUT
	*
	* @return (object) JSON result
	*/
	function curl($urlPath, $postData = null, $postType = null)
	{
		if (strpos($urlPath, 'http') === false) {
			$url = $this->endpointBaseUrl.'/'.trim($urlPath, '/');
		} else {
			$url = rtrim($urlPath, '/');
		}
		$ch = curl_init($url);
		
		$headers = array(
			'Content-Type: application/json',
			'Authorization: Bearer '.$this->apiKey
		);
		
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		
		if (isset($postData)) {
			if (is_object($postData) || is_array($postData)) {
				$postData = json_encode($postData);
			}
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
		}
		
		if (!empty($postType)) {
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $postType);
		}
		
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
		$response = curl_exec($ch);

    //var_export($response);
    //die();

		curl_close($ch);

		$out = @json_decode($response);
		if (!$out && json_last_error()) {
			$err = json_last_error_msg();
			throw new Exception($err);
		}
		
		return $out;
	}
	
	
	/**
	* Nejbližší pracovní den (včetně dneška pokud je před hodinou X)
	*
	* @param (int) $hour = 9 - pokud je třeba 8 ráno, tak vrátí aktuální pracovní den
	*/
	function getNextPossiblePickup($hour = 9)
	{
		$ts = time();
		$day = date("N");
		$H = (int) date("H");
		
		if ($day <= 5 && $H < $hour); // it is work day and before 14 a.m.
		elseif ($day == 5) $ts = strtotime("+3 day"); // friday
		elseif ($day == 6) $ts = strtotime("+2 day"); // saturday
		else $ts = strtotime("+1 day");
		
		return date("Ymd", $ts);
	}
	
	
	/**
	* Vrátí štítky
	* DPD nepodporuje tisk více štítků zároveň
	* Vytiskne to více balíků v zásilce, ale vždy jen jednu zásilku!
	* Přepokládám, že UNION štítků by se musel udělat samostatně stejně jako u GeoAPI
	* Pro více se podívejte do /GeoApi/ kde k tomu používám další knihovny
	*
	* @param (array) $number - Buď shipmentId / parcelNumber
	* @param (string) $printFormat - PDF/ZPL
	*/
	function getLabels($number, $printFormat = 'ZPL')
	{
		// Pozor! Tady se rozhodli použít GET namísto POST
		$col = 'parcelNumber';
		if (strlen($number) < 12) $col = 'shipmentId';
		
		$res = $this->curl('labels?' . http_build_query(array(
			$col => $number,
			'labelSize' => 'A4', // labelSize je default A4, může být A6
			'printFormat' => strtolower($printFormat),
		)));
		
		// pdfFile se to jmenuje i v případě ZPL
		$out = $this->parseLabelFile($res->pdfFile);
		
		return $out;
	}
	
	
	/**
	* Štítek
	* Udělá base64_decode a smaže blbosti...
	*/
	function parseLabelFile($labelFile)
	{
		$out = $labelFile;
		$prefix = "data:application/pdf;base64,";
		if (substr($out, 0, strlen($prefix)) == $prefix) {
			$out = substr($out, strlen($prefix));
			$out = base64_decode($out);
		}
		return $out;
	}
}



