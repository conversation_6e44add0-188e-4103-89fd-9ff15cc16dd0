<?php
include 'class_DpdShippingApi.php';


// klíč od DPD (asi testovací)
$DPD = new DpdShippingApi('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJWYmtSZHpob0dpd2VGWEhwdWgxYUpTYzhpaWk0bzNlbCJ9.FeVUcWPbJ0YeyvGNJN2S1sr3KaB_hQjxpUlzmIzASTU');


/**
* VYTVOŘENÍ SVOZU / PICKUP
* https://nst-preprod.dpsin.dpdgroup.com/api/docs/#nst-shipment-api-pickup-order
*/
$res = $DPD->curl('pickup', [
	'buCode' => '015',
	'customerId' => '5017678',
	'pickupOrder' => [
		'additionalInfo' => 'string',
		'contactEmail' => 'string',
		'contactName' => 'string',
		'contactPhone' => 'string',
		'contactPhonePrefix' => 'string',
		'externalPickupAddressId' => 0,
		'fromTime' => 'string',
		'internalPickupAddressId' => 0,
		'parcelCount' => 0,
		'pickupDate' => 'string',
		'shipmentIds' => [
			0,
		],
		'toTime' => 'string',
		'totalWeight' => 0,
	],
]);

print_r($res);



/*
PŘÍKLAD VÝSTUPU:

stdClass Object
(
    [transactionId] => 4772176
    [resultList] => Array
        (
            [0] => stdClass Object
                (
                    [errors] => Array
                        (
                            [0] => stdClass Object
                                (
                                    [errorCode] => MSG44
                                    [errorContent] => The shipment is cancelled but included pickup order is not allowed to cancel. Please contact DPD!
                                )

                        )

                    [shipmentId] => 13032268
                )

        )

)
*/