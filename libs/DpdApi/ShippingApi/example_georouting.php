<?php
include 'class_DpdShippingApi.php';


// klíč od DPD (asi testovací)
$DPD = new DpdShippingApi('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJWYmtSZHpob0dpd2VGWEhwdWgxYUpTYzhpaWk0bzNlbCJ9.FeVUcWPbJ0YeyvGNJN2S1sr3KaB_hQjxpUlzmIzASTU');


/**
* GEOROUTING
* https://nst-preprod.dpsin.dpdgroup.com/api/docs/#back-office-api-georouting-webservices
*
* POZOR!
* Tohle vypadá, že není vůbec implementované pro ČR API
*/

$res = $DPD->curl('routing/city-validation', [
	'cityName' => 'PARIS',
	'countryCode' => 'FR',
]);

print_r($res);
