<?php
include 'class_DpdShippingApi.php';


// klíč od DPD (asi testovací)
$DPD = new DpdShippingApi('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJWYmtSZHpob0dpd2VGWEhwdWgxYUpTYzhpaWk0bzNlbCJ9.FeVUcWPbJ0YeyvGNJN2S1sr3KaB_hQjxpUlzmIzASTU');


/**
* TISK ŠTÍTKŮ
*/

// tohle vytiskne 3 štítky jenom proto, že daná zásilka má 3 balíky
// a nepodporuje to tisk více zásilek najednou

/*
$out = $DPD->getLabels('13925023570849', 'PDF');
header("Content-type: application/pdf");
echo $out;
exit;
*/


// TATO AKTUÁLNĚ NEZDOKUMENTOVANÁ METODA UMÍ TISKNOUT VÍCE ZÁSILEK NAJEDNOU
// - EVIDENTNĚ TO ZVLÁDÁ JENOM SHIPMENTIDLIST, NIKOLIV PARCEL
// - POUŽÍVÁ ENDPOINT v1.0
$out = $DPD->curl('https://shipping.dpdgroup.com/api/v1.0/label/shipment-ids', [
    "buCode" => "015",
    "customerId" => "5017678",
    "labelSize" => "A4",
    "printFormat" => "pdf",
    /*
	"shipmentIdList" => [
        13098989,
        13099023,
        13099030
    ]
	*/
	
]);
print_r($out);
exit;