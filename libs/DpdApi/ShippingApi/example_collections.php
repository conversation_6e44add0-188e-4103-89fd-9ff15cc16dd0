<?php
include 'class_DpdShippingApi.php';


// klíč od DPD (asi testovací)
$DPD = new DpdShippingApi('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJWYmtSZHpob0dpd2VGWEhwdWgxYUpTYzhpaWk0bzNlbCJ9.FeVUcWPbJ0YeyvGNJN2S1sr3KaB_hQjxpUlzmIzASTU');


/**
* Collections vytvoření
* https://nst-preprod.dpsin.dpdgroup.com/api/docs/#nst-collection-request-api
*/
$res = $DPD->curl('collections', [
	'requestorId' => '777', // toto neexistuje, takže nemám jak otestovat
	'buCode' => '015',
	'collectionRequests' => [
		[
			'numOrder' => 1,
			'sender' => [
				'additionalAddressInfo' => 'Domestic collection reqest cod',
				'address2' => '',
				'address3' => '',
				'city' => 'Praha',
				'companyName' => 'DPD CZ',
				'companyName2' => 'Kleovka',
				'contactEmail' => '<EMAIL>',
				'contactFax' => '',
				'contactFaxPrefix' => '',
				'contactInterphoneName' => '',
				'contactMobile' => '*********',
				'contactMobilePrefix' => '',
				'contactName' => 'Tomas',
				'contactPhone' => '*********',
				'contactPhonePrefix' => '',
				'countryCode' => 'CZ',
				'department' => 'CT4C',
				'doorCode' => '310',
				'flatNo' => 'CT4',
				'floor' => '3',
				'houseNo' => '2',
				'name' => 'Tomáš Horčík',
				'name2' => '',
				'street' => 'Táborská 2',
				'zipCode' => '14000',
			],
			'receiver' => [
				'additionalAddressInfo' => 'delivery to the CZ address',
				'address2' => '',
				'address3' => '',
				'city' => 'Praha',
				'companyName' => 'DPD',
				'companyName2' => '',
				'contactEmail' => '<EMAIL>',
				'contactFax' => '',
				'contactFaxPrefix' => '',
				'contactInterphoneName' => '',
				'contactMobile' => '*********',
				'contactMobilePrefix' => '',
				'contactName' => 'Tomas Horcik',
				'contactPhone' => '*********',
				'contactPhonePrefix' => '',
				'countryCode' => 'CZ',
				'department' => '',
				'doorCode' => '',
				'flatNo' => '',
				'floor' => '',
				'houseNo' => '',
				'name' => 'TOMÁŠ Horčík domestic',
				'name2' => '',
				'street' => 'Táborská 2',
				'zipCode' => '14000',
			],
			'parcel' => [
				'dimensionHeight' => 11.11,
				'dimensionLength' => 12.11,
				'dimensionWidth' => 23.11,
				'weight' => 12.11,
			],
			'service' => [
				'additionalService' => [
					'cod' => [
						'amount' => '2005',
						'currency' => 'CZK',
						'paymentType' => 'Credit card',
						'reference' => '152555',
						'split' => 'First parcel',
					],
					'highInsurance' => [
						'amount' => '120000',
						'currency' => 'CZK',
						'split' => 'Even',
					],
					'predicts' => [
						[
							'destination' => '<EMAIL>',
							'destinationType' => '',
							'language' => '',
							'trigger' => '',
							'type' => 'EMAIL',
						],
						[
							'destination' => '*********',
							'destinationType' => '',
							'language' => '',
							'trigger' => '',
							'type' => 'SMS',
						],
					],
				],
				'mainServiceCode' => '',
				'mainServiceElementCodes' => [
					'001',
					'013',
				],
			],
			'ref1' => 'ref1',
			'ref2' => 'ref2',
			'ref3' => 'ref3',
			'ref4' => 'ref4',
			'compInfo1' => 'compInfo1',
			'compInfo2' => 'compInfo2',
			'pickupDate' => $DPD->getNextPossiblePickup(0),
		],
	],
]);

print_r($res);



/*
VÝSTUP PROMĚNNÁ $res
---------------------------
stdClass Object
(
    [transactionId] => 4770228
    [shipmentResults] => Array
        (
            [0] => stdClass Object
                (
                    [numOrder] => 58
                    [labelFile] => data:appliTTlkJw................base64
                    [pickupOrderId] => 1057915
                    [shipment] => stdClass Object
                        (
                            [shipmentId] => 13032268
                            [shpParcelCounts] => 3
                            [shpWeight] => 15
                            [mpsidCckey] => A
                            [customerId] => 5017678
                            [createDate] => 20231105
                            [createTime] => 161806
                            [printDate] => 20231105
                            [printTime] => 161806
                            [sendingBuCode] => 015
                            [updateDate] => 20231105
                            [updateTime] => 161806
                            [sender] => stdClass Object
                                (
                                    [addressId] => 23
                                    [name] => Tomáš Horčík DPD CZ
                                    [name2] => TECHNICKÁ PODPORA
                                    [countryCode] => CZ
                                    [zipCode] => 12000
                                    [city] => Praha
                                    [street] => Nad Petruskou 63/1
                                    [contactName] => Tomáš Horčík
                                    [contactPhonePrefix] => +420
                                    [contactPhone] => *********
                                    [contactEmail] => <EMAIL>
                                    [senderCustId] => 5017678
                                )

                            [receiver] => stdClass Object
                                (
                                    [name] => Tomas Horcik API1
                                    [name2] => Petr Novák
                                    [companyName] => DPD internal test1
                                    [companyName2] => DPD 2
                                    [countryCode] => CZ
                                    [zipCode] => 14000
                                    [city] => Praha
                                    [street] => Táborská 12
                                    [houseNo] => C
                                    [flatNo] => CT4
                                    [department] => CT5555
                                    [floor] => 3
                                    [doorCode] => 310
                                    [contactName] => Tomas Horcik
                                    [contactMobile] => *********
                                    [contactPhone] => *********
                                    [contactEmail] => <EMAIL>
                                    [additionalAddressInfo] => test shipping api
                                )

                            [routing] => stdClass Object
                                (
                                    [buCode] => 015
                                    [buAlphaString] => DPD
                                    [networkCode] => 203
                                    [dcountry] => CZ
                                    [barcodeId] => %
                                    [serviceText] => AM2-COD
                                    [version] => ********
                                    [dDepotCountry] => CZ
                                    [dDepot] => 0151381
                                    [dDepotStr] => 1381
                                    [dSort] => 107A
                                )

                            [parcels] => Array
                                (
                                    [0] => stdClass Object
                                        (
                                            [parcelId] => ********
                                            [parcelNumber] => 13925023570840
                                            [parcelNumberCckey] => A
                                            [weight] => 5
                                            [dimensionWidth] => 13
                                            [dimensionHeight] => 25
                                            [dimensionLength] => 14
                                            [limitedQuantity] => 0
                                            [barcodeText] => 0014000139250235708402372031
                                            [reference1] => REF BALIKU414
                                            [reference2] => re2 22
                                            [reference3] => re3 33
                                            [reference4] => re4 44
                                            [soCode] => 237
                                            [parcelCod] => stdClass Object
                                                (
                                                    [codAmount] => 2700
                                                    [currency] => CZK
                                                    [paymentType] => Cash
                                                    [reference] => 12555
                                                )

                                        )

                                    [1] => stdClass Object
                                        (
                                            [parcelId] => 15387389
                                            [parcelNumber] => 13925023570841
                                            [parcelNumberCckey] => 8
                                            [weight] => 5
                                            [dimensionWidth] => 13
                                            [dimensionHeight] => 25
                                            [dimensionLength] => 14
                                            [limitedQuantity] => 0
                                            [barcodeText] => 001400013925023570841237203L
                                            [reference1] => REF BALIKU414
                                            [reference2] => re2 22
                                            [reference3] => re3 33
                                            [reference4] => re4 44
                                            [soCode] => 237
                                            [parcelCod] => stdClass Object
                                                (
                                                    [codAmount] => 0
                                                    [currency] => CZK
                                                    [paymentType] => Cash
                                                    [reference] => 12555
                                                )

                                        )

                                    [2] => stdClass Object
                                        (
                                            [parcelId] => 15387390
                                            [parcelNumber] => 13925023570842
                                            [parcelNumberCckey] => 6
                                            [weight] => 5
                                            [dimensionWidth] => 13
                                            [dimensionHeight] => 25
                                            [dimensionLength] => 14
                                            [limitedQuantity] => 0
                                            [barcodeText] => 001400013925023570842237203P
                                            [reference1] => REF BALIKU414
                                            [reference2] => re2 22
                                            [reference3] => re3 33
                                            [reference4] => re4 44
                                            [soCode] => 237
                                            [parcelCod] => stdClass Object
                                                (
                                                    [codAmount] => 0
                                                    [currency] => CZK
                                                    [paymentType] => Cash
                                                    [reference] => 12555
                                                )

                                        )

                                )

                            [shpReference1] => ref1
                            [shpReference2] => ref2
                            [shpReference3] => ref3
                            [shpReference4] => ref4
                            [pickupDate] => 20231106
                            [status] => 1
                            [mpsId] => 13925023570840
                            [printRef1AsBarcode] => 
                            [service] => stdClass Object
                                (
                                    [mainServiceElementCodes] => Array
                                        (
                                            [0] => 023
                                        )

                                    [additionalService] => stdClass Object
                                        (
                                            [cod] => stdClass Object
                                                (
                                                    [split] => first parcel
                                                    [amount] => 2700
                                                    [currency] => CZK
                                                    [paymentType] => Cash
                                                    [reference] => 12555
                                                )

                                            [additionalProductList] => Array
                                                (
                                                    [0] => stdClass Object
                                                        (
                                                            [name] => COD
                                                            [elements] => Array
                                                                (
                                                                    [0] => 100
                                                                )

                                                            [nonopsElements] => Array
                                                                (
                                                                )

                                                        )

                                                )

                                        )

                                )

                            [sDepot] => 0151392
                        )

                )

        )

)

*/

