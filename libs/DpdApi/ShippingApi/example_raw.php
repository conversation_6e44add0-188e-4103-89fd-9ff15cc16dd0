<?php
/**
* PŘÍKLAD RAW REQUESTU
*/

include 'class_DpdShippingApi.php';


// klí<PERSON> od DPD (asi testovací)
$DPD = new DpdShippingApi('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJWYmtSZHpob0dpd2VGWEhwdWgxYUpTYzhpaWk0bzNlbCJ9.FeVUcWPbJ0YeyvGNJN2S1sr3KaB_hQjxpUlzmIzASTU');


// parametr může být obojí - string nebo array/object
// tady uvádím string

print_r($DPD->curl('shipments', '{
    "buCode": "015",
    "customerId": "5017678",
    "shipments": [
        {
            "numOrder": 58,
            "senderAddressId": "23",
            
           
            "receiver": {
                "additionalAddressInfo": "test shipping api",
                "address2": "",
                "address3": "",
                "city": "Praha",
                "companyName": "DPD internal test1",
                "companyName2": "DPD 2",
                "contactEmail": "<EMAIL>",
                "contactFax": "",
                "contactFaxPrefix": "",
                "contactInterphoneName": "",
                "contactMobile": "*********",
                "contactMobilePrefix": "",
                "contactName": "Tomas Horcik",
                "contactPhone": "*********",
                "contactPhonePrefix": "",
                "countryCode": "CZ",
                "department": "CT5555",
                "doorCode": "310",
                "flatNo": "CT4",
                "floor": "3",
                "houseNo": "C",
                "name": "Tomas Horcik API1",
                "name2": "Petr Novák",
                "street": "Táborská 12",
                "zipCode": "14000"
            },
            "parcels": [
                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALIKU414",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                },
                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALIKU414",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                },
                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALIKU414",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                }
            ],
            "service": 
                {
                    "additionalService": {
                        "cod": {
                            "amount": "2700",
                            "currency": "CZK",
                            "paymentType": "Cash",
                            "reference": "12555",
                            "split": "First parcel"
                            
                        }
                    },
                "mainServiceCode": "",
                "mainServiceElementCodes": [
                    "023"
                ]
            },
     "pickupDate": "20230804",
      "fromTime": "09:00",
      "toTime": "12:00",
      "reference1": "ref1",
      "reference2": "ref2",
      "reference3": "ref3",
      "reference4": "ref4",
      "saveMode": "printed",
      "printFormat": "PDF", 
      "labelSize": "A4",
      "extendShipmentData": true,
      "printRef1AsBarcode": false
        }
    ]
}'));



