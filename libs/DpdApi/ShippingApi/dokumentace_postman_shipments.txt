V�ECHNY TYTO P��KLADY PAT�� K [example_shipments.php]
V�<PERSON> je stejn�ch...
Mo�n� to je k ni�emu, ale pro �plnost uv�d�m - dostali jsme od DPD.

---------------------------------
DPD EXPRESS EU

{
    "buCode": "015",
    "customerId": "5017678",
    "shipments": [
        {
            "numOrder": 52,
            "senderAddressId": "23",
            
           
            "receiver": {
                "additionalAddressInfo": "test shipping api",
                "address2": "",
                "address3": "",
                "city": "Praha",
                "companyName": "DPD internal testqq",
                "companyName2": "DPD 2",
                "contactEmail": "<EMAIL>",
                "contactFax": "",
                "contactFaxPrefix": "",
                "contactInterphoneName": "",
                "contactMobile": "*********",
                "contactMobilePrefix": "",
                "contactName": "Tomas <PERSON> test swap",
                "contactPhone": "*********",
                "contactPhonePrefix": "",
                "countryCode": "CZ",
                "department": "CT5555",
                "doorCode": "310",
                "flatNo": "CT4",
                "floor": "3",
                "houseNo": "C",
                "name": "API Tomas Horcik DPD PRIVATE",
                "name2": "Petr Nov�k",
                "street": "T�borsk� 12",
                "zipCode": "14000"
            },
            "parcels": [
                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALIKUtom4474",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                },
            

                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALItomKU4766",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                }
            ],
            "service": 
                {
                    "additionalService": {
                       
           

                          "highInsurance": {
                            "amount": "400000",
                            "currency": "CZK",
                            
                            "split": "Even"
                            
                        },
                         "predicts": [
            {
              "destination": "+420*********",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "SMS"
            },

            {
              "destination": "<EMAIL>",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "email"
            }
          ]
                    },
               "mainServiceCode": "",
                "mainServiceElementCodes": [
                    "001", "013"
                ]
            },
     "pickupDate": "20230810",
      "fromTime": "09:00",
      "toTime": "12:00",
      "reference1": "ref1",
      "reference2": "ref2",
      "reference3": "ref3",
      "reference4": "ref4",
      "saveMode": "printed",
      "printFormat": "ZPL", 
      "labelSize": "A4",
      "extendShipmentData": true,
      "printRef1AsBarcode": false
        }
    ]
}"

-------------------------------------
DPD EXPRESS international

{
    "buCode": "015",
    "customerId": "5017678",
    "shipments": [
        {
            "numOrder": 52,
            "senderAddressId": "23",
            
           
            "receiver": {
                "additionalAddressInfo": "test shipping api",
                "address2": "",
                "address3": "",
                "city": "Praha",
                "companyName": "DPD internal testqq",
                "companyName2": "DPD 2",
                "contactEmail": "<EMAIL>",
                "contactFax": "",
                "contactFaxPrefix": "",
                "contactInterphoneName": "",
                "contactMobile": "*********",
                "contactMobilePrefix": "",
                "contactName": "Tomas Horcik test swap",
                "contactPhone": "*********",
                "contactPhonePrefix": "",
                "countryCode": "CZ",
                "department": "CT5555",
                "doorCode": "310",
                "flatNo": "CT4",
                "floor": "3",
                "houseNo": "C",
                "name": "API Tomas Horcik DPD PRIVATE",
                "name2": "Petr Nov�k",
                "street": "T�borsk� 12",
                "zipCode": "14000"
            },
            "parcels": [
                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALIKUtom4474",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                },
            

                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALItomKU4766",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                }
            ],
            "service": 
                {
                    "additionalService": {
                       
           

                          "highInsurance": {
                            "amount": "400000",
                            "currency": "CZK",
                            
                            "split": "Even"
                            
                        },
                         "predicts": [
            {
              "destination": "+420*********",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "SMS"
            },

            {
              "destination": "<EMAIL>",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "email"
            }
          ]
                    },
               "mainServiceCode": "",
                "mainServiceElementCodes": [
                    "001", "013"
                ]
            },
     "pickupDate": "20230810",
      "fromTime": "09:00",
      "toTime": "12:00",
      "reference1": "ref1",
      "reference2": "ref2",
      "reference3": "ref3",
      "reference4": "ref4",
      "saveMode": "printed",
      "printFormat": "ZPL", 
      "labelSize": "A4",
      "extendShipmentData": true,
      "printRef1AsBarcode": false
        }
    ]
}

-------------------------------------
DPD RETURN LABEL

{
    "buCode": "015",
    "customerId": "5017678",
    "shipments": [
        {
            "numOrder": 52,
            "senderAddressId": "23",
            
           
            "receiver": {
                "additionalAddressInfo": "test shipping api",
                "address2": "",
                "address3": "",
                "city": "Praha",
                "companyName": "DPD internal testqq",
                "companyName2": "DPD 2",
                "contactEmail": "<EMAIL>",
                "contactFax": "",
                "contactFaxPrefix": "",
                "contactInterphoneName": "",
                "contactMobile": "*********",
                "contactMobilePrefix": "",
                "contactName": "Tomas Horcik test swap",
                "contactPhone": "*********",
                "contactPhonePrefix": "",
                "countryCode": "CZ",
                "department": "CT5555",
                "doorCode": "310",
                "flatNo": "CT4",
                "floor": "3",
                "houseNo": "C",
                "name": "API Tomas Horcik DPD PRIVATE",
                "name2": "Petr Nov�k",
                "street": "T�borsk� 12",
                "zipCode": "14000"
            },
            "parcels": [
                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALIKUtom4474",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                },
            

                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALItomKU4766",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                }
            ],
            "service": 
                {
                    "additionalService": {
                       
           

                          "highInsurance": {
                            "amount": "400000",
                            "currency": "CZK",
                            
                            "split": "Even"
                            
                        },
                         "predicts": [
            {
              "destination": "+420*********",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "SMS"
            },

            {
              "destination": "<EMAIL>",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "email"
            }
          ]
                    },
               "mainServiceCode": "",
                "mainServiceElementCodes": [
                    "001", "013"
                ]
            },
     "pickupDate": "20230810",
      "fromTime": "09:00",
      "toTime": "12:00",
      "reference1": "ref1",
      "reference2": "ref2",
      "reference3": "ref3",
      "reference4": "ref4",
      "saveMode": "printed",
      "printFormat": "ZPL", 
      "labelSize": "A4",
      "extendShipmentData": true,
      "printRef1AsBarcode": false
        }
    ]
}

--------------------------------------
Customs clearance

{
    "buCode": "015",
    "customerId": "5017678",
    "shipments": [
        {
            "numOrder": 52,
            "senderAddressId": "23",
            
           
            "receiver": {
                "additionalAddressInfo": "test shipping api",
                "address2": "",
                "address3": "",
                "city": "Praha",
                "companyName": "DPD internal testqq",
                "companyName2": "DPD 2",
                "contactEmail": "<EMAIL>",
                "contactFax": "",
                "contactFaxPrefix": "",
                "contactInterphoneName": "",
                "contactMobile": "*********",
                "contactMobilePrefix": "",
                "contactName": "Tomas Horcik test swap",
                "contactPhone": "*********",
                "contactPhonePrefix": "",
                "countryCode": "CZ",
                "department": "CT5555",
                "doorCode": "310",
                "flatNo": "CT4",
                "floor": "3",
                "houseNo": "C",
                "name": "API Tomas Horcik DPD PRIVATE",
                "name2": "Petr Nov�k",
                "street": "T�borsk� 12",
                "zipCode": "14000"
            },
            "parcels": [
                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALIKUtom4474",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                },
            

                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALItomKU4766",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                }
            ],
            "service": 
                {
                    "additionalService": {
                       
           

                          "highInsurance": {
                            "amount": "400000",
                            "currency": "CZK",
                            
                            "split": "Even"
                            
                        },
                         "predicts": [
            {
              "destination": "+420*********",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "SMS"
            },

            {
              "destination": "<EMAIL>",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "email"
            }
          ]
                    },
               "mainServiceCode": "",
                "mainServiceElementCodes": [
                    "001", "013"
                ]
            },
     "pickupDate": "20230810",
      "fromTime": "09:00",
      "toTime": "12:00",
      "reference1": "ref1",
      "reference2": "ref2",
      "reference3": "ref3",
      "reference4": "ref4",
      "saveMode": "printed",
      "printFormat": "ZPL", 
      "labelSize": "A4",
      "extendShipmentData": true,
      "printRef1AsBarcode": false
        }
    ]
}

--------------------------------------
Shop to Shop

{
    "buCode": "015",
    "customerId": "5017678",
    "shipments": [
        {
            "numOrder": 52,
            "senderAddressId": "23",
            
           
            "receiver": {
                "additionalAddressInfo": "test shipping api",
                "address2": "",
                "address3": "",
                "city": "Praha",
                "companyName": "DPD internal testqq",
                "companyName2": "DPD 2",
                "contactEmail": "<EMAIL>",
                "contactFax": "",
                "contactFaxPrefix": "",
                "contactInterphoneName": "",
                "contactMobile": "*********",
                "contactMobilePrefix": "",
                "contactName": "Tomas Horcik test swap",
                "contactPhone": "*********",
                "contactPhonePrefix": "",
                "countryCode": "CZ",
                "department": "CT5555",
                "doorCode": "310",
                "flatNo": "CT4",
                "floor": "3",
                "houseNo": "C",
                "name": "API Tomas Horcik DPD PRIVATE",
                "name2": "Petr Nov�k",
                "street": "T�borsk� 12",
                "zipCode": "14000"
            },
            "parcels": [
                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALIKUtom4474",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                },
            

                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALItomKU4766",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                }
            ],
            "service": 
                {
                    "additionalService": {
                       
           

                          "highInsurance": {
                            "amount": "400000",
                            "currency": "CZK",
                            
                            "split": "Even"
                            
                        },
                         "predicts": [
            {
              "destination": "+420*********",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "SMS"
            },

            {
              "destination": "<EMAIL>",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "email"
            }
          ]
                    },
               "mainServiceCode": "",
                "mainServiceElementCodes": [
                    "001", "013"
                ]
            },
     "pickupDate": "20230810",
      "fromTime": "09:00",
      "toTime": "12:00",
      "reference1": "ref1",
      "reference2": "ref2",
      "reference3": "ref3",
      "reference4": "ref4",
      "saveMode": "printed",
      "printFormat": "ZPL", 
      "labelSize": "A4",
      "extendShipmentData": true,
      "printRef1AsBarcode": false
        }
    ]
}


---------------------------------
SHOP TO HOME
{
    "buCode": "015",
    "customerId": "5017678",
    "shipments": [
        {
            "numOrder": 52,
            "senderAddressId": "23",
            
           
            "receiver": {
                "additionalAddressInfo": "test shipping api",
                "address2": "",
                "address3": "",
                "city": "Praha",
                "companyName": "DPD internal testqq",
                "companyName2": "DPD 2",
                "contactEmail": "<EMAIL>",
                "contactFax": "",
                "contactFaxPrefix": "",
                "contactInterphoneName": "",
                "contactMobile": "*********",
                "contactMobilePrefix": "",
                "contactName": "Tomas Horcik test swap",
                "contactPhone": "*********",
                "contactPhonePrefix": "",
                "countryCode": "CZ",
                "department": "CT5555",
                "doorCode": "310",
                "flatNo": "CT4",
                "floor": "3",
                "houseNo": "C",
                "name": "API Tomas Horcik DPD PRIVATE",
                "name2": "Petr Nov�k",
                "street": "T�borsk� 12",
                "zipCode": "14000"
            },
            "parcels": [
                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALIKUtom4474",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                },
            

                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALItomKU4766",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                }
            ],
            "service": 
                {
                    "additionalService": {
                       
           

                          "highInsurance": {
                            "amount": "400000",
                            "currency": "CZK",
                            
                            "split": "Even"
                            
                        },
                         "predicts": [
            {
              "destination": "+420*********",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "SMS"
            },

            {
              "destination": "<EMAIL>",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "email"
            }
          ]
                    },
               "mainServiceCode": "",
                "mainServiceElementCodes": [
                    "001", "013"
                ]
            },
     "pickupDate": "20230810",
      "fromTime": "09:00",
      "toTime": "12:00",
      "reference1": "ref1",
      "reference2": "ref2",
      "reference3": "ref3",
      "reference4": "ref4",
      "saveMode": "printed",
      "printFormat": "ZPL", 
      "labelSize": "A4",
      "extendShipmentData": true,
      "printRef1AsBarcode": false
        }
    ]
}


---------------------------------------
Masked sender DPD Classic COD
{
    "buCode": "015",
    "customerId": "5017678",
    "shipments": [
        {
            "numOrder": 52,
            "senderAddressId": "23",
            
           
            "receiver": {
                "additionalAddressInfo": "test shipping api",
                "address2": "",
                "address3": "",
                "city": "Praha",
                "companyName": "DPD internal testqq",
                "companyName2": "DPD 2",
                "contactEmail": "<EMAIL>",
                "contactFax": "",
                "contactFaxPrefix": "",
                "contactInterphoneName": "",
                "contactMobile": "*********",
                "contactMobilePrefix": "",
                "contactName": "Tomas Horcik test swap",
                "contactPhone": "*********",
                "contactPhonePrefix": "",
                "countryCode": "CZ",
                "department": "CT5555",
                "doorCode": "310",
                "flatNo": "CT4",
                "floor": "3",
                "houseNo": "C",
                "name": "API Tomas Horcik DPD PRIVATE",
                "name2": "Petr Nov�k",
                "street": "T�borsk� 12",
                "zipCode": "14000"
            },
            "parcels": [
                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALIKUtom4474",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                },
            

                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALItomKU4766",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                }
            ],
            "service": 
                {
                    "additionalService": {
                       
           

                          "highInsurance": {
                            "amount": "400000",
                            "currency": "CZK",
                            
                            "split": "Even"
                            
                        },
                         "predicts": [
            {
              "destination": "+420*********",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "SMS"
            },

            {
              "destination": "<EMAIL>",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "email"
            }
          ]
                    },
               "mainServiceCode": "",
                "mainServiceElementCodes": [
                    "001", "013"
                ]
            },
     "pickupDate": "20230810",
      "fromTime": "09:00",
      "toTime": "12:00",
      "reference1": "ref1",
      "reference2": "ref2",
      "reference3": "ref3",
      "reference4": "ref4",
      "saveMode": "printed",
      "printFormat": "ZPL", 
      "labelSize": "A4",
      "extendShipmentData": true,
      "printRef1AsBarcode": false
        }
    ]
}


---------------------------------------
Dv� z�silky najednou

{
    "buCode": "015",
    "customerId": "5017678",
    "shipments": [
        {
            "numOrder": 52,
            "senderAddressId": "23",
            
           
            "receiver": {
                "additionalAddressInfo": "test shipping api",
                "address2": "",
                "address3": "",
                "city": "Praha",
                "companyName": "DPD internal testqq",
                "companyName2": "DPD 2",
                "contactEmail": "<EMAIL>",
                "contactFax": "",
                "contactFaxPrefix": "",
                "contactInterphoneName": "",
                "contactMobile": "*********",
                "contactMobilePrefix": "",
                "contactName": "Tomas Horcik test swap",
                "contactPhone": "*********",
                "contactPhonePrefix": "",
                "countryCode": "CZ",
                "department": "CT5555",
                "doorCode": "310",
                "flatNo": "CT4",
                "floor": "3",
                "houseNo": "C",
                "name": "API Tomas Horcik DPD PRIVATE",
                "name2": "Petr Nov�k",
                "street": "T�borsk� 12",
                "zipCode": "14000"
            },
            "parcels": [
                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALIKUtom4474",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                },
            

                {
                    "dimensionHeight": 25,
                    "dimensionLength": 14,
                    "dimensionWidth": 13,
                    "limitedQuantity": false,
                    "reference1": "REF BALItomKU4766",
                    "reference2": "re2 22",
                    "reference3": "re3 33",
                    "reference4": "re4 44",
                    "weight": 5,
                    "insCurrency": "",
                    "codCurrency": ""
                }
            ],
            "service": 
                {
                    "additionalService": {
                       
           

                          "highInsurance": {
                            "amount": "400000",
                            "currency": "CZK",
                            
                            "split": "Even"
                            
                        },
                         "predicts": [
            {
              "destination": "+420*********",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "SMS"
            },

            {
              "destination": "<EMAIL>",
              "destinationType": "Consignee",
              "language": "",
              "trigger": "",
              "type": "email"
            }
          ]
                    },
               "mainServiceCode": "",
                "mainServiceElementCodes": [
                    "001", "013"
                ]
            },
     "pickupDate": "20230810",
      "fromTime": "09:00",
      "toTime": "12:00",
      "reference1": "ref1",
      "reference2": "ref2",
      "reference3": "ref3",
      "reference4": "ref4",
      "saveMode": "printed",
      "printFormat": "ZPL", 
      "labelSize": "A4",
      "extendShipmentData": true,
      "printRef1AsBarcode": false
        }
    ]
}


