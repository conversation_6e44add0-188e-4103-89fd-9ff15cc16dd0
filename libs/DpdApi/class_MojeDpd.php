<?php
/**
* Moje DPD API
*
* <AUTHOR> | zvarik.cz
* @version 2018-10-01
*/

class MojeDpd
{
	private $login;
	private $pswd;

  /**
   * __construct
   *
   * @param $login
   * @param $pswd
   */
	function __construct($login, $pswd)
	{
		$this->login = $login;
		$this->pswd = $pswd;
	}

  private function getClient($name) {
	  $url = 'https://www.mojedpd.cz/IT4EMWebServices/eshop/';
	  switch ($name) {
	    case 'createShipment':
      case 'deleteShipment':
      case 'getShipmentStatus':
      case 'getShipmentLabel':
      case 'reprintParcelLabel':
        $url .= 'ShipmentServiceImpl?wsdl';
        break;

      case 'closeManifest':
      case 'reprintManifest':
        $url .= 'ManifestServiceImpl?wsdl';
        break;

      case 'createPickupOrder':
        $url .= 'PickupOrderServiceImpl?wsdl';
        break;

      default:
	      throw new Exception("Špatné volání služby.");
	  }

    return new SoapClient(
      $url,
      array(
        'location' => $url,
        'trace' => 1,
        'exceptions' => 0, // Soap vyhodí vlastní chybu s popisem
        'encoding' => 'UTF-8',
        'stream_context'=> stream_context_create(array(
          'ssl'=> array(
            'verify_peer'=>false,
            'verify_peer_name'=>false,
            'allow_self_signed' => true
            )
          )
        )
      )
    );
  }

  /**
   * Volání nějaké metody
   *
   * @param $name
   * @param $arguments
   * @return
   * @throws Exception
   */
	function __call($name, $arguments) {
		$args['wsUserName'] = $this->login;
		$args['wsPassword'] = $this->pswd;
		$args['wsLang'] = 'CZ';
		$args['applicationType'] = 9; // nevím co to je, v dokumentaci bez zmínky

    if ($name === 'createShipment') {
      $args['priceOption'] = 'WithoutPrice'; // nevím co to je
      $args['shipmentList'] = $arguments[0];
    } else if ($name === 'closeManifest') {
      $args['manifest']['manifestReferenceNumber'] = $arguments[0];
      $args['manifest']['manifestNotes'] = '';
      $args['manifest']['shipmentReferenceList'] = $arguments[1];
      $args['manifestPrintOption'] = 'PrintOnlyManifest';
      $args['printOption'] = 'Pdf';
    } else if ($name === 'reprintManifest') {
      $args['manifestPrintOption'] = 'PrintOnlyManifest';
      $args['printOption'] = 'Pdf';
      $args['manifestReference'] = $arguments[0];
    } else if ($name === 'getShipmentLabel') {
      $args['shipmentReferenceList'] = $arguments[0];
      $args['printOption'] = 'Pdf';
    } else if ($name === 'reprintParcelLabel') {
      $args['parcelReferenceList'] = $arguments[0];
      $args['printOption'] = 'Pdf';
    } else if ($name === 'getShipmentStatus') {
      $args['shipmentReferenceList'][] = $arguments[0];
    } else if ($name === 'deleteShipment') {
      $args['shipmentReferenceList'] = $arguments[0];
    } else if ($name === 'createPickupOrder') {
      $args['shipmentReferenceList'] = $arguments[0];
    }

    $client = $this->getClient($name);

    $ret =  $client->$name($args);
    return $ret;
	}
}
