@echo off
:: cesta do MySQL bin adresáře - !!! NASTAVIT PODLE SEBE
SET MySQLDir=C:\xampp\mysql\bin\

:: LOKALNI CESTA K PROJEKTU - !!! NASTAVIT PODLE SEBE
SET WwwDir=D:\work\www\24krbycz\

:: <PERSON>OK<PERSON><PERSON> SERVER - !!! NASTAVIT PODLE SEBE
SET host_local=127.0.0.1
SET database_local=goldfitnesscz
SET user_local=root
SET pw_local=root

:: VZDALENY SERVER
SET host=db.koblihcz.savana-hosting.cz
SET port=10013
SET database=devgoldfitnesscz
SET user=devgoldfitnesscz

cls
set /p pw= <PERSON>adej heslo pro uzivatele %user% k VZDALNENE databazi %database%:
cls
Echo Stahuji databazi ...
%MySQLDir%mysqldump --user=%user% --password=%pw% --host=%host% --port=%port% --add-drop-table --single-transaction --routines --triggers --events --default-character-set=utf8 %database% > %0\..\dump.sql

IF EXIST %__CD__%dump.sql (
  Echo Importuji do lokalni databaze ...
  %MySQLDir%mysql --host=%host_local% --user=%user_local% --password=%pw_local% %database_local% < %__CD__%dump.sql
  Echo Mazu dump ...
  del %__CD__%dump.sql

  Echo Mazu cache ...
  php %WwwDir%www\cc.php -f k=lZwJIL

  Echo .
  Echo Hotovo!
) ELSE (
  Echo Dump %__CD__%dump.sql nenalezen.
)

PAUSE
