php: # PHP configuration
  date.timezone: Europe/Prague
  zlib.output_compression: yes
tracy:
  strictMode: true
  email: <EMAIL>
application:
  catchExceptions: true
  errorPresenter: Front:Error
session:
  autoStart: true
  expiration: + 60 days
security:
  debugger: true
  roles:
    guest:
    editor:
    expedice:
    admin:
  resources:
    Admin:Admin:
    Admin:User:
    Admin:Catalog:
    Admin:Product:
    Admin:Export:
    Admin:Import:
    Admin:Order:
    Admin:DeliveryMode:
    Admin:Manufacturer:
    Admin:Page:
    Admin:Article:
    Admin:Menu:
    Admin:New:
    Admin:MenuIndex:
    Admin:Config:
    Admin:Dictionary:
    Admin:Discount:
    Admin:Comment:
    Admin:Mailing:
    Admin:Mall:
    Admin:Alza:
    Admin:Mallcatalog:
    Admin:Coupon:
parameters:
  app:
    basketOrderOnOnePage: FALSE
  labels:
    #nazvy cenovych hladin
    com: Cena běžná
    a: Cena e-shop
    b: Cena V.I.P.
    c: Cena B
    d: Cena A
    e: Cena C
    f: Cena D
    #nazvy priznaku u zbozi (akce, novinka, atd.)
    protypid: Akce
    protypid2: Novinka
    protypid3: Tip
    protypid4: Zlaté dny
    protypid5: Vegan
  currency:
    1:
      id: 1
      key: CZK
      code: Kč
      decimals: 0
    2:
      id: 2
      code: €
      key: EUR
      decimals: 1
  hosts:
    localhost:
      curId: 1
    127.0.0.1:
      curId: 1
    goldfitness.cz:
      curId: 1
    www.goldfitness.sk:
      curId: 2
    goldfitnesscz.ekramek.cz:
      curId: 1
    goldfitnesssk.ekramek.cz:
      curId: 2
  googleAnalytics:
    ua:
    domain:
  sms:
    login:
    passw:
  heureka:
    IdOverenoZakazniky: 591d9a803772e16ef8cf2ea85a60948a
    IdOverenoZakaznikyCertifikat:
    KeyMereniKonverzi: B44E4CCE85B0684C090DF2A02B0011E3
  zbozicz:
    IdMereniKonverzi: 5184
    secretKey: 'w0QOyVhYFq30lfKaKch_34S2gtjOuqOy'
  fio:
    account:
    token:
  ulozenka:
    shopId: 14427
    apiKey: '9wmUPz4eQQhZJ7o5qeOKP06R8'
  ulozenka_mall:
    shopId: 14481
    apiKey: 'F7gyFJtfYDZprQQM0N8dUk83k'
  dpd: #DPD API
    login: goldfitness
    passw: GoldDPD2021
    payerId: 5275106
    senderAddressId: ********
    serviceId: 109
    parcelShopServiceId: 50101
    status: #login do api na zjišťování statusu
      login: Goldfitnes
      passw: 'X41BbF1+'

  dpdShipping:
    apiKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJpdHVrWGN6N3dQT0Q3VmlMS3ZkblAxRXc3Vk5WTDdCUSJ9.G96xK68zCsflFYMeaa65rWtRgiRLLLZKOCMt5fDAxss'
    customerId: 5275106
    senderAddressId: 56763
    buCode: '015'
    serviceId: 54
    parcelShopServiceId: 55
    mainServiceElementCodes:
        54: #DPD private
           - '001'
           - '013'
        55: #DPDPickup
           - '001'
           - '013'
           - '200'

  wedo: #We|Do API
    login: gold
    passw: GoldP82021*
    apiUrl: 'https://bridge.intime.cz/api-test/v1'
    apiKey:
  zasilkovna: #Zasilkovna API
    key: 'd71b3a93840ef5dd'
    passw: 'd71b3a93840ef5dde0f051884413d678'
    eshopName: 'goldfitness.cz'
  alzaMarketplace:
    supplierId: 1065
    secretKey: "+Vqlg'x,SSx2YA7F"
    branchId: 4504
  payUApi:
    CZK:
      enviroment: secure
      curKey: CZK
      language: cs #jazyk zákazníka
      posId: 1713471
      secondKey: '44af9186225f1a94d5eef6da58999887'
      clientId: 1713471
      clientSecret: '0a53d829965f5f2f14e10fc9e672b3e6'
      notifyUrl: 'https://www.goldfitness.cz/pay-u/notify?rs=s'
    EUR:
      enviroment: secure
      curKey: EUR
      language: sk #jazyk zákazníka
      posId: 1721670
      secondKey: 041bcc822f817b9ae09709097bb8a1c9
      clientId: 1721670
      clientSecret: cb4e6235becfb9099446e9b5a1df1620
      notifyUrl: 'https://www.goldfitness.sk/pay-u/notify?rs=s'
  payU1:
    posId: 132515
    key1: 80e87ad14d75b411e7f578a8210bf7ff
    key2: 13b650a71bdf02a922108f5b21f682e9
    posAuthKey: HidghCb
  payU2:
    posId: 212546
    key1: c04874e4c826762b24c4faca48f1dea0
    key2: c87108c232812b1bfd79f959eed8481d
    posAuthKey: G5JkFoo
  onlinePayTypes:
    c: "Platební kartou - online"
    rf: "eKonto (Raiffeisenbank)"
    kb: "MojePlatba (Komerční banka)"
    cs: "PLATBA 24 (Česká spořitelna)"
    pf: "Fio banka"
    pg: "GE Money Bank"
    mp: "mPeníze (mBank)"
    uc: "UniCredit bank"
    pv: "Sberbank"
    cb: "ČSOB"
    era: "Era banka"
    mo: "Mobito (peníze v mobilu)"
    psc: "PaySec"

  seznamOAuth:
    clientId: '36e76c14e5cffb0779ea6a21d6c5cc072257876efdef72b4'
    clientSecret: '480a7a4e101b9d0bc4398f6fb61abab1f9781a0dad9a894a'
    redirectUri: 'https://www.goldfitness.cz/user/seznam-login'

  googleOAuth:
    clientId: '*************-ig8t1bu91rn761r4bnq670d2e8abtc3q.apps.googleusercontent.com'
    clientSecret: '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'
    redirectUri: 'https://www.goldfitness.cz/user/google-login'

  appleOAuth:
    clientId: ''
    clientSecret: ''
    redirectUri: 'https://www.goldfitness.cz/user/apple-login'

  database:
    driver: mysqli
    host: 127.0.0.1
    database: goldfitnesscz1
    username: goldfitness.cz
    password: h9je4N3A
    profiler: false
    explain: false

  geoip:
    dbPath: %tempDir%/geoip/database
    account_id: 1177812
    license_key: ****************************************

extensions:
  mobileDetect: IPub\MobileDetect\DI\MobileDetectExtension
  visualPaginator: IPub\VisualPaginator\DI\VisualPaginatorExtension

services:
  - Classes\NeonParametersRepository(@container::getParameters())

  myTemplateHelpers:
    factory: \TemplateFilters( %wwwDir%)

  connection:
    class: DibiConnection
    factory: dibi::connect(%database%)
    run: TRUE
    setup:
      - @connection.panel::register(@connection)

  connection.panel:
      class: Dibi\Bridges\Tracy\Panel

  authenticator:
    class: MyAuthenticator

  nette.authorizator:
    setup:
     #guest
      - @self::allow( guest, 'Admin:Admin', ['login', 'logout', 'default'])
     #admin - moduly
      - @self::allow( admin, 'Admin:Admin', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:User', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Catalog', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Product', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Export', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Import', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Order', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:DeliveryMode', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Manufacturer', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Page', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Article', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Menu', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:New', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:MenuIndex', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Config', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Discount', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Comment', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Mailing', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Mall', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Alza', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Mallcatalog', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Coupon', Nette\Security\Permission::ALL)
     #admin - akce
      - @self::allow( admin, 'Admin:Admin', ['changeForeign', 'orderSetAuthor', 'multiaccounts'])
     #editor - moduly
      - @self::allow( editor, 'Admin:Admin', ['login', 'logout', 'default', 'edit'])
      - @self::allow( editor, 'Admin:Comment', Nette\Security\Permission::ALL)
      - @self::allow( editor, 'Admin:Article', Nette\Security\Permission::ALL)