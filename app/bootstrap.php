<?php

require __DIR__ . '/../vendor/autoload.php';

//kate<PERSON><PERSON> nav<PERSON>c (<PERSON><PERSON><PERSON>, d<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>)
const CAT_OTHERS = '19,20,97';
const MAIN_CATIDS = "(catpathids LIKE '|84|%' OR catpathids LIKE '|85|%' OR catpathids LIKE '|57|%')";

define('IS_PRODUCTION', $_SERVER["SERVER_NAME"] != '127.0.0.1' && $_SERVER["SERVER_NAME"] != 'localhost');

// Configure application
$configurator = new Nette\Configurator;

// Enable Nette Debugger for error visualisation & logging
Tracy\Debugger::$logDirectory = __DIR__ . '/../log';
//Tracy\Debugger::setSessionStorage(new Tracy\NativeSession);

//Tracy\Debugger::enable(['**********', '127.0.0.1']);
<PERSON>\Debugger::enable();

//die(print_r($_SERVER, TRUE));

// Enable RobotLoader - this will load all classes automatically
$configurator->setTempDirectory(__DIR__ . '/../temp');
$configurator->createRobotLoader()
  ->addDirectory(__DIR__)
  ->addDirectory(__DIR__ . '/../libs')
  ->register();

// Create Dependency Injection container from config.neon file
$configurator->addConfig(__DIR__ . '/config/config.neon');
$configurator->addConfig(__DIR__ . '/config/config.local.neon');
$container = $configurator->createContainer();

$container->getByType('Dibi\Connection');

dibi::setConnection($container->getByType('Dibi\Connection'));

dibi::query("SET sql_mode=(SELECT REPLACE(@@sql_mode, 'ONLY_FULL_GROUP_BY', ''))");

//Setup application router
$router = new Nette\Application\Routers\RouteList;

$router[] = new Nette\Application\Routers\Route('index.php', array(
    'module' => 'Front',
    'presenter' => 'Homepage',
    'action' => 'default',
), Nette\Application\Routers\Route::ONE_WAY);

// Admin
$router[] = new Nette\Application\Routers\Route('administrace/<presenter>/<action>', array(
  'module' => 'Admin',
  'presenter' => 'Admin',
  'action' => 'default',
));

// routa pro detail zbozi
$router[] = new Nette\Application\Routers\Route('<key>-d<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Product',
    'action' => 'detail',
));
//jednosmerna routa puvodni
$router[] = new Nette\Application\Routers\Route('<key>.html,det,<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Product',
    'action' => 'detailOld',
), Nette\Application\Routers\Route::ONE_WAY);

// routa pro detail katalogu
$router[] = new Nette\Application\Routers\Route('<key>-k<id [0-9]+>/[<path .+>/]', array(
    'module' => 'Front',
    'presenter' => 'Catalog',
    'action' => 'detail',
    'path' => array(
      Nette\Application\Routers\Route::VALUE => NULL,
      Nette\Application\Routers\Route::FILTER_IN => NULL,
      Nette\Application\Routers\Route::FILTER_OUT => NULL,
  ),
));

//jednosmerna routa puvodni
$router[] = new Nette\Application\Routers\Route('<key>.html,kat,<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Catalog',
    'action' => 'detailOld',
), Nette\Application\Routers\Route::ONE_WAY);

// routa pro textove stranky
$router[] = new Nette\Application\Routers\Route('<key>-t<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Page',
    'action' => 'detail',
));

// routa pro články
$router[] = new Nette\Application\Routers\Route('<key>-c<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Article',
    'action' => 'detail',
));
//jednosmerna routa puvodni
$router[] = new Nette\Application\Routers\Route('<key>.html,rec,<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Article',
    'action' => 'detailOld',
), Nette\Application\Routers\Route::ONE_WAY);

// routa pro vyrobce
$router[] = new Nette\Application\Routers\Route('<key>-m<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Manufacturer',
    'action' => 'detail',
));
//jednosmerna routa puvodni
$router[] = new Nette\Application\Routers\Route('vyrobce-<key>.html', array(
    'module' => 'Front',
    'presenter' => 'Manufacturer',
    'action' => 'detailOld',
), Nette\Application\Routers\Route::ONE_WAY);


// routa pro mailing
$router[] = new Nette\Application\Routers\Route('c/<typid [0-9]+>[/<mamid [0-9]+>][/<usrid [0-9]+>][/<par1>]', array(
    'module' => 'Front',
    'presenter' => 'Mailing',
    'action' => 'click',
));

$router[] = new Nette\Application\Routers\Route('d/<mamid [0-9]+>/<usrid [0-9]+>/<key>', array(
    'module' => 'Front',
    'presenter' => 'Mailing',
    'action' => 'detail',
));


/*

// routa pro uzivatele
$router[] = new Nette\Application\Routers\Route('muj-ucet/<action>[/<id [0-9]+>]', array(
    'module' => 'Front',
    'presenter' => 'Nette\Security\User',
    'action' => 'default',
));

// routa pro detail zbozi
$router[] = new Nette\Application\Routers\Route('<key>-d<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Product',
    'action' => 'detail',
));

//jednosmerna routa pro zbozi z puvodniho webu
$router[] = new Nette\Application\Routers\Route('kolo-<key>-<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Product',
    'action' => 'detail',
), Nette\Application\Routers\Route::ONE_WAY);

// routa pro detail katalogu
$router[] = new Nette\Application\Routers\Route('<key>-k<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Catalog',
    'action' => 'detail',
));
*/

$presenters = array(
  'kosik' => 'Basket',
  'vyrobce' => 'Manufacturer',
  'hledani' => 'Search',
);

$actions = array(
  'novy' => 'add',
  'upravit' => 'edit',
  'prihlasit' => 'login',
  'odhlasit' => 'logout',
  'detail-objednavky' => 'order',
  'kontaktni-informace' => 'orderContact',
  'zpusob-dodani' => 'orderDelMode',
  'objednavka-odeslana' => 'accepted',
  'novinky' => 'news',
);

// deklarace obecné dvousměrné routy s cool-url tvarem
$router[] = new Nette\Application\Routers\Route('<presenter>/<action>[/<id>]', array(
    'module' => 'Front',
    'presenter' => array(
      Nette\Application\Routers\Route::VALUE => 'Homepage',
      Nette\Application\Routers\Route::FILTER_TABLE => $presenters,
    ),
    'action' => array(
      Nette\Application\Routers\Route::VALUE => 'default',
      Nette\Application\Routers\Route::FILTER_TABLE => $actions,
    ),
    'id' => NULL,
));

$container->addService('router', $router);

return $container;
