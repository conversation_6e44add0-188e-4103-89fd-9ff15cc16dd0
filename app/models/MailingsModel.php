<?php
namespace Model;
use <PERSON>te, dibi;
                
class MailingsModel extends BaseModel {
  protected $tableName = "mailings";
  protected $fieldPrefix = "mam";
  
  public $usersTable = "users"; 


  public function getUsersSql($mailing): array {
    //Poskládám cenové hladiny
    $priceLevels = [];
    if ($mailing->mampricea == 1) $priceLevels[] = 'a';
    if ($mailing->mampriceb == 1) $priceLevels[] = 'b';
    if ($mailing->mampricec == 1) $priceLevels[] = 'c';
    if ($mailing->mampriced == 1) $priceLevels[] = 'd';
    if ($mailing->mampricee == 1) $priceLevels[] = 'e';


    $sql[] = "SELECT * FROM users";
    if ($mailing->mambuynot == 2 && (!empty($mailing->mambuycatid) || !empty($mailing->mambuydays))) {
      $sql[] = "INNER JOIN orders ON (usrmail=ordmail)
        INNER JOIN orditems ON (ordid=oriordid)
        INNER JOIN products ON (proid=oriproid)
        INNER JOIN catplaces ON (proid=capproid)
        INNER JOIN catalogs ON (catid=capcatid)";
    }

    $sql[] = " WHERE usrstatus=0 AND usrmaillist=%i";
    $sql[] = $mailing->mammaillist;

    if ($mailing->mambuynot == 1) {
      $sql[] = " AND NOT EXISTS (SELECT 1 FROM orders WHERE ordmail=usrmail AND orddatec + INTERVAL %i";
      $sql[] = $mailing->mambuydays;
      $sql[] = " DAY>NOW())";
      $sql[] = " AND EXISTS (SELECT 1 FROM orders WHERE ordmail=usrmail AND ordstatus NOT IN (5,9,10)) ";
    }

    if (count($priceLevels) > 0) {
      $sql[] = " AND usrprccat IN (%s)";
      $sql[] = $priceLevels;
    }

    if ($mailing->mambuynot == 2 && (!empty($mailing->mambuycatid) || !empty($mailing->mambuydays))) {
      $sql[] = "AND ordstatus NOT IN (5,9,10)";
      if ($mailing->mambuycatid > 0) {
        $sql[] = " AND catpathids LIKE '%|$mailing->mambuycatid|%'";
      }
      if ($mailing->mambuydays > 0) {
        $sql[] = " AND orddatec + INTERVAL %i";
        $sql[] = $mailing->mambuydays;
        $sql[] = " DAY>NOW()";
      }
      $sql[] = " GROUP BY usrid";
    }
    $sql[] = "ORDER BY usrid";

    return $sql;
  }

  public function getUsers($mailing): array {
    return dibi::fetchAll($this->getUsersSql($mailing));
  }

  /**
  * ciselnik admstatus
  * @return array
  */
  public function getEnumMamStatus() {
    return array(
      0 => 'Vytvořený',
      1 => 'Připravený k odeslání',
      2 => 'Odesílá se',
      4 => 'Pozastavený',
      3 => 'Odmailovaný',
    );
  }
}
?>