<?php
namespace Model;
use Nette, dibi;

class MenusModel extends BaseModel {

  protected $tableName = "menus";
  protected $fieldPrefix = "men";

  /**
  * pregeneruje cesty ukladane v katalogu
  *
  */

  private function rebuildPaths() {
    $menus = $this->model->getMenusModel();
    $result = dibi::query("SELECT * FROM menus");
    foreach ($result as $n => $row) {
      $pathstr = "";
      $pathidsstr = "";
      $path = array();
      $pathids = array();
      $pathlevel = 0;
      if ($row->menmasid > 0) {
        $lastmasid = $row->menmasid;
        do {
          $pathlevel ++;
          $catalog = $menus->load($lastmasid);
          $lastmasid = $catalog->menmasid;
          $path[$catalog->menid] = $catalog->menname;
          $pathids[$catalog->menid] = $catalog->menid;
        } while ($lastmasid > 0);
        $path = array_reverse($path, True);
        $pathids = array_reverse($pathids, True);
        $pathstr = "|".implode("|", $path);
        $pathidsstr = "|".implode("|", $pathids);
      }
      $pathlevel ++;
      $pathstr .= "|$row->menname|";
      $pathidsstr .= "|$row->menid|";

      //updatnu prislusny catalog pokud se neco zmenilo
      if ($row->menlevel != $pathlevel || $row->menpath != $pathstr || $row->menpathids != $pathidsstr) {
        $values = array();
        $values["menlevel"] = $pathlevel;
        $values["menpath"] = $pathstr;
        $values["menpathids"] = $pathidsstr;
        $menus->update($row->menid, $values, false);
      }
    }
  }

  public function insert($data) {
    $ret = parent::insert($data);
    //pregeneruju cesty
    if ($ret) $this->rebuildPaths();
    return ($ret);
  }

  public function update($id, $data, $setDateU = Null) {
    $ret = parent::update($id, $data, $setDateU);
    //pregeneruju cesty
    if ($ret) $this->rebuildPaths();
    return ($ret);
  }

  public function delete($id) {
    $ret = parent::delete($id);
    //pregeneruju cesty
    if ($ret) $this->rebuildPaths();
    return ($ret);
  }

  /********************* ciselniky *********************/

  /**
  * ciselnik catstatus
  * @return array
  */
  public function getEnumMenStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  /**
  * Vraci strom katalogu (vhodne pro combo box)
  * CACHED
  * @return array
  */
  public function getEnumMenuCombo($emptytext="") {
    $nameAdd = (empty($emptytext) ? "" : "_et");
    $cache = $this->cacheGet('MenuCombo'.$nameAdd);
    $arr = array();
    if (!$cache) {
      unset($cache);
      $arr[0] = "Kořenová úroveň";
      $this->addEnumMenuComboLevel(0, $arr);
      $cache = $arr;
    }
    if ($emptytext != "") {
      $cache =  array_merge(array('' => $emptytext), $cache);
    }
    $this->cacheSave('MenuCombo'.$nameAdd, $arr);
    return $cache;
  }

  /**
  * vraci jednu vetev katalogu
  *
  * @param integer $menmasid
  * @param array $arr
  */
  private function addEnumMenuComboLevel($menmasid, &$arr) {
    $res = dibi::query("SELECT * FROM menus WHERE menmasid=%i", $menmasid);

    while ($row = $res->fetch()) {
      //zjistim v jake urovni zanoreni jsem
      $lev = $row->menlevel + 1;
      $arr[$row->menid] = str_repeat('-', $lev * 2).' '.$row->menname;
      $this->addEnumMenuComboLevel($row->menid, $arr);
    }
  }

  /**
  * Vraci strom katalogu
  * CACHED
  * @return array
  */
  public function getEnumMenuTree($rootId=0) {
    $key = 'MenuTree_'.$rootId;
    $cache = $this->cacheGet($key);
    if (!$cache) {
      unset($cache);
      $cache = array();
      $cache = $this->addEnumMenuTreeLevel($rootId);
      $this->cacheSave('$key', $cache);
    }
    return $cache;
  }

  /**
  * vraci jednu vetev katalogu
  *
  * @param integer $menmasid
  * @param array $arr
  */
  private function addEnumMenuTreeLevel($menmasid) {
    $items = dibi::query("SELECT * FROM menus LEFT JOIN pages ON (mensrctype='page' AND pagid=mensrcid) WHERE menmasid=$menmasid ORDER BY menorder")
      ->fetchAssoc('menid');

    $arr = array();
    foreach ($items as $key => $row) {
      $arr[$key]["data"] = $row;
      $arr[$key]["subitems"] = $this->addEnumMenuTreeLevel($key);
    }
    return $arr;
  }

    /**
  * ciselnik typ stranky
  * @return array
  */
  public function getEnumMenPagId() {
    $items = dibi::query("SELECT * FROM pages ORDER BY pagtypid")
      ->fetchPairs('pagid', 'pagname');
    return $items;
  }
}
