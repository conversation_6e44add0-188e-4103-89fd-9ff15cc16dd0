<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class PropackagesModel extends BaseModel {

  protected $tableName = "propackages";
  protected $fieldPrefix = "pac";

  public function updateSet($data) {
    $pacid = (int)dibi::fetchSingle('SELECT pacid FROM propackages WHERE pacproid=%i', $data['pacproid'], ' AND pacsubproid=%i', $data['pacsubproid']);
    if ($pacid > 0) {
      $this->update($pacid, $data);
    } else {
      $pacid = $this->insert($data);
    }
    return $pacid;
  }
}
