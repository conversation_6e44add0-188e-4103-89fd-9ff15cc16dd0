<?php
namespace Model;
use dibi;
use <PERSON>bi\Exception;
use DibiException;
use Nette;

class ProductsModel extends BaseModel {

  protected $tableName = "products";
  protected $fieldPrefix = "pro";

  public function getDataSource($sql="") {
    if ($sql == "") $sql = $this->getSql();
    return dibi::dataSource($sql);
  }

  public function getSqlFields(): string {
    return "$this->tableName.*, 
      proname AS proname1, concat(proname, ' ', proname2) AS proname, ".($this->curId == 1 ? 'prodelfree' : '0 AS prodelfree'). ',
      IF(proprice' .$this->curId.$this->prccat. '>0,proprice' .$this->curId.$this->prccat. ',proprice' .$this->curId. "a) AS proprice,
      IF('" .$this->prccat. "' = 'a' || '" .$this->prccat. "' = 'b', progifts, NULL) AS progifts,
      proprice" .$this->curId."com AS propricecom";
  }

  public function getSql() {
    return "
      SELECT " . $this->getSqlFields() . ", manname 
      FROM $this->tableName
      INNER JOIN manufacturers ON (promanid=manid)
    ";
  }
  
  public function getPriceField($sql="") {
    if ($sql == "") $sql = "SELECT *, IF(proprice".$this->curId.$this->prccat.">0,proprice".$this->curId.$this->prccat.",proprice".$this->curId."a) AS proprice FROM $this->tableName";
    return dibi::dataSource($sql);
  }


  public function getVariants($proId) {
    return dibi::fetchAll($this->getSqlList("prostatus=0 AND promasid=".$proId, "proaccess, proprice, proorder, proname"));
  }

  /**
   * vrátí produkty dle statiskiky prodejnosti
   *
   * @param $catalog
   * @param int $exclProId - tento produkt tam nechci
   * @return array
   */
  public function getSaleStatsProducts($catalog, $exclProId=0) {
    $catalogSaleStats = [];
    if (!empty($catalog->catsalestat)) {
      $proCodesList = explode(',',trim($catalog->catsalestat, ','));
      $cnt = 0;
      foreach ($proCodesList as $proId) {
        $cnt++;
        $proId = trim($proId);
        if (!empty($proId)) {
          $item = $this->load($proId);
          if ($item && $item->proid != $exclProId) $catalogSaleStats[] = $item;
        }
        if ($cnt>=3) break;
      }
    }
    return $catalogSaleStats;
  }

  /**
  * prepocita statistiku prodejnosti
  * 
  */
  public function recalcSaleStat() { 
    dibi::query("DELETE FROM products_salestat");
    
    //x dni stara statistika
    $daysArr = array(10, 30);
    foreach ($daysArr as $days) {
      dibi::query("                     
    INSERT INTO products_salestat 
    SELECT oriproid, SUM(oriqty), NULL, $days, NOW(), NULL 
    FROM orditems 
    INNER JOIN orders ON (oriordid=ordid)
    WHERE oritypid=0 AND 
    ordstatus IN (3,4) AND
    ((COALESCE(orddateu, orddatec) + INTERVAL $days DAY) >= CURDATE())
    GROUP BY oriproid
    ");
    }
    
    //nactu vsechny produkty ze statistik a nactu jejich zarazeni do katalogu
    $rows = dibi::fetchAll("
    SELECT prsproid, catpathids 
    FROM products_salestat
    INNER JOIN products AS pro ON (prsproid=pro.proid) 
    LEFT JOIN products AS promas ON (pro.promasid=promas.proid) 
    INNER JOIN catplaces ON (capproid=COALESCE(promas.proid,pro.proid)) 
    INNER JOIN catalogs ON (capcatid=catid)
    GROUP BY prsproid");
    foreach ($rows as $key => $row) {
      dibi::query("UPDATE products_salestat SET prscatpathids=%s", $row->catpathids, " WHERE prsproid=%i", $row->prsproid);
    }                                                      
    
    $catalog = $this->model->getCatalogsModel();
    $catalog->cacheClean();
  }

  /**
   * aktualizuje zarazeni do katalogu
   *
   * @param integer $proId
   * @param $catIds
   * @throws Exception
   */
  public function updateCatPlace(int $proId, $catIds) {
    $idlst = "";
    $catplace = $this->model->getCatPlacesModel();
    foreach ($catIds as $value) {
      $idlst .= $value.",";
      //zjistim jestli zaznam existuje
      $cnt = (integer)dibi::fetchSingle("SELECT count(*) FROM catplaces WHERE capcatid=$value AND capproid=$proId");
      //zaznam neexistuje
      if ($cnt == 0) {
        $vals = array('capcatid' => $value, 'capproid' => $proId);
        $catplace->insert($vals);
      }
    }
    $idlst = trim($idlst, ',');
    //vsechny ostatni zaznamy vymazu
    dibi::query("DELETE FROM catplaces WHERE capproid=$proId".($idlst != "" ? " AND capcatid NOT IN ($idlst)" : ""));
  }

  /**
   * kontrola duplicity kodu zbozi
   *
   * @param integer $id - id polozky pokud se jedna o editaci
   * @param string $value - kod zbozi
   * @throws Exception
   */
  private function checkDuplicityProCode($id, $value) {
    $cnt = dibi::fetchSingle("SELECT COUNT(*) AS cnt FROM $this->tableName WHERE procode='$value'".($id>0 ? " AND proid!=$id" : ""));
    if ($cnt > 0) throw New \Exception("Tento kód zboží již existuje.");
  }

  public function save(&$id, $data, $setDateU = True) {
    if ($data["procode"] != "") $this->checkDuplicityProCode($id, $data["procode"]);
    return parent::save($id, $data, $setDateU);
  }

  public function update($id, $data,$setDateU = True) {
    if (!empty($data["prokey"])) {
      $data["prokey"] = Nette\Utils\Strings::webalize($data["prokey"]);
    }
    $pp = $this->load($id);
    //zjistím jestli se mění stav skladu
    $logVals = array();

    if (isset($data["proqty"]) && (int)$data["proqty"] !== (int)$pp->proqty) {
      $logVals["prlqty"] = $data["proqty"];
      $logVals["prlqty_bef"] = $pp->proqty;
    }
    if (isset($data["proqty_store"]) && (int)$data["proqty_store"] !== (int)$pp->proqty_store) {
      $logVals["prlqty_store"] = $data["proqty_store"];
      $logVals["prlqty_store_bef"] = $pp->proqty_store;
    }
    if (isset($data["proqty_shop1"]) && (int)$data["proqty_shop1"] !== (int)$pp->proqty_shop1) {
      $logVals["prlqty_shop1"] = $data["proqty_shop1"];
      $logVals["prlqty_shop1_bef"] = $pp->proqty_shop1;
    }
    if (isset($data["proqty_shop2"]) && (int)$data["proqty_shop2"] !== (int)$pp->proqty_shop2) {
      $logVals["prlqty_shop2"] = $data["proqty_shop2"];
      $logVals["prlqty_shop2_bef"] = $pp->proqty_shop2;
    }
    if (isset($data["proaccess"]) && (int)$data["proaccess"] !== (int)$pp->proaccess) {
      $logVals["prlaccess"] = $data["proaccess"];
      $logVals["prlaccess_bef"] = $pp->proaccess;
    }

    if (count($logVals) > 0) {
      $logVals["prlproid"] = $id;
      $logVals["prlurl"] = $_SERVER["REQUEST_URI"];
      $logVals["prldatec"] = new \DateTime();

      //dibi::insert('products_log', $logVals)->execute(dibi::IDENTIFIER);
    }


    $ret = parent::update($id, $data, $setDateU);
    //zjistim jestli se zmenila hodnota promasid
    if (isset($data["promasid"])) {
      if ($data["promasid"] > 0) $this->refreshMasterStatus($data["promasid"]);
      if ($pp->promasid > 0) $this->refreshMasterStatus($pp->promasid);
    }
    return $ret;
  }

  public function insert($data) {
    if (!empty($data["prokey"])) $data["prokey"] = Nette\Utils\Strings::webalize($data["prokey"]);
    $ret = parent::insert($data);
    //pokud vklada novou polozku jako podrizenou refreshnu masterstatus
    if (isset($data["promasid"])) {
      if ($data["promasid"] > 0) $this->refreshMasterStatus($data["promasid"]);
    }
    return $ret;
  }

  public function refreshMasterStatus($proid) {
    $cnt = dibi::fetchSingle("SELECT COUNT(proid) AS cnt FROM products WHERE promasid=%i", $proid);
    $proIsMaster = (int)($cnt > 0);
    $vals = array('proismaster'=>$proIsMaster);
    $this->update($proid, $vals);
  }


  /**
   * aktualizuje u master položky výčet variant [ID|Název varianty]
   *
   * @param $proId
   * @throws DibiException
   */
  public function recalcProductVariants($proId) {
    $pro = dibi::fetch("SELECT proid, promasid, proismaster, provariants FROM products WHERE proid=%i", $proId);
    $proMasId = 0;
    if ($pro) {
      if ((int)$pro->proismaster === 1) {
        //je to master položka
        $proMasId = $pro->proid;
      } else if ((int)$pro->promasid > 0) {
        $proMasId = $pro->promasid;
      }
      if ($proMasId > 0) {
        //mám id master položky - aktualizuji varianty
        $rows = dibi::fetchAll("SELECT proid, proname FROM products WHERE proaccess=0 AND promasid=%i", $proMasId, " ORDER BY proorder, proname");
        $itemsArr = array();
        foreach ($rows as $row) {
          //zjistím název varianty
          $pos = strrpos($row->proname, " - ");
          if ($pos !== FALSE) {
            $var = trim(substr($row->proname, $pos + 1), '-');
            $itemsArr[] = $row->proid . "|" . trim($var);
          }
        }
        $proVariants = "";
        if (count($itemsArr) > 0) {
          $proVariants = implode("\n", $itemsArr);
        }
        $this->update($proMasId, array("provariants" => $proVariants));
      }
    }
  }

  /**
   * aktualizuje dostupnost master položky podle variant
   *
   * @param $promasid
   * @return bool
   * @throws DibiException
   */
  public function updateMasterStore($promasid) {
    $row = dibi::fetch("
      SELECT 
        SUM(proqty) AS proqty,
        SUM(proqty_store) AS proqty_store,
        SUM(proqty_shop1) AS proqty_shop1,
        SUM(proqty_shop2) AS proqty_shop2,
        MIN(proprice1com) AS proprice1com,   
        MIN(proprice1a) AS proprice1a,   
        MIN(proprice1b) AS proprice1b,   
        MIN(proprice1c) AS proprice1c,   
        MIN(proprice1d) AS proprice1d,
        MIN(proprice1e) AS proprice1e,
        MIN(proprice1f) AS proprice1f,
        MIN(proprice2com) AS proprice2com,
        MIN(proprice2a) AS proprice2a,   
        MIN(proprice2b) AS proprice2b,   
        MIN(proprice2c) AS proprice2c,   
        MIN(proprice2d) AS proprice2d,   
        MIN(proprice2e) AS proprice2e,   
        MIN(proprice2f) AS proprice2f   
      FROM products 
      WHERE prostatus=0 AND proaccess=0 AND promasid=%i", $promasid
    );

    if ($row->proprice1a == 0) {
      $row = dibi::fetch("
        SELECT 
          SUM(proqty) AS proqty,
          SUM(proqty_store) AS proqty_store,
          SUM(proqty_shop1) AS proqty_shop1,
          SUM(proqty_shop2) AS proqty_shop2,
          MIN(proprice1com) AS proprice1com,   
          MIN(proprice1a) AS proprice1a,   
          MIN(proprice1b) AS proprice1b,   
          MIN(proprice1c) AS proprice1c,   
          MIN(proprice1d) AS proprice1d,
          MIN(proprice1e) AS proprice1e,
          MIN(proprice1f) AS proprice1f,
          MIN(proprice2com) AS proprice2com,
          MIN(proprice2a) AS proprice2a,   
          MIN(proprice2b) AS proprice2b,   
          MIN(proprice2c) AS proprice2c,   
          MIN(proprice2d) AS proprice2d,   
          MIN(proprice2e) AS proprice2e,   
          MIN(proprice2f) AS proprice2f   
        FROM products 
        WHERE prostatus=0 AND promasid=%i", $promasid
      );
    }

    $vals = array();

    $pro = $this->load($promasid);
    if (!$pro) {
      return FALSE;
    }

    $storeUpdated = FALSE;
    if ((int)$pro->proqty != (int)$row->proqty) {
      $vals['proqty'] = (int)$row->proqty;
      $storeUpdated = TRUE;
    }
    if ((int)$pro->proqty_store != (int)$row->proqty_store) {
      $vals['proqty_store'] = (int)$row->proqty_store;
      $storeUpdated = TRUE;
    }
    if ((int)$pro->proqty_shop1 != (int)$row->proqty_shop1) {
      $vals['proqty_shop1'] = (int)$row->proqty_shop1;
      $storeUpdated = TRUE;
    }
    if ((int)$pro->proqty_shop2 != (int)$row->proqty_shop2) {
      $vals['proqty_shop2'] = (int)$row->proqty_shop2;
      $storeUpdated = TRUE;
    }

    $proaccess = $row->proqty > 0 ? 0 : 100;
    if ((int)$pro->proaccess != $proaccess) {
      $vals['proaccess'] = $proaccess;
      $storeUpdated = TRUE;
    }

    if ($storeUpdated) {
      $vals['promalldateavu'] = NULL;
    }

    if ((float)$pro->proprice1com != (float)$row->proprice1com) {
      $vals['proprice1com'] = (float)$row->proprice1com;
    }
    if ((float)$pro->proprice1a != (float)$row->proprice1a) {
      $vals['proprice1a'] = (float)$row->proprice1a;
    }
    if ((float)$pro->proprice1b != (float)$row->proprice1b) {
      $vals['proprice1b'] = (float)$row->proprice1b;
    }
    if ((float)$pro->proprice1c != (float)$row->proprice1c) {
      $vals['proprice1c'] = (float)$row->proprice1c;
    }
    if ((float)$pro->proprice1d != (float)$row->proprice1d) {
      $vals['proprice1d'] = (float)$row->proprice1d;
    }
    if ((float)$pro->proprice1e != (float)$row->proprice1e) {
      $vals['proprice1e'] = (float)$row->proprice1e;
    }
    if ((float)$pro->proprice1f != (float)$row->proprice1f) {
      $vals['proprice1f'] = (float)$row->proprice1f;
    }
    if ((float)$pro->proprice2com != (float)$row->proprice2com) {
      $vals['proprice2com'] = (float)$row->proprice2com;
    }
    if ((float)$pro->proprice2a != (float)$row->proprice2a) {
      $vals['proprice2a'] = (float)$row->proprice2a;
    }
    if ((float)$pro->proprice2b != (float)$row->proprice2b) {
      $vals['proprice2b'] = (float)$row->proprice2b;
    }
    if ((float)$pro->proprice2c != (float)$row->proprice2c) {
      $vals['proprice2c'] = (float)$row->proprice2c;
    }
    if ((float)$pro->proprice2d != (float)$row->proprice2d) {
      $vals['proprice2d'] = (float)$row->proprice2d;
    }
    if ((float)$pro->proprice2e != (float)$row->proprice2e) {
      $vals['proprice2e'] = (float)$row->proprice2e;
    }
    if ((float)$pro->proprice2f != (float)$row->proprice2f) {
      $vals['proprice2f'] = (float)$row->proprice2f;
    }

    if (count($vals) > 0) {
      $this->update($promasid, $vals);
    }
  }

  /**
   * aktualizuje stav skladu u sestavy
   *
   * @param $pacproid
   * @param bool $updateMasterStore
   * @throws DibiException
   */
  public function updatePackageQty($pacproid, $updateMasterStore = TRUE) {
    if ($pacproid == 2518) {
      //echo "";
    }

    $items = dibi::fetchAll('SELECT pacsubproid, pacqty FROM propackages WHERE pacproid=%i', $pacproid);
    $vals = array();
    $vals['proqty_shop1'] = 0;
    $vals['proqty_shop2'] = 0;
    $vals['proqty_store'] = 0;
    $qtys = array();
    foreach ($items as $key => $row) {
      $packItem = dibi::fetch("SELECT 
        FLOOR(proqty_shop1/".$row->pacqty.") AS proqty_shop1, 
        FLOOR(proqty_shop2/".$row->pacqty.") AS proqty_shop2, 
        FLOOR(proqty_store/".$row->pacqty.") AS proqty_store 
        FROM products WHERE proid=%i", $row->pacsubproid);

      $qtys['proqty_shop1'][$row->pacsubproid] = $packItem->proqty_shop1;
      $qtys['proqty_shop2'][$row->pacsubproid] = $packItem->proqty_shop2;
      $qtys['proqty_store'][$row->pacsubproid] = $packItem->proqty_store;
    }
    foreach ($qtys as $storeId => $items) {
      $qtyMin = NULL;
      foreach ($items as $qty) {
        if ($qtyMin === NULL) {
          $qtyMin = $qty;
        } else {
          $qtyMin = min($qtyMin, $qty);
        }
      }
      $vals[$storeId] = $qtyMin;
    }

    $vals['proqty'] = $vals['proqty_shop1'] + $vals['proqty_shop2'] + $vals['proqty_store'];
    $vals['proaccess'] = ($vals['proqty'] > 0 ? 0 : 100);

    //updatnu jen to co se změni
    $pro = $this->load($pacproid);
    if (($pro->proaccess != $vals['proaccess'] && $vals['proaccess'] == 0) || ($vals['proqty'] != $pro->proqty)) {
      //změnila se dostupnost, promalldateavu IS NULL - vyšší priorita pro aktualizaci dat
      $vals['promalldateavu'] = NULL;
      $this->update($pacproid, $vals);
    }

    //zjistim jestli položka je varianta
    if ($updateMasterStore) {
      $promasid = dibi::fetchSingle("SELECT promasid FROM products WHERE proid=%i", $pacproid);
      if ($promasid > 0) {
        //aktualizuji stav skladu master položky
        $this->updateMasterStore($promasid);
      }
    }
  }

  /**
   * odečítá ze skladu
   *
   * @param $product
   * @param $qty
   * @param bool $updatePackages
   * @throws DibiException
   */
  public function updateQty($product, $qty, $updatePackages = TRUE) {
    if ($product->prostocktypep === 'set') {
      //projdu všechny položky setu a aktualizuji QTY
      $rows = dibi::fetchAll('
        SELECT * FROM products
        INNER JOIN propackages ON pacsubproid=proid
        WHERE pacproid=%i', $product->proid
      );
      //aktualizuji položky setu
      foreach ($rows as $row) {
        $this->updateQty($row, $qty * $row->pacqty, FALSE);
      }
      //aktualizuji všechny sety ve kterých se tyto položky nacházejí
      $setIds = array();
      foreach ($rows as $row) {
        if (!isset($setIds[$row->pacproid])) {
          $this->updatePackageQty($row->pacproid);
          $setIds[$row->pacproid] = $row->pacproid;
        }
      }
    } else {
      //aktualizace stavu skladu samostatné položky
      $cnt = $qty;
      $vals["proqty"] = max($product->proqty - $cnt, 0);
      if ($vals["proqty"] == 0) {
        $vals["proqty_store"] = 0;
        $vals["proqty_shop1"] = 0;
        $vals["proqty_shop2"] = 0;
        $vals["proaccess"] = 100;
      } else {
        //odečtu ze skladu store
        if ($cnt <= $product->proqty_store) {
          $vals["proqty_store"] = $product->proqty_store - $cnt;
          $cnt = 0;
        } else {
          $vals["proqty_store"] = 0;
          $cnt -= $product->proqty_store;
        }
        if ($cnt > 0) {
          //odečtu ze skladu shop1
          if ($cnt <= $product->proqty_shop1) {
            $vals["proqty_shop1"] = $product->proqty_shop1 - $cnt;
            $cnt = 0;
          } else {
            $vals["proqty_shop1"] = 0;
            $cnt -= $product->proqty_shop1;
          }
        }
        if ($cnt > 0) {
          //odečtu ze skladu shop2
          if ($cnt <= $product->proqty_shop2) {
            $vals["proqty_shop2"] = $product->proqty_shop2 - $cnt;
          } else {
            $vals["proqty_shop2"] = 0;
          }
        }
      }

      $this->update($product->proid, $vals);

      //aktualizuji všechny sety ve kterých se tato položka nachází
      if ($updatePackages) {
        $rows = dibi::fetchAll('
          SELECT pacproid FROM propackages
          WHERE pacsubproid=%i', $product->proid, ' GROUP BY pacproid'
        );
        foreach ($rows as $row) {
          $this->updatePackageQty($row->pacproid);
        }
      }
      if ($product->promasid > 0) {
        //aktualizuji stav skladu master položky
        $this->updateMasterStore($product->promasid);
        $this->recalcProductVariants($product->promasid);
      }
    }
  }

  /**
   * @param $promasid
   * @throws DibiException
   */
  public function copyParamsToVariants($promasid) {
    $variants = dibi::fetchAll('SELECT proid FROM products WHERE promasid=%i', $promasid);
    //pokud nemá varianty vyypadnu
    if (count($variants) == 0) {
      return;
    }

    $params = dibi::fetchAll('select prpid, prpname, prpvalue, prptypid FROM proparams WHERE prpproid=%i', $promasid);

    $prps = $this->model->getProParamsModel();

    foreach ($variants as $variant) {
      foreach ($params as $par) {
        $prpid = (int)dibi::fetchSingle('select prpid FROM proparams WHERE prpproid=%i', $variant->proid, ' AND prpname=%s', $par->prpname);
        if ($prpid > 0) {
          //varianta má parametr, upravím ho
          $prps->update($prpid, array('prpvalue' => $par->prpvalue, 'prptypid' => $par->prptypid));
        } else {
          //varianta nemá par. doplním
          $prps->insert(array('prpproid' => $variant->proid, 'prpname' => $par->prpname, 'prpvalue' => $par->prpvalue, 'prptypid' => $par->prptypid));
        }
      }
    }
  }
  
  public function delete($id) {
    $p = $this->load($id);
    $ret = parent::delete($id);

    //pokud ma master polozku refreshnu jeji master status
    if ($p->promasid > 0) $this->refreshMasterStatus($p->promasid);

    //pokud mazu master polozku odparuju podrizene polozky
    dibi::query("UPDATE products SET promasid=0 WHERE promasid=%i", $p->proid);

    //vymazu prilohy
    dibi::query("DELETE FROM attachments WHERE ataproid=%i", $id);
    //vymazu zarazeni do catalogu
    dibi::query("DELETE FROM catplaces WHERE capproid=%i", $id);
    //vymazu products_salestat
    dibi::query("DELETE FROM products_salestat WHERE prsproid=%i", $id);
    //vymazu proparams
    dibi::query("DELETE FROM proparams WHERE prpproid=%i", $id);
    return ($ret);
  }

  /********************* preddefinovane SQL *********************/

  /**
   * vraci SQL SELECT
   * seznam zbozi
   *
   * @param string $where
   * @param string $orderBy
   * @return string
   */
  public function getSqlList($where, $orderBy="proorder") {
    if ($where != "") $where = " WHERE $where";
    if ($orderBy != "") $orderBy = "ORDER BY $orderBy";
    return($this->getSqlBase() . " $where $orderBy");
  }

  /**
   * vraci SQL SELECT
   * seznam zbozi
   *
   * @param array $where
   * @param string $orderBy
   * @return array
   */
  public function getSqlListArr(array $where, $orderBy="proorder") {
    $query[] = $this->getSqlBase();
    if (count($where)) {
      $query[] = " WHERE ";
      $query = array_merge($query, $where);
    }
    if ($orderBy != "") {
      $query[] = " ORDER BY $orderBy";
    }
    return($query);
  }

  /**
  * vraci čistý SQL SELECT
  * seznam zbozi
  *
  * @return string
  */
  public function getSqlBase() {
    return("SELECT proid, protypid, promasid, proismaster, provariants, protypid2, protypid3, protypid4, protypid5, propromodateto, prokey, procode, proname AS proname1, proisexp, 
    IF('" .$this->prccat. "' = 'a' || '" .$this->prccat. "' = 'b', progifts, NULL) AS progifts,
    concat(proname, ' ', proname2) AS proname, proname2, propicname, propicnamevar, pronames, prodescs, proaccess, proaccesstext, proqty, proqty_store, proqty_shop1, proqty_shop2, proprice".$this->curId. 'com AS pricecom, 
    IF(proprice' .$this->curId.$this->prccat. '>0,proprice' .$this->curId.$this->prccat. ',proprice' .$this->curId. 'a) AS proprice, 
    proprice' .$this->curId. 'a AS propricea, proprice' .$this->curId. 'b AS propriceb, proprice' .$this->curId. 'c AS propricec, proprice' .$this->curId. 'd AS propriced, proprice' .$this->curId. 'e AS propricee, 
    proprice' .$this->curId. 'com AS propricecom, provatid, prostatus, proorder, manname, prorating, ' .($this->curId == 1 ? 'prodelfree' : '0 AS prodelfree').", pronotdisc
      FROM products
      LEFT JOIN manufacturers ON (promanid=manid)");
  }

  /**
   * vraci SQL SELECT
   * seznam zbozi v katalogu
   *
   * @param int $catid - id urovne katalogu
   * @param string $where
   * @param string $orderBy
   * @param bool $subitems
   * @return string
   */
  public function getSqlCatalogList(int $catid, string $where = "", string $orderBy="proorder", bool $subitems=FALSE) {
    if ($where != "") $where = " AND ($where)";
    if ($orderBy != "") $orderBy = "ORDER BY $orderBy";
    return("
SELECT proid, promasid, proismaster, provariants, protypid, protypid2, protypid3, protypid4, protypid5, propromodateto, prokey, procode, proname AS proname1, concat(proname, ' ', proname2) AS proname, 
proname2, pronames, prodescs, proaccess, proaccesstext, proqty, proqty_store, proqty_shop1, proqty_shop2, proprice".$this->curId. 'com AS propricecom, proisexp,
propicname, propicnamevar, IF(proprice' .$this->curId.$this->prccat. '>0,proprice' .$this->curId.$this->prccat. ',proprice' .$this->curId. "a) AS proprice, 
IF('" .$this->prccat. "' = 'a' || '" .$this->prccat. "' = 'b', progifts, NULL) AS progifts,proweight,proforid,
provatid, " .($this->curId == 1 ? 'prodelfree' : '0 AS prodelfree'). ", pronotdisc, prostatus, proorder, manname, prorating
FROM products
INNER JOIN catplaces ON (capproid=proid)
INNER JOIN catalogs ON (capcatid=catid)
LEFT JOIN manufacturers ON (promanid=manid)
WHERE " .($subitems ? '' : ' promasid=0 AND ') . ($catid != 118 ? " catpathids LIKE '%|$catid|%' " : " (catpathids LIKE '%|$catid|%' OR protypid=1 OR protypid4=1) ") .
$where . "
GROUP BY proid
$orderBy");
  }

  /**
   * vraci SQL SELECT
   * seznam zbozi v katalogu
   *
   * @param string $catFitter
   * @param string $where
   * @param string $limit
   * @param string $orderBy
   * @return string
   */
  public function getSqlMallCatalogList($catFitter="", $where="", $limit="", $orderBy="") {
    if ($orderBy === '') {
      $orderBy = " ORDER BY if(promalldateu is null, 0, 1), promalldateu ";
    }
    $sql = "
SELECT proid, promasid, procode, procode2, proismaster, provariants, protypid, protypid2, protypid3, protypid4, protypid5, propromodateto, prokey, procode, proname AS proname1, concat(proname, ' ', proname2) AS proname, 
proname2, pronames, prodescs, prodesc, pronutrition, proaccess, proaccesstext, proqty, proprice".$this->curId. "com AS propricecom, proisexp, promallblock, promalllabels,
propicname, propicnamevar, IF(proprice" .$this->curId.$this->prccat. ">0,proprice" .$this->curId.$this->prccat. ",proprice" .$this->curId. "a) AS proprice, 
IF('" .$this->prccat. "' = 'a' || '" .$this->prccat. "' = 'b', progifts, NULL) AS progifts,
provatid, prodelfree, pronotdisc, prostatus, proorder, manmalid, manname, prorating, catid, catpathids, catmallparams
FROM products
INNER JOIN catplaces ON (capproid=proid)
INNER JOIN catalogs ON (" . $catFitter . " capcatid=catid)
LEFT JOIN manufacturers ON (promanid=manid)
WHERE prostatus=0 ".($where != "" ? " AND " . $where : "")."
GROUP BY proid
$orderBy
$limit";
    //Debugger::log($sql);
    return $sql;
  }

  /**
   * prověří zda je položka v dané kategorii
   *
   * @param $proid
   * @param $catid
   * @return bool
   * @throws DibiException
   */
  public function isInCatalog($proid, $catid) {
    $cnt = (int)dibi::fetchSingle("SELECT count(capid) FROM catplaces INNER JOIN catalogs ON (capcatid=catid) WHERE capproid=%i", $proid, " AND catpathids LIKE '%|$catid|%'");
    return $cnt > 0;
  }

  /**
   * vraci obsah cache
   *
   * @param string $key - identifikator promenne
   * @return bool
   */
  public function cacheGet(string $key) {
    $key = $key.'_'.$this->curId.$this->prccat;
    return parent::cacheGet($key);
  }

  /**
   * ulozi cache
   *
   * @param string $key - identifikator promenne
   * @param mixed $data
   * @return mixed
   */
  public function cacheSave(string $key, $data) {
    $key = $key.'_'.$this->curId.$this->prccat;
    return parent::cacheSave($key, $data);
  }

  /********************* ciselniky *********************/

  /**
  * ciselnik prostatus
  * @return array
  */
  public function getEnumProStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  public function getEnumProVolumeUnits() {
    return array(
      'kg' => 'kg',
      'g' => 'g',
      'l' => 'l',
      'ml' => 'ml',
      'kap' => 'kapsle',
      'tab' => 'tableta',
      'amp' => 'ampule',
      'ks' => 'kusy',
      'par' => 'pár'
    );
  }

  /**
   * ciselnik vyrobcu
   *
   * @param bool $onlyActive
   * @return array
   * @throws Exception
   */
  public function getEnumProManId(bool $onlyActive=TRUE): array {
    return dibi::query("SELECT manid, manname FROM manufacturers " . ($onlyActive ? " WHERE manstatus=0" : "") . " ORDER BY manname")
      ->fetchPairs('manid', 'manname');
  }

  /**
  * ciselnik sazby DPH zbozi
  * @return array
  */
  public function getEnumProVatId() {
    return array(
      0 => 'Základní',
      1 => 'Snížená',
      2 => 'Nulová',
    );
  }

  /**
  * ciselnik dostupnosti
  * @return array
  */
  public function getEnumProAccess() {
    return array(
      0 => 'Skladem',
      3 => 'do 3 dnů',
      7 => 'do týdne',
      14 => 'do 2 týdnů',
      30 => 'do měsíce',
      32 => 'více než měsíc',
      100 => 'není skladem',
    );
  }

    /**
  * ciselnik puvod
  * @return array
  */
  public function getEnumProOrigin() {
    return array(
      'neznámá' => 'Neznámá',
      'CZ' => 'Česká distribuce',
      'EU' => 'Distribuce EU',
      'mimo EU' => 'Mimo EU',
    );
  }
  
  /**
  * ciselnik forma
  * @return array
  */
  public function getEnumProForId() {
    return array(
      1 => 'Sypká',
      2 => 'Kapsle',
      4 => 'Tablety',
      3 => 'Tekutá',
    );
  }
  
  /**
  * ciselnik ikonka ke zbozi
  * @return array
  */
  public function getEnumProIcons() {
    return array(
  'darek.png' => 'Dárek zdarma',
    );
  }


  public function getEnumMallParNames() {
    return array(
      'BODYBUILDING' => 'Silové sporty',
      'BEFORE_ACTION' => 'Před výkonem',
      'DURING_ACTION' => 'Při výkonu',
      'AFTER_ACTION' => 'Po výkonu',
      'FOOD_SUPPLEMENTS' => 'Doplňky stravy',
      'ENDURANCE_SPORTS' => 'Vytrvalostní sporty',
      'TYPE_NA161' => 'Typ',
      'CONSISTENCE' => 'Konzistence',
      'FLAVOR' => 'Příchuť',
      'NUMBER_TABLETS_PORTION' => 'Počet tablet/porcí',
    );
  }


  public function getEnumMallParValuesByParamName($parName) {
    switch ($parName) {
      case 'BODYBUILDING':
      case 'BEFORE_ACTION':
      case 'DURING_ACTION':
      case 'AFTER_ACTION':
      case 'FOOD_SUPPLEMENTS':
      case 'ENDURANCE_SPORTS':
        return array(
          'YES' => 'Ano',
          'NO' => 'Ne',
        );
        break;

      case 'TYPE_NA161':
        return $this->getEnumMallParTypeValues();
        break;

      case 'CONSISTENCE':
        return $this->getEnumMallParConsistenceValues();
        break;

      case 'FLAVOR':
        return $this->getEnumMallParFlavorValues();
        break;

      case 'NUMBER_TABLETS_PORTION':
        return array();
        break;
    }
  }

  /**
   * číselník MALL parametr TYPE
   *
   * @return array
   */
  public function getEnumMallParTypeValues() {
    return array(
      'aminokyseliny' => 'aminokyseliny',
      'anabolizéry' => 'anabolizéry',
      'arginin' => 'arginin',
      'bcaa' => 'bcaa',
      'beta-alanine' => 'beta-alanine',
      'energetické gely' => 'energetické gely',
      'expediční strava' => 'expediční strava',
      'glutamin' => 'glutamin',
      'iontové nápoje' => 'iontové nápoje',
      'isotonické nápoje' => 'isotonické nápoje',
      'keratin' => 'keratin',
      'kloubní výživa' => 'kloubní výživa',
      'komplexní aminokyseliny' => 'komplexní aminokyseliny',
      'kreatin' => 'kreatin',
      'proteinové tyčinky' => 'proteinové tyčinky',
      'proteiny' => 'proteiny',
      'sacharidy' => 'sacharidy',
      'spalovače' => 'spalovače',
      'speciální balíčky' => 'speciální balíčky',
      'stimulanty a energizéry' => 'stimulanty a energizéry',
      'taurine' => 'taurine',
      'tyčinky' => 'tyčinky',
      'vitamíny a minerály' => 'vitamíny a minerály',
      'volné aminokyseliny' => 'volné aminokyseliny',
      'zdravá výživa' => 'zdravá výživa'
    );
  }

  /**
   * číselník MALL parametr TYPE
   *
   * @return array
   */
  public function getEnumMallParConsistenceValues() {
    return array(
      'gel' => 'gel',
      'nápoj' => 'nápoj',
      'prášek' => 'prášek',
      'sirup' => 'sirup',
      'tablety' => 'tablety',
      'tyčinka' => 'tyčinka',
    );
  }


public function getEnumMallParFlavorValues() {
  $arr = array (
    0 => 'CHOCOLATE ALMOND',
    1 => 'CHOCOLATE NOUGAT',
    2 => 'CHOCOLATE SACHER',
    3 => 'CHOCOLATE+NOUGAT WITH CRANBERR',
    4 => 'CHOCOLATE-COCOA',
    5 => 'Coconut-dark Chocolate',
    6 => 'DOUBLE BERRY',
    7 => 'DOUBLE RICH CHOCOLATE',
    8 => 'DRAGON FRUIT',
    9 => 'ENERGY',
    10 => 'ENERGY PUNCH',
    11 => 'EUKALYPTUS+KIWI',
    12 => 'all american cookie dough',
    13 => 'americká káva',
    14 => 'ananas',
    15 => 'ananas (s kofeinem)',
    16 => 'ananas s kokosem',
    17 => 'ananas-hruška',
    18 => 'ananas-mango',
    19 => 'ananas-zázvor',
    20 => 'apple pie',
    21 => 'apple-crumble',
    22 => 'arašídové máslo',
    23 => 'arašídové máslo-sušenka',
    24 => 'arašídy',
    25 => 'arašídy s mléčnou čokoládou',
    26 => 'arašídy-karamel',
    27 => 'banana nut muffin',
    28 => 'banán',
    29 => 'banán - jahoda',
    30 => 'banán - tvaroh',
    31 => 'banán s čokoládovou polevou',
    32 => 'banán v bílé čokoládě',
    33 => 'banán-vanilka',
    34 => 'banánovo-mandlový koláč',
    35 => 'banánový koláč',
    36 => 'belgická čokoláda',
    37 => 'berry',
    38 => 'berry blast',
    39 => 'berry mix',
    40 => 'bez příchutě',
    41 => 'bezový květ',
    42 => 'birthday cake',
    43 => 'blue energy',
    44 => 'borůvka',
    45 => 'borůvka - granátové jablko',
    46 => 'borůvka-brusinka-jogurt.poleva',
    47 => 'borůvkový muffin',
    48 => 'bramborová kaše se šunkou',
    49 => 'brambory & kerblík',
    50 => 'brazilská káva',
    51 => 'brazilské ovoce curuba',
    52 => 'brokolice',
    53 => 'brokolice s krůtím masem',
    54 => 'broskev',
    55 => 'broskev - banán',
    56 => 'broskev+maracuja',
    57 => 'brusinka',
    58 => 'brusinka-citron',
    59 => 'burákové máslo - karamel      ',
    60 => 'bílá čokoláda',
    61 => 'bílá čokoláda - Pralinka',
    62 => 'bílý grep',
    63 => 'cajun hot',
    64 => 'cereálie',
    65 => 'chai-kurkuma-maca',
    66 => 'cherry-cola',
    67 => 'cherry-watermelon',
    68 => 'chilly con carne',
    69 => 'chocolate-lava',
    70 => 'cikánská pečeně',
    71 => 'cinnamon-almond',
    72 => 'citron - Smetana',
    73 => 'citron - jogurt',
    74 => 'citron - lesní plody',
    75 => 'citron - limetka',
    76 => 'citron - tvaroh',
    77 => 'citron-ledový čaj',
    78 => 'citron-máta',
    79 => 'citronový cheesecake',
    80 => 'citronový koláč',
    81 => 'citrón',
    82 => 'classic',
    83 => 'cola',
    84 => 'couscous',
    85 => 'cuketa s bramborami',
    86 => 'dark choc raspberry',
    87 => 'divoké houby & nudle',
    88 => 'double choc chips',
    89 => 'dušené brambory',
    90 => 'dušené jehněčí',
    91 => 'dýně',
    92 => 'exotic',
    93 => 'exotic punch',
    94 => 'fresh',
    95 => 'fresh apple',
    96 => 'fresh banana',
    97 => 'fresh grep',
    98 => 'fík-papája',
    99 => 'fík-černý jeřáb',
    100 => 'goji-jahoda',
    101 => 'granátové jablko',
    102 => 'green',
    103 => 'green fresh',
    104 => 'grep',
    105 => 'hot&sweet - hovězí',
    106 => 'hovězí',
    107 => 'hovězí Stroganoff',
    108 => 'hovězí na pepři s rýží',
    109 => 'hovězí směs s kaší',
    110 => 'hrozen',
    111 => 'hruška',
    112 => 'ice fresh',
    113 => 'irská čokoláda',
    114 => 'jablka s borůvkami',
    115 => 'jablka s hruškami',
    116 => 'jablko',
    117 => 'jablko + vlašský ořech',
    118 => 'jablko - banán',
    119 => 'jablko - datle - fíky',
    120 => 'jablko - jahoda - borůvka',
    121 => 'jablko - meruňka - jahoda',
    122 => 'jablko - mrkev',
    123 => 'jablko - rozinky',
    124 => 'jablko - skořice',
    125 => 'jablko - švestka',
    126 => 'jahoda',
    127 => 'jahoda s banánem',
    128 => 'jahoda-vanilka',
    129 => 'jahodová limonáda',
    130 => 'jahodový cheesecake',
    131 => 'jahodový koláč',
    132 => 'jarní zelenina',
    133 => 'jarní zelenina s krůtím masem',
    134 => 'jehněčí - jáhly',
    135 => 'jogurt',
    136 => 'jogurt - banán',
    137 => 'jogurt - broskev',
    138 => 'jogurt - jahoda',
    139 => 'jogurt - pomeranč',
    140 => 'jogurt/müsli',
    141 => 'juicy steak',
    142 => 'kakao',
    143 => 'kakao-brusinka',
    144 => 'kakao-pomeranč',
    145 => 'kapučíno',
    146 => 'karamel',
    147 => 'karotka s krůtím masem',
    148 => 'kešu-jablko',
    149 => 'kokos',
    150 => 'kokos-bílá čokoláda',
    151 => 'kokos-citron-malina',
    152 => 'kokos-papája',
    153 => 'kokos-čokoláda',
    154 => 'krupice',
    155 => 'krupicová s medem',
    156 => 'krupicová s vanilkou',
    157 => 'krupicová se skořicí',
    158 => 'krupková s kuřetem',
    159 => 'krutí s rajčaty',
    160 => 'králík',
    161 => 'krůtí maso',
    162 => 'kukuřice',
    163 => 'kuře - jablka',
    164 => 'kuře - špenát',
    165 => 'kuře na kari se smetanou',
    166 => 'kuřecí maso',
    167 => 'kuřecí rizoto se zeleninou',
    168 => 'květák',
    169 => 'káva',
    170 => 'latte macchiato',
    171 => 'ledová káva',
    172 => 'lemon-starfruit',
    173 => 'lesní jahoda',
    174 => 'lesní ovoce',
    175 => 'lesní plody',
    176 => 'lesní směs',
    177 => 'letní ovoce',
    178 => 'limetka',
    179 => 'limetka s papájou',
    180 => 'limonáda',
    181 => 'lipová',
    182 => 'lískový oříšek',
    183 => 'lískový oříšek-banán',
    184 => 'lískový oříšek-kešu',
    185 => 'magic berry',
    186 => 'malina',
    187 => 'malina - Smetana',
    188 => 'malina s brusinkou',
    189 => 'malina v bílé čokoládě',
    190 => 'malina-brusinka',
    191 => 'malina-jogurt',
    192 => 'mandarinka',
    193 => 'mandle',
    194 => 'mandle s bílou čokoládou',
    195 => 'mandle-pistácie',
    196 => 'mango',
    197 => 'mango s broskví',
    198 => 'mango-maracuja',
    199 => 'mango-pomeranč',
    200 => 'marcipán',
    201 => 'marcipán s mandlemi',
    202 => 'marcipán s čokoládovou polevou',
    203 => 'matcha + vanilka',
    204 => 'mařinka',
    205 => 'med',
    206 => 'meloun',
    207 => 'meruňka',
    208 => 'meruňka - banán',
    209 => 'meruňka+pekanový ořech s jog.',
    210 => 'meruňka-mandle',
    211 => 'meruňka-zázvor',
    212 => 'mexican-fruit',
    213 => 'mirabelka',
    214 => 'mix příchutí',
    215 => 'mléčná rýže',
    216 => 'mléčná čokoláda',
    217 => 'mocha',
    218 => 'modrá malina',
    219 => 'mojito',
    220 => 'mojito (s kofeinem)',
    221 => 'mořská ryba',
    222 => 'mrkev',
    223 => 'mrkev - brambor',
    224 => 'mrkev - brambor - hovězí',
    225 => 'multivitamin',
    226 => 'myslivecká směs',
    227 => 'máta',
    228 => 'míchaná vajíčka',
    229 => 'müsli s jogurtem a ovocem',
    230 => 'müsli s oříšky a kakaem',
    231 => 'müsli s rozinkami',
    232 => 'nasi Goreng',
    233 => 'natural',
    234 => 'natural - neslazené',
    235 => 'natural - slazené',
    236 => 'neperlivá',
    237 => 'nudlová s kuřetem',
    238 => 'nugát',
    239 => 'orange fire',
    240 => 'original',
    241 => 'original - hot&sweet',
    242 => 'original - hovězí',
    243 => 'ostružina',
    244 => 'ostružina-limetka',
    245 => 'ostružiny',
    246 => 'ovoce',
    247 => 'ovoce s cereáliemi',
    248 => 'ovocná směs',
    249 => 'ovocný punč',
    250 => 'oříšek',
    251 => 'oříšek-nugát',
    252 => 'oříšky - Karamel',
    253 => 'paella',
    254 => 'panna cotta',
    255 => 'paprika',
    256 => 'pasta "Siciliana" s olivami',
    257 => 'pasta se zeleninou na smetaně',
    258 => 'pastinák',
    259 => 'peach ice tea',
    260 => 'pekanové oříšky - nugát',
    261 => 'perfect chocolate',
    262 => 'pistácie',
    263 => 'pistácie + kokos',
    264 => 'piňa colada',
    265 => 'piškotová',
    266 => 'pohanka - hruška',
    267 => 'pomelo',
    268 => 'pomeranč',
    269 => 'pomeranč (s kofeinem)',
    270 => 'pomeranč - limetka',
    271 => 'pomeranč - maracuja',
    272 => 'pomeranč s čokoládovou polevou',
    273 => 'pomeranč-citron',
    274 => 'pomeranč-kokos',
    275 => 'pomeranč-mango',
    276 => 'pomeranč-višeň',
    277 => 'pudinková vanilka',
    278 => 'punč-brusinka',
    279 => 'punč-lesní plody',
    280 => 'pšeničná',
    281 => 'pšeničná s ovocem',
    282 => 'rajčata s hovězím a bramborami',
    283 => 'rajčata se špagetami',
    284 => 'red berry',
    285 => 'red fresh',
    286 => 'red fruit',
    287 => 'rozinky+ořech',
    288 => 'rybíz',
    289 => 'rýže',
    290 => 'rýže s krůtím masem',
    291 => 'rýže s kuřecím masem',
    292 => 'rýžovo-kukuřičná',
    293 => 'rýžová s banány',
    294 => 'rýžová s jahodami',
    295 => 'rýžová s malinami',
    296 => 'rýžová s meruňkami',
    297 => 'rýžová se skořicí',
    298 => 'rýžový pudink',
    299 => 'růžový grep',
    300 => 's hovězím masem',
    301 => 's jehněčím a divočákem',
    302 => 's jehněčím masem',
    303 => 's kachním a bažantem',
    304 => 's kachním masem',
    305 => 's kančím masem',
    306 => 's králičím masem',
    307 => 's krůtím masem',
    308 => 's kuřecím masem',
    309 => 's lososem',
    310 => 's lososem a krocanem',
    311 => 's mrkví a hráškem',
    312 => 's oceánskou rybou',
    313 => 's osmi cereáliemi a medem',
    314 => 's osmi cereáliemi a ovocem',
    315 => 's pstruhem',
    316 => 's tuňákem',
    317 => 's vepřovým masem',
    318 => 'salted caramel',
    319 => 'salty peanut butter-caramel',
    320 => 'salty peanuts',
    321 => 'se zvěřinou',
    322 => 'se čtyřmi druhy masa',
    323 => 'skořice',
    324 => 'skořicový šnek',
    325 => 'slanina',
    326 => 'slaný arašídový karamel',
    327 => 'smetana',
    328 => 'smooked',
    329 => 'sobí maso',
    330 => 'stracciatella',
    331 => 'strawberry lime',
    332 => 'strawberry-almond',
    333 => 'sušenka',
    334 => 'sušenky & krém',
    335 => 'sušená kýta s rajčaty',
    336 => 'sůl',
    337 => 'telecí maso',
    338 => 'teriyaky',
    339 => 'tiramisu',
    340 => 'tmavá čokoláda',
    341 => 'tofee-karamel',
    342 => 'triple chocolate',
    343 => 'tropic blue',
    344 => 'tropical',
    345 => 'tropické ovoce',
    346 => 'těstoviny Bella Italia',
    347 => 'těstoviny se šunkou',
    348 => 'třešeň-citrus',
    349 => 'třešeň-mandle',
    350 => 'třešeň-marcipán',
    351 => 'třešeň-pomeranč',
    352 => 'třešeň-punč',
    353 => 'třešeň/jogurt',
    354 => 'třešeň/kokos',
    355 => 'vanilka',
    356 => 'vanilka - karamel             ',
    357 => 'vanilka - skořice',
    358 => 'vanilka-borůvka',
    359 => 'vanilka-karamel',
    360 => 'vanilka-kokos s čokoládovou po',
    361 => 'vanilka-med',
    362 => 'vanilka/jahoda',
    363 => 'vanilková zmrzlina',
    364 => 'vanilkový pudink',
    365 => 'vanilla',
    366 => 'vanilla-almond',
    367 => 'vepřové',
    368 => 'višeň',
    369 => 'višeň-jahoda',
    370 => 'višeň/čokoláda',
    371 => 'vodní meloun',
    372 => 'vícezrnná',
    373 => 'white choc blondie',
    374 => 'white chocolate raspberry',
    375 => 'zahradní ovoce',
    376 => 'zahradní zelenina',
    377 => 'zelenina se šunkou',
    378 => 'zeleninová směs',
    379 => 'zeleninové rizoto',
    380 => 'zelené jablko',
    381 => 'zelený čaj s citronem',
    382 => 'zelený čaj s opuncií',
    383 => 'zelený čaj+bezinka (kofein)',
    384 => 'černý rybíz',
    385 => 'černý rybíz s brusinkami',
    386 => 'červené plody ovoce',
    387 => 'červený pomeranč',
    388 => 'červený pomeranč (s kofeinem)',
    389 => 'čoko-arašídové máslo',
    390 => 'čoko-máta',
    391 => 'čoko-oříšek',
    392 => 'čoko-pomeranč',
    393 => 'čokoláda',
    394 => 'čokoláda + mandle s mléčnou čo',
    395 => 'čokoláda - banán',
    396 => 'čokoláda - kakao',
    397 => 'čokoláda - lískové oříšky',
    398 => 'čokoláda - malina',
    399 => 'čokoláda - mandle',
    400 => 'čokoláda - oříšek s karamelem',
    401 => 'čokoláda - pistácie',
    402 => 'čokoláda - višeň',
    403 => 'čokoláda s čokoládovou polevou',
    404 => 'čokoláda-banán s hořkou čokolá',
    405 => 'čokoláda-kokos s hořkou čoko.',
    406 => 'čokoláda-lískový ořech',
    407 => 'čokoláda-oříšek-karamel',
    408 => 'čokoláda-višeň s hořkou čokolá',
    409 => 'čokoláda-černý rybíz',
    410 => 'čokoláda/cookies',
    411 => 'čokoláda/kokos',
    412 => 'čokoláda/třešeň',
    413 => 'čokoládové brownies',
    414 => 'čočka na slanině',
    415 => 'špagety bolognese',
    416 => 'špagety carbonara',
    417 => 'špagety s hovězím masem',
    418 => 'špagety s krůtím masem',
    419 => 'špalda, jáhly',
    420 => 'špenát - brambor',
    421 => 'špenát - bramor - hovězí',
    422 => 'šunka s hráškem a bramb. kaší',
    423 => 'švestka',
    424 => 'švestka-goji',
    425 => 'švestka-lískový ořech',
    426 => 'žlutá malina',
    );

    return array_combine(array_values($arr), array_values($arr));
  }

  public function getEnumMallParSizes() {
    return array (
      'M' => 'M',
      'S' => 'S',
      'L' => 'L',
      'XL' => 'XL',
      'XS' => 'XS',
      'XXL' => 'XXL',
      'Univerzální' => 'Univerzální'
    );
  }
}
