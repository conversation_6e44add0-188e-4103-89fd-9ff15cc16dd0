<?php
namespace Model;
use dibi;
use ModelFactory;
use Nette;

abstract class BaseModel {
  use Nette\SmartObject;

  protected string $prccat = 'a';

  //identifikatory vlastnosti datoveho pole
  /** @var string nazev tabulky */
  protected $tableName;
  
  /** @var string nastaveni */
  protected $config;

  /** @var string nazev tabulky s prefixem */
  private $table;

  protected $cache = Null;

  /**
  * @var Nette\Caching\IStorage
  */
  public $storage;

  /**
  * @var ModelFactory
  */
  public ModelFactory $model;

  /** nastaveni z neonu */
  public $neonParameters = array();
  
  /** ID aktualni meny; */
  protected $curId = 1;
  
  /** zaokrouleni pocet des. mist aktualni meny */
  protected $curDigits = 2;
  
  /** meny ktere eshop pouziva */
  protected $currencies = array();

  public function __construct($model) {
    $this->table = $this->tableName;
    $this->model = $model;
    $this->storage = $model->storage;
    $this->cache = new Nette\Caching\Cache($this->storage, $this->tableName);
    //$this->cache = new Cache($storage);
  }
  
  /**
  * nastavi aktualni menu
  *
  */
  public function setCurrency($currencies, $curId) {
    $this->currencies = $currencies;
    $this->curId = (int)$curId;
    $this->curDigits = (int)$this->currencies[$this->curId]["decimals"];
  }

  /**
  * nastavi cenovou kategorii pro preddefinovane SQL
  *
  */
  public function setPrcCat($val) {
    if ($val == "") $val = "a";
    $this->prccat = $val;
  }

  /**
  * vraci cache modelu
  *
  */
  public function getCache() {
    return ($this->cache);
  }

  /**
  * vraci vlastnosti sloupce
  *
  * @param string $colName nazev sloupce
  * @return array
  */
  public function getColProperties($colName) {
    return Null;
  }

  /**
  * vraci pro dany sloupec hodnotu prislusne property
  *
  * @param string $colName nazev sloupce
  * @param string $colProperty nazev property [type|size|nullable|default]
  * @return string
  */
  public function getColProperty($colName, $colProperty) {
    return Null;
  }

  /**
  * nastavi proslusnemu sloupci hodnotu prislusne property
  *
  * @param string $colName nazev sloupce
  * @param string $colProperty nazev property [type|size|nullable|default]
  * @param string $propertyValue hodnota property
  * @return boolean
  */
  protected function setColProperty($colName, $colProperty, $propertyValue) {
    return true;
  }

  public function getDataSource($sql="") {
    if ($sql == "") $sql = $this->getSql();
    return dibi::dataSource($sql);
  }
  
  public function getSql () {
    return "SELECT * FROM $this->table";
  }

  /**
   * vraci jeden zaznam
   *
   * @param integer|string $id hodnota id ve smyslu jednoznacneho identifikatoru, nemusi byt primarni klic
   * @param string $col nazev sloupce bez prefixu
   * @return \DibiRow
   * @throws \DibiException
   */
  public function load($id, $col='id') {
    $colName = $this->fieldPrefix.$col;
    if ($col == 'id') {
      $f = 'i';
    } else {
      $f = 's';
    }
    return dibi::fetch($this->getSql()." WHERE ".$colName.'=%'.$f, $id, " LIMIT 1");
  }

  public function update($id, $data,$setDateU = True) {
    $this->cacheClean();
    if ($setDateU) $data[$this->fieldPrefix.'dateu'] = new \DateTime;

    return dibi::update($this->table, $data)
      ->where($this->fieldPrefix.'id=%i', $id)
      ->execute();
  }

  public function insert($data) {
    $this->cacheClean();
    if (!isset($data[$this->fieldPrefix.'datec'])) {
      $data[$this->fieldPrefix.'datec'] = new \DateTime;
    }
    return dibi::insert($this->table, $data)
      ->execute(dibi::IDENTIFIER);
  }

  public function save(&$id, $data, $setDateU = True) {
    if ($id > 0) {
      return $this->update($id, $data, $setDateU);
    } else {
      $id = $this->insert($data);
      return($id > 0);
    }
  }

  public function delete($id) {
    $this->cacheClean();
    return dibi::delete($this->table)
      ->where($this->fieldPrefix.'id=%i', $id)
      ->execute();
  }

  public function fetchAll($sql) {
    $result = dibi::dataSource($sql)
      ->getResult();
    return $result->fetchAll();
  }

  /**
  * vraci obsah cache
  *
  * @param string $key - identifikator promenne
  */
  public function cacheGet(string $key) {
    $value = $this->cache->load($key);
    if ($value === NULL) {
      return false;
    } else {
      return $value;
    }
  }

  /**
  * ulozi cache
  *
  * @param string $key - identifikator promenne
  * @param mixed $data
  * @return mixed
  * @throws \Throwable
  */
  public function cacheSave(string $key, $data) {
    return $this->cache->save($key, $data, array(Nette\Caching\Cache::TAGS => array($this->tableName)));
  }

  /**
  * vymaze cache vazanou k prislusne tabulce
  *
  */
  public function cacheClean() {
    $this->cache->clean(array(Nette\Caching\Cache::TAGS => array($this->tableName)));
  }
  
  public function getConfig() {
    if (empty($this->config)) {
      $cfgs = $this->model->getConfigModel();
      $this->config = $cfgs->getConfig();
    }
    return $this->config;
  }

  public function escapeString($str) {
    if(is_array($str)) {
      return array_map(__METHOD__, $str);
    }
    if(!empty($str) && is_string($str)) {
      return str_replace(array('\\', "\0", "\n", "\r", "'", '"', "\x1a"), array('\\\\', '\\0', '\\n', '\\r', "\'", '\"', '\\Z'), $str);
    }
    return $str;
  }

  public function getEnumStores() {
    return array(
      'store'=>'Sklad',
      'shop1'=>'Prodejna Praha 10',
      'shop2'=>'Prodejna Praha 2',
    );
  }

  public function getEnumRobotsIndex() {
    return array(
      'index,follow'=>'Indexovat, procházet',
      'noindex,follow'=>'Neindexovat, procházet',
      'noindex,nofollow'=>'Neindexovat, NEprocházet',
    );
  }
}
