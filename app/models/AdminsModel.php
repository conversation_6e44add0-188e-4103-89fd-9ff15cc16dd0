<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class AdminsModel extends BaseModel {

  protected $tableName = "admins";
  protected $fieldPrefix = "adm";

  /********************* ciselniky *********************/

  /**
  * ciselnik admstatus
  * @return array
  */
  public function getEnumAdmStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  public function getEnumAdmins() {
    //naplnim zpusoby dopravy
     return dibi::query("SELECT admid, admname FROM admins WHERE admstatus=0 ORDER BY admname")->fetchPairs('admid', 'admname');
  }

  public function getEnumAuthors() {
    //naplnim zpusoby dopravy
     return dibi::query("SELECT admid, admname FROM admins WHERE admstatus=0 AND coalesce(admfunction, '') != '' ORDER BY admname")->fetchPairs('admid', 'admname');
  }

}
