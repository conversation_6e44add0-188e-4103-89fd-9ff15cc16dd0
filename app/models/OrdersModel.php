<?php
namespace Model;
use dibi;

class OrdersModel extends BaseModel {

  protected $tableName = "orders";
  protected $fieldPrefix = "ord";
  
  public function insert($data) {
    $cous = $this->model->getCouponsModel();

    //naplnim kod objednavky - jedna spolecna ciselna rada pro vsechny objednavky pro aktualni rok
    $today = dibi::fetch("SELECT YEAR(CURDATE()) AS today_year, MONTH(CURDATE()) AS today_month");
    $month = substr('0'.$today->today_month, -2);
    $year = substr($today->today_year, 2, 2);
    $lastOrdCode = dibi::fetchSingle("SELECT ordcode FROM orders ORDER BY ordid DESC LIMIT 1");
    if (!empty($lastOrdCode)) {
      $number = substr($lastOrdCode, 4);
      $number = (int)$number + 1;
      $data["ordcode"] = $year.$month.$number;
    } else {
      $data["ordcode"] = $year.$month.'00001';
    }
    if (empty($data["ordcurid"])) {
      $data["ordcurid"] = $this->curId;
    }
    if ($data["ordcurid"] == 2) {
      $data["ordcurrate"] = (double)dibi::fetchSingle("SELECT cfgvalue FROM config WHERE cfgcode='PRICE2RATE'");
    }

    if (!empty($data["ordcoucode"])) {
      $cous->useCoupon($data["ordcoucode"], 0, $data["ordmail"], FALSE);
    }


    if (isset($data["ordstcouid"]) && empty($data["ordstcouid"])) {
      if (!empty($data["ordicouid"])) {
        $data["ordstcouid"] = $data["ordicouid"];
      } else {
        $data["ordstcouid"] = 1;
      }
    }

    $ordid = parent::insert($data);
    if ($ordid > 0) {
      $this->logStatus($ordid, 0);
      return $ordid;
    }
    return false;
  }

  public function update($id, $data, $setDateU = TRUE) {
    //pokud je vyplněn kód kupónu
    if (isset($data["ordcoucode"])) {
      $ord = $this->load($id);
      if (!empty($data["ordcoucode"]) && empty($ord->ordcoucode)) {
        //doplnil jsem slevový kupón budu kontrolovat
        $cous = $this->model->getCouponsModel();

        $ret = $cous->useCoupon($data["ordcoucode"], 0, $ord->ordmail, exclOrdId: $id);
        if ($ret["status"] != "ok") {
          throw new \Exception($ret["text"]);
        }
      } else if (empty($data["ordcoucode"]) && !empty($ord->ordcoucode)) {
        //vamazal kupón
        $cous = $this->model->getCouponsModel();
        $cous->unsetCoupon($ord->ordcoucode);

      }
    }

    return parent::update($id, $data, $setDateU);
  }

  public function delete($id) {
    if (parent::delete($id)) {
      //vymazi vsechny polozky
      return dibi::query("DELETE FROM orditems WHERE oriordid=%i", $id);
    }
    return false;
  }

  public function logStatus($ordid, $status, $date=NULL, $desc=NULL)  {
    //zapisu do logu
    if ($date === NULL) {
      $date = new \DateTime;
    }
    return dibi::insert('orders_log', array(
      'orlordid'=>$ordid,
      'orlstatus'=>$status,
      'orldatec'=>$date,
      'orldesc'=>$desc,
    ))->execute(dibi::IDENTIFIER);
  }

  //prepocita objednavku
  public function recalcOrder($id) {
    $config = $this->getConfig();
    $order = $this->load($id);

    if (!$order) {
      return;
    }

    $ordItems = $this->model->getOrdItemsModel();
    $priceSumDisc = 0;
    $priceSumAllProducts = 0;
    $discountsOff = !empty($order->ordmalid);
    if ($discountsOff === FALSE) {
      //suma objednane zbozi pro vypocet slevy
      $priceSumDisc = (double)dibi::fetchSingle("
        SELECT SUM(oriprice*oriqty)
        FROM orditems
        INNER JOIN products ON (oriproid=proid)
        WHERE
        oriordid=%i AND
        pronotdisc=0 AND
        oritypid=0", $order->ordid);

      //suma objednane zbozi pro vypocet % slevy z kupónu
      $priceSumAllProducts = (double)dibi::fetchSingle("
        SELECT SUM(oriprice*oriqty)
        FROM orditems
        INNER JOIN products ON (oriproid=proid)
        WHERE
        oriordid=%i AND
        oritypid=0", $order->ordid);

      //zjistim jestli ma narok na slevu
      $discount = 0;
      $userDiscPer = 0;
      $userPrcCat = 'a';
      //pokud je nastaven user na obj. zjistim uzivatelskou slevu
      //nactu zakaznika
      $usr = false;
      if ($order->ordusrid > 0) {
        $usr = dibi::fetch("SELECT * FROM users WHERE usrid=%i", $order->ordusrid);
      }
      if ($usr) {
        $userDiscPer = (int)$usr->usrdiscount;
        $userPrcCat = $usr->usrprccat;
      }
      if (!empty($order->ordprccat)) {
        $userPrcCat = $order->ordprccat;
      }

      if ($order->orddiscpercent == 0) {
        //je tam slevova polozka ale neni nastavena sleva na objednavce - zjistim jestli ma narok na slevu
        if ($priceSumDisc > 0) {
          //zjistim jestli je mnozstevni sleva
          $disc = (int)dibi::fetchSingle("SELECT dispercent FROM discounts WHERE distypid='volume' AND disprccat=%s", $userPrcCat, " AND discurid=%i", $this->curId, " AND $priceSumDisc BETWEEN disfrom AND disto AND disstatus=0");
          //vyssi slevu pouziju
          if ($disc < $userDiscPer) {
            $discount = $userDiscPer;
            $discountName = "Zákaznická sleva";
          } else {
            $discount = $disc;
            $discountName = "Množstevní sleva";
          }
        }
      } else {
        $discount = $order->orddiscpercent;
        $discountName = "Sleva";
      }

      if ((int)$this->config["DISCOUNT_DISSOLVE"] == 1) {
        if ($discount == 0) {
          //vymazu slevu
          dibi::query("UPDATE orditems SET oriprice=oripriceoriginal, oridiscper=0 WHERE oriordid=%i", $id);
        } else if ($discount > 0) {
          //u vsech polozek vypocitam slevu
          $rows = dibi::fetchAll("
          SELECT *
          FROM orditems
          INNER JOIN products ON (oriproid=proid)
          WHERE
          oriordid=%i AND
          pronotdisc=0 AND
          oritypid=0", $order->ordid);
          foreach ($rows as $key => $row) {
            $disc = round((double)$row->oriprice * (double)$discount / 100);
            $oriprice = (double)$row->oripriceoriginal - $disc;
            dibi::query("UPDATE orditems SET oriprice=%f", $oriprice, ", oridiscper=%f", $discount, "  WHERE oriid=%i", $row->oriid);
          }
        }
      } else {
        //zjistim ID polozky slevy
        $oriid = (int)dibi::fetchSingle("SELECT oriid FROM orditems WHERE oriordid=%i", $id , " AND oritypid=3");

        if ($oriid > 0 && $discount == 0) {
          //vymazu slevu
          $ordItems->delete($oriid);
          dibi::query("UPDATE orditems SET oridiscper=0 WHERE oriordid=%i", $id, " AND oritypid=0 AND oridiscper!=0");
        } else if ($oriid == 0 && $discount > 0) {
          //zalozim polozku slevy
          $values = array(
            'oriordid' => $id,
            'oritypid' => 3,
            'oriqty' => 1,
            'oriname' => $discountName." ".$discount."%",
            'oriprice' => round(-1 * $priceSumDisc * $discount / 100, 0),
          );
          $oriid = $ordItems->insert($values);
        } else if ($oriid > 0 && $discount > 0) {
          //upravim polozku
          $values = array(
            'oritypid' => 3,
            'oriname' => $discountName." ".$discount."%",
            'oriprice' => round(-1 * $priceSumDisc * $discount / 100, 0),
          );
          $ordItems->update($oriid, $values);
        }
        //uložím slevu k pložkám které jsou slevněny
        if ($oriid > 0 && $discount > 0) {
          $rows = dibi::fetchAll("
          SELECT *
          FROM orditems
          INNER JOIN products ON (oriproid=proid)
          WHERE
          oriordid=%i AND
          pronotdisc=0 AND
          oritypid=0", $order->ordid);
          foreach ($rows as $key => $row) {
            dibi::query("UPDATE orditems SET oridiscper=%f", $discount, " WHERE oriid=%i", $row->oriid);
          }
        }
      }
      //doprava, aktualizuju polozku
      //zjistim, jestli nenei v kosiku polozka s dopravou zdarma
      //jen pro cenovou kategoii A a B zohlednovat polozky co maji priznak doprava zdarma
      $delFreeCnt = 0;
      if ($this->curId == 1 && ($userPrcCat == 'a' || $userPrcCat == 'b')) {
        $delFreeCnt = (int)dibi::fetchSingle("SELECT COUNT(oriid) FROM orditems INNER JOIN products ON (oriproid=proid) WHERE prodelfree=1 AND oriordid=%i AND oritypid=0", $order->ordid);
      }
      //zjistim cenu objednaneho zbozi vcetne slevy pro vypocet dopravy zdarma
      $priceSum = (double)dibi::fetchSingle("SELECT SUM((oriprice*oriqty)-oridisc) FROM orditems WHERE oriordid=%i AND oritypid IN (0, 3)", $id);
      //nactu si zpusob dopravy
      $paymode = dibi::fetch("
        SELECT delid, delmasid, delname, IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$priceSum.", 0, delprice".$this->curId.$userPrcCat.") AS delprice
        FROM deliverymodes 
        LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $userPrcCat, " AND discurid=%i", $this->curId, ")
        WHERE delid=%i", $order->orddelid
      );
      $delmode = dibi::fetch("
        SELECT delid, delmasid, delname, ".($delFreeCnt > 0 ? "IF(delid=18,delprice".$this->curId.$userPrcCat.",0)" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$priceSum.", 0, delprice".$this->curId.$userPrcCat.")")." AS delprice
        FROM deliverymodes 
        LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $userPrcCat, " AND discurid=%i", $this->curId, ")
        WHERE delid=%i", $paymode->delmasid
      );
      //zjistim ID polozky dopravy
      $delRow = dibi::fetch("SELECT oriid, oripricemaster FROM orditems WHERE oriordid=%i", $order->ordid , " AND oritypid=1");
      If ($delRow->oripricemaster === NULL) {
        if ($delFreeCnt > 0 && $delmode->delid != 18 && $this->curId == 1) {
          $delPrice = $paymode->delprice;
        } else {
          $delPrice = $paymode->delprice + $delmode->delprice;
        }
      } else {
        $delPrice = $delRow->oripricemaster;
      }

      $values = array(
        'oriprice' => $delPrice,
      );
      $orditems = $this->model->getOrdItemsModel();
      $orditems->update($delRow->oriid, $values);
    }

    if (!empty($order->ordcoucode)) {
      $cous = $this->model->getCouponsModel();
      $cou = $cous->validateCoupon($order->ordcoucode, 0, $order->ordmail, excludeOrdId: $order->ordid);
      //zjistim ID polozky slevy - slevový kupón
      $oriid = (int)dibi::fetchSingle("SELECT oriid FROM orditems WHERE oriordid=%i", $id , " AND oritypid=6");

      //zalozim polozku slevy
      $couValue = (double)$cou["data"]->couvalue;
      $couName = "Slevový kupón " . $cou["data"]->coucode . " (sleva " . $cou["data"]->couvalue . $cou["data"]->couvalueunit . ")";

      if ($cou["data"]->couvalueunit === "%") {

        //spočítám slevu v %
        //zjistím jestli má množstevní slevu
        $sumDisc = (int)dibi::fetchSingle("SELECT oriprice FROM orditems WHERE oriordid=%i", $id , " AND oritypid=3") ;

        $discValue = $priceSumAllProducts;

        if ($cou["data"]->coumanid > 0) {
          //slevňuji jen položky od určité značky
          //suma objednane zbozi pro vypocet % slevy z kupónu
          $discValue = (double)dibi::fetchSingle("
            SELECT SUM(oriprice*oriqty)
            FROM orditems
            INNER JOIN products ON (oriproid=proid)
            WHERE
            oriordid=%i AND pronotdisc != 1 AND 
            oritypid=0", $order->ordid, " AND promanid=%i", $cou["data"]->coumanid);

          $couValue = round($discValue * $cou["data"]->couvalue / 100);
        } else {
          $couValue = round(($discValue - $sumDisc) * $cou["data"]->couvalue / 100);
        }



        //vychází mu sleva v % z kupónu nemůže mít množstevní slevu
        //dibi::query("DELETE FROM orditems WHERE oriordid=%i", $id , " AND oritypid=3");
      }

      if ($oriid > 0 && $cou["status"] != 'ok') {
        //vymazu slevu
        $ordItems->delete($oriid);
      } else if ($oriid == 0 && $cou["status"] == 'ok' && !empty($cou["data"]->couvalue)) {
        $values = array(
          'oriordid' => $id,
          'oritypid' => 6, //slevový kupón
          'oriqty' => 1,
          'oriname' => $couName,
          'oriprice' => $couValue * -1,
        );
        $ordItems->insert($values);
      } else if ($oriid > 0 && $cou["status"] == 'ok' && !empty($cou["data"]->couvalue)) {
        $values = array(
          'oritypid' => 6,
          'oriname' => $couName,
          'oriprice' => $couValue * -1,
        );
        $ordItems->update($oriid, $values);
      }
    }

    //zjistim cenu objednavky
    $priceSum = dibi::fetchSingle("SELECT SUM((oriprice*oriqty)-oridisc) FROM orditems WHERE oriordid=%i", $id);
    $discSum = dibi::fetchSingle("SELECT SUM(oridisc) FROM orditems WHERE oriordid=%i", $id);
    
    $vat = (int)$config['VATTYPE_0'];
    $vatLow = (int)$config['VATTYPE_1'];
    $vatType = (string)$config['PRICEVAT'];
    if ($vatType == 'inclvat') {
      $priceSumVat = $priceSum;
      $priceSum = dibi::fetchSingle("SELECT SUM(((oriprice*oriqty)-oridisc)/(1+(IF(COALESCE(orivatid,0)=0,$vat,$vatLow)/100))) FROM orditems WHERE oriordid=%i", $id);
    } else {
      $priceSumVat = dibi::fetchSingle("SELECT SUM(((oriprice*oriqty)-oridisc)*(1+(IF(COALESCE(orivatid,0)=0,$vat,$vatLow)/100))) FROM orditems WHERE oriordid=%i", $id);
    }
    $priceSumVat = round($priceSumVat, 2);

    //zjistim celkovou hmotnost
    $weightSum = (double)dibi::fetchSingle("SELECT SUM(proweight*oriqty) FROM orditems INNER JOIN products ON (proid=oriproid) WHERE oriordid=%i", $id);

    $vals = array(
      'ordprice'=>round($priceSum, $this->curDigits),
      'ordpricevat'=>round($priceSumVat, $this->curDigits),
      'orddisc'=>round($discSum, $this->curDigits),
      'ordweight'=>$weightSum,
    );
    $this->update($id, $vals);
  }

  /**
   *
   *
   * @param integer $id
   * @return array
   * @throws \DibiException
   */
  public function blAnalyse($order) {
    $orders = array();
    //$order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
    if ($order->ordstatus != 7) {
      //vyberu vsechny objednavky ktere maji priznak spam a maji stejne udaje o ojednavateli
      $sql = "SELECT ordid, ordcode FROM orders WHERE ordstatus=7 AND ordid!=$order->ordid AND (
      ordilname='$order->ordilname' OR
      ".(empty($order->ordstlname) ? "" : " ordstlname='$order->ordstlname' OR ")."
      (ordistreet='$order->ordistreet' AND ordistreetno='$order->ordistreetno' AND ordicity='$order->ordicity' AND ordipostcode='$order->ordipostcode') OR
      ".(empty($order->ordststreet) ? "" : "(ordststreet='$order->ordststreet' AND ordststreetno='$order->ordststreetno' AND ordstcity='$order->ordstcity' AND ordstpostcode='$order->ordstpostcode') OR ")."
      ordmail='$order->ordmail'
      ".(empty($order->ordtel) ? "" : " OR ordtel='$order->ordtel'").")";
      $rows = dibi::fetchAll($sql);
      foreach ($rows as $row) {
        $orders[$row->ordid] = $row->ordid;
      }
    }
    return($orders);

  }

  public function makeInvoice($id) {
    $orders = $this->model->getOrdersModel();
    $order = $orders->load($id);
    if (!$order) {
      throw new \Exception('Příslušná objednávka nenalezena.');
    } else {
      if (!empty($order->ordinvcode)) {
        throw new \Exception('Faktura je už vystavená.');
      } else {
        //zjistim aktualni rok
        $year = (int)dibi::fetchSingle("SELECT YEAR(Now())");
        //zjistim maximalni cislo fa v tomto roce
        $lastInvCode = (int)dibi::fetchSingle("SELECT MAX(ordinvcode) FROM orders WHERE YEAR(ordinvdate)=$year");
        if ($lastInvCode === 0) {
          $str = $year.'0000';
          $lastInvCode = (int)$str;
        }
        $nextInvCode = $lastInvCode + 1;
        if (!$orders->update($id, array('ordinvcode'=>$nextInvCode, 'ordinvdate'=>new \DateTime))) {
          throw new \Exception('Fakturu se nepodařilo vystavit.');
        }
      }
    }
  }

  /********************* ciselniky *********************/

  /**
  * ciselnik ordstatus
  * @return array
  */
  public function getEnumOrdStatus() {
    return array(
      0 => 'Čeká na zpracování  (Blokována)', //MALL: BLOCKED
      1 => 'Vyřizuje se (Otevřená)', //MALL: OPEN
      //2 => 'Čeká na platbu',
      6 => 'Zaplaceno',
      8 => 'Připraveno k odběru',
      3 => 'Odeslána', //MALL: SHIPPED
      4 => 'Dodána', //MALL: DELIVERED
      5 => 'Vrácená', //MALL: RETURNED
      10 => 'Vrácená přepravcem', //MALL: RETURNED
      9 => 'Stornovaná', //MALL: CANCELED
      //7 => 'Černá listina',
    );
  }

  /**
  * ciselnik ordstatus
  * @return array
  */
  public function getEnumOrdStatusFront() {
    return array(
      0 => 'Čeká na zpracování',
      1 => 'Vyřizuje se',
      6 => 'Zaplaceno',
      8 => 'Připraveno k odběru',
      3 => 'Odeslána',
      4 => 'Dodána',
      5 => 'Vrácená',
      10 => 'Vrácená přepravcem',
      9 => 'Stornovaná',
    );
  }

  /**
  * ciselnik ordexported
  * @return array
  */
  public function getEnumOrdExported(): array {
    return array(
      0 => 'Čeká na export',
      1 => 'Exportována',
      2 => 'Export blokován',
    );
  }

    /**
  * ciselnik ordtype
  * @return array
  */
  public function getEnumOrdType(): array {
    return array(
      0 => 'eshop',
      1 => 'administrace',
      2 => 'mall',
      3 => 'přepravní štítek',
    );
  }

  public function getEnumOrdDelId($onlyActive=TRUE) {
    //naplnim zpusoby dopravy
    $rows = dibi::fetchAll("SELECT delid, delname FROM deliverymodes WHERE delmasid=0 " . ($onlyActive === TRUE ? "AND delstatus=0" : "") . " ORDER BY delorder");
    $deliveryModeRows  = array();
    foreach ($rows  as $row) {
      $deliveryModeRows[$row->delname] = dibi::query("SELECT delid, CONCAT('".$row->delname." / ', delname) AS delname FROM deliverymodes WHERE delmasid=%i " . ($onlyActive === TRUE ? "AND delstatus=0" : "") . " ORDER BY delorder", $row->delid)->fetchPairs('delid', 'delname');
    }
    return $deliveryModeRows;
  }

  public function getEnumOrdDelIdSimple() {
    //naplnim zpusoby dopravy
     return dibi::query("SELECT delid, delname FROM deliverymodes WHERE delmasid> 0 AND delstatus=0 ORDER BY delorder")->fetchPairs('delid', 'delname');
  }

  public function getEnumFirms() {
    //naplnim zpusoby dopravy
    return array(
      1 => 'Dodavatel - NEplátce DPH',
      2 => 'Dodavatel - plátce DPH',
    );
  }

}
