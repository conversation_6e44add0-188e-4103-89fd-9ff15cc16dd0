<?php
namespace Model;
use Nette, dibi;

class ConfigModel extends BaseModel {

  protected $tableName = "config";
  protected $fieldPrefix = "cfg";

  public function getConfig() {
    $cache = $this->cacheGet('configDb');
    if ($cache === FALSE) {
      //naplnim uzivatelske nastaveni do cache
      $result = dibi::query('SELECT * FROM config');
      $cache = $result->fetchPairs('cfgcode', 'cfgvalue');    
      
      if (isset($cache["PRICE2RATE"])) $cache["PRICE2RATE"] = (double)str_replace(',', '.', $cache["PRICE2RATE"]);

      //naformatuju pole HOLIDAYS_FORMATED
      $a = explode(',', trim($cache["HOLIDAYS"], ','));
      $cache["HOLIDAYS_FORMATED"] = array();
      foreach ($a as $d) {
        $ss = explode('.', $d);
        $ss[0] = substr('0'.$ss[0], -2);
        $ss[1] = substr('0'.$ss[1], -2);
        $cache["HOLIDAYS_FORMATED"][$ss[1].'-'.$ss[0]] = $ss[1].'-'.$ss[0];
      }

      $this->cacheSave('configDb', $cache);
    }
    $this->config = $cache;
    return $cache;
  }
  
}
