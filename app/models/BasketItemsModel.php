<?php
namespace Model;
use <PERSON>te, dibi;
                
class BasketItemsModel extends BaseModel {
  
  protected $tableName = "basketitems";
  protected $fieldPrefix = "bas";

  public function deleteProduct($usrId, $proId) {
    return dibi::query("DELETE FROM basketitems WHERE basusrid=%i", $usrId, " AND basproid=%i", $proId);
  }

  public function deleteUserProducts($usrId) {
    return dibi::query("DELETE FROM basketitems WHERE basusrid=%i", $usrId);
  }

  public function updateProduct($usrId, $proId, $proQty, $sesId) {
    if ((int)$usrId === 0) {
      return;
    }
    $cnt = (int)dibi::fetchSingle("SELECT count(basid) FROM basketitems WHERE basusrid=%i", $usrId, " AND basproid=%i", $proId);

    if ($cnt === 0) {
      $this->insert(array(
        'basproid' => $proId,
        'basqty' => $proQty,
        'basusrid' => $usrId,
        'bassesid' => $sesId,
      ));
    } else {
      dibi::query("UPDATE basketitems SET basqty=%i", $proQty, " WHERE basusrid=%i", $usrId, " AND basproid=%i", $proId);
    }
  }
}