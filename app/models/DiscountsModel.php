<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class DiscountsModel extends BaseModel {

  protected $tableName = "discounts";
  protected $fieldPrefix = "dis";


  public function cacheClean() {
    parent::cacheClean();
    $dels = $this->model->getDeliveryModesModel();
    $dels->cacheClean();
  }
  /********************* ciselniky *********************/

  /**
  * ciselnik admstatus
  * @return array
  */
  public function getEnumDisStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaná',
    );
  }
}
