<?php
namespace Model;
use <PERSON><PERSON>\Exception;
use <PERSON><PERSON>, dibi;

class ManufacturersModel extends BaseModel {

  protected $tableName = "manufacturers";
  protected $fieldPrefix = "man";

  /********************* ciselniky *********************/

  /**
  * ciselnik manstatus
  * @return array
  */
  public function getEnumManStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  /**
   * ciselnik usrstatus
   *
   * @param bool $onlyActive
   * @return array
   * @throws Exception
   */
  public function getEnumManId(bool $onlyActive=FALSE): array {
    return dibi::query("SELECT manid, manname FROM manufacturers " . ($onlyActive ? " WHERE manstatus=0 " : "") . " ORDER BY manname")->fetchPairs('manid', 'manname');
  }
}
