<?php
namespace Model;
use dibi;

class DeliveryModesModel extends BaseModel {

  protected $tableName = "deliverymodes";
  protected $fieldPrefix = "del";
  
  public function getDataSource($sql="") {
    if ($sql == "") $sql = $this->getSql();
    return dibi::dataSource($sql);
  }
  
  public function getSql () {
    return "SELECT *, 
      delprice".$this->curId."a AS delprice
      FROM $this->tableName";
  }
  
  public function getDelFreeLimit($usrPrcCat) {
    $cacheKey = 'delFreeLimit_'.$this->curId.$usrPrcCat;
    $delFreeLimit = $this->cacheGet($cacheKey);
    if ($delFreeLimit === FALSE) {
      //cache neni musim naplnit
      $delFreeLimit = dibi::fetchSingle("
      SELECT MIN(disfrom) 
      FROM deliverymodes
      INNER JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $usrPrcCat, " AND discurid=%i", $this->curId, ")
      WHERE (delcode<>'OSOBNE' OR delcode IS NULL) AND delmasid=0 AND delstatus=0 AND delid!=9
      ORDER BY disfrom");
      $this->cacheSave($cacheKey, $delFreeLimit);  
    }
    return $delFreeLimit;
  }

  /**
   * vrati dopravu podle delid
   * pokud delid = 0 vrati vsechny dopravy
   *
   * @param $delid
   * @param $basketPrice
   * @param $priceCat
   * @return \DibiRow
   * @throws \DibiException
   */
  public function getDelMode($delid, $basketPrice, $priceCat) {
    return dibi::fetch("
    SELECT *, IF(COALESCE(disform, 0) > 0 AND COALESCE(disform, 0) < $basketPrice, 0, delprice) AS delprice 
    FROM deliverymodes 
    LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $priceCat, " AND discurid=%i", $this->curId, ")
    WHERE delid=%i", $delid, " ORDER BY delorder");
  }

  /**
   * vrací kod dopravy u objednávky
   *
   * @param $order
   * @return string|null
   * @throws \DibiException
   */
  public function getDelCodeFromOrder($order) {
    $payId = $order->orddelid;
    $delId = (int)dibi::fetchSingle("SELECT delmasid FROM deliverymodes WHERE delid=%i", $payId);
    $delCode = Null;
    if ($delId > 0) {
      $delCode = (string)dibi::fetchSingle("SELECT delcode FROM deliverymodes WHERE delid=%i", $delId);
    }
    return $delCode;
  }

  /**
   * vcrací platbu a dopravu u objednávky
   *
   * @param $order
   * @return array|boolean
   * @throws \DibiException
   */
  public function getDeliveryPaymentFromOrder($order) {
    $payId = $order->orddelid;
    $pay = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payId);
    $del = null;
    if ($pay) {
      $del = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $pay->delmasid);
      if ($del) {
        return array(
          "delivery" => $del,
          "payment" => $pay,
        );
      }
    }
    return FALSE;
  }
  
  /********************* ciselniky *********************/

  /**
  * ciselnik delstatus
  * @return array
  */
  public function getEnumDelStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  public function getEnumPayTypes() {
    return array(
      'cash' => 'V hotovosti',
      'paybefore' => 'Platba předem',
      'cetelem' => 'cetelem',
      'dobirka' => 'Dobírka',
      'payonline' => 'Platba ONLINE',
      'creditcard' => 'Platba kartou',
      'mallpay' => 'MallPay',
    );
  }

public function getEnumDelTypes() {
    return array(
      'CESKA_POSTA' => 'Česká pošta',
      'CESKA_POSTA_NA_POSTU' => 'Česká pošta - Balík Na poštu',
      'CESKA_POSTA_BALIKOVNA' => 'Česká pošta - Balíkovna',
      'CSAD_LOGISTIK_OSTRAVA' => 'ČSAD Logistik Ostrava',
      'DPD' => 'DPD',
      'DPD_PICKUP' => 'DPD Pickup',
      'DHL' => 'DHL',
      'EMS' => 'EMS',
      'FOFR' => 'FOFR',
      'GEBRUDER_WEISS' => 'Gebrüder Weiss',
      'GEIS' => 'Geis',
      'GENERAL_PARCEL' => 'General Parcel',
      'GLS' => 'GLS',
      'HDS' => 'HDS',
      'HEUREKAPOINT' => 'HeurekaPoint',
      'INTIME' => 'InTime',
      'OSOBNE' => 'Osobně',
      'PPL' => 'PPL',
      'RADIALKA' => 'Radiálka',
      'SEEGMULLER' => 'Seegmuller',
      'TNT' => 'TNT',
      'TOPTRANS' => 'TOPTRANS',
      'UPS' => 'UPS',
      'ULOZENKA' => 'Uloženka',
      'VLASTNI_PREPRAVA' => 'Vlastní přeprava',
      'WEDO' => 'WE|DO',
      'ZASILKOVNA' => 'Zásilkovna',
      'ZASILKOVNA_NA_ADRESU' => 'Zásilkovna na adresu',

    );
  }

  /**
   * ciselnik zpusoby dodani
   *
   * @return array
   * @throws \DibiException
   */
  public function getEnumDelModes() {
    return dibi::query("SELECT * FROM deliverymodes WHERE delmasid=0 ORDER BY delorder")
      ->fetchPairs('delid', 'delname');
  }

  Public function getEnumUlozenkaPlaces() {
    return dibi::query("SELECT uloshortcut, uloname FROM ulozenkapoints WHERE ulostatus=0 ORDER BY ulocountry, uloname")->fetchPairs("uloshortcut", "uloname");
  }

  Public function getEnumWedoPlaces() {
    return dibi::query("SELECT wedcode, CONCAT(wedcity, ', ', wedstreet, ', ', wedname, ', ', wedpostcode, ' (', wedcode, ')') AS wedname FROM wedopoints WHERE wedstatus=0 ORDER BY wedcountry, wedname")->fetchPairs("wedcode", "wedname");
  }

  Public function getEnumBalikovnaPlaces() {
    return dibi::query("SELECT balid2, CONCAT(balcity, ', ', balstreet, ', ', balpostcode, ' (', balid2, ')') as balname FROM balikovnapoints WHERE balstatus=0 ORDER BY balname")->fetchPairs("balid2", "balname");
  }

  Public function getEnumZasilkovnaPlaces() {
    return dibi::query("SELECT zasid2, CONCAT(zascity, ', ', zasname, ', ', zaspostcode, ' (', zasid2, ')') as zasname FROM zasilkovnapoints ORDER BY zasname")->fetchPairs("zasid2", "zasname");
  }

  Public function getEnumDelPickupPlaces() {
    return dibi::query("SELECT dpdshopid2, CONCAT(dpdcity, ', ', dpdstreet, ' ', dpdstreetno, ', ', dpdpostcode, ', ', dpdname, ' (', dpdshopid2, ')') as dpdname FROM dpdpoints WHERE dpdstatus=0 ORDER BY dpdname")->fetchPairs("dpdshopid2", "dpdname");
  }
}
