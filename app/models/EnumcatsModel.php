<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class EnumcatsModel extends BaseModel {

  protected $tableName = "enumcats";
  protected $fieldPrefix = "enu";

  public function getEnumEnuTypId() {
    return array(
      1 => 'C<PERSON>lové země',
    );
  }

   public function getEnumEnuStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  public function getEnumDelRegions() {
    return array(
      1 => 'Region 1',
      2 => 'Region 2',
      3 => 'Region 3',
    );
  }

  public function getEnumCountries() {
    $arr = array();
    $arr["1"] = 'Česká republika';
    $arr["2"] = 'Slovensko';
    return $arr;
  }
}
