<?php
namespace Model;
use dibi;

class UsersModel extends BaseModel {

  const EVENT_REGISTER = 1;
  const EVENT_GDPR = 2;
  const EVENT_MAILLIST_ADD = 3;
  const EVENT_MAILLIST_REM = 4;
  const EVENT_MAIL_VERIFICATION_SEND = 5;
  const EVENT_MAIL_VERIFIED = 6;
  const EVENT_DELETED = 7;

  protected $tableName = "users";
  protected $fieldPrefix = "usr";

  public function checkDuplicityEMail($usrid, $value) {
    $cnt = dibi::fetchSingle("SELECT COUNT(*) AS cnt FROM $this->tableName WHERE usrmail='$value'".($usrid>0 ? " AND usrid!=$usrid" : ""));
    if ($cnt > 0) throw New \Exception("Tento email již existuje.");
  }

  /**
   * zaloguje novou událost
   *
   * @param $usrid
   * @param $eventid
   * @return \Dibi\Result|int
   * @throws \DibiException
   */
  public function logEvent($usrid, $eventid)  {
    //zapisu do logu
    return dibi::insert('users_log', array(
      'uslusrid'=>$usrid,
      'uslevtid'=>$eventid,
      'usldatec'=>new \DateTime,
    ))->execute(dibi::IDENTIFIER);
  }

  public function clearPersonalData($usrId) {
    $vals = array(
      'usrpassw' => '',
      'usrmail' => 'DELETED_'.$usrId,
      'usrstname' => '',
      'usrstlname' => '',
      'usrstfirname' => '',
      'usrststreet' => '',
      'usrststreetno' => '',
      'usrstcity' => '',
      'usrstpostcode' => '',
      'usrstcouid' => 0,
      'usrtel' => '',
      'usriname' => '',
      'usrilname' => '',
      'usrifirname' => '',
      'usristreet' => '',
      'usristreetno' => '',
      'usricity' => '',
      'usripostcode' => '',
      'usricouid' => 0,
      'usrvat' => 1,
      'usric' => '',
      'usrdic' => '',
      'usrip' => '',
      'usrnote' => 'účet vymazán',
      'usrmailvcode' => '',
      'usrmailverified' => 0,
      'usrmaillist' => 0,
      'usrstatus' => 1,
    );
    $this->update($usrId, $vals);
    $this->logEvent($usrId, self::EVENT_DELETED);
  }

  /********************* ciselniky *********************/

  /**
  * ciselnik usrstatus
  * @return array
  */
  public function getEnumUsrStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  public function getEnumUsrPrcCat($labels=array()) {
    $vals["a"] = (isset($labels["a"]) ? $labels["a"] : 'cena A'); 
    $vals["b"] = (isset($labels["b"]) ? $labels["b"] : 'cena B'); 
    $vals["c"] = (isset($labels["c"]) ? $labels["c"] : 'cena C'); 
    $vals["d"] = (isset($labels["d"]) ? $labels["d"] : 'cena D'); 
    $vals["e"] = (isset($labels["e"]) ? $labels["e"] : 'cena E');
    return $vals;
  }

  public function  getEnumUslEvtId() {
    return array(
      self::EVENT_REGISTER => 'Registrace',
      self::EVENT_GDPR => 'Souhlas s uchováním osobních údajů',
      self::EVENT_MAILLIST_ADD => 'Přihlášení do maillingu',
      self::EVENT_MAILLIST_REM => 'Odhlášení z mailingu',
      self::EVENT_MAIL_VERIFIED => 'Email ověřen',
      self::EVENT_MAIL_VERIFICATION_SEND => 'Odeslána žádost o ověření',
      self::EVENT_DELETED => 'Vymazaný'
    );
  }

  function generatePassword($length = 16) {
    $seed = str_split('abcdefghijklmnopqrstuvwxyz'
                 .'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
                 .'0123456789!@#$%^&*()'); // and any other characters
    shuffle($seed); // probably optional since array_is randomized; this may be redundant
    $rand = '';
    foreach (array_rand($seed, $length) as $k) {
      $rand .= $seed[$k];
    }

    return (password_hash($rand, PASSWORD_DEFAULT));
  }
}
