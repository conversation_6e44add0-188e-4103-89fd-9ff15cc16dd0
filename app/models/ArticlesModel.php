<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class ArticlesModel extends BaseModel {

  protected $tableName = "articles";
  protected $fieldPrefix = "art";

  public function runCounter($id) {
    return dibi::query("UPDATE articles SET artcnt=artcnt+1 WHERE artid=%i", $id);
  }
  
  /********************* ciselniky *********************/
  /**
  * ciselnik typy <PERSON>ů
  * @return array
  */
  public function getEnumArtTypId() {
    return array(
      237 => 'Suplementy',
      251 => '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      238 => 'Strava',
      280 => 'Recenze',
      252 => 'Ostatní',
    );
  }
  
  public function getEnumArtTop() {
    return array(
      0 => 'žádné',
      1 => 'Odborné články',
      2 => 'Proč nakupovat u nás',
    );
  }
  
  public function getEnumArtStatus() {
    return array(
      0 => 'Aktivní',
      1 => '<PERSON><PERSON>kovaný',
    );
  }
}
