<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class DictionariesModel extends BaseModel {

  protected $tableName = "dictionaries";
  protected $fieldPrefix = "dic";

  static function getDictionary($lng) {
    $cache = $this->cacheGet('dic');
    if (!$cache) {
      unset($cache);
      $result = dibi::query("SELECT dicfrom, dicto_$lng FROM dictionaries")->fetchPairs('dicfrom', 'dicto_'.$lng);
      $cache = array(Nette\Caching\Cache::TAGS => array('dictionaries'));
      $this->cacheSave('dic', $cache);
    }
    return $cache;
  }
}
