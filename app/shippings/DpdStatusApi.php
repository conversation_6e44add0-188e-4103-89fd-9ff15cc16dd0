<?php
/**
* DPD GetParcelStatus
*
* <AUTHOR> | zvarik.cz
* @version 2018-03-20
*
* @see https://www.dpd.com/cz/business_customers/vas_pruvodce_prepravou/aplikace_a_nastroje/dpd_geoapi
* @see https://www.dpd.com/cz/content/download/6062/104117/file/DPD_API_GetParcelStatus_Quick_Guide_1.3.pdf
*/
class DpdStatusApi
{
	private $login;
	private $pswd;
	private $client;

	/**
	* __construct
	*/
	public function __construct($login, $pswd)
	{
		$this->login = $login;
		$this->pswd = $pswd;

		$url = 'https://reg-prijemce.dpd.cz/Product_api/Product_api.svc?wsdl';

		$client = new SoapClient(
			$url,
			array(
				'location' => $url,
				'trace' => 1,
				'exceptions' => 0, // Soap vyhodí vlastní chybu s popisem
				'encoding' => 'UTF-8',
				'stream_context'=> stream_context_create(array(
					'ssl'=> array(
						'verify_peer'=>false,
						'verify_peer_name'=>false,
						'allow_self_signed' => true
						)
					)
				)
			)
		);

		$this->client = $client;

	}


  /**
   * Volání nějaké metody
   *
   * @param $name
   * @param $arguments
   * @return
   */
	public function __call($name, $arguments)
	{
		$args = $arguments[0];
		$args['username'] = $this->login; // Pozor: 'username' nikoliv 'login'
		$args['password'] = $this->pswd;

		$response = $this->client->$name($args);

		if (isset($response->{$name.'Result'})) {
			$response = $response->{$name.'Result'};
		}
		if (isset($response->{$name.'ResultVO'})) {
			$response = $response->{$name.'ResultVO'};
		}

		if (!empty($response->FaultMessage) && $response->FaultMessage->Code != 0) {
			throw new \RuntimeException( $response->FaultMessage->Description, (int) $response->FaultMessage->Code);
		}
		return $response;
	}
}

