<?php
/**
* DPD API Shipping 1.1
 *
 * https://nst-preprod.dpsin.dpdgroup.com/api/docs/#create-shipment
 * https://nst-preprod.dpsin.dpdgroup.com/api/docs/page/v1.0/
*/

namespace App\Shippings;

use <PERSON><PERSON>;
use <PERSON><PERSON>\Exception;
use ModelFactory;

include LIBS_DIR . '/DpdApi/ShippingApi/class_DpdShippingApi.php';

class DpdShippingApi extends BaseApi {

  public array $errMsg = array();

  /* @/class_MojeDpd */
  private \DpdShippingApi $dpd;

  /* @string */
  private string $apiKey;

  /* @string */
  private string $customerId;

  /* @string */
  private string $serviceId;

  /* @string */
  private string $parcelShopServiceId;

  /* @string */
  private array $mainServiceElementCodes;

  /**
  * @var ModelFactory
  */
  public ModelFactory $model;

  public function __construct(array $config, ModelFactory $model) {

    $this->model = $model;

    $this->customerId = (int)$config["customerId"];
    $this->serviceId = (int)$config["serviceId"];
    $this->parcelShopServiceId = (int)$config["parcelShopServiceId"];
    $this->mainServiceElementCodes = $config["mainServiceElementCodes"];
    $this->senderAddressId = (int)$config["senderAddressId"];

    $this->dpd = new \DpdShippingApi($config["apiKey"]);
  }

  /**
   * POST consignments
   * /v3/consignments{?timeFrom,updatedFrom,limit,offset}
   *
   * Vytvoření nové zásilky
   *
   * @param $order
   * @return array|bool parcels
   * @throws Exception
   */
  public function prepareShipmentData($order, int $counter=1): bool|array {
    $ords = $this->model->getOrdersModel();

    if (!empty($order->ordparcode)) {
      $this->errMsg[] = "Obj. č.: " . $order->ordparcode . " už má přiřazené číslo balíku.";
      return FALSE;
    }

    $phone = trim($order->ordtel);
    $phone = str_replace(' ', '', $phone);
    $phone = substr($phone, -9);

    $phonePrefix = (int)$order->ordcurid === 1 ? "+420" : "+421";
    //nactu zpusob platby
    $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
    $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);
    //pokud je jina dodaci adresa
    if (!empty($order->ordstname)) {
      $order->ordiname = $order->ordstname;
      $order->ordilname = $order->ordstlname;
      $order->ordifirname = $order->ordstfirname;
      $order->ordistreet = $order->ordststreet;
      $order->ordistreetno = $order->ordststreetno;
      $order->ordicity = $order->ordstcity;
      $order->ordipostcode = $order->ordstpostcode;
    }

    $ReferenceNumber = (!empty($order->ordmalid) ? substr(trim($order->ordmalid), 0, 9) : $order->ordcode);
    $ReferenceNumber1 = (!empty($order->ordmalid) ? trim($order->ordmalid) : $order->ordcode);

    $shipment = [
      'numOrder' => $counter,
			'senderAddressId' => $this->senderAddressId,
      //'pickupDate' => $this->dpd->getNextPossiblePickup(), // vrátí další pracovní den, pokud je dnešek po 9. hodině
      //'fromTime' => '09:00',
      //'toTime' => '12:00',
      'reference1' => $ReferenceNumber,
      'reference2' => $ReferenceNumber1,
      'reference3' => '',
      'reference4' => '',
      'saveMode' => 'printed',
      'printFormat' => 'PDF', // PDF nebo ZPL
      'labelSize' => 'A4',  // A4 nebo A6
      'extendShipmentData' => true,
      'printRef1AsBarcode' => false
    ];

    $receiver = array(
      'companyName' => $order->ordifirname,
      'companyName2' => '',
      'contactEmail' => $order->ordmail,
      'contactFax' => '',
      'contactFaxPrefix' => '',
      'contactInterphoneName' => '',
      'contactMobile' => $phone,
      'contactMobilePrefix' => $phonePrefix,
      'contactName' => $order->ordiname . " " . $order->ordilname,
      'contactPhone' => '',
      'contactPhonePrefix' => '',
      'countryCode' => $order->ordcurid === 1 ? 'CZ' : 'SK',
      'department' => '',
      'doorCode' => '',
      'flatNo' => '',
      'floor' => '',
      'name' => $order->ordiname . " " . $order->ordilname,
      'name2' => '',
      'street' => $order->ordistreet,
      'houseNo' => $order->ordistreetno,
      'city' => $order->ordicity,
      'zipCode' => str_replace(" ", "", trim($order->ordipostcode)),
      'additionalAddressInfo' => '',
      'address2' => '',
      'address3' => '',
    );

    //SK/CZ
    //defaultně posílám v CZK
    $currency = 'CZK';
    $price = (double)$order->ordpricevat;
    if (!empty($order->ordpricecod)) {
      $price = (double)$order->ordpricecod;
    }

    if ((int)$order->ordcurid === 2) {
      $currency = 'EUR';
    }

    //balíky
    if (empty($order->ordparcelscount)) {
      $order->ordparcelscount = 1;
    }

    //pokudje vyplněna hmotnost, rozpočtám ji
    $ordweight = 2;
    if (!empty($order->ordweight)) {
      $ordweight = round((double)$order->ordweight/(int)$order->ordparcelscount, 1);
    }

    $parcels = [];
    $service = [];
    for ($i = 1; $i <= $order->ordparcelscount; $i++) {
      $parcels[] = [
        //'dimensionHeight' => NULL,
        //'dimensionLength' => NULL,
        //'dimensionWidth' => NULL,
        'limitedQuantity' => false,
        'reference1' => $ReferenceNumber . "_" . $i,
        'reference2' => $ReferenceNumber1 . "_" . $i,
        'reference3' => '',
        'reference4' => '',
        'weight' => $ordweight,
        'insCurrency' => '',
        'codCurrency' => ''
      ];
    }

    //AdditionalService Dobírka
    if ($payMode->delcode == 'cash' || $payMode->delcode == 'dobirka') {
      $service["additionalService"]["cod"] = [
        'amount' => $price, //Double M COD amount
        'currency' => $currency, //String M COD currency (EUR, LAT…)
        'paymentType' => 'Cash', // Enum M it can be Cash, CreditCard, CrossedCheck
        'referenceNumber' => $ReferenceNumber,
        'reference' => $ReferenceNumber,
        'split' => 'First parcel'
      ];
    }

    $serviceId = $this->serviceId;

    if ($delMode->delcode == 'DPD_PICKUP' && !empty($order->orddelspec)) {
      //pro DPD pickup je jiná služba
      $serviceId = $this->parcelShopServiceId;

      $dpds = $this->model->getDpdpointsModel();
      $point = $dpds->load($order->orddelspec, "shopid2");
      if ($point) {
        $service["additionalService"]["pudoId"] = $point->dpdshopid2;
      } else {
        $this->errMsg[] = "ordId:" . $order->ordid . " | parcelShopId:" . $order->orddelspec . " nenalezen";
        \Tracy\Debugger::log("ordId:" . $order->ordid . " | parcelShopId:" . $order->orddelspec . " nenalezen");
      }
    }

    $service["mainServiceCode"] = '';
    $service["mainServiceElementCodes"] = $this->mainServiceElementCodes[$serviceId];

    if ($price > 50000) {
      $service["additionalService"]["highInsurance"] = array(
        'goodsValue' => $price, //Double M goods value amount
        'currency' => $currency, //String M COD currency (EUR, LAT…)
        'content' => 'sportovní výživa', // String M goods content
      );
    }

    $service["additionalService"]["predicts"][] = [
      "destination" => $order->ordmail,
      "type" => "email"
    ];

    $service["additionalService"]["predicts"][] = [
      "destination" => $phonePrefix . $phone,
      "type" => "SMS"
    ];

    $shipment["receiver"] = $receiver;
    $shipment["parcels"] = $parcels;
    $shipment["service"] = $service;

    $data = [
      "customerId" => (int)$this->customerId,
    ];

    $data["shipments"][] = $shipment;

    return $data;
  }

  public function createShipment($parcels): bool {

    // vytvoříme novou zásilku
    $ords = $this->model->getOrdersModel();

    try {
      $res = $this->dpd->curl('shipments', $parcels);
    } catch (Exception $e) {
      $this->errMsg[] = 'Chyba: [' . $e->getCode() . '] ' . $e->getMessage(); // nějaká chyba
    }

    $parcelNumbers = [];

    foreach ($res->shipmentResults as $shipment) {

      //nastala chyba?
      if (isset($shipment->errors) && count($shipment->errors) > 0) {
        foreach ($shipment->errors as $error) {
          $this->errMsg[] = $error->errorCode . "|" . $error->errorContent;
        }
        return FALSE;
      }

      $shipmentId = $shipment->shipment->shipmentId; // ID zásilky
      $ordCode = (string)$shipment->shipment->shpReference1;
      $ordCode1 = (string)$shipment->shipment->shpReference2;

      $ord = $ords->load($ordCode, "code");
      if (!$ord) {
        //není použito jako referenceNumber číslo objednávky
        //načtu podle mall
        $ordid = (int)dibi::fetchSingle("SELECT ordid FROM orders WHERE ordmalid=%s ORDER BY ordid DESC", $ordCode1);
      } else {
        $ordid = $ord->ordid;
      }

      // tady jsou 3 čísla všech balíků v zásilce
      foreach ($shipment->shipment->parcels as $parcel) {
        $parcelNumbers[] = $parcel->parcelNumber;
      }

      if ($ordid > 0) {
        $ords->update($ordid, [
          'ordparid' => $shipmentId,
          'ordparcode' => $parcelNumbers[0],
        ]);
      }
    }

    return count($this->errMsg) === 0;
  }

  public function getShipmentLabel($shipmentIdList): bool {
    try {
      $out = $this->dpd->curl('https://shipping.dpdgroup.com/api/v1.0/label/shipment-ids', [
        "customerId" => $this->customerId,
        "labelSize" => "A4",
        "printFormat" => "pdf",
        "shipmentIdList" => $shipmentIdList
    ]);

    } catch (Exception $e) {
      $this->errMsg[] = 'Chyba: ' . $e->getMessage(); // nějaká chyba
      return FALSE;
    }

    if (!empty($out->pdfFile)) {
      $this->downloadPdf($this->dpd->parseLabelFile($out->pdfFile), 'stitky');
    }
    return TRUE;
  }

  public function reprintShipmentLabel($shipmentIdList): bool {
    try {
      $ret = $this->getShipmentLabel($shipmentIdList);
      print_r($ret);
    } catch (Exception $e) {
      $this->errMsg[] = 'Chyba: ' . $e->getMessage(); // nějaká chyba
      return FALSE;
    }

    return TRUE;
  }

  public function deleteShipment($parcels) {
    $ords = $this->model->getOrdersModel();
    try {

      $res = $this->dpd->curl('shipments/cancellation', [
        'customerId' => $this->customerId,
        'shipmentIdList' => $parcels
      ], 'PUT');

      //projdu co vrátil a úspěšně vymazané vymažu i v eshopu
      foreach ($res->resultList as $result) {
        if (!empty($result->errors)) {
          foreach ($result->errors as $error) {
            $this->errMsg[] = 'Chyba: ' . $error->errorCode . "|" . $error->errorContent; // nějaká chyba
          }
        } else {
          if (isset($result->shipmentId)) {
            $shipmentId = $result->shipmentId;
            //balík byl vymazaný
            $ord = $ords->load($shipmentId, "parid");
            $ords->update($ord->ordid, array("ordparcode" => NULL), array("ordparid" => NULL));
          }

        }
      }
      return count($this->errMsg) === 0;
    } catch (Exception $e) {
      $this->errMsg[] = 'Chyba: [' . $e->getCode() . '] ' . $e->getMessage(); // nějaká chyba
      return FALSE;
    }
  }

  public function closeManifest($parcels) {
    try {
      $dpms = $this->model->getDpdManifestsModel();
      $dpmId = $dpms->getNewManifest();

      $ret = $this->dpd->closeManifest($dpmId, $parcels);
    } catch (Exception $e) {
      $this->errMsg[] = 'Chyba: ' . $e->getMessage(); // nějaká chyba
      return FALSE;
    }
    if (!empty($ret->return->error->text)) {
      $this->errMsg[] = 'Chyba: ' . $ret->return->error->text; // nějaká chyba
      return FALSE;
    }

    if (!empty($ret->return->pdfManifestFile)) {
      $this->downloadPdf($ret->return->pdfManifestFile, 'soupiska');
    }

    if (!empty($ret->return->manifestId)) {
      $dpms->update($dpmId, array("dpmmanid" => $ret->return->manifestId));
      return $dpmId;
    }
    return false;
  }

  public function reprintManifest($dpmId) {
    try {
      $dpmmanid = (int)dibi::fetchSingle("SELECT dpmmanid FROM dpdmanifests WHERE dpmid=%i", $dpmId);
      if ($dpmmanid > 0) {
        $ret = $this->dpd->reprintManifest(array(
          "referenceNumber"=>$dpmId,
          "id"=>$dpmmanid,
          )
        );
      }
    } catch (Exception $e) {
      $this->errMsg[] = 'Chyba: ' . $e->getMessage(); // nějaká chyba
      return FALSE;
    }
    if (!empty($ret->return->error->text)) {
      $this->errMsg[] = 'Chyba: ' . $ret->return->error->text; // nějaká chyba
      return FALSE;
    }

    if (!empty($ret->return->pdfManifestFile)) {
      $this->downloadPdf($ret->return->pdfManifestFile, 'soupiska');
      return TRUE;
    }
    return FALSE;
  }

  private function downloadPdf($file, $fileName='stitky') {

    /*
    header('Content-type: application/pdf');
    echo $file;
    exit;
    */

    @unlink(TEMP_DIR . '/print.pdf');
    file_put_contents(TEMP_DIR . '/print.pdf', $file);

    header("Content-type:application/pdf");
    header("Content-Disposition:attachment;filename=$fileName.pdf");
    readfile(TEMP_DIR . '/print.pdf');

    /*
    header('Content-Disposition: attachment;filename="dpd_' . $fileName . '.pdf"');
    header('Content-Type: application/force-download');
    header('Content-Length: ' . (strlen($file)));
    ob_flush();
    flush();
    */

    echo $file;
  }

  public function getShipmentStatus($parCode, $ordCode) {
    $data = array();
    $ReferenceVo = array(
      'id' => $parCode,
      'referenceNumber' => $ordCode,
    );

    $ret = $this->dpd->getShipmentStatus($ReferenceVo);
    if ($ret !== FALSE && !empty($ret->result->statusInfoList->statusInfo)) {
      if (!empty($ret->result->statusInfoList->statusInfo->parcelNo)) {
        $data["parcelNo"] = $ret->result->statusInfoList->statusInfo->parcelNo;
      }
      if (!empty($ret->result->statusInfoList->statusInfo->scans)) {
        $data["scans"] = $ret->result->statusInfoList->statusInfo->scans;
      }
    }
    return $data;
  }

  public function getShipments(array $shipmentIds) {
    try {
      $out = $this->dpd->curl('https://shipping.dpdgroup.com/api/v1.0/shipments/ids', [
        "customerId" => $this->customerId,
        "shipmentIdList" => $shipmentIds
    ]);

    } catch (Exception $e) {
      $this->errMsg[] = 'Chyba: ' . $e->getMessage(); // nějaká chyba
      return FALSE;
    }

    return $out->shipmentResults;
  }

  public function updateBranches() {
    $dpds = $this->model->getDpdpointsModel();
    $start = dibi::fetchSingle("SELECT now()");

    //načtu země
    $url = "https://pickup.dpd.cz/api/get-countries";
    $json = file_get_contents($url);
    $jsonDecoded = json_decode($json);
    $data = $jsonDecoded->data->items;

    $countries[203] = [];
    $countries[703] = [];

    foreach ($data as $row) {
      if (isset($countries[$row->id])) {
        $countries[$row->id] = [
          "id" => $row->id,
          "name" => $row->name,
          "code" => $row->iso,
          "cod_allowed" => $row->cod_allowed,
        ];
      }
    }
    foreach ($countries as $country) {
      //zjistím info o cílové zemi

      $url = 'https://pickup.dpd.cz/api/get-all?country=' . $country["id"];
      $json = file_get_contents($url);
      $jsonDecoded = json_decode($json);
      $data = $jsonDecoded->data->items;
      foreach ($data as $row) {
        $vals = array(
          'dpdshopid' => substr($row->id, 2),
          'dpdshopid2' => $row->id,
          'dpdname' => (string)$row->company,
          'dpdstreet' => (string)$row->street,
          'dpdstreetno' => $row->house_number,
          'dpdcity' => (string)$row->city,
          'dpdpostcode' => (string)$row->postcode,
          'dpdcountrycode' => $country["code"],
          'dpdemail' => (string)$row->email,
          'dpdphone' => (string)$row->phone,
          'dpdurl' => (string)$row->homepage,
          'dpdurlphoto' => (string)$row->photo,
          'dpdgpsn' => (string)$row->latitude,
          'dpdgpse' => (string)$row->longitude,
        );

        $id = (int)dibi::fetchSingle("SELECT dpdid FROM dpdpoints WHERE dpdshopid2=%s", (string)$row->id);

        if ($id > 0) {
          $dpds->update($id, $vals);
        } else {
          $dpds->insert($vals);
        }
      }
    }
    dibi::query("DELETE FROM dpdpoints WHERE coalesce(dpddateu, dpddatec)<%dt", $start);
  }
}