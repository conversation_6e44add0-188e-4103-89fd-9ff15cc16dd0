<?php
/**
* Dpd API
*/

namespace App\Shippings;

use <PERSON><PERSON>;
use <PERSON><PERSON>\Exception;
use ModelFactory;
use MojeDpd;
use <PERSON>\Debugger;

include LIBS_DIR . '/DpdApi/class_MojeDpd.php';

class DpdApi extends BaseApi {

  public array $errMsg = array();

  /* @/class_MojeDpd */
  private MojeDpd $dpd;

  /* @string */
  private mixed $payerId;

  /* @string */
  private mixed $senderAddressId;

  /* @string */
  private mixed $serviceId;

  /* @string */
  private mixed $parcelShopServiceId;

  /**
  * @var ModelFactory
  */
  public ModelFactory $model;

  public function __construct(array $config, ModelFactory $model) {
    $login = $config["login"];
    $passw = $config["passw"];

    $this->model = $model;

    $this->payerId = $config["payerId"];
    $this->senderAddressId = $config["senderAddressId"];
    $this->serviceId = $config["serviceId"];
    $this->parcelShopServiceId = $config["parcelShopServiceId"];

    $this->dpd = new MojeDpd($login, $passw);
  }

  /**
   * POST consignments
   * /v3/consignments{?timeFrom,updatedFrom,limit,offset}
   *
   * Vytvoření nové zásilky
   *
   * @param $order
   * @return array|bool parcels
   * @throws Exception
   */
  public function prepareShipmentData($order): bool|array {
    $ords = $this->model->getOrdersModel();

    if (!empty($order->ordparcode)) {
      $this->errMsg[] = "Obj. č.: " . $order->ordparcode . " už má přiřazené číslo balíku.";
      return FALSE;
    }

    $phone = trim($order->ordtel);
    $phone = str_replace(' ', '', $phone);
    $phone = ((int)$order->ordcurid === 1 ? "+420" : "+421") . substr($phone, -9);
    //nactu zpusob platby
    $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
    $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);
    //pokud je jina dodaci adresa
    if (!empty($order->ordstname)) {
      $order->ordiname = $order->ordstname;
      $order->ordilname = $order->ordstlname;
      $order->ordifirname = $order->ordstfirname;
      $order->ordistreet = $order->ordststreet;
      $order->ordistreetno = $order->ordststreetno;
      $order->ordicity = $order->ordstcity;
      $order->ordipostcode = $order->ordstpostcode;
    }

    $ReferenceNumber = (!empty($order->ordmalid) ? substr(trim($order->ordmalid), 0, 9) : $order->ordcode);
    $ReferenceNumber1 = (!empty($order->ordmalid) ? trim($order->ordmalid) : $order->ordcode);

    $shipment = array(
      'shipmentReferenceNumber' => $ReferenceNumber,
      'shipmentReferenceNumber1' => $ReferenceNumber1,
      'payerId' => $this->payerId, //Long M DPD SYSTEM customer id for payer
      'senderAddressId' => $this->senderAddressId, //Long M DPD SYSTEM address id for sender (shiping address)
      'receiverName' => $order->ordiname . " " . $order->ordilname, //String M Receiver name
      'receiverFirmName' => $order->ordifirname, //String O Receiver firm name
      'receiverCountryCode' => $order->ordcurid === 1 ? 'CZ' : 'SK', //String M Receiver country code (EE, LV…)
      'receiverZipCode' => str_replace(" ", "", trim($order->ordipostcode)), //String M Receiver zip code
      'receiverCity' => $order->ordicity, //String M Receiver city
      'receiverStreet' => $order->ordistreet, //String M Receiver address information
      'receiverHouseNo' => mb_substr($order->ordistreetno, 0, 10, 'utf8'), //String O Receiver house number
      'receiverPhoneNo' => $phone, // String O Receiver phone number
      'mainServiceCode' => $this->serviceId, // Long M DPD SYSTEM main Product id
    );

    //balíky
    if (empty($order->ordparcelscount)) {
      $order->ordparcelscount = 1;
    }

    //pokudje vyplněna hmotnost, rozpočtám ji
    $ordweight = 2;
    if (!empty($order->ordweight)) {
      $ordweight = round((double)$order->ordweight/(int)$order->ordparcelscount, 1);
    }

    for ($i = 1; $i <= $order->ordparcelscount; $i++) {
      $shipment["parcels"][] = array(
        'parcelId' => $i, //long O DPD SYSTEM parcel Id.
        'parcelReferenceNumber' => $ReferenceNumber . "_" . $i, // String M Integrators parcel id
        'parcelReferenceNumber1' => $ReferenceNumber1 . "_" . $i, // String M Integrators parcel id
        'dimensionsHeight' => NULL, // double O Package height (meter)
        'dimensionsWidth' => NULL, // double O Package width (meter)
        'dimensionsLength' => NULL, // double O Package length (meter)
        'weight' => $ordweight, // double M Package weight (kg)
        'description' => NULL, // String O Package description
      );
    }

    //SK/CZ
    //defaultně posílám v CZK
    $currency = 'CZK';
    $price = (double)$order->ordpricevat;
    if (!empty($order->ordpricecod)) {
      $price = (double)$order->ordpricecod;
    }

    if ((int)$order->ordcurid === 2) {
      $currency = 'EUR';
    }

    //AdditionalService Dobírka
    if ($payMode->delcode == 'cash' || $payMode->delcode == 'dobirka') {
      $shipment["additionalServices"]["cod"] = array(
        'amount' => $price, //Double M COD amount
        'currency' => $currency, //String M COD currency (EUR, LAT…)
        'paymentType' => 'Cash', // Enum M it can be Cash, CreditCard, CrossedCheck
        'referenceNumber' => $ReferenceNumber, //String O
      );
    }

    if ($delMode->delcode == 'DPD_PICKUP' && !empty($order->orddelspec)) {
      //pro DPD pickup je jiná služba
      $shipment["mainServiceCode"] = $this->parcelShopServiceId;

      $dpds = $this->model->getDpdpointsModel();
      $point = $dpds->load($order->orddelspec, "shopid2");
      if ($point) {
        $shipment["additionalServices"]["parcelShop"] = [
          'parcelShopId' => $point->dpdshopid2, //long M Parcel shop Id
          'phoneNo' => $phone,
          /*
          'fetchGsPUDOpoint' => 0,
          'companyName' => $point->dpdname, //String M Parcel shop company name
          'street' => $point->dpdstreet, //String M Parcel shop street
          'houseNo' => $point->dpdstreetno, //String M Parcel shop house number
          'countryCode' => strtoupper($point->dpdcountrycode), //String M Parcel shop country code alpha
          'zipCode' => $point->dpdpostcode, //String M Parcel shop zip code
          'city' => $point->dpdcity
          */
        ];
      } else {
        $this->errMsg[] = "ordId:" . $order->ordid . " | parcelShopId:" . $order->orddelspec . " nenalezen";
        \Tracy\Debugger::log("ordId:" . $order->ordid . " | parcelShopId:" . $order->orddelspec . " nenalezen");
      }
    }

    $shipment["additionalServices"]["highInsurance"] = array(
      'goodsValue' => $price, //Double M goods value amount
      'currency' => $currency, //String M COD currency (EUR, LAT…)
      'content' => 'sportovní výživa', // String M goods content
    );

    return $shipment;
  }

  public function createShipment($parcels): bool {
    // vytvoříme novou zásilku
    $ords = $this->model->getOrdersModel();
    try {
      $res = $this->dpd->createShipment($parcels);

      if (!empty($res->result->resultList->error->text)) {
        $this->errMsg[] = 'Chyba: ' . $res->result->resultList->error->text; // nějaká chyba
        return FALSE;
      }
      if (!empty($res->result->resultList->shipmentReference)) {
        $resultList[] = $res->result->resultList;
      } else {
        $resultList = $res->result->resultList;
      }

      $data = array();
      foreach ($resultList as $result) {

        $parNumber = NULL;
        if (!empty($result->error->text)) {
          $this->errMsg[] = 'Chyba: ' . $result->error->text; // nějaká chyba
        } else {
          if (isset($result->shipmentReference->id)) {
            $parNumber = (string)$result->shipmentReference->id;
            $ordCode = (string)$result->shipmentReference->referenceNumber;
            $ordCode1 = (string)$result->shipmentReference->referenceNumber1;
          }
          if (isset($result->id)) {
            $parNumber = (string)$result->id;
            $ordCode = (string)$result->referenceNumber;
            $ordCode1 = (string)$result->referenceNumber1;
          }
          //balík byl vytvořený
          $ord = $ords->load($ordCode, "code");
          if ($ord == FALSE) {
            //není použito jako referenceNumber číslo objednávky
            //načtu podle mall
            $ordid = (int)dibi::fetchSingle("SELECT ordid FROM orders WHERE ordmalid=%s ORDER BY ordid DESC", $ordCode1);
          } else {
            $ordid = $ord->ordid;
          }

          \Tracy\Debugger::log("ordId:" . $ordid . " | ordCode:" . $ordCode . "|" . $ordCode1 . " | parNumber:" . $parNumber);
          if ($ordid > 0) {
            $ords->update($ordid, array('ordparid' => $parNumber));
          }
          //poskládám seznam zásilek
          $data[] = array(
            'referenceNumber' => $ordCode,
            'id' => $parNumber,
          );
        }
      }
      if (count($data) > 0) {
        $ret = $this->getShipmentLabel($data);
        if ($ret) {
          foreach ($data as $item) {

            $ret = $this->getShipmentStatus($item["id"], $item["referenceNumber"]);

             \Tracy\Debugger::log("ordId:" . $item["id"] . " | referenceNumber:" . $item["referenceNumber"] . " | parcelNo:" . $ret["parcelNo"]);

            if (!empty($ret["parcelNo"])) {
              $ordCode = $item["referenceNumber"];
              $ord = $ords->load($ordCode, "code");
              if ($ord == FALSE) {
                //není použito jako referenceNumber číslo objednávky
                //načtu podle mall
                $ordid = (int)dibi::fetchSingle("SELECT ordid FROM orders WHERE ordmalid LIKE '" . $ordCode . "%' ORDER BY ordid DESC");
              } else {
                $ordid = $ord->ordid;
              }

              \Tracy\Debugger::log("ordId:" . $ordid . " | parcelNo:" . $ret["parcelNo"]);

              if ($ordid > 0) {
                $ords->update($ordid, array('ordparcode' => $ret["parcelNo"]));
              }
            }
          }
        }
      }
      return count($this->errMsg) === 0;
    } catch (Exception $e) {
      $this->errMsg[] = 'Chyba: [' . $e->getCode() . '] ' . $e->getMessage(); // nějaká chyba
      return FALSE;
    }
  }

  public function getShipmentLabel($data): bool {
    try {
      $ret = $this->dpd->getShipmentLabel($data);
    } catch (Exception $e) {
      $this->errMsg[] = 'Chyba: ' . $e->getMessage(); // nějaká chyba
      return FALSE;
    }

    if (!empty($ret->return->error->text)) {
      $this->errMsg[] = 'Chyba: ' . $ret->return->error->text; // nějaká chyba
      return FALSE;
    }

    if (!empty($ret->result->pdfFile)) {
      $this->downloadPdf($ret->result->pdfFile, 'stitky');
    }
    return TRUE;
  }

  public function reprintShipmentLabel($data): bool {
    try {
      $ret = $this->dpd->getShipmentLabel($data);
    } catch (Exception $e) {
      $this->errMsg[] = 'Chyba: ' . $e->getMessage(); // nějaká chyba
      return FALSE;
    }

    if (!empty($ret->return->error->text)) {
      $this->errMsg[] = 'Chyba: ' . $ret->return->error->text; // nějaká chyba
      return FALSE;
    }

    if (!empty($ret->result->error->text)) {
      $this->errMsg[] = 'Chyba: ' . $ret->result->error->text; // nějaká chyba
      return FALSE;
    }

    if (!empty($ret->result->pdfFile)) {
      $this->downloadPdf($ret->result->pdfFile, 'stitky');
    }
    return TRUE;
  }

  public function deleteShipment($parcels) {
    $ords = $this->model->getOrdersModel();
    try {
      $res = $this->dpd->deleteShipment($parcels);
      if (!empty($res->result->resultList->error->text)) {
        $this->errMsg[] = 'Chyba: ' . $res->result->resultList->error->text; // nějaká chyba
        return FALSE;
      }
      if (!empty($res->result->resultList->shipmentReference)) {
        $resultList[] = $res->result->resultList;
      } else {
        $resultList = $res->result->resultList;
      }
      //projdu co vrátil a úspěšně vymazané vymažu i v eshopu
      foreach ($resultList as $result) {
        if (!empty($result->error->text)) {
          $this->errMsg[] = 'Chyba: ' . $result->error->text; // nějaká chyba
        } else {
          if (isset($result->shipmentReference->id)) {
            $ordCode = $result->shipmentReference->referenceNumber;
          }
          if (isset($result->id)) {
            $ordCode = $result->referenceNumber;
          }
          //balík byl vymazaný
          $ord = $ords->load($ordCode, "code");
          $ords->update($ord->ordid, array("ordparcode" => NULL), array("ordparid" => NULL));
        }
      }
      return count($this->errMsg) === 0;
    } catch (Exception $e) {
      $this->errMsg[] = 'Chyba: [' . $e->getCode() . '] ' . $e->getMessage(); // nějaká chyba
      return FALSE;
    }
  }

  public function closeManifest($parcels) {
    try {
      $dpms = $this->model->getDpdManifestsModel();
      $dpmId = $dpms->getNewManifest();

      $ret = $this->dpd->closeManifest($dpmId, $parcels);
    } catch (Exception $e) {
      $this->errMsg[] = 'Chyba: ' . $e->getMessage(); // nějaká chyba
      return FALSE;
    }
    if (!empty($ret->return->error->text)) {
      $this->errMsg[] = 'Chyba: ' . $ret->return->error->text; // nějaká chyba
      return FALSE;
    }

    if (!empty($ret->return->pdfManifestFile)) {
      $this->downloadPdf($ret->return->pdfManifestFile, 'soupiska');
    }

    if (!empty($ret->return->manifestId)) {
      $dpms->update($dpmId, array("dpmmanid" => $ret->return->manifestId));
      return $dpmId;
    }
    return false;
  }

  public function reprintManifest($dpmId) {
    try {
      $dpmmanid = (int)dibi::fetchSingle("SELECT dpmmanid FROM dpdmanifests WHERE dpmid=%i", $dpmId);
      if ($dpmmanid > 0) {
        $ret = $this->dpd->reprintManifest(array(
          "referenceNumber"=>$dpmId,
          "id"=>$dpmmanid,
          )
        );
      }
    } catch (Exception $e) {
      $this->errMsg[] = 'Chyba: ' . $e->getMessage(); // nějaká chyba
      return FALSE;
    }
    if (!empty($ret->return->error->text)) {
      $this->errMsg[] = 'Chyba: ' . $ret->return->error->text; // nějaká chyba
      return FALSE;
    }

    if (!empty($ret->return->pdfManifestFile)) {
      $this->downloadPdf($ret->return->pdfManifestFile, 'soupiska');
      return TRUE;
    }
    return FALSE;
  }

  private function downloadPdf($file, $fileName='stitky') {
    @unlink(TEMP_DIR . '/print.pdf');
    file_put_contents(TEMP_DIR . '/print.pdf', $file);

    header("Content-type:application/pdf");
    header("Content-Disposition:attachment;filename=$fileName.pdf");
    readfile(TEMP_DIR . '/print.pdf');

    /*
    header('Content-Disposition: attachment;filename="dpd_' . $fileName . '.pdf"');
    header('Content-Type: application/force-download');
    header('Content-Length: ' . (strlen($file)));
    ob_flush();
    flush();
    */

    echo $file;
  }

  public function getShipmentStatus($parCode, $ordCode) {
    $data = array();
    $ReferenceVo = array(
      'id' => $parCode,
      'referenceNumber' => $ordCode,
    );

    $ret = $this->dpd->getShipmentStatus($ReferenceVo);
    if ($ret !== FALSE && !empty($ret->result->statusInfoList->statusInfo)) {
      if (!empty($ret->result->statusInfoList->statusInfo->parcelNo)) {
        $data["parcelNo"] = $ret->result->statusInfoList->statusInfo->parcelNo;
      }
      if (!empty($ret->result->statusInfoList->statusInfo->scans)) {
        $data["scans"] = $ret->result->statusInfoList->statusInfo->scans;
      }
    }
    return $data;
  }

  public function updateBranches() {
    $dpds = $this->model->getDpdpointsModel();
    $start = dibi::fetchSingle("SELECT now()");

    //načtu země
    $url = "https://pickup.dpd.cz/api/get-countries";
    $json = file_get_contents($url);
    $jsonDecoded = json_decode($json);
    $data = $jsonDecoded->data->items;

	$countries[203] = [];
	$countries[703] = [];

	//zjistím info o cílové zemi
    foreach ($data as $row) {
      if (isset($countries[$row->id])) {
        $countries[$row->id] = [
          "id" => $row->id,
          "name" => $row->name,
          "code" => $row->iso,
          "cod_allowed" => $row->cod_allowed,
        ];
      }
    }

    foreach ($countries as $country) {
      //$url = 'https://pickup.dpd.cz/api/get-all?country=' . $country["id"];
	  $url = 'https://pickup.dpd.cz/Export/xml?country=' . $country["id"];
	  $xmlFile = WWW_DIR . '/../data/dpd_branches_' . $country["code"] . '.xml';
      copy($url, $xmlFile);
	  $xml = new \XMLReader();
	  if (!$xml->open($xmlFile)) {
		  throw new \Exception("Zdrojový soubor se nepodařilo načíst");
	  }

      while ($xml->read()) {
	      if ($xml->nodeType == \XMLReader::ELEMENT && $xml->name == 'parcelshop') {
	        $xmlElement = $xml->readOuterXml();
	        $row = simplexml_load_string($xmlElement, 'SimpleXMLElement', LIBXML_NOBLANKS && LIBXML_NOWARNING);

	        $vals = array(
	          'dpdshopid' => substr($row->id, 2),
	          'dpdshopid2' => (string)$row->id,
	          'dpdname' => (string)$row->company,
	          'dpdstreet' => (string)$row->street,
	          'dpdstreetno' => (string)$row->house_number,
	          'dpdcity' => (string)$row->city,
	          'dpdpostcode' => (string)$row->postcode,
	          'dpdcountrycode' => (string)$country["code"],
	          'dpdemail' => (string)$row->email,
	          'dpdphone' => (string)$row->phone,
	          'dpdurl' => (string)$row->homepage,
	          'dpdurlphoto' => (string)$row->photo,
	          'dpdgpsn' => (string)$row->latitude,
	          'dpdgpse' => (string)$row->longitude,
	        );

	        $id = (int)dibi::fetchSingle("SELECT dpdid FROM dpdpoints WHERE dpdshopid2=%s", (string)$row->id);

	        if ($id > 0) {
	          $dpds->update($id, $vals);
	        } else {
	          $dpds->insert($vals);
	        }
	      }
	  }
    }
    dibi::query("DELETE FROM dpdpoints WHERE coalesce(dpddateu, dpddatec)<%dt", $start);
  }
}