<?php
/**
* We|Do API
* https://bridge.intime.cz/doc/restapi.html
* <AUTHOR>
*/

namespace App\Shippings;

use ModelFactory;
use Dibi;
use Dibi\Exception;
use PhpOffice\PhpSpreadsheet\Calculation\MathTrig\Base;

class WedoApi extends BaseApi {

  /**
   * Vytvoření nové zásilky
   *
   * POST /package
   *
   * @param $order
   * @return bool|int parcelId
   * @throws Exception
   */
  public function postParcel($order): bool|int {
    if (!empty($order->ordparcode)) {
      $this->errMsg = "Už byla odeslaná/má přiřazené číslo balíku.";
      return FALSE;
    }

    $phone = $this->formatPhoneNumber($order->ordtel);
    //nactu zpusob platby
    $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
    $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);

    if (empty($order->orddelspec) && (string)$delMode->delcode === self::TYPE_WEDO) {
      $this->errMsg = "Nemá vyplněno odběrné místo.";
      return FALSE;
    }

    //pokud je jina dodaci adresa
    if (!empty($order->ordstname)) {
      $order->ordiname = $order->ordstname;
      $order->ordilname = $order->ordstlname;
      $order->ordifirname = $order->ordstfirname;
      $order->ordistreet = $order->ordststreet;
      $order->ordistreetno = $order->ordststreetno;
      $order->ordicity = $order->ordstcity;
      $order->ordipostcode = $order->ordstpostcode;
    }

    $ReferenceNumber = (!empty($order->ordmalid) ? substr(trim($order->ordmalid), 0, 9) : $order->ordcode);

    //určím typ zásilky
    if ((string)$delMode->delcode === self::TYPE_WEDO) {
      $type = "home";
    } else {
      //branch - načtu info pokud je vyplněno
      $branch = dibi::fetch("SELECT wedid, wedid2, wedcode, wedtype FROM wedopoints WHERE wedstatus=0 AND wedcode=%s", $order->orddelspec);
      if ($branch === FALSE) {
        $this->errMsg = "Odběrné místo $order->orddelspec nenalezeno v naší databázi.";
        return FALSE;
      }
      $type = $branch->wedtype;
    }

    $receiver = [
      "name"=> empty($order->ordifirname) ? $order->ordiname . " " . $order->ordilname : $order->ordifirname,
      "firstname"=> (string)$order->ordiname,
      "surname"=> (string)$order->ordilname,
      "email" => (string)$order->ordmail,
      "mobile" => $phone,
      "city"=> (string)$order->ordicity,
      "street"=> $order->ordistreet.' '.$order->ordistreetno,
      "state"=> $order->ordcurid===1 ? "CZ" : "SK",
      "postal_code"=> (string)$order->ordipostcode
    ];

    //hodnota zásilky
    $orderValue = (double)$order->ordpricevat;
    if ((int)$order->ordcurid === 2) {
      $orderValue = (double)$order->ordpricevat * (double)$order->ordcurrate
      ;
    }

    $data = [
      "reference_number" => $ReferenceNumber,
      "package_count" => max((int)$order->ordparcelscount, 1),
      "weight" => 3,
      "value" => $orderValue,
      "cash_on_delivery" => ($payMode->delcode=='cash' || $payMode->delcode=='dobirka') ? (double)$order->ordpricevat : "",
      "comment" => "křehké".(!empty($order->ordnotedel) ? ", ".$order->ordnotedel : ""),
    ];

    //doplním dobírku
    if ($payMode->delcode=='cash' || $payMode->delcode=='dobirka') {
      $price = (double)$order->ordpricevat;
      if (!empty($order->ordpricecod)) {
        $price = (double)$order->ordpricecod;
      }

      if ((int)$order->ordcurid === 2) {
        $price = $price * (double)$order->ordcurrate;
      }

      $data["additional_service"]["cash_on_delivery"] = $price;
    }

    $data["receiver"] = $receiver;

    //podle typu zásilky doplním
    if ($type === "home") {
      //dodání na adresui
      $data["receiver"] = $receiver;
    } else if ($type === "branch") {
      //dodání na odběrné místo
      $data["pup_branch"] = $order->orddelspec;
      $data["pup_contact"] = $phone;
    } else if ($type === "box") {
      //dodání doboxu
      $data["box"] = $order->orddelspec;
      $data["box_contact"] = $phone;
    }

    $json = json_encode($data);
    $response = $this->curlQuery("package", $json);
    if ($response === FALSE) {
      $this->errMsg = "cUrl error";
      return FALSE;
    } else {
      $res = json_decode($response);
      if (isset($res->message) && $res->message === 'Doslo k vice chybam') {
        $errMsg = "Objednávka: " . $order->ordcode . "|";
        if (!empty($res->detail)) {
          $errMsg .= implode(", ", $res->detail);
        } else {
          $errMsg .= "Nastala chyba";
        }
        $this->errMsg = $errMsg;
        return FALSE;
      } else if (is_countable($res) && count($res) > 0 && !empty($res[0]->order_number)) {
        return $res[0]->order_number;
      }
      $this->errMsg = "Objednávka: " . $order->ordcode . "|Neznámá chyba";
      return FALSE;
    }
  }

  public function postBatch(array $orderNumbers) {
    $fields = [];

    $fields["articles"] = array_values($orderNumbers);
    $jsonFields = json_encode($fields);
    $path = "batch";
    $response = $this->curlQuery($path, $jsonFields);
    if ($response === FALSE) {
      $this->errMsg = "cUrl error:";
      return FALSE;
    } else {
      $res = json_decode($response);
      if (isset($res->message) && $res->message === 'Doslo k vice chybam') {
        $errMsg = "";
        if (!empty($res->detail)) {
          $errMsg .= implode(", ", $res->detail);
        } else {
          $errMsg .= "Nastala chyba";
        }
        $this->errMsg = $errMsg;
        return FALSE;
      } else if (!empty($res->batch->number)) {
        return $res->batch->number;
      }
      $this->errMsg = "Neznámá chyba";
      return FALSE;
    }
  }

  /**
   *
   * vrati sadu stitku
   *
   * @param int $orderNumber cislo baliku
   * @param string $format
   * @param int $firstPosition
   * @return false|void
   */
  public function getLabels(int $orderNumber, string $format="pdf", int $firstPosition=0) {

    $urlAdd = "?skip_first=$firstPosition&format=a4";
    $path = "package/$orderNumber/labels." . $format. $urlAdd;
    $response = $this->curlQuery($path, '', 'GET');
    if ($response === FALSE) {
      $this->errMsg = "cUrl error:";
      return FALSE;
    } else {
      $this->downloadPdf($response, 'labels_' . $orderNumber);
      return true;
    }
  }

  /**
   *
   * vrati sadu stitku k dané dávce
   *
   * @param string $batchNumber cislo svozu
   * @return false|void
   */
  public function getBatchManifest(string $batchNumber) {

    $path = "batch/$batchNumber/manifest.pdf";
    $response = $this->curlQuery($path, '', "GET");
    if ($response === FALSE) {
      $this->errMsg = "cUrl error:";
      return FALSE;
    } else {
      $this->downloadPdf($response, 'manifest_' . $batchNumber);
      return true;
    }
  }

  /**
   *
   * vrati sadu stitku k dané dávce
   *
   * @param string $batchNumber cislo svozu
   * @param string $format
   * @param int $firstPosition
   * @return false|void
   */
  public function getBatchLabels(string $batchNumber, string $format="pdf", int $firstPosition=0) {

    $urlAdd = "?skip_first=$firstPosition&format=a4";
    $path = "batch/$batchNumber/labels.pdf" . $urlAdd;
    $response = $this->curlQuery($path, '', "GET");
    if ($response === FALSE) {
      $this->errMsg = "cUrl error:";
      return FALSE;
    } else {
      $this->downloadPdf($response, 'labels_' . $batchNumber);
    }
  }


  /**
  * GET statuses
  * /v3/statuses/{id}
  *
  * Zjistí stav zásilky
  *
  * @param int $id ID balíku
  * @return int parcelId
  */
  public function getConsignments($id) {
    if (empty($id)) return FALSE;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $this->getApiUrl()."/consignments/".$id);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      "X-Shop: ".$this->shopId,
      "X-Key: ".$this->apiId,
    ));
    $response = curl_exec($ch);
    $err = curl_error($ch);
    curl_close($ch);
    if ($response === FALSE) {
      $this->errMsg = "cUrl error: $err";
      return FALSE;
    } else {
      $res = json_decode($response);
      if (isset($res->code)) {
        $code = $res->code;
        if (substr($code, 0, 1) == '2') {
          $id = (int)$res->data[0]->status->id;
          $name = $res->data[0]->status->name;
          $timeUpdated = $res->data[0]->time_updated->date;
          return array('id'=>$id, 'text'=>$name, 'datetime'=>$timeUpdated);
        } else {
          $str = $res->errors[0]->description;
          $this->errMsg = 'getParcelStatus error. '.$str.', ParcelId='.$id;
          return FALSE;
        }
      }
    }
  }

  public function updateBranches() {
    //dibi::query("DELETE FROM ulozenkapoints");
    //dibi::query("ALTER TABLE ulozenkapoints AUTO_INCREMENT=1");
    $weds = $this->model->getWedopointsModel();
    $start = dibi::fetchSingle("SELECT now()");

    $url = "https://bridge.intime.cz/public/branches/branches.json";
    $json = file_get_contents($url);
    $jsonDecoded = json_decode($json);


    foreach ($jsonDecoded->intime as $key => $types) {

      $type = $key === "boxes" ? "box" : "branch";

      foreach ($types as $row) {

        $openingHours = "";
        if (isset($row->opening_hours->mon->from)) {
          $openingHours .= "Pondělí: " . $row->opening_hours->mon->from . " - " . $row->opening_hours->mon->to . "<br>\n";
        }
        if (isset($row->opening_hours->tue->from)) {
          $openingHours .= "Úterý: " . $row->opening_hours->tue->from . " - " . $row->opening_hours->tue->to . "<br>\n";
        }
        if (isset($row->opening_hours->wed->from)) {
          $openingHours .= "Středa: " . $row->opening_hours->wed->from . " - " . $row->opening_hours->wed->to . "<br>\n";
        }
        if (isset($row->opening_hours->thu->from)) {
          $openingHours .= "Čtvrtek: " . $row->opening_hours->thu->from . " - " . $row->opening_hours->thu->to . "<br>\n";
        }
        if (isset($row->opening_hours->fri->from)) {
          $openingHours .= "Pátek: " . $row->opening_hours->fri->from . " - " . $row->opening_hours->fri->to . "<br>\n";
        }
        if (isset($row->opening_hours->sat->from)) {
          $openingHours .= "Sobota: " . $row->opening_hours->sat->from . " - " . $row->opening_hours->sat->to . "<br>\n";
        }
        if (isset($row->opening_hours->sun->from)) {
          $openingHours .= "Neděle: " . $row->opening_hours->sun->from . " - " . $row->opening_hours->sun->to . "\n";
        }


        $navigation = (string)$row->location_description;

        $status = 0;

        $data = array(
          'wedid2' => (int)$row->id,
          'wedcode' => (string)$row->code,
          'wedtype' => $type,
          'wedname' => (string)$row->name,
          'wedstreet' => $row->address->street . ' ' . $row->address->number,
          'wedcity' => (string)$row->address->town,
          'wedpostcode' => (string)$row->address->postal_code,
          'wedcountry' => (string)$row->address->country,
          'wedemail' => !empty($row->contacts->email) ? (string)$row->contacts->email : '',
          'wedphone' => !empty($row->contacts->phone) ? (string)$row->contacts->phone : '',
          'wedopeninghours' => $openingHours,
          'wednavigation' => $navigation,
          'wedstatus' => $status,
          'wedurlphoto' => (string)$row->photo,
          'wedgpsn' => !empty($row->contacts->lat) ? (string)$row->position->lat : '',
          'wedgpse' => !empty($row->contacts->lng) ? (string)$row->position->lng : '',
        );

        $id = (int)dibi::fetchSingle("SELECT wedid FROM wedopoints WHERE wedid2=%i", $row->id);

        if ($id > 0) {
          $weds->update($id, $data);
        } else {
          $weds->insert($data);
        }
      }
    }
    dibi::query("DELETE FROM wedopoints WHERE (weddatec < '$start' AND weddateu IS NULL) OR (weddateu IS NOT NULL && weddateu < '$start')");
  }

  private function curlQuery($path, $json, $type="POST") {
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, $this->getApiUrl(). "/" . $path);
    curl_setopt($ch, CURLOPT_USERPWD, $this->config["login"] . ":" . $this->config["passw"]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);
    if ($type === "POST") {
      curl_setopt($ch, CURLOPT_POST, TRUE);
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    } else {
      curl_setopt($ch, CURLOPT_POST, false);
			curl_setopt($ch, CURLOPT_POSTFIELDS, '');
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
    }
    if (!empty($json)) {
      curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
    }
    curl_setopt($ch, CURLOPT_HTTPHEADER, ["Content-Type: application/json"]);

    $response = curl_exec($ch);

    $errors = curl_error($ch);

    curl_close($ch);

    return $response;
  }

  public function setErrorMsg($msg) {
    Tracy\Debugger::log($msg);
    header("HTTP/1.0 500 Internal Server Error");
    $data = array(
      "id" => 500,
      "msg" => $msg,
    );
    echo json_encode($data);
    die();
  }


  private function downloadPdf($file, $fileName='stitky') {
    header('Content-Disposition: attachment;filename="wedo_' . $fileName . '.pdf"');
    header('Content-Type: application/force-download');
    header('Content-Length: ' . (strlen($file)));
    flush();
    echo $file;
  }
}
