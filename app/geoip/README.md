# GeoIP Service

Service for working with GeoLite2 geolocation database that provides country information based on IP address.

## Installation

### Manual Installation
1. Download the GeoLite2 Country database from [MaxMind](https://dev.maxmind.com/geoip/geolite2-free-geolocation-data)
2. Place the `GeoLite2-Country.mmdb` file in the `/var/www/html/app/geoip/database/1/` directory
3. Create a file `/var/www/html/app/geoip/database/active_dir` with content `1`
4. Install the required library via Composer:

```bash
composer require geoip2/geoip2
```

### Automatic Installation
The service includes a method to automatically download and update the database:

```php
// Download and update the GeoLite2 database
\geoip\GeoIpService::updateDatabase();

// Force update even if the database was recently updated
\geoip\GeoIpService::updateDatabase(true);
```

## Configuration

The service uses the following constants defined directly in the class:

```php
/**
 * Base directory for database storage
 */
const DB_BASE_DIR = '/var/www/html/app/geoip/database';

/**
 * MaxMind Account ID
 */
const ACCOUNT_ID = '1177812';

/**
 * MaxMind License Key
 */
const LICENSE_KEY = '****************************************';
```

To change these values, modify the constants directly in the `GeoIpService.php` file.

## Database Rotation System

The service uses a rotation system with two directories (`1` and `2`) to ensure that the database remains available during updates:

1. The service keeps track of which directory is currently active in the `active_dir` file
2. When updating the database, it downloads to the inactive directory
3. After a successful download, it switches to the new directory
4. This ensures that there's no downtime during database updates

## Usage

The service contains static methods that can be easily used in any project:

```php
// Get country code
$countryCode = \geoip\GeoIpService::getCountryCode('*******');
// Returns e.g. 'US'

// Get country name
$countryName = \geoip\GeoIpService::getCountryName('*******');
// Returns e.g. 'United States'

// Get country name in a different language
$countryName = \geoip\GeoIpService::getCountryName('*******', 'cs');
// Returns e.g. 'Spojené státy'

// Check if IP address is from a specific country
$isFromCzech = \geoip\GeoIpService::isFromCountry('*******', 'CZ');
// Returns false because ******* is from the USA

// Get continent code
$continentCode = \geoip\GeoIpService::getContinentCode('*******');
// Returns e.g. 'NA' (North America)

// Get complete country record
$record = \geoip\GeoIpService::getCountryRecord('*******');
// Returns an object with complete information

// Update the database (downloads from MaxMind if needed)
$success = \geoip\GeoIpService::updateDatabase();
```

## Using in Other Projects

To use in other projects:

1. Copy the `GeoIpService.php` file to your project
2. Modify the namespace as needed
3. Update the constants in the class if necessary (DB_BASE_DIR, ACCOUNT_ID, LICENSE_KEY)
4. Install the `geoip2/geoip2` dependency via Composer
5. Create the directory structure with subdirectories `1` and `2` and an `active_dir` file

## License

The GeoLite2 database is provided under the [Creative Commons Attribution-ShareAlike 4.0 International License](https://creativecommons.org/licenses/by-sa/4.0/).
