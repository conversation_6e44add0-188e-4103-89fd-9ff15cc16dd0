<?php

/**
 * GeoIP Service
 * 
 * Service for working with GeoLite database to get geolocation data from IP addresses
 * 
 * <AUTHOR>
 */

namespace geoip;

use AdminModule\PagePresenter;
use GeoIp2\Database\Reader;
use GeoIp2\Exception\AddressNotFoundException;
use Max<PERSON><PERSON>\Db\Reader\InvalidDatabaseException;
use <PERSON>\Debugger;

class GeoIpService
{
    /**
     * Base directory for database storage
     */
    const DB_BASE_DIR = APP_DIR . '/geoip/database';
    
    /**
     * MaxMind Account ID
     */
    const ACCOUNT_ID = '1177812';
    
    /**
     * MaxMind License Key
     */
    const LICENSE_KEY = '****************************************';
    
    /**
     * Path to GeoLite2 Country database file
     * 
     * @var string|null
     */
    private static $dbPath = null;
    
    /**
     * Current active database directory (1 or 2)
     * 
     * @var int|null
     */
    private static $activeDbDir = null;
    
    /**
     * Path to the active directory marker file
     * 
     * @var string|null
     */
    private static $activeMarkerPath = null;
    
    /**
     * Reader instance for working with the database
     * 
     * @var Reader|null
     */
    private static $reader = null;

    /**
     * Initialize the service
     * 
     * @return bool True if initialization was successful, false otherwise
     */
    private static function initialize()
    {
        // Check if the GeoIP2 library is available
        if (!class_exists('GeoIp2\Database\Reader')) {
            trigger_error('GeoIP2 library is not installed. Please install the geoip2/geoip2 package using Composer.', E_USER_WARNING);
            return false;
        }

        // If we already have initialized the database, do nothing
        if (self::$reader !== null) {
            return true;
        }

        // Determine which directory is active
        self::$activeDbDir = self::getActiveDirectory();
        $dbPath = self::DB_BASE_DIR . '/' . self::$activeDbDir . '/GeoLite2-Country.mmdb';
        self::$dbPath = $dbPath;

        // Check if the database file exists
        if (!file_exists($dbPath)) {
            trigger_error('GeoIP database not found at: ' . $dbPath, E_USER_WARNING);
            return false;
        }

        try {
            // Create reader for working with the database
            self::$reader = new Reader($dbPath);
            return true;
        } catch (\Exception $e) {
            trigger_error('Error loading GeoIP database: ' . $e->getMessage(), E_USER_WARNING);
            return false;
        }
    }
    
    /**
     * Get active directory number (1 or 2)
     * 
     * @return int Active directory number
     */
    private static function getActiveDirectory()
    {
        self::$activeMarkerPath = self::DB_BASE_DIR . '/active_dir';
        
        if (file_exists(self::$activeMarkerPath)) {
            $activeDir = (int)trim(file_get_contents(self::$activeMarkerPath));
            if ($activeDir === 1 || $activeDir === 2) {
                return $activeDir;
            }
        }
        
        // Default to directory 1 if no marker file exists or it has invalid content
        return 1;
    }
    
    /**
     * Set the active database directory
     * 
     * @param int $dirNumber Directory number (1 or 2)
     * @return bool Success
     */
    private static function setActiveDirectory($dirNumber)
    {
        if ($dirNumber !== 1 && $dirNumber !== 2) {
            return false;
        }
        
        // Update the marker file
        if (file_put_contents(self::$activeMarkerPath, $dirNumber) !== false) {
            self::$activeDbDir = $dirNumber;
            self::$dbPath = self::DB_BASE_DIR . '/' . self::$activeDbDir . '/GeoLite2-Country.mmdb';
            return true;
        }
        
        return false;
    }
    
    /**
     * Switch to the other database directory
     * 
     * @return int New active directory number
     */
    private static function switchActiveDirectory()
    {
        $newActiveDir = (self::$activeDbDir === 1) ? 2 : 1;
        self::setActiveDirectory($newActiveDir);
        return $newActiveDir;
    }

    /**
     * Get country ISO code from IP address
     * 
     * @param string $ipAddress IP address to lookup
     * @return string|null Country ISO code (e.g. 'CZ', 'SK') or null if not found
     */
    public static function getCountryCode($ipAddress)
    {
        // Check if the GeoIP2 library is available
        if (!class_exists('GeoIp2\Database\Reader')) {
            return null;
        }

        try {
            if (!self::initialize()) {
                return null;
            }

            $record = self::$reader->country($ipAddress);
			//Debugger::log($record->country->isoCode . "|" . $ipAddress);
            return $record->country->isoCode;
        } catch (AddressNotFoundException $e) {
            // IP address not found in database
            return null;
        } catch (\Exception $e) {
            trigger_error('Error getting country code: ' . $e->getMessage(), E_USER_WARNING);
            return null;
        }
    }

    /**
     * Get country name from IP address
     * 
     * @param string $ipAddress IP address to lookup
     * @param string $locale Locale for country name (default: 'en')
     * @return string|null Country name or null if not found
     */
    public static function getCountryName($ipAddress, $locale = 'en')
    {
        // Check if the GeoIP2 library is available
        if (!class_exists('GeoIp2\Database\Reader')) {
            return null;
        }

        try {
            if (!self::initialize()) {
                return null;
            }
            
            $record = self::$reader->country($ipAddress);
            
            return $record->country->names[$locale] ?? null;
        } catch (AddressNotFoundException $e) {
            // IP address not found in database
            return null;
        } catch (\Exception $e) {
            trigger_error('Error getting country name: ' . $e->getMessage(), E_USER_WARNING);
            return null;
        }
    }

    /**
     * Check if IP address is from specified country
     * 
     * @param string $ipAddress IP address to check
     * @param string $countryCode Country ISO code to check against (e.g. 'CZ')
     * @return bool True if IP is from specified country, false otherwise
     */
    public static function isFromCountry($ipAddress, $countryCode)
    {
        $country = self::getCountryCode($ipAddress);
        return $country !== null && strtoupper($country) === strtoupper($countryCode);
    }

    /**
     * Get continent code from IP address
     * 
     * @param string $ipAddress IP address to lookup
     * @return string|null Continent code or null if not found
     */
    public static function getContinentCode($ipAddress)
    {
        // Check if the GeoIP2 library is available
        if (!class_exists('GeoIp2\Database\Reader')) {
            return null;
        }

        try {
            if (!self::initialize()) {
                return null;
            }
            
            $record = self::$reader->country($ipAddress);
            
            return $record->continent->code;
        } catch (AddressNotFoundException $e) {
            // IP address not found in database
            return null;
        } catch (\Exception $e) {
            trigger_error('Error getting continent code: ' . $e->getMessage(), E_USER_WARNING);
            return null;
        }
    }

    /**
     * Get full country record from IP address
     * 
     * @param string $ipAddress IP address to lookup
     * @return object|null Full country record or null if not found
     */
    public static function getCountryRecord($ipAddress)
    {
        // Check if the GeoIP2 library is available
        if (!class_exists('GeoIp2\Database\Reader')) {
            return null;
        }

        try {
            if (!self::initialize()) {
                return null;
            }
            
            return self::$reader->country($ipAddress);
        } catch (AddressNotFoundException $e) {
            // IP address not found in database
            return null;
        } catch (\Exception $e) {
            trigger_error('Error getting country record: ' . $e->getMessage(), E_USER_WARNING);
            return null;
        }
    }
    
    /**
     * Download and update GeoLite2 Country database
     * 
     * This method uses a rotating directory system to ensure that the current
     * database remains available during the update process.
     * 
     * @param bool $forceUpdate Force update even if the database file already exists
     * @return bool True if database was successfully downloaded and updated, false otherwise
     */
    public static function updateDatabase($forceUpdate = false)
    {
        try {
            self::initialize();
            
            // Get the inactive directory to use for the update
            $inactiveDir = (self::$activeDbDir === 1) ? 2 : 1;
            $targetDir = self::DB_BASE_DIR . '/' . $inactiveDir;
            $targetDbPath = $targetDir . '/GeoLite2-Country.mmdb';
            
            // Check if database file already exists and we're not forcing an update
            if (!$forceUpdate && file_exists(self::$dbPath)) {
                // Check if the database was updated in the last week
                if (filemtime(self::$dbPath) > strtotime('-7 days')) {
                    return true;
                }
            }
            
            // Create database directory if it doesn't exist
            if (!is_dir($targetDir)) {
                if (!mkdir($targetDir, 0755, true)) {
                    throw new \Exception('Failed to create database directory: ' . $targetDir);
                }
            }
            
            // Temporary files
            $tempFile = tempnam(sys_get_temp_dir(), 'geoip_') . '.tar.gz';
            $tempDir = tempnam(sys_get_temp_dir(), 'geoip_dir_');
            
            // Remove the temporary file created by tempnam and create a directory with the same name
            if (file_exists($tempDir)) {
                unlink($tempDir);
            }
            if (!mkdir($tempDir, 0755, true)) {
                throw new \Exception('Failed to create temporary directory for extraction');
            }
            
            // Download the database
            $downloadUrl = 'https://download.maxmind.com/geoip/databases/GeoLite2-Country/download?suffix=tar.gz';
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $downloadUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_USERPWD, self::ACCOUNT_ID . ':' . self::LICENSE_KEY);
            $data = curl_exec($ch);
            
            if (curl_errno($ch)) {
                throw new \Exception('Failed to download database: ' . curl_error($ch));
            }
            
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            if ($httpCode !== 200) {
                throw new \Exception('Failed to download database, HTTP code: ' . $httpCode);
            }
            
            curl_close($ch);
            
            // Save the downloaded file
            file_put_contents($tempFile, $data);
            
            // Extract the tar.gz file using PHP native functions
            try {
                // Nejdříve zkusíme použít PharData způsobem, který by měl být spolehlivější
                $phar = new \PharData($tempFile);
                $phar->extractTo($tempDir, null, true); // true means overwrite
            } catch (\Exception $pharException) {
                // Pokud selže PharData, zkusíme alternativní metodu
                try {
                    // Rozbalíme pomocí GZ streams
                    $gzResource = gzopen($tempFile, 'rb');
                    if ($gzResource) {
                        $tarFile = tempnam(sys_get_temp_dir(), 'geoip_') . '.tar';
                        $tarResource = fopen($tarFile, 'wb');
                        
                        if ($tarResource) {
                            while (!gzeof($gzResource)) {
                                fwrite($tarResource, gzread($gzResource, 4096));
                            }
                            fclose($tarResource);
                            gzclose($gzResource);
                            
                            // Nyní rozbalíme tar soubor (phar umí pracovat i s tar soubory)
                            try {
                                $tarPhar = new \PharData($tarFile);
                                $tarPhar->extractTo($tempDir, null, true);
                                unlink($tarFile);
                            } catch (\Exception $ex) {
                                throw new \Exception('Nelze rozbalit TAR soubor: ' . $ex->getMessage());
                            }
                        } else {
                            gzclose($gzResource);
                            throw new \Exception('Nelze vytvořit dočasný TAR soubor');
                        }
                    } else {
                        throw new \Exception('Nelze otevřít GZ soubor');
                    }
                } catch (\Exception $ex) {
                    throw new \Exception('Alternativní metoda rozbalení selhala: ' . $ex->getMessage());
                }
            }
            
            // Find the .mmdb file in the extracted directory
            $mmdbFile = null;
            $directories = new \RecursiveDirectoryIterator($tempDir);
            $iterator = new \RecursiveIteratorIterator($directories);
            foreach ($iterator as $file) {
                if ($file->isFile() && $file->getExtension() === 'mmdb') {
                    $mmdbFile = $file->getPathname();
                    break;
                }
            }
            
            if ($mmdbFile === null) {
                throw new \Exception('Could not find .mmdb file in the downloaded archive');
            }
            
            // Copy the database file to the target directory
            if (!copy($mmdbFile, $targetDbPath)) {
                throw new \Exception('Failed to copy database file to destination');
            }
            
            // Clean up temporary files
            @unlink($tempFile);
            self::recursiveRemoveDirectory($tempDir);
            
            // Switch to the new database directory
            self::switchActiveDirectory();
            
            return true;
        } catch (\Exception $e) {
            trigger_error('GeoIP database update error: ' . $e->getMessage(), E_USER_WARNING);
            return false;
        }
    }
    
    /**
     * Recursively remove a directory and its contents
     * 
     * @param string $dir Directory to remove
     * @return bool True if successful
     */
    private static function recursiveRemoveDirectory($dir)
    {
        if (!is_dir($dir)) {
            return false;
        }
        
        $objects = scandir($dir);
        foreach ($objects as $object) {
            if ($object === '.' || $object === '..') {
                continue;
            }
            
            $path = $dir . '/' . $object;
            if (is_dir($path)) {
                self::recursiveRemoveDirectory($path);
            } else {
                @unlink($path);
            }
        }
        
        return @rmdir($dir);
    }
}
