<?php
namespace AdminModule;

use dibi;
use Nette;

final class CouponPresenter extends BasePresenter {

  /** @persistent */
  public $sCode = '';

  /** @persistent */
  public $sName = '';


  /** @persistent */
  public $sManId = '';

  /** @persistent */
  public $sStatus = '0';


  /********************* view default *********************/

  public function renderDefault() {
    $cous = $this->model->getCouponsModel();
    $mans = $this->model->getManufacturersModel();

    $where = "";
    if (!empty($this->sCode)) $where .= " coucode = '$this->sCode' AND ";
    if (!empty($this->sName)) $where .= " couname LIKE '%$this->sName%' AND ";
    if (!empty($this->sManId)) $where .= " coumanid=$this->sManId AND ";
    if ((string)$this->sStatus==='1') {
      $where .= " (coustatus=1 OR couvalidto<CURDATE()) AND ";
    } else if ((string)$this->sStatus==='0') {
      $where .= " (coustatus=0 AND couvalidto>=CURDATE()) AND ";
    }

    if (!empty($where)) {
      $where = " WHERE ".substr($where, 0, -5);
    }

    if (!empty($this->sOrderBy)) {
      $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;
    } else {
      $orderBy = " ORDER BY IF(coalesce(couname,'')!='',0,1),couname";
    }

    $dataSource = $cous->getDataSource("SELECT *, IF(couvalidto>=CURDATE(), 1, 0) AS date_valid FROM coupons $where $orderBy");

    $paginator = $this['paginator']->getPaginator();
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page;
    $this->template->dataRows = $dataRows;
    //ciselnik statusu
    $this->template->enum_coustatus = $cous->getEnumCouStatus();
    $this->template->enum_coumanid = $mans->getEnumManId();
  }

  public function renderEdit($id) {
    $form = $this['editForm'];

    if (!$form->isSubmitted()) {
      $cous = $this->model->getCouponsModel();
      $pros = $this->model->getProductsModel();
      $pros->setPrcCat("a");
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $cous->load($id);
        //doplnil info o produktech
        foreach ($dataRow->products as $product) {
          $pro = $pros->load($product["proid"]);
          $dataRow->products[$product["proid"]]["proname"] = $pro->proname;
          $dataRow->products[$product["proid"]]["proprice"] = $pro->proprice;
        }

        if (!$dataRow) {
          throw new NBadRequestException('Záznam nenalezen');
        }
        $dataRow->couvalidto = $this->formatDate($dataRow->couvalidto);
        $form->setDefaults($dataRow);
      }
      $this->template->dataRow = $dataRow;
      $this->template->id = $id;
    }
  }

  public function renderStats($id) {
    $cous = $this->model->getCouponsModel();
    $pros = $this->model->getProductsModel();
    $cou = $cous->load($id);
    $proids = "";
    foreach ($cou->products as $proid => $item) {
      //dotahnu info o produktu
      $proids .= $proid . ",";
      $pro = $pros->load($proid);
      $cou->products[$pro->proid]["proname"] = $pro->proname;
      $cou->products[$pro->proid]["proprice"] = $pro->proprice;
    }
    $proids = trim($proids, ",");
    $this->template->dataRow = $cou;
    //celková suma objednávek obsahující tento kupón
    $this->template->orders = dibi::fetch("SELECT SUM(ordpricevat) AS ordprice, COUNT(ordid) AS cnt FROM orders WHERE ordcoucode=%s", $cou->coucode);

    //produkty na které se vzdahovala sleva
    $this->template->orditems = dibi::fetchAll("
      SELECT ordcode, SUM(oriprice*oriqty) AS oriprice_sum,
      SUM(oripriceoriginal*oriqty) AS oripriceoriginal_sum, oripriceoriginal, oriprice,
      proid, procode, proname FROM orders
      INNER JOIN orditems ON (ordid=oriordid)
      INNER JOIN products ON (proid=oriproid)
      WHERE oritypid=0 AND ordcoucode IS NOT NULL ".(!empty($proids) ? "AND oriproid IN ($proids)" : "")."
      GROUP BY oriproid
    ");
  }

  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParameter('id');
    $cous = $this->model->getCouponsModel();
    $mans = $this->model->getManufacturersModel();

    $form = new Nette\Application\UI\Form();

    $form->addText("coucode", "Kód")
      ->setOption("description", "Zadejte unikátní kód");

    $form->addText("couvalidto", "Platí do")
      ->setOption("description", "Zadejte datum ve tvaru dd.mm.rrrr")
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno");

    $form->addText("couqty", "Počet použití")
      ->setOption("description", "0 = neomezeně")
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno")
      ->addRule(Nette\Forms\Form::INTEGER, "%label musí být celé číslo")
      ->setDefaultValue(1);

    $form->addText("couname", "Jméno");

    $form->addSelect("coumanid", "Značka", $mans->getEnumManId())
      ->setPrompt("Vše");

    $form->addText("couvalue", "Hodnota kupónu")
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno")
      ->addRule(Nette\Forms\Form::INTEGER, "%label musí být celé číslo");

    $form->addSelect("couvalueunit", "Jednotka kupónu", array(
        "Kč" => "Kč",
        "%" => "%"
      ))
      ->addConditionOn($form["couvalue"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněna");

    $form->addText("couvaluelimit", "Minimální hodnota obj.")
      ->setOption("description", "0 = bez omezení")
      ->setDefaultValue(0)
      ->addConditionOn($form["couvalue"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněna pokud je vyplněna Hodnota kupónu");

    /*
    $form->addCheckbox("coudelfree", "Doprava zdarma");
    $form->addCheckbox("coupayfree", "Platba zdarma");
    */

    $form->addSelect("coustatus", "Status", $cous->getEnumCouStatus());
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    return $form;
  }

  public function editFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $coupons = $this->model->getCouponsModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();
      $vals->couvalidto = $this->formatDateMySQL($vals->couvalidto);
      try {
        if ($id > 0) {
          $coupons->update($id, $vals);
        } else {
          $id = $coupons->insert($vals);
        }
      } catch (\Exception $e) {
        $form->addError($e->getMessage());
      }

      $this->flashMessage('Uloženo v pořádku');
      $this->redirect('edit',$id);
    }
  }

  protected function createComponentSearchForm() {
    $cous = $this->model->getCouponsModel();
    $mans = $this->model->getManufacturersModel();

    $form = new Nette\Application\UI\Form();
    $form->addGroup("Vyhledávání");
    $form->addText("code", "Kód ", 10)
      ->setDefaultValue($this->sCode);

    $form->addText("name", "Jméno ", 10)
      ->setDefaultValue($this->sName);

    $form->addSelect("manid", "Stav", $mans->getEnumManId())
      ->setPrompt("Vše");

    $arr["all"] = 'Vše';
    $arr = $arr + $cous->getEnumCouStatus();
    $form->addSelect("status", "Stav", $arr);

    if ($this->sStatus != '') $form["status"]->setDefaultValue($this->sStatus);
    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sCode = Null;
        $this->sName = Null;
        $this->sStatus = 0;
        $this->sManId = NULL;
      } else {
        $vals = $form->getValues();
        $this->sCode = $vals["code"];
        $this->sName = $vals["name"];
        $this->sStatus = $vals["status"];
        $this->sManId = $vals["manid"];
      }
    }
    $this->redirect("default");
  }
}
?>
