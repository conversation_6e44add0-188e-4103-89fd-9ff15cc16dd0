<?php
namespace AdminModule;

use dibi;
use Nette;

final class ArticlePresenter extends BasePresenter {

  public function renderDefault() {
    $articles = $this->model->getArticlesModel();
    $dataRows = dibi::query("SELECT * FROM articles ORDER BY artdate DESC")
      ->fetchAssoc('artid');

    $this->template->dataRows = $dataRows;
    $this->template->enum_arttypid = $articles->getEnumArtTypId();
    $this->template->enum_arttop = $articles->getEnumArtTop();
    $this->template->enum_artstatus = $articles->getEnumArtStatus();
  }

  public function renderEdit($id) {
    $articles = $this->model->getArticlesModel();
    $dataRow = array();
    if ($id > 0) {
      $dataRow = $articles->load($id);
      if (!$dataRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }
    }
    $form = $this['editForm'];
    if (!empty($dataRow->artdate)) $dataRow->artdate = $this->formatDate($dataRow->artdate);
    $form->setDefaults($dataRow);
    $this->template->dataRow = $dataRow;
    $this->template->id = $id;
    $this->template->enum_arttypid = $articles->getEnumArtTypId();
    $this->template->enum_arttop = $articles->getEnumArtTop();

    //nactu prilohy
    $this->template->attachments = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype NOT IN ('jpg', 'png', 'gif', 'webp')", $id);
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype IN ('jpg', 'png', 'gif', 'webp')", $id);
    if (count($this->template->images) > 0) $this->template->imagesListId = $id;
    
    //vlozim hlavni obrazek
    $fileName = 'art_'.$id.'.jpg';
    $this->template->mainImageName = "";
    if (file_exists(WWW_DIR."/pic/art/list/$fileName")) $this->template->mainImageName = $fileName;
  }

  public function renderJsImagesList($id) {
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype IN ('jpg', 'png', 'gif')", $id);
  }

  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParam('id');

    $articles = $this->model->getArticlesModel();
    $adms = $this->model->getAdminsModel();

    $form = new Nette\Application\UI\Form();
    $form->addSelect('arttypid', 'Typ článku:', $articles->getEnumArtTypId());
    $form->addSelect('arttop', 'Umístění:', $articles->getEnumArtTop());

    $form->addText('artname', 'Název:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Název: je nutné vyplnit');

    $form->addText('artdate', 'Datum článku:', 30)
      ->addRule(Nette\Forms\Form::FILLED, '%label: je nutné vyplnit');  

    $form->addSelect('artrobots', 'Nastavení indexace:', $articles->getEnumRobotsIndex());

    $form->addText('arturlkey', 'URL:', 30);

    $form->addText('arttitle', 'Title:', 100);
    $form->addTextArea('artdescription', 'Anotace:', 75, 3)
      ->setAttribute('maxlength', '255')
      ->setOption('description', Nette\Utils\Html::el('p')->class('charsRemaining'));
    $form->addText('artkeywords', 'Keywords:', 100);

    $form->addText('artprocode', 'Kód produktu:', 30);

    $form->addTextArea('artbody', 'Popis:', 60, 20);
    $form['artbody']->getControlPrototype()->class('mceEditor');

    $form->addUpload("imageMain", "Hlavní obrázek:")
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete');

    $form->addSelect("artadmid", "Autor", $adms->getEnumAuthors())
      ->setPrompt("");

    $form->addSelect('artstatus', 'Status:', $articles->getEnumArtStatus());
    
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    return $form;
  }

  public function editFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {

      $article = $this->model->getArticlesModel();
      $id = (int)$this->getParameter('id');
      $formVals = $form->getValues();

      $image = null;
      if ($formVals["imageMain"]->isOk()) $image = $formVals["imageMain"];
      unset($formVals["imageMain"]);
      
      if (empty($formVals["arturlkey"])) $formVals["arturlkey"] = Nette\Utils\Strings::webalize($formVals["artname"]);
      if (empty($formVals["artdate"])) {
        $formVals["artdate"] = new \DateTime();
      } else {
        $formVals["artdate"] = $this->formatDateMySQL($formVals["artdate"]);
      }  

      try {
        if ($article->save($id, $formVals)) {
          $this->flashMessage('Uloženo v pořádku');
          
          if (isset($image)) {
            //$sizes = (array($this->config["PROPICSIZE_BIG"].'xbig', $this->config["PROPICSIZE_DETAIL"].'xdetail', $this->config["PROPICSIZE_LIST"].'xlist'));
            //ulozim obrazek
            $image->move(WWW_DIR . "/pic/art/src/art_" . $id . ".jpg");
            $this->deletePic(WWW_DIR . "/pic/art/", "art_" . $id . ".jpg");
            //$this->saveImage($image->toImage(), WWW_DIR."/pic/art", 'art_'.$id.".jpg", $sizes);
          }
          
          $this->redirect('default');
        }
      } catch (\Exception $e) {
        $form->addError($e->getMessage());
      }
    }
  }

  
  protected function createComponentUploadForm() {
    $form = new Nette\Application\UI\Form();
    $form->addText("ataname", 'Název přílohy')
      ->addRule(Nette\Forms\Form::FILLED);
    $form->addUpload('file', 'Příloha:')
      ->addRule(Nette\Forms\Form::FILLED);
    $form->addSubmit('save', 'Připojit')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'UploadFormSubmitted');
    return $form;
  }

  public function UploadFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $artid = $this->getParameter('id');
      if ($vals["file"]->isOk()) {
        //ulozim do db
        $atts = $this->model->getAttachmentsModel();

        $pathParts = pathinfo($vals["file"]->getName());
        $fExtension = $pathParts["extension"];

        $ataVals = array(
          'ataartid'=>$artid,
          'ataname'=>$vals["ataname"],
          'atafilename'=>Nette\Utils\Strings::webalize($vals["ataname"]).'_'.$artid.'.'.$fExtension,
          'atasize'=>(string)$vals["file"]->getSize(),
          'atatype'=> $fExtension,
        );
        $ataid = $atts->insert($ataVals);
        $ataVals["atafilename"] = Nette\Utils\Strings::webalize($ataVals["ataname"]).'_'.$ataid.'.'.$ataVals["atatype"];
        $atts->update($ataid, $ataVals);
        $vals["file"]->move(WWW_DIR.'/files/'.$ataVals["atafilename"]);

        $arts = $this->model->getArticlesModel();
        $arts->cacheClean();

      }
      $this->redirect('edit', $artid);
    }
  }

  public function actionDelete($artid) {
    $this->model->getArticlesModel()->delete($artid);
    $files = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i", $artid);
    foreach ($files as $file) {
      @unlink(WWW_DIR.'/files/'.$file->atafilename);
      dibi::query("DELETE FROM attachments WHERE ataid=%i", $file->ataid);
    }

    $this->redirect("default");
  }

  public function actionDeleteAttachment($ataid, $artid) {
    $file = dibi::fetch("SELECT * FROM attachments WHERE ataid=%i", $ataid);
    if ($file) {
      @unlink(WWW_DIR.'/files/'.$file->atafilename);
      dibi::query("DELETE FROM attachments WHERE ataid=%i", $ataid);
    }

    $arts = $this->model->getArticlesModel();
    $arts->cacheClean();

    $this->redirect('edit', $artid);
  }

}
