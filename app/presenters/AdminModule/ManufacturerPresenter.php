<?php
namespace AdminModule;

use dibi;
use Nette;

final class ManufacturerPresenter extends BasePresenter {

  /********************* view default *********************/

  public function renderDefault() {
    $manufacturer = $this->model->getManufacturersModel();
    $dataRows = dibi::query("SELECT * FROM manufacturers ORDER BY manname")
      ->fetchAssoc('manid');

    $this->template->dataRows = $dataRows;
    //ciselnik statusu
    $this->template->enum_manstatus = $manufacturer->getEnumManStatus();
  }

  public function renderEdit($id) {
    $form = $this['editForm'];

    if (!$form->isSubmitted()) {
      $manufacturer = $this->model->getManufacturersModel();
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $manufacturer->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
      }
      $this->template->dataRow = $dataRow;
      $this->template->id = $id;
    }
  }

  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParameter('id');

    $manufacturer = $this->model->getManufacturersModel();

    $form = new Nette\Application\UI\Form();

    $form->addText('manname', 'Název:', 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Název: je nutné vyplnit');

    $form->addText('manurl', 'URL:', 60);

    $form->addText('mantitle', 'Title:', 60);

    $form->addTextArea('mandescription', 'Description:', 110, 3);

    $form->addTextArea('mandesc', 'Popis:', 60, 10)
      ->getControlPrototype()->class('mceEditor');

    $form->addUpload("photo", "Logo:");

    $form->addSelect('manstatus', 'Status:', $manufacturer->getEnumManStatus());

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }

  public function editFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {

      $manufacturer = $this->model->getManufacturersModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();

      if ($vals["photo"]->isOk()) {
        $photo = $vals["photo"];
      }

      unset($vals["photo"]);

      try {
        if ($manufacturer->save($id, $vals)) {

          if (isset($photo)) {
            $this->deletePic(WWW_DIR . "/pic/manufacturer/", $id . ".jpg");
            $photo->move(WWW_DIR . "/pic/manufacturer/src/" . $id . ".jpg");
          }

          $this->flashMessage('Uloženo v pořádku');
          $this->redirect('Manufacturer:default');
        }

      } catch (\Exception $e) {
        $form->addError($e->getMessage());
      }
    }
  }
}
