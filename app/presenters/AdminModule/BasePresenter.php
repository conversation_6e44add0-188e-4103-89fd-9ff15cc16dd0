<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  IPub\VisualPaginator\Components as VisualPaginator;
  
abstract class BasePresenter extends \BasePresenter {
  /** @persistent */
  public $backlink = '';
  
  const LOGIN_NAMESPACE = 'admin';
  /** identita */
  protected $user;
  protected $adminData = Null;

  protected function startup() {

    parent::startup();

    // autorizace administratora
    $this->user = $this->getUser();
    $this->user->getStorage()->setNamespace(self::LOGIN_NAMESPACE);

    if ($this->user->isLoggedIn() && $this->action == "login") {
      $this->redirect('Admin:default');
    } else {
      if ($this->action != "login") {
        if (!$this->user->isLoggedIn()) {
          if ($this->user->getLogoutReason() === Nette\Security\IUserStorage::INACTIVITY) {
            $this->flashMessage('Byl/a jste odhl<PERSON>en/a z důvodu delší neaktivity.');
          }
          $backlink = $this->storeRequest();
          $this->redirect('Admin:login', $backlink);
        }
      }
    }
    $this->adminData = false;
    if ($this->user->isLoggedIn()) {
      $admins = $this->model->getAdminsModel();
      $this->adminData = $admins->load($this->user->id);
    }

    if (!$this->adminData) {
      $this->user->logout();
      $this->adminData = new \Dibi\Row(array('admid'=>0));
    }

    //kontrola prav
    if (!$this->user->isAllowed($this->name, $this->action)) {
      $this->flashMessage('Nemáte povolen přístup. ('.$this->name.':'.$this->action.')');
      $this->redirect('Admin:default');
    }



  }

  protected function beforeRender() {
    //nactu administratora
    $this->template->identity = $this->user;
    $this->template->admin = $this->adminData;
    $this->template->enum_prccat = $this->getEnumPrcCat();

    //nactu nazev DB ripojeny
    $config = dibi::getConnection()->getConfig();
    $this->template->dbName  = $config["database"];

    parent::beforeRender();
  }

  /*
  function createComponentPaginator($name){
    $vp = new \VisualPaginator($this, $name);
  }
  */

  /**
	 * Create items paginator
	 *
	 * @return VisualPaginator\Control
	 */
	protected function createComponentPaginator() {
		// Init visual paginator
		$control = new VisualPaginator\Control;
		$control->setTemplateFile(APP_DIR . '/../templates/@paginator.latte');
		$control->disableAjax();
		return $control;
	}

}
