<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;

final class CatalogPresenter extends BasePresenter {

  public function catalogEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $catalogs = $this->model->getCatalogsModel();
      $id = (int)$this->getParam('id');
      $formVals = $form->getValues();

      //kontrola zda nedal stejnou nadrizenou uroven jako je id
      if ($id > 0 && $id == $formVals["catmasid"]) {
        $form->addError("Nelze zadat do nadřízené kategorie editovanou kategorii.");
      } else {
        //vezmu si obrazek pokud byl odeslan
        $image = null;
        if ($formVals["picture"]->isOk()) {
          $image = $formVals["picture"];
        }
        unset($formVals["picture"]);
        if ($id > 0) {
          $catalogs->update($id, $formVals);
          $this->flashMessage('Aktualizováno v pořádku');
        } else {
          $id = $catalogs->insert($formVals);
          $this->flashMessage('Nový záznam uložen v pořádku');
        }
        //upravim a ulozim obrazek
        if ($image) {
          $image->move(WWW_DIR . '/pic/catalog/src/'. "$id.jpg");
          $this->deletePic(WWW_DIR . "/pic/catalog/", $id . ".jpg");
          $this->saveImage($image->toImage(), WWW_DIR . "/pic/catalog", "$id.jpg", array($this->config["CATPICSIZE"]));
        }
      }
      if (!$form->hasErrors()) $this->redirect('default');
    }
  }

  /********************* view default *********************/

  public function renderDefault() {
    $catalogs = $this->model->getCatalogsModel();
    //$dataRows = $catalogs->fetchAll("SELECT * FROM catalogs WHERE ORDER BY catorder");
    $this->template->items = $catalogs->getEnumCatalogTree();

    //ciselnik statusu
    $this->template->enum_catstatus = $catalogs->getEnumCatStatus();
  }

  public function renderEdit($id) {
    $form = new Nette\Application\UI\Form();
    $form = $this['catalogEditForm'];

    if (!$form->isSubmitted()) {
      $catalogs = $this->model->getCatalogsModel();
      if ($id > 0) {
        $dataRow = $catalogs->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
        $this->template->dataRow = $dataRow;

        //zjistim jestli existuje obrazek
        $fileName = WWW_DIR."/pic/catalog/$id.jpg";
        if(file_exists($fileName)) $this->template->imagePath = "pic/catalog/$id.jpg";

      } else {
        $defVals = array();
        $catmasid = $this->getParam("catmasid");
        if ($catmasid > 0) $defVals["catmasid"] = $catmasid;

        $form->setDefaults($defVals);
      }
    }
  }

  public function renderDelete($id) {
    if ($id > 0) {
      //zjistim jestli nema podrizene vetve
      $cnt = dibi::fetchSingle("SELECT count(*) FROM catalogs WHERE catmasid=%i", $id);
      if ($cnt == 0) {
        $catalogs = $this->model->getCatalogsModel();
        $catalogs->delete($id);
        $this->flashMessage('Záznam byl vymazán.');
        $catalogs->cacheClean();
      } else {
        $this->flashMessage('Záznam nebyl vymazán. Existují jemu podřízené větve katalogu.', 'err');
      }
    }
    $this->redirect('default');
  }


  /********************* facilities *********************/

  protected function createComponentCatalogEditForm() {
    $catalog = $this->model->getCatalogsModel();
    $macs = $this->model->getMallcatalogsModel();
    $form = new Nette\Application\UI\Form();

    $form->addSelect('catmasid', 'Nadřízená úroveň:', $catalog->getEnumCatalogCombo())
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte název.');

    $form->addText('catname', 'Název:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte název.');

    $form->addText('catkey', 'URL klíč:', 30)
      ->setOption('description', 'Pokud ponecháte prázdné, generuje se z názvu kategorie');

    $form->addText('catkeywords', 'Klíčové slova:', 30)
      ->setOption('description', 'slova oddělené čárkou');

    $form->addSelect('catmacid', 'Mall kategorie:', $macs->getEnumMacId())
      ->setPrompt("");

    $form->addTextArea('catdescription', 'Description:', 75, 3)
      ->setAttribute('maxlength', '255')
      ->setOption('description', Nette\Utils\Html::el('p')->class('charsRemaining'));

    $form->addText('catsalestat', 'Nejprodávanější zboží:', 75)
      ->setOption('description', 'kódy zboží oddělené čárkou');

    //$form->addText('catparams', 'Heuréka katalogová cesta:', 75);
    
    $form->addText('catpathheureka', 'Katalogová cesta dle taxonomie heureka.cz:', 100)
      ->setOption('description', Nette\Utils\Html::el('')->setHtml('<br>[ <a href="http://www.heureka.cz/direct/xml-export/shops/heureka-sekce.xml" target="_blank">zde kompletní taxonomie<a/> ] Vyplňte buď CATEGORY_ID nebo CATEGORY_FULLNAME'));

    $form->addTextArea('catparamheureka', 'Parametry vyhledávání pro heureka.cz:', 100, 3)
      ->setOption('description', Nette\Utils\Html::el('')->setHtml('<br>[ <a href="https://docs.google.com/spreadsheets/d/1bOroHe1jlLabfyLA2WN7ka8Wa940DsXrpE20JBm5zuY/edit#gid=0" target="_blank">zde kompletní seznam kategorií a povinných parametrů<a/> ] Vyplňte Název parametru v eshopu;Název parametru heuréka, co řádek to jeden parametr'));

    $form->addText('catpathgoogle', 'Katalogová cesta dle taxonomie Google nákupy:', 100)
      ->setOption('description', Nette\Utils\Html::el('')->setHtml('<br>[ <a href="https://support.google.com/merchants/answer/1705911" target="_blank">zde kompletní taxonomie<a/> ] Vyplňte buď ID nebo kompletní cestu katalogovou cestu. Např. pro MP3 přehrávače vyplňte Elektronika > Audio > Audiopřehrávače a rekordéry > Přehrávače MP3'));
    $form->addText('catpathzbozi', 'Katalogová cesta dle taxonomie zbozi.cz:', 100)
      ->setOption('description', Nette\Utils\Html::el('')->setHtml('<br>[ <a href="http://www.zbozi.cz/static/categories.csv" target="_blank">zde kompletní taxonomie<a/> ] Vyplňte hodnotu ze sloupce: Celá cesta'));

    $form->addText('catclass', 'Název stylu:', 75);

    $form->addTextArea('catdesc', 'Popis:', 75, 5)
      /*->setAttribute('maxlength', '255')*/
      ->setOption('description', Nette\Utils\Html::el('p')->class('charsRemaining'));

    //obrazek
    $form->addUpload('picture', 'Obrázek:')
      ->setOption('description', 'Obrázek bude zmenšen na rozměr '.$this->config["CATPICSIZE"].'. Zdrojový obrázek by měl mít stejný poměr stran. Nahráním nového obrázku ten původní přepíšete.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');

    $form->addText('catorder', 'Pořadí:', 15)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte pořadí.');

    $form->addSelect('catstatus', 'Status:', $catalog->getEnumCatStatus())
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte status.');

    $form->addCheckbox('catnoindex', 'Neindexovat');

    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('default');

    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'catalogEditFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }
}
