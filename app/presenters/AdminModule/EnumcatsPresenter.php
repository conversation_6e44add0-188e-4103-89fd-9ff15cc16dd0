<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;

final class EnumcatPresenter extends BasePresenter {

  public function renderDefault($typid) {
    $enumcats = $this->model->getEnumcatsModel();
    $this->template->enumcats = dibi::fetchAll("SELECT * FROM enumcats WHERE enutypid=%i ORDER BY enuname DESC", $typid);
    $this->template->enum_enutypid = $enumcats->getEnumEnuTypId();
    $this->template->enum_enustatus = $enumcats->getEnumEnuStatus();
  }

  public function renderEdit($id, $typid) {
    $form = $this['editForm'];
    $row = array();
    if (!$form->isSubmitted()) {
      $id = $this->getParam('id');
      $enumcats = $this->model->getEnumcatsModel();
      if ($id > 0) {
        $row = $enumcats->load($id);
        if (!$row) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
      } else {
        $row["enutypid"] = $typid;
      }
      $row->enutagnum = (int)$row->enutagnum;
      $form->setDefaults($row);
    }
  }

  public function editFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $id = $this->getParam('id');
      $typid = $this->getParam('typid');
      $formVals = $form->getValues();
      $enumcats = $this->model->getEnumcatsModel();
      if ($id > 0) {
        $enumcats->update($id, $formVals);
        $this->flashMessage('Změny uloženy v pořádku');
      } else {
        $formVals["enutypid"]  =  $typid;
        $id = $enumcats->insert($formVals);
        $this->flashMessage('Nová položka uložena v pořádku');
      }
      $this->redirect('default', $typid);
    }
  }

  protected function createComponentEditForm() {
    $enums = $this->model->getEnumcatsModel();
    $typid = $this->getParam('typid');
    $form = new Nette\Application\UI\Form;
    $id = $this->getParam('id');
    $form->addtext('enuname', 'Název:', 50)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Název.');

    if ($typid == 1) {
      //zeme
      $form->addSelect('enutagnum', 'Region:', $enums->getEnumDelRegions())
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte region');
    }
    /*
    $form->addtext('enuname2', 'Url klíč:', 50)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Url klíč.');
    */

    $form->addSelect("enustatus", "Status:", $enums->getEnumEnuStatus());
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = [$this, 'editFormSubmitted'];

    return $form;
  }
}
