<?php
namespace AdminModule;

use App\Shippings\DpdShippingApi;
use App\Shippings\UlozenkaApi;
use App\Shippings\WedoApi;
use dibi;
use MpApiClient\Order\Entity\StatusEnum;
use Nette;
use <PERSON>\Debugger;

final class OrderPresenter extends BasePresenter {

  /** @persistent */
  public $sCode = '';

  /** @persistent */
  public $sName = '';

  /** @persistent */
  public $sAdmin = Null;

  /** @persistent */
  public $sStatus = '';

  /** @persistent */
  public $sNotClosed =  true;

  /** @persistent */
  public $sOrdType;

  /** @persistent */
  public $sOrdDateFrom;

  /** @persistent */
  public $sOrdDateTo;

  /** @persistent */
  public $sRows;

  /** @persistent */
  public $sDelId;

  /** @persistent */
  public $sPayId;

    /** @persistent */
  public $sUsrId;

  /** @persistent */
  public $sOrdDateType;

  /** @persistent */
  public $sOrderBy = 'ordid';

  /** @persistent */
  public $sOrderByType = 'DESC';

  /** @persistent */
  public $sExport = 0;

  public function orderEditFormSubmitted (Nette\Application\UI\Form $form) {
    $id = $this->getParameter('id');
    if ($form->isSubmitted()) {
      $orders = $this->model->getOrdersModel();
      $values = $form->getValues();
      //nactu si objednavku pokud existuje
      $order = false;
      if ($id > 0) {
        $order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
        $orders->setCurrency($this->currencies, $order->ordcurid);
      }

      $values["ordpricecod"] = (double)$values["ordpricecod"];
      if ($values["ordpricecod"] === 0.0) {
        $values["ordpricecod"] = NULL;
      }
      $values["ordparcelscount"] = (int)$values["ordparcelscount"];
      If ($values["ordparcelscount"] === 0) {
        $values["ordparcelscount"] = 1;
      }

      try {
        $orders->save($id, $values);
        $order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
        $orders->recalcOrder($id);
        $this->flashMessage('Uloženo v pořádku');
      } catch (\Exception $e) {
        $this->flashMessage('Chyba: ' . $e->getMessage(), "err");
      }

    }
    $this->redirect('edit', $id);
  }

  private function  changeOrderStatus($formVals, $mailSend=TRUE, $skipMall=FALSE) {
    $id = $formVals["ordid"];
    $stateDate = NULL;
    if (!empty($formVals["statedate"])) {
      $stateDate = $formVals["statedate"];
    }

    unset($formVals["ordid"]);
    unset($formVals["statedate"]);

    $orders = $this->model->getOrdersModel();
    $dels = $this->model->getDeliveryModesModel();
    $order = $orders->load($id);

    $orders->setCurrency($this->currencies, $order->ordcurid);
    if ($order) {
      //ujistim jestli se meni status
      if ($order->ordstatus == $formVals["ordstatus"]) return(TRUE);
    }

    //u mall obj. nejde nastavit status vyřizuje se
    if (!empty($order->ordmalid) && $formVals["ordstatus"] == 0) {
      $this->flashMessage("U MALL objednávek nelze nastavit stav Čeká na zpracování", "err");
      return FALSE;
    }

    $formVals["ordstatus"] = (int)$formVals["ordstatus"];

    if ($formVals["ordstatus"] == 6) {
      //nastavim zaplaceno
      $formVals["ordpaystatus"] = 1;
    } else if ($formVals["ordstatus"] == 3) {
      //nastaveno odeslano
      $formVals["ordadmid"] = $this->adminData->admid;
    }

    //nastavím status v MALL
    /*
      0 => 'Čeká na zpracování', //MALL: BLOCKED
      1 => 'Vyřizuje se', //MALL: OPEN
      2 => 'Čeká na platbu',
      6 => 'Zaplaceno',
      8 => 'Připraveno k odběru',
      3 => 'Odeslána', //MALL: SHIPPED
      4 => 'Dodána', //MALL: DELIVERED
      5 => 'Vrácená', //MALL: RETURNED
      9 => 'Stornovaná', //MALL: CANCELED
      7 => 'Černá listina',
   */


    if (!empty($order->ordmalid) && IS_PRODUCTION && !$skipMall) {
      if ($formVals["ordstatus"] > 1 && (int)$order->ordstatus === 0) {
        $this->flashMessage("U MALL objednávek nelze měnit status pokud je ve stavu Čeká na zpracování", "err");
        return FALSE;
      }

      $config = $this->config;
      $config["neonParameters"] = $this->neonParameters;
      $mallApi = new \MallApi($config, $this->model);

      try {
        if ($formVals["ordstatus"] === 3) {
          $arr = $dels->getDeliveryPaymentFromOrder($order);
          $delMode = $arr["delivery"];
          $params = array();
          if (!empty($order->ordparcode)) {
             $params["ordparcode"] = (string)$order->ordparcode;
             if (!empty($delMode->delurlparcel)) {
              $url = str_replace('#CODE#', $order->ordparcode, $delMode->delurlparcel);
              $params["trackingurl"] = $url;
            }
          }
          $mallApi->setStatus($order->ordmalid, StatusEnum::SHIPPED()->getValue(), TRUE, $params);
        } else if ($formVals["ordstatus"] === 4) {
          $date = new \DateTime;
          if (!empty($stateDate)) {
            $date = $stateDate;
          }
          $params["delivered_at"] = $date;
          $mallApi->setStatus($order->ordmalid, StatusEnum::DELIVERED()->getValue(), false, $params);
        } else if ($formVals["ordstatus"] === 5) {
          $mallApi->setStatus($order->ordmalid, StatusEnum::RETURNED()->getValue());
        } else if ($formVals["ordstatus"] === 9) {
          $mallApi->setStatus($order->ordmalid, StatusEnum::CANCELLED()->getValue());
        }
      } catch (\Exception $e) {
        $this->flashMessage("MALL objednávka: " . $e->getMessage(), "err");
        return FALSE;
      }
    }

    $orders->update($id, $formVals);
    $date = NULL;
    if (!empty($stateDate)) {
      $date = $stateDate;
    }
    $orders->logStatus($id, $formVals["ordstatus"], $date);

    //mailuji změnu stavu obj
    if (empty($order->ordmalid) && $mailSend) {
      $order = $orders->load($id);
      $this->mailOrderChanged($order);
    }

    $this->flashMessage('Změna stavu provedena v pořádku [ID:'.$id.']');
    return TRUE;
  }

  public function orderChangeStateFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $formVals = $form->getValues();
      $formVals["ordid"] = $this->getParameter('id');
      $mailSend = !(bool)$formVals["mail_block"];
      $skipMall = (bool)$formVals["skip_mall"];
      unset($formVals["mail_block"]);
      unset($formVals["skip_mall"]);
      $this->changeOrderStatus($formVals, $mailSend, $skipMall);
    }
    $this->redirect('default');
  }

  /********************* view default *********************/

  public function renderDefault() {
    if (empty($this->sRows)) {
      $this->sRows = $this->config['ADMIN_ROWSCNT'];
    }

    $orders = $this->model->getOrdersModel();
    //$where = "orddatec + INTERVAL 3 DAY >= Now() AND ";
    $where = "";
    if (!empty($this->sCode)) {
      $where .= " ordcode LIKE '%$this->sCode%' AND ";
    }
    if (!empty($this->sAdmin)) {
      $where .= " ordadmid = " . $this->sAdmin . " AND ";
    }
    if (!empty($this->sName)) {
      $where .= " (ordilname LIKE '%$this->sName%' OR ordstlname LIKE '%$this->sName%' OR ordstfirname LIKE '%$this->sName%' OR ordifirname LIKE '%$this->sName%') AND ";
    }
    if ($this->sNotClosed) {
      $where .= " ordstatus NOT IN (4,5,9) AND orddatec + INTERVAL 90 DAY>Now() AND ";
    }
    if ($this->sOrdType) {
      $where .= " ordmalid IS NOT NULL AND ";
    }

    if ($this->sPayId) {
      $where .= " orddelid=" . $this->sPayId . " AND ";
    } else if ($this->sDelId) {
      if (is_numeric($this->sDelId)) {
        $where .= " dm.delid=" . $this->sDelId . " AND ";
      } else if ($this->sDelId == 'ULOZENKAWEDO') {
        $where .= " (dm.delcode LIKE 'ULOZENKA%' OR dm.delcode LIKE 'WEDO%') AND ";
      } else {
        $where .= " dm.delcode LIKE '" . $this->sDelId . "%' AND ";
      }
    }

    if ((string)$this->sStatus!='' && !empty($this->sOrdDateFrom) && !empty($this->sOrdDateTo)) {
      $where .= " EXISTS (
        SELECT 1 FROM orders_log WHERE 
          orlordid=ordid AND orlstatus=" . (int)$this->sStatus . " AND 
          DATE(orldatec) BETWEEN '" . $this->formatDateMySQL($this->sOrdDateFrom) . "' AND '" . $this->formatDateMySQL($this->sOrdDateTo) . "') AND ";
    } else if ((string)$this->sStatus!='') {
      $where .= " ordstatus=$this->sStatus AND ";
    }

    if (!empty($this->sUsrId)) {
      $where .= " ordusrid=$this->sUsrId AND ";
      $usrs = $this->model->getUsersModel();
      $this->template->searchUser = $usrs->load($this->sUsrId);
    }

    if (!empty($where)) {
      $where = " WHERE ".substr($where, 0, -5);
    }

    if (!empty($this->sOrderBy)) {
      $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;
    } else {
      $orderBy = " ORDER BY orddatec DESC";
    }

    $sql = "
    SELECT orders.*, d.delname AS delname, dm.delname AS delnamemas, dm.delcode AS delcodemas, d.delcode AS paycode, admname
    FROM orders
    LEFT JOIN deliverymodes AS d ON (orddelid=d.delid)
    LEFT JOIN deliverymodes AS dm ON (d.delmasid=dm.delid)
    LEFT JOIN admins ON (ordadmid=admid)
    $where
    $orderBy
    ";

    if ($this->sExport === 1) {
      $template = $this->createTemplate();
      $rows = dibi::fetchAll($sql);

      $template->rows = $rows;
      $template->enum_ordstatus = $orders->getEnumOrdStatus();

      $template->setFile(WWW_DIR.'/../templates/AdminModule/Order.export.latte');

      header("Content-type: text/csv");
      header("Content-Disposition: attachment; filename=export_objednavek.csv");
      header("Pragma: no-cache");
      header("Expires: 0");

      echo (string)$template;
      $this->terminate();
    } else {
      //ciselnik statusu
      $this->template->enum_ordstatus = $orders->getEnumOrdStatus();

      $dataSource = dibi::dataSource($sql);
      $paginator = $this['paginator']->getPaginator();
      $paginator->itemsPerPage = $this->sRows;
      $paginator->itemCount = $dataSource->count();
      //$dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
      $dataRows = dibi::fetchAll($sql . " LIMIT $paginator->itemsPerPage OFFSET $paginator->offset");
      $this->template->page = $paginator->page;
      $this->template->dataRows = $dataRows;
    }

    $this->template->enum_ordtype = $orders->getEnumOrdType();
    $this->template->enum_zasilkovna = $this->model->getDeliveryModesModel()->getEnumZasilkovnaPlaces();
  }

  public function renderAutocompleteProducts(int $ordId) {
    //zjistím cenovou kategorii
    $prcCat = 'a';
    if ($ordId > 0) {
      $ords = $this->model->getOrdersModel();
      $ord = $ords->load($ordId);

      if (!empty($ord->ordprccat)) {
        $prcCat = $ord->ordprccat;
      }
    }

    $term = $this->getParameter('term');
    if (!empty($term)) {
      $this->template->rows = dibi::fetchAll("SELECT proid, procode, proname, proprice" . $this->curId . $prcCat . " AS proprice FROM products WHERE proismaster=0 AND proname LIKE '%$term%' OR  procode LIKE '$term%'");
    }
  }

  public function renderAutocompleteName() {

    $term = $this->getParameter('term');
    if (!empty($term)) {
      $rows = dibi::fetchAll("
        SELECT usrid, usrilname, usriname, usrifirname, usrstlname, usrstname, usrstfirname 
        FROM users 
        WHERE 
              usrilname LIKE '%$term%' OR usrifirname LIKE '%$term%' OR 
              usrstlname LIKE '%$term%' OR usrstfirname LIKE '%$term%'
      ");

      foreach ($rows as $key => $row) {
        if (!empty($row->usrstlname)) {
          $rows[$key]->usriname = $row->usrstname;
          $rows[$key]->usrilname = $row->usrstlname;
          $rows[$key]->usrifirname = $row->usrstfirname;
        }
      }
      $this->template->rows = $rows;
    }
  }
  
  public function renderQuick() {
    $this->template->formType = (int)$this->getParameter("type");
    $usrId = (int)$this->getParameter("usrid");
    if ($usrId > 0) {
      $usrs = $this->model->getUsersModel();

      $usr = $usrs->load($usrId);

      if ($usr) {

        if (!empty($usr->usrstname)) {
          $usr->usriname = $usr->usrstname;
          $usr->usrilname = $usr->usrstlname;
          $usr->usrifirname = $usr->usrstfirname;
          $usr->usristreet = $usr->usrststreet;
          $usr->usristreetno = $usr->usrststreetno;
          $usr->usricity = $usr->usrstcity;
          $usr->usripostcode = $usr->usrstpostcode;
          $usr->usricouid = $usr->usrstcouid;
        }


        $vars = [
          'ordusrid' => $usrId,
          'ordiname' => $usr->usriname,
          'ordilname' => $usr->usrilname,
          'ordifirname' => $usr->usrifirname,
          'ordistreet' => $usr->usristreet,
          'ordistreetno' => $usr->usristreetno,
          'ordicity' => $usr->usricity,
          'ordipostcode' => $usr->usripostcode,
          'ordstname' => $usr->usrstname,
          'ordstlname' => $usr->usrstlname,
          'ordstfirname' => $usr->usrstfirname,
          'ordststreet' => $usr->usristreet,
          'ordststreetno' => $usr->usristreetno,
          'ordstcity' => $usr->usricity,
          'ordstpostcode' => $usr->usripostcode,
          'ordcurid' => ($usr->usricouid === 2 ? 2 : 1),
          'ordtel' => $usr->usrtel,
          'ordmail' => $usr->usrmail,
        ];

        $form = $this['orderQuickForm'];
        $form->setDefaults($vars);
      }

    }

  }

  public function renderEdit($id) {
    $form = $this['orderEditForm'];
    $formSate = $this['orderChangeStateForm'];

    if (!$form->isSubmitted() && !$formSate->isSubmitted()) {
      $orders = $this->model->getOrdersModel();
      $dataRow = $orders->load($id);
      if (!$dataRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }

      //zkontroluji platné odběrné místa ulozenky
      $dels = $this->model->getDeliveryModesModel();
      $delPay = $dels->getDeliveryPaymentFromOrder($dataRow);
      if (!empty($dataRow->orddelspec)) {
        $delCode = $delPay["delivery"]->delcode;
        /*
        if ($delCode === "ULOZENKA") {
          $arr = $dels->getEnumUlozenkaPlaces();
          if (!array_key_exists($dataRow->orddelspec, $arr)) {
            $this->template->message = "Chybně zadáno odběrné místo!!! ($dataRow->orddelspec)";
            unset($dataRow->orddelspec);
          }
        } else if ($delCode === "CESKA_POSTA_BALIKOVNA") {
          $arr = $dels->getEnumBalikovnaPlaces();
          if (!array_key_exists($dataRow->orddelspec, $arr)) {
            $this->template->message = "Chybně zadáno odběrné místo!!! ($dataRow->orddelspec)";
            unset($dataRow->orddelspec);
          }
        }
        */
      }

      $form->setDefaults($dataRow);
      $formSate->setDefaults($dataRow);

      $this->template->dataRow = $dataRow;


      $this->template->delivery = $delPay["delivery"];
      $this->template->payment = $delPay["payment"];

      //doplnim polozky objednavky
      $this->template->ordItems = dibi::query("SELECT * FROM orditems LEFT JOIN products ON (proid=oriproid) WHERE oriordid=%i", $dataRow->ordid)->fetchAssoc('oriid');

      //doplnim polozku se slevou
      $this->template->ordItemDisc = dibi::fetchAll("SELECT * from orditems where oriordid=%i AND oritypid IN (3,5,6)", $dataRow->ordid);

       //doplnim log zmen
      $this->template->statusLog = dibi::fetchAll("SELECT * FROM orders_log WHERE orlordid=%i", $dataRow->ordid);
      $this->template->enum_ordstatus = $orders->getEnumOrdStatus();
      $this->template->enum_ordexported = $orders->getEnumOrdExported();

      //kontrola cerne listiny
      //$this->template->bl = $orders->blAnalyse($dataRow);

      //kontrola PSČ
      $postCodeNotFound = FALSE;
      if ($dataRow->ordcurid == 1) {
        $postCodeNotFound = !$this->checkPostCode($dataRow->ordipostcode);
        if ($postCodeNotFound === FALSE) {
          $postCodeNotFound = !$this->checkPostCode($dataRow->ordstpostcode);
        }
      }

      $this->template->postCodeNotFound = $postCodeNotFound;
    }
  }

  public function actionDelete($id) {
    if ($id > 0) {
      $orders = $this->model->getOrdersModel();
      $orders->delete($id);
      $this->flashMessage('Záznam byl vymazán');
    }
    $this->redirect('default');
  }

  public function actionMakeInvoice($id) {
    if ($id > 0) {
      $orders = $this->model->getOrdersModel();
      try {
        $orders->makeInvoice($id);
        $this->flashMessage('Faktura byla vystavena');
      } catch (\Exception $e) {
        $this->flashMessage($e->getMessage(), 'err');
      }
    }
    $this->redirect('edit', $id);
  }

  public function actionPrint($id) {
    if ($id > 0) {
      $this->printOrder($id, 'D');
    }
    //$this->redirect('default');
  }
  
  public function actionPrintInvoice($id, $target='I') {
    if ($id > 0) {
      $this->printOrder($id, $target, 'Invoice.latte');
    }
    //$this->redirect('default');
  }

  public function printDpdShippingList($ids, $target='D') {
    $dpdApi = new DpdShippingApi($this->neonParameters["dpdShipping"], $this->model);
    if (!$dpdApi->isApiOn()) {
      $this->flashMessage("Api DPD není aktivované", "err");
    }

    $template = $this->createTemplate();
    $orders = dibi::fetchAll("SELECT * FROM orders WHERE ordid IN(%i)", $ids);
    $template->setFile(WWW_DIR.'/../templates/pdf/dpdShippingList.latte');

    $shippings = [];
    $emptyOrders = [];
    foreach ($orders as $order) {
      if ($order->ordparid > 0) {
        $shippings[] = $order->ordparid;
      } else {
        $emptyOrders[] = $order->ordcode;
      }
    }

    if (count($emptyOrders) > 0) {
      $this->flashMessage("Objednávky " . implode(", ", $emptyOrders) . " nebyly odeslány do API DPD", "err");
      $this->redirect("default");
    }

    if (count($shippings) > 0) {
      $shippingList = $dpdApi->getShipments($shippings);

      if ($shippingList && count($shippingList) > 0) {
        $shippingListData = [];
        foreach ($shippingList as $key => $shipping) {
          foreach ($shipping->parcels as $parcel) {
            $shippingListData[$parcel->parcelNumber] = [
              "parcelNumber" => $parcel->parcelNumber,
              "reference1" => $parcel->reference1,
              "name" => $shipping->receiver->name,
              "countryCode" => $shipping->receiver->countryCode,
              "zipCode" => $shipping->receiver->zipCode,
              "street" => $shipping->receiver->street,
              "houseNo" => $shipping->receiver->houseNo,
              "city" => $shipping->receiver->city,
              "cod" => NULL,
            ];

            if (isset($shipping->service->additionalService->cod->amount) && !empty($shipping->service->additionalService->cod->amount)) {
              $shippingListData[$parcel->parcelNumber]["cod"] = $shipping->service->additionalService->cod->amount;
            };
          }
        }
        $template->shippingListData = $shippingListData;
        $template->date = new \DateTime();

        // mPDF
        $mpdf = new \Mpdf\Mpdf();
        $mpdf->SetDisplayMode('real');

        $mpdf->AddPage('L');
        $mpdf->WriteHTML((string)$template, 2);

        if ($target=="I" || $target=="F") {
          $name = TEMP_DIR."/dpd-soupiska-baliku.pdf";
        } else {
          $name = "dpd-soupiska-baliku.pdf";
        }
        $mpdf->Output($name, $target);
      }
    }
  }

  public function actionPrintOrder($id, $target='I') {
    if ($id > 0) {
      $this->printOrder($id, $target, 'Order.latte');
      if ($target = 'd') {
        $this->terminate();
      }
    }
    //$this->redirect('default');
  }

  /*
   public function dpdBatchActionBackup($action, $ids, $priceCods=[], $parcelsCounts=[]) {
    $dpdApi = new DpdApi($this->neonParameters["dpd"], $this->model);
    if (!$dpdApi->isApiOn()) {
      $this->flashMessage("Api DPD není aktivované", "err");
    }

    if (count($ids) == 0) {
      $this->flashMessage("Nevybrali jste žádný záznam", "err");
      $this->redirect('Order:default');
    }

    $orders = $this->model->getOrdersModel();
    $data = array();

    $dpmId = 0;

    foreach ($ids as $id) {
      $order = $orders->load($id);
      if ($order) {
        if ($action === 'export') {
          //updatnu objednávku ohledně nastavení počtu balíků a dobírkové ceny
          $vals = [];
          if (!empty($priceCods[$id]) && (double)$priceCods[$id] !== (double)$order->ordpricecod) {
            $vals["ordpricecod"] = (double)$priceCods[$id];
          }
          if (!empty($parcelsCounts[$id]) && (double)$parcelsCounts[$id] !== (double)$order->ordparcelscount) {
            $vals["ordparcelscount"] = (double)$parcelsCounts[$id];
          }
          if (count($vals) > 0) {
            $orders->update($id, $vals);
            $order = $orders->load($id);
          }

          //nastaveno odeslano pokud se jedna o ulozenku
          $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
          $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);

          if ($delMode->delcode == 'DPD' || $delMode->delcode == 'DPD_PICKUP') {
            $ret = $dpdApi->prepareShipmentData($order);

            if ($ret !== FALSE) {
              $data[] = $ret;
            }
          } else {
            $this->flashMessage("Balík pro objednávku " . $order->ordcode . " není DPD.", "err");
          }
        } else if ($action === 'delete' || $action === 'print' || $action === 'reprintlabel' || $action === 'pickup') {
          if (empty($order->ordparid)) {
            $this->flashMessage("Objednávka " . $order->ordcode . " nemá přiřazené id balíku.", "err");
            continue;
          }

          if (!empty($order->ordparcode2) && $action === 'print') {
            $this->flashMessage("Objednávka " . $order->ordcode . " již má přiřazenou soupisku. Zvolte prosím opakovaný tisk.", "err");
            continue;
          }

          //poskládám seznam zásilek

          $ReferenceNumber = (!empty($order->ordmalid) ? substr(trim($order->ordmalid), 0, 9) : $order->ordcode);

          $data[] = array(
            'referenceNumber' => $ReferenceNumber,
            'id' => (int)$order->ordparid,
          );

          $couCode = $order->ordicouid === 1 ? 'CZ' : 'SK';
          if (!isset($pickupPieces[$couCode])) {
            $pickupPieces[$couCode] = array(
              "serviceCode" => 1,
              "quantity" => 0,
              "weight" => 0,
              "destinationCountryCode" => $couCode
            );
          }
          $pickupPieces[$couCode]["quantity"] += 1;
          $pickupPieces[$couCode]["weight"] += $order->ordweight;

        } else if (($action === 'reprint') && empty($dpmId)) {
          $dpmId = $order->ordparcode2;
        }
      }
    }

    if (count($data) > 0) {
      if ($action === 'export') {
        $ret = $dpdApi->createShipment($data);

        //vypišu chyby pokud nějaké nastaly
        if (count($dpdApi->errMsg) > 0) {
          foreach ($dpdApi->errMsg as $msg) {
            $this->flashMessage($msg, "err");
          }
        }

        foreach ($ids as $id) {
          $ord = $orders->load($id);
          //nastavím status odesláno pokud ještě není a má vhodný stav
          if (($ord->ordstatus == 0 || $ord->ordstatus == 1 || $ord->ordstatus == 6) && !empty($ord->ordparcode)) {
            $orders->update($ord->ordid, array('ordstatus'=>3));
            $orders->logStatus($ord->ordid, 3, NULL, "API dopravce");

            //nastavím MALL status odesláno
            if (!empty($ord->ordmalid) && IS_PRODUCTION) {
              $config = $this->config;
              $config["neonParameters"] = $this->neonParameters;
              $mallApi = new \MallApi($config, $this->model);

              $dels = $this->model->getDeliveryModesModel();
              $arr = $dels->getDeliveryPaymentFromOrder($ord);
              $delMode = $arr["delivery"];
              $params = array();
              if (!empty($ord->ordparcode)) {
                 $params["ordparcode"] = (string)$ord->ordparcode;
                 if (!empty($delMode->delurlparcel)) {
                  $url = str_replace('#CODE#', $ord->ordparcode, $delMode->delurlparcel);
                  $params["trackingurl"] = $url;
                }
              }

              try {
                $mallApi->setStatus($ord->ordmalid, StatusEnum::SHIPPED()->getValue(), TRUE, $params);
              } catch (\Exception $e) {
                Debugger::log($e->getMessage());
              }

            }

            //mailuji zmenu stavu
            $this->mailOrderChanged($ord);
          }
        }

      } else if ($action === 'reprintlabel') {
        $ret = $dpdApi->reprintShipmentLabel($data);
      } else if ($action === 'delete') {
        $ret = $dpdApi->deleteShipment($data);
      } else if ($action === 'print') {
        $dpmId = $dpdApi->closeManifest($data);
        if ($dpmId !== FALSE) {
          //uložím si ID manifestu
          foreach ($data as $item) {
            $ordId = (int)dibi::fetchSingle("SELECT ordid FROM orders WHERE ordcode=%s", $item["referenceNumber"]);
            if ($ordId > 0) {
              $orders->update($ordId, array("ordparcode2" => $dpmId));
            }
          }
          $this->terminate();
        }
      } else {
        $ret = FALSE;
        $this->flashMessage("Neplatná akce.", "err");
      }
    } else if ($action === 'reprint' && $dpmId > 0) {
      $ret = $dpdApi->reprintManifest($dpmId);
    }

    if (count($dpdApi->errMsg) > 0) {
      $this->flashMessage("Nastala chyba. " . implode("|", $dpdApi->errMsg), "err");
    } else {
      $this->flashMessage("Hotovo");
    }

    $this->redirect('Order:default');
  }
  */

  public function dpdBatchAction($action, $ids, $priceCods=[], $parcelsCounts=[]) {
    $dpdApi = new DpdShippingApi($this->neonParameters["dpdShipping"], $this->model);
    if (!$dpdApi->isApiOn()) {
      $this->flashMessage("Api DPD není aktivované", "err");
    }

    if (count($ids) == 0) {
      $this->flashMessage("Nevybrali jste žádný záznam", "err");
      $this->redirect('Order:default');
    }

    $orders = $this->model->getOrdersModel();
    $data = array();

    $dpmId = 0;

    foreach ($ids as $id) {
      $order = $orders->load($id);
      if ($order) {
        if ($action === 'export') {
          //updatnu objednávku ohledně nastavení počtu balíků a dobírkové ceny
          $vals = [];
          $data = [];
          if (!empty($priceCods[$id]) && (double)$priceCods[$id] !== (double)$order->ordpricecod) {
            $vals["ordpricecod"] = (double)$priceCods[$id];
          }
          if (!empty($parcelsCounts[$id]) && (double)$parcelsCounts[$id] !== (double)$order->ordparcelscount) {
            $vals["ordparcelscount"] = (double)$parcelsCounts[$id];
          }
          if (count($vals) > 0) {
            $orders->update($id, $vals);
            $order = $orders->load($id);
          }

          $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
          $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);

          if (str_starts_with($delMode->delcode, 'DPD')) {
            $data = $dpdApi->prepareShipmentData($order);
            $ret = $dpdApi->createShipment($data);
            //vypišu chyby pokud nějaké nastaly
            if (count($dpdApi->errMsg) > 0) {
              foreach ($dpdApi->errMsg as $msg) {
                $this->flashMessage($msg, "err");
              }
              $this->redirect('Order:default');
            }

            if ($ret !== FALSE) {
              $data[$id] = $ret;
            }
          } else {
            $this->flashMessage("Balík pro objednávku " . $order->ordcode . " není DPD.", "err");
          }
        } else if ($action === 'delete' || $action === 'print' || $action === 'pickup') {
          if (empty($order->ordparid)) {
            $this->flashMessage("Objednávka " . $order->ordcode . " nemá přiřazené id balíku.", "err");
            continue;
          }

          if (!empty($order->ordparcode2) && $action === 'print') {
            $this->flashMessage("Objednávka " . $order->ordcode . " již má přiřazenou soupisku. Zvolte prosím opakovaný tisk.", "err");
            continue;
          }

          //poskládám seznam zásilek

          $ReferenceNumber = (!empty($order->ordmalid) ? substr(trim($order->ordmalid), 0, 9) : $order->ordcode);

          $data[] = (int)$order->ordparid;

          $couCode = $order->ordicouid === 1 ? 'CZ' : 'SK';
          if (!isset($pickupPieces[$couCode])) {
            $pickupPieces[$couCode] = array(
              "serviceCode" => 1,
              "quantity" => 0,
              "weight" => 0,
              "destinationCountryCode" => $couCode
            );
          }
          $pickupPieces[$couCode]["quantity"] += 1;
          $pickupPieces[$couCode]["weight"] += $order->ordweight;

        } else if (($action === 'reprint') && empty($dpmId)) {
          $dpmId = $order->ordparcode2;
        }
      }
    }

    if (count($data) > 0) {
      if ($action === 'export') {

        $parIds = [];
        foreach ($ids as $id) {
          $ord = $orders->load($id);
          $parIds[] = $ord->ordparid;
          //nastavím status odesláno pokud ještě není a má vhodný stav
          if (($ord->ordstatus == 0 || $ord->ordstatus == 1 || $ord->ordstatus == 6) && !empty($ord->ordparcode)) {
            $orders->update($ord->ordid, array('ordstatus'=>3));
            $orders->logStatus($ord->ordid, 3, NULL, "API dopravce");

            $ord = $orders->load($id);

            //nastavím MALL status odesláno
            if (!empty($ord->ordmalid) && IS_PRODUCTION) {
              $config = $this->config;
              $config["neonParameters"] = $this->neonParameters;
              $mallApi = new \MallApi($config, $this->model);

              $dels = $this->model->getDeliveryModesModel();
              $arr = $dels->getDeliveryPaymentFromOrder($ord);
              $delMode = $arr["delivery"];
              $params = array();
              if (!empty($ord->ordparcode)) {
                 $params["ordparcode"] = (string)$ord->ordparcode;
                 if (!empty($delMode->delurlparcel)) {
                  $url = str_replace('#CODE#', $ord->ordparcode, $delMode->delurlparcel);
                  $params["trackingurl"] = $url;
                }
              }

              try {
                $mallApi->setStatus($ord->ordmalid, StatusEnum::SHIPPED()->getValue(), TRUE, $params);
              } catch (\Exception $e) {
                Debugger::log($e->getMessage());
              }

            }

            //mailuji zmenu stavu
            $this->mailOrderChanged($ord);
          }
        }
        $dpdApi->getShipmentLabel($parIds);

      } else if ($action === 'delete') {
        $ret = $dpdApi->deleteShipment($data);
      } else if ($action === 'print') {
        $this->printDpdShippingList($ids, );
        $this->terminate();
      } else {
        $ret = FALSE;
        $this->flashMessage("Neplatná akce.", "err");
      }
    } else if ($action === 'reprint' && $dpmId > 0) {
      $ret = $dpdApi->reprintManifest($dpmId);
    }

    if (count($dpdApi->errMsg) > 0) {
      $this->flashMessage("Nastala chyba. " . implode("|", $dpdApi->errMsg), "err");
    } else {
      $this->flashMessage("Hotovo");
    }

    $this->redirect('Order:default');
  }

  public function actionBatchAction() {
    $orders = $this->getParameter('export_orders');
    $ids = $this->getParameter('ordid');

    if ($ids === NULL || count($ids) == 0) {
      $this->flashMessage("Nevybrali jste žádný záznam", "err");
      $this->redirect('Order:default');
    }

    $priceCods = $this->getParameter('pricecod');
    $parcelsCounts = $this->getParameter('parcelscount');
    if ($orders !== NULL) {
      $this->printOrder($ids, "D", 'Order.latte');
    }

    //DPD
    $dpd_export = $this->getParameter('dpd_export');
    $dpd_delete = $this->getParameter('dpd_delete');
    $dpd_svoz = $this->getParameter('dpd_pickup');
    $dpd_print = $this->getParameter('dpd_print');
    $dpd_reprint = $this->getParameter('dpd_reprint');
    $dpd_reprintLabel = $this->getParameter('dpd_reprintlabel');
    if (isset($dpd_export)) {
      $this->dpdBatchAction("export", $ids, $priceCods, $parcelsCounts);
    } else if (isset($dpd_delete)) {
      $this->dpdBatchAction("delete", $ids);
    } else if (isset($dpd_svoz)) {
      $this->dpdBatchAction("pickup", $ids);
    } else if (isset($dpd_print)) {
      $this->dpdBatchAction("print", $ids);
    } else if (isset($dpd_reprint)) {
      $this->dpdBatchAction("reprint", $ids);
    } else if (isset($dpd_reprintLabel)) {
      $this->dpdBatchAction("reprintlabel", $ids);
    }

    $ulozenka = $this->getParameter('export_ulozenka');
    if ($ulozenka !== NULL) {
      $orders = $this->model->getOrdersModel();
      $parcels = array();

      foreach ($ids as $id) {
        $order = $orders->load($id);
        $uloName = (!empty($order->ordmalid) ? "ulozenka_mall" : "ulozenka");
        $ulozenkaApi = new UlozenkaApi($this->neonParameters[$uloName], $this->model);
        if ($order) {
          //nastaveno odeslano pokud se jedna o ulozenku
          $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
          $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);

          if ($delMode->delcode == 'ULOZENKA') {
            if ($ulozenkaApi->isApiOn()) {
              $ret = $ulozenkaApi->postParcelAdd($order);
              if ($ret === FALSE) {
                $this->flashMessage("Balík pro objednávku $order->ordcode se nepodařilo předat do Uloženky. ".$ulozenkaApi->errMsg, "err");
              } else if ($ret > 0) {
                $orders->update($order->ordid, array('ordparcode'=>$ret));
                $parcels[] = (int)$ret;
              }
            }
          } else {
            $this->flashMessage("Balík pro objednávku " . $order->ordcode . " nebyl vytvořený. Nejedná se o Uloženku.", "err");
          }
        }
      }

      $this->redirect('Order:default');
    }

    $zasilkovna = $this->getParameter('export_zasilkovna');
    $zasilkovnaTisk = $this->getParameter('print_zasilkovna');
    if (isset($zasilkovna) || isset($zasilkovnaTisk)) {
      $ids = $this->getParameter('ordid');
      $orders = $this->model->getOrdersModel();
      $parcels = array();
      if (!isset($ids) || count($ids) == 0) {
        $this->flashMessage("Nevybrali jste žádný záznam", "err");
        $this->redirect('Order:default');
      }

      $zasilkovnaApi = new \ZasilkovnaApi($this->neonParameters["zasilkovna"], $this->model);

      if (isset($zasilkovnaTisk)) {

        foreach ($ids as $id) {
          $order = $orders->load($id);
          if ($order) {
            $parcels[] = $order->ordparcode;
          }
        }

      } else {

        foreach ($ids as $id) {
          $order = $orders->load($id);
          if ($order) {
            //nastaveno odeslano pokud se jedna o ulozenku
            $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
            $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);

            if (str_starts_with($delMode->delcode, 'ZASILKOVNA')) {
              if ($zasilkovnaApi->isApiOn()) {
                $ret = $zasilkovnaApi->postParcelAdd($order);
                if ($ret === FALSE) {
                  $this->flashMessage("Balík pro objednávku $order->ordcode se nepodařilo předat do Zásilkovny. ".$zasilkovnaApi->errMsg, "err");
                } else if ($ret > 0) {
                  $orders->update($order->ordid, array('ordparcode'=>$ret));
                  $parcels[] = (string)$ret;

                  $ord = $orders->load($id);
                  //nastavím status odesláno pokud ještě není a má vhodný stav
                  if (($ord->ordstatus == 0 || $ord->ordstatus == 1 || $ord->ordstatus == 6) && !empty($ord->ordparcode)) {
                    $orders->update($ord->ordid, array('ordstatus'=>3));
                    $orders->logStatus($ord->ordid, 3, NULL, "API dopravce");

                    //nastavím MALL status odesláno
                    if (!empty($ord->ordmalid) && IS_PRODUCTION) {
                      $config = $this->config;
                      $config["neonParameters"] = $this->neonParameters;
                      $mallApi = new \MallApi($config, $this->model);

                      $dels = $this->model->getDeliveryModesModel();
                      $arr = $dels->getDeliveryPaymentFromOrder($ord);
                      $delMode = $arr["delivery"];
                      $params = array();
                      if (!empty($ord->ordparcode)) {
                         $params["ordparcode"] = (string)$ord->ordparcode;
                         if (!empty($delMode->delurlparcel)) {
                          $url = str_replace('#CODE#', $ord->ordparcode, $delMode->delurlparcel);
                          $params["trackingurl"] = $url;
                        }
                      }

                      try {
                        $mallApi->setStatus($ord->ordmalid, StatusEnum::SHIPPED()->getValue(), TRUE, $params);
                      } catch (\Exception $e) {
                        Debugger::log($e->getMessage());
                      }

                    }
                    //mailuji zmenu stavu
                    $this->mailOrderChanged($ord);
                  }
                }
              }
            }
          }
        }

      }

      $format = (string)$this->getParameter('formatZasilkovna');
      $firstPosition = (string)$this->getParameter('fpZasilkovna');
      if (count($parcels) > 0) {
        $ret = $zasilkovnaApi->postLabels($parcels, $format, $firstPosition);
        if (!$ret) {
          $this->flashMessage("Štítky s kódy " . (implode(', ', $parcels)) . " , se nepodařilo vytisknout. ".$zasilkovnaApi->errMsg, "err");
        }
      }
      $this->redirect('Order:default');
    }

    /*
    $wedo = $this->getParameter('wedo_export');

    if ($wedo !== NULL) {
      $orders = $this->model->getOrdersModel();
      $parcels = array();

      foreach ($ids as $id) {
        $order = $orders->load($id);
        $wedoApi = new WedoApi($this->neonParameters["wedo"], $this->model);
        if ($order) {
          //nastaveno odeslano pokud se jedna o ulozenku
          $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
          $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);

          if (($delMode->delcode == 'ULOZENKA' || $delMode->delcode == 'WEDO') && empty($order->ordmalid)) {
            if ($wedoApi->isApiOn()) {
              $ret = $wedoApi->postParcel($order);
              if ($ret === FALSE) {
                $this->flashMessage("Balík pro objednávku $order->ordcode se nepodařilo předat do Uloženky/WE|DO. ".$wedoApi->errMsg, "err");
              } else if ($ret > 0) {
                $orders->update($order->ordid, array('ordparcode'=>$ret));
              }
            }
          } else {
            $this->flashMessage("Balík pro objednávku " . $order->ordcode . " nebyl vytvořený. Nejedná se o Uloženku nebo WE|DO a nebo se jedná o MALL objednávku.", "err");
          }
        }
      }

      $this->redirect('Order:default');
    }

    $wedoAction = "";
    $wedo_batch = $this->getParameter('wedo_batch');
    $wedo_print = $this->getParameter('wedo_print');
    $wedo_batch_print = $this->getParameter('wedo_batch_print');
    $wedo_batch_printlabels = $this->getParameter('wedo_batch_printlabels');
    if ($wedo_batch !== NULL) {
      $wedoAction = "wedo_batch";
    } else if ($wedo_batch_print !== NULL) {
      $wedoAction = "wedo_batch_print";
    } else if ($wedo_batch_printlabels !== NULL) {
      $wedoAction = "wedo_batch_printlabels";
    } else if ($wedo_print !== NULL) {
      $wedoAction = "wedo_print";
    }

    if ($wedoAction !== "") {
      $orders = $this->model->getOrdersModel();
      $parcels = array();

      $batchNumber = 0;
      $orderId = 0;

      $wedoApi = new WedoApi($this->neonParameters["wedo"], $this->model);

      foreach ($ids as $id) {
        $order = $orders->load($id);
        if ($order) {
          $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
          $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);

          if (($delMode->delcode != 'ULOZENKA' && $delMode->delcode != 'WEDO') || !empty($order->ordmalid)) {
            $this->flashMessage("Balík pro objednávku " . $order->ordcode . " byl vynechán. Nejedná se o Uloženku nebo WE|DO a nebo se jedná o MALL objednávku.", "err");
            continue;
          }

          if ($wedoAction === "wedo_batch") {
            if (!empty($order->ordparcode)) {
              $parcels[$order->ordid] = $order->ordparcode;
            } else {
              $this->flashMessage("Balík pro objednávku " . $order->ordcode . " byl vynechán. Nemá přiřazeno číslo balíku.", "err");
            }
          } else if ($wedoAction === "wedo_print") {
            if (!empty($order->ordparcode)) {
              $orderId = (int)$order->ordparcode;
              break;
            }
          } else if ($wedoAction === "wedo_batch_print" || $wedoAction === "wedo_batch_printlabels") {
            if (!empty($order->ordparcode2)) {
              $batchNumber = $order->ordparcode2;
            }
          }
        }
      }

      if ($wedoApi->isApiOn()) {
        if ($wedoAction === "wedo_batch" && count($parcels) > 0) {
          $ret = $wedoApi->postBatch($parcels);
          if ($ret === FALSE) {
            $this->flashMessage("Soupisku balíků se nepodařilo předat do Uloženky/WE|DO. ".$wedoApi->errMsg, "err");
          } else if (!empty($ret)) {
            foreach ($parcels as $ordId => $parCode) {
              $orders->update($ordId, array('ordparcode2'=>$ret));
            }
          }
        } else if ($wedoAction === "wedo_print" && $orderId > 0) {
          $ret = $wedoApi->getLabels($orderId);
        } else if ($wedoAction === "wedo_batch_print" && !empty($batchNumber)) {
          $ret = $wedoApi->getBatchManifest($batchNumber);
        } else if ($wedoAction === "wedo_batch_printlabels" && !empty($batchNumber)) {
          $ret = $wedoApi->getBatchLabels($batchNumber);
        }
      }

      $this->flashMessage("Hotovo");
      $this->redirect('Order:default');

    }
    */

    $post = $this->getParameter('export_post');
    if (isset($post)) {
      $ids = $this->getParameter('ordid');
      $this->redirect(':Front:Export:post', array('ids'=>$ids));
    }

    $changeStatus = $this->getParameter('change_status');
    if (isset($changeStatus)) {
      $status = $this->getParameter('ordstatus');
      $ms = (int)$this->getParameter('mail_send');
      $mailSend = 1;
      if ($ms === 1) {
        $mailSend = 0;
      }

      //pošlu dál jen ty statusy co mají zaškrtnuté ID
      foreach ($status as $oid => $stat) {
        if (!isset($ids[$oid])) {
          unset($status[$oid]);
        }
      }

      $this->redirect('batchStatus', array('statuses'=>$status, 'mailSend'=> $mailSend));
    }
  }

  public function actionGetWedoLabels(int $orderNumber) {
    $wedoApi = new WedoApi($this->neonParameters["wedo"], $this->model);
    $wedoApi->getLabels($orderNumber);
  }

  public function actionBatchStatus(array $statuses, $mailSend) {
    foreach ($statuses as $key => $value) {
      $this->changeOrderStatus(array('ordid'=>$key, 'ordstatus'=>$value), (bool)$mailSend);
    }
    $this->redirect('default');
  }

  public function actionDeleteItem($id, $ordid) {
    if ($id > 0) {
      $orders = $this->model->getOrdersModel();
      $ordItems = $this->model->getOrdItemsModel();
      $ordItems->delete($id);
      $orders->recalcOrder($ordid);
      $this->flashMessage('Položka byla vymazána');
    }
    $this->redirect('edit', $ordid);
  }

  /********************* facilities *********************/
  protected function createComponentOrderChangeStateForm() {
    $order = $this->model->getOrdersModel();
    $form = new Nette\Application\UI\Form();
    $form->addSelect("ordstatus", "Nový stav objednávky", $order->getEnumOrdStatus());
    $form->addCheckbox("mail_block", "NEzasílat informační email");
    $form->addCheckbox("skip_mall", "Změnu stavu NEposílat do MALL");
    $form->addSubmit('newstate', 'Zmenit stav');
    $form->onSuccess[] = array($this, 'orderChangeStateFormSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }


  protected function createComponentOrderEditForm() {
    $order = $this->model->getOrdersModel();
    $dels = $this->model->getDeliveryModesModel();
    $enums = $this->model->getEnumcatsModel();
    $admins = $this->model->getAdminsModel();
    $enumCountries = $enums->getEnumCountries(false);
    $id = $this->getParam('id');
    $ord = $order->load($id);
    $payMode = $dels->load($ord->orddelid);
    $delMode = $dels->load($payMode->delmasid);

    $form = new Nette\Application\UI\Form();

    $form->addGroup('');
    $form->addCheckbox("ordpaystatus", "Zaplaceno");
    $form->addSelect("ordprccat", "Cenová kategorie:", $this->getEnumPrcCat());
    $form->addText("ordpricevat", "Celková cena:", 15)
      ->setOption('description', 'Změna cenové kategorie způsobí přepočtení ceny dopravy, ostatní položky nutno upravit ručně')
      ->setDisabled(True);
    $form->addText("orddisc", "Celková sleva:", 15)
      ->setDisabled(True);

    $form->addText("ordweight", "Celková hmotnost:", 15)
      ->setDisabled(True);

    $form->addSelect("ordadmid", "Obchodník:", $admins->getEnumAdmins())
      ->setPrompt('');

    $form->addText("ordparcode", "Číslo balíku:", 15);
    $form->addText("ordpricecod", "Cena dobírky:", 15);
    $form->addText("ordparcelscount", "Počet balíků:", 15);

    $form->addText("orddiscpercent", "Sleva v %:", 15)
      ->setRequired(FALSE)
      ->addRule(Nette\Forms\Form::NUMERIC, "Sleva v procentech musí být číslo;");

    $form->addText("ordcoucode", "Slevový kupón:", 60);

    $form->addselect("orddelid", "Doprava / platba:", $order->getEnumOrdDelId(FALSE))
      ->addRule(Nette\Forms\Form::FILLED, "Způsob dopravy musí být vyplněný")
      ->setPrompt('Vyberte ...');

    if ($delMode->delcode == 'ULOZENKA') {
      $arr = $dels->getEnumWedoPlaces();
      if (array_key_exists($ord->orddelspec, $arr) || empty($ord->orddelspec)) {
        $form->addselect("orddelspec", "WE|DO:", $arr)
          ->setPrompt("Nutno zadat odběrné místo");
      } else {
        $form->addText("orddelspec", "WE|DO:", 20)
          ->setDefaultValue($ord->orddelspec)
          ->addRule(Nette\Forms\Form::FILLED, "Nutno zadat odběrné místo");
      }
      if (!empty($ord->orddelspectext)) {
        $form["orddelspec"]->setOption('description', 'Odběrné místo:' . $ord->orddelspectext);
      }
    } else if ($delMode->delcode == 'CESKA_POSTA_BALIKOVNA') {
      $arr = $dels->getEnumBalikovnaPlaces();
      if (array_key_exists($ord->orddelspec, $arr) || empty($ord->orddelspec)) {
        $form->addselect("orddelspec", "Balíkovna:", $arr)
          ->setPrompt("Nutno zadat ...");
      } else {
        $form->addText("orddelspec", "Balíkovna:", 20)
          ->setDefaultValue($ord->orddelspec)
          ->addRule(Nette\Forms\Form::FILLED, "Nutno zadat odběrné místo");
      }

      if (!empty($ord->orddelspectext)) {
        $form["orddelspec"]->setOption('description', 'Odběrné místo:' . $ord->orddelspectext);
      }
    } else if ($delMode->delcode == 'ZASILKOVNA') {
      $arr = $dels->getEnumZasilkovnaPlaces();
      if (array_key_exists($ord->orddelspec, $arr) || empty($ord->orddelspec)) {
        $form->addselect("orddelspec", "Zásilkovna:", $arr)
          ->setPrompt("Nutno zadat ...");
      } else {
        $form->addText("orddelspec", "Zásilkovna:", 20)
          ->setDefaultValue($ord->orddelspec)
          ->addRule(Nette\Forms\Form::FILLED, "Nutno zadat odběrné místo");
      }

      if (!empty($ord->orddelspectext)) {
        $form["orddelspec"]->setOption('description', 'Odběrné místo:' . $ord->orddelspectext);
      }
    } else if ($delMode->delcode == 'DPD_PICKUP') {
      $arr = $dels->getEnumDelPickupPlaces();
      if (array_key_exists($ord->orddelspec, $arr) || empty($ord->orddelspec)) {
        $form->addselect("orddelspec", "Pickup point:", $arr)
          ->setPrompt("Nutno zadat ...");
      } else {
        $form->addText("orddelspec", "Pickup point:", 20)
          ->setDefaultValue($ord->orddelspec)
          ->addRule(Nette\Forms\Form::FILLED, "Nutno zadat odběrné místo");
      }

      if (!empty($ord->orddelspectext)) {
        $form["orddelspec"]->setOption('description', 'Odběrné místo:' . $ord->orddelspectext);
      }
    }

    $form->addText("ordmail", "Email:", 20);
    $form->addText("ordtel", "Telefon:", 10);

    $form->addGroup('Fakturační adresa');

    $form->addText("ordiname", "Jméno:", 60);
    $form->addText("ordilname", "Přijmení:", 60);
    $form->addText("ordifirname", "Název firmy:", 60);

    $form->addText("ordistreet", "Ulice:", 60);
    $form->addText("ordistreetno", "Číslo popisné:", 60);
    $form->addText("ordicity", "Město, obec:", 60);
    $form->addText("ordipostcode", "PSČ:", 6);
    //$form->addSelect("ordicouid", "Země:", $enumCountries);

    $form->addText("ordic", "IČ:", 10);
    $form->addText("orddic", "DIČ:", 10);
    $form->addCheckbox("ordusrvat", "Plátce DPH");

    $form->addGroup('Dodací adresa');

    $form->addText("ordstname", "Jméno:", 60);
    $form->addText("ordstlname", "Přijmení:", 60);
    $form->addText("ordstfirname", "Název firmy:", 60);

    $form->addText("ordststreet", "Ulice:", 60);
    $form->addText("ordststreetno", "Číslo popisné:", 60);
    $form->addText("ordstcity", "Město, obec:", 60);
    $form->addText("ordstpostcode", "PSČ:", 6);
    //$form->addSelect("ordstcouid", "Země:", $enumCountries);

    $form->addGroup('Poznámka');

    $form->addTextArea("ordnote", "", 100, 3);

      $form->addcheckbox("ordheurekagdpr", "Nesouhlas Heureka Ověřeno")->setDisabled();
      $form->addcheckbox("ordheurekamailblock", "Blokovat Heureka email - poděkování za nákup " . ($ord->ordheurekamail ? " (odesláno: " . $this->formatDate($ord->ordheurekamail) . ")" : ""));
    /*
    $form->addGroup('Fakturační údaje');
    $form->addText("ordinvcode", "Číslo faktury:", 10);
    //$form->addText("ordinvdate", "Datum vystavení:", 10);
    */

    $form->addSubmit('makeorder', 'Uložit');
    $form->onSuccess[] = array($this, 'orderEditFormSubmitted');
    return $form;
  }

  protected function createComponentOrdItemsEditForm() {
    $form = new Nette\Application\UI\Form();
    $ordid = (int)$this->getParam("id");
    //nactu polozky objedmavky
    if ($ordid > 0) {
      $form->addContainer('items');
      $rows = dibi::fetchAll("SELECT * FROM orditems WHERE oritypid=0 AND oriordid=%i", $ordid, " ORDER BY oriname");
      foreach ($rows as $row) {
        $key = 'item_'.$row->oriid;
        $form["items"]->addContainer($key);
        $form["items"][$key]->addHidden("oriid", $row->oriid);
        $form["items"][$key]->addText("oriproid", "", 5)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ID zboží.')
          ->setDefaultValue($row->oriproid);
        $form["items"][$key]->addText("oriname", "", 50)
          ->setAttribute('class', 'autocomplete')
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte název položky.')
          ->setDefaultValue($row->oriname);
        $form["items"][$key]->addText("oriprice", "", 5)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte cenu položky.')
          ->setDefaultValue($row->oriprice);
        $form["items"][$key]->addText("sn", "", 20);
        $form["items"][$key]->addText("oriqty", "", 3)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte počet položek.')
          ->setDefaultValue($row->oriqty);
      }
      //postovne
      $deliv = dibi::fetch("SELECT * FROM orditems WHERE oritypid=1 AND oriordid=%i", $ordid, " ORDER BY oriname");
      if ($deliv!==false) {
        $form['items']->addContainer('delivery');
        $form['items']['delivery']->addHidden("oriid", $deliv->oriid);
        $form['items']['delivery']->addHidden("oritypid", 1);
        $form['items']['delivery']->addHidden("oriqty", 1);
        $form['items']['delivery']->addText("oriname", "", 50)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte popis poštovného.')
          ->setDefaultValue($deliv->oriname);
        $form['items']['delivery']->addText("oripricemaster", "", 5)
          ->setDefaultValue($deliv->oripricemaster);
      }
      //nova polozka
      $form->addContainer('newitem');
      $form['newitem']->addHidden("oriordid", $ordid);
      $form['newitem']->addText("oriproid", "", 5);
      $form['newitem']->addText("oriname", "", 50)
        ->setAttribute('class', 'autocomplete');
      $form['newitem']->addText("oriprice", "", 5);
      $form['newitem']->addText("oriqty", "", 3)
        ->setDefaultValue(1);
    }
    $form->addSubmit('saveitems', 'Uložit');
    $form->onSuccess[] = array($this, 'ordItemsEditFormSubmitted');

    return $form;
  }

   public function ordItemsEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $ordid = (int)$this->getParam("id");
      $formVals = $form->getValues(TRUE);

      $orders = $this->model->getOrdersModel();
      $ordItems = $this->model->getOrdItemsModel();
      //nejdrive zjistim jestli nepipnul S/N
      $isSn = false;
      foreach ($formVals['items'] as $item) {
        if (!empty($item["sn"])) {
          $isSn = true;
          dibi::query("UPDATE orditems SET orisn=CONCAT(COALESCE(orisn, ''), IF(orisn IS NOT NULL, '|', ''), '".$item["sn"]."') WHERE oriid=%i", $item["oriid"]);
        }
      }
      if ($isSn) $this->redirect('edit#edititems', $ordid);

      foreach ($formVals['items'] as $item) {
        $id = $item["oriid"];
        unset($item["oriid"]);
        unset($item["sn"]);
        if (isset($item["oripricemaster"])) {
          if (trim($item["oripricemaster"]) == "") $item["oripricemaster"] = NULL;  
        }
        if (!empty($item["oriproid"])) {
          $product = dibi::fetch("SELECT * FROM products WHERE proid=%i", $item["oriproid"]);
          $item["oriprocode"] = $product->procode;
          $item["oriprocode2"] = $product->procode2;
          $item["orivatid"] = $product->provatid;
        }
        $ordItems->update($id, $item);

      }
      if (!empty($formVals['newitem']['oriname'])) {
        if (!empty($formVals['newitem']["oriproid"])) {
          $product = dibi::fetch("SELECT * FROM products WHERE proid=%i", $formVals['newitem']["oriproid"]);
          $formVals['newitem']["oriprocode"] = $product->procode;
          $formVals['newitem']["oriprocode2"] = $product->procode2;
          $formVals['newitem']["orivatid"] = $product->provatid;
          $ordItems->insert($formVals['newitem']);
        } else {
          $this->flashMessage("Nelze vložit položku, která není v databázi. Id není vyplněno.", "err");
          $this->redirect('edit', $ordid);
        }
      }
      $orders->recalcOrder($ordid);

      $this->flashMessage("Položky objednávky byly aktualizovány, celková cena objednávky byla prepočítána.");
    }
    $this->redirect('edit#edititems', $ordid);
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sCode = Null;
        $this->sName = Null;
        $this->sAdmin = Null;
        $this->sStatus = Null;
        $this->sOrdDateFrom = Null;
        $this->sOrdDateTo = Null;
        $this->sRows = Null;
        $this->sDelId = Null;
        $this->sPayId = Null;
        $this->sNotClosed = Null;
        $this->sOrdType = Null;
        $this->sOrderBy = Null;
        $this->sOrderByType = Null;
        $this->sExport = 0;
        $this->sUsrId = NULL;
      } else {
        $vals = $form->getValues();
        $this->sCode = $vals["code"];
        $this->sName = $vals["name"];
        $this->sAdmin = $vals["admin"];
        $this->sStatus = $vals["status"];
        $this->sOrdDateFrom = $vals["datefrom"];
        $this->sOrdDateTo = $vals["dateto"];
        $this->sRows = $vals["rows"];
        $this->sDelId = $vals["delid"];
        $this->sPayId = $vals["payid"];
        $this->sNotClosed = $vals["notclosed"];
        $this->sOrdType = $vals["ordtype"];
        $this->sOrderBy = $vals["orderby"];
        $this->sOrderByType = $vals["orderbytype"];
        $this->sExport = (int)$form["export"]->isSubmittedBy();
      }
    }
    $this->redirect("Order:default");
  }

  protected function createComponentSearchForm() {
    $orders = $this->model->getOrdersModel();
    $catalogs = $this->model->getCatalogsModel();
    $admins = $this->model->getAdminsModel();
    $dels = $this->model->getDeliveryModesModel();

    $form = new Nette\Application\UI\Form();
    $form->addGroup("Vyhledávání");

    $form->addText("code", "Kód objednávky", 10)
      ->setDefaultValue($this->sCode);

    $form->addText("datefrom", "Datum od", 6)
      ->setDefaultValue($this->sOrdDateFrom);

    $form->addText("dateto", "Datum do", 6)
      ->setDefaultValue($this->sOrdDateTo);


    $form->addText("rows", "Řádků na stránku", 6)
      ->setDefaultValue($this->sRows);

    $form->addText("name", "Příjmení nebo název firmy", 10)
      ->setDefaultValue($this->sName);

    $form->addSelect("admin", "Obchodník", $admins->getEnumAdmins())
      ->setPrompt('')
      ->setDefaultValue($this->sAdmin);

    $form->addSelect("status", "Stav", $orders->getEnumOrdStatus())
      ->setPrompt('');

    if (!empty($this->sStatus)) {
      $form["status"]->setDefaultValue($this->sStatus);
    }

    $arr = $dels->getEnumDelModes();

    $arr["CESKA_POSTA"] = "Česká pošta všechny typy";
    $arr["DPD"] = "DPD všechny typy";
    $arr["ULOZENKAWEDO"] = "WE|DO všechny typy";
    $arr["ZASILKOVNA"] = "Zásilkovna všechny typy";

    $form->addSelect("delid", "Doprava", $arr)
      ->setDefaultValue($this->sDelId)
      ->setPrompt('');

    $form->addSelect("payid", "Platba", $orders->getEnumOrdDelId(FALSE))
      ->setDefaultValue($this->sPayId)
      ->setPrompt('');

    if (!empty($this->sStatus)) {
      $form["status"]->setDefaultValue($this->sStatus);
    }
      
    $form->addCheckbox("notclosed", "Neuzavřené")
      ->setDefaultValue($this->sNotClosed);

    $form->addCheckbox("ordtype", "jen Mall objednávky")
      ->setDefaultValue($this->sOrdType);


    $arr = array(
      'ordid'=>'Data vytvoření',
    );
    $form->addSelect("orderby", "Řadit podle", $arr)
      ->setDefaultValue($this->sOrderBy);

    $arr = array(
      'ASC'=>'Vzestupně',
      'DESC'=>'Sestupně',
    );
    $form->addSelect("orderbytype", "Typ řazení", $arr)
      ->setDefaultValue($this->sOrderByType);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->addSubmit('export', 'Exportovat');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  protected function createComponentOrderQuickForm() {
    $order = $this->model->getOrdersModel();
    $enums = $this->model->getEnumcatsModel();
    $enumCountries = $enums->getEnumCountries(false);

    $form = new Nette\Application\UI\Form();

    $ordType = (int)$this->getParameter("type");
    if (empty($ordType)) {
      $ordType = 1;
    }

    $form->addHidden("ordusrid");

    $form->addHidden("ordadmid", $this->adminData->admid);

    $form->addHidden("ordexported", 2);
    $form->addHidden("ordexportblock", 0);
    $form->addHidden("ordtype", $ordType);

    $form->addText("ordiname", "Jméno:", 60)
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno");
    $form->addText("ordilname", "Přijmení:", 60)
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno");

    $form->addText("ordifirname", "Název firmy:", 60);

    $form->addText("ordistreet", "Ulice:", 60);
    $form->addText("ordistreetno", "Číslo popisné:", 60);
    $form->addText("ordicity", "Město, obec:", 60);
    $form->addText("ordipostcode", "PSČ:", 6);
    $form->addSelect("ordicouid", "Země:", $enumCountries);

    $form->addselect("orddelid", "Doprava / platba:", $order->getEnumOrdDelId(FALSE))
      ->addRule(Nette\Forms\Form::FILLED, "Způsob dopravy musí být vyplněný")
      ->setPrompt('Vyberte ...');

    $form->addSelect("ordprccat", "Cenová kategorie:", $this->getEnumPrcCat());

    $form->addText("ordpricecod", "Cena dobírky:", 15)
      ->setDefaultValue(0);
    $form->addText("ordparcelscount", "Počet balíků:", 15)
      ->setDefaultValue(1);

    if ($this->secondCurrency) {  
      $form->addselect("ordcurid", "Měna:", $this->getEnumCurr())
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněná")
        ->setPrompt('Vyberte ...');  
    } else {
      $form->addHidden("ordcurid", 1);
    }    
      
    $form->addText("ordtel", "Telefon:", 30);
    $form->addText("ordmail", "Email:", 30)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addSubmit('makeorder', 'Vytvořit ' . ($ordType == 3 ? "přepravní štítek" : "objednávku a upravit položky"));
    $form->onSuccess[] = array($this, 'orderQuickFormSubmitted');
    return $form;
  }

  public function orderQuickFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $values = $form->getValues();
      $orders = $this->model->getOrdersModel();
      $dels = $this->model->getDeliveryModesModel();
      $dels->setCurrency($this->currencies, $values["ordcurid"]);
      $orders->setCurrency($this->currencies, $values["ordcurid"]);
      $values["ordusrid"] = (int)$values["ordusrid"];
      $id = $orders->insert($values);

      //vlozim dopravu
      //nactu si zpusob dopravy
      
      $paymode = $dels->load($values["orddelid"]);
      $delmode = $dels->load($paymode->delmasid);
      $delValues['oriordid'] = $id;
      $delValues['oritypid'] = 1;
      $delValues['oriproid'] = 0;
      $delValues['oriname'] = "Doprava: ".$delmode->delname." - ".$paymode->delname;
      $delValues['oriprice'] = $paymode->delprice;
      $delValues['orivatid'] = Null;
      $delValues['oricredit'] = 0;
      $delValues['oriqty'] = 1;
      $delValues['oriprobigsize'] = 0;
      $delValues['oriprooffer'] = 0;
      $orditems = $this->model->getOrdItemsModel();
      $orditems->insert($delValues);
      $orders->recalcOrder($id);
      $this->flashMessage('Uloženo v pořádku');
      $this->redirect('edit#edititems', $id);
    }
  }

  protected function createComponentPostImportParcelsForm(): Nette\Application\UI\Form {
    $form = new Nette\Application\UI\Form();
    $form->addUpload("csvfile", "Importní soubor");
    $form->addSubmit('submit', 'Nahrát');
    $form->onSuccess[] = array($this, 'postImportParcelsFormSubmitted');
    return $form;
  }

  public function postImportParcelsFormSubmitted (Nette\Application\UI\Form $form) {
    $vals = $form->getValues();
    $file = null;
    if (!$vals["csvfile"]->isOk()) {
      $form->addError("Neplatný importní soubor.");
      return;
    }

    $file = $vals["csvfile"];
    unset($vals["csvfile"]);

    $ords = $this->model->getOrdersModel();

    //číselník stavů balíku pošty a jejich ekvivalent v eshopu
    $parStatusEnum = array(
      '00' => NULL,  //Data z PodáníOnline
      '13' => NULL,  //Adresní údaje k zásilce
      '21' => NULL,  //Podaná
      '75' => NULL,  //Přepravovaná (pouze I.TÚ)
      '51' => NULL,  //Vstup na dodací poštu
      '8D' => NULL,  //Dosílka na jinou adresu
      '8E' => NULL,  //Poškozená
      '81' => NULL,  //Uloženo na poště
      '82' => NULL,  //Uložená
      '8T' => NULL,  //Chybně směrovaná (vinou pošty)
      '8U' => NULL,  //Předaná k doručení jiné poště
      '8Z' => NULL,  //Chybně směrovaná (vinou odesílatele)
      '88' => NULL,  //Vyšlá z evidence
      '91' => 4,  //Doručená
      '95' => 10,  //Vrácená (odepřeno přijetí)
      '96' => 10,  //Vrácená (adresát neznámý)
      '97' => 10,  //Vrácená (prošlá odběrní lhůta)
      '98' => 10,  //Vrácená (úmrtí adresáta)
      '9B' => 10,  //Vrácená (adresát se odstěhoval bez udání adresy)
      '9E' => 10,  //Vrácená (nedostatečná adresa)
      '9V' => 10,  //Doručená odesílateli
      'C1' => NULL,  //Výstup z vyměňovací pošty
      'D4' => NULL,  //Vstup do země určení
      'Da' => NULL,  //Vstup na vstupní poštu - vrácená
      'E' => NULL,  //Celní řízení
      'F1' => NULL,  //Ukončení celního řízení
      'M' => NULL,  //Pokus o doručení
      'I1' => 4,  //Doručená
      'H' => 10,  //Vrácená
    );

    //načtu csv a aktualizuji čísla balíků
    $cnt = 0;
    $cntUpd = 0;
    $cntState = 0;
    $cntClosed = 0;
    $parcels = array();
    if (($handle = fopen($file->getTemporaryFile(), 'rb')) !== FALSE) {
      while (($data = fgetcsv($handle, 1000, ";")) !== FALSE) {

        //načtu data o
        $ordCode = (string)$data[0];
        $parCode = (string)$data[1];
        $parDate = new \DateTime();

        $arr = explode(" ", (string)$data[2]);
        if (count($arr) == 2) {
          $parDate = $this->formatDateMySQL($arr[0]);
        }

        $parStatus = (string)$data[3];

        if (!empty($ordCode) && !empty($parCode)) {

          $ord = $ords->load($ordCode, "code");

          if ($ord) {
            $cnt++;
            $changeSendState = TRUE;
            //objednávka je expedována, zjistím jestli nebyla doručena / vrácena / stornována
            if (($ord->ordstatus == 3) && !empty($parStatusEnum[$parStatus])) {
              //balík má nějaký status, který nás zajímá
              $newStatus = (int)$parStatusEnum[$parStatus];
              $ords->update($ord->ordid, array('ordstatus'=>$newStatus));
              $ords->logStatus($ord->ordid, $newStatus, $parDate);
              $cntClosed++;
            }

            if ($ord->ordstatus == 2 || $ord->ordstatus == 3 || $ord->ordstatus == 4 || $ord->ordstatus == 5 || $ord->ordstatus == 7 || $ord->ordstatus == 9) {
              //$this->flashMessage("Objednávka č. $ordCode je v nevhodném stavu pro odeslání.", "err");
              $changeSendState = FALSE;
            } else {
              //zjistím jestli nebylo už odesláno
              $cntRows = (int)dibi::fetchSingle("SELECT count(orlid) FROM orders_log WHERE orlordid=%i", $ord->ordid, " AND orlstatus=3");
              if ($cntRows > 0) {
                $changeSendState = FALSE;
              }
            }
            if (empty($ord->ordparcode)) {

              if ($changeSendState) {

                $vals = array(
                  'ordid' => $ord->ordid,
                  'ordparcode' => $parCode,
                  'ordstatus' => 3,
                  'ordadmid' => $this->adminData->admid
                );

                $this->changeOrderStatus($vals);
                $ords->logStatus($ord->ordid, 3, $parDate);
                $cntState++;
              } else {
                $vals = array(
                  "ordparcode" => $parCode,
                );
                $ords->update($ord->ordid, $vals);
                $cntUpd++;
              }
            }
          }
        }
      }
      fclose($handle);
    }
    $this->flashMessage("Celkem nalezeno $cnt objednávek. Aktualizovaných objednávek: $cntUpd, nastaven stav odesláno u $cntState objednávek, dodáno/vráceno $cntClosed objednávek.");
    $this->redirect("Order:default");
  }

}
