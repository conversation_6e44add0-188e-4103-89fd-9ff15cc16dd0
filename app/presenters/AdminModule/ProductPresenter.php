<?php
namespace AdminModule;

use dibi;
use Nette;

final class ProductPresenter extends BasePresenter {

  /** @persistent */
  public $sCode = '';

  /** @persistent */
  public $sCode2 = '';

  /** @persistent */
  public $sCodeP = '';

  /** @persistent */
  public $sCodePEmpty = FALSE;

  /** @persistent */
  public $sCode2Empty = FALSE;

  /** @persistent */
  public $sWithoutVariants = FALSE;

  /** @persistent */
  public $sPaginationOff = FALSE;

  /** @persistent */
  public $sName = '';

  /** @persistent */
  public $sCatId = '';

  /** @persistent */
  public $sManId = '';

  /** @persistent */
  public $sTypId = Null;
  /** @persistent */
  public $sTypId2 = Null;
  /** @persistent */
  public $sTypId3 = Null;
  /** @persistent */
  public $sTypId4 = Null;
  /** @persistent */
  public $sTypId5 = Null;

  /** @persistent */
  public $sPrioritize = Null;

  /** @persistent */
  public $sStatus = 0;

  /** @persistent */
  public $sOrderBy = 'pro.procode';

  /** @persistent */
  public $sOrderByType = 'ASC';

  public function productEditFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {

      $product = $this->model->getProductsModel();
      $id = (int)$this->getParam('id');
      $vals = $form->getValues();

      //pokud nova polozka a neni vyplneny kod zbozi tak predplnim
      if (empty($vals["procode"]) AND ($id == 0 || $form['saveAsNew']->isSubmittedBy())) {
        $procode = (int)dibi::fetchSingle("SELECT procode FROM products ORDER BY CAST(procode AS UNSIGNED) DESC");
        $vals["procode"] = $procode + 1;
      }
      $digits1 = $this->neonParameters["currency"][1]["decimals"];
      $vals["proprice1com"] = round((double)$vals["proprice1com"], $digits1);
      $vals["proprice1a"] = round((double)$vals["proprice1a"], $digits1);
      $vals["proprice1b"] = round((double)$vals["proprice1b"], $digits1);
      $vals["proprice1c"] = round((double)$vals["proprice1c"], $digits1);
      $vals["proprice1d"] = round((double)$vals["proprice1d"], $digits1);
      $vals["proprice1e"] = round((double)$vals["proprice1e"], $digits1);
      $vals["proprice1f"] = round((double)$vals["proprice1f"], $digits1);
      if ($this->secondCurrency) {
        $digits2 = $this->neonParameters["currency"][2]["decimals"];
        $vals["proprice2com"] = round((double)($vals["proprice1com"] / $this->config["PRICE2RATE"]), $digits2); 
        $vals["proprice2a"] = round((double)($vals["proprice1a"] / $this->config["PRICE2RATE"]), $digits2); 
        $vals["proprice2b"] = round((double)($vals["proprice1b"] / $this->config["PRICE2RATE"]), $digits2); 
        $vals["proprice2c"] = round((double)($vals["proprice1c"] / $this->config["PRICE2RATE"]), $digits2); 
        $vals["proprice2d"] = round((double)($vals["proprice1d"] / $this->config["PRICE2RATE"]), $digits2); 
        $vals["proprice2e"] = round((double)($vals["proprice1e"] / $this->config["PRICE2RATE"]), $digits2);
        $vals["proprice2f"] = round((double)($vals["proprice1f"] / $this->config["PRICE2RATE"]), $digits2);
      }
      $copyPrice = (bool)$vals["copyprice"];

      $vals["procpcheureka"] = (double)$vals["procpcheureka"] === 0.0 ? NULL : (double)$vals["procpcheureka"];
      $vals["procpczbozi"] = (double)$vals["procpczbozi"] === 0.0 ? NULL : (double)$vals["procpczbozi"];

      //vezmu si obrazek pokud byl odeslan
      $image0 = null;
      $imagefile0 = NULL;
      $images = null;
      $image_usrsize = null;
      $imagefile_usrsize = NULL;
      $image_gift = null;
      $imagefile_gift = null;
      $image1 = null;
      $imagefile1 = null;
      $file1 = null;
      if ($vals["pic0"]->isOk()) {
        $imagefile0 = $vals["pic0"];
        $image0 = $vals["pic0"]->toImage();
      }
      /*
      if ($vals["pics"]->isOk()) {
        $images = $vals["pics"]->toImage();
      }
      */
      if ($vals["pic_usrsize"]->isOk()) {
        $imagefile_usrsize = $vals["pic_usrsize"];
        $image_usrsize = $vals["pic_usrsize"]->toImage();
      }

      $image_gift = $vals['pic_gift'];

      if ($vals["pic1"]->isOk()) {
        $imagefile1 = $vals["pic1"];
        $image1 = $vals["pic1"]->toImage();
        $image1_position = $vals["pic1position"];
      }
      if ($vals["attAdd"]->isOk()) {
        $file1 = $vals["attAdd"];
        $filedesc = $vals["ataname"];
      }

      if (!empty($vals->mastercode)) {
        $pi = (int)dibi::fetchSingle("SELECT proid FROM products WHERE procode=%s", $vals->mastercode);
        $vals->promasid = $pi;
      } else {
        $vals->promasid = 0;
      }
      unset($vals->mastercode);
      unset($vals["copyprice"]);
      
      unset($vals->watchdog_price);
      unset($vals->watchdog_store);
      
      //pokud neni vyplneny nazev obrazku, vyplnim kod zbozi
      if (empty($vals["propicname"])) $vals["propicname"] = $vals["procode"];

      //naformatuji datum pokud je vyplněno
      if (!empty($vals["propromodateto"])) {
        $vals["propromodateto"] = $this->formatDateMySQL($vals["propromodateto"]);
      } else {
        $vals["propromodateto"] = NULL;
      }

      //projdu formularova pole a ktere nejsou treba odstranim
      $catIds = array();
      $proParam = array();
      foreach ($vals as $key => $value) {
        //zarazeni do katalogu
        if (substr($key, 0, 4) === 'cat_') {
          if (($key == 'cat_0' || $key == 'cat_') && $value > 0) {
            //nove zarazeni
            $catIds[] = $value;
          } else if ($value) {
            //stavajici zarazeni
            $catIds[] = substr($key, 4);
          }
          unset($vals[$key]);
        }
        //parametry zbozi
        if (substr($key, 0, 10) == "param_name") {
          $arr = explode("_", $key);
          if (!empty($value) && (!empty($vals["param_value_".$arr[2]]) || (isset($vals["param_typid_".$arr[2]]) && $vals["param_typid_".$arr[2]] == 1))) {
            if (!isset($vals["param_typid_".$arr[2]])) {
              $prptypid = 0;
            } else {
              $prptypid = (int)$vals["param_typid_".$arr[2]];
            }
            $proParam[$arr[2]] = array('prpname' => $value, 'prpvalue' => $vals["param_value_".$arr[2]], 'prptypid' => $prptypid);
          }
        }

        if (substr($key, 0, 3) != "pro") unset($vals[$key]);
      }

      //pokud se ma ulozit jako nova polozka
      $saveAsNewSrcProId = 0;
      if ($id > 0) {
        if ($form['saveAsNew']->isSubmittedBy()) {
          $saveAsNewSrcProId = $id;
          $id = 0;
        }
      }
      $isnew = ($id == 0);
      try {

        $vals["proratingall"] = (int)$vals["proratingall"];
        $vals["proratinging"] = (int)$vals["proratinging"];
        $vals["proratingmis"] = (int)$vals["proratingmis"];
        $vals["proratingdig"] = (int)$vals["proratingdig"];
        $vals["proratingtas"] = (int)$vals["proratingtas"];

        $vals["promalldateu"] = NULL;

        if ($product->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');

          //upravim a ulozim hlavni a doplnkovy obrazek
          $sizes = (array($this->config["PROPICSIZE_BIG"].'xbig', $this->config["PROPICSIZE_DETAIL"].'xdetail', $this->config["PROPICSIZE_LIST"].'xlist'));
          if (isset($image0)) {
            $imagefile0->move(WWW_DIR . '/pic/product/src/'. $vals["propicname"] . ".jpg");
            $this->deletePic(WWW_DIR . "/pic/product/", $vals["propicname"] . ".jpg");
            $this->saveImage($image0, WWW_DIR . "/pic/product", $vals["propicname"] . ".jpg", $sizes);
          }
          if (isset($images)) {
            $this->saveImage($images, WWW_DIR . "/pic/product", $vals["propicname"] . ".jpg", array('200x284xslider'));
          }
          if (isset($image_usrsize)) {
            $imagefile_usrsize->move(WWW_DIR . '/pic/product/src/'. $vals["propicnamevar"] . ".jpg");
            $this->deletePic(WWW_DIR . "/pic/product/", $vals["propicnamevar"] . ".jpg");
            $this->saveImage($image_usrsize, WWW_DIR . "/pic/product", $vals["propicnamevar"] . ".jpg", array('40x40xusrsize'));
          }
          if ($image_gift->isOk()) {
            $image_gift->move(WWW_DIR . '/pic/product/gift/'. $vals['propicname'] . '.png');
          }
          if (isset($image1)) {
            $imagefile1->move(WWW_DIR . '/pic/product/src/'. $vals["propicname"] . "_" . $image1_position . ".jpg");
            $this->deletePic(WWW_DIR . "/pic/product/", $vals["propicname"] . "_" . $image1_position . ".jpg");
            $this->saveImage($image1, WWW_DIR . "/pic/product", $vals["propicname"] . "_" . $image1_position . ".jpg", $sizes);
          }
          //ulozim prilohu
          if (isset($file1)) {
            //ulozim do db
            $atts = $this->model->getAttachmentsModel();
            $ataVals = array(
              'ataproid'=>$id,
              'ataname'=>$filedesc,
              'atafilename'=>Nette\Utils\Strings::webalize($filedesc).'_'.$id.'.'.substr($file1->getName(), -3),
              'atasize'=>(string)$file1->getSize(),
              'atatype'=>substr($file1->getName(), -3),
            );
            $atts->insert($ataVals);
            $file1->move(WWW_DIR.'/files/'.$ataVals["atafilename"]);
          }
          //zpracovani zarazeni do katalogu
          $pro = $product->load($id);
          if ($id > 0) {
            $product->updateCatPlace($id, $catIds);
            //pokud má varianty přenesu zařazení i do variant
            if ($pro->proismaster === 1) {
              $rows = dibi::fetchAll("SELECT proid FROM products WHERE promasid=%i", $id);
              foreach ($rows as $row) {
                $product->updateCatPlace($row->proid, $catIds);
              }
            }
          }

          //zpracovani parametru
          $proparams = $this->model->getProParamsModel();
          if ($saveAsNewSrcProId > 0) {
            $params = dibi::fetchAll("SELECT * FROM proparams WHERE prpproid=%i", $saveAsNewSrcProId, " ORDER BY prpid");
            foreach ($params as $row) {
              unset($row->prpid, $row->prpdatec, $row->prpdateu);
              $row->prpproid = $id;
              $proparams->insert($row);
            }
          } else {
            foreach ($proParam as $key => $value) {
              if (substr($key, 0, 1) == 'N') {
                $key = 0;
                $value["prptypid"] = 0;
              } else if ($key === 'm0') {
                $key = 0;
                $value["prptypid"] = 1;
              } else if ($key === '0') {
                $key = 0;
                $value["prptypid"] = 0;
              }
              $proparams->save($key, array('prpproid' => $id, 'prpname' => $value["prpname"], 'prpvalue' => $value["prpvalue"], 'prptypid' => $value["prptypid"]));
            }
          }
          //prilohy
          if ($saveAsNewSrcProId > 0) {
            $files = dibi::fetchAll("SELECT * FROM attachments WHERE ataproid=%i", $saveAsNewSrcProId);
            $ata = $this->model->getAttachmentsModel();
            foreach ($files as $row) {
              unset($row->ataid);
              unset($row->atadatec);
              unset($row->atadateu);
              $row->ataproid = $id;
              $ata->insert($row);
            }
          }

          $pro = $product->load($id);

          //aktualizace podrizenych polozek
          if ($pro->proismaster === 1) {
            dibi::query("
              UPDATE products SET 
              prodelfree=%i", $vals["prodelfree"], ",
              provatid=%i", $vals["provatid"], ",
              promanid=%i", $vals["promanid"], ",
              promalllabels=%s", $vals["promalllabels"], ",
              propromodateto=%s", $vals["propromodateto"], ",
              pronotdisc=%f", $vals["pronotdisc"], ",
              proweight=%f", $vals["proweight"], ",
              provolume=%f", $vals["provolume"], ",
              provolumeunit=%s", $vals["provolumeunit"], "
              WHERE promasid=%i", $id
            );
          }

          $product->copyParamsToVariants($id);
          $product->recalcProductVariants($id);


          if ($pro->promasid > 0) {
            $product->updateMasterStore($pro->promasid);
          }

          if ($copyPrice && (int)$pro->proismaster === 1) {
            dibi::query("
              UPDATE products SET 
              proprice1com=%f", $vals["proprice1com"], ",
              proprice1a=%f", $vals["proprice1a"], ",
              proprice1b=%f", $vals["proprice1b"], ",
              proprice1c=%f", $vals["proprice1c"], ",
              proprice1d=%f", $vals["proprice1d"], ",
              proprice1e=%f", $vals["proprice1e"], ",
              proprice1f=%f", $vals["proprice1f"], ",
              proprice2com=%f", $vals["proprice2com"], ",
              proprice2a=%f", $vals["proprice2a"], ",
              proprice2b=%f", $vals["proprice2b"], ",
              proprice2c=%f", $vals["proprice2c"], ",
              proprice2d=%f", $vals["proprice2d"], ",
              proprice2e=%f", $vals["proprice2e"], ",
              proprice2f=%f", $vals["proprice2f"], ",
              protypid=%i", $vals["protypid"], ",
              protypid2=%i", $vals["protypid2"], ",
              protypid3=%i", $vals["protypid3"], ",
              protypid4=%i", $vals["protypid4"], ",
              protypid5=%i", $vals["protypid5"], "
              WHERE promasid=%i", $id
            );  
          }
        }        
      } catch (\Exception $e) {
        $form->addError($e->getMessage());
      }

      //redirect podle tlacitka
      $btn = $form->isSubmitted();
      $btn_name = $btn->name;

      if ($isnew) {
        $btn_name = "newitem";
      }
      switch ($btn_name) {
         case 'save':
           $this->redirect("default");
           break;
         case 'newitem':
           $this->redirect("edit", $id);
           break;
         case 'saveAsNew':
          $this->redirect("this");
           break;
         default:
           $this->redirect("edit#".$btn_name, array('id'=>$id));
           break;
      }
    }
  }

  public function productListEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {

      $products = $this->model->getProductsModel();
      $vals = $form->getValues();
      foreach ($vals as $id => $item) {

        if ($item['proaccess']!="" || $item['procode2']!="" || $item['procodep']!=""  || $item['prostatuschange'] || $item['proorder']!="") {
          if ($item['proaccess']=="") unset($item['proaccess']);
          if ($item['procode2']=="") unset($item['procode2']);
          if ($item['procodep']=="") unset($item['procodep']);
          if ($item['proorder']=="") unset($item['proorder']);
          if ($item['prostatuschange']) {
            $item['prostatus'] = ($item['prostatus'] == 1 ? 0 : 1);
          } else {
            unset($item['prostatus']);
          }
          unset($item['prostatuschange']);
          if (count($item) > 0) $products->update($id, $item);
        }
      }
      $this->redirect('default');
    }
  }

  public function productBatchUpdateFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $proPrice = (int)$vals["proprice"];
      $prcCat = $vals["prcid"];
      $sql = "UPDATE products SET proprice$prcCat=".$proPrice." WHERE proid IN (SELECT capproid FROM catplaces INNER JOIN catalogs ON (capcatid=catid) WHERE capcatid=".$vals["catid"]." AND catpathids LIKE '|".$vals["catid"]."|%')";
      if (dibi::query($sql)) {
          $this->flashMessage("Ceny byly aktualizovány");
         $this->redirect('this');
      } else {
        $form->addError("Nastala chyba při aktualizaci ceny");
      }

    }
  }


  /********************* view default *********************/
  private function getDataSourceSql() {
    $where = "";
    if (!empty($this->sCode)) $where .= " pro.procode LIKE '%$this->sCode%' AND ";
    if (!empty($this->sCode2)) $where .= " pro.procode2 LIKE '%$this->sCode2%' AND ";
    if (!empty($this->sCodeP)) $where .= " pro.procodep LIKE '%$this->sCodeP%' AND ";
    if ($this->sCodePEmpty) $where .= " (pro.procodep = '' OR pro.procodep IS NULL) AND pro.proismaster=0 AND ";
    if ($this->sCode2Empty) $where .= " (pro.procode2 = '' OR pro.procode2 IS NULL) AND pro.proismaster=0 AND ";
    if ($this->sWithoutVariants) $where .= " coalesce(pro.promasid, 0)=0 AND ";
    if (!empty($this->sName)) $where .= " pro.proname LIKE '%$this->sName%' AND ";
    if (!empty($this->sCatId)) $where .= " pro.proid IN (
      SELECT capproid 
      FROM catplaces 
      INNER JOIN catalogs ON (catid=capcatid)
      WHERE catpathids LIKE '%|$this->sCatId|%'
    ) AND ";
    if (!empty($this->sManId)) $where .= " pro.promanid=$this->sManId AND ";
    if (!empty($this->sTypId)) $where .= " pro.protypid=$this->sTypId AND ";
    if (!empty($this->sTypId2)) $where .= " pro.protypid2=$this->sTypId2 AND ";
    if (!empty($this->sTypId3)) $where .= " pro.protypid3=$this->sTypId3 AND ";
    if (!empty($this->sTypId4)) $where .= " pro.protypid4=$this->sTypId4 AND ";
    if (!empty($this->sTypId5)) $where .= " pro.protypid5=$this->sTypId5 AND ";
    if ((int)$this->sStatus > -1) $where .= " IF(pro.prostatus=1,pro.prostatus,COALESCE(promas.prostatus,pro.prostatus))=$this->sStatus AND ";
    if ((int)$this->sPrioritize === 1) $where .= " pro.proprioritize=1 AND ";

    if (!empty($where)) {
      $where = " WHERE ".substr($where, 0, -5);
    }
    if (empty($this->sOrderBy)) $this->sOrderBy = "pro.procode";
    $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;

    return "
      SELECT pro.proid, pro.promasid, pro.proismaster, pro.procode, pro.procode2, pro.procodep, pro.proname, pro.proname2, IF(pro.prostatus=1,pro.prostatus,COALESCE(promas.prostatus,pro.prostatus)) AS prostatus, pro.proprice1a, manname, pro.proaccess, pro.proorder, 
        coalesce(prscnt, 0) AS prscnt, pro.probigsize, pro.prooffer
        FROM products AS pro
        LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
        LEFT JOIN manufacturers ON (pro.promanid=manid)
        LEFT JOIN products_salestat ON (prsproid=pro.proid AND prsperiod=30)
        $where
        $orderBy";
  }


  private function getDataSource() {
    $product = $this->model->getProductsModel();
    return $product->getDataSource($this->getDataSourceSql());
  }

  public function renderDefault($level = 0) {
    $dataSource = $this->getDataSource();
    $paginator = $this['paginator']->getPaginator();
    $paginator->itemsPerPage = ($this->sPaginationOff ? 10000 : $this->config["ADMIN_ROWSCNT"]);
    $paginator->itemCount = $dataSource->count();
    $dataRows = dibi::fetchAll($this->getDataSourceSql() . " LIMIT $paginator->itemsPerPage OFFSET $paginator->offset");

    //$dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page;
    $this->template->dataRows = $dataRows;

    //ciselnik statusu
    $product = $this->model->getProductsModel();
    $this->template->enum_prostatus = $product->getEnumProStatus();

    //polozky co nejsou v katalogu
    $this->template->prosNotInCatalog = array(); //= dibi::fetchAll("SELECT * FROM products WHERE NOT EXISTS (SELECT 1 FROM catplaces WHERE capproid=proid)");
  }
  
  public function actionCalculatePrice2() {
    if ($this->secondCurrency) {
      $digits = (int)$this->neonParameters["currency"][2]["decimals"];
      $rate = (double)$this->config["PRICE2RATE"];
      dibi::query("
        UPDATE products SET 
        proprice2com = ROUND(proprice1com / ".$rate."*IF(promanid=1,1.05,1), ".$digits.")
        WHERE proprice1com IS NOT NULL
      ");
      dibi::query("
        UPDATE products SET 
        proprice2a = ROUND(proprice1a / ".$rate."*IF(promanid=1,1.05,1), ".$digits.")
        WHERE proprice1a IS NOT NULL
      ");
      dibi::query("
        UPDATE products SET 
        proprice2b = ROUND(proprice1b / ".$rate."*IF(promanid=1,1.05,1), ".$digits.")
        WHERE proprice1b IS NOT NULL
      ");
      dibi::query("
        UPDATE products SET 
        proprice2c = ROUND(proprice1c / ".$rate."*IF(promanid=1,1.05,1), ".$digits.")
        WHERE proprice1c IS NOT NULL
      ");
      dibi::query("
        UPDATE products SET 
        proprice2d = ROUND(proprice1d / ".$rate."*IF(promanid=1,1.05,1), ".$digits.")
        WHERE proprice1d IS NOT NULL
      ");
      dibi::query("
        UPDATE products SET 
        proprice2e = ROUND(proprice1e / ".$rate."*IF(promanid=1,1.05,1), ".$digits.")
        WHERE proprice1e IS NOT NULL
      ");

      $this->flashMessage("Ceny byly přepočítány aktuálním kurzem.");
      $pros = $this->model->getProductsModel();
      $pros->cacheClean();
      $this->redirect("default");
    }  
  }

  public function renderEdit($id) {
    $form = $this['productEditForm'];
    $dataRow = false;
    $this->template->subItems = array();
    if (!$form->isSubmitted()) {
      $product = $this->model->getProductsModel();
      if ($id > 0) {
        $dataRow = dibi::fetch("SELECT * FROM products WHERE proid=%i", $id);
        if (!$dataRow) throw new Nette\Application\BadRequestException('Záznam nenalezen');
        //pokud je promasid > 0 doplnim kod
        if ($dataRow->promasid > 0) {
          $dataRow->mastercode = dibi::fetchSingle("SELECT procode FROM products WHERE proid=%i", $dataRow->promasid);
        }

        //naformatuji datum pokud je vyplněno
        if (!empty($dataRow->propromodateto)) {
          $dataRow->propromodateto = $this->formatDate($dataRow->propromodateto);
        }

        $form->setDefaults($dataRow);
        $this->template->subItems = dibi::fetchAll("SELECT * FROM products WHERE promasid=%i", $id);
        $this->template->enum_prostatus = $product->getEnumProStatus();
        $this->template->enum_proaccess = $product->getEnumProAccess();
      } else {
        //$arr["procode"] = (int)dibi::fetchSingle("SELECT MAX(proid)+1 FROM products");
        //$form->setDefaults($arr);
      }
      $this->template->id = $id;
    }
    $this->template->dataRow = $dataRow;

  }

  public function actionDeleteParam($proid, $parid) {
    if ($parid > 0) {
      $proParam = $this->model->getProParamsModel();
      $proParam->delete($parid);
      $this->flashMessage('Parametr byl vymazán');
    }
    $this->redirect('Product:edit#tabs_param', $proid);
  }

  public function actionRecalcSaleStat() {
    $products = $this->model->getProductsModel();
    $products->recalcSaleStat();
    $this->flashMessage("Statistika prodejnosti byla přepočítána");
    $this->redirect('Admin:default');
  }

  public function actionDeleteImage($proid, $filename, $path="") {
    //vymazu jen pokud neni stejny obrazek pouzit u jinych produktu
    $fileBody = dibi::fetchSingle("SELECT propicname FROM products where proid=%i", $proid);
    //$cnt = dibi::fetchSingle("SELECT COUNT(*) FROM products where propicname=%s", $fileBody, " AND proid!=%i", $proid);
    //if ($cnt == 0) {
      $this->deletePic(WWW_DIR . '/pic/product/', $filename, TRUE);
    //} else {
    //  $this->flashMessage("Obrázek nejde vymazat, je použit u jiných produktů. Pokud nechcete sdílet obrázky s jiným produktem, zadejte jiný Název obrázku:", 'err');
    //}
    $this->redirect('Product:edit#tabs_pic', $proid);
  }

  public function actionDelete($proid) {
    $products = $this->model->getProductsModel();
    $pro = $products->load($proid);
    $products->delete($proid);
    //vymažu z mall
    $config = $this->config;
    $config["neonParameters"] = $this->neonParameters;
    $mallApi = new \MallApi($config, $this->model);
    $mallApi->deleteProduct($pro->procode);
    $this->redirect('Product:default');
  }

  public function actionDeleteFile($ataid, $proid) {
    $file = dibi::fetch("SELECT * FROM attachments WHERE ataid=%i", $ataid);
    if ($file) {
      dibi::query("DELETE FROM attachments WHERE ataid=%i", $ataid);

      //soubor vymazu jen pokud neexstuje v jine priloze
      $filesCnt = (int)dibi::fetchSingle("SELECT COUNT(*) FROM attachments WHERE atafilename=%s", $file->atafilename);
      if ($filesCnt == 0) @unlink(WWW_DIR.'/files/'.$file->atafilename);
    }
    $this->redirect('Product:edit#tabs_attachment', $proid);
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sCode = Null;
        $this->sCode2 = Null;
        $this->sCodeP = Null;
        $this->sCodePEmpty = FALSE;
        $this->sCode2Empty = FALSE;
        $this->sWithoutVariants = FALSE;
        $this->sPaginationOff = FALSE;
        $this->sName = Null;
        $this->sCatId = Null;
        $this->sManId = Null;
        $this->sTypId = Null;
        $this->sTypId2 = Null;
        $this->sTypId3 = Null;
        $this->sTypId4 = Null;
        $this->sTypId5 = Null;
        $this->sTypId5 = Null;
        $this->sPrioritize = NULL;
        $this->sOrderBy = Null;
        $this->sOrderByType = Null;
      } else {
        $vals = $form->getValues();
        $this->sCode = $vals["code"];
        $this->sCode2 = $vals["code2"];
        $this->sCodeP = $vals["codep"];
        $this->sCodePEmpty = $vals["codepempty"];
        $this->sCode2Empty = $vals["code2empty"];
        $this->sWithoutVariants = $vals["withoutvariants"];
        $this->sPaginationOff = $vals["paginationoff"];
        $this->sName = $vals["name"];
        $this->sCatId = $vals["catid"];
        $this->sManId = $vals["manid"];

        $this->sTypId = $vals["typid"];
        $this->sTypId2 = $vals["typid2"];
        $this->sTypId3 = $vals["typid3"];
        $this->sTypId4 = $vals["typid4"];
        $this->sTypId5 = $vals["typid5"];
        $this->sPrioritize = $vals["prioritize"];

        $this->sStatus = $vals["status"];

        $this->sOrderBy = $vals["orderby"];
        $this->sOrderByType = $vals["orderbytype"];
      }
    }
    $this->redirect("Product:default");
  }

  /********************* facilities *********************/

  protected function createComponentListEditForm() {
    $dataSource = $this->getDataSource();
    $paginator = $this['paginator']->getPaginator();
    $paginator->itemsPerPage = ($this->sPaginationOff ? 10000 : $this->config["ADMIN_ROWSCNT"]);
    $paginator->itemCount = $dataSource->count();
    $dataRows = dibi::fetchAll($this->getDataSourceSql() . " LIMIT $paginator->itemsPerPage OFFSET $paginator->offset");
    //$dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $products = $this->model->getProductsModel();
    $form = new Nette\Application\UI\Form();
    foreach ($dataRows as $row) {
      $cont = $form->addContainer($row->proid);
      $cont->addSelect('proaccess', '', $products->getEnumProAccess())
        ->setDefaultValue($row->proaccess);
      $cont->addText('procode2', '', 10);
      $cont->addText('procodep', '', 10);
      $cont->addText('proorder', '', 3)
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "hodnota musí být číslo");
              
      $cont->addCheckbox('prostatuschange')
        ->setDefaultValue(FALSE);
      $cont->addHidden('prostatus')
        ->setDefaultValue($row->prostatus);
    }
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = [$this, 'productListEditFormSubmitted'];
    return $form;
  }

  protected function createComponentProductEditForm() {
    $id = (int)$this->getParameter('id');

    $product = $this->model->getProductsModel();
    if ($id > 0) $dataRow = $product->load($id);

    $form = new Nette\Application\UI\Form();

    $form->addGroup()->setOption('container', Nette\Utils\Html::el('div')->id('tabs_editmain'));
    $form->addText('procode', 'Katalogové číslo:', 30);
    $form->addText('procode2', 'EAN:', 30);
    $form->addText('procodep', 'Kód Pohoda:', 30);

    $form->addText('mastercode', 'Kód nadřízené položky:', 30);

    $form->addText('proname', 'Název:', 100)
      ->addRule(Nette\Forms\Form::FILLED, '%label: je nutné vyplnit');

    $form->addText('proname2', 'Název 2:', 100);  
    
    $form->addText('pronames', 'Název Heuréka:', 100);

    $form->addText('pronamecz', 'Název česky:', 100);

    $form->addText('progift', 'Dárek textově:', 100);    

    $form->addText('progifts', 'Kód zboží - dárku:', 100)
      ->setOption('description', 'bez přípony, stejně se musí jmenovat obrázek');

    $form->addSelect('promanid', 'Výrobce:', $product->getEnumProManId(FALSE))
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit')
      ->setPrompt('Vyberte výrobce ... ');

    if ($id > 0) {
     $form["promanid"]->setOption('description', Nette\Utils\Html::el('')->setHtml('[ <a href="'.$this->link('Manufacturer:edit', $dataRow->promanid).'">upravit<a/> ]'));
    }

    //nastaveni popisku cen
    $labelTypId = 'Akce';
    if (isset($this->neonParameters["labels"]["protypid"])) $labelTypId = $this->neonParameters["labels"]["protypid"];
    $labelTypId2 = 'Novinka';
    if (isset($this->neonParameters["labels"]["protypid2"])) $labelTypId2 = $this->neonParameters["labels"]["protypid2"];
    $labelTypId3 = 'Tip';
    if (isset($this->neonParameters["labels"]["protypid3"])) $labelTypId3 = $this->neonParameters["labels"]["protypid3"];
    $labelTypId4 = 'Nejprodávanější';
    if (isset($this->neonParameters["labels"]["protypid4"])) $labelTypId4 = $this->neonParameters["labels"]["protypid4"];
    $labelTypId5 = 'Připravujeme';
    if (isset($this->neonParameters["labels"]["protypid5"])) $labelTypId5 = $this->neonParameters["labels"]["protypid5"];
    
    
    $form->addCheckbox('protypid', $labelTypId);
    $form->addCheckbox('protypid2', $labelTypId2);
    //novym polozka nastavim priznak novinka
     if ($id == 0) $form["protypid2"]->setDefaultValue(TRUE);

    $form->addCheckbox('protypid3', $labelTypId3);
    $form->addCheckbox('protypid4', $labelTypId4);
    $form->addCheckbox('protypid5', $labelTypId5);

    $form->addCheckbox('proprioritize', "Upřednostnit v hledání");

    $form->addText('propromodateto', 'Zlaté dny do:', 20);

    $form->addText('prorating', 'Hodnocení produktu:', 20);
    $form->addText('proratingall', 'Celkový dojem:', 20);
    $form->addText('proratinging', 'Hodnocení složení:', 20);
    $form->addText('proratingmis', 'Hodnocení rozmíchatelnost:', 20);
    $form->addText('proratingdig', 'Hodnocení stravitelnost:', 20);
    $form->addText('proratingtas', 'Hodnocení chuť:', 20);

    $form->addTextArea('prodescs', 'Krátký popis:', 100, 4);
    
    //nastaveni popisku cen
    $labelCom = 'Cena běžná';
    if (isset($this->neonParameters["labels"]["com"])) $labelCom = $this->neonParameters["labels"]["com"];
    $labelA = 'Cena A';
    if (isset($this->neonParameters["labels"]["a"])) $labelA = $this->neonParameters["labels"]["a"];
    $labelB = 'Cena B';
    if (isset($this->neonParameters["labels"]["b"])) $labelB = $this->neonParameters["labels"]["b"];
    $labelC = 'Cena C';
    if (isset($this->neonParameters["labels"]["c"])) $labelC = $this->neonParameters["labels"]["c"];
    $labelD = 'Cena D';
    if (isset($this->neonParameters["labels"]["d"])) $labelD = $this->neonParameters["labels"]["d"];
    $labelE = 'Cena E';
    if (isset($this->neonParameters["labels"]["e"])) $labelE = $this->neonParameters["labels"]["e"];
    $labelF = 'Cena F';
    if (isset($this->neonParameters["labels"]["f"])) $labelF = $this->neonParameters["labels"]["f"];
    
    $form->addText('proprice1com', $labelCom.' v '.$this->curCodes[1].':', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");
    
    $form->addText('proprice1a', $labelA.' v '.$this->curCodes[1].':', 15)
      ->addRule(Nette\Forms\Form::FILLED, '%label: je nutné vyplnit.')
      ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('proprice1b', $labelB.' v '.$this->curCodes[1].':', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('proprice1d', $labelD.' v '.$this->curCodes[1].':', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('proprice1c', $labelC.' v '.$this->curCodes[1].':', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('proprice1e', $labelE.' v '.$this->curCodes[1].':', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('proprice1f', $labelF.' v '.$this->curCodes[1].':', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    if ($this->secondCurrency) {    
      $form->addText('proprice2com', $labelCom.' v '.$this->curCodes[2].':', 15)->setDisabled();
      $form->addText('proprice2a', $labelA.' v '.$this->curCodes[2].':', 15)->setDisabled();
      $form->addText('proprice2b', $labelB.' v '.$this->curCodes[2].':', 15)->setDisabled();
      $form->addText('proprice2d', $labelD.' v '.$this->curCodes[2].':', 15)->setDisabled();
      $form->addText('proprice2c', $labelC.' v '.$this->curCodes[2].':', 15)->setDisabled();
      $form->addText('proprice2e', $labelE.' v '.$this->curCodes[2].':', 15)->setDisabled();
      $form->addText('proprice2f', $labelF.' v '.$this->curCodes[2].':', 15)->setDisabled();
    }

    $form->addText('procpcheureka', 'Cena za klik Heureka:', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");
    $form->addText('procpczbozi', 'Cena za klik zbozi.cz:', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");    
        
    $form->addCheckbox("pronotdisc", "Nezapočítávat do slevy");
    $form->addCheckbox("proisexp", "Expirační položka");
    $form->addCheckbox("prodelfree", "Doprava zdarma");
    $form->addCheckbox("copyprice", "Kopírovat cenu a parametry do podřízených položek")
      ->setDefaultValue(TRUE);

    $form->addCheckbox("progoogleoff", "Neposílat do Google nákupy");
    $form->addCheckbox("proheurekaoff", "Vyřadit z Heureka košíku");
    $form->addCheckbox("pronoindex", "Neindexovat fulltextovým vyhledávačem");
    $form->addCheckbox("promallblock", "Neposílat do Mall");

    $form->addSelect('proforid', 'Forma:', $product->getEnumProForId())
      ->setPrompt("");  
      
    $form->addSelect('provatid', 'Sazba DPH:', $product->getEnumProVatId());

    /*
    $form->addText('procredit', 'Kredity:', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\FormNUMERIC, "Kredity: hodnota musí být číslo");

    $form->addText('prorecycle', 'Recyklační poplatek:', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\FormFLOAT, "Recylační poplatek: hodnota musí být číslo");
    */

    $form->addSelect('proaccess', 'Dostupnost:', $product->getEnumProAccess())
      ->setPrompt("Vyberte ...")
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit.');

    $form->addText('proaccesstext', 'Dostupnost textově:', 30); 
    
    $form->addText('proqty', 'Kusů skladem (celkem):', 3);
    $form->addText('proqty_store', 'Kusů skladem (sklad):', 3);
    $form->addText('proqty_shop1', 'Kusů skladem (Praha 10):', 3);
    $form->addText('proqty_shop2', 'Kusů skladem (Praha 2):', 3);

    $form->addSelect('proorigin', 'Distribuce:', $product->getEnumProOrigin())
      ->setPrompt("Vyberte ...");


    $form->addText('proorder', 'Pořadí:', 30)
      ->setOption('description', 'Číslo podle kterého je možno řadit zboží v katalogu');

    $form->addText('proweight', 'Hmotnost:', 15)
      ->setOption('description', 'Kg')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "Hodnota musí být číslo");

    $form->addText('provolume', 'Velikost balení:', 15)
      ->setOption('description', 'vyplňte jen číslo')
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit.')
      ->addRule(Nette\Forms\Form::FLOAT, "Hodnota musí být číslo");

    $form->addSelect('provolumeunit', 'Jednotka:', $product->getEnumProVolumeUnits())
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit.')
      ->setPrompt("");

    $form->addSelect('prostatus', 'Status:', $product->getEnumProStatus());
    
    $form->addTextArea('pronoteint', 'Interní poznámka:', 100, 4);

    $form->addTextArea('promalllabels', 'Mall štítky:', 100, 3)
      ->setOption('description', 'Zadejte ve tvaru STITEK2;1.12.2020;31.12.2020 co řádek, jeden štítek.');

    $form->addSubmit('tabs_editmain', 'Uložit')->getControlPrototype()->class('default');

    //zarazeni do katalogu
    $group = $form->addGroup("")->setOption('container', Nette\Utils\Html::el('div')->id('tabs_editcatalog'));
    $cnt = 0;
    $cat = $this->model->getCatalogsModel();
    if ($id > 0) {
      //nactu aktualni zarazeni s chkboxem - korenove urovne
      $result = dibi::query("SELECT catid, capid, catname, catpath FROM catalogs INNER JOIN catplaces ON (capcatid=catid) WHERE capproid=$id");
      foreach ($result as $n => $row) {
        $cnt++;
        $form->addCheckbox("cat_".$row["catid"], ' '.str_replace('|', ' > ', $row["catpath"]))
          ->setDefaultValue(true);
      }
    }
    $form->addSelect('cat_0', '', $cat->getEnumCatalogCombo(0))
      ->setPrompt('Přiřadit další druh výrobku ...');
    if ($cnt === 0) {
      $group->setOption('description', 'Zboží zatím není zařazeno v katalogu!');
    } else {
      $group->setOption('description', 'Aktuální zařazení');
    }



    $form->addSubmit('tabs_editcatalog', 'Uložit')->getControlPrototype()->class('default');

    //detailni popis
    $form->addGroup()->setOption('container', Nette\Utils\Html::el('div')->id('tabs_editdesc'));

    $form->addTextArea('prodesc', 'Dlouhý popis', 130, 25);
    $form['prodesc']->getControlPrototype()->class('mceEditor');

    $form->addTextArea('pronutrition', 'Nutriční informace', 130, 25);
    $form['pronutrition']->getControlPrototype()->class('mceEditor');

    $form->addTextArea('prodescab', 'Popis pro cenu eshop a VIP', 130, 3);
    $form['prodescab']->getControlPrototype()->class('mceEditor');

    $form->addTextArea('prodesccd', 'Popis pro cenu A,B a C', 130, 3);
    $form['prodesccd']->getControlPrototype()->class('mceEditor');
    
    $form->addSubmit('tabs_editdesc', 'Uložit')->getControlPrototype()->class('default');

    //SEO
    $form->addGroup()->setOption('container', Nette\Utils\Html::el('div')->id('tabs_seo'));
    $form->addText('prokey', 'URL klíč:', 100)
      ->setOption('description', 'Pokud ponecháte prázdné, generuje se z názvu zboží');

    $form->addText('protitle', 'Title:', 100)
      ->setOption('description', 'Zadávejte pokud chcete jiné TITLE nez je název zboží');

    $form->addText('prokeywords', 'Klíčové slova:', 100)
      ->setOption('description', 'Zadávejte výčet klíčových slov, které nejsou v názvu zboží oddělený čárkou');

    $form->addTextArea('prodescription', 'Description', 100, 3)
      ->setAttribute('maxlength', '255')
      ->setOption('description', Nette\Utils\Html::el('p')->class('charsRemaining'));

    $form->addSubmit('tabs_seo', 'Uložit')->getControlPrototype()->class('default');

    //ostatni udaje
    $form->addGroup()->setOption('container', Nette\Utils\Html::el('div')->id('tabs_editrem'));
    /*
    $form->addCheckbox('probigsize', 'Nadrozměrné zboží');
    $form->addCheckbox('prooffer', 'Poptávkové zboží');
    */
    $form->addText('provideo', 'ID Youtube.com videa:', 100);
    
    $form->addText('proico', 'Ikona ke zboží:', 100);

    $form->addText('prooptionskeywords', 'Související zboží:', 100)
      ->setOption('description', 'Zadávejte začátky názvu souvisejících položek oddělené čárkou');
    /*
    $form->addText('prooptions', 'Alternativní zboží:', 100)
      ->setOption('description', 'Zadávejte kódy zboží oddělené svislítkem');
    */
    $form->addText('prowarranty', 'Záruka:', 30)
      ->setOption('description', 'Textově délka záruky, např. "24 mesíců"');
    /*
    $form->addText('protrarestr', 'Omezení dopravy:', 30)
      ->setOption('description', 'Zadávejte výčet ID doprav oddělený čářkou, které nejsou vhodné pro toto zboží');
    */
    $form->addSubmit('tabs_editrem', 'Uložit')->getControlPrototype()->class('default');

    //obrazky
    $emptypos = 1;
    if ($id > 0) {
      //nactu seznam obrazku
      $images = "";
      $baseUri = (string)$this->getHttpRequest()->url->baseUrl;
      for($piccnt=0;$piccnt<=10;$piccnt++){
        if ($piccnt == 0) {
          $title = "Hlavní obrázek";
          $filename = $dataRow->propicname.'.jpg';
        }  else {
          $title = $piccnt.". pozice";
          $filename = $dataRow->propicname.'_'.$piccnt.'.jpg';
        }

        if (file_exists(WWW_DIR.'/pic/product/detail/'.$filename)) {
          $images .= '
  <img title="'.$title.'" src="'.$baseUri.'/pic/product/detail/'.$filename.'?'.time().'" width="200px" />';
          if ($piccnt != 0) $images .= '<a href="'.$this->link('deleteImage', $id, $filename).'"><img src="'.$baseUri.'/admin/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat '.$title.'" /></a>';
        } else {
          if (empty($emptypos)) $emptypos = $piccnt;
        }
      }
      //obrazek usrsize
      $filename = $dataRow->propicnamevar.'.jpg';
      if (file_exists(WWW_DIR.'/pic/product/usrsize/'.$filename)) {
          $images .= '
  <img title="Obrázek pro usrsize" src="'.$baseUri.'/pic/product/usrsize/'.$filename.'?'.time().'" />';
          $images .= '<a href="'.$this->link('deleteImage', $id, $filename, 'usrsize').'"><img src="'.$baseUri.'/admin/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat obrázek pro usrsize" /></a>';
      }

      //obrazek gift
      $filename = $dataRow->propicname.'.png';
      if (file_exists(WWW_DIR.'/pic/product/gift/'.$filename)) {
          $images .= '
  <img title="Obrázek pro dárek" src="'.$baseUri.'/pic/product/gift/'.$filename.'?'.time().'" />';
          $images .= '<a href="'.$this->link('deleteImage', $id, $filename, 'gift').'"><img src="'.$baseUri.'/admin/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat obrázek pro dárek" /></a>';
      }

      /*
      //obrazek slider
      $filename = $dataRow->propicname.'.jpg';
      if (file_exists(WWW_DIR.'/pic/product/slider/'.$filename)) {
          $images .= '
  <img title="Obrázek pro slider" src="'.$baseUri.'/pic/product/slider/'.$filename.'?'.time().'" />';
          $images .= '<a href="'.$this->link('deleteImage', $id, $filename, 'slider').'"><img src="'.$baseUri.'/admin/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat obrázek pro slider" /></a>';
      }
      */

    }
    if (isset($images)) {
      $form->addGroup()
        ->setOption('container', Nette\Utils\Html::el('div')->id('tabs_pic'))
        ->setOption('description', Nette\Utils\Html::el('div')->setHtml($images));
    } else {
      $form->addGroup()
        ->setOption('container', Nette\Utils\Html::el('div')->id('tabs_pic'));
    }

    $form->addText('propicname', 'Název obrázku:', 30)
      ->addRule(Nette\Forms\Form::PATTERN, 'Název obrázku: můžete vyplnit pouze malá a velká písmena, pomlčku a podtržítko', '^[a-zA-Z0-9_ .-]*$')
      ->setRequired(FALSE)
      ->setOption('description', '.jpg');

    $form->addText('propicnamevar', 'Název obrázku varianty:', 30)
      ->addRule(Nette\Forms\Form::PATTERN, 'Název obrázku: můžete vyplnit pouze malá a velká písmena, pomlčku a podtržítko', '^[a-zA-Z0-9_ .-]*$')
      ->setRequired(FALSE)
      ->setOption('description', '.jpg');

    //obrazek hlavni
    $form->addUpload('pic0', 'Hlavní obrázek:')
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete. Je třeba dodržet poměr stran obrázku jinak bude obrázek oříznutý.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');
    /*
    //obrazek slider
    $form->addUpload('pics', 'Obrázek pro slider:')
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete. Je třeba dodržet poměr stran obrázku 200x284px jinak bude obrázek oříznutý.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');
    */
    //obrazek dalsi
    $form->addUpload('pic1', 'Další obrázek:')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');

    for($i=1;$i<=10;$i++) $arri[$i]=$i;
    $form->addSelect('pic1position', 'Pozice obrázku:', $arri)
      ->setDefaultValue($emptypos)
      ->setOption('description', 'Nahráním nového obrázku na obsazenou pozici ten původní přepíšete. Je třeba dodržet poměr stran obrázku jinak bude obrázek oříznutý.') ;

    $form->addUpload('pic_usrsize', 'Obrázek "usrsize":')
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete. Je třeba dodržet poměr stran obrázku 40x40px jinak bude obrázek oříznutý.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');

    $form->addUpload('pic_gift', 'Obrázek dárek:')
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete.Obrázek se nahraje tak jak je bez úprav.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIME_TYPE, 'Povolené typy souborů jsou pouze .png.', 'image/png');

    $form->addSubmit('tabs_pic', 'Uložit')->getControlPrototype()->class('default');

    //prilohy
    $attachments = "";
    if ($id > 0) {
      //nactu aktualni prilohy
      $atts = dibi::fetchAll("SELECT * FROM attachments WHERE ataproid=%i", $id);
      foreach ($atts as $row) {
        $attachments .= '
  <a href="'.$baseUri.'/files/'.$row["atafilename"].'?'.time().'">'.$row["ataname"].'</a> <a href="'.$this->link('deleteFile', $row["ataid"], $id).'"><img src="'.$baseUri.'/admin/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat '.$row["ataname"].'" /></a><br />';
      }
    }
    if (isset($attachments)) {
      $form->addGroup()
        ->setOption('container', Nette\Utils\Html::el('div')->id('tabs_attachment'))
        ->setOption('description', Nette\Utils\Html::el('div')->setHtml($attachments));
    } else {
      $form->addGroup()
        ->setOption('container', Nette\Utils\Html::el('div')->id('tabs_attachment'));
    }
    //nova priloha
    $form->addUpload('attAdd', 'Nová příloha:');
    $form->addText('ataname', 'Název přílohy:', 100)
      ->addConditionOn($form["attAdd"],Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Vyplňte název přílohy');

    $form->addSubmit('tabs_attachment', 'Uložit')->getControlPrototype()->class('default');

    //parametry zbozi
    $form->addGroup()->setOption('container', Nette\Utils\Html::el('div')->id('tabs_param'));

    //stavajici parametry
    $pars = $this->model->getProParamsModel();
    //$paramNames = $pars->getEnumParamNames();
    $paramVals = $pars->getEnumParamVals();

    //novy parametr
    $form->addText('param_name_0', 'Nový parametr - název:', 80);
    $form->addText('param_value_0', 'Nový parametr - hodnota:', 80)
      ->addConditionOn($form["param_name_0"], Nette\Forms\Form::FILLED);
        //->addRule(Nette\Forms\Form::FILLED, 'Nový parametr - hodnota: je nutné vyplnit.');
    $form->addHidden('param_typid_0', 0);

    $form->addSelect('param_name_m0', 'Nový parametr MALL - název:', $product->getEnumMallParNames())
      ->setPrompt('');
    $form->addHidden('param_value_m0', '');
    $form->addHidden('param_typid_m0', 1);

    $rows = array();
    if ($id > 0) {
      $rows = dibi::fetchAll("SELECT * FROM proparams WHERE prpproid=$id ORDER BY prptypid, prpname");
      $malParNames = $product->getEnumMallParNames();

      foreach ($rows as $row) {
        $parName = ($row->prptypid === 1 ? "MALL: ".$malParNames[$row->prpname] : $row->prpname);
        $values = array();
        if ($row->prptypid === 1) {
          $values = $product->getEnumMallParValuesByParamName($row->prpname);
        }

        $form->addHidden('param_name_'.$row->prpid)
          ->setDefaultValue($row->prpname);

        $form->addHidden('param_typid_'.$row->prpid)
          ->setDefaultValue($row->prptypid);

        if ($row->prptypid === 1 && count($values) > 0) {
          $form->addSelect('param_value_'.$row->prpid, $parName.":", $values)
            ->setPrompt("Vyberte ...")
            ->setOption('description', Nette\Utils\Html::el('span')->setHtml('<a href="'.$this->link('deleteParam', $id, $row->prpid).'"><img src="'.$baseUri.'/admin/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat" /></a>'))
            ->addConditionOn($form['param_name_'.$row->prpid], Nette\Forms\Form::FILLED);
            //->addRule(Nette\Forms\Form::FILLED, 'Parametr - hodnota: je nutné vyplnit.');

          if (isset($values[$row->prpvalue])) {
            $form['param_value_'.$row->prpid]->setDefaultValue($row->prpvalue);
          }
        } else {
          $form->addText('param_value_'.$row->prpid, $parName.":", 80)
            ->setOption('description', Nette\Utils\Html::el('span')->setHtml('<a href="'.$this->link('deleteParam', $id, $row->prpid).'"><img src="'.$baseUri.'/admin/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat" /></a>'))
            ->setDefaultValue($row->prpvalue)
            ->addConditionOn($form['param_name_'.$row->prpid], Nette\Forms\Form::FILLED);
            //->addRule(Nette\Forms\Form::FILLED, 'Parametr - hodnota: je nutné vyplnit.');
        }
      }
    }
    if (count($rows) == 0) {
      //nema zadne parametry predplnim vsechny parametry
      $paramNames = $pars->getEnumParamNames();
      $cnt = 0;
      foreach ($paramNames as $key => $name) {
        $cnt++;
        $form->addHidden('param_typid_N'.$cnt, 0);
        $form->addHidden('param_name_N'.$cnt, $name);
        $form->addText('param_value_N'.$cnt, $name.":");
      }
    }

    $form->addSubmit('tabs_param', 'Uložit')->getControlPrototype()->class('default');

    $form->addGroup()->setOption('container', Nette\Utils\Html::el('div')->id('save_button'));
    $form->addSubmit('save', 'Uložit a vrátit se na seznam')->getControlPrototype()->class('default');
    if ($id > 0) $form->addSubmit('saveAsNew', 'Uložit jako novou položku')->getControlPrototype()->class('default');

    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'productEditFormSubmitted');

    return $form;
  }

  protected function createComponentSearchForm() {
    $products = $this->model->getProductsModel();
    $catalogs = $this->model->getCatalogsModel();

    $form = new Nette\Application\UI\Form();
    $form->getElementPrototype()->novalidate = 'novalidate';
    $form->addGroup("Vyhledávání");
    $form->addText("code", "Katalogové č.", 10)
      ->setDefaultValue($this->sCode);
    $form->addText("code2", "EAN", 10)
      ->setDefaultValue($this->sCode2);
    $form->addText("codep", "Kód Pohoda", 10)
      ->setDefaultValue($this->sCodeP);
    $form->addText("name", "Název zboží", 10)
      ->setDefaultValue($this->sName);
    
    $form->addSelect("catid", "Katalog", $catalogs->getEnumCatalogCombo());
    if (!empty($this->sCatId)) $form["catid"]->setDefaultValue($this->sCatId);


    //nastaveni popisku cen
    $labelTypId = 'Akce';
    if (isset($this->neonParameters["labels"]["protypid"])) $labelTypId = $this->neonParameters["labels"]["protypid"];
    $labelTypId2 = 'Novinka';
    if (isset($this->neonParameters["labels"]["protypid2"])) $labelTypId2 = $this->neonParameters["labels"]["protypid2"];
    $labelTypId3 = 'Tip';
    if (isset($this->neonParameters["labels"]["protypid3"])) $labelTypId3 = $this->neonParameters["labels"]["protypid3"];
    $labelTypId4 = 'Nejprodávanější';
    if (isset($this->neonParameters["labels"]["protypid4"])) $labelTypId4 = $this->neonParameters["labels"]["protypid4"];
    $labelTypId5 = 'Připravujeme';
    if (isset($this->neonParameters["labels"]["protypid5"])) $labelTypId5 = $this->neonParameters["labels"]["protypid5"];
    
    $form->addCheckbox("typid", $labelTypId)
      ->setDefaultValue($this->sTypId);

    $form->addCheckbox("typid2", $labelTypId2)
      ->setDefaultValue($this->sTypId2);

    $form->addCheckbox("typid3", $labelTypId3)
      ->setDefaultValue($this->sTypId3);

    $form->addCheckbox("typid4", $labelTypId4)
      ->setDefaultValue($this->sTypId4);

    $form->addCheckbox("typid5", $labelTypId5)
      ->setDefaultValue($this->sTypId5);

    $form->addCheckbox("prioritize", "Upřednostněno")
      ->setDefaultValue($this->sPrioritize);

    $form->addCheckbox("codepempty", "Chybí kód Pohoda")
      ->setDefaultValue($this->sCodePEmpty);

    $form->addCheckbox("code2empty", "Chybí EAN")
      ->setDefaultValue($this->sCode2Empty);

    $form->addCheckbox("withoutvariants", "Bez variant")
      ->setDefaultValue($this->sWithoutVariants);

    $form->addCheckbox("paginationoff", "Vypnout stránkování")
      ->setDefaultValue($this->sPaginationOff);

    $arr = array(
      '-1'=>'Nefiltrovat',
      '0'=>'Aktivní',
      '1'=>'Blokované',
    );

    $form->addSelect("status", "Status", $arr)
      ->setDefaultValue($this->sStatus);

    $form->addSelect("manid", "Výrobce", $products->getEnumProManId())
      ->setPrompt("");
    if (!empty($this->sManId)) $form["manid"]->setDefaultValue($this->sManId);  

    $arr = array(
      'pro.procode'=>'Katalogové číslo',
      'pro.proname'=>'Název zboží',
      'pro.procode2'=>'EAN',
      'prscnt'=>'Prodejnosti',
      'pro.proorder'=>'Pořadí',
    );
    $form->addSelect("orderby", "Řadit podle", $arr)
      ->setDefaultValue($this->sOrderBy);

    $arr = array(
      'ASC'=>'Vzestupně',
      'DESC'=>'Sestupně',
    );
    $form->addSelect("orderbytype", "Typ řazení", $arr)
      ->setDefaultValue($this->sOrderByType);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  protected function createComponentProductBatchUpdateForm() {
    $catalogs = $this->model->getCatalogsModel();

    $form = new Nette\Application\UI\Form();
    $form->addSelect("catid", "Katalog", $catalogs->addEnumCatalogRootLevel())
      ->addRule(Nette\Forms\Form::FILLED, 'Zařazení do katalogu musí být vyplněno');
    $form->addSelect("prcid", "Cenová hladina", array('a'=>'cena A', 'b'=>'cena B','c'=>'cena C','d'=>'cena D','e'=>'cena E'))
      ->addRule(Nette\Forms\Form::FILLED, 'Cenová hladina musí být vyplněna');

    $form->addText("proprice", "Nová cena")
      ->addRule(Nette\Forms\Form::FILLED, 'Nová cena musí být vyplněna');
    $form->addSubmit('update', 'Aktualizovat');
    $form->onSuccess[] = array($this, 'productBatchUpdateFormSubmitted');
    return $form;
  }
}