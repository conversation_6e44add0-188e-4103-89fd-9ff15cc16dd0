<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;
  
final class Mall<PERSON>alogPresenter extends BasePresenter {

  public function renderDefault() {
    $dataRows = dibi::fetchAll('
    SELECT *
    FROM mallcatalogs
    ORDER BY macname DESC');

    $this->template->rows = $dataRows;
  }

  public function renderEdit($id) {

    $form = $this['editForm'];

    if (!$form->isSubmitted() && $id > 0) {
      $macs = $this->model->getMallcatalogsModel();
      $row = $macs->load($id);
      if (!$row) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }
      $form->setDefaults($row);

      $this->template->dataRow = $row;
    }
  }

  protected function createComponentEditForm() {
    $id = $this->getParameter('id');
    $form = new Nette\Application\UI\Form;

    $form->addText('maccode', 'ID kategorie:', 100)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('macname', 'Katalogová cesta:', 100)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addTextArea('macparams', 'Třídící podmínky a doplňující parametry:', 100, 5)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('default');

    $form->onSuccess[] = [$this, 'editFormSubmitted'];

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }

  public function editFormSubmitted(Nette\Application\UI\Form $form) {

    $id = (int)$this->getParameter('id');

    if ($form['save']->isSubmittedBy()) {
      $formVars = $form->getValues();
      $macs = $this->model->getMallcatalogsModel();

      if ($id > 0) {
        $macs->update($id, $formVars);
        $this->flashMessage('Údaje byly aktualizovány.');
      } else {
        $id = $macs->insert($formVars);
        $this->flashMessage('Záznam byl vytvořen.');
      }
    }
    $this->redirect('default');
  }
}
