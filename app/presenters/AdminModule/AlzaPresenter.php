<?php
namespace AdminModule;

use Nette,
  dibi,
  Model;
use Nette\Diagnostics\Debugger;
use PHPExcel;
use PHPExcel_IOFactory;
use XLSXWriter;
use const http\Client\Curl\Features\LIBZ;

final class AlzaPresenter extends BasePresenter {

  public function renderDefault() {

  }

  protected function createComponentExportForm() {

    $form = new Nette\Application\UI\Form();
    $catalogs = $this->model->getCatalogsModel();

    $arr = dibi::query("SELECT catid, catname FROM catalogs WHERE catlevel=2 AND catstatus=0 ORDER BY catorder")->fetchPairs('catid', 'catname');
    //$arr[0] = "Vše";
    $form->addSelect('catid', 'Katalog:', $arr)
      ->setPrompt("");

    $form->addSubmit('save', 'Exportovat');
    $form->onSuccess[] = array($this, 'exportSubmitted');
    return $form;
  }

  public function exportSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();

      $this->redirect("GetProductsListingData", ['catId' => $vals->catid]);

      $catName = '';

      if ($vals["catid"] > 0) {
        $catalog = dibi::fetch("SELECT * FROM catalogs WHERE catid=%i", $vals["catid"]);
        $catName .= $catalog->catname;
      }

      $spreadsheet = new Spreadsheet();
      $sheet = $spreadsheet->setActiveSheetIndex(0);
        $cols = array(
          'Katalogové číslo',
          'Kód Pohoda',
          'EAN',
          'Název výrobce',
          'Název',
          'Počet dní do dodání zboží, 0 - skladem',
          'běžná cena(doporučená výrobcem)',
          'Cena A(bez DPH)',
          'Cena B(bez DPH)',
          'Cena C(bez DPH)',
          'Cena D(bez DPH)',
          'Cena E(bez DPH)',
          'Sazba DPH (0-zakladní, 1-snížená)'
        );

        $style_head = array(
        'fill' => array(
            'type' => Fill::FILL_SOLID,
            'color' => array('rgb'=>'C0C0C0'),
        ),
        'font' => array(
            'bold' => true,
        )
      );

      foreach ($cols as $index => $value) {
        $sheet->setCellValueByColumnAndRow($index+1,1, $value);
        $sheet->getStyleByColumnAndRow($index+1,1)->applyFromArray( $style_head );
      }


      foreach ($rows as $key=>$row) {
        $rowIndex++;
        $colIndex = 1;

        $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $row->procode, DataType::TYPE_STRING);
        $colIndex ++;
        $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $row->procodep, DataType::TYPE_STRING);
        $colIndex ++;
        $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $row->procode2, DataType::TYPE_STRING);
        $colIndex ++;
        $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $row->manname, DataType::TYPE_STRING);
        $colIndex ++;
        $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, trim($row->proname.' '.$row->proname2), DataType::TYPE_STRING);
        $colIndex ++;
        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proaccess);
        $colIndex ++;
        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proprice1com);
        $colIndex ++;
        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proprice1a);
        $colIndex ++;
        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proprice1b);
        $colIndex ++;
        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proprice1c);
        $colIndex ++;
        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proprice1d);
        $colIndex ++;
        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proprice1e);
        $colIndex ++;
        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->provatid);
        $colIndex ++;
      }

      $sheet->setTitle('Export');

      $writer = new Xls($spreadsheet);
      $fName = TEMP_DIR. '/' .Nette\Utils\Strings::webalize($catName). '.xls';
      $writer->save($fName);
      if (file_exists($fName)) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename='.Nette\Utils\Strings::webalize($catName). '.xls');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($fName));
        //ob_clean();
        flush();
        readfile($fName);
      }
    }
  }

  public function actionGetProductsListingData($catId) {

    $paramDesc = \AlzaApi::getAlzaParamNames($catId);

    //načtu produkty z kategorie
    $categories = \AlzaApi::getImportCategories();

    $alzaCatId = $categories[$catId]["MAIN_CATEGORY_ID"];
    $alzaCatName = $categories[$catId]["PARAMETER_0005"];

    $mandatoryParams = [
      //aminokyseliny
      4 => [
        "Rozměry obalu (cm)" => ["PARAMETER_7152", "PARAMETER_7155", "PARAMETER_7151"],
        "Příchuť" => "PARAMETER_16214",
      ],
      2 => [
        "Rozměry obalu (cm)" => ["PARAMETER_7152", "PARAMETER_7151", "PARAMETER_7155"],
        "Příchuť" => "PARAMETER_16214",
      ],
      3 => [
        "Rozměry obalu (cm)" => ["PARAMETER_7152", "PARAMETER_7151", "PARAMETER_7155"],
        "Příchuť" => "PARAMETER_16214",
      ],
      7 => [
        "Rozměry obalu (cm)" => ["PARAMETER_7152", "PARAMETER_7151", "PARAMETER_7155"],
        "Příchuť" => "PARAMETER_16214",
      ],
      5 => [
        "Rozměry obalu (cm)" => ["PARAMETER_7152", "PARAMETER_7151", "PARAMETER_7155"],
        "Příchuť" => "PARAMETER_16214",
      ],
      8 => [
        "Rozměry obalu (cm)" => ["PARAMETER_7152", "PARAMETER_7151", "PARAMETER_7155"],
        "Příchuť" => "PARAMETER_16214",
      ],
    ];

    $xmlData = [];

    $params = $categories[$catId];

    //načtu produkty
    $sql = "
    SELECT proid, promasid, proismaster, provariants, procodep, procode2, provolume, provolumeunit, protypid, protypid2, protypid3, protypid4, protypid5, propromodateto, prokey, procode, proname AS proname1, concat(proname, ' ', proname2) AS proname, 
    proname2, pronames, prodescs, pronutrition, proaccess, proaccesstext, proqty, proqty_store, proqty_shop1, proqty_shop2, proprice1com AS propricecom, proisexp,
    propicname, propicnamevar, IF(proprice1a>0,proprice1a,proprice1a) AS proprice, proforid, procodep,proweight,catpath,prodesc,
    provatid, prodelfree, pronotdisc, prostatus, proorder, manname, prorating, proprice1a, IF(provatid=0, ".$this->config["VATTYPE_0"].", ".$this->config["VATTYPE_1"].") AS provat
    FROM products
    INNER JOIN catplaces ON (capproid=proid)
    INNER JOIN catalogs ON (capcatid=catid)
    LEFT JOIN manufacturers ON (promanid=manid)
    WHERE prostatus=0 AND catpathids LIKE '%|$catId|%'";


    $sqlQuery = $sql . " AND (proismaster=1 OR (promasid=0 AND proismaster=0))";
    $rows = dibi::query($sqlQuery)->fetchAssoc("proid");

    //die($sqlQuery);
    foreach ($rows as $row) {
      if ($row->proismaster > 0) {
        $variants = dibi::fetchAll($sql . " AND coalesce(procode2, '')!='' AND coalesce(procodep, '')!='' AND promasid=" . $row->proid);
        foreach ($variants as $variant) {

          $ret = $this->getProductData($params, $mandatoryParams[$catId], $variant, $row);
          if ($ret !== false) {
            $xmlData[$variant->procodep] = $variant;
            $xmlData[$variant->procodep]["params"] = $ret;
            $xmlData[$variant->procodep]["master"] = $row;
          }
        }
      } else {
        $ret = $this->getProductData($params, $mandatoryParams[$catId], $row, NULL);
        if ($ret !== false) {
          $xmlData[$row->procodep] = $row;
          $xmlData[$row->procodep]["params"] = $ret;
        }


      }
    }

    $this->template->rows = $xmlData;
    $this->template->paramDesc = $paramDesc;

    $check = (bool)$this->getParameter('check');
    if ($check) {
      $this->setView("productsListingXmlCheck");
    } else {

      /*
      $file_name = $alzaCatName . " (" . $alzaCatId . ").xls";
      header("Content-Disposition: attachment; filename=\"$file_name\"");
      header("Content-Type: application/vnd.ms-excel");
      */

      //To define column name in first row.
      $columnIdsSet = false;
      $columnNames = [];
      $alzaColNames = \AlzaApi::getAlzaParamNames($catId);
      // run loop through each row in $customers_data

      /*
      foreach($xmlData as $row) {
        if(!$columnIdsSet) {
          $columnIds = array_keys($row->params);
          foreach ($columnIds as $columnId) {
            if (isset($alzaColNames[$columnId])) {
              $columnNames[] = $alzaColNames[$columnId];
            } else {
              $columnNames[] = '';
            }
          }
          echo implode("\t", $columnIds) . "\n";
          echo implode("\t", $columnNames) . "\n";
          $columnIdsSet = true;
        }

        foreach ($row->params as $key => $value) {
          echo $this->fixData($value) . "\t";
        }
        echo "\n";
      }
      */
      $dataRows = [];
      foreach($xmlData as $row) {
        if(!$columnIdsSet) {
          $columnIds = array_keys($row->params);
          foreach ($columnIds as $columnId) {
            if (isset($alzaColNames[$columnId])) {
              $columnNames[] = $alzaColNames[$columnId];
            } else {
              $columnNames[] = '';
            }
          }

          $dataRows[] = $columnIds;
          $dataRows[] = $columnNames;
          $columnIdsSet = true;
        }

        $dataRows[] = $row->params;
      }

      require_once(LIBS_DIR . '/XLSXWriter/xlsxwriter.class.php');

      $writer = new XLSXWriter();
      $writer->writeSheet($dataRows);
      $file_name = $alzaCatName . " (" . $alzaCatId . ").xlsx";
      //$writer->writeToFile($file_name);

      header("Content-Disposition: attachment; filename=\"$file_name\"");
      header("Content-Type: application/vnd.ms-excel");

      $writer->writeToStdOut();

      $this->terminate();
    }

  }

  private function getProductData($defaultParams, $mandatoryParams, $product, $proMas) {

    $dataRow = $defaultParams;

    $dataRow["PARAMETER_0001"] = $this->getParameter0001($product, $proMas);
    $dataRow["PARAMETER_0002"] = $this->getProNameSeo($product);
    $dataRow["PARAMETER_0003"] = $this->getParameter0003($product, $proMas); //NameExt custom - doplněk
    $dataRow["PARAMETER_0004"] = $product->manname;
    $dataRow["PARAMETER_2015"] = (string)$product->procodep;
    $dataRow["PARAMETER_1933"] = (string)$product->procode2;
    $dataRow["PARAMETER_5135"] = $product->proprice;
    $dataRow["PARAMETER_7153"] = $this->getProWeightAll($product, $proMas); //váha včetně obalu
    if (!empty($product->pronutrition) && isset($dataRow["PARAMETER_12943"])) {
      $dataRow["PARAMETER_12943"] = $product->pronutrition; //složení
    }

    //hmotnost objem
    $dataRow["PARAMETER_16215"] = "";
    $dataRow["PARAMETER_16230"] = "";

    $weight = $this->getProWeight($product, $proMas);
    if ($weight > 0) {
      $dataRow["PARAMETER_16215"] = $weight;
    } else {
      $volume = $this->getProVolume($product, $proMas);
      if ($volume > 0) {
        $dataRow["PARAMETER_16230"] = $volume;
      }
    }

    //načtu parametry
    $proParams = dibi::fetchPairs("SELECT prpname, prpvalue FROM proparams WHERE prpproid=?", $product->proid);
    if ($proMas) {
      $proMasParams = dibi::fetchPairs("SELECT prpname, prpvalue FROM proparams WHERE prpproid=?", $proMas->proid);
      if ($proMasParams) {
        $proParams = $proParams + $proMasParams;
      }
    }

    foreach ($mandatoryParams as $parName => $alzaPar) {
      if (is_array($alzaPar)) {
        foreach ($alzaPar as $p) {
          $dataRow[$p] = "";
        }
      } else {
        $dataRow[$alzaPar] = "";
      }
      if (isset($proParams[$parName])) {
        if ($parName === 'Rozměry obalu (cm)') {
          $arr = explode("x", $proParams[$parName]);
          if (count($arr) === 3) {
            for ($i = 0; $i <= 2; $i++) {
              $dataRow[$alzaPar[$i]] = $arr[$i];
            }
          }
        } else {
          $dataRow[$alzaPar] = $proParams[$parName];
        }
      } else {
        if ($parName === 'Příchuť') {
          $dataRow[$alzaPar] = 'Bez příchuti';
        }
      }
    }

    if (isset($dataRow["PARAMETER_16232"])) {
      //zjistím jesti neexistují parametry
      $parVals = [];
      if (isset($proParams["BEFORE_ACTION"]) && $proParams["BEFORE_ACTION"] == 'YES') {
        $parVals[] = 'před výkonem';
      }
      if (isset($proParams["DURING_ACTION"]) && $proParams["DURING_ACTION"] == 'YES') {
        $parVals[] = 'při výkonu';
      }
      if (isset($proParams["AFTER_ACTION"]) && $proParams["AFTER_ACTION"] == 'YES') {
        $parVals[] = 'po výkonu';
      }
      if (count($parVals) > 0) {
        $dataRow["PARAMETER_16232"] = implode(',', $parVals);
      }
    }

    //obrázky
    $path = "pic/product/mall/";
    $picCnt = 0;


    $dataRow["PICTURE_01"] = "";
    $dataRow["PICTURE_02"] = "";
    $dataRow["PICTURE_03"] = "";
    $dataRow["PICTURE_04"] = "";
    $dataRow["PICTURE_05"] = "";

    for ($i = 0; $i <= 10; $i++) {
      if ($product->promasid == 0)  {
        $picName = (!empty($product->propicname) ? $product->propicname : $product->procode) . ($i === 0 ? "" : "_" . $i);
        if (file_exists(WWW_DIR . '/' . $path . $picName . '.jpg')) {
          $picCnt ++;
          $dataRow["PICTURE_0" . $picCnt] = "https://www.goldfitness.cz/" . $path . $picName . '.jpg';
        }
      } else  if ($proMas) {
        $picName = (!empty($proMas->propicname) ? $proMas->propicname : $proMas->procode) . ($i === 0 ? "" : "_" . $i);
        if (file_exists(WWW_DIR . '/' . $path . $picName . '.jpg')) {
          $picCnt ++;
          $dataRow["PICTURE_0" . $picCnt] = "https://www.goldfitness.cz/" . $path . $picName . '.jpg';
        }
      }
      if ($picCnt >= 5) {
        break;
      }
    }

    /*
    $miss = [];
    foreach ($dataRow as $key => $value) {
      if (empty($value)) {
        $miss[] = $key;
      }
    }
    if (count($miss) > 0) {
      \Tracy\Debugger::log("$product->proid;$product->procodep;" . implode("|", $miss));
    }
    */
    return $dataRow;
  }

  private static function getProNameSeo($product): string {
    $proname = "";
    $arr = explode('-', $product->proname2);
    $vol = "";
    if (!empty($arr[0]) && !empty($arr[1])) {
      $vol = ' '.trim($arr[0]);
    }
    if (!empty($product->pronames)) {
      $proname = $product->manname.' '.$product->pronames;
    } else {
      if ($product->promasid > 0) {
        $proname = $product->manname.' '.$product->proname.' '.$product->proname2;
      } else {
        $proname = $product->manname.' '.$product->proname.$vol;
      }
    }
    return $proname;
  }

  private function getParameter0001($product, $proMas): string {

    if ($proMas) {
      $product = $proMas;
    }

    $words = [];
    if (!empty($product->catpath)) {
      $arr = explode('|', $product->catpath);
      foreach ($arr as $i => $text) {
        if ($i > 0) {
          $words[] = $text;
        }
      }
    }

    return implode("/", $words);
  }

  private function getParameter0003($product, $proMas): string {

    if ($proMas) {
      $product = $proMas;
    }

    $words = [];
    if (!empty($product->catpath)) {
      $arr = explode('|', $product->catpath);
      foreach ($arr as $i => $text) {
        if ($i > 0) {
          $words[] = $text;
        }
      }
    }

    $pros = $this->model->getProductsModel();
    $enumProForId = $pros->getEnumProForId();

    if ($product->proforid > 0 && isset($enumProForId[$product->proforid])) {
      $words[] = "forma " . strtolower($enumProForId[$product->proforid]);
    }

    return implode(",", $words);
  }

  private function getProWeight($product, $proMas): ?float {
    if ($proMas) {
      $product = $proMas;
    }

    if ($product->provolumeunit === 'kg') {
      return round($product->provolume, 2);
    } else if ($product->provolumeunit === 'g') {
      return round($product->provolume / 1000, 2);
    } else if ($product->proweight > 0) {
      return round(($product->proweight - 0.2), 2);
    } else {
      return NULL;
    }
  }

  private function getProWeightAll($product, $proMas): ?float {
    if ($proMas) {
      $product = $proMas;
    }

    if ($product->proweight > 0) {
      return round(($product->proweight), 2);
    } else if ($product->provolumeunit === 'kg') {
      return round($product->provolume + 0.2, 2);
    } else if ($product->provolumeunit === 'g') {
      return round($product->provolume / 1000, 2) + 0.2;
    } else {
      return NULL;
    }
  }

  private function getProVolume($product, $proMas): ?float {

    if ($proMas) {
      $product = $proMas;
    }

    if ($product->provolumeunit === 'l') {
      return round($product->provolume, 2);
    } else if ($product->provolumeunit === 'ml') {
      return round($product->provolume / 1000, 2);
    } else {
      return NULL;
    }
  }

  private function fixData($str): array|string|null {
    $str = preg_replace("/\t/", "\\t", $str);
    $str = preg_replace("/\r?\n/", "\\n", $str);
    if(str_contains($str, '"')) $str = '"' . str_replace('"', '""', $str) . '"';
    return $str;
  }

}
