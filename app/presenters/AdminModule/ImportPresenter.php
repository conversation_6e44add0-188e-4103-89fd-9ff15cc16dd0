<?php
namespace AdminModule;

use App\Shippings\BalikovnaApi;
use App\Shippings\UlozenkaApi;
use dibi;
use Nette;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xls;

define ('iPROCODE', 0);
define ('iPROCODEMAS', 1);
define ('iPROCODE2', 2);
define ('iPROMANNAME', 3);
define ('iPROTYPID', 4);
define ('iPROTYPID2', 5);
define ('iPROTYPID3', 6);
define ('iPROTYPID4', 7);
define ('iPROTYPID5', 8);
define ('iPRONAME', 9);
define ('iPRODESCS', 10);
define ('iPRODESC', 11);
define ('iPROKEYWORDS', 12);
define ('iPROACCESS', 13);
define ('iPROWARRANTY', 14);
define ('iPROPRICE1COM', 15);
define ('iPROPRICE1A', 16);
define ('iPROPRICE1B', 17);
define ('iPROPRICE1C', 18);
define ('iPROPRICE1D', 19);
define ('iPROPRICE2COM', 20);
define ('iPROPRICE2A', 21);
define ('iPROPRICE2B', 22);
define ('iPROPRICE2C', 23);
define ('iPROPRICE2D', 24);
define ('iPROVATID', 25);
define ('iPROWEIGHT', 26);
define ('iPROPICNAME', 27);
define ('iPROCATNAME', 28);
define ('iPRONAMES', 29);
define ('iPROSTATUS', 30);
define ('iPARAMS_START', 31);

final class ImportPresenter extends BasePresenter {

  public function getCsvForactiv($filterCatalog = '', $filterProduct = '') {
    $url = 'https://foractiv.cz/customDataFeed/6D4ED499-98A2-4EAB-8E40-62A6D4261DE2';
    if ($_SERVER['SERVER_NAME'] == '127.0.0.1') {
      $url = TEMP_DIR . '/6D4ED499-98A2-4EAB-8E40-62A6D4261DE2.xml';
    }
    $xml = new \SimpleXMLElement(file_get_contents($url));
    $rows = array();

    //nadpisy
    $rows[] = array(
      'Kód dodavatel',
      'Kód eshop',
      'EAN',
      'Název',
      'Název 2',
      'Popis',
      'Sazba DPH',
      'Cena běžná',
      'Cena eshop',
      'Dostupnost',
      'Katalog',
      'Obrázky'
    );

    //převedu na win1250
    foreach ($rows[0] as $key => $text) {
      $rows[0][$key] = $this->iconv2win1250($text);
    }

    //nazvy sloucu
    $rows[] = array(
      'codeven',
      'procode',
      'procode2',
      'proname',
      'proname2',
      'prodesc',
      'vatper',
      'proprice1com',
      'proprice1a',
      'availability',
      'cats',
      'pics'
    );

    $separator = "\t";
    foreach ($xml->SHOPITEM as $item) {
      //zkusim vycistit popis zbozi od zbytecneho HTML
      $desc = (string)$item->DESCRIPTION;
      $desc = html_entity_decode($desc);
      $desc = $this->strip_word_html($desc);
      $desc = str_replace("\n", '', $desc);

      $cats = array();
      $filterOk = false;
      $proName = (string)$item->NAME;
      $manName = trim((string)$item->PRODUCER);
      If (!empty($filterProduct)) {
        if (strpos($proName, $filterProduct) !== false) {
          $filterOk = true;
        }
        if ($filterOk === false) {
          if ($manName === $filterProduct) {
            $filterOk = true;
          }
        }
      }
      if ($filterOk == false) {
        foreach ($item->CATEGORIES->CATEGORY as $cat) {
          $catName = (string)$cat;
          $cats[] = $this->iconv2win1250($catName);
          If (!empty($filterCatalog)) {
            if (strpos($catName, $filterCatalog) !== false) {
              $filterOk = true;
            }
          }
        }
      }
      if ($filterOk === false && (!empty($filterCatalog) || !empty($filterProduct))) continue;

      $rows[] = array(
        'codeven' => (string)$item->CODE,
        'procode' => '',
        'procode2' => (string)$item->EAN,
        'proname' => $this->iconv2win1250((string)$item->NAME),
        'proname2' => '',
        'prodesc' => $this->iconv2win1250($desc),
        'vatper' => (int)$item->TAX,
        'proprice1com' => (double)$item->PRICEMC,
        'proprice1a' => (double)$item->PRICE,
        'availability' => $this->iconv2win1250((string)$item->AVAILABILITY),
        'cats' => implode("\n", $cats),
        'pics' => (string)$item->IMAGE,
      );
    }
    $fileName = TEMP_DIR . '/export.csv';
    $fp = fopen($fileName, 'w');
    foreach ($rows as $row) {
      fputcsv($fp, $row, $separator);
    }
    fclose($fp);
    if (file_exists($fileName)) {
      header('Content-Description: File Transfer');
      header('Content-Type: application/octet-stream');
      header('Content-Disposition: attachment; filename=export.csv');
      header('Content-Transfer-Encoding: binary');
      header('Expires: 0');
      header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
      header('Pragma: public');
      header('Content-Length: ' . filesize($fileName));
      //ob_clean();
      flush();
      readfile($fileName);
    }
    $this->terminate();
  }



  public function importSubmitted (Nette\Application\UI\Form $form) {
    $fileName = '';
    $proparams = $this->model->getProParamsModel();
    $log = array();
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      //podle typu importu se rozhodnu
      if (!$vals['imp_file']->isOk()) {
        $form->addError('Neplatný importní soubor.');
        return;
      } else {
        $fileName = $vals['imp_file']->getTemporaryFile();
      }
      /** PHPExcel_IOFactory */
      include LIBS_DIR.'/PHPExcel/PHPExcel/IOFactory.php';
      $objPHPExcel = PHPExcel_IOFactory::load($fileName);
      $sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,False);

      $cnt = 0;
      $cntinsOK = 0;
      $cntinsER = 0;
      $cntupdOK = 0;
      $cntupdER = 0;
      $manArr = array();
      $typArr = array();
      $products = $this->model->getProductsModel();
      $proParams = $this->model->getProParamsModel();
      $catalogs = $this->model->getCatalogsModel();

      foreach ($sheetData AS $row) {
        $cnt++;
        if ($cnt <= 1) continue;
        if (empty($row[iPROCODE])) {
          $log[] = 'Není vyplněn kód zboží';
          continue;
        }
        if (empty($row[iPRONAME])) {
          $log[] = $row[iPROCODE]. '|Není vyplněn název zboží';
          continue;
        }
        $arr = array();
        //naplnim pole pro ulozeni dat
        $arr['procode'] = trim($row[iPROCODE]);
        $arr['promasid'] = NULL;
        if (trim($row[iPROCODEMAS]) != '') {
          $proid = (int)dibi::fetchSingle('SELECT proid FROM products WHERE procode=%s', trim($row[iPROCODEMAS]));
          if ($proid>0) $arr['promasid'] = $proid;
        }
        $arr['procode2'] = trim($row[iPROCODE2]);


        //zjistim ID vyrobce
        if (!array_key_exists($row[iPROMANNAME], $manArr)) {
          $manArr[$row[iPROMANNAME]] = $this->getManId($row[iPROMANNAME]);
        }
        $arr['promanid'] = $manArr[$row[iPROMANNAME]];
        $arr['protypid'] = $row[iPROTYPID];
        $arr['protypid2'] = $row[iPROTYPID2];
        $arr['protypid3'] = $row[iPROTYPID3];
        $arr['protypid4'] = $row[iPROTYPID4];
        $arr['protypid5'] = $row[iPROTYPID5];
        $arr['proname'] = $row[iPRONAME];
        $arr['pronames'] = $row[iPRONAMES];
        $arr['prokey'] = Nette\Utils\Strings::webalize($arr['proname']);
        $arr['prodescs'] = $row[iPRODESCS];
        $arr['prodesc'] = $row[iPRODESC];
        $arr['protitle'] = $arr['proname'];
        $arr['prodescription'] = strip_tags($row[iPRODESCS]);
        $arr['prokeywords'] = strip_tags($row[iPROKEYWORDS]);
        $arr['proaccess'] = (int)$row[iPROACCESS];
        $arr['prowarranty'] = $row[iPROWARRANTY];
        $arr['proprice1com'] = (double)$row[iPROPRICE1COM];
        $arr['proprice1a'] = (double)$row[iPROPRICE1A];
        $arr['proprice1b'] = (double)$row[iPROPRICE1B];
        $arr['proprice1c'] = (double)$row[iPROPRICE1C];
        $arr['proprice1d'] = (double)$row[iPROPRICE1D];
        $arr['proprice2com'] = (double)$row[iPROPRICE2COM];
        $arr['proprice2a'] = (double)$row[iPROPRICE2A];
        $arr['proprice2b'] = (double)$row[iPROPRICE2B];
        $arr['proprice2c'] = (double)$row[iPROPRICE2C];
        $arr['proprice2d'] = (double)$row[iPROPRICE2D];
        $arr['proweight'] = (double)$row[iPROWEIGHT];
        $arr['provatid'] = (int)$row[iPROVATID];
        $arr['propicname'] = trim($row[iPROPICNAME]);
        $arr['prostatus'] = (int)$row[iPROSTATUS];

        //zjistim ID zbozi pokud existuje
        $proid = (int) dibi::fetchSingle('SELECT proid from products WHERE procode=%s', $arr['procode']);
        if ($proid > 0) {
          if ($products->update($proid, $arr)) {
            $cntupdOK ++;
          } else {
            $cntupdER ++;
          }
        } else {
          $proid = $products->insert($arr);
          if ($proid >0) {
            $cntinsOK ++;
          } else {
            $cntinsER ++;
          }
        }
        //zarazeni do katalogu
        //nactu ID kategorie
        $row[iPROCATNAME] = trim($row[iPROCATNAME]);
        if (!empty($row[iPROCATNAME])) {
          //zarazeni do katalogu
          $catPaths = explode(';', $row[iPROCATNAME]);
          $catIds = array();
          foreach ($catPaths as $catName) {
            $catmasid = 0;
            $name = trim($catName);
            $name = str_replace('  ', ' ', $name);
            $name = str_replace('  ', ' ', $name);
            $name = str_replace('  ', ' ', $name);
            //zjistim jestli kategorie existuje
            if (!empty($name)) {
              $sql = "SELECT catid FROM catalogs WHERE catname='$name'";
              $catid = dibi::fetchSingle($sql);
              if ($catid > 0) {
                $catIds[] = $catid;
              } else {
                $log[] = $arr['procode']. '|Nepodařilo se najít kategorii s názvem "' .$name. '"';
              }
            }
          }
          $products->updateCatPlace($proid, $catIds);
        } else {
          if ($proid > 0) dibi::query('DELETE FROM catplaces WHERE capproid=%i', $proid);
        }
        //zkusim jestli nejsdou zadany nejake parametry
        $parRows = array();

        for($i=iPARAMS_START;true;$i=$i+2){
          if (!isset($row[$i]) || !isset($row[$i+1])) break;
          $parname=(string)$row[$i];
          $parvalue=(string)$row[$i+1];
          if (!empty($parname) && !empty($parvalue)) {
            $parRows[] = array('prpproid' => $proid, 'prpname' => $parname, 'prpvalue' => $parvalue);
          } else {
            break;
          }
        }
        if (count($parRows) > 0) {
          //vamazu stavajici parametry a vlozim nove
          dibi::query('DELETE FROM proparams WHERE prpproid=%i', $proid);
          foreach ($parRows as $parRow) {
            $proparams->insert($parRow);
          }
        }
      }
      $log[] = '';
      $log[] = "Nové záznamy: $cntinsOK bez chyb, $cntinsER chyb";
      $log[] = "Aktualizované záznamy: $cntupdOK bez chyb, $cntupdER chyb";
      $log[] = 'Import dokončen.';
      //$catalogs->rebuildPaths();
      $this->template->log = $log;
      //$this->flashMessage("Import dokončen.");
      //$this->redirect('this');
    }

  }

  public function exportSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $catName = '';
      /*
      if ($vals["catid"] > 0) {
        $catalog = dibi::fetch("SELECT * FROM catalogs WHERE catid=%i", $vals["catid"]);
        $catName .= $catalog->catname;
      }
      */ 
      if ($vals['manid'] > 0) {
        $mans = dibi::fetch('SELECT * FROM manufacturers WHERE manid=%i', $vals['manid']);
        $catName .= $mans->manname;
      }
      
      if (empty($vals['catid']) && empty($vals['manid'])) {
        $catName = 'vse';
      }

      $spreadsheet = new Spreadsheet();
      $sheet = $spreadsheet->setActiveSheetIndex(0);
      if ($vals['type'] == 'pricelist') {
        $cols = array(
          'Katalogové číslo',
          'Kód Pohoda',
          'EAN',
          'Název výrobce',
          'Název',
          'Počet dní do dodání zboží, 0 - skladem',
          'běžná cena(doporučená výrobcem)',
          'Cena A(bez DPH)',
          'Cena B(bez DPH)',
          'Cena C(bez DPH)',
          'Cena D(bez DPH)',
          'Cena E(bez DPH)',
          'Sazba DPH (0-zakladní, 1-snížená)'
        );
      } else if ($vals['type'] == 'wolt') {
        $cols = array(
          'name',
          'description',
          'price',
          'vat_percentage',
          'display_unit',
          'volume_in_ml',
          'number_of_units',
          'weight_in_grams',
          'alcohol_percentage',
          'pos_id',
          'gtin',
          'merchant_sku',
          'ingredients',
          'additives',
          'nutrition_facts',
          'allergens',
          'distributor_information',
          'producer_information',
          'picture'
        );
      } else {
        $cols = array(
          'Katalogové číslo',
          'Kód Pohoda',
          'EAN',
          'kat. číslo nadřízené pol.',
          'objednávací číslo',
          'Název výrobce',
          'Akce',
          'Novinka',
          'Tip',
          'Nejprodávanější',
          'Připravujeme',
          'Název',
          'krátký popis',
          'dlouhý popis',
          'klíčové slova',
          'Počet dní do dodání zboží, 0 - skladem',
          'Záruka (volný text)',
          'běžná cena(doporučená výrobcem)',
          'Cena A(bez DPH)',
          'Cena B(bez DPH)',
          'Cena C(bez DPH)',
          'Cena D(bez DPH)',
          'Cena E(bez DPH)',
          'Sazba DPH (0-zakladní, 1-snížená)',
          'Hmotnost (v Kg)',
          'Název obrázku bez pripony (bez specialnich zaku jako jsou %, ?, /, ...)',
          'Zařazení do katalogu - název kategorií oddělený středníkem',
          'Název pro vyhledávače (zbozi.cz, heureka.cz)',
          'Status (0 - aktivní, 1 - neaktivni)',
          'Parametry (nazev par. a hodnota par. do samostatnych sloucu)'
        );
      }  
      $style_required = array(
        'fill' => array(
            'type' => Fill::FILL_SOLID,
            'color' => array('rgb'=>'00FF00'),
        ),
        'font' => array(
            'bold' => true,
        )
      );

      $style_head = array(
        'fill' => array(
            'type' => Fill::FILL_SOLID,
            'color' => array('rgb'=>'C0C0C0'),
        ),
        'font' => array(
            'bold' => true,
        )
      );
      
      foreach ($cols as $index => $value) {
        $sheet->setCellValueByColumnAndRow($index+1,1, $value);
        $sheet->getStyleByColumnAndRow($index+1,1)->applyFromArray( $style_head );
      }
      
      //nactu data
      $sql = 'SELECT pro.proid, (SELECT p.procode FROM products AS p WHERE pro.promasid=p.proid) AS procodemas, pro.prokey, pro.procode, pro.procode2,
pro.procodep, manname, pro.protypid, pro.protypid2, pro.protypid3, pro.protypid4, pro.protypid5, coalesce(pro.pronutrition, promas.pronutrition) AS pronutrition,
pro.proname, pro.proname2, pro.pronames, pro.prodesc, pro.prodescs, pro.prokeywords, pro.proaccess, pro.prowarranty, pro.proweight, pro.probigsize, pro.prooffer, 
pro.proprice1com, pro.proprice1a, pro.proprice1b, pro.proprice1c, pro.proprice1d, pro.proprice1e, 
pro.proprice2com, pro.proprice2a, pro.proprice2b, pro.proprice2c, pro.proprice2d, pro.proprice2e, 
pro.propicname, pro.provatid, pro.prostatus
FROM products AS pro
INNER JOIN manufacturers ON (manid=pro.promanid)
LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
WHERE 
pro.proismaster=0 AND pro.prostatus=0 AND (promas.prostatus IS NULL OR promas.prostatus=0)';
      /*
      if ($vals["catid"] > 0) {
        $sql .= "
AND catpathids LIKE '%|".$vals["catid"]."|%'";
      }
      */
      if ($vals['manid'] > 0) {
        $sql .= ' AND manid=' .$vals['manid'];
      }  
      $sql .= '
GROUP BY pro.proid
ORDER BY pro.proname';
      $rows = dibi::fetchAll($sql);
      $rowIndex = 1 ;

      //načtu parametry "Balení"
      $packages = [];
      if ($vals['type'] == 'wolt') {
        $packages = dibi::query("select prpproid, prpvalue from proparams where prpname='Balení'")->fetchPairs("prpproid", "prpvalue");
      }

      foreach ($rows as $key=>$row) {
        $rowIndex++;
        $colIndex = 1;

        if ($vals['type'] == 'wolt') {
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, trim($row->proname.' '.$row->proname2), DataType::TYPE_STRING);
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $row->prodescs, DataType::TYPE_STRING);
          $colIndex ++;
          $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proprice1a);
          $colIndex ++;

          if (isset($this->config["VATTYPE_" . $row->provatid])) {
            $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, (int)$this->config["VATTYPE_" . $row->provatid]);
          } else {
            $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, 21);
          }

          $weight = "";
          $volume = "";
          if (!empty($packages[$row->proid])) {
            if (strpos($packages[$row->proid], "g") !== FALSE) {
              $weight = $packages[$row->proid];
            } else {
              $volume = $packages[$row->proid];
            }
          }

          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, 'ks', DataType::TYPE_STRING);
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $volume, DataType::TYPE_STRING); //volume_in_ml
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, '', DataType::TYPE_STRING); //number_of_units
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $weight, DataType::TYPE_STRING); //weight_in_grams
          $colIndex ++;
          $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, 0); //alcohol_percentage
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $row->procode, DataType::TYPE_STRING); //pos_id
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $row->procode2, DataType::TYPE_STRING); //gtin
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, '', DataType::TYPE_STRING); //merchant_sku
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $row->pronutrition, DataType::TYPE_STRING); //ingredients
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, '', DataType::TYPE_STRING); //additives
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, '', DataType::TYPE_STRING); //nutrition_facts
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, '', DataType::TYPE_STRING); //allergens
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, '', DataType::TYPE_STRING); //distributor_information
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $row->manname, DataType::TYPE_STRING); //producer_information
          $colIndex ++;

          //sestavím název obrázku
          $proPicName = (!empty($row->propicname) ? $row->propicname : $row->procode);
          $proPicPath = "";
          if (file_exists(WWW_DIR . '/pic/product/big/' . $proPicName . '.jpg')) {
            $proPicPath = 'https://www.goldfitness.cz/pic/product/big/' . $proPicName . '.jpg';
          }
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $proPicPath, DataType::TYPE_STRING); //producer_information
          $colIndex ++;
        } else {
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $row->procode, DataType::TYPE_STRING);
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $row->procodep, DataType::TYPE_STRING);
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $row->procode2, DataType::TYPE_STRING);
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, $row->manname, DataType::TYPE_STRING);
          $colIndex ++;
          $sheet->setCellValueExplicitByColumnAndRow($colIndex, $rowIndex, trim($row->proname.' '.$row->proname2), DataType::TYPE_STRING);
          $colIndex ++;
          $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proaccess);
          $colIndex ++;
          $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proprice1com);
          $colIndex ++;
          $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proprice1a);
          $colIndex ++;
          $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proprice1b);
          $colIndex ++;
          $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proprice1c);
          $colIndex ++;
          $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proprice1d);
          $colIndex ++;
          $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->proprice1e);
          $colIndex ++;
          $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->provatid);
          $colIndex ++;
        }
      }

      $sheet->setTitle('Export');

      $writer = new Xls($spreadsheet);
      $fName = TEMP_DIR. '/' .Nette\Utils\Strings::webalize($catName). '.xls';
      $writer->save($fName);
      if (file_exists($fName)) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename='.Nette\Utils\Strings::webalize($catName). '.xls');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($fName));
        //ob_clean();
        flush();
        readfile($fName);
      }
    }
  }

  public function renderDefault() {

  }
  
  public function renderImportProductsParam() {
    $rows = dibi::fetchAll('SELECT * FROM cz_product');
    $prps = $this->model->getProParamsModel(); 
    $pros = $this->model->getProductsModel();
    foreach ($rows as $key => $row) {
      $proforid = 0;
      //zapisu parametry
      for ($i=1; $i<=20; $i++) {
        $par = trim($row['propar' .$i]);
        if (!empty($par)) {
          $arr = explode(':', $par);
          $parname = (!empty($arr[0]) ? trim($arr[0]) : '');
          $parvalue = (!empty($arr[1]) ? trim($arr[1]) : '');
          if ($parname == 'Doporučené dávkování') $parname = 'Dávkování'; 
          if (!empty($parname) && !empty($parvalue)) {
            $prps->insert(array(
              'prpproid' => $row->proid,
              'prpname' => $parname, 
              'prpvalue' => $parvalue,
            ));
            if ($parname == 'Balení') {
              //zjistim skupenství
              //1 => 'Prášek',
              //2 => 'Kapsle nebo tablety',
              //3 => 'Tekutina',
               
              if (strpos($parvalue, 'ml') > 0 || strpos($parvalue, 'litr') > 0) {
                $proforid = 3;
              } else if (strpos($parvalue, 'tbl') > 0 || strpos($parvalue, 'kps') || strpos($parvalue, 'kapslí') > 0) {
                $proforid = 2;
              } else if (strpos($parvalue, 'kg') > 0 || strpos($parvalue, 'g') > 0) { 
                $proforid = 1; 
              }
            }
          }
        }      
      }
      //zapisu skupenstvi pokud jsem ho zjistil
      if ($proforid > 0) {
        $pros->update($row->proid, array('proforid'=>$proforid));  
      }
      
    }
    die('hotovo');
  }
  

  public function actionImportForactivProductsCsv() {
    //codeven	procode	procode2	proname	proname2	prodesc	vatper	proprice1com	proprice1a	availability	cats	pics

    $cols = array(
      'codeven',
      'procode',
      'procode2',
      'proname',
      'proname2',
      'prodesc',
      'vatper',
      'proprice1com',
      'proprice1a',
      'availability',
      'cats',
      'pics',
    );
    //vymazu produkty a katalog
    /*
    dibi::query("TRUNCATE products");
    dibi::query("TRUNCATE catalogs");
    dibi::query("TRUNCATE catplaces");
    dibi::query("TRUNCATE manufacturers");
    */

    $cnt = 0;
    $products = $this->model->getProductsModel();
    $mans = $this->model->getManufacturersModel();
    $cats = $this->model->getCatalogsModel();
    $catps = $this->model->getCatPlacesModel();
    if (($handle = fopen(WWW_DIR. '/../data/foractiv-produkty.csv', 'r')) !== FALSE) {
      while (($data = fgetcsv($handle, Null, "\t")) !== FALSE) {
        $cnt++;
        if ($cnt <= 2) {
          continue;
        }
        $proid = 0;
        $vals = array_combine($cols, $data);
        $vals['promanid'] = 358;
        $vals['procode'] = 'FOR_'.$vals['codeven'];
        $vals['provatid'] = ($vals['vatper'] === 15 ? 1 : 0);
        $vals['prostatus'] = 1;
        $vals['proaccess'] = 100;
        //propicname bez pripony
        $vals['propicname'] = Nette\Utils\Strings::webalize($vals['procode']);

        //stahnu obrazky
        $tarFileName = $vals['propicname'];
        copy($vals['pics'], TEMP_DIR . "/" . $tarFileName . '.jpg');
        $image = Nette\Utils\Image::fromFile(TEMP_DIR . "/" . $tarFileName . '.jpg');
        $sizes = (array($this->config["PROPICSIZE_BIG"].'xbig', $this->config["PROPICSIZE_DETAIL"].'xdetail', $this->config["PROPICSIZE_LIST"].'xlist'));
        $this->saveImage($image, WWW_DIR . "/pic/product", $vals["propicname"] . ".jpg", $sizes);
        @unlink(TEMP_DIR . "/" . $tarFileName . '.jpg');
        //odstranim vswechny polozky ktere nejsou v db
        foreach ($vals as $key => $row) {
          if (substr($key, 0, 3) != 'pro') {
            unset($vals[$key]);
          }
        }

        $vals['prodesc'] = $this->strip_word_html($vals['prodesc']);
        //zjistim jestli už neexistuje
        $pro = $products->load($vals['procode'], 'code');
        if ($pro) {
          $products->update($pro->proid, $vals);
        } else {
          $proid = $products->insert($vals);
        }
      }
      fclose($handle);
    }
    die('hotovo');
  }

  public function renderImportProductsCsv() {
    $cols = array(
      'procode',
      'proname',
      'manname',
      'catname',
      'prowarranty',
      'protypid',
      'protypid2',
      'protypid3',
      'protypid4',
      'prodescs',
      'prodesc',
      'proaccess',
      'propicname',
      'picnames',
      'picnamesdesc',
      'files',
      'filesdesc',
      'proaccessories',
      'prooptions',
      'prostatus',
    );

    //vymazu produkty a katalog
    /*
    dibi::query("TRUNCATE products");
    dibi::query("TRUNCATE catalogs");
    dibi::query("TRUNCATE catplaces");
    dibi::query("TRUNCATE manufacturers");
    */

    $cnt = 0;
    $products = $this->model->getProductsModel();
    $mans = $this->model->getManufacturersModel();
    $cats = $this->model->getCatalogsModel();
    $catps = $this->model->getCatPlacesModel();
    if (($handle = fopen(WWW_DIR. '/data/produkty3.csv', 'r')) !== FALSE) {
      while (($data = fgetcsv($handle, Null, ';')) !== FALSE) {
        $cnt++;
        $catid = 0;
        $manid = 0;
        $proid = 0;
        $vals = array();
        if ($cnt == 1) {
          continue;
        }
        $vals = array_combine($cols, $data);

        if ($vals['procode'] == 'Gkm570st') {
          echo '';
        }
        //prevedu pokud treba
        $vals['protypid'] = ($vals['protypid'] == 'True' ? 1 : 0);   //AKCE
        $vals['protypid2'] = ($vals['protypid2'] == 'True' ? 1 : 0); //NOVINKA
        $vals['protypid3'] = ($vals['protypid3'] == 'True' ? 1 : 0); //DOPORUCUJEME
        $vals['protypid4'] = ($vals['protypid4'] == 'True' ? 1 : 0); //NEJPROD
        $vals['prostatus'] = ($vals['prostatus'] == 'True' ? 1 : 0);

        //propicname bez pripony
        if (!empty($vals['propicname'])) {
          $vals['propicname'] = substr($vals['propicname'], 0, -4);
        }

        //zjistim vyrobce pokud neni vytvorim
        $manid = (int)dibi::fetchSingle('SELECT manid FROM manufacturers WHERE manname=%s', $vals['manname']);
        //neni zalozim
        if ($manid == 0) $manid = $mans->insert(array('manname'=>$vals['manname']));
        $vals['promanid'] = $manid;
        //zjistim katalog pokud neni vytvorim
        $vals['catname'] = str_replace('/', '|', $vals['catname']);
        $catid = (int)dibi::fetchSingle('SELECT catid FROM catalogs WHERE catpath=%s', $vals['catname']);
        //neni zalozim
        if ($catid == 0) {
          //rozparsruju cestu
          $arr = explode('|', $vals['catname']);
          $masid = 0;
          foreach ($arr as $val) {
            $catid = (int)dibi::fetchSingle("SELECT catid FROM catalogs WHERE catname=%s AND catmasid=$masid", $val);
            if ($catid == 0) $catid = $cats->insert(array('catmasid'=>$masid, 'catname'=>$val), false) ;
            $masid = $catid;
          }
          $cats->rebuildPaths();
          $catid = $masid;
        }
        /*
        //stahnu obrazky
        $tarFileName = \Nette\Strings::webalize($vals['procode']);
        $this->getProPic($vals["propicname"].'.jpg', $tarFileName.'.jpg');
        $arr = explode('|', $vals["picnames"]);
        $c = 0;
        foreach ($arr as $picname) {
          $c++;
          if (!empty($picname)) $this->getProPic($picname, $tarFileName.'_'.$c.'.jpg', "add");
        }
        $vals['propicname'] = $tarFileName;
        */
        //odstranim vswechny polozky ktere nejsou v db
        foreach ($vals as $key => $row) {
          if (substr($key, 0, 3) != 'pro') {
            unset($vals[$key]);
          }
        }

        $vals['prodesc'] = $this->strip_word_html($vals['prodesc']);
        $proid = $products->insert($vals);

        //zaradim do kataogu
        if ($catid > 0 && $proid > 0) $catps->insert(array('capproid'=>$proid, 'capcatid'=>$catid));
      }
      fclose($handle);
    }
    die('hotovo');
  }

    public function renderImportProductImagesCsv() {
    if (($handle = fopen(WWW_DIR. '/data/produkty3.csv', 'r')) !== FALSE) {
      while (($data = fgetcsv($handle, Null, ';')) !== FALSE) {
        //stahnu obrazky
        $procode = $data[0];
        if ($procode == 'KOD') continue;
        $tarFileName = Nette\Utils\Strings::webalize($procode);

        $this->getProPic($data[12], $tarFileName.'.jpg');
        $arr = explode('|', $data[13]);
        $c = 0;
        foreach ($arr as $picname) {
          $c++;
          if (!empty($picname)) $this->getProPic($picname, $tarFileName.'_'.$c.'.jpg', 'add');
        }

        dibi::query("UPDATE products SET propicname='$tarFileName' WHERE procode=%s", $procode);
      }
      fclose($handle);
    }
    die('hotovo');
  }


  public function renderImportPricesCsv() {
    $cols = array(
      'procode',
      'propricea',
      'vat',
      'disc',
    );

    $cnt = 0;
    $cntNf = 0;
    $products = $this->model->getProductsModel();
    if (($handle = fopen(WWW_DIR. '/data/ceny.csv', 'r')) !== FALSE) {
      while (($data = fgetcsv($handle, 0, ';')) !== FALSE) {
        $cnt++;
        $vals = array();
        if ($cnt == 1) {
          continue;
        }
        $vals = array_combine($cols, $data);
        //prevedu pokud treba
        $vals['provatid'] = ($vals['vat'] == '21' ? 0 : 1);
        //zjistim ID zbozi
        $proid = dibi::fetchSingle('SELECT proid FROM products WHERE procode=%s', $vals['procode']);

        //odstranim vswechny polozky ktere nejsou v db
        unset($vals['vat']);
        unset($vals['disc']);
        unset($vals['procode']);
        if ($proid > 0) {
          $products->update($proid, $vals);
        } else {
          //echo "Nenalezeno:".$vals['procode']."<br>";
          $cntNf++;
        }
      }
      fclose($handle);
    }
    die('hotovo:' .$cnt);
  }

  public function renderImportParamsCsv() {
    $cnt = 0;
    $cntNf = 0;
    dibi::query('TRUNCATE proparams');
    $params = $this->model->getProParamsModel();
    if (($handle = fopen(WWW_DIR. '/data/parametry.csv', 'r')) !== FALSE) {
      while (($data = fgetcsv($handle, 0, ';')) !== FALSE) {
        $cnt++;
        $vals = array();
        if ($cnt == 1) {
          continue;
        }
        //zjistim ID zbozi
        $proid = dibi::fetchSingle('SELECT proid FROM products WHERE procode=%s', $data[0]);

        if ($proid > 0) {

          //odstranim nesmysly
          $pos = strpos($data[2], '$');
          if ($pos === false) {
          } else {
            $data[2] = substr($data[2], 0, $pos);
          }

          $vals = array(
            'prpproid' => $proid,
            'prpname' => $data[1],
            'prpvalue' => trim($data[2].' '.$data[3]),
          );
          $params->insert($vals);
        } else {
          $cntNf++;
        }
      }
      fclose($handle);
    }
    die('hotovo:' .$cnt);
  }

  protected function getProPic($sourceName, $picName, $type= 'main') {
    global $server;
    if ($type == 'main') {
      $file_target = 'D:\work\www\mobil-bar.cz\www\pic\product\big\\'.$picName;
      $file_source = 'http://www.mobil-bar.cz/fotocache/bigorig/' .$sourceName;
    } else {
      $file_target = 'D:\work\www\mobil-bar.cz\www\pic\product\big\\'.$picName;
      $file_source = 'http://www.mobil-bar.cz/fotocache/bigadd/' .$sourceName;
    }

    if (!file_exists($file_target)) {
      $file_source = str_replace(' ', '%20', html_entity_decode($file_source)); // fix url format

      try {

        // Begin transfer
        if (($rh = @fopen($file_source, 'rb')) === FALSE) { return false; } // fopen() handles
        if (($wh = @fopen($file_target, 'wb')) === FALSE) { return false; } // error messages.
        while (!feof($rh)) {
          // unable to write to file, possibly because the harddrive has filled up
          if (fwrite($wh, fread($rh, 1024)) === FALSE) { fclose($rh); fclose($wh); return false; }
        }
        } catch (Exception $e) {
        }
      // Finished without errors
      fclose($rh);
      fclose($wh);
      if ($type == 'main') {
        $file_target = 'D:\work\www\mobil-bar.cz\www\pic\product\list\\'.$picName;
        $file_source = 'http://www.mobil-bar.cz/fotocache/small/' .$sourceName;
      } else {
        $file_target = 'D:\work\www\mobil-bar.cz\www\pic\product\list\\'.$picName;
        $file_source = 'http://www.mobil-bar.cz/fotocache/add/' .$sourceName;
      }
      if (!file_exists($file_target)) {
        $file_source = str_replace(' ', '%20', html_entity_decode($file_source)); // fix url format

        // Begin transfer
        if (($rh = fopen($file_source, 'rb')) === FALSE) { return false; } // fopen() handles
        if (($wh = fopen($file_target, 'wb')) === FALSE) { return false; } // error messages.
        while (!feof($rh)) {
          // unable to write to file, possibly because the harddrive has filled up
          if (fwrite($wh, fread($rh, 1024)) === FALSE) { fclose($rh); fclose($wh); return false; }
        }

        // Finished without errors
        fclose($rh);
        fclose($wh);
        if ($type == 'main') {
          $file_target = 'D:\work\www\mobil-bar.cz\www\pic\product\detail\\'.$picName;
          $file_source = 'http://www.mobil-bar.cz/fotocache/mid/' .$sourceName;
        } else {
          return;
        }
        if (!file_exists($file_target)) {
          $file_source = str_replace(' ', '%20', html_entity_decode($file_source)); // fix url format

          // Begin transfer
          if (($rh = fopen($file_source, 'rb')) === FALSE) { return false; } // fopen() handles
          if (($wh = fopen($file_target, 'wb')) === FALSE) { return false; } // error messages.
          while (!feof($rh)) {
            // unable to write to file, possibly because the harddrive has filled up
            if (fwrite($wh, fread($rh, 1024)) === FALSE) { fclose($rh); fclose($wh); return false; }
          }

          // Finished without errors
          fclose($rh);
          fclose($wh);
        }
      }
    }
  }

  /********************* facilities *********************/

  protected function createComponentImportPohodaForm() {
    $pros = $this->model->getProductsModel();
    $form = new Nette\Application\UI\Form();

    $form->addUpload('imp_file', 'Importní soubor', 100)
      ->addRule(Nette\Forms\Form::FILLED, 'Vyberte importní soubor');

    $form->addSubmit('save', 'Aktualizovat stavy skladu');
    $form->onSuccess[] = array($this, 'importPohodaSubmitted');
    return $form;
  }

  public function importPohodaSubmitted (Nette\Application\UI\Form $form) {
    dibi::getConnection()->onEvent = NULL;
    die('Zatím nespouštějte dokud nedoladíme ...');

    $pros = $this->model->getProductsModel();
    $fileName = '';
    $log = array();
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $file = $vals['imp_file'][0];
      //podle typu importu se rozhodnu
      if (!$file->isOk()) {
        $form->addError('Neplatný importní soubor.');
        return;
      }
      $fileName = $file->getTemporaryFile();

      if (file_exists($fileName)) {
        //pro výpočet EURa
        $digits = (int)$this->neonParameters['currency'][2]['decimals'];
        $rate = (double)$this->config['PRICE2RATE'];

        $xml = simplexml_load_file($fileName);

        $ns = $xml->getNamespaces(true);
        $data = $xml->children($ns['rsp']);
        $data = $data->children($ns['lStk']);
        $products = array();
        $skipped = 0;
        foreach ($data->listStock->stock as $item) {
          $stock = $item->children($ns['stk']);
          $stockHeader = $stock->children($ns['stk']);

          $count = (int)$stockHeader->count;
          $countOrders = (int)$stockHeader->countReceivedOrders;
          $count = max(0, $count - $countOrders);

          $stores = array(
            'Kobylisy' => 'store',
            'Strašnice' => 'shop1', //Praha 1O
            'Vinohrady' => 'shop2', //Praha 2
          );
          //zjistim sklad
          $sto = $stockHeader->storage->children('http://www.stormware.cz/schema/version_2/type.xsd');
          $str = (string)$sto->ids;
          $arr = explode('/', $str);
          $store = '';
          if (!empty($stores[$arr[0]])) {
            $store = $stores[$arr[0]];
          }

          if (empty($store)) {
            $skipped++;
            continue;
          }

          $codep = trim((string)$stockHeader->code);

          $ean = trim((string)$stockHeader->EAN);
          $products[$codep]['procodep'] = $codep;
          if (!empty($ean)) {
            $products[$codep]['procode2'] = $ean;
          }
          $products[$codep]['proqty_' .$store] = $count;

          $stockPriceItem = $stock->stockPriceItem->children($ns['stk']);

          if ($codep ==='01445') {
            //echo "";
          }

          foreach($stockPriceItem->stockPrice as $stockPrice) {
            $node = $stockPrice->children($ns['typ']);
            $priceType = (string)$node->ids;
            $price = (double)$node->price;
            $prccat = '';
            switch ($priceType) {
              case 'MOC':
                $prccat = 'com';
                break;
              case 'Prodejní':
                $prccat = 'a';
                break;
              case 'V.I.P.':
                $prccat = 'b';
                break;
              case 'VOC - A':
                $prccat = 'd';
                break;
              case 'VOC - B':
                $prccat = 'c';
                break;
              case 'VOC - C':
                $prccat = 'e';
                break;
            }
            if (!empty($prccat) && !empty($price)) {
              $products[$codep]['proprice1' .$prccat] = $price;
            }
          }

        }
      } else {
        $log[] = "Nepodařilo se načíst soubor $fileName";
      }

      $cntupdOK = 0;
      $cntupdER = 0;
      $cnt = 0;

      $log[] = "Přeskočeno $skipped položek";


      foreach ($products as $item) {
        $proid = 0;
        $cnt++;
        //projdu polozky a zaktualizuji stav skladu a kod z pohody
        if (!empty($item['procodep'])) {
          $proid = (int)dibi::fetchSingle('
            SELECT pro.proid 
            FROM products as pro
            LEFT JOIN products AS promas ON (pro.promasid=promas.proid) 
            WHERE pro.procodep=%s', $item['procodep'], ' AND coalesce(promas.prostatus, pro.prostatus)=0');
          if ($proid === 0) {
            $log[] = 'Kód pohody nenalezen v eshopu :' .$item['procodep'];
          }

        }
        if ($proid === 0 && !empty($item['procode2'])) {
          //zkusim najit produkt pres EAN
          $rows = dibi::fetchAll('
            SELECT pro.proid
            FROM products as pro
            LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
            WHERE pro.procode2=%s', $item['procode2'], ' AND coalesce(promas.prostatus, pro.prostatus)=0');
          if ($rows) {
            if (count($rows) === 1) {
              $proid = $rows[0]->proid;
              $log[] = 'Položka nalezena přes EAN :' .$item['procode2'];
              unset($item['procode2']);
            } else {
              $log[] = 'EAN :' .$item['procode2'] . ' nalezen vícekrát. Nelze použít pro identifikaci';
            }

          } else {
            $log[] = 'Položka nenalezena přes kód Pohody (' .$item['procodep']. ') ani přes EAN (' .$item['procode2']. ')';
          }
        } elseif ($proid > 0) {
          //nalezeno podle kodu pohody
          unset($item['procodep']);
        }
        if ($proid > 0) {
          //nactu vyrobce a vypocitam cenu v EUR
          $manid = (int)dibi::fetchSingle('SELECT promanid FROM products WHERE proid=%i', $proid);
          $koef = ($manid = 1 ? 1.05 : 1);
          if (isset($item['proprice1com'])) {
            $item['proprice2com'] = round($item['proprice1com'] / $rate * $koef, $digits);
          }
          if (isset($item['proprice1a'])) {
            $item['proprice2a'] = round($item['proprice1a'] / $rate * $koef, $digits);
          }
          if (isset($item['proprice1b'])) {
            $item['proprice2b'] = round($item['proprice1b'] / $rate * $koef, $digits);
          }
          if (isset($item['proprice1c'])) {
            $item['proprice2c'] = round($item['proprice1c'] / $rate * $koef, $digits);
          }
          if (isset($item['proprice1d'])) {
            $item['proprice2d'] = round($item['proprice1d'] / $rate * $koef, $digits);
          }
          if (isset($item['proprice1e'])) {
            $item['proprice2e'] = round($item['proprice1e'] / $rate * $koef, $digits);
          }

          //pokud není na nějakém skladu doplním 0
          if (!isset($item['proqty_store'])) {
            $item['proqty_store'] = 0;
          }
          if (!isset($item['proqty_shop1'])) {
            $item['proqty_shop1'] = 0;
          }
          if (!isset($item['proqty_shop2'])) {
            $item['proqty_shop2'] = 0;
          }

          if ($pros->update($proid, $item)) {
            $cntupdOK++;
          } else {
            $cntupdER++;
            $log[] = "Chyba proid=$proid.";
          }
        }
      }

      //odpoznamkovat až budou všude kódyp pohody
      //dibi::query("UPDATE products set proqty=(proqty_store+proqty_shop1+proqty_shop2)");
      //dibi::query("UPDATE products set proaccess=IF((proqty_store+proqty_shop1+proqty_shop2)>0, 0, 100)");

      $log[] = "Celkem $cnt. Aktualizované záznamy: $cntupdOK bez chyb, $cntupdER chyb";
      $log[] = 'Import dokončen.';
      $this->template->log = $log;
    }
  }

  protected function createComponentExportVendorForm() {
    $form = new Nette\Application\UI\Form();
    $form->addText('filtercatalog', 'Filtrovat podle názvu katalogu :');
    $form->addText('filterproduct', 'Filtrovat podle názvu zboží:');
    $form->addSelect('type', 'Dodavatel:', array('foractiv' => 'Foractiv'));

    $form->addSubmit('save', 'Exportovat');
    $form->onSuccess[] = array($this, 'exportVendorSubmitted');
    return $form;
  }

  public function exportVendorSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      switch ($vals->type) {
        case 'foractiv':
          $this->getCsvForactiv($vals->filtercatalog, $vals->filterproduct);
          break;
      }
    }
  }

  protected function createComponentExportForm() {

    $form = new Nette\Application\UI\Form();
    $catalogs = $this->model->getCatalogsModel();
    $products = $this->model->getProductsModel();
    /*
    $arr = $catalogs->getEnumCatalogCombo();
    //$arr[0] = "Vše";
    $form->addSelect('catid', 'Katalog:', $arr)
      ->setPrompt("Vše");
    */
    $form->addSelect('manid', 'Výrobce:', $products->getEnumProManId())
      ->setPrompt('Vše');

    $form->addSelect('type', 'Typ exportu:', array('pricelist'=>'Ceník', 'wolt'=>'Wolt'));

    $form->addSubmit('save', 'Exportovat');
    $form->onSuccess[] = array($this, 'exportSubmitted');
    return $form;
  }

  private function getManId($name) {
    $manid = dibi::fetchSingle('SELECT manid FROM manufacturers WHERE manname=%s', $name);
    if ($manid > 0) {
    } else {
      $manufacturers = $this->model->getManufacturersModel();
      $manid = $manufacturers->insert(array('manname'=>$name));
    }
    return($manid);

  }

  private function getTypId($name) {
    $enuid = dibi::fetchSingle('SELECT enuid FROM enumcats WHERE enutypid=2 AND enuname=%s', $name);
    if ($enuid > 0) {
    } else {
      $enumcats = $this->model->getEnumcatsModel();
      $enuid = $enumcats->insert(array('enutypid'=>2, 'enuname'=>$name));
    }
    return($enuid);

  }

  private function prepStr($str) {
    $str = trim($str, '"');
    $str = ereg_replace('""', '"', $str);
    return $str;
  }

  private function strip_word_html($text, $allowed_tags = '<a><ul><li><br><br/><br /><p>') {
    mb_regex_encoding('UTF-8');
    //replace MS special characters first
    $search = array('/&lsquo;/u', '/&rsquo;/u', '/&ldquo;/u', '/&rdquo;/u', '/&mdash;/u');
    $replace = array('\'', '\'', '"', '"', '-');
    $text = preg_replace($search, $replace, $text);
    //make sure _all_ html entities are converted to the plain ascii equivalents - it appears
    //in some MS headers, some html entities are encoded and some aren't
    //$text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
    //try to strip out any C style comments first, since these, embedded in html comments, seem to
    //prevent strip_tags from removing html comments (MS Word introduced combination)
    if(mb_stripos($text, '/*') !== FALSE){
        $text = mb_eregi_replace('#/\*.*?\*/#s', '', $text, 'm');
    }
    //introduce a space into any arithmetic expressions that could be caught by strip_tags so that they won't be
    //'<1' becomes '< 1'(note: somewhat application specific)
    $text = preg_replace(array('/<([0-9]+)/'), array('< $1'), $text);
    $text = strip_tags($text, $allowed_tags);
    //eliminate extraneous whitespace from start and end of line, or anywhere there are two or more spaces, convert it to one
    $text = preg_replace(array('/^\s\s+/', '/\s\s+$/', '/\s\s+/u'), array('', '', ' '), $text);
    //strip out inline css and simplify style tags
    $search = array('#<(strong|b)[^>]*>(.*?)</(strong|b)>#isu', '#<(em|i)[^>]*>(.*?)</(em|i)>#isu', '#<u[^>]*>(.*?)</u>#isu');
    $replace = array('<b>$2</b>', '<i>$2</i>', '<u>$1</u>');
    $text = preg_replace($search, $replace, $text);
    //on some of the ?newer MS Word exports, where you get conditionals of the form 'if gte mso 9', etc., it appears
    //that whatever is in one of the html comments prevents strip_tags from eradicating the html comment that contains
    //some MS Style Definitions - this last bit gets rid of any leftover comments */
    $num_matches = preg_match_all("/\<!--/u", $text, $matches);
    if($num_matches){
        $text = preg_replace('/\<!--(.)*--\>/isu', '', $text);
    }
    $text = preg_replace('/(<[^>]+) style=".*?"/i', '$1', $text);
  return $text;
  }

  /**
   * @param string $string
   * @return string
   */
  public function iconv2win1250($string) {
    $string = \Nette\Utils\Strings::fixEncoding($string);
    $string = iconv('utf-8//IGNORE', 'windows-1250//IGNORE', $string);
    return ($string);
  }

  public function actionUpdateVariantsParams() {
    $pros = $this->model->getProductsModel();
    $rows = dibi::fetchAll('SELECT promasid FROM products where promasid > 0 group by promasid');
    foreach ($rows as $key => $row) {
      $pros->copyParamsToVariants($row->promasid);
    }
    $this->terminate();
  }

  public function actionRecalcProIsMaster() {
    $pros = $this->model->getProductsModel();
    $rows = dibi::fetchAll('SELECT proid, proprice1com, proprice2com FROM products');
    foreach ($rows as $row) {
      $cnt = (int)dibi::fetchSingle('SELECT
    COUNT(proid) AS cnt
    FROM products WHERE promasid=%i', $row->proid);

      $proIsMaster = (int)($cnt > 0);
      $vals = array('proismaster'=>$proIsMaster);
      
      if ($proIsMaster == 1) {
        dibi::query('
          UPDATE products SET 
          proprice1com=%f', $row->proprice1com, ',
          proprice2com=%f', $row->proprice2com, '
          WHERE promasid=%i', $row->proid
        ); 
        
        /*
        $vals['proprice1com']=$row->proprice1com;
        $vals['proprice1a']=$row->proprice1a;
        $vals['proprice1b']=$row->proprice1b;
        $vals['proprice1c']=$row->proprice1c;
        $vals['proprice1d']=$row->proprice1d;
        $vals['proprice1e']=$row->proprice1e;
        $vals['proprice2com']=$row->proprice2com;
        $vals['proprice2a']=$row->proprice2a;
        $vals['proprice2b']=$row->proprice2b;
        $vals['proprice2c']=$row->proprice2c;
        $vals['proprice2d']=$row->proprice2d;
        $vals['proprice2e']=$row->proprice2e;
        */
      }
      
      $pros->update($row->proid, $vals);
    }
    die('hotovo');
  }

  public function actionUpdateUlozenkaBranches() {
    $uloapi = new UlozenkaApi($this->neonParameters["ulozenka"], $this->model);
    $uloapi->updateBranches();
    $uloapi = new UlozenkaApi($this->neonParameters["ulozenka_mall"], $this->model);
    $uloapi->updateBranches();

    $this->flashMessage("Aktualizace provedena.");
    $this->redirect("default");
  }

  public function actionUpdateBalikovnaBranches() {
    $balapi = new BalikovnaApi($this->model);
    $balapi->updateBranches();

    $this->flashMessage("Aktualizace provedena.");
    $this->redirect("default");
  }

  public function actionFillFlavor() {
    $parName = 'Příchuť';
    $prps = $this->model->getProParamsModel();

    //nactu vsechny podrizene polozky co mají v názvu pomlčku
    $rows = dibi::fetchAll("SELECT proid, promasid, procode, proname FROM products WHERE promasid > 0");

    foreach ($rows as $row) {

      //zjistim jestli je z kategorie sportovní výživa
      $cnt = (int)dibi::fetchSingle('
        SELECT COUNT(*) FROM catalogs 
        INNER JOIN catplaces ON (catid=capcatid)
        WHERE capproid=%i', $row->promasid > 0 ? $row->promasid : $row->proid, " AND catpathids LIKE '|84|%'
        ");

      if ($cnt === 0) {
        continue;
      }

      $cnt = (int)dibi::fetchSingle('SELECT count(*) FROM proparams WHERE prpproid=%i', $row->proid, ' AND prpname=%s', $parName);
      if ($cnt > 0) {
        continue;
      }

      $parValue = '';
      $arr = explode(' - ', $row->proname);

      if (count($arr) >= 2) {
        $parValue = $arr[count($arr) - 1];
        if (substr($parValue, 0, 4) == 'EXP.' && count($arr) >= 3) {
          $parValue = $arr[count($arr) - 2];
        }
      }

      if (substr($parValue, 0, 4) == 'EXP.') {
        continue;
      }

      if (!empty($parValue)) {
        //vložím parametr
        $vals = array(
          'prpproid' => $row->proid,
          'prpname' => $parName,
          'prpvalue' => $parValue,
        );
        $prps->insert($vals);
        echo $row->procode. ';'. $row->proname. ';' .$parName. ';' .$parValue. ';https://www.goldfitness.cz/administrace/product/upravit?id=' .$row->proid. '<br>';
      } else {
        echo "$parValue empty " . $row->procode . ': ' . $row->proname."<br>";
      }
    }
    die('hotovo');
  }
}
