<?php
namespace AdminModule;

use dibi;
use Model;
use Nette;

final class UserPresenter extends BasePresenter {

  /** @persistent */
  public $sName = '';

  /** @persistent */
  public $sMail = '';

  /** @persistent */
  public $sMailing = '';

  /** @persistent */
  public $sMailingSend = false;

  /** @persistent */
  public $sPrcCat = [];

  /** @persistent */
  public $sIc = '';

  /** @persistent */
  public $sFirName = '';

  /** @persistent */
  public $sDateFrom = '';

  /** @persistent */
  public $sDateTo = '';

  /** @persistent */
  public $sDateUntil = '';

  /** @persistent */
  public $sDateAfter = '';


  public function userEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $users = $this->model->getUsersModel();
      $id = $this->getParameter('id');
      $values = $form->getValues();
      if ($id > 0) {
        //kontrola jestli se změnil email
        $usr = $users->load($id);

        $users->update($id, $values);

        if ($usr->usrmail != $values->usrmail) {
          $this->sendVerification($usr->usrid);
        }

        //zjistim jestli nebyl zmenen status zasílání emailů
        if ($values["usrmaillist"] != $usr->usrmaillist) {
          if ($values["usrmaillist"] == FALSE) {
            $users->logEvent($id, \Model\UsersModel::EVENT_MAILLIST_REM);
          } else if ($values["usrmaillist"] == TRUE) {
            $users->logEvent($id, \Model\UsersModel::EVENT_MAILLIST_ADD);
          }
        }

        $this->flashMessage('Aktualizováno v pořádku');
      } else {
        $id = $users->insert($values);
        $this->sendVerification($id);
        if ($values["usrmaillist"] == TRUE) {
          $users->logEvent($id, \Model\UsersModel::EVENT_MAILLIST_ADD);
        }
        $this->flashMessage('Nový záznam uložen v pořádku');
      }
    }
    $this->redirect('default');
  }

  /********************* view default *********************/

  public function renderDefault() {
    $users = $this->model->getUsersModel();
    
    $doExport = (bool)$this->getParameter("doExport");
    $doExportMd5 = (bool)$this->getParameter("doExportMd5");
    $doExportEcomail = (bool)$this->getParameter("doExportEcomail");

    $where = "";
    if (!empty($this->sIc)) $where .= " usric like '$this->sIc%' AND ";
    if (!empty($this->sPrcCat)) $where .= " usrprccat IN ('" . implode("','", $this->sPrcCat) . "') AND ";
    if (!empty($this->sName)) $where .= " (usrilname LIKE '%$this->sName%' OR  usrstlname LIKE '%$this->sName%') AND ";
    if (!empty($this->sFirName)) $where .= " (usrifirname LIKE '%$this->sFirName%' OR  usrstfirname LIKE '%$this->sFirName%') AND ";
    if (!empty($this->sMail)) $where .= " usrmail LIKE '%$this->sMail%' AND ";
    if ($this->sMailingSend == 1) $where .= " usrmaillist=1 AND ";

    if ($where != "") {
      $where = substr($where, 0, -5);
    }
    if (!empty($where)) $where = " WHERE $where";

    if (!empty($this->sMailing)) {
      $mailings = $this->model->getMailingsModel();
      $mailing = $mailings->load($this->sMailing);
      $dataSource = dibi::dataSource($mailings->getUsersSql($mailing));
    } else if (!empty($this->sDateFrom)) {
      $dataSource = dibi::dataSource("
      SELECT users.*, SUM(ordpricevat) AS orderssum FROM users
      INNER JOIN orders ON (usrmail=ordmail)
      WHERE orddatec BETWEEN %s ", $this->formatDateMySQL($this->sDateFrom), " AND %s ",  $this->formatDateMySQL($this->sDateTo), " 
      GROUP BY usrid
      ORDER BY SUM(ordpricevat) DESC"
      );
	} else if (!empty($this->sDateUntil)) {
      $dataSource = dibi::dataSource("
	      SELECT users.* 
	      FROM users
	      " . ($where === "" ? " WHERE " : " $where AND ") . "
	      NOT EXISTS (
		    SELECT 1
		    FROM orders
		    WHERE ordmail = usrmail
		      AND orddatec > '" . $this->formatDateMySQL($this->sDateUntil) . "'
		  )
	  ");
	} else if (!empty($this->sDateAfter)) {
      $dataSource = dibi::dataSource("
	      SELECT users.* 
	      FROM users
	      " . ($where === "" ? " WHERE " : " $where AND ") . "
	      EXISTS (
		    SELECT 1
		    FROM orders
		    WHERE ordmail = usrmail
		      AND orddatec > '" . $this->formatDateMySQL($this->sDateAfter) . "'
		  )
	  ");
    } else {
      $dataSource = $users->getDataSource("
      SELECT *, IF(uslid IS NULL,0,1) AS aprove
      FROM users
      LEFT JOIN users_log ON (uslusrid=usrid AND uslevtid=2)
      $where
      GROUP BY usrid
      ORDER BY usriname ASC
      ");
    }
    
    If ($doExport || $doExportMd5 || $doExportEcomail) {
      if ($doExport) {
        $fileName = 'mails_export.csv';
      } else if ($doExportMd5) {
        $fileName = 'mails_export_hash.csv';
      } else if ($doExportEcomail) {
        $fileName = 'mails_export_ecomail.csv';
      }

      header('Content-Type: application/csv');
      header('Content-Disposition: attachment; filename="' . $fileName . '"');

      if ($doExportMd5) {
        echo "Email\n";
      } else if ($doExport) {
        echo "Email\n";
      } else if ($doExportEcomail) {
        echo "Jméno;Příjmení;E-mail\n";
      }

      $rows = $dataSource->fetchAll();
      foreach ($rows as $row) {

        if ($doExportMd5) {
          echo hash("sha256", trim($row->usrmail)) . ";\n";
        } else if ($doExport) {
          echo trim($row->usrmail) . ";\n";
        } else if ($doExportEcomail) {
          $name = "";
          $lname = "";

          if (empty($row->usriname)) {
            $arr = explode(" ", $row->usrilname);
            if (count($arr) === 2) {
              $name = $arr[0];
              $lname = $arr[1];
            }
          }
          if ($name === "") {
            $name = $row->usriname;
            $lname = $row->usrilname;
          }

          echo $name . ";" . $lname . ";" . trim($row->usrmail) . ";\n";
        }
      }
      
      $this->terminate();
    } else {
      $paginator = $this['paginator']->getPaginator();
      $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
      $paginator->itemCount = $dataSource->count();
      $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
      $this->template->page = $paginator->page;

      if (count($dataRows) === 1 && is_null($dataRows[0]->usrid)) {
        $dataRows = [];
      }

      $this->template->dataRows = $dataRows;
  
      //ciselnik statusu
      $this->template->enum_usrstatus = $users->getEnumUsrStatus();
      $this->template->enum_usrprccat = $this->getEnumPrcCat();
    }  
  }

  public function renderEdit($id) {
    $form = $this['userEditForm'];

    if (!$form->isSubmitted()) {
      $users = $this->model->getUsersModel();
      $dataRow = $users->load($id);
      if (!$dataRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }
      $form->setDefaults($dataRow);

      $this->template->dataRow = $dataRow;

      $this->template->usersLog = dibi::fetchAll('SELECT * FROM users_log WHERE uslusrid=%i', $dataRow->usrid, ' ORDER BY usldatec DESC');
      $this->template->enum_uslevtid = $users->getEnumUslEvtId();
    }
  }

  public function renderDelete($id) {
    if ($id > 0) {
      $users = $this->model->getUsersModel();
      //zjistím jestli je v nějaké obj
      $cnt = (int)dibi::fetchSingle("SELECT COUNT(ordid) FROM orders WHERE ordusrid=%i", $id);
      if ($cnt === 0) {
        $users->delete($id);
        $this->flashMessage('Záznam byl vymazán');
      } else {
        $users->clearPersonalData($id);
        $this->flashMessage('Účet je součástí několika objednávek. Byly proto jen vymazány osobní data.');
      }
    }
    $this->redirect('default');
  }


  /********************* facilities *********************/

  protected function createComponentUserEditForm() {
    $user = $this->model->getUsersModel();
    $form = new Nette\Application\UI\Form();

    $form->addGroup('Základní údaje');

    $form->addCheckbox("usrmaillist", "Mailování novinek");

    $form->addText('usrmail', 'Email:', 30)
      ->setOption('description', ' slouží klientovi zároveň jako přihlašovací jméno')
      ->setEmptyValue('@')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte email.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addText('usrdiscount', 'Sleva v %:', 30)
      ->addRule(Nette\Forms\Form::FILLED, "Sleva v procentech musí být vyplněna")
      ->addRule(Nette\Forms\Form::NUMERIC, "Sleva v procentech musí být číslo")
      ->setDefaultValue(0);

    $form->addSelect('usrprccat', 'Cenová hladina', $this->getEnumPrcCat());

    $form->addText('usrtel', 'Telefon:', 30);

    $form->addCheckbox("usrvat", "Plátce DPH");
    $form->addText('usric', 'IČ:', 30);
    $form->addText('usrdic', 'DIČ:', 30)
      ->addConditionOn($form["usrvat"], Nette\Forms\Form::EQUAL, TRUE)
         ->addRule(Nette\Forms\Form::FILLED, 'DIČ musí být vyplněno, pokud je plátce DPH');

    $form->addGroup('Adresa fakturační:');

    $form->addText('usrilname', 'Příjmení:', 30);

    $form->addText('usriname', 'Jméno:', 30)
      ->addConditionOn($form["usrilname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('usrifirname', 'Firma:', 30);

    $form->addText('usristreet', 'Ulice:', 30)
      ->addConditionOn($form["usrilname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('usristreetno', 'Číslo popisné:', 30)
      ->addConditionOn($form["usrilname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

	$form->addText('usricity', 'Město:', 30)
      ->addConditionOn($form["usrilname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('usripostcode', 'PSČ:', 30)
      ->addConditionOn($form["usrilname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.')
        ->addRule(Nette\Forms\Form::INTEGER, 'Prosím vyplňte pouze číslice.')
        ->addRule(Nette\Forms\Form::LENGTH, 'Prosím vyplňte přesně %d číslic', 5);

    $form->addGroup('Adresa dodací:');
    $form->addText('usrstlname', 'Příjmení:', 30);

    $form->addText('usrstname', 'Jméno:', 30)
      ->addConditionOn($form["usrstlname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('usrstfirname', 'Firma:', 30);

    $form->addText('usrststreet', 'Ulice:', 30)
      ->addConditionOn($form["usrstlname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('usrststreetno', 'Číslo popisné:', 30)
      ->addConditionOn($form["usrstlname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

	$form->addText('usrstcity', 'Město:', 30)
      ->addConditionOn($form["usrstlname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('usrstpostcode', 'PSČ:', 30)
      ->addConditionOn($form["usrstlname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.')
        ->addRule(Nette\Forms\Form::INTEGER, 'Prosím vyplňte pouze číslice.')
        ->addRule(Nette\Forms\Form::LENGTH, 'Prosím vyplňte přesně %d číslic', 5);

    $form->addTextArea('usrnote', 'Poznámka', 100, 5);

    $form->addSelect('usrstatus', 'Status', $user->getEnumUsrStatus());

    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('default');
    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'userEditFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }

  protected function createComponentSearchForm() {
    $catalogs = $this->model->getCatalogsModel();
    $usrs = $this->model->getUsersModel();

    $form = new Nette\Application\UI\Form();
    $form->addGroup("Vyhledávání");
    $form->addText("ic", "IČ", 10)
      ->setDefaultValue($this->sIc);
    $form->addText("stname", "Příjmení", 10)
      ->setDefaultValue($this->sName);
    $form->addText("firname", "Firma", 10)
      ->setDefaultValue($this->sFirName);
    $form->addText("email", "Email", 10)
      ->setDefaultValue($this->sMail);

    $form->addText("datefrom", "Obrat od", 10)
      ->setDefaultValue($this->sDateFrom);

	  $form->addText("dateto", " do ", 10)
      ->setDefaultValue($this->sDateTo);

	  $form->addText("dateuntil", "NEobjednal po datu", 10)
		  ->setDefaultValue($this->sDateUntil);

	  $form->addText("dateafter", "Objednal po datu", 10)
		  ->setDefaultValue($this->sDateAfter);

	  $form->addCheckbox("mailingsend", "Souhlas se zasíláním novinek")
      ->setDefaultValue($this->sMailingSend);

    $form->addCheckboxList("prccat", "", $this->getEnumPrcCat())
      ->getSeparatorPrototype()->setName(NULL);

    $arr = dibi::query("SELECT mamid, mamsubject FROM mailings WHERE mamstatus IN (0,1) ORDER BY mamid DESC")->fetchPairs("mamid", "mamsubject");
    $form->addSelect("mailing", "Mailing", $arr)
      ->setPrompt("");

    if (!empty($this->sMailing)) $form["mailing"]->setDefaultValue($this->sMailing);
    if (!empty($this->sPrcCat)) $form["prccat"]->setDefaultValue($this->sPrcCat);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('export', 'nezakódované');
    $form->addSubmit('export_md5', 'zakódované');
    $form->addSubmit('export_ecomail', 'ecomail.cz');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

    public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sIc= Null;
        $this->sPrcCat = Null;
        $this->sName = Null;
        $this->sFirName = Null;
        $this->sMail = Null;
        $this->sMailing = Null;
        $this->sMailingSend = FALSE;
        $this->sDateFrom = NULL;
        $this->sDateTo = NULL;
	    $this->sDateUntil = NULL;
	    $this->sDateAfter = NULL;
      } else {
        $vals = $form->getValues();
        $this->sIc= $vals["ic"];
        $this->sName = $vals["stname"];
        $this->sPrcCat = $vals["prccat"];
        $this->sFirName = $vals["firname"];
        $this->sMail = $vals["email"];
        $this->sMailing = $vals["mailing"];
        $this->sMailingSend = $vals["mailingsend"];
        $this->sDateFrom = $vals["datefrom"];
		$this->sDateTo = $vals["dateto"];
		$this->sDateUntil = $vals["dateuntil"];
		$this->sDateAfter = $vals["dateafter"];
      }
    }
    if ($form["export"]->isSubmittedBy()) {
      $this->redirect("default", ["doExport" => 1]);
    } else if ($form["export_md5"]->isSubmittedBy()) {
      $this->redirect("default", ["doExportMd5" => 1]);
    } else if ($form["export_ecomail"]->isSubmittedBy()) {
      $this->redirect("default", ["doExportEcomail" => 1]);
    } else {
      $this->redirect("default");
    }
  }

  public function actionClientCardPdf($id) {
    if ($id > 0) {
      $this->ClientCardPdf($id, 'D');
    }
    $this->terminate();
  }

  public function ClientCardPdf($id, $dest="D") {
    $template = $this->getTemplate();
    $users = $this->model->getUsersModel();
    $template->dataRow = $users->load($id);
    $template->setFile(WWW_DIR.'/../templates/pdf/clientCardPdf.latte');
    $fname = ('klient-'.$template->dataRow->usrid.'-'.Nette\Utils\Strings::webalize($template->dataRow->usriname.'-'.$template->dataRow->usrilname));
    // mPDF
    require(LIBS_DIR."/mpdf/mpdf.php");
    $mpdf = new mPDF('utf-8','A4', 12,'',10,10,10,10,9,9,'P');
    $mpdf->useOnlyCoreFonts = true;
    $mpdf->SetDisplayMode('real');
    $mpdf->SetAutoFont(0);
    $template->headers = (object) NULL;

    $pdfHtml = (string) $template; // vyrenderujeme šablonu už nyní
    $mpdf->AddPage('P');
    $mpdf->WriteHTML($pdfHtml, 2);
    if ($dest=="I") {
      $name = TEMP_DIR."/".$fname.".pdf";
    } else {
      $name = $fname.".pdf";
    }
    $mpdf->Output($name, $dest);

  }

  private function sendVerification($usrid) {
    $usrs = $this->model->getUsersModel();
    $usr = $usrs->load($usrid);
    if (empty($usr->usrmailvcode)) {
      $usrs->update($usr->usrid, array(
        'usrmailvcode' => $this->getVerifyCode(),
        'usrmailverified' => 0
      ));
      $usr = $usrs->load($usr->usrid);
    }
    $mailTemplate = $this->createTemplate();
    $mailTemplate->user = $usr;
    $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/userVerify.latte');

    $this->mailSend($usr->usrmail, 'Ověření emailu', $mailTemplate);
    //zaloguji odeslání žádosti
    $usrs->logEvent($usr->usrid, Model\UsersModel::EVENT_MAIL_VERIFICATION_SEND);
    $this->flashMessage('Ověřovací email byl úspěšně odeslán.');
  }
}