<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;

final class NewPresenter extends BasePresenter {

  public function renderDefault() {
    //seznam aktualnich upozorneni
    $new = $this->model->getNewsModel();
    $where = "";

    $this->template->news = $new->fetchAll("SELECT * FROM news ORDER BY newdate DESC");
  }

  public function renderEdit($id) {
    $form = $this['editForm'];
    $imageRows = array();
    if (!$form->isSubmitted()) {
      $id = $this->getParam('id');
      $this->template->id = $id;
      $new = $this->model->getNewsModel();
      $this->template->mainImageName = "";
      if ($id > 0) {
        $row = $new->load($id);
        if (!$row) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        $newdate = New \DibiDateTime($row->newdate);
        $row->newdate = $newdate->format('d.m.Y');
        $form->setDefaults($row);
        //vlozim hlavni obrazek
        $fileName = 'new_'.$id.'.jpg';
        if (file_exists(WWW_DIR."/pic/new/list/$fileName")) $this->template->mainImageName = $fileName;
      }
    }
  }

  public function renderDelete($id) {
    if ($id > 0) {
      $product = $this->model->getNewsModel();
      if ($product->delete($id)) {
        @unlink(WWW_DIR."/pic/list/new_".$id.".jpg");
        @unlink(WWW_DIR."/pic/detail/new_".$id.".jpg");
        $image = $this->model->getImagesModel();
        $image->deleteObjImages('new', $id);
      }

      $this->flashMessage('Záznam byl vymazán');
    }
    $this->redirect('default');
  }

  public function editFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $id = $this->getParam('id');
      $formVals = $form->getValues();

      $product = $this->model->getNewsModel();
      $image = null;
      if ($formVals["imageMain"]->isOk()) $image = $formVals["imageMain"]->toImage();
      unset($formVals["imageMain"]);

      $formVals['newdate'] = NEW \DibiDateTime($formVals['newdate']);
      if ($id > 0) {
        $product->update($id, $formVals);
        $this->flashMessage('Změny uloženy v pořádku');
      } else {
        $id = $product->insert($formVals);
        $this->flashMessage('Nová novinka uložen v pořádku');
      }
      if (isset($image)) {
        $sizes = (array($this->config["PROPICSIZE_BIG"].'xbig', $this->config["PROPICSIZE_DETAIL"].'xdetail', $this->config["PROPICSIZE_LIST"].'xlist'));
        //ulozim obrazek
        $this->saveImage($image, WWW_DIR."/pic/new", 'new_'.$id.".jpg", $sizes);
      }
      $this->redirect('edit', $id);
    }
  }


  /********************* facilities *********************/


  protected function createComponentEditForm() {

    $product = $this->model->getNewsModel();
    $form = new Nette\Application\UI\Form();
    $id = $this->getParam('id');

    //doplnim ciselnik katalogu
    $catalogs = $this->model->getCatalogsModel();


    $form->addtext('newtitle', 'Titulek:', 100, $product->getColProperty('newtitle', 'size'))
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte titulek.');

    $form->addtext('newdate', 'Datum:', 10, $product->getColProperty('newdate', 'size'))
      ->setOption('description', 'dd.mm.rrrr');

    $form->addTextArea('newannot', 'Anotace', 100, 4)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte text anotaci.');

    $form->addTextArea('newtext', 'Text', 100, 8)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte text novinky.');
    $form['newtext']->getControlPrototype()->class('mceEditor');

    $form->addUpload("imageMain", "Hlavní obrázek:")
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete');

    $form->getElementPrototype()->onsubmit('tinyMCE.triggerSave()');
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = [$this, 'editFormSubmitted'];

    return $form;
  }
}
