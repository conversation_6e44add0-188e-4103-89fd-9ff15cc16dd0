<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;

final class MenuPresenter extends BasePresenter {

  public function menuEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $formVals = $form->getValues();

      $menus = $this->model->getMenusModel();
      $id = (int)$this->getParam('id');

      //nastavim srctype a srcid
      $srctype = substr($formVals["target"], 0, 4);
      $srcid = substr($formVals["target"], 4);
      $formVals["menurlsys"] = '';

      switch ($srctype) {
         case 'pag_':
           $formVals["mensrctype"] = "page";
           $formVals["mensrcid"] = $srcid;
           //zjistim zda se jedna o uvodku
           $pagtypid = dibi::fetchSingle("SELECT pagtypid FROM pages WHERE pagid=$srcid");
           switch ($pagtypid) {
              case 1:
                $formVals["menurlsys"] = 'Homepage:default';
                break;
             default:
                $formVals["menurlsys"] = 'Page:detail';
           }

           break;
         case 'url_':
           $formVals["mensrctype"] = "url";
           $formVals["mensrcid"] = $srcid;
           $formVals["menurlsys"] = '';
           break;
      }
      unset($formVals["target"]);
      if ($id > 0) {
        $menus->update($id, $formVals);
        $this->flashMessage('Aktualizováno v pořádku');
      } else {
        $menus->insert($formVals);
        $this->flashMessage('Nový záznam uložen v pořádku');
      }
    }
    $this->redirect('default');
  }

  /********************* view default *********************/

  public function renderDefault() {
    $menus = $this->model->getMenusModel();
    //$dataRows = $menus->fetchAll("SELECT * FROM menus WHERE ORDER BY menorder");
    $this->template->items = $menus->getEnumMenuTree();

    //ciselnik statusu
    $this->template->enum_menstatus = $menus->getEnumMenStatus();
  }

  public function renderEdit($id) {
    $form = new Nette\Application\UI\Form();
    $form = $this['menuEditForm'];

    if (!$form->isSubmitted()) {
      $menus = $this->model->getMenusModel();
      if ($id > 0) {
        $dataRow = $menus->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }

        switch ($dataRow->mensrctype) {
           case 'page':
             $dataRow->target = 'pag_'.$dataRow->mensrcid;
             break;
           case 'gallery':
            $dataRow->target = 'pag_'.$dataRow->mensrcid;
            break;
           case 'url':
            $dataRow->target = 'url_0';
            break;
        }

        $form->setDefaults($dataRow);
        $this->template->dataRow = $dataRow;

      } else {
        $defVals = array();
        $menmasid = $this->getParam("menmasid");
        if ($menmasid > 0) $defVals["menmasid"] = $menmasid;

        $form->setDefaults($defVals);
      }
    }
  }

  public function renderDelete($id) {
    if ($id > 0) {
      $menus = $this->model->getMenusModel();
      $menus->delete($id);
      $this->flashMessage('Záznam byl vymazán');
    }
    $this->redirect('default');
  }


  /********************* facilities *********************/

  protected function createComponentMenuEditForm() {
    $menu = $this->model->getMenusModel();
    $form = new Nette\Application\UI\Form();
    $id = (int)$this->getParam('id');
    $type = $this->getParam('mensrctype');

    //pole pro seznam stranek
    $menpagarr = dibi::query("SELECT concat('pag_', pagid) AS pagid, pagname FROM pages ORDER BY pagname")
      ->fetchPairs('pagid', 'pagname');

    //pole pro ostatní
    $menotharr = array('url_0' => 'URL', 'not_0' => 'Nic');

    $items = array('Stránky' => $menpagarr, 'Jiné' => $menotharr);
    $form->addSelect('target', 'Cíl odkazu:', $items);

    $form->addText('menurl', 'URL:', 100)
      ->setOption('description', 'Vyplňte jen v případě, že cíl odkazu je URL')
      ->addConditionOn($form['target'], Nette\Forms\Form::EQUAL, 'url_0')
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte URL.');

    $form->addSelect('menmasid', 'Nadřízená úroveň:', $menu->getEnumMenuCombo());

    $form->addText('menname', 'Název:', 15)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte název.');

    $form->addText('menorder', 'Pořadí:', 15)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte pořadí.');

    $form->addSelect('menstatus', 'Status:', $menu->getEnumMenStatus())
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte status.');

    $form->addSubmit('save', 'Uložit');

    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'menuEditFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }
}
