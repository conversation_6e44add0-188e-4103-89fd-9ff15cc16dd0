<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  <PERSON>;

final class CommentPresenter extends BasePresenter {
  /** @persistent */
  public $sText = '';

  /** @persistent */
  public $sProCode = '';

  /** @persistent */
  public $sUsrMail = '';

  public function renderDefault() {
    //seznam aktualnich upozorneni
    $coms = $this->model->getCommentsModel();
    $where = "";

    if (!empty($this->sText)) $where .= " (cmtsubj like '%$this->sText%' OR cmttext like '%$this->sText%') AND ";
    if (!empty($this->sUsrMail)) $where .= " cmtmail LIKE '%$this->sUsrMail%' AND ";
    if (!empty($this->sProCode)) $where .= " procode LIKE '%$this->sProCode%' AND ";
    if (!empty($where)) {
      $where = substr($where, 0, -5);
      $where = " WHERE $where";
    }

    $sql = "SELECT * FROM comments
      LEFT JOIN products ON (cmtproid=proid)
      $where 
      ORDER BY cmtdatec DESC";

    $dataSource = $coms->getDataSource($sql);
    $paginator = $this['paginator']->getPaginator();
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    //$dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $dataRows = dibi::fetchAll($sql . " LIMIT $paginator->itemsPerPage OFFSET $paginator->offset");

    $this->template->page = $paginator->page;
    $this->template->comments = $dataRows;
    $this->template->enum_cmtcatid = $coms->getEnumCmtCatId();
  }

  public function renderEdit($id, $reid=0) {
    $pros = $this->model->getProductsModel();

    $form = $this['editForm'];
    if (!$form->isSubmitted()) {
      $id = $this->getParameter('id');
      $this->template->id = $id;
      $coms = $this->model->getCommentsModel();
      if ($id > 0) {
        $row = $coms->load($id);
        if (!$row) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }

        //pokud se jedná odpověď, přesměruji na otázku
        if ($row->cmtreid > 0) {
          $this->redirect("edit#odpoved", $row->cmtreid);
        }

        if ($row->cmtproid > 0) {
          $this->template->product = $pros->load($row->cmtproid);
          $row->cmtprocode = $this->template->product->procode;
        }
        $this->template->comment = $row;
        $form->setDefaults($row);

        //načtu reply pokud je
        if ((int)$row->cmtreid === 0 && $id > 0) {
          $commentReply = dibi::fetch("SELECT * FROM comments WHERE cmtreid=%i", $id);

          if ($commentReply) {
            if ($commentReply->cmtproid >0) {
              $this->template->productReply = $pros->load($commentReply->cmtproid);
              $commentReply->cmtprocode = $this->template->productReply->procode;
            }

            $formReply = $this['editReplyForm'];
            $formReply->setDefaults($commentReply);
            $this->template->commentReply = $commentReply;
          }
        }
      }
    }
  }

  public function renderDelete($id) {
    if ($id > 0) {
      $coms = $this->model->getCommentsModel();
      if ($coms->delete($id)) {
        $this->flashMessage('Záznam byl vymazán');  
      } else {
        $this->flashMessage('Záznam nebyl vymazán', 'err');  
      }
    }
    $this->redirect('default');
  }

  protected function createComponentEditForm() {

    $coms = $this->model->getCommentsModel();
    $adms = $this->model->getAdminsModel();
    $cats = $this->model->getCatalogsModel();

    $form = new Nette\Application\UI\Form();
    $id = (int)$this->getParameter('id');
    $reid = (int)$this->getParameter('reid');

    $com = $coms->load($id);

    $form->addHidden("cmtreid", $reid);

    $form->addSelect("cmtadmid", "Autor", $adms->getEnumAuthors())
      ->setPrompt('');

    $arr = $coms->getEnumCmtCatId();
    
    $form->addSelect("cmtcatid", "Téma 1", $arr)
      ->setPrompt('');
    
    $form->addSelect("cmtcatid2", "Téma 2", $arr)
      ->setPrompt('');

    $form->addSelect('cmtcatcatid', 'Kategorie 1', $cats->getEnumCatalogCombo(0))
      ->setPrompt('1. zařazení do katalogu ...');

    $form->addSelect('cmtcatcatid2', 'Kategorie 2', $cats->getEnumCatalogCombo(0))
      ->setPrompt('2. zařazení do katalogu ...');

    $form->addtext('cmtprocode', 'Kód produktu:', 100);

    $form->addtext('cmtnick', 'Přezdívka:', 100)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');
    
    $form->addtext('cmtmail', 'Email:', 100);
    
    $form->addCheckbox('cmtsendreply', "Zaslat odpověď na email");
    
    $form->addtext('cmtsubj', 'Předmět:', 100)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addCheckbox('cmtaproved', 'Povolit do patičky');

    $form->addTextArea('cmttext', 'Text', 100, 4)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = [$this, 'editFormSubmitted'];

    return $form;
  }

  protected function createComponentEditReplyForm() {

    $coms = $this->model->getCommentsModel();
    $adms = $this->model->getAdminsModel();
    $form = new Nette\Application\UI\Form();
    $id = (int)$this->getParameter('id');

    $form->addHidden("cmtreid", $id);
    $form->addHidden("cmtid");
    $form->addHidden("cmtnick", "");
    $form->addHidden("cmtmail", "");
    $form->addHidden("cmtsubj", "");

    $form->addSelect("cmtadmid", "Autor", $adms->getEnumAuthors())
      ->setPrompt('');

    $arr = $coms->getEnumCmtCatId();

    /*
    $form->addSelect("cmtcatid", "Téma 1", $arr)
      ->setPrompt('');

    $form->addSelect("cmtcatid2", "Téma 2", $arr)
      ->setPrompt('');

    $form->addtext('cmtnick', 'Přezdívka:', 100);

    $form->addtext('cmtmail', 'Email:', 100);
    */

    $form->addtext('cmtprocode', 'Kód produktu:', 100);

    $form->addTextArea('cmttext', 'Text', 100, 4)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addSubmit('saveReply', 'Uložit');
    $form->onSuccess[] = [$this, 'editFormSubmitted'];

    return $form;
  }
  
  public function editFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {

      $formVals = $form->getValues();

      $isReply = FALSE;
      if (isset($form["saveReply"]) && $form["saveReply"]->isSubmittedBy()) {
        $isReply = TRUE;
        $id = (int)$formVals["cmtid"];
      } else {
        $id = $this->getParameter('id');
      }

      unset($formVals["cmtid"]);

      $coms = $this->model->getCommentsModel();
      $pros = $this->model->getProductsModel();

      if (empty($formVals["cmtusrid"])) {
        $formVals["cmtusrid"] = 0;
      }
      if (empty($formVals["cmtip"])) {
        $formVals["cmtip"] = '';
      }

      if (!empty($formVals["cmtprocode"])) {
        $pro = $pros->load($formVals["cmtprocode"], "code");
        if ($pro) {
          $formVals["cmtproid"] = $pro->proid;
        } else {
          $this->flashMessage("Dle zadaného kódu produkt nenalezen.", "err");
        }

      }

      unset($formVals["cmtprocode"]);

      if ($id > 0) {
        $coms->update($id, $formVals);

        $this->flashMessage('Změny uloženy v pořádku');
      } else {
        $id = $coms->insert($formVals);

        //pokud vkládá odpověď a odpověď má mailování odpovědi
        if ($formVals["cmtreid"] > 0) {
          $com = $coms->load($formVals["cmtreid"]);
          if($com && (int)$com->cmtsendreply === 1 && !empty($com->cmtmail)) {
            //budu mailovat že někdo odpověděl
            $template = $this->createTemplate();

            if ($com->cmtproid > 0) {
              $pro = $pros->load($com->cmtproid);
              $template->questionProduct = $pro;
            }
            $template->question = $com;
            $template->reply = $coms->load($id);
            $template->setFile(WWW_DIR.'/../templates/Mails/mailProductCommentReply.latte');
            $this->mailSend($com->cmtmail, "Nová odpověď na Váš dotaz", $template);
          }
        }

        $this->flashMessage('Nový záznam uložen v pořádku');
      }

      if ($isReply) {
        $this->redirect('edit', $formVals["cmtreid"]);
      } else {
        $this->redirect('edit', $id);
      }
    }
  }

  protected function createComponentSearchForm() {
    $catalogs = $this->model->getCatalogsModel();

    $form = new Nette\Application\UI\Form();
    $form->addGroup("Vyhledávání");
    $form->addText("text", "Text", 30)
      ->setDefaultValue($this->sText);
    $form->addText("usrmail", "Email", 20)
      ->setDefaultValue($this->sUsrMail);
    $form->addText("procode", "Kód zboží", 10)
      ->setDefaultValue($this->sProCode);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sText= Null;
        $this->sProCode = Null;
        $this->sUsrMail = Null;
      } else {
        $vals = $form->getValues();
        $this->sText= $vals["text"];
        $this->sProCode = $vals["procode"];
        $this->sUsrMail = $vals["usrmail"];
      }
    }
    $this->redirect("default");
  }
}
