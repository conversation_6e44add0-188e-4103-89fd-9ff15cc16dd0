<?php
namespace AdminModule;

use dibi;
use Nette;

final class MenuIndexPresenter extends BasePresenter {

  public function menuEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $formVals = $form->getValues();

      $menus = $this->model->getMenuIndexsModel();
      //pred ulozenim vymazu z cache prislusny ciselnik
      $cache = $menus->cacheClean();

      $id = (int)$this->getParameter('id');

      //vezmu si obrazek pokud byl odeslan
      $imageFile = null;
      if ($formVals["picture"]->isOk()) {
        $imageFile = $formVals["picture"];
      }
      unset($formVals["picture"]);

      if ($id > 0) {
        $menus->update($id, $formVals);
        $this->flashMessage('Aktualizováno v pořádku');
      } else {
        $id = $menus->insert($formVals);
        $this->flashMessage('Nový záznam uložen v pořádku');
      }
      //upravim a ulozim obrazek
      //$size = ($formVals["meibig"] ? $this->config["MEIPICSIZEBIG"] : $this->config["MEIPICSIZE"]);
      //$this->saveImage($image, WWW_DIR."/pic/menuindex/", "$id.jpg", array($size));

      if ($imageFile !== NULL) {
        $imageFile->move(WWW_DIR."/pic/menuindex/" . "$id.jpg");
        //uložím jako webp
        $image = $imageFile->toImage();
        $image->save(WWW_DIR."/pic/menuindex/" . "$id.webp", 92, Nette\Utils\Image::WEBP);
      }
    }
    $this->redirect('default');
  }

  /********************* view default *********************/

  public function renderDefault() {
    $menus = $this->model->getMenuIndexsModel();
    //$dataRows = $menus->fetchAll("SELECT * FROM menus WHERE ORDER BY menorder");
    $this->template->items = $menus->getEnumMenuIndexTree();

    //ciselnik statusu
    $this->template->enum_menstatus = $menus->getEnumMenStatus();
  }

  public function renderEdit($id) {
    $form = new Nette\Application\UI\Form();
    $form = $this['menuIndexEditForm'];

    if (!$form->isSubmitted()) {
      $menus = $this->model->getMenuIndexsModel();
      if ($id > 0) {
        $dataRow = $menus->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }

        $form->setDefaults($dataRow);
        $this->template->dataRow = $dataRow;

        //zjistim jestli existuje obrazek
        $fileName = WWW_DIR."/pic/menuindex/$id.jpg";
        if(file_exists($fileName)) $this->template->imagePath = "pic/menuindex/$id.jpg";

      } else {
        $defVals = array();
        $menmasid = $this->getParam("meimasid");
        if ($menmasid > 0) $defVals["meimasid"] = $menmasid;

        $form->setDefaults($defVals);
      }
    }
  }

  public function renderDelete($id) {
    if ($id > 0) {
      $menus = $this->model->getMenuIndexsModel();
      $menus->delete($id);
      $this->flashMessage('Záznam byl vymazán');
    }
    $this->redirect('default');
  }


  /********************* facilities *********************/

  protected function createComponentMenuIndexEditForm() {
    $menu = $this->model->getMenuIndexsModel();
    $form = new Nette\Application\UI\Form();
    $id = (int)$this->getParam('id');
    $type = $this->getParam('meisrctype');
    $form->addHidden('meibig', 1);

    $form->addText('meiname', 'Název:', 15)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte název.');

    //pole pro seznam stranek
    $menpagarr = dibi::query("SELECT pagid, pagname FROM pages WHERE pagblock=0 ORDER BY pagname")
      ->fetchPairs('pagid', 'pagname');
    $form->addSelect('meipagid', 'Textová stránka:', $menpagarr)
      ->setPrompt('');
    $catalogs = $this->model->getCatalogsModel();
    $arr = $catalogs->getEnumCatalogCombo();
    unset($arr[0]);

    $form->addSelect('meicatid', 'Katalog:', $arr)
      ->setPrompt('');
    $form->addText('meiprocode', 'Kód zboží:', 15) ;
    $form->addText('meiurl', 'URL:', 100);

    //$form->addSelect('meimasid', 'Nadřízená úroveň:', $menu->getEnumMenuIndexCombo());
    $form->addHidden('meimasid', 0);

    $form->addTextArea('meidesc', 'Texty:', 80, 6) ;
    
    //obrazek
    $form->addUpload('picture', 'Obrázek:')
      ->setOption('description', 'Obrázek nahrávejte ve velikosti '.$this->config["MEIPICSIZEBIG"].'. Obrázek nebude upravován, jen se nahraje tak jak je. Nahráním nového obrázku ten původní přepíšete.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');

    $form->addText('meiorder', 'Pořadí:', 15)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte pořadí.');

    $form->addSelect('meistatus', 'Status:', $menu->getEnumMenStatus())
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte status.');

    $form->addSubmit('save', 'Uložit');

    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'menuEditFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }
}
