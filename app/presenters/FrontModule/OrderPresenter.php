<?php
namespace FrontModule;
use dibi;
use <PERSON>ureka\ShopCertification\Exception;
use Nette;
use <PERSON><PERSON><PERSON><PERSON>\CzQrPayment\QrPayment;
use <PERSON>\Debugger;

final class OrderPresenter extends BasePresenter {

  public function renderStatus($id) {
    $ords = $this->model->getOrdersModel();
    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);

    $dels = $this->model->getDeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);

    $ordId = substr($id, 0,-8);
    $key = substr($id, -8);

    $order = FALSE;

    if (!empty($ordId) && strlen($key) === 8) {
      $order = $ords->load($ordId);
      if ($order) {
        //kontrola klíče
        $key2 = substr(md5($order->ordid.$order->orddatec->getTimestamp()), 0, 8);
        if ($key !== $key2) {
          $this->flashMessage("Neplatné volání stránky.", 'critical');
          $this->redirect("Basket:default");
        }
      }
    }

    if ($order === FALSE) {
      throw new Nette\Application\BadRequestException('Objednávka nebyla nenalezena. Prosím kontaktujte nás.');
    }

    $this->template->payment = $dels->load($order->orddelid);
    $this->template->delivery = $dels->load($this->template->payment->delmasid);

    $this->template->discounts = dibi::fetchAll("SELECT * FROM orditems WHERE oriordid=%i AND oritypid IN (3,6) GROUP BY oriid", $order->ordid);

    $this->template->ordDelRow = dibi::fetch("SELECT * FROM orditems WHERE oritypid=1 AND oriordid=%i", $order->ordid);

    $order->items = dibi::fetchAll("SELECT orditems.*, catpath, catpathids, " . $pros->getSqlFields() . " FROM orditems
INNER JOIN products ON (oriproid=proid)
LEFT JOIN catplaces ON (oriproid=capproid)
LEFT JOIN catalogs ON (catid=capcatid)
WHERE oriordid=%i GROUP BY oriid", $order->ordid);
    $priceSum = 0;
    $priceSumVat = 0;
    $price = 0;
    foreach ($order->items as $key => $row) {
      $price = 0;
      $vatType = (string)$this->config["PRICEVAT"];
      if ($vatType == 'inclvat') {
        $priceSumVat += $row->oriprice * $row->oriqty;
        $vatLevel = (int)$this->config["VATTYPE_" . $row->orivatid];
        $price = round($row->oriprice / (1 + ($vatLevel / 100)) * $row->oriqty, 2);
        $order->items[$key]->oripricenovat = $price;
        $priceSum += $price;
      } else {
        $price = $row->oriprice * $row->oriqty;
        $priceSum += $price;
        $order->items[$key]->oripricenovat = $price;
        $priceSumVat += round($this->getPriceVat($row->oriprice, $row->orivatid) * $row->oriqty, 2);
      }
    }

    $order->ordpricenovat = $priceSum;
    $order->ordpriceinclvat = $priceSumVat;
    $order->orddelprice = dibi::fetchSingle("SELECT oriprice FROM orditems WHERE oritypid=1 AND oriordid=%i", $order->ordid);

    $this->template->order = $order;
    $this->template->blockOrderAccepted = dibi::fetch("SELECT * FROM pages WHERE pagurlkey='objednavka_odeslana' AND pagstatus=0");

    //pokud je platba prevodem, nactu QR kod a info k platbe
    if ($this->template->payment->delcode == 'paybefore') {
      $accNo = ($this->curId == 1 ? $this->config["SERVER_ACCNO"] : '2927827192/1100');
      $qrPlatba = new \QRPlatba($accNo, $order->ordpricevat);
      $qrPlatba->setVariableSym($order->ordcode);
      $qrPlatba->setMessage($order->ordiname . " " . $order->ordilname . ", " . $order->ordicity);
      if ($this->curId == 2) $qrPlatba->setCurrency("EUR");
      $this->template->qrPlatba = urlencode((string)$qrPlatba);
    }

    $this->template->order = $order;

    $zboziConfig = $this->neonParameters["zbozicz"];
    $this->template->zboziConfig = $zboziConfig;

    if ($order->ordpublished != 1) {
      if (IS_PRODUCTION && (int)$order->ordheurekagdpr !== 1 && $this->currency["key"] === 'CZK') {
        //zavolam heureku, overeno zakazniky
        $heurekaConfig = $this->neonParameters["heureka"];
        if (count($order->items) > 0 && !empty($heurekaConfig["IdOverenoZakazniky"])) {
          //require_once LIBS_DIR.'/heureka/HeurekaOvereno.php';
          try {
            $overeno = new \Heureka\ShopCertification($heurekaConfig["IdOverenoZakazniky"]);
            $overeno->setOrderId($order->ordid);
            $overeno->setEmail($order->ordmail);
            foreach ($order->items as $row) {
              $overeno->addProductItemId($row->oriproid);
            }
            $overeno->logOrder();
            $this->template->heurekaConfig = $heurekaConfig;
          } catch (\Exception $e) {
            // handle errors
            Debugger::log("Error: " . $e->getMessage());
            $this->mailSend("<EMAIL>", "Priorita - nová objednávka - heureka ERR", "ORDID: $order->ordid, msg:" . $e->getMessage());
          }
        }
      }

      //vyexportuji obj do pohody
      //$this->exportPohodaOrders($order->ordid);

      //zbozi pokrocile konverze
      if (IS_PRODUCTION && count($order->items) > 0 && !empty($zboziConfig["IdMereniKonverzi"]) && !empty($zboziConfig["secretKey"]) && $this->currency["key"] === 'CZK') {
        try {
          $zbozi = new \ZboziKonverze($zboziConfig["IdMereniKonverzi"], $zboziConfig["secretKey"]);

          $pay = $this->template->payment;
          $del = $this->template->delivery;

          //$zbozi->useSandbox(true);

          $zboziDelCodes = [
            'OSOBNE' => 'VLASTNI_VYDEJNI_MISTA',
            'WEDO' => 'WEDO_ULOZENKA',
            'ULOZENKA' => 'WEDO_ULOZENKA',
          ];

          $delCode = $del->delcode;

          if (isset($zboziDelCodes[$delCode])) {
            $delCode = $zboziDelCodes[$delCode];
          }

          $data = [
            "orderId" => $order->ordcode,
            "email" => $order->ordmail,
            "deliveryPrice"=>$order->orddelprice,
            "otherCosts"=>0,
            "paymentType"=>$pay->delcode,
            "deliveryType"=> $delCode
          ];

          if ((int)$order->ordzbozigdpr == 1) {
            unset($data["email"]);
          }

          $zbozi->setOrder($data);

          //Debugger::barDump($data);
          //Debugger::barDump($zboziConfig);

          foreach ($order->items as $row) {
            $zbozi->addCartItem(array(
                "itemId" => $row->oriproid,
                "productName" => $row->oriname,
                "quantity" => $row->oriqty,
                "unitPrice" => $row->oriprice,
            ));
          }
          $zbozi->send();
        } catch (\Exception $e) {
          Debugger::log("Error: " . $e->getMessage());
          Debugger::log($data);
        }

      }

    }

    //jestli nebyla ještě objednávka publikována do statistik nastavím
    if ($order->ordpublished != 1) {
      $ords->update($order->ordid, array('ordpublished' => 1));
    }

    $this->template->enum_ordstatus = $ords->getEnumOrdStatusFront();


    $enums = $this->model->getEnumcatsModel();
    $this->template->enum_countries = $enums->getEnumCountries();
  }

  protected function createComponentFeedbackForm() {
    $form = $this->createAppForm();

    $form->addTextArea('feedback', "Text zprávy", 40, 4)
      ->setRequired("Prosím vyplňte %label");

    $form->addSubmit('submit', 'Odeslat zprávu');
    $form->onSuccess[] = array($this, 'feedbackFormSubmitted');

    return $form;
  }

  public function feedbackFormSubmitted(Nette\Application\UI\Form $form) {
    $ords = $this->model->getOrdersModel();
    $formVals = $form->getValues();
    $k = $this->getParameter('k');
    $ordId = substr($k, 0,-8);
    $key = substr($k, -8);

    if (!empty($ordId) && strlen($key) === 8) {
      $ord = $ords->load($ordId);
      if ($ord) {
        //kontrola klíče
        $key2 = substr(md5($ord->ordid . $ord->orddatec->getTimestamp()), 0, 8);
        if ($key === $key2) {
          $body = 'Feedback k objednávce č. '.$ord->ordcode.':<br />' . nl2br($formVals["feedback"]);
          try {
            $this->mailSend($this->config["SERVER_MAIL"], 'Feedback k objednávce č. '.$ord->ordcode, $body, $this->config["SERVER_MAIL"], array(), (!empty($ord->ordmail) ? $ord->ordmail : NULL));
            $this->flashMessage("Váš vzkaz byl odeslán. Děkujeme!");
          } catch (Nette\InvalidStateException $e) {
            $this->flashMessage("Vzkaz se nepodařilo odeslat.", 'critical');
          }
        }
      }
    }
    $this->redirect('this');
  }

  public function renderQrCode($id): void
    {
	    $ordId = substr($id, 0,-8);
	    $key = substr($id, -8);

	    $order = FALSE;

	    $ords = $this->model->getOrdersModel();
	    if (!empty($ordId) && strlen($key) === 8) {
	      $order = $ords->load($ordId);
	      if ($order) {
	        //kontrola klíče
	        $key2 = substr(md5($order->ordid.$order->orddatec->getTimestamp()), 0, 8);
	        if ($key !== $key2) {
	          $this->terminate();
	        }
	      }
	    }

		if ($order->curid == 2) {
			$accountNumber = 'CZ70 5500 0000 0021 0600 5044';
			$bankCode = '5500';
		} else {
			$arr = explode('/', $this->config["SERVER_ACCNO"]);
			if (count($arr) != 2) {
				$this->terminate();
			}
			$accountNumber = $arr[0];
			$bankCode = $arr[1];
		}
		$amount = $order->ordpricevat;
		$variableSymbol = $order->ordcode;

		$payment = QrPayment::fromAccountAndBankCode($accountNumber, $bankCode);
        $payment->setAmount($amount)->setCurrency('CZK');

        if ($variableSymbol) {
            $payment->setVariableSymbol($variableSymbol);
        }

        $qrCode = $payment->getQrCode();

        $this->getHttpResponse()->setContentType('image/png');
        $this->sendResponse(new \Nette\Application\Responses\TextResponse($qrCode->getRawString()));
    }

}
