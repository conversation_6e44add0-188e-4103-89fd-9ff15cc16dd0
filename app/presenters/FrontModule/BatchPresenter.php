<?php
namespace FrontModule;
use App\Shippings\UlozenkaApi;
use dibi;
use Model\UsersModel;
use Nette;
use Tracy\Debugger;

final class BatchPresenter extends BasePresenter {

  public function actionCheckParcelStatus() {
    $delCode = (string)$this->getParameter("delCode");

    $delCodeWhere = "'ZASILKOVNA','ZASILKOVNA_NA_ADRESU','ULOZENKA','DPD','DPD_PICKUP'";

    $delCodeWhere = "delmode.delcode IN (" . $delCodeWhere . ") ";
    if (!empty($delCode)) {
      $delCodeWhere .= " AND orddelcode='" . $delCode . "' ";
    }

    //projdu všechny odeslané objednávky a podívám se zda nejsou uz doruceny
    $rows = dibi::fetchAll("
      SELECT ordid, ordmalid, ordcode, ordparcode, ordparid, delmode.delcode as delcode 
      FROM orders 
      INNER JOIN deliverymodes as paymode ON (orddelid=paymode.delid)
      INNER JOIN deliverymodes as delmode ON (paymode.delmasid=delmode.delid)
      WHERE ordstatus IN (3) AND coalesce(ordparcode, '') != '' AND " . $delCodeWhere .
      " AND orddatec>'2022-03-01 00:00:00' order by orddatec");

    $uloApi = new UlozenkaApi($this->neonParameters["ulozenka"], $this->model);
    $uloMallApi = new UlozenkaApi($this->neonParameters["ulozenka_mall"], $this->model);

    $statusApiConfig = $this->neonParameters["dpd"]["status"];
    $dpdStatusApi = new \DpdStatusApi($statusApiConfig["login"], $statusApiConfig["passw"]);
    $zasilkovnaApi = new \ZasilkovnaApi($this->neonParameters["zasilkovna"], $this->model);

    //$malApi = new \MallApi($this->config, $this->model);

    $ords = $this->model->getOrdersModel();

    $cnt = 0;
    foreach ($rows as $row) {

      //echo $row->delcode. "|" . "Obj:$row->ordcode|Bal:$row->ordparcode|MALL:$row->ordmalid<br>";

      $newStatus = NULL;
      $newMall = NULL;
      $date = new \DateTime();
      if ((string)$row->delcode === 'ULOZENKA') {
        if (!empty($row->ordmalid)) {
          //$ret = $uloMallApi->getConsignments($row->ordparcode);
        } else {
          $ret = $uloApi->getConsignments($row->ordparcode);
        }
        if (!empty($ret["id"])) {
          if (!empty($ret["datetime"])) {
            $date = new \DateTime($ret["datetime"]);
          }
          //předáno
          if ((int)$ret["id"] === 10) {
            $newStatus = 4;
            //$newMall = \MallApi::ORDER_STATUS_DELIVERED;
          }
          //vráceno
          if ((int)$ret["id"] === 11) {
            $newStatus = 5;
            //$newMall = \MallApi::ORDER_STATUS_RETURNED;
          }
          //storno
          if ((int)$ret["id"] === 12 || (int)$ret["id"] === 13 || (int)$ret["id"] === 14 || (int)$ret["id"] === 15) {
            $newStatus = 9;
            //$newMall = \MallApi::ORDER_STATUS_CANCELED;
          }
        }
      } else if ((string)$row->delcode === 'DPD' || (string)$row->delcode === 'DPD_PICKUP') {
        $lasScan = "";
        $serviceCode = "";
        $res = $dpdStatusApi->GetParcelStatus(array(
          'parcels' => array(
            // seznam zásilek pro zjištění stavu
            $row->ordparcode
          )
        ));

        if ($res->Response_code === "0" && !empty($res->parcel_status)) {
          $arr = (array($res->parcel_status));
          $lasScan = (string)$arr[0]->Parcel_Status->last_scan_code;
          $serviceCode = (string)$arr[0]->Parcel_Status->service_code;
          $dateStr = (string)$arr[0]->Parcel_Status->scan_date;
          if (!empty($dateStr)) {
            $date = new \DateTime($dateStr);
          }
        }

        if ($lasScan === '13') {
          //doručeno
          if ($serviceCode === '298' || $serviceCode === '299' || $serviceCode === '300' || $serviceCode === '301') {
            //doručeno, ale jedná se o vrácení zásilky odesílateli
            $newStatus = 5;
          } else {
            $newStatus = 4;
          }
        } else if ($lasScan === '18') { //*používá se pro stavy, které bylo nutné vypočítat. Např. doručení, kde kurýr zapomněl načíst balík, nebo vydání zásilky příjemci na výdejním místě Pickup.
          $newStatus = 4;
        } else if ($lasScan === '047' || $lasScan === '049') {
          //nedoručeno
          $newStatus = 5;
        }
      } else if ((string)$row->delcode === 'ZASILKOVNA' || (string)$row->delcode === 'ZASILKOVNA_NA_ADRESU') {
        $ret = $zasilkovnaApi->getParcelStatus($row->ordparcode);

        if ($ret !== FALSE && !empty($ret->statusCode)) {
          if (!empty($ret->dateTime)) {
            $date = new \DateTime($ret->dateTime);
          }
          //předáno
          if ((int)$ret->statusCode === 7) {
            $newStatus = 4;
          }
          //vráceno
          if ((int)$ret->statusCode === 9 || (int)$ret->statusCode === 10) {
            $newStatus = 5;
          }
          //storno
          if ((int)$ret->statusCode === 11) {
            $newStatus = 9;
          }
        }
      }

      //nastavím status
      if ($newStatus !== NULL) {
        try {

          //echo $row->delcode. "|" . $newStatus . "|" . $newMall . "Obj:$row->ordcode|Bal:$row->ordparcode|MALL:$row->ordmalid<br>";

          //nastavím status do mall
          /*$mallStatus = "Není mall";
          if (!empty($row->ordmalid)) {
            $mallStatus = $malApi->getOrder($row->ordmalid)->getStatus();
            if ($mallStatus !== $newMall) {
              $params = array();
              $params["delivered_at"] = $date->format("Y-m-d H:i:s");
              $malApi->setStatus($row->ordmalid, $newMall, FALSE, $params);
            }
          }*/

          $ords->update($row->ordid, array('ordstatus'=>$newStatus));
          $dateString = "(" . $date->format('d.m.Y H:i:s') . ")";

          $ords->logStatus($row->ordid, $newStatus, $date, "API dopravce " . $dateString);

          //mailuji změnu stavu obj
          if (empty($order->ordmalid)) {
            $order = $ords->load((int)$row->ordid);
            $this->mailOrderChanged($order);
          }

          $cnt++;
        } catch (\Exception $e) {
          Debugger::log("MalId:" . $row->ordmalid  . "|" . $e->getMessage() . "<br>");
        }
      }
    }
    echo "Načteno " . count($rows) . " objednávek, " . $cnt . " objednávek aktualizováno.";
    $this->terminate();
  }

  //přepočítá informace o variantách v master produktu
  public function actionRecalcProductsVariants() {
    $pros = $this->model->getProductsModel();
    $rows = dibi::fetchAll("SELECT promasid FROM products WHERE promasid>0 AND prostatus=0 GROUP BY promasid");
    foreach ($rows as $row) {
      $pros->recalcProductVariants($row->promasid);
    }
    $this->terminate();
  }


  public function actionCheckParcelStatusPrev() {
    //projdu všechny odeslané objednávky a podívám se zda nejsou uz doruceny
    $rows = dibi::fetchAll("
      SELECT ordid, ordmalid, ordparcode 
      FROM orders 
      INNER JOIN deliverymodes as paymode ON (orddelid=paymode.delid)
      INNER JOIN deliverymodes as delmode ON (paymode.delmasid=delmode.delid)
      WHERE ordstatus=3 AND 
      delmode.delcode='ULOZENKA'");

    $uloApi = new UlozenkaApi($this->neonParameters["ulozenka"], $this->model);
    $uloMallApi = new UlozenkaApi($this->neonParameters["ulozenka_mall"], $this->model);
    $malApi = new \MallApi($this->config, $this->model);
    $ords = $this->model->getOrdersModel();

    $cnt = 0;
    foreach ($rows as $key => $row) {
      //dotaz na stav balíku přes API Ulozenky
      if (!empty($row->ordmalid)) {
        $ret = $uloMallApi->getConsignments($row->ordparcode);
      } else {
        $ret = $uloApi->getConsignments($row->ordparcode);
      }
      $newStatus = NULL;
      $newMall = NULL;
      $date = NULL;
      if (!empty($ret["id"])) {
        if (!empty($ret["datetime"])) {
          $date = new \DateTime($ret["datetime"]);
        }
        //předáno
        if ((int)$ret["id"] === 10) {
          $newStatus = 4;
          $newMall = \MallApi::ORDER_STATUS_DELIVERED;
          $cnt ++;
        }
        //vráceno
        if ((int)$ret["id"] === 11) {
          $newStatus = 5;
          $newMall = \MallApi::ORDER_STATUS_RETURNED;
          $cnt ++;
        }
        //storno
        if ((int)$ret["id"] === 12) {
          $newStatus = 9;
          $newMall = \MallApi::ORDER_STATUS_CANCELED;
          $cnt ++;
        }
      }
      //nastavím status
      if ($newStatus !== NULL) {

        $ords->update($row->ordid, array('ordstatus'=>$newStatus));
        $ords->logStatus($row->ordid, $newStatus, $date);

        //nastavím status do mall
        if (!empty($row->ordmalid)) {
          $params = array();
          if ($date !== NULL) {
            $params["delivered_at"] = $date;
          }
          $malApi->setStatus($row->ordmalid, $newMall, FALSE, $params);

          echo $newStatus . "|" . $newMall . "<br>";
        }
      }
    }
    echo "Načteno " . count($rows) . " objednávek, " . $cnt . " objednávek aktualizováno.";
    $this->terminate();
  }


  /**
   *
   *
   * @throws Nette\Application\AbortException
   */
  public function actionNewMallOrder() {
    //jen pro testování
    $this->terminate();
    $config = $this->config;
    $config["neonParameters"] = $this->neonParameters;
    $malApi = new \MallApi($config, $this->model);
    $malApi->setNewOrder('10728152301');
  }

  public function actionCheckMallClosedOrders() {
    //projdu všechny odeslané objednávky a podívám se zda nejsou uz doruceny
    $rows = dibi::fetchAll("
    SELECT ordpricevat, ordid, ordmalid, ordcode, orldatec, ordstatus
      FROM orders
      inner join orders_log on orlordid=ordid and orlstatus=4
      WHERE coalesce(ordmalid, '') != '' AND  date(orldatec) between '2019-03-01' AND '2019-03-31'
      ORDER BY orldatec
    ");

    $config = $this->config;
    $config["neonParameters"] = $this->neonParameters;
    $malApi = new \MallApi($config, $this->model);

    $cnt = 0;
    $cntDif = 0;
    $cntFill = 0;
    $cntEmpty = 0;
    foreach ($rows as $key => $row) {
      $cnt++;

      //načtu objednávku z mall
      $order = $malApi->getOrder($row->ordmalid);
      $data = $order->getData();
      if (empty($data["delivered_at"])) {
        $cntEmpty++;
        $dateStr = $row->orldatec->format('Y-m-d H:i:s');
        echo "EMP:" . $row->ordcode . "\t" . $row->ordmalid  . "\t" . $dateStr . "\n";
        $params["delivered_at"] = $row->orldatec;
        if ($data["status"] === \MallApi::ORDER_STATUS_DELIVERED) {
          try {
            $malApi->setStatus($row->ordmalid, \MallApi::ORDER_STATUS_DELIVERED, FALSE, $params);
            echo "SET:" . $row->ordcode . "\t" . $row->ordmalid  . "\t" . \MallApi::ORDER_STATUS_DELIVERED . "\t" . $params["delivered_at"] . "\n";
          } catch (Exception $e) {
            echo "ERR:" . $row->ordcode . "\t" . $row->ordmalid  . "\t" . $dateStr . "\t" . $e->getMessage() . "\n";
          }
        } else {
          echo "ERR:" . $row->ordcode . "\t" . $row->ordmalid  . "\t" . $dateStr . "\t jiný status: " . $data["status"] . "\n";
        }

      } else {
        $cntFill ++;
        $mallDate = new \DateTime($data["delivered_at"]);
        $mallDateStr = $mallDate->format('Y-m-d');
        $dateStr = $row->orldatec->format('Y-m-d');
        if ($mallDateStr !== $dateStr) {
          echo "DIF:" . $row->ordcode . "\t" . $row->ordmalid  . "\t" . $dateStr . "\t" . $mallDateStr . "\n";
          $cntDif ++;
        } else {
          echo "OK :" . $row->ordcode . "\t" . $row->ordmalid  . "\t" . $dateStr . "\t" . $mallDate->format('Y-m-d H:i:s') . "\n";
        }
      }
    }

    echo "Celkem " . $cnt . ", datum nevyplněno $cntEmpty , vyplněno: $cntFill, rozdílné datumy: $cntDif" ;
    $this->terminate();
  }

  public function actionSynchronizeParcelStatus() {
    //projdu všechny odeslané objednávky a podívám se zda nejsou uz doruceny
    $rows = dibi::fetchAll("
      SELECT ordid, ordmalid, ordparcode, ordstatus
      FROM orders 
      INNER JOIN deliverymodes as paymode ON (orddelid=paymode.delid)
      INNER JOIN deliverymodes as delmode ON (paymode.delmasid=delmode.delid)
      WHERE ordstatus IN (4,5,9) AND coalesce(ordmalid, '') != '' AND  
      delmode.delcode='ULOZENKA'");

    $config = $this->config;
    $config["neonParameters"] = $this->neonParameters;
    $malApi = new \MallApi($config, $this->model);

    $openOrders = array();
    $shippedOrders = array();
    $deliveredOrders = array();
    $returnedOrders = array();
    $cancelledOrders = array();

    $arr = $malApi->getOrdersByStatus(\MallApi::ORDER_STATUS_OPEN);
    if (is_array($arr)) {
      $openOrders = array_map(function($var){ return (string)$var; }, $arr);
      $openOrders = array_flip($openOrders);
    }

    $arr = $malApi->getOrdersByStatus(\MallApi::ORDER_STATUS_SHIPPED);
    if (is_array($arr)) {
      $shippedOrders = array_map(function($var){ return (string)$var; }, $arr);
      $shippedOrders = array_flip($shippedOrders);
    }

    $arr = $malApi->getOrdersByStatus(\MallApi::ORDER_STATUS_DELIVERED);
    if (is_array($arr)) {
      $deliveredOrders = array_map(function($var){ return (string)$var; }, $arr);
      $deliveredOrders = array_flip($deliveredOrders);
    }

    $arr = $malApi->getOrdersByStatus(\MallApi::ORDER_STATUS_RETURNED);
    if (is_array($arr)) {
      $returnedOrders = array_map(function($var){ return (string)$var; }, $arr);
      $returnedOrders = array_flip($returnedOrders);
    }

    $arr = $malApi->getOrdersByStatus(\MallApi::ORDER_STATUS_CANCELED);
    if (is_array($arr)) {
      $cancelledOrders = array_map(function($var){ return (string)$var; }, $arr);
      $cancelledOrders = array_flip($cancelledOrders);
    }

    $cnt = 0;
    foreach ($rows as $key => $row) {

      if (empty($row->ordparcode)) {
        continue;
      }
      $params = array();

      echo $row->ordid . "|" . $row->ordmalid . "|Status:" . $row->ordstatus . "|";

      if ($row->ordstatus == 4 && !isset($deliveredOrders[$row->ordmalid])) {

        //zjistím jestli nemá jiný konečná stav:
        if (isset($returnedOrders[$row->ordmalid])) {
          echo "!!! jiný finální status: " . \MallApi::ORDER_STATUS_RETURNED;
        } else if (isset($cancelledOrders[$row->ordmalid])) {
          echo "!!! jiný finální status: " . \MallApi::ORDER_STATUS_CANCELED;
        } else {
          //nastavím status do mall
          //zjistím datum
          $date = dibi::fetchSingle("SELECT orldatec from orders_log WHERE orlordid=%i", $row->ordid, " AND orlstatus=%i", $row->ordstatus, " ORDER BY orldatec DESC");
          if ($date) {
            $params["delivered_at"] = $date;
            echo $date->format('Y-m-d H:i:s');
          }
          $malApi->setStatus($row->ordmalid, \MallApi::ORDER_STATUS_DELIVERED, FALSE, $params);
          $cnt ++;
        }

      } else if ($row->ordstatus == 5 && !isset($returnedOrders[$row->ordmalid])) {
        //zjistím jestli nemá jiný konečná stav:
        if (isset($deliveredOrders[$row->ordmalid])) {
          echo "!!! jiný finální status: " . \MallApi::ORDER_STATUS_DELIVERED;
        } else if (isset($cancelledOrders[$row->ordmalid])) {
          echo "!!! jiný finální status: " . \MallApi::ORDER_STATUS_CANCELED;
        } else {
          //nastavím status do mall
          $malApi->setStatus($row->ordmalid, \MallApi::ORDER_STATUS_RETURNED);
          $cnt++;
        }
      } else if ($row->ordstatus == 9 && !isset($cancelledOrders[$row->ordmalid])) {
        //zjistím jestli nemá jiný konečná stav:
        if (isset($deliveredOrders[$row->ordmalid])) {
          echo "!!! jiný finální status: " . \MallApi::ORDER_STATUS_DELIVERED;
        } else if (isset($returnedOrders[$row->ordmalid])) {
          echo "!!! jiný finální status: " . \MallApi::ORDER_STATUS_RETURNED;
        } else {
          //nastavím status do mall
          $malApi->setStatus($row->ordmalid, \MallApi::ORDER_STATUS_CANCELED);
          $cnt++;
        }
      }

      echo "Status: $row->ordstatus, Mall status: ";
      if (isset($openOrders[$row->ordmalid])) {
        echo " otevřená ";
      }
      if (isset($shippedOrders[$row->ordmalid])) {
        echo " odesláno ";
      }
      if (isset($deliveredOrders[$row->ordmalid])) {
        echo " dodáno ";
      }
      if (isset($returnedOrders[$row->ordmalid])) {
        echo " vráceno ";
      }
      if (isset($cancelledOrders[$row->ordmalid])) {
        echo " storno ";
      }
      echo "<br>";
    }
    echo "Načteno " . count($rows) . " objednávek, " . $cnt . " objednávek aktualizováno.";
    $this->terminate();
  }

  public function actionReplaceOldUrls() {
    $runUpdate = FALSE;
    //kometare
    echo "*** Prohledávám komentáře<br>";    
    $rows = dibi::fetchAll("SELECT cmtid, cmttext FROM comments WHERE cmttext LIKE '%.html,%' OR cmttext LIKE '%vyrobce-%'");
    foreach ($rows as $key => $row) {
      $res = $this->replaceUrl($row->cmttext, $row->cmtid);
      if ($res === FALSE) {
        //neni treba aktualizovat
      } else {
        if ($runUpdate === TRUE && is_string($res)) dibi::query("UPDATE comments SET cmttext=%s", $res, " WHERE cmtid=%i", $row->cmtid);
      }
    }
    echo "*** Prohledávám catalog<br>";    
    $rows = dibi::fetchAll("SELECT catid, catdesc FROM catalogs WHERE catdesc LIKE '%.html,%' OR catdesc LIKE '%vyrobce-%'");
    foreach ($rows as $key => $row) {
      $res = $this->replaceUrl($row->catdesc, $row->catid);
      if ($res === FALSE) {
        //neni treba aktualizovat
      } else {
        if ($runUpdate === TRUE && is_string($res)) dibi::query("UPDATE catalogs SET catdesc=%s", $res, " WHERE catid=%i", $row->catid);
      }
    }
    
    echo "*** Prohledávám popis zbozi<br>";    
    $rows = dibi::fetchAll("SELECT proid, prodesc FROM products WHERE prodesc LIKE '%.html,%' OR prodesc LIKE '%vyrobce-%'");
    foreach ($rows as $key => $row) {
      $res = $this->replaceUrl($row->prodesc, $row->proid);
      if ($res === FALSE) {
        //neni treba aktualizovat
      } else {
        if ($runUpdate === TRUE && is_string($res)) dibi::query("UPDATE products SET prodesc=%s", $res, " WHERE proid=%i", $row->proid);
      }
    }
    
    echo "*** Prohledávám články<br>";    
    $rows = dibi::fetchAll("SELECT artid, artbody FROM articles WHERE artbody LIKE '%.html,%' OR artbody LIKE '%vyrobce-%'");
    foreach ($rows as $key => $row) {
      $res = $this->replaceUrl($row->artbody, $row->artid);
      if ($res === FALSE) {
        //neni treba aktualizovat
      } else {
        if ($runUpdate === TRUE && is_string($res)) dibi::query("UPDATE articles SET artbody=%s", $res, " WHERE artid=%i", $row->artid);
      }
    }
    
    echo "*** Prohledávám page<br>";    
    $rows = dibi::fetchAll("SELECT pagid, pagbody FROM pages WHERE pagbody LIKE '%.html,%' OR pagbody LIKE '%vyrobce-%'");
    foreach ($rows as $key => $row) {
      $res = $this->replaceUrl($row->pagbody, $row->pagid);
      if ($res === FALSE) {
        //neni treba aktualizovat
      } else {
        if ($runUpdate === TRUE && is_string($res)) dibi::query("UPDATE pages SET pagbody=%s", $res, " WHERE pagid=%i", $row->pagid);
      }
    }
      
    $this->terminate(); 
  }
  
  private function replaceUrl($text, $srcid) {

    //odkazy na detail zbozi
    $regexStr = '/http:\/\/www.goldfitness\.cz\/.(.(?!"))*\.html,det,([0-9]*)/mi';
    $notChanged = TRUE;
    while (TRUE){
      $matches = array();
      $ret = preg_match($regexStr, $text, $matches);
      if ($ret == 0) break;
      
      $id = (int)$matches[2];
      if ($id > 0) {
        $row = dibi::fetch("SELECT proid, concat(proname, ' ', proname2) AS proname, prokey FROM products WHERE proid=%i", $id);
        $newLink = $this->link("//Product:detail", $row->proid, (!empty($row->prokey) ? $row->prokey : Nette\Utils\Strings::webalize($row->proname)));
        echo "ID:".$srcid."<br>".$matches[0]."<br>".$newLink."<br><br>";
        $text = str_replace($matches[0], $newLink, $text);
        $notChanged = FALSE;
      }
    }
    
    //odkazy na detail v katalogu
    $regexStr = '/http:\/\/www.goldfitness\.cz\/.(.(?!"))*\.html,kat,([0-9]*)/mi';
    while (TRUE){
      $matches = array();
      $ret = preg_match($regexStr, $text, $matches);
      if ($ret == 0) break;
      
      $id = (int)$matches[2];
      if ($id > 0) {
        $row = dibi::fetch("SELECT catid, catname, catkey FROM catalogs WHERE catid=%i", $id);
        $urlkey = (!empty($row->catkey) ? $row->catkey : Nette\Utils\Strings::webalize($row->catname));
        //presmeruju na novy
        $newLink = $this->link('//Catalog:detail', $id, $urlkey);
        echo $matches[0]."<br>".$newLink."<br><br>";
        $text = str_replace($matches[0], $newLink, $text);
        $notChanged = FALSE;
      }
    }
    
    //odkazy na detail clanky
    $regexStr = '/http:\/\/www.goldfitness\.cz\/.(.(?!"))*\.html,rec,([0-9]*)/mi';
    while (TRUE){
      $matches = array();
      $ret = preg_match($regexStr, $text, $matches);
      if ($ret == 0) break;
      
      $id = (int)$matches[2];
      if ($id > 0) {
        $row = dibi::fetch("SELECT artid, artname FROM articles WHERE artid=%i", $id);
        $urlkey = (!empty($row->arturlkey) ? $row->arturlkey : Nette\Utils\Strings::webalize($row->artname));
        //presmeruju na novy
        $newLink = $this->link('//Article:detail', $id, $urlkey);
        echo $matches[0]."<br>".$newLink."<br><br>";
        $text = str_replace($matches[0], $newLink, $text);
        $notChanged = FALSE;
      }
    }
    
    /*
    //odkazy na vyrobce
    $regexStr = '/http:\/\/www.goldfitness\.cz\/vyrobce-(.*).html/mi';
    while (TRUE){
      $matches = array();
      $ret = preg_match($regexStr, $text, $matches);
      if ($ret == 0) break;
      
      $vyrobce = $matches[1];
      if (!empty($vyrobce)) {
        $vyrobce = str_replace('-', ' ', urldecode($vyrobce));
        //zkusím najít výrobce s tím jménem
        $man = dibi::fetch("SELECT manid, manname FROM manufacturers WHERE manname=%s", $vyrobce);
        if ($man) {
          $urlkey = Nette\Utils\Strings::webalize($man->manname);
          //presmeruju na novy
          $newLink = $this->link('//Manufacturer:detail', $man->manid, $urlkey);
          echo $matches[0]."<br>".$newLink."<br><br>";
          $text = str_replace($matches[0], $newLink, $text);
          $notChanged = FALSE;
        } else {
          continue;
        }
      }
    }
    */
        
    if ($notChanged) {
      return(FALSE);
    } else {
      return($text);  
    }
  }
  
  public function actionNightBatch() {
    $k = (string)$this->getParam('k');
    if ($k != '942a13a8cb5840') $this->terminate();
    //vyberu vsechny produkty co maji nastavenou novinku a datum vytvoreni je starsi nez X dni.
    
    $days = (int)$this->config["PRODUCT_NEWAGE"];

    if ($days > 0) {
      dibi::query("UPDATE products SET protypid2=0 WHERE protypid2=1 AND DATE(prodatec + INTERVAL ".$days." DAY) < CURDATE()");
    }

    file_get_contents('http://www.google.com/webmasters/tools/ping?sitemap=https://www.goldfitness.cz/sitemap');
    file_get_contents('http://www.bing.com/webmaster/ping.aspx?siteMap=https://www.goldfitness.cz/sitemap');
    
    $this->terminate();
  }

  public function actionMailWatchDogs() {
    $users = array();
    $dogs = $this->model->getWatchdogsModel();
    //naskladněno
    $rows = dibi::fetchAll("
      SELECT dogid, dogmail, proid, procode, proname, prokey, proprice1a
      FROM watchdogs
      INNER JOIN products ON (proid=dogproid)
      WHERE dogstore=1 AND proqty>0  
    ");
    foreach ($rows as $key => $row) {
      $users[$row->dogmail]['onstock'][$row->proid] = $row;
      //vymazu doga
      $dogs->delete($row->dogid);
    }

    //slevneno
    $rows = dibi::fetchAll("
      SELECT dogid, dogmail, proid, procode, proname, prokey, proprice1a 
      FROM watchdogs
      INNER JOIN products ON (proid=dogproid)
      WHERE dogprice=1 AND 
      (
        (dogpricevalue>proprice1a && dogpricename='1a') OR 
        (dogpricevalue>proprice1b && dogpricename='1b') OR 
        (dogpricevalue>proprice1c && dogpricename='1c') OR 
        (dogpricevalue>proprice1d && dogpricename='1d') OR
        (dogpricevalue>proprice1e && dogpricename='1e') OR
        (dogpricevalue>proprice2a && dogpricename='2a') OR 
        (dogpricevalue>proprice2b && dogpricename='2b') OR 
        (dogpricevalue>proprice2c && dogpricename='2c') OR 
        (dogpricevalue>proprice2d && dogpricename='2d') OR
        (dogpricevalue>proprice2e && dogpricename='2e')
      )   
    ");
    foreach ($rows as $key => $row) {
      $users[$row->dogmail]['price'][$row->proid] = $row;
      //vymazu doga
      $dogs->delete($row->dogid);
    }

    //mailuji
    $mailTemplate = $this->createTemplate();
    $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailWatchDog.latte');
    foreach ($users as $mail => $data) {
      $mailTemplate->data = $data;
      $this->mailSend($mail, 'Zlevnili jsme nebo naskladnili co Vás zajímá', $mailTemplate);
      //$this->mailSend('<EMAIL>', 'Zlevnili jsme nebo naskladnili co Vás zajímá', $mailTemplate);
    }
    $this->terminate();
  }

  public function actionMailing() {
    //$this->terminate();
    $k = (string)$this->getParameter('k');
    if ($k != '942a13a8cb5840') $this->terminate();
    dibi::getConnection()->onEvent = NULL;
    
    $pros = $this->model->getProductsModel();
    $mails = $this->model->getMailsModel();
    $mailings = $this->model->getMailingsModel();
    $usersTable = $mailings->usersTable;
    
    //zjistim jestli neni nachystane nejake mailovani
    $rows = dibi::fetchAll("SELECT * FROM mailings WHERE mamstatus=1 AND (mamdate<=CURDATE() OR mamdate IS NULL)");
    foreach ($rows as $mailing) {

      dibi::begin();

      //naplnim produkty
      $prosArr = explode("\n", trim($mailing->mamproducts));
      
      $arrDesc1 = explode("\n", trim($mailing->mamdesc1));
      $arrDesc2 = explode("\n", trim($mailing->mamdesc2));
      $arrDesc3 = explode("\n", trim($mailing->mamdesc3));
      
      $cnt = 0;
      $products = array();
      foreach ($prosArr as $proid) {
        if (!empty($proid)) {
          $pro = $pros->load($proid);
          if ($pro) {
            $pro->promamdesc1 = (isset($arrDesc1[$cnt]) ? $arrDesc1[$cnt] : "");
            $pro->promamdesc2 = (isset($arrDesc2[$cnt]) ? $arrDesc2[$cnt] : "");
            $pro->promamdesc3 = (isset($arrDesc3[$cnt]) ? $arrDesc3[$cnt] : "");
            $products[$cnt] = $pro;
            $cnt ++;
          }
        }

      }
      //nastavim sablonu
      $mailTemplate = $this->createTemplate();
      $mailTemplate->products = $products;
      
      
      $mailTemplate->config = $this->config;
      $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailing.latte');
      //adresati
      $users = $mailings->getUsers($mailing);

      $mailTemplate->mailing = $mailing;
      $mailsCnt = 0;
      foreach ($users as $user) {
        //kontrola formatu mailu
        if (!Nette\Utils\Validators::isEmail($user->usrmail)) {
          dibi::query("UPDATE ".$usersTable." SET usrmaillist=0 WHERE usrid=%i", $user->usrid, " LIMIT 1");
          continue;
        }  
        
        $mailTemplate->usrid = $user->usrid;
        $mailTemplate->loKey = md5($user->usrid.$user->usrdatec);    
        $mailTemplate->usrmail = $user->usrmail;
        $mailTemplate->mamid = $mailing->mamid;
        $mailTemplate->isMail = TRUE;   
        $date = (empty($mailing->mamdate) ? NULL : $mailing->mamdate);
        $templateHtml = (string)$mailTemplate;
        $data = array(
          'malmamid' => $mailing->mamid,
          'malusrid' => $user->usrid,
          'maldate' => $date,
          'malfrom' => $this->config["SERVER_MAILING_MAIL"],
          'malmail' => $user->usrmail,
          'malsubject' => $mailing->mamsubject,
          'malbody' => $templateHtml,
        );
        $mails->insert($data);
        $mailsCnt ++;
      }  
      
      //nastavim ze se maily odesilaji
      $mailings->update($mailing->mamid, array(
        'mamstatus'=>2,
        'mamcntall'=>$mailsCnt,
        'mamdatestart'=> new \DateTime()
      ));

      dibi::commit();

      $this->terminate();
    }

    //spoustim mailovaci davku
    $rows = dibi::fetchAll("SELECT * FROM mails WHERE (maldate<=CURDATE() OR maldate IS NULL) AND malstatus=0 ORDER BY malid LIMIT ".(int)$this->config["SERVER_MAILING_CNT"]);
    $cnt = 0;
    $cntSleep = 0;
    $mamIsBlocked = array();
    foreach ($rows as $row) {
      
      //zjistim jestli prislusny mailing neni pozastaveny
      if (!isset($mamIsBlocked[$row->malmamid])) {
        $mamIsBlocked[$row->malmamid] = (bool)dibi::fetchSingle("SELECT mamstatus=4 FROM mailings WHERE mamid=%i", $row->malmamid);    
      }
      if ($mamIsBlocked[$row->malmamid]) continue;

      if (!Nette\Utils\Validators::isEmail($row->malmail)) {
        $mails->delete($row->malid);
        continue;
      }

      $mail = new Nette\Mail\Message(); 
      //$mail->mailer->commandArgs = '-f'.$row->malfrom;
      if (!empty($row->malmamid)) $mail->setHeader('X-mamid', $row->malmamid);
      if (!empty($row->malusrid)) $mail->setHeader('X-usrid', $row->malusrid);
      if (!empty($row->malserid)) $mail->setHeader('X-serid', $row->malserid);
      $mail->setFrom($row->malfrom, $this->config["SERVER_NAMESHORT"]);
      $mail->setReturnPath($row->malfrom);
      $mail->addReplyTo($row->malfrom);
      $mail->addTo($row->malmail);
      $mail->setSubject($row->malsubject);
      $mail->setHtmlBody($row->malbody);
      try {
        $this->mailMail($mail);
        $mails->delete($row->malid);
      } catch (\Exception $e) {
        \Tracy\Debugger::log($row->malid.":".$e->getMessage());
        $mails->delete($row->malid);
      }
      /*
      if ($cnt % 10 == 0) {
        sleep(30);
        $cntSleep++;
      }
      */
      $cnt ++;
      unset($mail);                                               
    }
    foreach ($mamIsBlocked as $key => $value) {
      if ($value) \Tracy\Debugger::log("Maily pro mailing ID $key jsou pozastaveny.");
    }
    //\Tracy\Debugger::log("Odmailováno $cnt mailů.");
    
    //zjistim jestli neni nejaka davka uz cela odmailovana
    $rows = dibi::fetchAll("SELECT mamid FROM mailings WHERE mamstatus=2");
    foreach ($rows as $row) {
      $cnt = (int)dibi::fetchSingle("SELECT COUNT(malmamid) FROM mails WHERE malmamid=%i", $row->mamid, " AND malstatus=0");
      if ($cnt == 0) {
        $mailings->update($row->mamid, array(
          'mamstatus'=>3, 
          'mamleft'=>0,
          'mamdateend'=> new \DateTime() 
        ));
      } else {
        $mailings->update($row->mamid, array('mamleft'=>$cnt));
      }
    }
    $this->terminate();
  }

  /**
   * vytvoří mailling pro  GDPR
   *
   * @return bool
   * @throws \DibiException
   * @throws Nette\Application\AbortException
   */
  public function actionGdprMailling() {
    $this->terminate();
    $mails = $this->model->getMailsModel();
    $mailings = $this->model->getMailingsModel();
    $usrs = $this->model->getUsersModel();

    $mamid = $mailings->insert(array(
      'mamsubject' => 'Chceme s vámi zůstat v kontaktu',
      'mamdatec' => new Nette\Utils\DateTime(),

    ));
    $mailing = $mailings->load($mamid);
    if (!$mailing) {
      return false;
    }
    //navolit zda všem posílat nebo jen registrovaným: usrmaillist=1
    $rows = dibi::fetchAll("select * from users WHERE usrstatus=0");
    $mailsCnt = 0;
    foreach ($rows as $key => $row) {

      $usrs->update($row->usrid, array(
        'usrmailvcode' => $this->getVerifyCode(),
        'usrmailverified' => 0,
      ));

      $row = $usrs->load($row->usrid);
      $mailTemplate = $this->createTemplate();
      $mailTemplate->user = $row;
      $mailTemplate->mailing = $mailing;
      $mailTemplate->key = substr(md5($row->usrid.$row->usrdatec.$row->usrmailvcode), 0, 8);
      $mailTemplate->config = $this->config;
      $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/gdprMailing.latte');
      $templateHtml = (string)$mailTemplate;
      $data = array(
        'malmamid' => $mailing->mamid,
        'malusrid' => $row->usrid,
        'maldate' => New Nette\Utils\DateTime(),
        'malfrom' => $this->config["SERVER_MAILING_MAIL"],
        'malmail' => $row->usrmail,
        'malsubject' => $mailing->mamsubject,
        'malbody' => $templateHtml,
      );
      $mails->insert($data);
      $mailsCnt ++;
      $usrs->logEvent($row->usrid, UsersModel::EVENT_MAIL_VERIFICATION_SEND);
    }
    //nastavim ze se maily odesilaji
    $mailings->update($mailing->mamid, array(
      'mamstatus'=>2,
      'mamcntall'=>$mailsCnt,
      'mamdatestart'=> new \DateTime()
    ));
    $this->terminate();
  }

  /**
   * ověření zák účtu GDPR
   *
   * @param $i
   * @param int $t
   * @param string $k
   * @throws \DibiException
   */
  public function actionVerifyAccount($i, $t, $k) {
    /*
     * 1 - zůstat registrovaný, přihlásit maillingu
     * 2 - zůstat registrovaný
     * 3 - smazat
    */
    $t = (int)$t;

    $usrs = $this->model->getUsersModel();
    $usr = $usrs->load($i);
    $ok = FALSE;
    if ($usr) {
      $uKey = substr(md5($usr->usrid.$usr->usrdatec.$usr->usrmailvcode), 0, 8);
      if ($uKey === $k) {
        $ok = TRUE;
        if ($t === 1 || $t === 2) {
          //verifikuji mail a zaloguji souhlas s uchováním maillist
          $usrs->update($i, array(
            'usrmailvcode' => NULL,
            'usrmailverified' => 1,
            'usrmaillist' => $t === 1 ? 1 : 0,
          ));
          $usrs->logEvent($i, UsersModel::EVENT_MAIL_VERIFIED);
          if ($t === 1) {
            $usrs->logEvent($i, UsersModel::EVENT_MAILLIST_ADD);
            $usrs->logEvent($i, UsersModel::EVENT_GDPR);
          } else if ($t === 2) {
            $usrs->logEvent($i, UsersModel::EVENT_MAILLIST_REM);
            $usrs->logEvent($i, UsersModel::EVENT_GDPR);
          }
          $this->flashMessage("Děkujeme za Vaši důvěru. Vítejte zpět!");

        } else if ($t === 3) {
          //vymazu
          $usrs->clearPersonalData($i);

          $this->flashMessage("Mrzí nás, že odcházíte, ale kdykoliv vás rádi přivítáme zpět!");
        }
      } else {
        $this->flashMessage("Nejspíš se snažíte reagovat na naši prosbu ohledně GDPR opakovaně. Pokud chcete svou volbu změnit, prosím kontaktujte nás.");
        $ok = TRUE;
      }
    }
    if ($ok === FALSE) {
      $this->flashMessage("Omlouváme se, ale něco se pokazilo a požadovanou akci se nepodařil provést.");
    }
    $this->redirect("Article:detail", 45, 'desatero-proc-nakupovat-u-nas');
  }

  public function actionFillCatPlacesToVariants() {
    $pros = $this->model->getProductsModel();
    $masters = dibi::fetchAll("SELECT proid FROM products where proismaster=1");

    foreach ($masters as $master) {
      $catIds = [];
      //zjistím zařazení master položky v katalogu
      $catPlaces = dibi::fetchAll("SELECT capcatid FROM catplaces WHERE capproid=%i", $master->proid);

      if (is_array($catPlaces) && count($catPlaces) > 0) {
        foreach ($catPlaces as $cat) {
          $catIds[] = $cat->capcatid;
        }

        //načtu všechny varianty master položky
        $variants = dibi::fetchAll("SELECT proid FROM products WHERE promasid=%i", $master->proid);
        foreach ($variants as $variant) {
          $pros->updateCatPlace((int)$variant->proid, $catIds);
        }
      }
    }

    echo "hotovo";
    $this->terminate();

  }

    /**
   * aktualizuje soupis balíků na poštu
   */
  public function actionUpdatePostCodes() {
    $zipFile = TEMP_DIR . "/post_codes.zip";
    $csvFile = TEMP_DIR . "/zv_psc_adr.csv";
    $fileUrl = 'https://www.ceskaposta.cz/documents/10180/3738087/db_psc_a.zip/d28208a4-279a-10e3-205a-e44ccc6214b2';

    $f = file_put_contents($zipFile, fopen($fileUrl, 'r'), LOCK_EX);
    if (FALSE === $f) {
      die("Couldn't write to file.");
    }
    $zip = new \ZipArchive;
    $res = $zip->open($zipFile);
    if ($res === TRUE) {
      $zip->extractTo(TEMP_DIR);
      $zip->close();
    }

    if (file_exists($csvFile)) {
      $vals = array();
      if (($handle = fopen($csvFile, "r")) !== FALSE) {
          while (($data = fgetcsv($handle, 1000, ";")) !== FALSE) {
            if ($data[0] === 'PSC') {
              continue;
            }
            $vals[] = array(
              'cpccode' => (int)$data[0],
            );
          }
          fclose($handle);
      }

      unlink($zipFile);
      unlink($csvFile);

      //aktualizuji pošty
      $start = dibi::fetchSingle("SELECT Now()");
      foreach ($vals as $item) {
        $cpid = (int)dibi::fetchSingle("SELECT cpcid FROM czechpostcodes WHERE cpccode=%s", $item["cpccode"]);
        if ($cpid === 0) {
          $item['cpcdatec'] = new \DateTime();
          dibi::insert('czechpostcodes', $item)
            ->execute(\dibi::IDENTIFIER);
        } else {
          $item['cpcdateu'] = new \DateTime();
          dibi::update('czechpostcodes', $item)
            ->where('cpcid=%i', $cpid)
            ->execute();
        }
      }
      dibi::query("DELETE from czechpostcodes WHERE coalesce(cpcdateu,cpcdatec) < %d", $start);
    }
    $this->terminate();
  }

  public function actionRecalcVolumes() {
    $rows = dibi::fetchAll("
        SELECT proid, proname, prpname, prpvalue, proweight, provolume, provolumeunit from proparams
        inner join products on proid=prpproid
        where prpname = 'Balení'
    ");

    $pros = $this->model->getProductsModel();

    /*
    $enum = array(
      'kg' => 'kg',
      'g' => 'g',
      'gramů' => 'g',
      'l' => 'l',
      'litr' => 'l',
      'ml' => 'ml',
      'ml.' => 'ml',
      'kapsle' => 'kap',
      'kapslí' => 'kap',
      'cps.' => 'kap',
      'kps' => 'kap',
      'tableta' => 'tab',
      'tablet' => 'tab',
      'tbl.' => 'tab',
      'tbl' => 'tab',
      'ampule' => 'amp',
      'ampulí' => 'amp',
      'amp.' => 'amp',
      'pár' => 'par',
    );
    foreach ($rows as $row) {
      foreach ($enum as $val => $key) {
        $bal = $row->prpvalue;

        if (substr($bal, -1) === '.') {
          $bal = substr($bal, 0, -1);
        }

        $bal = str_replace('sáček', "", $bal);
        $bal = str_replace('(sklo)', "", $bal);
        $bal = str_replace('(prášek)', "", $bal);
        $bal = str_replace('Čistá hmotnost', "", $bal);
        $bal = str_replace('Čistá váha:', "", $bal);
        $bal = str_replace('v plechovce', "", $bal);
        $bal = str_replace('plech', "", $bal);
        $bal = str_replace(['-', ' '], ['', ''], $bal);

        if (str_contains($bal, $val)) {
          $rem = str_replace($val, "", $bal);
          $rem = str_replace([',', ' '], ['.', ''], $rem);
          if (is_numeric($rem)) {
            $pros->update($row->proid, ["provolume" => (double)$rem, "provolumeunit" => $key]);
            break;
          } else {
            if ($key === 'g') {
              preg_match_all('/\(([^\)]*)\)/', $bal, $matches);

              foreach ($matches[1] as $m2) {
                if (str_contains($m2, $val)) {
                  $rem = str_replace($val, "", $m2);
                  $rem = str_replace([',', ' '], ['.', ''], $rem);
                  if (is_numeric($rem)) {
                    $pros->update($row->proid, ["provolume" => (double)$rem, "provolumeunit" => $key]);
                    break;
                  }
                }
              }
            }
            if ($key === 'kg' && $row->proweight > 0) {
              $pros->update($row->proid, ["provolume" => (double)$row->proweight, "provolumeunit" => $key]);
            }
          }
        }
      }
    }
    */
    $rows = dibi::fetchAll("
      SELECT proid, promasid, proname, provolume, provolumeunit
      from products
      where promasid > 0 AND provolume is null
    ");

    $masters = [];
    foreach ($rows as $row) {
      if (!isset($masters[$row->promasid])) {
        $masters[$row->promasid] = $pros->load($row->promasid);
      }
      if (!empty($masters[$row->promasid]) && !empty($masters[$row->promasid]->provolume) && !empty($masters[$row->promasid]->provolumeunit)) {
        $pros->update($row->proid, ["provolume" => (double)$masters[$row->promasid]->provolume, "provolumeunit" => $masters[$row->promasid]->provolumeunit]);
      }
    }

    $this->terminate();
  }

  public function actionAddPackageSize() {
    $pros = $this->model->getProductsModel();
    $prps = $this->model->getProParamsModel();
    /*
    1 => 'Sypká',
    2 => 'Kapsle',
    3 => 'Tekutá',
    4 => 'Tablety',
    */

    //aminokyseliny
    $catInfo[4] = [
      //dle ID formy
      '' => [
        "size" => '17x12x12',
      ],
      //Sypká
      1 => [
        "size" => '17x12x12',
        "weight" => 0.7,
      ],
      //Kapsle
      2 => [
        "size" => '12x8x8',
        "weight" => 0.3,
      ],
      //Tekutá
      3 => [
        "size" => '17x12x12',
      ],
      //Tablety
      4 => [
        "size" => '17x12x12',
      ],
    ];

    //proteiny
    $catInfo[3] = [
      //dle ID formy
      '' => [
        "size" => '30x20x20',
      ],
      //Sypká
      1 => [
        "size" => '30x20x20',
      ],
      //Kapsle
      2 => [
        "size" => '30x20x20',
        "weight" => 0.3,
      ],
      //Tekutá
      3 => [
        "size" => '30x20x20',
      ],
      //Tablety
      4 => [
        "size" => '30x20x20',
      ],
    ];

    //sacharidy
    $catInfo[2] = [
      //dle ID formy
      '' => [
        "size" => '30x20x20',
      ],
      //Sypká
      1 => [
        "size" => '30x20x20',
      ],
      //Kapsle
      2 => [
        "size" => '30x20x20',
        "weight" => 0.3,
      ],
      //Tekutá
      3 => [
        "size" => '30x20x20',
      ],
      //Tablety
      4 => [
        "size" => '30x20x20',
      ],
    ];

    //kreatin
    $catInfo[7] = [
      //dle ID formy
      '' => [
        "size" => '17x12x12',
      ],
      //Sypká
      1 => [
        "size" => '17x12x12',
      ],
      //Kapsle
      2 => [
        "size" => '17x12x12',
        "weight" => 0.3,
      ],
      //Tekutá
      3 => [
        "size" => '17x12x12',
      ],
      //Tablety
      4 => [
        "size" => '17x12x12',
      ],
    ];

    //Anabolizéry
    $catInfo[5] = [
      //dle ID formy
      '' => [
        "size" => '17x12x12',
      ],
      //Sypká
      1 => [
        "size" => '17x12x12',
      ],
      //Kapsle
      2 => [
        "size" => '17x12x12',
        "weight" => 0.3,
      ],
      //Tekutá
      3 => [
        "size" => '17x12x12',
      ],
      //Tablety
      4 => [
        "size" => '17x12x12',
      ],
    ];

    //Spalovače
    $catInfo[8] = [
      //dle ID formy
      '' => [
        "size" => '12x8x8',
      ],
      //Sypká
      1 => [
        "size" => '12x8x8',
      ],
      //Kapsle
      2 => [
        "size" => '12x8x8',
        "weight" => 0.3,
      ],
      //Tekutá
      3 => [
        "size" => '12x8x8',
      ],
      //Tablety
      4 => [
        "size" => '12x8x8',
      ],
    ];

    //Vitamíny, minerály a antioxidanty
    $catInfo[32] = [
      //dle ID formy
      '' => [
        "size" => '12x8x8',
      ],
      //Sypká
      1 => [
        "size" => '12x8x8',
      ],
      //Kapsle
      2 => [
        "size" => '12x8x8',
        "weight" => 0.3,
      ],
      //Tekutá
      3 => [
        "size" => '12x8x8',
      ],
      //Tablety
      4 => [
        "size" => '12x8x8',
      ],
    ];

   //Kloubní výživa
    $catInfo[9] = [
      //dle ID formy
      '' => [
        "size" => '15x12x12',
      ],
      //Sypká
      1 => [
        "size" => '15x12x12',
      ],
      //Kapsle
      2 => [
        "size" => '15x12x12',
        "weight" => 0.3,
      ],
      //Tekutá
      3 => [
        "size" => '15x12x12',
      ],
      //Tablety
      4 => [
        "size" => '15x12x12',
      ],
    ];

    //Stimulanty a energizery
    $catInfo[48] = [
      //dle ID formy
      '' => [
        "size" => '17x12x12',
      ],
      //Sypká
      1 => [
        "size" => '17x12x12',
      ],
      //Kapsle
      2 => [
        "size" => '17x12x12',
        "weight" => 0.3,
      ],
      //Tekutá
      3 => [
        "size" => '17x12x12',
      ],
      //Tablety
      4 => [
        "size" => '17x12x12',
      ],
    ];

    //iontové nápoje
    $catInfo[1] = [
      //dle ID formy
      '' => [
        "size" => '30x10x10',
      ],
      //Sypká
      1 => [
        "size" => '30x10x10',
      ],
      //Kapsle
      2 => [
        "size" => '30x10x10',
        "weight" => 0.3,
      ],
      //Tekutá
      3 => [
        "size" => '30x10x10',
      ],
      //Tablety
      4 => [
        "size" => '30x10x10',
      ],
    ];

    //tyčinky
    $catInfo[10] = [
      //dle ID formy
      '' => [
        "size" => '15x4x4',
      ],
      //Sypká
      1 => [
        "size" => '15x4x4',
      ],
      //Kapsle
      2 => [
        "size" => '15x4x4',
        "weight" => 0.3,
      ],
      //Tekutá
      3 => [
        "size" => '15x4x4',
      ],
      //Tablety
      4 => [
        "size" => '15x4x4',
      ],
    ];

    foreach ($catInfo as $catId => $info) {
      $rows = dibi::fetchAll($pros->getSqlCatalogList($catId));

      foreach ($rows as $row) {
        $parCode = "Rozměry obalu (cm)";
        $par = dibi::fetch("SELECT prpid, prpvalue FROM proparams WHERE prpproid=? AND prpname=? AND prptypid=0", $row->proid, $parCode);

        $proForId = "";
        if ($row->proforid > 0) {
          $proForId = (int)$row->proforid;
        }

        if (!isset($info[$proForId])) {
          continue;
        }

        if ($par) {
          if ($par->prpvalue != $info[$proForId]["size"]) {
            $vals = [
              "prpvalue" => $info[$proForId]["size"],
            ];
            $prps->update($par->prpid, $vals);
          }
        } else {
          $vals = [
            "prpproid" => $row->proid,
            "prptypid" => 0,
            "prpname" => 'Rozměry obalu (cm)',
            "prpvalue" => $info[$proForId]["size"],
          ];
          $prps->insert($vals);
        }

        //hmotnost včetně obalu pokud je vyplněná
        if (isset($info[$proForId]["weight"]) && (double)$row->proweight === 0.0) {
          $pros->update($row->proid, ["proweight" => (double)$info[$proForId]["weight"]]);
        }

      }
    }
    $this->terminate();
  }

    public function actionHeurekaMailling() {
        $rows = dibi::fetchAll("select ordid, ordusrid, ordheurekagdpr, ordheurekamailblock, ordmail, ordheurekamail, orddatec, date(DATE_SUB(NOW(), INTERVAL 6 DAY))
            from orders
            WHERE
                ordstatus IN (8,3,4) AND
                ordheurekagdpr!=1 AND
                ordheurekamailblock=0 AND
                ordcurid=1 AND
                ordheurekamail IS NULL AND
                date(orddatec) = date(DATE_SUB(NOW(), INTERVAL 6 DAY)) AND
                (
                ordusrid IS NULL OR ordusrid=0 OR
                ordusrid IN (SELECT usrid
                             FROM users
                             WHERE usrprccat IN ('a', 'b'))
                )");
        $ordIds = [];
        foreach ($rows as $row) {
            $mailTemplate = $this->createTemplate();
            $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailHeurerkaReference.latte');
            //mailuji
            try {
                $this->mailSend($row->ordmail, "Poděkování za nákup", $mailTemplate);
                //Debugger::log((string)$mailTemplate);
            } catch (Nette\InvalidStateException $e) {
                Debugger::log($e, Debugger::EXCEPTION);
            }
            $ordIds[] = $row->ordid;
        }

        //nastavim, že se maily odeslaly
        if (count($ordIds) > 0) {
            dibi::query("update orders set ordheurekamail=now() WHERE ordid in (%i)", $ordIds);
        }
        Debugger::log(count($ordIds));
        $this->terminate();
    }
}