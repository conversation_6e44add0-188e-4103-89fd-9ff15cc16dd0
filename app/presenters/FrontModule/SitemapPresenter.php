<?php
namespace FrontModule;
use dibi;
use Model\CatalogsModel;
use Model\ManufacturersModel;
use Model\ProductsModel;
use Model\SitemapsModel;
use Nette;

final class SitemapPresenter extends BasePresenter {

  private $filterFormsIdName = array();
  private $filterFormsIdKey = array();
  private $filterFormsKeyId = array();
  private $filterTypesIdName = array();
  private $filterTypesIdKey = array();
  private $filterTypesKeyId = array();
  private $filterManufacturersIdName = array();
  private $filterManufacturersIdKey = array();
  private $filterManufacturersKeyId = array();


  protected function startup() {
    parent::startup();
    //naplnim ciselniky
    $pros = $this->model->getProductsModel();

    //forma
    $this->filterFormsIdName = $pros->getEnumProForId();

    foreach ($this->filterFormsIdName as $id => $name) {
      $key = Nette\Utils\Strings::webalize($name);
      $this->filterFormsIdKey[$id] = $key;
      $this->filterFormsKeyId[$key] = $id;
    }

    //typ
    $this->filterTypesIdName = $this->getEnumTypes();

    foreach ($this->filterTypesIdName as $id => $name) {
      $key = Nette\Utils\Strings::webalize($name);
      $this->filterTypesIdKey[$id] = $key;
      $this->filterTypesKeyId[$key] = $id;
    }

    //vyrobce
    $this->filterManufacturersIdName = dibi::query("SELECT manid, manname FROM manufacturers where manstatus=0")->fetchPairs('manid', 'manname');
    foreach ($this->filterManufacturersIdName as $id => $name) {
      $key = Nette\Utils\Strings::webalize($name);
      $this->filterManufacturersIdKey[$id] = $key;
      $this->filterManufacturersKeyId[$key] = $id;
    }
  }

  public function renderProducts() {
    //produkty
    $this->template->products=dibi::fetchAll("
      SELECT pro.*, coalesce(pro.prodateu, pro.prodatec) AS moddate 
      FROM products AS pro
      INNER JOIN catplaces ON (capproid=pro.proid)
      INNER JOIN catalogs ON (capcatid=catid)
      LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
      WHERE pro.proprice".$this->curId."a > 0 AND catstatus=0 AND pro.prostatus=0 AND  
      (promas.prostatus IS NULL OR promas.prostatus = 0) AND coalesce(promas.pronoindex, pro.pronoindex, 0)=0
      GROUP BY pro.proid");
    unset($this->template->manufacturers);
    $this->setView("default");
  }

  public function renderImages() {
    //produkty
    $rows = dibi::fetchAll("
      SELECT pro.*, coalesce(pro.prodateu, pro.prodatec) AS moddate 
      FROM products AS pro
      INNER JOIN catplaces ON (capproid=pro.proid)
      INNER JOIN catalogs ON (capcatid=catid)
      LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
      WHERE pro.proprice".$this->curId."a > 0 AND catstatus=0 AND pro.prostatus=0 AND  
      (promas.prostatus IS NULL OR promas.prostatus = 0) AND coalesce(promas.pronoindex, pro.pronoindex, 0)=0
      GROUP BY pro.proid");

    foreach ($rows as $key => $row) {
      //zjistim obrazky produktu
      $proPicNameBase = ($row->propicname != "" ? trim($row->propicname) : $row->procode);
      $picPath = "pic/product/big/";
      $images = array();

      $proPicName = $proPicNameBase.'.jpg';
      if (file_exists(WWW_DIR . "/" . $picPath . $proPicName)) {
        $proPicName = rawurlencode($proPicName);
        $images[0]["url"] = $picPath.$proPicName;
      }
      for ($i = 1; $i <= 10; $i++) {
        $proPicName = $proPicNameBase . "_" . $i .'.jpg';
        if (file_exists(WWW_DIR . "/" . $picPath.$proPicName)) {
          $proPicName = rawurlencode($proPicName);
          $images[$i]["url"] = $picPath.$proPicName;
        }
      }
      $rows[$key]->images = $images;
    }

    $this->template->products = $rows;

    //články
    $rows = dibi::fetchAll("SELECT * FROM articles WHERE artstatus=0");
    foreach ($rows as $key => $row) {
      $images = array();
      $path = "pic/art/list/";
      $fileName = 'art_'.$row->artid.'.jpg';

      //hlavni obrázek
      if (file_exists(WWW_DIR . "/" .$path.$fileName)) {
        $fileName = rawurlencode($fileName);
        $images[0]["url"] = $path.$fileName;
        $images[0]["title"] = empty($row->arttitle) ? $row->artname :$row->arttitle;
      }

      //přílohy
      $atts = dibi::fetchAll("SELECT * FROM attachments where ataartid=%i", $row->artid);
      $picPath = "files/";
      $i = 1;
      foreach ($atts as $aKey => $att) {
        if (file_exists(WWW_DIR . "/" . $picPath . $att->atafilename)) {
          $fileName = rawurlencode($att->atafilename);
          $images[$i]["url"] = $picPath . $fileName;
          $images[$i]["title"] = $att->ataname;
          $i++;
        }
      }
      $rows[$key]->images = $images;
    }
    $this->template->articles = $rows;
  }


  public function renderDefault() {

    //katalog + všechny kombinace
    $this->template->catalogs = dibi::fetchAll('SELECT * FROM sitemaps WHERE sitrows>0');

    //manufacturers
    $this->template->manufacturers = dibi::fetchAll("SELECT manid, manname, coalesce(mandateu, mandatec) as moddate FROM manufacturers where manstatus=0");

    //clanky
    $this->template->articles = dibi::fetchAll("
      SELECT *, coalesce(artdateu, artdatec) AS moddate 
      FROM articles 
      WHERE artstatus=0 AND artrobots='index,follow'
    ");

    //stranky
    $this->template->pages = dibi::fetchAll("
      SELECT *, coalesce(pagdateu, pagdatec) AS moddate 
      FROM pages  
      WHERE pagblock=0 AND pagstatus=0 AND pagrobots='index,follow' 
    ");
  }

  /**
   * projde nejstarší odkazy v sitemapě a zaktualizuje u katalogogvých odkazů počet odkazů a poslední aktualizaci
   *
   * @throws Nette\Application\AbortException
   * @throws \DibiException
   */

  public function actionRecalc() {
    dibi::getConnection()->onEvent = NULL;

    echo date('H:i:s').'|Recalc<br>';

    $pros = $this->model->getProductsModel();
    $sits = $this->model->getSitemapsModel();

    $sql = "
      SELECT COUNT(proid) AS cnt, MAX(coalesce(prodateu, prodatec)) AS dateu FROM products
      INNER JOIN catplaces ON (capproid=proid)
      INNER JOIN catalogs ON (capcatid=catid)
      WHERE prostatus=0 AND catstatus=0 AND catnoindex=0 AND promasid=0 AND pronoindex=0";

    $cntCat = 0;
    $cntKey = 0;
    $cntMat = 0;
    $cntMaf = 0;
    $cntMtf = 0;
    $where = '';

    //načtu nejstaší záznamy
    $rows = dibi::fetchAll("SELECT * FROM sitemaps ORDER BY COALESCE(sitdateu,sitdatec) ASC LIMIT 5000");
    foreach ($rows as $row) {
      //catalog
      if ($row->sittypid === 'cat') {
        $where = '';
        $cntCat++;
      }

      if ($row->sittypid === 'key') {
        $cntKey++;
        //keys
        //analyzuji klicove slovo
        $where = $this->getSqlForKeyName($row->sitkeykey);
      }

      //KATALOG - VÝROBCE - TYP
      if ($row->sittypid === 'mat') {
        $cntMat++;
        $whereTyp = $this->getSqlForTypName($row->sitkeytyp);
        $whereMan = $this->getSqlForManName($row->sitkeyman);

        $where = $whereTyp . $whereMan;
      }

      //KATALOG - VÝROBCE - FORMA
      if ($row->sittypid === 'maf') {
        $cntMaf++;
        $whereForm = $this->getSqlForFormName($row->sitkeyfor);
        $whereMan = $this->getSqlForManName($row->sitkeyman);

        $where = $whereForm . $whereMan;
      }

      //KATALOG - VÝROBCE - TYP - FORMA
      if ($row->sittypid === 'mtf') {
        $cntMtf++;
        $whereForm = $this->getSqlForFormName($row->sitkeyfor);
        $whereTyp = $this->getSqlForTypName($row->sitkeytyp);
        $whereMan = $this->getSqlForManName($row->sitkeyman);

        $where = $whereForm . $whereTyp . $whereMan;
      }

      $r = dibi::fetch($sql, " AND catpathids LIKE '%|".$row->sitcatid."|%'", $where);

      $vals = array(
        'sitrows' => $r->cnt,
        'sitlastmod' => $r->dateu,
        'sitdateu' => New \DateTime()
      );
      $sits->update($row->sitid, $vals);
    }
    echo date('H:i:s').'|konec<br>
    Katalog: '.$cntCat.'<br>
    Klíčová slova: '.$cntKey.'<br>
    výrobce - typ: '.$cntMat.'<br>
    výrobce - forma: '.$cntMaf.'<br>
    výrobce - typ - forma: '.$cntMtf.'<br>
    ';
    $this->terminate();
  }

  /*
   * vymaže skryté, zakázané
   */
  public function actionClear() {
    $cnt = 0;
    //skryté neindexovatelné kategorie vymažu
    $rows = dibi::fetchAll('SELECT catid FROM catalogs WHERE catnoindex>0 OR catstatus>0 OR catlevel < 2');
    foreach ($rows as $key => $row) {
      $cnt++;
      dibi::query('DELETE FROM sitemaps WHERE sitcatid=%i', $row->catid);
    }
    //vymazu skryté výrobce
    $rows = dibi::fetchAll('SELECT manid, manname FROM manufacturers WHERE manstatus>0');
    foreach ($rows as $key => $row) {
      $manKey = Nette\Utils\Strings::webalize($row->manname);
      dibi::query('DELETE FROM sitemaps WHERE sitkeyman=%s', $manKey);
      $cnt++;
    }
    echo 'Vymazáno:' . $cnt;
    $this->terminate();
  }


  /**
   * aktualizuje seznam odkazu sitemapě
   *
   * https://www.goldfitness.cz/sitemap/update/cat
   * https://www.goldfitness.cz/sitemap/update/key
   * https://www.goldfitness.cz/sitemap/update/mat
   * https://www.goldfitness.cz/sitemap/update/mtf?step=1
   * https://www.goldfitness.cz/sitemap/update/mtf?step=2
   * https://www.goldfitness.cz/sitemap/update/mtf?step=3
   * https://www.goldfitness.cz/sitemap/update/maf?step=1
   * https://www.goldfitness.cz/sitemap/update/maf?step=2
   * https://www.goldfitness.cz/sitemap/update/maf?step=3
   * vymaže skryté, zakázané
   * https://www.goldfitness.cz/sitemap/clear
   *
   * @param $id
   * @param $step
   * @throws Nette\Application\AbortException
   * @throws \DibiException
   */
  public function actionUpdate($id, $step=1) {
    dibi::getConnection()->onEvent = NULL;

    $mans = $this->model->getManufacturersModel();
    $pros = $this->model->getProductsModel();
    $sits = $this->model->getSitemapsModel();

    echo date('H:i:s').' - '.$id.'<br>';

    $catalogs = dibi::query("
    SELECT *, coalesce(catdateu, catdatec) AS moddate
    FROM catalogs
    WHERE catnoindex=0 AND catstatus=0 AND catlevel >= 2
    ORDER BY catlevel, catorder")->fetchAssoc('catid');#
    $keywords = array();
    //vyrobci
    $arr = $this->filterManufacturersIdName;
    $this->template->mans = array();
    foreach ($arr as $manId => $keyword) {
      if (!empty(trim($keyword))) {
        $manufacturers[$manId] = $keyword;
        $keywords[] = $keyword;
      }
    }

    //forma
    $forms = $pros->getEnumProForId();
    foreach ($forms as $item) {
      $keywords[] = $item;
    }

    //typ
    $types = array(
      'skladem' => 'Skladem',
      'dopravazdarma' => 'Doprava zdarma',
    );

    $cntNewCat = 0;
    $cntNewKey = 0;
    $cntNewMtf = 0;
    $cntNewMat = 0;
    $cntNewMaf = 0;
    $i = 1;
    foreach ($catalogs as $catalog) {
      if ($i === 4) {
        $i = 1;
      }
      $catKey = $this->getCatKey($catalog);
      if ($id == 'cat') {
        //katalog
        $sit = dibi::fetch('SELECT sitid, sitcatkey FROM sitemaps WHERE sitcatkey=%s', $catKey, ' AND sittypid=\'cat\'');
        if ($sit === FALSE) {
          $vals = array(
            'sitcatid' => $catalog->catid,
            'sitcatkey' => $catKey,
            'sittypid' => 'cat',
          );
          $sits->insert($vals);
          $cntNewCat++;
        }
      }

      if ($id == 'key') {
        //klicove slova
        foreach ($keywords as $keyword) {
          $keyKey = Nette\Utils\Strings::webalize($keyword);
          $sit = dibi::fetch('SELECT sitid, sitcatkey, sitkeykey FROM sitemaps WHERE sitcatkey=%s', $catKey, ' AND sitkeykey=%s', $keyKey, ' AND sittypid=\'key\'');
          if ($sit === FALSE) {
            $vals = array(
              'sitcatid' => $catalog->catid,
              'sitcatkey' => $catKey,
              'sitkeykey' => $keyKey,
              'sittypid' => 'key',
            );
            $sits->insert($vals);
            $cntNewKey++;
          }
        }
      }

      if ($id == 'mtf') {
        if ($step !== $i) {
          $i++;
          continue;
        }
        foreach ($manufacturers as $manname) {
          foreach ($types as $typname) {
            foreach ($forms as $forname) {
              $keyMan = Nette\Utils\Strings::webalize($manname);
              $keyTyp = Nette\Utils\Strings::webalize($typname);
              $keyForm = Nette\Utils\Strings::webalize($forname);

              $sit = dibi::fetch('
                SELECT sitid, sitcatkey, sitkeykey 
                FROM sitemaps 
                WHERE 
                  sitcatkey=%s', $catKey, ' AND 
                  sitkeyman=%s', $keyMan, ' AND 
                  sitkeytyp=%s', $keyTyp, ' AND 
                  sitkeyfor=%s', $keyForm, ' AND 
                  sittypid=\'mtf\'');
              if ($sit === FALSE) {
                $vals = array(
                  'sitcatid' => $catalog->catid,
                  'sitcatkey' => $catKey,
                  'sitkeyman' => $keyMan,
                  'sitkeytyp' => $keyTyp,
                  'sitkeyfor' => $keyForm,
                  'sittypid' => 'mtf',
                );
                $sits->insert($vals);
                $cntNewMtf++;
              }
            }
          }
        }
      }

      // CAT - MAN - TYP
      if ($id == 'mat') {
        foreach ($manufacturers as $manname) {
          foreach ($types as $typname) {
            $keyMan = Nette\Utils\Strings::webalize($manname);
            $keyTyp = Nette\Utils\Strings::webalize($typname);

            $sit = dibi::fetch('
              SELECT sitid, sitcatkey, sitkeykey 
              FROM sitemaps 
              WHERE 
                sitcatkey=%s', $catKey, ' AND 
                sitkeyman=%s', $keyMan, ' AND 
                sitkeytyp=%s', $keyTyp, ' AND 
                sittypid=\'mat\'');
            if ($sit === FALSE) {
              $vals = array(
                'sitcatid' => $catalog->catid,
                'sitcatkey' => $catKey,
                'sitkeyman' => $keyMan,
                'sitkeytyp' => $keyTyp,
                'sittypid' => 'mat',
              );
              $sits->insert($vals);
              $cntNewMat++;
            }
          }
        }
      }

      // CAT - MAN - FORM
      if ($id == 'maf') {
        if ($step !== $i) {
          $i++;
          continue;
        }
        foreach ($manufacturers as $manname) {
          foreach ($forms as $forname) {
            $keyMan = Nette\Utils\Strings::webalize($manname);
            $keyFor = Nette\Utils\Strings::webalize($forname);

            $sit = dibi::fetch('
              SELECT sitid, sitcatkey, sitkeykey 
              FROM sitemaps 
              WHERE 
                sitcatkey=%s', $catKey, ' AND 
                sitkeyman=%s', $keyMan, ' AND 
                sitkeyfor=%s', $keyFor, ' AND 
                sittypid=\'maf\'');
            if ($sit === FALSE) {
              $vals = array(
                'sitcatid' => $catalog->catid,
                'sitcatkey' => $catKey,
                'sitkeyman' => $keyMan,
                'sitkeyfor' => $keyFor,
                'sittypid' => 'maf',
              );
              $sits->insert($vals);
              $cntNewMaf++;
            }
          }
        }
      }

      $i++;
    }

    echo 'catalog: nových: ' . $cntNewCat.'<br>';
    echo 'catalog - keywords: nových: ' . $cntNewKey.'<br>';
    echo 'catalog - Man+Typ+Form: nových: ' . $cntNewMtf.'<br>';
    echo 'catalog - Man+Typ: nových: ' . $cntNewMat.'<br>';
    echo 'catalog - Man+For: nových: ' . $cntNewMaf.'<br>';
    echo date('H:i:s');
    $this->terminate();
  }

  /**
  * vraci URL klic katalogu
  *
  * @param dibirow $row
  * @return URL klic
  */
  private function getCatKey($row) {
    return (!empty($row->catkey) ? $row->catkey : Nette\Utils\Strings::webalize($row->catname));
  }


  private function getSqlForKeyName($keyName) {
    //vyzkouším postupně všechny a pokud vrátá nějaký SQL tak už dále nepokračuji
    //typ
    $where = $this->getSqlForTypName($keyName);
    if (!empty($where)) {
      return $where;
    }
    //forma
    $where = $this->getSqlForFormName($keyName);
    if (!empty($where)) {
      return $where;
    }
    //výrobce
    $where = $this->getSqlForManName($keyName);
    if (!empty($where)) {
      return $where;
    }
    return $where;
  }

  private function getSqlForTypName($keyName) {
    $where = '';
    switch ($keyName) {
      case 'skladem': //Skladem
        $where = " AND proaccess=0 ";
        break;
      case 'novinka': //Novinky
        $where = " AND protypid2=1 ";
        break;
      case 'akce': //Akce
        $where = " AND protypid=1 ";
        break;
      case 'doprava-zdarma': //Doprava zdarma
        $where = " AND prodelfree=1 ";
        break;
      case 'tip': //tip
        $where = " AND protypid3=1 ";
        break;
      case 'zlate-dny': //zlate dny
        $where = " AND protypid4=1 ";
        break;
    }
    return $where;
  }

  private function getSqlForManName($keyName) {
    $where = '';
    if (!empty($this->filterManufacturersKeyId[$keyName])) {
      $id = $this->filterManufacturersKeyId[$keyName];
      $where = " AND promanid=" . $id . " ";
    }
    return $where;
  }

  private function getSqlForFormName($keyName) {
    $where = '';
    if (!empty($this->filterFormsKeyId[$keyName])) {
      $id = $this->filterFormsKeyId[$keyName];
      $where = " AND proforid=" . $id . " ";
    }
    return $where;
  }
}
