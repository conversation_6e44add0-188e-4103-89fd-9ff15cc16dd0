<?php

namespace FrontModule;

use Nette\Application\BadRequestException;
use <PERSON>\Debugger;

final class EcomailPresenter extends BasePresenter {

  public function actionWebHook() {
    $k = (string)$this->getParameter("k");

    if ($k !== '27e30875c89564a1ca3ec29b89c6163c') {
      throw new BadRequestException('Položka nenalezena', '404');
    }

    $json = file_get_contents('php://input');
    $data = json_decode($json, TRUE);

    if (!empty($data)) {
      Debugger::log($json);
    }

    if (!empty($data["payload"]["email"])) {
      $users = $this->model->getUsersModel();
      $usr = $users->load($data["payload"]["email"], "mail");
      if ($usr) {
        if ((string)$data["payload"]["status"] === "UNSUBSCRIBED" && (int)$usr->usrmaillist === 1) {
          $users->update($usr->usrid, ["usrmaillist"=>0]);
          $users->logEvent($usr->usrid, \Model\UsersModel::EVENT_MAILLIST_REM);
        } else if ((string)$data["payload"]["status"] === "SUBSCRIBED" && (int)$usr->usrmaillist === 0) {
          $users->update($usr->usrid, ["usrmaillist"=>1]);
          $users->logEvent($usr->usrid, \Model\UsersModel::EVENT_MAILLIST_ADD);
        }
      }
    }

    $this->terminate();
  }
}
