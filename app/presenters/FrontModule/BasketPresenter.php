<?php

namespace FrontModule;

use dibi;
use Nette;
use <PERSON>\Debugger;

final class BasketPresenter extends BasePresenter {

  public $delid = NULL;

  private $deliveryModeRows;
  private $basketOrderOnOnePage;

  protected function startup() {

    parent::startup();
    $this->basketOrderOnOnePage = (bool)$this->neonParameters["app"]["basketOrderOnOnePage"];
    $this->recalc();
  }

  protected function beforeRender() {
    parent::beforeRender();
    $this->template->showMenuLeft = FALSE;
    $mans = $this->model->getManufacturersModel();
    $this->template->enum_promanid = $mans->getEnumManId();
  }

  /**
   * vymaze polozku z kosiku
   *
   * @param integer $proid id polozky
   * @throws Nette\Application\AbortException
   */
  public function actionDelete($proid) {
    if (array_key_exists($proid, $this->basketNamespace->items)) {
      $this->updateBasketItem($proid, 0);
    }
    $this->redirect('default');
  }

  /**
   * prida polozku do kosiku
   *
   * @param integer $proid id polozky
   * @param integer $count pocet kusu
   * @throws Nette\Application\AbortException
   */
  public function actionAdd($proid, $count = NULL) {

    if ($count === NULL) {
      $count = 1;
    }

    if (!isset($this->basketNamespace->items[$proid])) {
      $this->basketNamespace->items[$proid] = 0;
    }
    $this->updateBasketItem($proid, $this->basketNamespace->items[$proid] + $count);

    if ($this->isAjax()) {
      $this->recalc();
      $this->template->invalidateBasket = TRUE;
      $this->redrawControl("basketWindow");
    } else {
      $this->redirect('Basket:default');
    }
  }

  public function renderDefault() {
    $this->basketNamespace->delid = 0;
    $form = $this->getComponent("basketForm");
    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $product->setCurrency($this->currencies, $this->curId);
    $this->template->blockPromoRegistrace = dibi::fetch("SELECT * FROM pages WHERE pagurlkey='promo_registrace' AND pagstatus=0");
    if (!$form->isSubmitted()) {
      if (count($this->basketNamespace->items) > 0) {
        $this->template->productRows = dibi::query($product->getSqlList("proid IN (" . implode(",", array_keys($this->basketNamespace->items)) . ")"))->fetchAssoc('proid');
      } else {
        $this->template->productRows = array();
      }

      //zjistim nejblizsi slevu
      /*
      $nextDisc = dibi::fetch("SELECT dispercent, disfrom - ".$this->basketNamespace->priceSumVatDisc." AS diff FROM discounts WHERE distypid='volume' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, " AND disstatus=0 AND disfrom > ".$this->basketNamespace->priceSumVatDisc);
      */
      $nextDisc = NULL;
      $nextDelsFree = NULL;
      $nextDelFreeMas = NULL;

      $dels = FALSE;
      if ($this->basketNamespace->delFree == FALSE) {
        //nejblizsi doprava zdarma - jen rozvoz po Praze
        $dels = dibi::query("
        SELECT *, disfrom - " . $this->basketNamespace->priceSumVat . " AS diff 
        FROM deliverymodes 
        INNER JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
        WHERE delid=9 AND delmasid=0 AND disfrom > " . $this->basketNamespace->priceSumVat . " ORDER BY disfrom")->fetchAssoc("disfrom,delid");
      }

      if ($dels) $nextDelsFree = $dels;

      $this->template->nextDisc = $nextDisc;
      $this->template->nextDelsFree = $nextDelsFree;
      //naplnim access
      $this->template->enum_proaccess = $product->getEnumProAccess();

      //produkty v košíku
      $basketProducts = $product->cacheGet('basketProducts');
      if ($basketProducts === FALSE) {
        $basketProducts = array();
        //z nastaveni nactu kody zbozi
        $proCodesList = explode(',',$this->config["BASKET_PRODUCTLIST"]);
        $cnt = 0;
        foreach ($proCodesList as $proid) {
          $proid = trim($proid);
          if (!empty($proid)) {
            $item = $product->load($proid);
            if ($item) {
              //pokud je to master produkt načtu varianty
              if ($item->proismaster==1) {
                $variants = dibi::fetchAll("SELECT proid, proname FROM products WHERE prostatus=0 AND proaccess=0 AND promasid=%i", $item->proid);
                $vars = array();
                foreach ($variants as $row) {
                  //zjistím název varianty
                  $pos = strrpos($row->proname, " - ");
                  if ($pos !== FALSE) {
                    $var = trim(substr($row->proname, $pos + 1), '-');
                    $vars[$row->proid] = $var;
                  }
                }
                if (count($vars) > 0) {
                  $item->variants = $vars;
                } else {
                  //žádná varianta není dostupná tak tento produkt nepřidám
                  continue;
                }
              }
              $basketProducts[] = $item;
            }
          }
        }
        $product->cacheSave('basketProducts', $basketProducts);
      }

      $productsCount = 4;
      if (count($basketProducts) > $productsCount) {
        $randKeys = array_rand($basketProducts, $productsCount);
        $arr = array();
        foreach ($randKeys as $key) {
          $arr[$key] = $basketProducts[$key];
        }
        $basketProducts = $arr;
      }

      $this->template->basketPromoProducts = $basketProducts;

    }
  }

  public function renderOrderDelMode() {
    if (count($this->basketNamespace->items) == 0) {
      $this->flashMessage("Košík je prázdný. Vyberte nejprve nějaké zboží.");
      $this->redirect('Basket:default');
    }

    $delId = 0;
    $payId = 0;

    if (!empty($this->basketNamespace->contact["did"])) {
      $delId = (int)$this->basketNamespace->contact["did"];
    }
    if (!empty($this->basketNamespace->contact["pid"])) {
      $payId = (int)$this->basketNamespace->contact["pid"];
    }

    $basketPrice = $this->basketNamespace->priceSumVat;

    $this->template->delModes = dibi::query("
      SELECT delid, delname, delcode, " . ($this->basketNamespace->delFree ? "IF(delid=18,delprice" . $this->curId . $this->userData->usrprccat . ",0)" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice" . $this->curId . $this->userData->usrprccat . ")") . " AS delprice, delurlparcel, deldesc, deltext" . $this->curId . " AS deltext 
      FROM deliverymodes 
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delstatus=0 AND (delcurid=0 OR delcurid=%i", $this->curId, ") AND delmasid=0 ORDER BY delorder
    ")->fetchAssoc('delid');

    // zjistíme aktuální hodinu
		$now = new \DateTime();
    // zjistíme aktuální hodinu
		$actualHour = (int)$now->format("H");
    // zjistíme aktuální den v týdnu
		$actualDay = (int)$now->format('N');

		// pokud nebude mezi 10-17 tak skryjeme možnost objednání, pouze pracovní dny
		if ( $actualDay==6 || $actualDay==7 || $actualHour < 10 || $actualHour > 16 ) {
			unset($this->template->delModes[18]);
		}

    //vezmu prvni dopravu ze seznamu
    if ($delId == 0) {
      $first = current($this->template->delModes);
      $delId = $first->delid;
    }

    $this->delid = $delId;

    $this->template->payModes = dibi::query("
      SELECT delid, delmasid, delname, delcode, IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice" . $this->curId . $this->userData->usrprccat . ") AS delprice, delurlparcel, deldesc, deltext" . $this->curId . " AS deltext 
      FROM deliverymodes 
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delstatus=0 AND (delcurid=0 OR delcurid=%i", $this->curId, ") AND delmasid=%i", $delId, " ORDER BY delmasid, delorder
    ")->fetchAssoc('delid');

    $currentDeliveryMode = $this->template->delModes[$delId];


    if (($currentDeliveryMode->delcode === 'ULOZENKA') && !empty($this->basketNamespace->contact["orddelspec"])) {
      $this->template->ordDelSpec = $this->basketNamespace->contact["orddelspec"];
      //podle ID místa zkusím načíst další info
      $branch = dibi::fetch("SELECT * FROM wedopoints WHERE wedcode=%s", $this->basketNamespace->contact["orddelspec"]);
      $orddelspectext = $this->basketNamespace->contact["orddelspectext"];
      if ($branch) {
        $orddelspectext = $branch->wedname . ", $branch->wedstreet, $branch->wedcity";
        $this->basketNamespace->contact["orddelspectext"] = $orddelspectext;
      }
      $this->template->ordDelSpecText = $orddelspectext;

    } else if ($currentDeliveryMode->delcode === 'CESKA_POSTA_BALIKOVNA' && !empty($this->basketNamespace->contact["orddelspec"])) {
      $this->template->ordDelSpec = $this->basketNamespace->contact["orddelspec"];
      $this->template->ordDelSpecText = $this->basketNamespace->contact["orddelspectext"];
    } else if ($currentDeliveryMode->delcode === 'ZASILKOVNA' && !empty($this->basketNamespace->contact["orddelspec"])) {
      $this->template->ordDelSpec = $this->basketNamespace->contact["orddelspec"];
      $this->template->ordDelSpecText = $this->basketNamespace->contact["orddelspectext"];
    } else if ($currentDeliveryMode->delcode === 'DPD_PICKUP' && !empty($this->basketNamespace->contact["orddelspec"])) {
      $this->template->ordDelSpec = $this->basketNamespace->contact["orddelspec"];
      $this->template->ordDelSpecText = $this->basketNamespace->contact["orddelspectext"];
    } else if ($currentDeliveryMode->delcode !== 'ULOZENKA' && $currentDeliveryMode->delcode !== 'CESKA_POSTA_BALIKOVNA' && $currentDeliveryMode->delcode !== 'ZASILKOVNA') {
      unset($this->basketNamespace->contact["orddelspec"]);
      unset($this->basketNamespace->contact["orddelspectext"]);
    }

    $this->template->delMode = $currentDeliveryMode;

    if ($payId > 0) {
      $this->template->payMode = $this->template->payModes[$payId];
    }

    $this->template->delId = $delId;
    $this->template->payId = $payId;

    //doplním položky v košíku
    $this->template->productRows = $this->basketNamespace->products;
    $this->template->basket = $this->basketNamespace;
  }

  public function renderMakeOrder() {
    if (count($this->basketNamespace->items) == 0) {
      $this->flashMessage("Košík je prázdný. Vyberte nejprve nějaké zboží.");
      $this->redirect('Basket:default');
    }
    $this->template->productRows = $this->basketNamespace->products;
    $this->template->basket = $this->basketNamespace;
    $basketPrice = $this->basketNamespace->priceSumTotalVat;
    $this->template->delModes = dibi::query("
      SELECT delid, delname, delcode, " . ($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice" . $this->curId . $this->userData->usrprccat . ")") . " AS delprice, delurlparcel, deldesc, deltext" . $this->curId . " AS deltext 
      FROM deliverymodes 
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delstatus=0 AND delmasid=0 ORDER BY delorder
    ")->fetchAssoc('delid');
    $delId = 0;
    //vezmu prvni dopravu ze seznamu
    if ($delId == 0) {
      $first = current($this->template->delModes);
      $delId = $first->delid;
    }
    $this->delid = $delId;

    $this->template->payModes = dibi::fetchAll("
      SELECT delid, delmasid, delname, delcode, " . ($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice" . $this->curId . $this->userData->usrprccat . ")") . " AS delprice, delurlparcel, deldesc, deltext" . $this->curId . " AS deltext 
      FROM deliverymodes 
      LEFT JOIN discounts ON (disdelid=delmasid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delcode!='payonline' AND delstatus=0 AND delmasid=%i", $this->delid, " ORDER BY delmasid, delorder
    ");
    $payMO = dibi::fetch("
      SELECT delid, delmasid, delname, delcode, " . ($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice" . $this->curId . $this->userData->usrprccat . ")") . " AS delprice, delurlparcel, deldesc, deltext" . $this->curId . " AS deltext 
      FROM deliverymodes 
      LEFT JOIN discounts ON (disdelid=delmasid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delcode='payonline' AND delstatus=0 AND delmasid=%i", $this->delid, " ORDER BY delmasid, delorder
    ");
    $payModesOnline = Array();
    $payTypeOnline = $this->neonParameters["onlinePayTypes"];
    if (count($payTypeOnline) > 0 && $payMO) {
      foreach ($payTypeOnline as $ikey => $iname) {
        $row = clone $payMO;
        $row->delid = $ikey . '_' . $payMO->delid;
        $row->delname = $iname;
        $payModesOnline[] = $row;
      }
    }
    $this->template->payModesOnline = $payModesOnline;

    $this->template->payModesJs = dibi::query("
      SELECT delid, delmasid, delname, " . ($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice" . $this->curId . $this->userData->usrprccat . ")") . " AS delprice, delurlparcel, deldesc 
      FROM deliverymodes 
      LEFT JOIN discounts ON (disdelid=delmasid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delstatus=0 AND delmasid=%i", $this->delid, " ORDER BY delmasid, delorder")->fetchAssoc('delid');
    $this->template->priceSumTotalVat = $basketPrice;

    $LoggedUser = $this->userData;
    if ($LoggedUser->usrid > 0) {
      $defVals = array(
        'ordiname' => $LoggedUser->usriname,
        'ordilname' => $LoggedUser->usrilname,
        'ordifirname' => $LoggedUser->usrifirname,
        'ordistreet' => $LoggedUser->usristreet,
        'ordistreetno' => $LoggedUser->usristreetno,
        'ordicity' => $LoggedUser->usricity,
        'ordipostcode' => $LoggedUser->usripostcode,
        'ordicouid' => $LoggedUser->usricouid,
        'ordic' => $LoggedUser->usric,
        'orddic' => $LoggedUser->usrdic,
        'ordstname' => $LoggedUser->usrstname,
        'ordstlname' => $LoggedUser->usrstlname,
        'ordstfirname' => $LoggedUser->usrstfirname,
        'ordststreet' => $LoggedUser->usrststreet,
        'ordststreetno' => $LoggedUser->usrststreetno,
        'ordstcity' => $LoggedUser->usrstcity,
        'ordstpostcode' => $LoggedUser->usrstpostcode,
        'ordstcouid' => (int)$LoggedUser->usrstcouid,
        'ordtel' => $LoggedUser->usrtel,
        'ordmail' => $LoggedUser->usrmail,
        'shipto' => ($LoggedUser->usrstname != ""),
      );
      $form = $this->getComponent("makeOrderForm");
      $form->setDefaults($defVals);
    }

  }

  public function renderOrderContact() {
    $dels = $this->model->getDeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);

    if (count($this->basketNamespace->items) == 0) {
      $this->flashMessage("Košík je prázdný. Vyberte nejprve nějaké zboží.");
      $this->redirect('Basket:default');
    }

    $basketPrice = $this->basketNamespace->priceSumVat;

    if (empty($this->basketNamespace->contact["orddelid"])) {
      $this->flashMessage("Vyberte dopravu a způsob platby.", "err");
      $this->redirect('Basket:orderDelMode');
    } else {

      $payMode = dibi::fetch("
        SELECT delid, delmasid, delname, delcode, IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice" . $this->curId . $this->userData->usrprccat . ") AS delprice, delurlparcel, deldesc, deltext" . $this->curId . " AS deltext 
        FROM deliverymodes 
        LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
        WHERE delstatus=0 AND (delcurid=0 OR delcurid=%i", $this->curId, ") AND delid=%i", $this->basketNamespace->contact["orddelid"], " ORDER BY delmasid, delorder
      ");

      If ($payMode) {
        $this->template->payMode = $payMode;

        $delMode = dibi::fetch("
          SELECT delid, delname, delcode, " . ($this->basketNamespace->delFree ? "IF(delid=18,delprice" . $this->curId . $this->userData->usrprccat . ",0)" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice" . $this->curId . $this->userData->usrprccat . ")") . " AS delprice, delurlparcel, deldesc, deltext" . $this->curId . " AS deltext 
          FROM deliverymodes 
          LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
          WHERE delstatus=0 AND (delcurid=0 OR delcurid=%i", $this->curId, ") AND delid=%i", $payMode->delmasid , " ORDER BY delorder
        ");

        $this->template->delMode = $delMode;
      }

      if ($delMode) {
        if ($delMode->delcode === 'ULOZENKA' && empty($this->basketNamespace->contact["orddelspec"])) {
          $this->flashMessage("Vyplňte prosím odběrné místo Uloženky.", 'err');
          $this->redirect('orderDelMode');
        } else if ($delMode->delcode === 'CESKA_POSTA_BALIKOVNA' && empty($this->basketNamespace->contact["orddelspec"])) {
          $this->flashMessage("Vyplňte prosím odběrné místo Balíkovny.", 'err');
          $this->redirect('orderDelMode');
        } else if ($delMode->delcode === 'ZASILKOVNA' && empty($this->basketNamespace->contact["orddelspec"])) {
          $this->flashMessage("Vyplňte prosím odběrné místo Zásilkovny.", 'err');
          $this->redirect('orderDelMode');
        } else if ($delMode->delcode === 'DPD_PICKUP' && empty($this->basketNamespace->contact["orddelspec"])) {
          $this->flashMessage("Vyplňte prosím odběrné místo pro DPD pickup.", 'err');
          $this->redirect('orderDelMode');
        }
      }
    }

    if (is_array($this->basketNamespace->contact)) {
      //nactu kontaktni údaje
      if (empty($this->basketNamespace->contact["ordiname"])) {
        $LoggedUser = $this->userData;
        if ($LoggedUser->usrid > 0) {
          $defVals = array(
            'ordiname' => $LoggedUser->usriname,
            'ordilname' => $LoggedUser->usrilname,
            'ordifirname' => $LoggedUser->usrifirname,
            'ordistreet' => $LoggedUser->usristreet,
            'ordistreetno' => $LoggedUser->usristreetno,
            'ordicity' => $LoggedUser->usricity,
            'ordipostcode' => $LoggedUser->usripostcode,
            'ordicouid' => $LoggedUser->usricouid,
            'ordic' => $LoggedUser->usric,
            'orddic' => $LoggedUser->usrdic,
            'ordstname' => $LoggedUser->usrstname,
            'ordstlname' => $LoggedUser->usrstlname,
            'ordstfirname' => $LoggedUser->usrstfirname,
            'ordststreet' => $LoggedUser->usrststreet,
            'ordststreetno' => $LoggedUser->usrststreetno,
            'ordstcity' => $LoggedUser->usrstcity,
            'ordstpostcode' => $LoggedUser->usrstpostcode,
            'ordstcouid' => (int)$LoggedUser->usrstcouid,
            'ordtel' => $LoggedUser->usrtel,
            'ordmail' => $LoggedUser->usrmail,
            'shipto' => ($LoggedUser->usrstname != ""),
          );
          $form = $this->getComponent("orderContactForm");
          $form->setDefaults($defVals);
        }
      } else {
        $form = $this['orderContactForm'];
        $form->setDefaults($this->basketNamespace->contact);
      }
    }
    //načtu dopravu - jestli jde o uloženku
    $orddelid = $this->basketNamespace->contact["orddelid"];
    if (!is_numeric($orddelid)) {
      $arr = explode('_', $orddelid);
      $orddelid = 0;
      if (!empty($arr[1])) $orddelid = (int)$arr[1];
    }

    $payMode = dibi::fetch("SELECT * FROM deliverymodes  WHERE delid=%i", $orddelid);
    if ($payMode) {
      $delMode = dibi::fetch("SELECT * FROM deliverymodes  WHERE delid=%i", $payMode->delmasid);
      $this->template->isSpecDel = $delMode->delcode === 'ULOZENKA';
    }

    $enums = $this->model->getEnumcatsModel();
    $this->template->enum_countries = $enums->getEnumCountries();

    //doplním položky v košíku
    $this->template->productRows = $this->basketNamespace->products;
    $this->template->basket = $this->basketNamespace;
  }


  public function renderOrderSumarize() {
    if (count($this->basketNamespace->items) == 0) {
      $this->flashMessage("Košík je prázdný. Vyberte nejprve nějaké zboží.");
      $this->redirect('Basket:default');
    }
    if (empty($this->basketNamespace->contact["orddelid"])) {
      $this->flashMessage("Vyberte dopravu a způsob platby.", "err");
      $this->redirect('Basket:orderDelMode');
    }
    if (empty($this->basketNamespace->contact["ordiname"])) {
      $this->flashMessage("Vyplňte nejprve Dodací údaje.");
      $this->redirect('Basket:orderContact');
    }

    //doplnim dopravu
    $orddelid = $this->basketNamespace->contact["orddelid"];
    $payTypeAdd = "";
    if (!is_numeric($orddelid)) {
      $arr = explode('_', $orddelid);
      $orddelid = 0;
      if (!empty($arr[1])) $orddelid = (int)$arr[1];
      $payTypeOnline = $this->neonParameters["onlinePayTypes"];
      $payTypeAdd = $payTypeOnline[$arr[0]];
    }

    //zjistim cenu platby
    $delprice = 0;
    $delname = "";

    $pay = dibi::fetch("
      SELECT delmasid, delname, IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < " . $this->basketNamespace->priceSumVat . ", 0, delprice" . $this->curId . $this->userData->usrprccat . ") AS delprice
      FROM deliverymodes 
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delid=%i", $orddelid
    );
    if ($pay) {
      $delprice = $pay->delprice;

      //zjistim cenu dopravy
      $delivery = dibi::fetch("
        SELECT delid, delmasid, delname, " . ($this->basketNamespace->delFree ? "IF(delid=18,delprice" . $this->curId . $this->userData->usrprccat . ",0)" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < " . $this->basketNamespace->priceSumVat . ", 0, delprice" . $this->curId . $this->userData->usrprccat . ")") . " AS delprice
        FROM deliverymodes 
        LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
        WHERE delid=%i", $pay->delmasid
      );
      if ($delivery) {
        $delprice += $delivery->delprice;
        $delname = $delivery->delname . " " . $pay->delname . (!empty($payTypeAdd) ? " ($payTypeAdd) " : "");

        //zkontroluji jestli má rozvoz po praze a pokud ano, jestli ma pražské PSČ
        if ($delivery->delid === 9 || $delivery->delid === 18) {
          $psc = !empty($this->basketNamespace->contact["ordstpostcode"]) ? $this->basketNamespace->contact["ordstpostcode"] : $this->basketNamespace->contact["ordipostcode"];
          if (substr($psc, 0, 1) !== '1') {
            $this->flashMessage("Vámi zvolenou dopravou rozvážíme jen na území Prahy. Prosím zvolte jinou dopravu.", "err");
            $this->redirect('Basket:orderDelMode');
          }
        }
      }
    }
    $this->template->delprice = $delprice;
    $this->template->delname = $delname;

    $this->template->productRows = $this->basketNamespace->products;
    $this->template->basket = $this->basketNamespace;
    $basketPrice = $this->basketNamespace->priceSumTotalVat;
    $this->template->form = $this->basketNamespace->contact;
    $enums = $this->model->getEnumcatsModel();
    $this->template->enum_countries = $enums->getEnumCountries();
  }


  public function basketFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $formVars = $form->getValues();
      foreach ($formVars as $key => $value) {
        if (substr($key, 0, 6) === 'count_') {
          $proid = substr($key, 6);
          $this->updateBasketItem($proid, $value);
        }
      }

      $cous = $this->model->getCouponsModel();

      $usrEmail = "";
      if ($this->userData->usrid > 0) {
        $usrEmail = $this->userData->usrmail;
      }

      if($form["recalc"]->isSubmittedBy()) {

        $this->recalc();

        if (!empty($formVars["coupon"])) {
          //nactu kupon slevovy
          $cou = $cous->validateCoupon($formVars["coupon"], $this->basketNamespace->priceSumTotalVat, $usrEmail, TRUE);
          if ($cou["status"] != 'ok') {
            $this->flashMessage($cou["text"], 'danger');
          } else {
            $this->basketNamespace->coupon = $cou["data"];
          }
        }
      } else {
        $this->recalc();
      }

      //zkontroluji platnost kupónu
      if (!empty($this->basketNamespace->coupon)) {
        $cous = $this->model->getCouponsModel();
        $cou = $cous->validateCoupon($this->basketNamespace->coupon->coucode, $this->basketNamespace->priceSumTotalVat, $usrEmail, TRUE);
        if ($cou["status"] != 'ok') {
          unset($this->basketNamespace->coupon);
          $this->recalc();
          $this->flashMessage($cou["text"], 'err');
          $this->redirect("this");
        } else {
          $this->basketNamespace->coupon = $cou["data"];
        }
      }

      if ($form["recalc"]->isSubmittedBy()) {
        $this->redirect("this");
      } else if ($form["makeorder"]->isSubmittedBy()) {
        if ($this->basketOrderOnOnePage) {
          $this->redirect("Basket:makeOrder");
        } else {
          $this->redirect("Basket:orderDelMode");
        }
      }
    }
  }

  protected function createComponentBasketForm() {
    $form = $this->createAppForm();
    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $product->setCurrency($this->currencies, $this->curId);
    foreach ($this->basketNamespace->items as $key => $count) {
      if (empty($key)) {
        unset($this->basketNamespace->items[$key]);
        continue;
      }
      $row = $product->load($key);
      if ($row->proid > 0 && $row->prostatus == 0) {
        $form->addText("count_" . $row->proid, "", 3)
          //->addRule(Nette\Forms\Form::INTEGER, "Počet kusů musí být celé číslo")
          ->setRequired(true)
          ->setDefaultValue($count);
      }
    }

    //slevovy kupon
    if (empty($this->basketNamespace->coupon)) {
      $form->addText("coupon", "Slevový kupón", 30);
      $form->addSubmit('couponAdd', 'Přidat slevu')->getControlPrototype()->class('button');
    }

    $form->addSubmit('recalc', 'Přepočítat')->getControlPrototype()->class('button');
    $form->addSubmit('makeorder', 'Vybrat dopravu a platbu');
    $form->onSuccess[] = array($this, 'basketFormSubmitted');
    return $form;
  }

  protected function createComponentOrderDelModeForm() {
    $enums = $this->model->getEnumcatsModel();
    $enumCountries = $enums->getEnumCountries();
    $dels = $this->model->getDeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $form = $this->createAppForm();

    //cena objednaneho zbozi v kosiku
    $basketPrice = $this->basketNamespace->priceSumTotalVat;
    //bezne platby
    $payModes = dibi::query("
      SELECT delid, delname 
      FROM deliverymodes 
      WHERE delstatus=0 AND (delcurid=0 OR delcurid=%i", $this->curId, ") AND delmasid=%i", $this->delid
    )->fetchPairs("delid", "delname");

    /*
    $payMO = dibi::fetch("
      SELECT delid, delname 
      FROM deliverymodes 
      WHERE delcode='payonline' AND delstatus=0 AND (delcurid=0 OR delcurid=%i", $this->curId, ") AND delmasid=%i", $this->delid
    );
    $payTypeOnline = $this->neonParameters["onlinePayTypes"];
    if (count($payTypeOnline) > 0 && $payMO) {
      foreach ($payMO as $key => $name) {
        foreach ($payTypeOnline as $ikey => $iname) {
          $payModes[$ikey . '_' . $payMO->delid] = $iname;
        }
      }
    }
  */

    //$items = $dels->getEnumUlozenkaPlaces();
    $form->addText('ulozenkaac', '');

    $form->addText('balikovnaac', '');

    $form->addHidden('orddelspec', '')
      ->setHtmlId('orddelspec');

    $form->addRadioList('orddelid', '', $payModes);

    $form->addSubmit('submit', 'Zadat dodací údaje');
    $form->onSuccess[] = array($this, 'orderDelModeFormSubmitted');
    return $form;
  }

  public function orderDelModeFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $formVars = $form->getValues();
      //unset 
      unset($formVars["delmasid"]);

      $delMasId = (int)dibi::fetchSingle("SELECT delmasid FROM deliverymodes WHERE delid=%i", $formVars["orddelid"]);
      $delCode = (string)dibi::fetchSingle("SELECT delcode FROM deliverymodes WHERE delid=%i", $delMasId);


      $this->basketNamespace->contact = array_replace((array)$this->basketNamespace->contact, (array)$formVars);

      if ($delCode === 'ULOZENKA' && empty($formVars["orddelspec"])) {
        $this->flashMessage("Vyplňte prosím odběrné místo Uloženky.", 'err');
        $this->redirect('orderDelMode');
      } else if ($delCode === 'CESKA_POSTA_BALIKOVNA' && empty($formVars["orddelspec"])) {
        $this->flashMessage("Vyplňte prosím odběrné místo Balíkovny.", 'err');
        $this->redirect('orderDelMode');
      } else if ($delCode === 'ZASILKOVNA' && empty($formVars["orddelspec"])) {
        $this->flashMessage("Vyplňte prosím odběrné místo Zásilkovny.", 'err');
        $this->redirect('orderDelMode');
      } else if ($delCode === 'DPD_PICKUP' && empty($formVars["orddelspec"])) {
        $this->flashMessage("Vyplňte prosím odběrné místo pro DPD pickup.", 'err');
        $this->redirect('orderDelMode');
      }

      $this->redirect("Basket:orderContact");
    }
  }

  /**
   * odstraní položku z košíku
   *
   * @param null $did ID dopravy
   * @param null $pid ID Platby
   * @param null $sid odběrné místo vybráno
   * @throws Nette\Application\AbortException
   */
  public function handleDelModeChanged($did=NULL, $pid=NULL, $sid=NULL, $stext="") {

    $delModes = $this->model->getDeliveryModesModel();

    $params = [];

    if (!empty($pid)) {
      $params["pid"] = $pid;
      $pay = $delModes->load($pid);

      if ($pay) {
        $params["pid"] = $pay->delid;
        $params["orddelid"] = $pay->delid;
        $params["did"] = $pay->delmasid;
      }

    } else {
      if (!empty($did)) {
        $params["did"] = $did;
        $params["pid"] = NULL;
        $params["orddelid"] = NULL;
      }
    }

    if (!empty($sid)) {
      $params["orddelspec"] = $sid;
      $params["orddelspectext"] = $stext;
      if (!empty($this->basketNamespace->contact["did"])) {
        $del = $delModes->load($this->basketNamespace->contact["did"]);

        if ((string)$del->delcode === "ZASILKOVNA") {
          $point = dibi::fetch("SELECT * FROM zasilkovnapoints WHERE zasid2=?", (int)$params["orddelspec"]);
          Debugger::log("ZASILKOVNA|" . $params["orddelspec"] . "|" . $params["orddelspectext"] . (isset($point->zasname) ? "|" . $point->zasname : ""));
        }
      }
    }

    //pokud se změnila doprava vymažu odběrné místo
    if (!empty($params["did"]) && !empty($this->basketNamespace->contact["did"]) && (int)$this->basketNamespace->contact["did"] !== $params["did"]) {
      $params["orddelspec"] = NULL;
    }

    $this->basketNamespace->contact = array_replace((array)$this->basketNamespace->contact, $params);

    if ($this->isAjax()) {
      $this->recalc();
      $this->redrawControl("basketDeliveryModes");
    }

  }

  protected function createComponentOrderContactForm() {
    $enums = $this->model->getEnumcatsModel();
    $enumCountries = $enums->getEnumCountries();
    $dels = $this->model->getDeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $form = $this->createAppForm();

    //cena objednaneho zbozi v kosiku
    $basketPrice = $this->basketNamespace->priceSumTotalVat;

    $form->addGroup('Fakturační a dodací adresa');

    $form->addText("ordiname", "Jméno", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno.');

    $form->addText("ordilname", "Přijmení", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte přijmení.');

    $form->addText("ordifirname", "Název firmy", 60);

    $form->addText("ordistreet", "Ulice", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ulici.');

    $form->addText("ordistreetno", "Číslo popisné", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte číslo popisné.');

    $form->addText("ordicity", "Město, obec", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte město, obec.');

    $form->addText("ordipostcode", "PSČ", 5)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte PSČ.')
      ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Prosím vyplňte minimálně %d číslic', 5)
      ->addRule(Nette\Forms\Form::MAX_LENGTH, 'Prosím vyplňte maximálně %d znaků', 6);

    $form->addText("ordtel", "Mobilní telefon", 10)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte telefon.');

    $form->addText("ordmail", "Email", 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addText("ordic", "IČ", 10);
    $form->addText("orddic", "DIČ", 10)
      ->setHtmlId("orddic");

    $form->addCheckbox("ordusrvat", "Jsem plátce DPH")
      ->setHtmlId("ordusrvat");

    $form->addCheckbox('shipto', 'Chci zadat jinou adresu dodání')
      ->setDefaultValue(FALSE);

    $form->addCheckbox('onfirm', 'Chci objednat na firmu')
      ->setDefaultValue(FALSE)
      ->addCondition(Nette\Forms\Form::EQUAL, TRUE)// conditional rule: if is checkbox checked...
        ->toggle('onFirm'); // toggle #invoiceAddress

    $form->addText("ordstname", "Jméno", 60)
      ->addConditionOn($form["shipto"], $form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno (doručovací adresa).');

    $form->addText("ordstlname", "Příjmení", 60)
      ->addConditionOn($form["shipto"], $form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte přijmení (doručovací adresa).');

    $form->addText("ordstfirname", "Název firmy", 60);

    $form->addText("ordststreet", "Ulice", 60)
      ->addConditionOn($form["shipto"], $form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ulici (doručovací adresa).');

    $form->addText("ordststreetno", "Číslo popisné", 60)
      ->addConditionOn($form["shipto"], $form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte číslo popisné (doručovací adresa).');

    $form->addText("ordstcity", "Město, obec", 60)
      ->addConditionOn($form["shipto"], $form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte město, obec (doručovací adresa).');

    $form->addText("ordstpostcode", "PSČ", 6)
      ->addConditionOn($form["shipto"], $form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte PSČ (doručovací adresa).')
        ->addRule(Nette\Forms\Form::MAX_LENGTH, 'Prosím vyplňte maximálně %d znaků (doručovací adresa)', 6)
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Prosím vyplňte minimálně %d číslic (doručovací adresa)', 5);

    if ($this->userData->usrid > 0) {
      if ($this->userData->usrmaillist == 0) {
        $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
          ->setDefaultValue(TRUE);
      }
    } else {
      $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
        ->setDefaultValue(TRUE);

      $form->addText('antispam', 'Vyplňte číslo ' . $this->config["ANTISPAM_NO"], 10)
        ->setHtmlId('antispam')
        ->setRequired('Vyplňte prosím číslo ' . $this->config["ANTISPAM_NO"] . ', jde o test proti robotum')
        ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);

      $form->addCheckbox("ordregister", "Chci se zároveň registrovat a získat výhody navíc.");

      $form->addPassword("ordregisterpassw", "Vaše přihlašovací heslo")
        ->addConditionOn($form["ordregister"], Nette\Forms\Form::EQUAL, TRUE)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo.')
          ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);
    }

    $form->addTextArea("ordnote", "Vzkaz k objednávce", 60, 3);

    $form->addCheckbox("ordheurekagdpr", "Nesouhlasím se zasláním dotazníku spokojenosti v rámci programu Heureka Ověřeno zákazníky, který pomáhá zlepšovat naše služby.");
    $form->addCheckbox("ordzbozigdpr", "Nesouhlasím s předáním údajů za účelem nezávislého hodnocení nákupu přes zbozi.cz");

    $form->addSubmit('submit', 'Souhrn a objednávka')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'makeOrderFormSubmitted');
    return $form;
  }

  public function orderContactFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $formVars = $form->getValues();
      //unset
      if ((boolean)$formVars["shipto"] === FALSE) {
        //vynuluji shipto adresu
        $formVars["ordstname"] = '';
        $formVars["ordstlname"] = '';
        $formVars["ordstfirname"] = '';
        $formVars["ordststreet"] = '';
        $formVars["ordststreetno"] = '';
        $formVars["ordstcity"] = '';
        $formVars["ordstpostcode"] = '';
        $formVars["ordstcouid"] = 0;
      }

      unset($formVars["antispam"]);
      unset($formVars["delmasid"]);

        $formVars["ordzbozigdpr"] = (boolean)$formVars["ordheurekagdpr"];

      $this->basketNamespace->contact = array_replace((array)$this->basketNamespace->contact, (array)$formVars);
      $this->redirect("Basket:orderSumarize");
    }
  }

  protected function createComponentMakeOrderForm() {
    $enums = $this->model->getEnumcatsModel();
    $enumCountries = $enums->getEnumCountries();
    $dels = $this->model->getDeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $form = $this->createAppForm();

    //cena objednaneho zbozi v kosiku
    $basketPrice = $this->basketNamespace->priceSumTotalVat;

    $form->addGroup('Doprava a platba');
    $payModes = dibi::query("
      SELECT delid, delname 
      FROM deliverymodes 
      WHERE delstatus=0 AND delmasid>0
    ")->fetchPairs("delid", "delname");
    $form->addRadioList('orddelid', '', $payModes)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Způsob dodání a platby.');


    $items = $dels->getEnumUlozenkaPlaces();
    $form->addSelect('orddelspec', '', $items)
      ->setPrompt("Vyberte prosím výdejní místo ...");

    //$form->getElementPrototype()->id = 'makeOrderForm';
    $form->addGroup('Fakturační a dodací adresa');

    $form->addText("ordiname", "Jméno", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno.');

    $form->addText("ordilname", "Přijmení", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte přijmení.');

    $form->addText("ordifirname", "Název firmy", 60);

    $form->addText("ordistreet", "Ulice", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ulici.');

    $form->addText("ordistreetno", "Číslo popisné", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte číslo popisné.');

    $form->addText("ordicity", "Město, obec", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte město, obec.');

    $form->addText("ordipostcode", "PSČ", 6)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte PSČ.');

    $form->addText("ordtel", "Mobilní telefon", 20);

    $form->addText("ordmail", "Email", 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addText("ordic", "IČ", 10);
    $form->addText("orddic", "DIČ", 10)
      ->setHtmlId("orddic");

    $form->addCheckbox("ordusrvat", "Jsem plátce DPH")
      ->setHtmlId("ordusrvat");

    $form->addGroup('Fakturační adresa')
      ->setOption('embedNext', TRUE);

    $form->addCheckbox('shipto', 'Chci zadat jinou adresu dodání')
      ->setDefaultValue(FALSE)
      ->addCondition(Nette\Forms\Form::EQUAL, TRUE)// conditional rule: if is checkbox checked...
      ->toggle('invoiceAddress'); // toggle #invoiceAddress

    $form->addText("ordstname", "Jméno", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, TRUE)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno.');

    $form->addText("ordstlname", "Příjmení", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, TRUE)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte přijmení.');

    $form->addText("ordstfirname", "Název firmy", 60);

    $form->addText("ordststreet", "Ulice", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, TRUE)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ulici.');

    $form->addText("ordststreetno", "Číslo popisné", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, TRUE)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte číslo popisné.');

    $form->addText("ordstcity", "Město, obec", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, TRUE)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte město, obec.');

    $form->addText("ordstpostcode", "PSČ", 6)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, TRUE)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte PSČ.');
    /*
    $form->addSelect("ordstcouid", "Země", $enumCountries)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte zemi.');
    */

    if ($this->userData->usrid > 0) {
      if ($this->userData->usrmaillist == 0) {
        $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
          ->setDefaultValue(TRUE);
      }
    } else {
      $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
        ->setDefaultValue(TRUE);

      $form->addText('antispam', 'Vyplňte číslo ' . $this->config["ANTISPAM_NO"], 10)
        ->setHtmlId('antispam')
        ->setRequired('Vyplňte prosím číslo ' . $this->config["ANTISPAM_NO"] . ', jde o test proti robotum')
        ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);
    }

    $form->addTextArea("ordnote", "Vzkaz k objednávce", 60, 3);

    $form->addSubmit('submit', 'Odeslat objednávku')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'makeOrderFormSubmitted');
    //$form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  protected function createComponentOrderSumarizeForm() {
    $form = $this->createAppForm();
    $form->addCheckbox("ordheurekagdpr", "Nesouhlasím se zasláním dotazníku spokojenosti v rámci programu Ověřeno zákazníky, který pomáhá zlepšovat naše služby.");

    $form->addCheckbox("ordregister", "Chci se zároveň registrovat a získat výhody navíc.")
      ->addCondition(Nette\Forms\Form::EQUAL, TRUE)
        ->toggle("ordregisterpassw");

    $form->addPassword("ordregisterpassw", "Vaše přihlašovací heslo")
      ->addConditionOn($form["ordregister"], Nette\Forms\Form::EQUAL, TRUE)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo.')
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);

    $form->addSubmit('submit', 'Závazně objednat');
    $form->onSuccess[] = array($this, 'makeOrderFormSubmitted');
    return $form;
  }

  public function makeOrderFormSubmitted(Nette\Application\UI\Form $form) {
    $dels = $this->model->getDeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    if ($form->isSubmitted()) {
      if (count($this->basketNamespace->items) == 0) $this->redirect('Basket:default');

      $LoggedUser = $this->userData;
      $formVars = $form->getValues();

      unset($this->basketNamespace->contact["pid"]);
      unset($this->basketNamespace->contact["did"]);
      unset($this->basketNamespace->contact["sd"]);
      unset($this->basketNamespace->contact["sid"]);

      $this->basketNamespace->contact = array_replace((array)$this->basketNamespace->contact, (array)$formVars);

      $formVars = array_merge((array)$formVars, (array)$this->basketNamespace->contact);

      $ordRegister = FALSE;
      $ordRegisterPassw = "";
      if (!empty($formVars["ordregisterpassw"])) {
        $ordRegister = true;
        $ordRegisterPassw = (string)$formVars["ordregisterpassw"];
      }

      if (!$formVars["shipto"]) {
        //nechce dodat na spešl adresu, raději vymažu obsah st polí
        $formVars["ordstname"] = "";
        $formVars["ordstlname"] = "";
        $formVars["ordstfirname"] = "";
        $formVars["ordststreet"] = "";
        $formVars["ordststreetno"] = "";
        $formVars["ordstcity"] = "";
        $formVars["ordstpostcode"] = "";
      }

      unset($formVars["antispam"]);
      unset($formVars["shipto"]);
      unset($formVars["onfirm"]);
      unset($formVars["delmasid"]);
      unset($formVars["ulozenkaac"]);
      unset($formVars["balikovnaac"]);
      unset($formVars["ordregister"]);
      unset($formVars["ordregisterpassw"]);

      unset($formVars["pscValidated"]);

      $maillist = FALSE;
      if (isset($formVars["maillist"])) {
        $maillist = $formVars["maillist"];
        unset($formVars["maillist"]);
      }

      //zkontroluji PSČ
      if (isset($formVars["ordipostcode"])) {
        $ret = $this->checkPostCodeFormat($formVars["ordipostcode"]);
        if ($ret !== TRUE) {
          $form->addError("PSČ má špatný formát. " . $ret);
        }
      }
      if (isset($formVars["ordstpostcode"])) {
        $ret = $this->checkPostCodeFormat($formVars["ordstpostcode"]);
        if ($ret !== TRUE) {
          $form->addError("PSČ má špatný formát. " . $ret);
        }

        $formVars["ordipostcode"] = trim(str_replace(" ", "", $formVars["ordipostcode"]));
        $formVars["ordstpostcode"] = trim(str_replace(" ", "", $formVars["ordstpostcode"]));
      }

      if (!isset($this->basketNamespace->contact["pscValidated"])) {
        $this->basketNamespace->contact["pscValidated"] = FALSE;
      }

      if (!$form->hasErrors()) {
        if (!$this->checkPostCode($formVars["ordipostcode"]) && (bool)$this->basketNamespace->contact["pscValidated"] !== TRUE && (int)$this->curId === 1) {

          $this->flashMessage("Nejspíš chybné PSČ, prosím zkontrolujte ještě jednou, zda jej máte opravdu správně.", "danger");
          $this->basketNamespace->contact["pscValidated"] = TRUE;
          $this->redirect("orderContact");
        }
      }

      //cílovou zemi nastavím podle měny - mají stejné ID
      $formVars["ordicouid"] = $this->curId;
      $formVars["ordstcouid"] = $this->curId;

      //obednávka z eshopu
      $formVars["ordtype"] = 0;
      $formVars["ordusrid"] = $LoggedUser->usrid;

      $formVars["ordprccat"] = $LoggedUser->usrprccat;
      if (empty($formVars["ordprccat"])) {
        $formVars["ordprccat"] = "a";
      }

      $formVars["ordweight"] = $this->basketNamespace->weightSum;
      $formVars["ordcurid"] = $this->curId;

      //zjistim jestli neni specialni doprava
      if (!is_numeric($formVars["orddelid"])) {
        //vyseparuju kod platby
        $arr = explode('_', $formVars["orddelid"]);
        $formVars["orddelid"] = (int)$arr[1];
        $formVars["ordpaycode"] = $arr[0];
      }
      $payMode = $dels->load($formVars["orddelid"]);
      if ($payMode) {
        $delMode = $dels->load($payMode->delmasid);
      }

      if (!$payMode || !$delMode) {
        $form->addError("Špatné zadání dopravy a platby");
      }
      if ($delMode->delcode == 'ULOZENKA' && empty($formVars["orddelspec"])) {
        $form->addError("Prosim vyberte výdejní místo");
      } else if ($delMode->delcode == 'CESKA_POSTA_BALIKOVNA' && empty($formVars["orddelspec"])) {
        $form->addError("Prosim vyberte výdejní místo");
      } else if ($delMode->delcode == 'ZASILKOVNA' && empty($formVars["orddelspec"])) {
        $form->addError("Prosim vyberte výdejní místo");
      }
      if ($form->hasErrors()) {
        return;
      }
      $orders = $this->model->getOrdersModel();
      $orders->setCurrency($this->currencies, $this->curId);
      //vyberu firmu na kterou se objednavka vystavi
      $formVars["ordfirid"] = 2;
      //pokud je platce dam firmu pro platce
      if (!$formVars["ordusrvat"]) $formVars["ordfirid"] = 1;

      //slevový kupón
      if (!empty($this->basketNamespace->coupon["coucode"])) {
        $formVars["ordcoucode"] = $this->basketNamespace->coupon["coucode"];
      }

      //ulozim hlavicku objednavky
      $ordid = $orders->insert($formVars);

      //do profilu nakopiruju posledni kontaktni udaje
      $usrs = $this->model->getUsersModel();
      if ($this->userData->usrid > 0) {
        $usr = $usrs->load($this->userData->usrid);
        $usrUpd = array();
        /*
        if (empty($usr->usriname)) {
          $usrUpd["usriname"] = $formVars["ordiname"];
          $usrUpd["usrilname"] = $formVars["ordilname"];
          $usrUpd["usrifirname"] = $formVars["ordifirname"];
          $usrUpd["usristreet"] = $formVars["ordistreet"];
          $usrUpd["usristreetno"] = $formVars["ordistreetno"];
          $usrUpd["usricity"] = $formVars["ordicity"];
          $usrUpd["usripostcode"] = $formVars["ordipostcode"];
          $usrUpd["usrtel"] = $formVars["ordtel"];
          $usrUpd["usric"] = $formVars["ordic"];
          $usrUpd["usrdic"] = $formVars["orddic"];
          if (empty($usr->usrstname) && !empty($formVars["ordstname"])) {
            $usrUpd["usrstname"] = $formVars["ordstname"];
            $usrUpd["usrstlname"] = $formVars["ordstlname"];
            $usrUpd["usrstfirname"] = $formVars["ordstfirname"];
            $usrUpd["usrststreet"] = $formVars["ordststreet"];
            $usrUpd["usrststreetno"] = $formVars["ordststreetno"];
            $usrUpd["usrstcity"] = $formVars["ordstcity"];
            $usrUpd["usrstpostcode"] = $formVars["ordstpostcode"];
          }
        }
        */

        if ($maillist) {
          $usrUpd["usrmaillist"] = 1;
        }
        if (count($usrUpd) > 0) {
          $usrs->update($this->userData->usrid, $usrUpd);
        }
      } else {
        if ($maillist || $ordRegister) {
          $usrs = $this->model->getUsersModel();
          //zjistim jestli neni profil s timto mailem
          $usrid = dibi::fetchSingle("SELECT usrid FROM users WHERE usrmail=%s", trim($formVars["ordmail"]));
          if ($usrid > 0) {

            if ($maillist) {
              $usrs->update($usrid, array('usrmaillist' => 1));
            }

            //hláška že účet nebyl vytvořen, protože už existuje.
            if ($ordRegister) {
              $this->flashMessage("Registrace nebyla vytvořena, protože už existuje u nás účet s uvedeným emailem.", "err");
            }

          } else {

            $usrVals = array(
              'usrmail' => trim($formVars["ordmail"]),
              'usrmaillist' => ($maillist ? 1 : 0)
            );


            if ($ordRegister) {
              if (empty($usr->usriname)) {
                $usrVals["usriname"] = $formVars["ordiname"];
                $usrVals["usrilname"] = $formVars["ordilname"];
                $usrVals["usrifirname"] = $formVars["ordifirname"];
                $usrVals["usristreet"] = $formVars["ordistreet"];
                $usrVals["usristreetno"] = $formVars["ordistreetno"];
                $usrVals["usricity"] = $formVars["ordicity"];
                $usrVals["usripostcode"] = $formVars["ordipostcode"];
                $usrVals["usrtel"] = $formVars["ordtel"];
                $usrVals["usric"] = $formVars["ordic"];
                $usrVals["usrdic"] = $formVars["orddic"];
                if (empty($usr->usrstname) && !empty($formVars["ordstname"])) {
                  $usrVals["usrstname"] = $formVars["ordstname"];
                  $usrVals["usrstlname"] = $formVars["ordstlname"];
                  $usrVals["usrstfirname"] = $formVars["ordstfirname"];
                  $usrVals["usrststreet"] = $formVars["ordststreet"];
                  $usrVals["usrststreetno"] = $formVars["ordststreetno"];
                  $usrVals["usrstcity"] = $formVars["ordstcity"];
                  $usrVals["usrstpostcode"] = $formVars["ordstpostcode"];
                }
              }

              //naplnim overovaci kody
              $usrVals["usrmailvcode"] = $this->getVerifyCode();
              $usrVals["usrmailverified"] = 0;
              $usrVals["usrpassw"] = md5($ordRegisterPassw);

              //nastavím cenu a slevu
              $usrVals["usrdiscount"] = $this->config["DEFAULT_DISCOUT"];
              $usrVals["usrprccat"] = 'b';
            }

            $usrid = $usrs->insert($usrVals);

            //send GDPR
            if ($usrid > 0) {
              $this->logGdprEventsNewUser($usrid, $maillist);

              if ($ordRegister) {
                //mailuji o registraci
                //naplnim row
                $userRow = $usrs->load($usrid);

                $mailTemplate = $this->createTemplate();
                $mailTemplate->userRow = $userRow;
                $mailTemplate->setFile(WWW_DIR . '/../templates/Mails/mailUserMailAdd.latte');
                $this->mailSend($userRow->usrmail, $this->translator->translate("Registrace"), $mailTemplate);
                $mailTemplate->usrpassw = "";
                $this->mailSend($this->config["SERVER_MAIL"], $this->translator->translate("Nová registrace"), "Pravě se registroval nový uživatel: " . $userRow->usriname . " " . $userRow->usrilname . ' <a href="mailto:' . $userRow->usrmail . '">' . $userRow->usrmail . '</a>');

                //prihlasim
                $this->user->login($formVars["ordmail"], $ordRegisterPassw, self::LOGIN_NAMESPACE);

                $this->userData = $userRow;
                $this->sendVerification(FALSE);
              }
            }

          }
          $orders->update($ordid, array('ordusrid' => $usrid));
        }
      }

      if ($ordid > 0) {
        $ordItems = $this->model->getOrdItemsModel();
        $ordItemsVals = array(
          'oriordid' => $ordid,
        );
        //vlozim polozky
        $product = $this->model->getProductsModel();
        $product->setPrcCat($formVars["ordprccat"]);
        $product->setCurrency($this->currencies, $this->curId);
        $proIds = array();
        foreach ($this->basketNamespace->items as $id => $cnt) {
          $row = $product->load($id);
          if ($row->proid == $id && $row->prostatus == 0) {
            //polozka nalezena v db a je aktivni - vlozim mezi polozky objednavky
            $ordItemsVals['oriproid'] = $id;
            $ordItemsVals['oriprocode'] = $row->procode;
            $ordItemsVals['oriprocode2'] = $row->procode2;
            $ordItemsVals['oritypid'] = 0;
            $ordItemsVals['oriname'] = $row->proname;
            $ordItemsVals['oriprice'] = $row->proprice;
            $ordItemsVals['oripriceoriginal'] = $row->proprice;
            $ordItemsVals['orivatid'] = $row->provatid;
            $ordItemsVals['oricredit'] = $row->procredit;
            $ordItemsVals['oriqty'] = $cnt;
            $ordItemsVals['oriprobigsize'] = $row->probigsize;
            $ordItemsVals['oriprooffer'] = $row->prooffer;

            $ordItems->insert($ordItemsVals);
            //odečtu stav skladu
            if ($row->proqty > 0) {
              $product->updateQty($row, $cnt);
              $proIds[] = $row->proid;
            }
          }
          $ordItemsVals['oriprocode'] = NULL;
          $ordItemsVals['oriprocode2'] = NULL;
        }
        //podívám se jestli se objednané položky nevyprodaly
        //mailuji vynulované položky
        if (count($proIds) > 0) {
          $rows = dibi::fetchAll("SELECT proid, procodep, proname FROM products WHERE proid IN (%i)", $proIds, ' AND proaccess!=0');
          if (count($rows) > 0) {
            $mailTemplate = $this->createTemplate();
            $mailTemplate->products = $rows;
            $mailTemplate->setFile(WWW_DIR . '/../templates/Mails/mailProductsSold.latte');
            $this->mailSend('<EMAIL>', 'Vyprodané položky', $mailTemplate);
            $this->mailSend('<EMAIL>', 'Vyprodané položky', $mailTemplate);
            $this->mailSend('<EMAIL>', 'Vyprodané položky', $mailTemplate);
          }
        }

        //vlozim postovne
        $payMode = $dels->load($formVars["orddelid"]);
        $delMode = $dels->load($payMode->delmasid);
        $delPrice = (double)$delMode->delprice + (double)$payMode->delprice;

        $ordItemsVals['oritypid'] = 1;
        $ordItemsVals['oriproid'] = 0;
        $ordItemsVals['oriname'] = "Doprava: " . $delMode->delname . " - " . $payMode->delname;
        $ordItemsVals['oriprice'] = ($this->basketNamespace->specDel ? 0 : $delPrice);
        $ordItemsVals['orivatid'] = NULL;
        $ordItemsVals['oricredit'] = 0;
        $ordItemsVals['oriqty'] = 1;
        $ordItemsVals['oriprobigsize'] = 0;
        $ordItemsVals['oriprooffer'] = 0;
        $ordItems->insert($ordItemsVals);

        $orders->recalcOrder($ordid);
        $this->basketNamespace->ordid = $ordid;

        $order = $orders->load($ordid);

        //pokud přesáhne obj. 1000č tak rouška zdarma
        /*
        if ($order->ordpricevat >= 1000 && ($this->userData->usrprccat === "a" || $this->userData->usrprccat === "b")) {
          $row = $product->load('OP02', "code");
          if ($row) {
            $ordItemsVals['oriproid'] = $row->proid;
            $ordItemsVals['oriordid'] = $order->ordid;
            $ordItemsVals['oriprocode'] = $row->procode;
            $ordItemsVals['oriprocode2'] = $row->procode2;
            $ordItemsVals['oritypid'] = 0;
            $ordItemsVals['oriname'] = $row->proname;
            $ordItemsVals['oriprice'] = 0;
            $ordItemsVals['oripriceoriginal'] = 0;
            $ordItemsVals['orivatid'] = $row->provatid;
            $ordItemsVals['oricredit'] = 0;
            $ordItemsVals['oriqty'] = 1;
            $ordItemsVals['oriprobigsize'] = 0;
            $ordItemsVals['oriprooffer'] = 0;
            $ordItems->insert($ordItemsVals);
          }
        }
        */

        //odmailuju
        $mailTemplate = $this->createTemplate();
        $mailTemplate->orderRow = $order;
        $delModes = $this->model->getDeliveryModesModel();
        $delModes->setCurrency($this->currencies, $order->ordcurid);

        $enums = $this->model->getEnumcatsModel();
        $mailTemplate->basket = $this->basketNamespace;
        $mailTemplate->payMode = $delModes->load($order->orddelid);
        $mailTemplate->delMode = $delModes->load($mailTemplate->payMode->delmasid);
        $mailTemplate->enum_ulozenka = $delModes->getEnumUlozenkaPlaces();
        $mailTemplate->enum_balikovna = $delModes->getEnumBalikovnaPlaces();

        $mailTemplate->ordItemRows = dibi::fetchAll("
          SELECT orditems.*, manname, procodep  
          FROM orditems 
          left JOIN products on proid=oriproid 
          left JOIN manufacturers on promanid=manid 
          WHERE oriordid=%i", $ordid, " 
          ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END
        ");
        $mailTemplate->ordSpecDel = ((int)dibi::fetchSingle("SELECT COUNT(*) FROM orditems WHERE oriordid=%i", $ordid, " AND oritypid=0 AND (oriprobigsize=1 OR oriprooffer=1)") > 0);
        $mailTemplate->key = substr(md5($ordid . $mailTemplate->orderRow->orddatec), 0, 4);
        $mailTemplate->setTranslator($this->translator);
        $mailTemplate->lang = $this->lang;
        $mailTemplate->user = $this->userData;
        $mailTemplate->enum_usrprccat = $this->getEnumPrcCat();

        $mailTemplate->setFile(WWW_DIR . '/../templates/Mails/mailOrderCreated.latte');
        try {
          //mailuju zakaznikovi
          $mails = explode(',', $this->config["SERVER_MAILORDERS"]);
          $mailOrders = $mails[0];
          $this->mailSend($order->ordmail, "Objednávka č." . " " . $order->ordcode, $mailTemplate, $mailOrders);
          foreach ($mails as $mail) {
            $this->mailSend($mail, "Nová objednávka č. " . $order->ordcode, $mailTemplate);
          }
        } catch (Nette\InvalidStateException $e) {
          $this->flashMessage("Nepodařilo se ale odeslat informační email o nové objednávce", "err");
        }

        //vymazu kosik
        $specDel = $this->basketNamespace->specDel;
        $this->basketNamespace->remove();
        $this->basketNamespace = $this->getSession('basket');
        $this->basketNamespace->setExpiration(0);
        $this->basketNamespace->ordid = $ordid;

        //vymazu databázový košík
        if ($this->userData->usrid > 0) {
          $bass = $this->model->getBasketItemsModel();
          $bass->deleteUserProducts($this->userData->usrid);
        }

        $this->basketNamespace->remove();
        $this->redirect("Order:status", $order->ordid.substr(md5($order->ordid.$order->orddatec->getTimestamp()), 0, 8));
      }
    }
  }

  public function renderUlozenkaAc($q) {
    $uloConfig = $this->neonParameters["ulozenka"];
    $this->template->rows = dibi::fetchAll("SELECT * FROM ulozenkapoints where (ulocity LIKE %~like~", $q, " OR uloname LIKE %~like~", $q, ") GROUP BY uloid2");
  }

  public function renderBalikovnaAc($q) {
    $this->template->rows = dibi::fetchAll("SELECT * FROM balikovnapoints where (balname LIKE %~like~", $q, " OR balcity LIKE %~like~", $q, " OR balstreet LIKE %~like~", $q, "OR balpostcode LIKE %~like~", $q, ") ORDER BY CONCAT(balcity,' ',balstreet)");
  }
}