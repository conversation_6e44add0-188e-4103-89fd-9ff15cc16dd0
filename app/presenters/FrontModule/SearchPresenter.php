<?php
namespace FrontModule;
use dibi;
use Nette;

final class SearchPresenter extends BasePresenter {

  /** @persistent */
  public $name = '';

  /** @persistent */
  public $fulltext = '';

  /** @persistent */
  public $s = '';

  /** @persistent */
  public $o = '';

  /** @persistent */
  public $m = array();

  public function renderDefault () {
    $this->name = $this->getParameter("name");
    $this->fulltext = $this->getParameter("fulltext");
    //$this->m = $this->getParam("m");
    //$this->s = $this->getParam("s");
    $this->o = $this->getParameter('o');

    $where  = $this->getWhere();
    $orderBy = "IF(proqty>0,0,1) ASC,";
    switch ($this->o) {
       case '':
         $orderBy .= " proprioritize DESC, protypid4 DESC, protypid DESC, protypid3 DESC, proorder ASC ";
         break;
       case 'name':
         $orderBy .= " proname ASC";
         break;
       case 'name_':
         $orderBy .= " proname DESC";
         break;
       case 'price':
         $orderBy .= " IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) ASC";
         break;
       case 'price_':
         $orderBy .= " IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) DESC";
         break;
    }

    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat); //musim nastavit cenovou kategorii prihlaseneho uzivatele
    $product->setCurrency($this->currencies, $this->curId);
    $sql = $product->getSqlListArr($where, $orderBy);
    $dataSource = $product->getDataSource($sql);

    $paginator = $this['paginator']->getPaginator();
    $paginator->itemsPerPage = $this->config["CATALOG_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    //$this->template->productsData = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();

    $sql[] = " LIMIT ";
    $sql[] = $paginator->itemsPerPage;
    $sql[] = " OFFSET ";
    $sql[] = $paginator->offset;

    $this->template->productsData = dibi::fetchAll($sql);


    $query = "";
    if (!empty($this->name)) {
      $query = $this->name;
    } else if (!empty($this->fulltext)) {
      $query = $this->fulltext;
    }

    if (!empty($query)) {
      $this->template->catalogsData = dibi::fetchAll("SELECT catid,catkey, catname FROM catalogs WHERE catname LIKE %~like~", $query);
    }

    $this->template->query = $query;

    $form = $this['detailSearchForm'];
    if (!$form->isSubmitted()) {
      $form->setDefaults(array(
        'mans'=>$this->m,
        'name'=>$this->name,
        'fulltext'=>$this->fulltext,
        'onstock'=>$this->s,
      ));
    }
  }

  public function renderNews() {
    $where[] = "protypid2=1";
    $orderBy = "IF(proqty>0,0,1) ASC, prodatec DESC";

    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat); //musim nastavit cenovou kategorii prihlaseneho uzivatele
    $product->setCurrency($this->currencies, $this->curId);

    $sql = $product->getSqlListArr($where, $orderBy);
    $dataSource = dibi::dataSource($sql);

    $paginator = $this['paginator']->getPaginator();
    $paginator->itemsPerPage = $this->config["CATALOG_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $this->template->productsData = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->pageTitle = "Novinky";

    $this->template->paginator = $paginator;

    $this->setView("default");
  }

  public function renderSearchAc($q) {
    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $product->setCurrency($this->currencies, $this->curId);

    $where = array();
    $where[] = " prostatus=0 AND promasid=0 AND (proname LIKE %~like~";
    $where[] = $q;
    $where[] = " OR pronamecz LIKE %~like~";
    $where[] = $q;
    $where[] = " OR manname LIKE %~like~";
    $where[] = $q;
    $where[] = ")";

    $sql = $product->getSqlListArr($where, "proname ASC LIMIT 7");
    $this->template->productsData = dibi::fetchAll($sql);
  }
  
  protected function createComponentDetailSearchForm() {
    $form = $this->createAppForm();
    $form->addGroup();
    $form->addtext("name", "Název:", 40);
    $form->addtext("fulltext", "Fulltext:", 40);
    $where = $this->getWhere();
    $sql = array();
    $sql[] = "SELECT manid, manname
      FROM products
      INNER JOIN manufacturers ON (manid=promanid)
      WHERE ";
    if (count($where) > 0) {
      $sql = array_merge($sql, $where);
    }

    $sql[] = "GROUP BY manid
      ORDER BY manname";

    $manufacts = dibi::fetchAll($sql);

    $form->addGroup("Výrobci");
    $container = $form->addContainer('mans');
    foreach ($manufacts as $row) {
      $container->addCheckbox($row->manid, $row->manname);
    }
    $form->addCheckbox('onstock', "Skladem")
      ->setHtmlId("onstock");
    $form->addSubmit('detailSearch', 'Hledej')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $vals = $form->getValues();

      $this->name = $vals["name"];
      $this->fulltext = $vals["fulltext"];
      $this->s = $vals["onstock"];
      $this->m = array();
      foreach ($vals['mans'] as $key => $val) {
        if ($val) $this->m[$key] = $key;
      }
      $this->redirect('this');

      //$this->redirect('Search:default', $searchParam);
    }
  }

  Private function getWhere($manOff = false) {
    $where = array();

    $where[] = " promasid=0 AND prostatus=0 ";

    if (!empty($this->name)) {
      $where[] = " AND (proname LIKE %~like~";
      $where[] = $this->name;
      $where[] = " OR proname2 LIKE %~like~";
      $where[] = $this->name;
      $where[] = " OR pronamecz LIKE %~like~";
      $where[] = $this->name;
      $where[] = " OR manname LIKE %~like~";
      $where[] = $this->name;
      $where[] = ")";
    }
    if (!empty($this->fulltext)) {
      $where[] = " AND (proname LIKE %~like~";
      $where[] = $this->fulltext;
      $where[] = " OR proname2 LIKE %~like~";
      $where[] = $this->fulltext;
      $where[] = " OR pronamecz LIKE %~like~";
      $where[] = $this->fulltext;
      $where[] = " OR manname LIKE %~like~";
      $where[] = $this->fulltext;
      $where[] = " OR prodescs LIKE %~like~";
      $where[] = $this->fulltext;
      $where[] = ")";

    }
    if (count($this->m) > 0 && $manOff == false) {
      $where[] = " AND promanid IN (%i)";
      $where[] = $this->m;
    }

    if ($this->s) {
      $where[] = " AND proaccess=0";
    }

    if (count($where) === 0) {
      $where[] = "proid=-1";
    }
    return $where;
  }
}
