<?php
namespace FrontModule;
use dibi;
use Nette;
use <PERSON>\Debugger;

final class ErrorPresenter extends BasePresenter {

	public function renderDefault($exception) {

    if ($exception instanceof \Nette\Application\BadRequestException) {
      $this->template->dataRow = dibi::fetch("SELECT * FROM pages where pagurlkey='nenalezeno-404' LIMIT 1");
			$this->setView('404'); // load template 404.latte

		} else {
			$this->setView('500'); // load template 500.latte
			Debugger::log($exception); // and handle error by Nette\Debug
		}
	}

  /*
  protected function shutdown($response) {
    $exception = $this->params['exception'];
    NDebug::processException($exception); // pozor, volá exit()
  }
  */
}
