<?php

namespace FrontModule;

use Nette\Application\BadRequestException;
use <PERSON>\Debugger;

final class FbPresenter extends BasePresenter {

  const PIXEL_ID = '1754813784783260';
  const TOKEN = 'EAAINydqn85IBOzISQKiN5OoOKrKozfwQWMLDiRa6kscPtUPrX8dz3cdI6rUhxqm2b4ZAwyRize97K5cDvlyngIbCwI70mnQRxifgeTDl9A4l3wNnmnPupiZCIDAIUmox91D22BU2jE0tkezp0WIhlCw8Qtb5o6mOFKjZChUX8DfF2IkOdRg4O8BuDwqOxBWogZDZD';

  public function actionConversion(): void {

    header("Access-Control-Allow-Origin: https://www.goldfitness.cz");
    header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
    header("Access-Control-Allow-Headers: *");

    // Při OPTIONS requestu vrátíme jen <PERSON>
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
      http_response_code(204);
      exit;
    }

// Check POST data
    $post_json = file_get_contents('php://input');
    if (!$post_json) die('The post data required'); // The post data required

// Setup
    $fb_access_token = self::TOKEN;
    $fb_pixel_id = self::PIXEL_ID;
    $fb_test_event_code = '';

    $timestamp = time();
    $post_array = array();
    $data = array();
    $error_send = FALSE;

    $post_array = json_decode($post_json, true);
    if (!$post_array['event']) die('The event name required'); // The event name required

// IP address
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
      $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
      $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
      $ip = $_SERVER['REMOTE_ADDR'];
    }

    $ch = curl_init();

    $data['event_name'] = &$post_array['event'];
    $data['event_time'] = $timestamp;
    if ($post_array['event_id']) {
      $data['event_id'] = &$post_array['event_id'];
    }
    $data['event_source_url'] = &$post_array['url'];
    $data['action_source'] = "website";
    if ($post_array['event_data']) {
      $data['custom_data'] = &$post_array['event_data'];
    }
    $data['user_data']['client_ip_address'] = $ip; // ip address
    $data['user_data']['client_user_agent'] = &$post_array['user_agent'];
    $data['user_data']['fbp'] = &$post_array['fbp'];
    $data['user_data']['fbc'] = &$post_array['fbclid'];
    if ($post_array['em']) {
      $data['user_data']['em'] = &$post_array['em']; // email
    }

    $fields = json_encode([
      'data' => [$data],
      'test_event_code' => $fb_test_event_code ?? null,
    ]);

    curl_setopt_array($ch, array(
      CURLOPT_URL => 'https://graph.facebook.com/v10.0/'.$fb_pixel_id.'/events?access_token='.$fb_access_token,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => '',
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 0,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => 'POST',
      CURLOPT_POSTFIELDS => $fields,
      CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json'
      ),
    ));

    $result = curl_exec($ch);
    if (curl_errno($ch)) {
      Debugger::log('Curl error: ' . curl_error($ch), Debugger::ERROR);
    }
    Debugger::log('FB response: ' . $result, Debugger::INFO);
    curl_close($ch);
  }
}
