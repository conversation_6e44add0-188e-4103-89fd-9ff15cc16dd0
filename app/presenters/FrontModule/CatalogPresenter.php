<?php
namespace FrontModule;
use dibi;
use Nette;

final class CatalogPresenter extends BasePresenter {

  public $pF = Null; //cena od
  public $pT = Null; //cena do

  public $t = array(); //typy
  public $m = array(); //vyrobce
  public $f = array(); //forma
  public $o = ''; //řazeni

  /** @persistent */
  public $path = "";

  /** @persistent */
  public $page = 1;

  public $productsRows = array();

  private $filterFormsIdName = array();
  private $filterFormsIdKey = array();
  private $filterFormsKeyId = array();
  private $filterTypesIdName = array();
  private $filterTypesIdKey = array();
  private $filterTypesKeyId = array();
  private $filterManufacturersIdName = array();
  private $filterManufacturersIdKey = array();
  private $filterManufacturersKeyId = array();

  /** app */
  protected $catNamespace;

  protected function startup() {
    parent::startup();

    //inicializace session katalogu
    $this->catNamespace = $this->getSession('catalog');
    $this->catNamespace->setExpiration(0);
    if (!isset($this->catNamespace->listWhere)) $this->catNamespace->listWhere = '';
    if (!isset($this->catNamespace->listOrderBy)) $this->catNamespace->listOrderBy = '';

    //naplnim ciselniky
    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);

    //forma
    $this->filterFormsIdName = $pros->getEnumProForId();
    foreach ($this->filterFormsIdName as $id => $name) {
      $key = Nette\Utils\Strings::webalize($name);
      $this->filterFormsIdKey[$id] = $key;
      $this->filterFormsKeyId[$key] = $id;
    }
    //typ
    $this->filterTypesIdName = $this->getEnumTypes();
    foreach ($this->filterTypesIdName as $id => $name) {
      $key = Nette\Utils\Strings::webalize($name);
      $this->filterTypesIdKey[$id] = $key;
      $this->filterTypesKeyId[$key] = $id;
    }
    //vyrobce
    $mans = $this->model->getManufacturersModel();
    $this->filterManufacturersIdName = $mans->getEnumManId();
    foreach ($this->filterManufacturersIdName as $id => $name) {
      $key = Nette\Utils\Strings::webalize($name);
      $this->filterManufacturersIdKey[$id] = $key;
      $this->filterManufacturersKeyId[$key] = $id;
    }
  }

  public function renderDetailOld($id, $key, $page=1) {
    $catalog = $this->model->getCatalogsModel();

    //aktualni polozka katalogu
    $catalogData = $catalog->load($id);
    if ($catalogData === false) {
      throw new Nette\Application\BadRequestException('Katalog nenalezen', '404');
    } else{
      //kontrola platnosti URL
      $urlkey = (!empty($catalogData->catkey) ? $catalogData->catkey : Nette\Utils\Strings::webalize($catalogData->catname));
      //presmeruju na novy
      $this->redirect(301, 'Catalog:detail', array('id'=>$id, 'key'=>$urlkey));
    }
  }

  public function renderFavorites() {
    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat); //musim nastavit cenovou kategorii prihlaseneho uzivatele
    $pros->setCurrency($this->currencies, $this->curId);

    $favorites = $this->getUserFavorites($this->userData->usrid);
    if (count($favorites) > 0) {
      $proIds = array_keys($favorites);
      $where[] = 'proid IN (%i)';
      $where[] = $proIds;
    } else {
      $where[] = 'proid = 0';
    }
    $this->template->productsData = dibi::fetchAll($pros->getSqlListArr($where, "proname"));
  }


  public function renderDetail($id, $key, $path, $page=1) {
    $priceFrom = 0;
    $priceTo = 0;

    $pathCannonical = array();

    if (isset($this->payload->path)) {
      $path = $this->payload->path;
    }

    $this->template->path = $path;
    $this->path = $path;

    //analyzuju $path
    if (!empty($path)) {
      $pathArr = explode("/", $path);
      $pathCannonical = $pathArr;
      foreach ($pathArr as $k => $pathKey) {
        if (array_key_exists($pathKey, $this->filterTypesKeyId)) {
          //nebude v canonical URL
          unset($pathCannonical[$k]);
          //typy
          $this->t[$this->filterTypesKeyId[$pathKey]] = $this->filterTypesKeyId[$pathKey];
          continue;
        } else if (array_key_exists($pathKey, $this->filterFormsKeyId)) {
          //forma
          $this->f[$this->filterFormsKeyId[$pathKey]] = $this->filterFormsKeyId[$pathKey];
          continue;
        } else if (array_key_exists($pathKey, $this->filterManufacturersKeyId)) {
          //vyrobci
          $this->m[$this->filterManufacturersKeyId[$pathKey]] = $this->filterManufacturersKeyId[$pathKey];
          continue;
        } else if (substr($pathKey, 0, 7) == 'cena-od') {
          //nebude v canonical URL
          unset($pathCannonical[$k]);
          $this->pF = (int)substr($pathKey, 8);
          $priceFrom = $this->pF;
        } else if (substr($pathKey, 0, 7) == 'cena-do') {
          //nebude v canonical URL
          unset($pathCannonical[$k]);
          $this->pT = (int)substr($pathKey, 8);
          $priceTo = $this->pT;
        } else if ($pathKey == 'nejlevnejsi') {
          //nejlevnejsi
          $this->o = "nejlevnejsi";
          //nebude v canonical URL
          unset($pathCannonical[$k]);
          continue;
        } else if ($pathKey == 'nejdrazsi') {
          //nejdrazsi
          //nebude v canonical URL
          unset($pathCannonical[$k]);
          $this->o = "nejdrazsi";
          continue;
        }

      }
    }

    $this->template->pathCanonical = "";
    if (count($pathCannonical) > 0) {
      $this->template->pathCanonical = implode("/", $pathCannonical);
    }

    $catalog = $this->model->getCatalogsModel();
    $mans = $this->model->getManufacturersModel();

    //aktualni polozka katalogu
    $catalogData = $catalog->load($id);

    //zjistim jestli budu spojovat nazvy kategorii
    $this->template->catNameFull = $catalogData->catname;
    if ($catalogData->catlevel == 3) {
      //zjistim nadrizenou kategorii
      $catMasName = (string)dibi::fetchSingle("SELECT catname FROM catalogs WHERE catid=%i", $catalogData->catmasid);
      If (!empty($catMasName)) $this->template->catNameFull = $catMasName." - ".$catalogData->catname;
    }

    if (!$catalogData) {
      throw new Nette\Application\BadRequestException('Katalog nenalezen', '404');
    } else if ($catalogData->catstatus > 0) {
      $this->redirect(301, 'Homepage:default');
    } else {
      //kontrola platnosti URL
      $urlkey = (!empty($catalogData->catkey) ? $catalogData->catkey : Nette\Utils\Strings::webalize($catalogData->catname));
      //pokud se zmenil klic presmeruju na novy
      if ($key != $urlkey) $this->redirect(301, 'Catalog:detail', array('id'=>$id, 'key'=>$urlkey, 'path' => $path));

      $this->template->catalogData = $catalogData;
    }

    //id aktualni kategorie
    $this->template->thisCatId = $catalogData->catid;
    $idPath = explode('|', trim($catalogData->catpathids, '|'));

    $rootCatId = 0;
    if (isset($idPath[1])) $rootCatId = (int)$idPath[1];

    $this->template->masterCatId = $idPath[0];
    if (!empty($idPath[1])) {
      $this->appNamespace->masterCatId = $idPath[1];
    } else {
      $this->appNamespace->masterCatId = $idPath[0];
    }

    $catalogPath = array();
    foreach ($idPath as $catid) {
      $catalogPath[$catid] = dibi::fetch("SELECT * from catalogs WHERE catid=$catid");
    }

    $this->template->catalogPath = $catalogPath;

    $this->template->rootCatId = $rootCatId;

    if (isset($catalogPath[$rootCatId])) $this->template->rootCatalog = $catalogPath[$rootCatId];

    //k aktualni kategorii načtu podkategorie
    $this->template->thisCatId = $catalogData->catid;
    //podrizene kategorie
    $catalogSubItems = dibi::fetchAll("SELECT * FROM catalogs WHERE catmasid=%i", $catalogData->catid, " AND catstatus=0 ORDER BY catorder");
    $this->template->catalogSubItems = $catalogSubItems;
    $menuCatalogSubItems = array();
    If ($catalogData->catlevel == 3) {
      $menuCatalogSubItems[$catalogData->catmasid] = dibi::fetchAll("SELECT * FROM catalogs WHERE catmasid=%i", $catalogData->catmasid, " AND catstatus=0 ORDER BY catorder");
    } elseIf ($catalogData->catlevel == 2) {
      $menuCatalogSubItems[$catalogData->catid] = $catalogSubItems;
    }
    $this->template->menuCatalogSubItems = $menuCatalogSubItems;
    //naplnim si do katalogu prislusne zbozi
    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat); //musim nastavit cenovou kategorii prihlaseneho uzivatele
    $product->setCurrency($this->currencies, $this->curId);
    //sestavim WHERE
    $orderBy = "IF(proqty>0,0,1),";
    switch ($this->o) {
       case '':
         $orderBy .= " proprioritize DESC, protypid4 DESC, protypid DESC, protypid3 DESC, proorder ASC ";
         break;
       case 'az':
         $orderBy .= "proname ASC";
         break;
       case 'za':
         $orderBy .= "proname DESC";
         break;
       case 'nejlevnejsi':
         $orderBy .= "IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) ASC";
         break;
       case 'nejdrazsi':
         $orderBy .= "IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) DESC";
         break;
    }
    //nactu do filtru podminky
    $where = "prostatus=0";
    //vyrobce
    $manwhere = "";
    //vyrobci
    if (count($this->m) > 0) {
      $manwhere = implode(',', $this->m);
    }
    if (count($this->m) == 1) {
      $manId = (int)reset($this->m);
      $this->template->manufacturer = $mans->load($manId);
    }

    //nefiltrovat podle vyrobce pokud prislusenstvi
    if (!empty($manwhere)) $where = $where." AND promanid IN (".$manwhere.")";

    //typy
    if (count($this->t) > 0) {
      $tWhere = "";
      foreach ($this->t as $sKey => $value) {
        switch ($sKey) {
          case 'skladem': //Skladem
            $tWhere = $tWhere."proaccess=0 AND ";
            break;
          case 'novinka': //Novinky
            $tWhere = $tWhere."protypid2=1 AND ";
            break;
          case 'akce': //Akce
            $tWhere = $tWhere."protypid=1 AND ";
            break;
          case 'dopravazdarma': //Doprava zdarma
            $tWhere = $tWhere."prodelfree=1 AND ";
            break;
          case 'tip': //tip
            $tWhere = $tWhere."protypid3=1 AND ";
            break;
          case 'vegan': //vegan
            $tWhere = $tWhere."protypid5=1 AND ";
            break;
          case 'zlatedny': //zlate dny
            $tWhere = $tWhere."protypid4=1 AND ";
            break;
        }
      }
      if (!empty($tWhere)) $tWhere = " AND (".substr($tWhere, 0, -4).")";
      $where = $where.$tWhere;
    }
    //forma
    if (count($this->f) > 0) {
      $fWhere = implode(',', $this->f);
      if (!empty($fWhere)) $where = $where . " AND proforid IN (".$fWhere.")";
    }

    //nactu max a min cenu
    $proMinMax = dibi::fetch("
SELECT MAX(IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId.$this->userData->usrprccat.")) AS proprice_max, 
MIN(IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId.$this->userData->usrprccat.")) AS proprice_min
FROM products 
INNER JOIN catplaces ON (capproid=proid)
INNER JOIN catalogs ON (capcatid=catid)
WHERE catpathids LIKE '%|$id|%' AND
$where");
    
    if (empty($proMinMax->proprice_max)) $proMinMax->proprice_max = 0;
    if (empty($proMinMax->proprice_min)) $proMinMax->proprice_min = 0;
    if ((empty($this->pF) && empty($this->pT)) || ((int)$this->pF == (int)$proMinMax->proprice_min && (int)$this->pT == (int)$proMinMax->proprice_max)) {
      $this->pF = NULL;
      $this->pT = NULL;
    } else {
      if ((int)$this->pT > (int)$proMinMax->proprice_max) {
        $this->pT = NULL;
      } else if ((int)$this->pF < (int)$proMinMax->proprice_min) {
        $this->pF = NULL;
      } else {
        //$wherePrice = " AND IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) BETWEEN ".(int)$this->pF." AND ".(int)$this->pT;  
      }  
    }

    //cena od
    if (!empty($this->pF)) {
      $where = $where." AND IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a)>=".(int)$this->pF;  
    }
    //cena do
    if (!empty($this->pT)) {
      $where = $where." AND IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a)<=".(int)$this->pT;  
    }

    $this->catNamespace->listWhere = $where;
    $this->catNamespace->listOrderBy = $orderBy;

    if (!isset($this->template->productsData)) {
      $this->template->productsData = $this->getProductsList($id, 1, $page);
    }
    $this->template->proMinMax = $proMinMax;
    $this->template->proPriceFrom = (empty($this->pF) ? $proMinMax->proprice_min : $this->pF);
    $this->template->proPriceTo = (empty($this->pT) ? $proMinMax->proprice_max : $this->pT);
    
    //statistika prodejnosti
    $this->template->saleStatProducts = [];
    if ($rootCatId > 0) {
      $this->template->saleStatProducts = $product->getSaleStatsProducts($catalogData);
    }  
    
    //naplnim filtr vybranymi hodnotami
    if (!empty($this->m)) {
      $arr["m"] = $this->m;
    }
    if (!empty($this->o)) {
      $arr["o"] = $this->o;
    }
    if (!empty($this->t)) {
      $arr["t"] = $this->t;
    }
    if (!empty($this->f)) {
      $arr["f"] = $this->f;
    }
    if (!empty($this->pF)) {
      $arr["pF"] = $this->pF;
    }
    if (!empty($this->pT)) {
      $arr["pT"] = $this->pT;
    }

    $arr["pFDef"] = $proMinMax->proprice_min;
    $arr["pTDef"] = $proMinMax->proprice_max;

    if (!empty($arr)) {
      
      $form = $this->getComponent("catalogSearchForm");
      $form->setDefaults($arr);
    }
    //zakazu indexovani strankuje
    $page = (int)$this->getParameter('page');
    if ($page > 1) {
      $this->template->pageRobots = "noindex,follow";
    }
    
    //sestavim popisek co ma ve filtru
    $arr = array();
    //typy
    if (count($this->t) > 0) {
      $a = $this->filterTypesIdName;
      $aar = array();
      foreach ($this->t as $k => $value) {
        $aar[$k] = $a[$k];
      }
      $arr["t"] = $aar;
    }
    //vyrobci
    if (count($this->m) > 0) {
      $a = $this->filterManufacturersIdName;
      $aar = array();
      foreach ($this->m as $k => $value) {
        $aar[$k] = $a[$k];
      }
      $arr["m"] = $aar;
    }
    //forma
    if (count($this->f) > 0) {
      $a = $product->getEnumProForId();
      $aar = array();
      foreach ($this->f as $k => $value) {
        $aar[$k] = $a[$k];
      } 
      $arr["f"] = $aar;
    }
    If (!empty($priceFrom)) $arr["pF"] = $priceFrom;
    If (!empty($priceTo)) $arr["pT"] = $priceTo;

    $this->template->formVals = $arr;
    
    //sestavim výčet klíčových slov ve filtru
    $searchWords = "";
    if (count($arr) > 0) {
      $searchWords = ", ";
      foreach ($arr as $k => $value) {
        if ($k == 't' || $k == 'm' || $k == 'f') {
          foreach ($value as $name) {
            $searchWords .= $name . ", ";
          }
        } elseif ($k == 'pF') {
          $searchWords .= "Cena od: ".$this->formatPrice($value);
        } elseif ($k == 'pT') {
          $searchWords .= " do: ".$this->formatPrice($value);
        }
      }
      $searchWords = ", ".trim($searchWords, " ,");
    }

    $this->template->searchWords = ""; // $searchWords;
    //doplnim katalogove cesty pro zbozi.cz
    $zboziCatalogs = dibi::query("SELECT catid, catname, catpathzbozi FROM catalogs WHERE catpathzbozi is not null AND catpathzbozi!=''")->fetchAssoc("catid");
    $catPathIds = array_reverse($idPath); //zacnu prohledavat od konce vetve
    foreach ($catPathIds as $catid) {
      //prvni kategorii co ma zbozi cestu vezmu pro danou kategorii a vypadnu
      if (isset($zboziCatalogs[$catid])) {
        $this->template->zboziczCategoryPath = $zboziCatalogs[$catid]->catpathzbozi;
        break;
      }
    }

    //načtu dotazy a odpovědi pro danou kategorii
    $comments = dibi::fetchAll("
      SELECT comments.*, products.* 
      FROM comments
        INNER JOIN catplaces ON ((cmtcatcatid>0 OR cmtcatcatid2>0) AND (capcatid=cmtcatcatid OR capcatid=cmtcatcatid2))
        INNER JOIN catalogs ON (capcatid=catid)
        LEFT JOIN products ON (cmtproid>0 AND proid=cmtproid)
      WHERE cmtreid = 0 AND catpathids LIKE '%|$id|%'
      GROUP BY cmtid
      ORDER BY cmtid DESC
      LIMIT 30");

    foreach ($comments as $key => $row) {
      $comments[$key]->res = dibi::fetchAll("
        SELECT comments.*, " . $product->getSqlFields() . "
        FROM comments 
        LEFT JOIN products ON (cmtproid=proid) 
        WHERE cmtreid=%i", $row->cmtid, " ORDER BY cmtid DESC");
    }

    $this->template->comments = $comments;

    //autoři ke komentářům
    $this->template->authors = dibi::query("SELECT * FROM admins WHERE coalesce(admfunction, '') != '' ORDER BY admorder, admname")->fetchAssoc("admid");

    if ($this->isAjax()) {
      if (empty($this->payload->path)) {
        $this->redrawControl("productsList");
        $this->redrawControl("pageTitle");
        $this->redrawControl("sideFilter");
      }
    }

  }

  private function getProductsList($catid, $pages = 1, $page=1) {
    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat); //musim nastavit cenovou kategorii prihlaseneho uzivatele
    $product->setCurrency($this->currencies, $this->curId);
    $dataSource = $product->getDataSource($product->getSqlCatalogList($catid, $this->catNamespace->listWhere, $this->catNamespace->listOrderBy));

    $paginator = new Nette\Utils\Paginator;

    if (!empty($this->payload->setPage)) {
      $paginator->setPage((int)$this->payload->setPage);
    } else {
      $paginator->setPage($page);
    }

    $itemCount = $dataSource->count();

    if ($pages > 1) {
      $itemsOnPage = $this->config["CATALOG_ROWSCNT"] * $pages;
    } else {
      $itemsOnPage = $this->config["CATALOG_ROWSCNT"] * $paginator->page;
    }

    $this->template->isMore = FALSE;
    if ($itemCount > $itemsOnPage) {
      $this->template->isMore = TRUE;
      $diff = min($itemCount - $itemsOnPage, $this->config["CATALOG_ROWSCNT"]);
      $this->template->isMoreCnt = $diff;
    } else {
      $itemsOnPage = $itemCount;
    }

    $paginator->setItemCount($itemCount);

    $this->template->pages = max($paginator->getPage(), $pages);
    $this->template->page = $paginator->getPage();

    $paginator->itemsPerPage = $this->config["CATALOG_ROWSCNT"];

    $this->template->paginator = $paginator;

    if ($pages == 1) {
      return $dataSource->applyLimit($paginator->getItemsPerPage(), $paginator->getOffset())->fetchAll();
    } else {
      $offset = 0;
      //if ($paginator->page > 1) $offset =  $this->config["CATALOG_ROWSCNT"] * $paginator->page;
      $paginator->page = $pages;
      return $dataSource->applyLimit($itemsOnPage, $offset)->fetchAll();
    }
  }

  public function handleNextPage($pages) {
    if ($this->isAjax()) {
      $this->template->productsData = $this->getProductsList((int)$this->getParameter("id"), $pages, $pages);
      $this->redrawControl('productsList');
    } else {
      $this->redirect("this");
    }
  }


  protected function createComponentCatalogSearchForm() {
    $form = $this->createAppForm();
    
    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);

    $catid = $this->getParameter('id');
    //nactu vyrobce v katalogu
    $manufacts = dibi::query("
      SELECT manid, manname
      FROM products
      INNER JOIN catplaces ON (capproid=proid)
      INNER JOIN catalogs ON (capcatid=catid)
      INNER JOIN manufacturers ON (manid=promanid)
      WHERE prostatus=0 AND manstatus=0 AND COALESCE(promasid,0)=0 AND catpathids LIKE '%|$catid|%' 
      GROUP BY manid
      ORDER BY manname
    ")->fetchPairs("manid", "manname");
    $container = $form->addContainer('m');
    foreach ($manufacts as $key=>$name) {
      $container->addCheckbox($key, $name);
    }
    
    //$arr = $this->getEnumOrderBy();
    $form->addHidden("o", "");

    $container = $form->addContainer('t');
    foreach ($this->filterTypesIdName as $key=>$name) {
      $container->addCheckbox($key, $name);
    }
    
    $container = $form->addContainer('f');
    foreach ($this->filterFormsIdName as $key=>$name) {
      $container->addCheckbox($key, $name);
    }
    $form->addText("pF", 'Cena od', 5);  
    $form->addText("pT", 'Cena do', 5);
    $form->addHidden("pFDef");
    $form->addHidden("pTDef");
      
    $form->addSubmit('search', 'Filtrovat')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'catalogSearchFormSubmitted');
    return $form;
  }

  public function catalogSearchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $this->m = array();
      $pathArr = array();
      foreach ($vals['m'] as $key => $val) {
        if ($val && !empty($this->filterManufacturersIdKey[$key])) $pathArr[] = $this->filterManufacturersIdKey[$key];
      }
      $this->t = array();
      foreach ($vals['t'] as $key => $val) {
        if ($val && !empty($this->filterTypesIdKey[$key])) $pathArr[] = $this->filterTypesIdKey[$key];
      }
      $this->f = array();
      foreach ($vals['f'] as $key => $val) {
        if ($val && !empty($this->filterFormsIdKey[$key])) $pathArr[] = $this->filterFormsIdKey[$key];
      }

      if ((string)$vals->pF !== "" && (string)$vals->pFDef  !== "" && (double)$vals->pF !== (double)$vals->pFDef)  {
        $price = $this->formatNumberMySQL($vals->pF);
        $pathArr[] = "cena-od-".$price;
      }
      if ((string)$vals->pT !== "" && (string)$vals->pTDef  !== "" && (double)$vals->pT !== (double)$vals->pTDef)  {
        $price = $this->formatNumberMySQL($vals->pT);
        $pathArr[] = "cena-do-".$price;
      }

      if (!empty($vals["o"])) {
        $pathArr[] = $vals["o"];
      }


      $path = "";
      if (count($pathArr) > 0) {
        $path = implode("/", $pathArr);
      }


      if ($this->isAjax()) {
        $this->payload->postGet = true;
        $this->payload->url = $this->link("this", array('path' => $path, 'page' => 1));
        $this->payload->path = $path;
        $this->payload->setPage = 1;
        $this->redrawControl("productsList");
        $this->redrawControl("pageTitle");
        //$this->redrawControl("sideFilter");
      } else {
        $this->redirect("this", array('path' => $path));
      }

      //$this->redirect("this", array('path' => $path));

    }
  }
  
  /**
  * číselínk pro filtrování OrderBy
  * 
  */
  private function getEnumOrderBy() {
    return array(
       '' => 'Výchozí řazení',
       'nejlevnejsi' => 'Řadit cenu od nejnižší',
       'nejdrazsi' => 'Řadit cenu od nejvyšší',
       'a-z' => 'Řadit podle názvu A-Z',
       'z-a' => 'Řadit podle názvu Z-A',
     );
  }
}
