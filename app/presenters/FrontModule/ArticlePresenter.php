<?php
namespace FrontModule;
use dibi;
use Nette;

final class ArticlePresenter extends BasePresenter {
  
  public function renderDetailOld($id, $key) {
    $articles = $this->model->getArticlesModel();
    $articleData = $articles->load($id);
    if ($articleData) {
      //kontrola platnosti URL
      $urlkey = (!empty($articleData->arturlkey) ? $articleData->arturlkey : Nette\Utils\Strings::webalize($articleData->artname));
      //presmeruju na novy
      $this->redirect(301, 'Article:detail', array('id'=>$id, 'key'=>$urlkey));
    } else {
      throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    }
  }  
  
  public function renderDetail($id, $key) {
    $articles = $this->model->getArticlesModel();

    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);

    $articleData = $articles->load($id);
    if ($articleData) {
      if ($articleData->artstatus == 1) throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
      if ($articleData->artid > 0) {
        $this->template->urlkey = $articleData->arturlkey;
        $this->template->article = $articleData;
      }
    } else {
      throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    }
    $articles->runCounter($articleData->artid);

    //nactu prilohy
    $this->template->attachments = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype NOT IN ('jpg', 'png', 'gif')", $articleData->artid);
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype IN ('jpg', 'png', 'gif')", $articleData->artid);

    if ($articleData->artadmid > 0) {
      $this->template->author = dibi::fetch("SELECT * FROM admins WHERE admid=%i", $articleData->artadmid);
    }

    $this->template->enum_arttypid = $articles->getEnumArtTypId();

    //dotáhnu pokud je vyplněný produkt
    if (!empty($articleData->artprocode)) {
      $pro = $pros->load($articleData->artprocode, "code");
      if ($pro) {
        $this->template->product = $pro;
      }
    }
  }
}
