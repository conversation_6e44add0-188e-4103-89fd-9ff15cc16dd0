<?php
namespace FrontModule;
use dibi;
use Nette;

final class HomepagePresenter extends BasePresenter {

  /********************* view default *********************/

  public function renderDefault() {
    //seznam produktu na uvodni strance
    $this->template->homepageProducts = array();
    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $product->setCurrency($this->currencies, $this->curId);
    $homepageProducts = $product->cacheGet('homepageProducts');
    if ($homepageProducts === FALSE) {
      $homepageProducts = array();
      //z nastaveni nactu kody zbozi
      $proCodesList = explode(',',$this->config["INDEX_PRODUCTLIST"]);
      $cnt = 0;
      foreach ($proCodesList as $proid) {
        $proid = trim($proid);
        if (!empty($proid)) {
          $item = $product->load($proid);
          if ($item) {
            $homepageProducts[] = $item;
            $cnt ++;
          }
        }
        if (count($homepageProducts) >= $this->config["CATALOG_ROWSCNT_MOBI"] && $this->mobileDetect->isPhone()) break;
      }
      $product->cacheSave('homepageProducts', $homepageProducts);
    }
    $this->template->homepageProducts = $homepageProducts;

    //novinky na uvodce
    $homepageNewProducts = $product->cacheGet('homepageNewProducts');
    if ($homepageNewProducts === FALSE) {
      $homepageNewProducts = array();
      //z nastaveni nactu kody zbozi
      $proCodesList = explode(',',$this->config["INDEX_PRODUCTLISTNEW"]);
      $cnt = 0;
      foreach ($proCodesList as $proid) {
        $proid = trim($proid);
        if (!empty($proid)) {
          $item = $product->load($proid);
          if ($item) {
            $homepageNewProducts[] = $item;
            $cnt ++;
          }
        }
      }
      $product->cacheSave('homepageNewProducts', $homepageNewProducts);
    }
    $this->template->homepageNewProducts = $homepageNewProducts;

    //pripravujeme
    $homepagePrepareProducts = $product->cacheGet('homepagePrepareProducts');
    if ($homepageNewProducts === FALSE) {
      $homepagePrepareProducts = $product->fetchAll($product->getSqlList("protypid2=1", "proorder LIMIT 4"));
      $product->cacheSave('homepagePrepareProducts', $homepagePrepareProducts);
    }
    $this->template->homepagePrepareProducts = $homepagePrepareProducts;


    $pages = $this->model->getPagesModel();
    $homepageNews = $pages->cacheGet('homepageNews');
    if ($homepageNews === FALSE) {
      $homepageNews = dibi::fetchAll("SELECT * FROM news ORDER BY newdate DESC LIMIT 1");
      $pages->cacheSave('homepageNews', $homepageNews);
    }
    $this->template->news = $homepageNews;


    //nactu polozky do slideru
    $menuIndexs = $this->model->getMenuIndexsModel();
    $homepageSlides = $menuIndexs->cacheGet('homepageSlides');
    if ($homepageSlides === FALSE) {
      $homepageSlides = dibi::fetchAll("
        SELECT * 
        FROM menuindexs
        LEFT JOIN pages ON (pagid=meipagid)
        LEFT JOIN catalogs ON (catid=meicatid)
        LEFT JOIN products ON (procode=meiprocode)
        WHERE meimasid=0 AND meistatus=0 
        GROUP BY meiid
        ORDER BY meiorder");
      $menuIndexs->cacheSave('homepageSlides', $homepageSlides);
    }
    $this->template->menuIndexs = $homepageSlides;

    //naplnim access
    $this->template->enum_proaccess = $product->getEnumProAccess();
    
    /*
    $this->template->menuIndexs = dibi::fetchAll("
    SELECT * FROM menuindexs
    LEFT JOIN pages ON (pagid=meipagid)
    LEFT JOIN catalogs ON (catid=meicatid)
    LEFT JOIN products ON (procode=meiprocode)
    WHERE meimasid=0 AND meistatus=0 AND meibig=0 ORDER BY meiorder");
    */
  }
}
