<?php
namespace FrontModule;
use dibi;
use Nette;

final class CetelemPresenter extends BasePresenter {
  /** @persistent */
  public $p = array();

	public function renderCalculator() {
	  $form = $this['calculatorForm'];

    if (!$form->isSubmitted()) {
      $cetelem = new Cetelem();
      if (isset($this->p["kodBaremu"])) $cetelem->kodBaremu =  $this->p["kodBaremu"];
      if (isset($this->p["kodPojisteni"])) $cetelem->kodPojisteni = $this->p["kodPojisteni"];
      if (isset($this->p["cenaZbozi"])) $cetelem->cenaZ<PERSON>zi = $this->p["cenaZbozi"];
      if (isset($this->p["primaPlatba"])) $cetelem->primaPlatba = $this->p["primaPlatba"];
      if (isset($this->p["pocetSplatek"])) $cetelem->pocetSplatek = $this->p["pocetSplatek"];

      if (!empty($cetelem->kodBaremu)) {
        $cetelem->calculate();
        $this->p["kodPojisteni"] = $cetelem->kodPojisteni;
        $this->p["kodMaterialu"] = $cetelem->kodMaterialu;
        $this->p["vyseUveru"] = $cetelem->vyseUveru;
        $this->p["odklad"] = $cetelem->odklad;
        $this->p["vyseSplatky"] = $cetelem->vyseSplatky;
        $this->p["cenaUveru"] = $cetelem->cenaUveru;
        $this->p["RPSN"] = $cetelem->RPSN;
        $this->p["ursaz"] = $cetelem->ursaz;
        $this->p["celkovaCastka"] = $cetelem->celkovaCastka;
      }

      $form->setDefaults($this->p);

      if ($cetelem->status == 'error') {
        $form->addError($cetelem->zprava);
        return;
      }
    }
	}

  protected function createComponentCalculatorForm() {
    $form = $this->createAppForm();
    $cetelem = new Cetelem();

    $form->addSelect('kodBaremu', 'Barem (nutno vyplnit)', $cetelem->getCombo('barem'))
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addSelect('kodPojisteni', 'Pojištění (nutno vyplnit)', $cetelem->getCombo('pojisteni'))
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('cenaZbozi', 'Celková cena zboží (nutno vyplnit)')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('primaPlatba', 'Přímá platba (nutno vyplnit)')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('pocetSplatek', 'Počet splátek (nutno vyplnit)')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('vyseSplatky', 'Výše splátky')
      ->setDisabled();
    $form->addText('cenaUveru', 'Cena úvěru')
      ->setDisabled();
    $form->addText('vyseUveru', 'Výše úvěru')
      ->setDisabled();
    $form->addText('ursaz', 'Úroková sazba')
      ->setDisabled();
    $form->addText('RPSN', 'RPSN')
      ->setDisabled();
    $form->addText('celkovaCastka', 'Celková částka splatná klientem')
      ->setDisabled();

    $form->addSubmit('submit', 'Přepočítat')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'calculatorSubmitted');

    return $form;
  }

  public function calculatorSubmitted($form) {
    if ($form->isSubmitted()) {
      $this->p = $form->getValues();
      $this->redirect('calculator');
    }
  }


}
