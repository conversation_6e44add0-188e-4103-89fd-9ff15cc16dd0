<?php
namespace FrontModule;
use dibi;
use Model\UsersModel;
use Nette;
use <PERSON>\Debugger;

final class UserPresenter extends BasePresenter {

  protected function startup() {
    parent::startup();
    // autorizace zakaznika
    if ($this->action != "login" && !str_contains(strtolower($this->action), "seznam")  && !str_contains(strtolower($this->action), "google")  && !str_contains(strtolower($this->action), "apple")  && $this->action != 'mv' && $this->action != 'add' && $this->action != 'sendPassword') {
      if (!$this->user->isLoggedIn()) {
        if ($this->user->getLogoutReason() === Nette\Security\IUserStorage::INACTIVITY) {
          $this->flashMessage('Byl/a jste odhlášen/a z důvodu delší neaktivity.');
        }
        $this->redirect('login');
      }
    }
  }

  public function actionLogin() {
    if ($this->user->isLoggedIn()) $this->redirect('default');
  }

  public function actionGoogleLogin() {

  }

  public function actionAppleLogin() {

  }

  public function actionSeznamLogin() {
    $seznamOauth = new \SeznamOAuth($this->neonParameters["seznamOAuth"]);

    $usrs = $this->model->getUsersModel();

    try {
      $data = $seznamOauth->getUserData($this->getParameter("code"));
    } catch (Nette\Security\AuthenticationException $e) {
      Debugger::log($e->getMessage());
      $this->flashMessage("Autorizace se nezdařila, zkuste to znovu nebo nás kontaktujte.");
      $this->redirect("login");
    }

    $verify = FALSE;

    if ($data) {
      //přihlásím na účet, nebo zaregistruji
      //najdu úšet se seznam emailem
      $usr = $usrs->load($data["email"], "mail");
      if ($usr) {
        //je účet aktivní?
        if ($usr->usrstatus == 1) {
          $this->flashMessage("Váš účet je blokovaný, prosím kontaktujte nás", "success");
          $this->redirect("login");
        }
      } else {
        //registruji
        $id = $usrs->insert(
          [
            "usrmail" => $data["email"],
            "usrpassw" => $usrs->generatePassword(),
            "usrmailverified" => 1,
            "usrmaillist" => 1,
          ]
        );

        $usr = $usrs->load($id);

        $this->logGdprEventsNewUser($id, 1);

        $mailTemplate = $this->createTemplate();
        $mailTemplate->userRow = $usr;
        $mailTemplate->usrpassw = "přihlášení přes seznam.cz";
        $mailTemplate->seznamLogin = TRUE;
        $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailUserMailAdd.latte');

        $this->mailSend($usr->usrmail, $this->translator->translate("Registrace"), $mailTemplate);
        $mailTemplate->usrpassw = "";
        $this->mailSend($this->config["SERVER_MAIL"], $this->translator->translate("Nová registrace - seznam"), "Pravě se registroval nový uživatel: ".$usr->usriname." ".$usr->usrilname.' <a href="mailto:'.$usr->usrmail.'">'.$usr->usrmail.'</a>');
      }

      $this->user->login(new Nette\Security\Identity($usr->usrid, self::LOGIN_NAMESPACE));
      $this->userData = $usr;

      $this->justLoggedIn();

      $this->flashMessage("Přihlášení proběhlo úspěšně", "success");

      if (!empty($this->appNamespace->loggedFrom)) {
        if ($this->appNamespace->loggedFrom === "basket") {
          $this->redirect('Basket:orderContact');
        } else {
          $this->redirect('this');
        }
      }

      $this->redirect("default");
    }

    $this->redirect("login");
  }

  public function actionInitSeznamLogin() {
    if ($this->user->isLoggedIn()) $this->redirect('default');

    //odkud se hlásí, tam přesměruji

    $from = (string)$this->getParameter("from");

    $this->appNamespace->loggedFrom = $from;

    $seznamOauth = new \SeznamOAuth($this->neonParameters["seznamOAuth"]);
    $url = $seznamOauth->getOpenUrl();
    $this->redirectUrl($url);
  }

  /**
   * kopie objednávky do košíku
   *
   * @param $ordId
   * @throws \DibiException
   */
  public function actionCopyOrder($ordId) {
    $rows = dibi::fetchAll("
        SELECT oriproid, oriqty, proqty, prostatus
        FROM orditems
        INNER JOIN products ON (proid=oriproid)
        WHERE 
              oriordid=%i", $ordId, " AND 
              oritypid=0 AND 
              oriproid > 0  
        ");
    $itemOutOfStock = false;
    $qtySmaller = false;
    if ($rows !== FALSE) {
      foreach ($rows as $row) {
        if ((int)$row->prostatus > 0 || (int)$row->proqty === 0) {
          $itemOutOfStock = true;
          continue;
        }

        //zjistim pocet kusu skladem a pokud je méně odečtu
        $qty = $row->oriqty;
        if ($row->oriqty > $row->proqty) {
          $qty = $row->proqty;
          $qtySmaller = true;
        }
        $this->updateBasketItem($row->oriproid, $qty);
      }
      if ($itemOutOfStock){
        $this->flashMessage("Některé položky nebyly vloženy do košíku, protože aktuálně nejsou dostupné.");
      }

      if ($qtySmaller){
        $this->flashMessage("U některých položek byl snížen počet kusů, protože aktuálně nemáme skladem požadované množství.");
      }

      $this->flashMessage("Položky vybrané objednávky (pokud jsou dostupné) byly vloženy do košíku. Původní zboží v košíku (pokud jste nějaké měli) bylo v košíku ponecháno.");
      $this->redirect("Basket:default");
    }
  }

  /**
   * mailuje verifikační kód
   *
   * @throws Nette\Application\AbortException
   */
  public function actionSendVerification() {
    $this->sendVerification();
    $this->redirect('default');
  }

  public function actionMv($k) {
    $arr = explode('-', $k);
    $usrid = 0;
    $isVerified = false;
    if (isset($arr[0])) {
      $usrid = $arr[0];
    }
    $usrs = $this->model->getUsersModel();
    $usr = $usrs->load($usrid);
    if ($usr && isset($arr[1]) && !empty($usr->usrmailvcode)) {
      $key = substr(md5($usr->usrid . $usr->usrmailvcode), 0, 6);
      if ($key === $arr[1]) {
        $usrs->update($usr->usrid, array(
          'usrmailvcode' => NULL,
          'usrmailverified' => 1
        ));
        $usrs->logEvent($usr->usrid, UsersModel::EVENT_MAIL_VERIFIED);
        $isVerified = TRUE;
      }
    }

    if ($isVerified) {
      $this->flashMessage("Váš email byl úspěšně ověřen. Děkujeme.", "success");
    } else {
      $this->flashMessage("Váš email se nepodařilo ověřit.", 'danger');
    }

    if ($this->userData->usrid > 0) {
      $this->redirect('User:default');
    }
    $this->redirect('Homepage:default');
  }

  public function actionLogout() {
    $user = $this->user;
    $user->logout();
    $this->flashMessage("Odhlášení proběhlo úspěšně.");
    $this->redirect('login');
  }

  public function actionWatchDogStore($proid) {
    $pro = dibi::fetch("SELECT proid, proname, prokey FROM products WHERE proid=%i", $proid);
    if ($pro) {
      if ($this->userData->usrid > 0) {
        $dogid = (int)dibi::fetchSingle("SELECT dogid FROM watchdogs WHERE dogstore=1 AND dogproid=%i", $pro->proid, " AND dogusrid=%i", $this->userData->usrid);
        $dogs = $this->model->getWatchdogsModel();
        if ($dogid > 0) {
          //dog je, tak ho smazu
          $dogs->delete($dogid);
        } else {
          //dog neni, vytvorim
          $dogs->insert(array(
            'dogproid'=> $pro->proid,
            'dogstore'=> 1,
            'dogusrid'=> $this->userData->usrid,
          ));
        }
      }  
      $urlkey = (!empty($pro->prokey) ? $pro->prokey : \Nette\Utils\Strings::webalize($pro->proname));
      $this->redirect('Product:detail', $pro->proid, $urlkey);      
    } else {
      throw new NBadRequestException('Položka nenalezena', '404'); 
    }
  }
  
  public function actionWatchDogPrice($id) {
    $pro = dibi::fetch("SELECT proid, proname, prokey FROM products WHERE proid=%i", $id);
    if ($pro) {
      if ($this->userData->usrid > 0) {
        $dogid = (int)dibi::fetchSingle("SELECT dogid FROM watchdogs WHERE dogprice=1 AND dogproid=%i", $pro->proid, " AND dogusrid=%i", $this->userData->usrid);
        $dogs = $this->model->getWatchdogsModel();
        if ($dogid > 0) {
          //dog je, tak ho smazu
          $dogs->delete($dogid);
        } else {
          //dog neni, vytvorim
          $dogs->insert(array(
            'dogproid'=> $pro->proid,
            'dogprice'=> 1,
            'dogusrid'=> $this->userData->usrid,
          ));
        }
      }  
      $urlkey = (!empty($pro->prokey) ? $pro->prokey : \Nette\Utils\Strings::webalize($pro->proname));
      $this->redirect('Product:detail', $pro->proid, $urlkey);      
    } else {
      throw new NBadRequestException('Položka nenalezena', '404'); 
    }
  }
  
  /********************* view default *********************/

  public function renderDefault() {
    $user = $this->model->getUsersModel();

    $userRow = $user->load($this->user->getIdentity()->id);
    $this->template->userRow = $userRow;

    //otevrene objednavky
    $this->template->openedOrders = dibi::fetchAll("SELECT * FROM orders WHERE ordusrid=$userRow->usrid AND orddatec + INTERVAL 2 HOUR > NOW() ORDER BY ordid DESC");
    //uzavrene objednavky
    $this->template->closedOrders = dibi::fetchAll("SELECT * FROM orders WHERE ordusrid=$userRow->usrid AND orddatec + INTERVAL 2 HOUR <= NOW() ORDER BY ordid DESC");
    $orders = $this->model->getOrdersModel();
    $this->template->enum_ordstatus = $orders->getEnumOrdStatus();
  }

  /********************* view add, edit *********************/
  public function renderAdd() {
    if ($this->user->isLoggedIn()) $this->redirect('default');
    $form = $this->getComponent('userAddForm');
    $form['save']->caption = 'Registrovat';
    $this->template->form = $form;
    $this->template->blockPromoRegistrace = dibi::fetch("SELECT * FROM pages WHERE pagurlkey='promo_registrace' AND pagstatus=0");
  }

  public function renderEdit() {
    $id = $this->getUser()->getIdentity()->id;
    $form = $this->getComponent("userEditForm");

    if (!$form->isSubmitted()) {
      $user = $this->model->getUsersModel();
      $userRow = $user->load($id);
      if (!$userRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }


      $userRow["stadr"] = ($userRow["usrstname"] != "");
      $form->setDefaults($userRow);
    }
  }

  public function renderOrder($id) {
    $order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
    if ($order===false) throw new Nette\Application\BadRequestException('Objednávka nenalezena', '404');
    //kontrola zda tato objednavka patri prihlasenemu
    if ($this->userData->usrid != $order->ordusrid) throw new Nette\Application\BadRequestException('Objednávka nenalezena', '404');
    $this->template->order = $order;

    $enums = $this->model->getEnumcatsModel();
    $delModes = $this->model->getDeliveryModesModel();
    $delModes->setCurrency($this->currencies, $order->ordcurid);
    $this->template->payMode = $delModes->load($order->orddelid);
    $this->template->delMode = $delModes->load($this->template->payMode->delmasid);
    $this->template->enum_countries = $enums->getEnumCountries();
    $this->template->ordItems = dibi::fetchAll("SELECT * from orditems WHERE oriordid=%i", $order->ordid, " ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END");
  }

  public function renderBookmarks() {
    $this->template->bookmarks = dibi::fetchAll("
      SELECT proid, protypid, promasid, proismaster, protypid2, protypid3, protypid4, protypid5, prokey, procode, proname AS proname1, 
      concat(proname, ' ', proname2) AS proname, proname2, propicname, prodescs, proaccess, proaccesstext, proqty, proprice".$this->curId."com AS pricecom, 
      IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) AS proprice, 
      proprice".$this->curId."a AS propricea, proprice".$this->curId."b AS propriceb, proprice".$this->curId."c AS propricec, proprice".$this->curId."d AS propriced, 
      proprice".$this->curId."com AS propricecom, provatid, prostatus, proorder, manname, prorating, ".($this->curId == 1 ? "prodelfree" : "0 AS prodelfree").", pronotdisc
      FROM bookmarks 
      INNER JOIN products ON (bokproid=proid)
      INNER JOIN manufacturers ON (promanid=manid)
      WHERE bokusrid=%i", $this->userData->usrid);
  }
  
  public function sendPasswordFormSubmitted($form) {
    if ($this->user->isLoggedIn()) $this->redirect('login');

    //kontrola zda takovy email existuje v DB
    if ($form['submit']->isSubmittedBy()) {
      $user = $this->model->getUsersModel();
      $formVars = $form->getValues();
      $dataRow = $user->load($formVars["usrmail"], 'mail');
      if ($dataRow && $dataRow["usrid"] > 0) {
        //zmenim heslo a poslu email
        $newPassw = $this->getVerifyCode();
        $vals = array('usrpassw' => md5($newPassw));
        if ($user->update($dataRow->usrid, $vals)) {
          //odmailuju nove heslo
          $this->template->newPassword = $newPassw;
          $this->template->setFile(WWW_DIR.'/../templates/Mails/mailUserSendPassword.latte');

          if (!$this->mailSend($dataRow->usrmail, $this->translator->translate("Žádost o nové heslo"), $this->template)) $form->addError('Nové heslo se nepodařilo odeslat');
          $this->flashMessage("Email s novým heslem byl odeslán.");
          $this->redirect('this');
        } else {
          $form->addError('Nové heslo se nepodařilo nastavit');
        }

      } else {
        $form->addError('Zadaný email nebyl nalezen');
      }
    }
  }

  public function userFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $id = 0;
      if ($this->user->isLoggedIn()) {
        $id = $this->user->getIdentity()->id;
      }
      $formVars = $form->getValues();

      unset($formVars["antispam"]);

      //kontrola duplicity emailu
      $cnt = dibi::fetchSingle("SELECT COUNT(usrid) FROM users WHERE usrmail='".$formVars["usrmail"]."'".($id > 0 ? " AND usrid != $id" : ""));
      if ($cnt > 0) {
        $form->addError("Účet s tímto emailem už existuje. Pokud jste zapomněli heslo, požádejte si o heslo nové.");
      }

      //zkontroluji PSČ
      if (isset($formVars["usripostcode"])) {
        $ret = $this->checkPostCodeFormat($formVars["usripostcode"]);
        if ($ret !== TRUE) {
          $form->addError("PSČ má špatný formát. " . $ret);
        }
      }
      if (isset($formVars["usrstpostcode"])) {
        $ret = $this->checkPostCodeFormat($formVars["usrstpostcode"]);
        if ($ret !== TRUE) {
          $form->addError("PSČ má špatný formát. " . $ret);
        }

        $formVars["usripostcode"] = trim(str_replace(" ", "", $formVars["usripostcode"]));
        $formVars["usrstpostcode"] = trim(str_replace(" ", "", $formVars["usrstpostcode"]));
      }

      if ($form->hasErrors()) {
        return;
      }

      $user = $this->model->getUsersModel();
      if ($id > 0) {
        //editace zaznamu

        //zjistim jestli nebyl zmenen email
        $mailNewCode = false;
        $userRow = $user->load($id);
        if (isset($formVars["usrmail"]) && $formVars["usrmail"] != $userRow->usrmail) {
          $mailNewCode = true;
          $formVars["usrmailvcode"] = NULL;
          $formVars["usrmailverified"] = 0;
        }
        //zjistim jestli nebyl zmenen status zasílání emailů
        if ($formVars["usrmaillist"] != $userRow->usrmaillist) {
          if ($formVars["usrmaillist"] == FALSE) {
            $user->logEvent($id, \Model\UsersModel::EVENT_MAILLIST_REM);
          } else if ($formVars["usrmaillist"] == TRUE) {
            $user->logEvent($id, \Model\UsersModel::EVENT_MAILLIST_ADD);
          }
        }

        //kontrola pokud chce zmenit heslo
        $passw_changed = false;
        if ($formVars["usrpassw_old"] != "") {
          if (md5($formVars["usrpassw_old"]) != $userRow["usrpassw"]) {
            $this->flashMessage("Původní heslo jste nevyplnil/a správně. Heslo nebylo změněno");
            unset($formVars["usrpassw"]);
          } else {
            $formVars["usrpassw"] = md5($formVars["usrpassw"]);
            $passw_changed = true;
          }
        } else {
          unset($formVars["usrpassw"]);
        }

        unset($formVars["usrpassw_old"]);
        unset($formVars["usrpassw2"]);
        unset($formVars["stadr"]);

        $user->update($id, $formVars);

        //mailuji žádost o verifikaci emailu
        if ($mailNewCode) {
          $this->flashMessage('Váš email byl změněn. Je nutné ho ověření.');
          $this->sendVerification(TRUE);
        }
        $this->flashMessage('Vaše údaje byly aktualizovány.');
        if ($passw_changed) {
          $this->flashMessage('Heslo bylo změněno, prosím znovu se přihlašte.', "success");
          $this->redirect('logout');
        } else {
          $this->redirect('edit');
        }
      } else {
        //novy zazanam
        $formVars = $form->getValues();
        $formVars["usrdiscount"] = $this->config["DEFAULT_DISCOUT"];
        $formVars["usrprccat"] = 'b';
        $mailTemplate = $this->createTemplate();
        //ulozim si do sablony hesle nez se zaheshuje
        $mailTemplate->usrpassw = $formVars["usrpassw"];

        //uklidim promenne ktere nejsou v objektu user
        unset($formVars["usrpassw2"]);
        unset($formVars["usrgdpr"]);
        unset($formVars["antispam"]);

        //naplnim overovaci kody
        $formVars["usrmailvcode"] = $this->getVerifyCode();
        $formVars["usrmailverified"] = 0;
        $formVars["usrpassw"] = md5($formVars["usrpassw"]);

        //ulozim novou registraci
        $id = $user->insert($formVars);

        if ($id > 0) {
          //zaloguji, regiistraci
          $this->logGdprEventsNewUser($id, $formVars["usrmaillist"]);

          //naplnim row
          $userRow = $user->load($id);

          $mailTemplate->userRow = $userRow;
          $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailUserMailAdd.latte');

          $this->mailSend($userRow->usrmail, $this->translator->translate("Registrace"), $mailTemplate);
          $mailTemplate->usrpassw = "";
          $this->mailSend($this->config["SERVER_MAIL"], $this->translator->translate("Nová registrace"), "Pravě se registroval nový uživatel: ".$userRow->usriname." ".$userRow->usrilname.' <a href="mailto:'.$userRow->usrmail.'">'.$userRow->usrmail.'</a>');

          //prihlasim
          //$this->user->setExpiration('+ 14 days');
          $this->user->login($form['usrmail']->getValue(), $form['usrpassw']->getValue(), self::LOGIN_NAMESPACE);

          $this->userData = $userRow;
          $this->sendVerification(FALSE);
        }
        $this->flashMessage('Vaše registrace byla přijata a nyní jste přihlášen/a na svůj účet. Na Váš email jsme Vam poslali přihlašovací údaje.', "success");
        $this->redirect('default');
      }
    }
  }

  /********************* facilities *********************/
  protected function createComponentSetVerifyCodeForm() {
    //prihlasovaci form
    $form = $this->createAppForm();
    $form->addText('usrmailvcode', 'Ověřovací kód:')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');
    $form->addSubmit('submit', 'Ověřit email')->getControlPrototype()->class('btn btn--big');
    $form->onSuccess[] = array($this, 'setVerifyCodeFormSubmitted');
    return $form;
  }

  public function setVerifyCodeFormSubmitted($form) {
    if ($this->userData->usrid == 0) {
      $this->flashMessage('Nejprve se prosím přihlašte.');
      $this->redirect('login');
    }
    //kontrola zda takovy email existuje v DB
    if ($form->isSubmitted()) {
      $formVars = $form->getValues();
      if ($formVars->usrmailvcode === $this->userData->usrmailvcode) {
        $usrs = $this->model->getUsersModel();
        $usrs->update($this->userData->usrid, array(
          'usrmailverified' => 1,
          'usrmailvcode' => NULL
        ));
        $usrs->logEvent($this->userData->usrid, UsersModel::EVENT_MAIL_VERIFIED);
        $this->flashMessage('Email byl úspěšně ověřen.');
      } else {
        $this->flashMessage('Email se nepodařilo ověřit.', 'danger');
      }
      $this->redirect('default');
    }
  }


  protected function createComponentSendPasswordForm() {
    //prihlasovaci form
    $form = $this->createAppForm();
    $form->addText('usrmail', 'Email:')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Vaše přihlašovací jméno (email).')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"])
      ->setOption('description', 'test proti robotum')
      ->setRequired('Vyplňte prosím číslo ' . $this->config["ANTISPAM_NO"] . ', jde o test proti robotum')
      ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"])
      ->setHtmlId('antispam');

    $form->addSubmit('submit', 'Zaslat heslo')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'sendPasswordFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }


  /**
  * editace, registrace novych zakazniku
  */
  protected function createComponentUserAddForm() {
    $user = $this->model->getUsersModel();
    $form = $this->createAppForm();

    //prihlasovaci udaje
    $form->addText('usrmail', 'Váš email', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');
    
    $form->addText('usriname', 'Jméno', 30);

    $form->addText('usrilname', 'Přijmení', 30);
    
    $form->addPassword('usrpassw', 'Heslo', 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);

    $form->addPassword('usrpassw2', 'Heslo podruhé', 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo podruhé.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Heslo', $form["usrpassw"]);

    $form->addCheckbox("usrmaillist", "Chci dostávat informace o novinkách a akčních nabídkách")
      ->setDefaultValue(true);

    $form->addCheckbox("usrgdpr", "")
      ->addCondition(Nette\Forms\Form::EQUAL, FALSE)
        ->addRule(Nette\Forms\Form::FILLED, 'Pro registraci je nutné souhlasit se zpracováním osobních údajů.');

    $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"],  10)
      ->setHtmlId('antispam')
      ->setRequired('Vyplňte prosím číslo ' . $this->config["ANTISPAM_NO"] . ', jde o test proti robotum')
      ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);

    $form->addSubmit('save', 'Zaregistrovat se')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'userFormSubmitted');
    
    return $form;
  }

  protected function createComponentUserEditForm() {
    $user = $this->model->getUsersModel();
    $enums = $this->model->getEnumcatsModel();
    $enumCountries = $enums->getEnumCountries();
    $form = $this->createAppForm();

    //prihlasovaci udaje
    $form->addGroup('Přihlašovací údaje');
    $form->addText('usrmail', 'Email', 30)
      ->setOption('description', ' slouží zároveň jako přihlašovací jméno')
      ->setEmptyValue('@')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');
    $form->addCheckbox("usrmaillist", "Chci dostávat informace o novinkách a akčních nabídkách")
      ->setDefaultValue(true);

    //Adresa dodani
    $form->addGroup('Fakturační adresa');

    $form->addText('usrilname', 'Přijmení', 30);

    $form->addText('usriname', 'Jméno', 30)
      ->addConditionOn($form["usrilname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('usrifirname', 'Název firmy', 30);

    $form->addText('usristreet', 'Ulice', 30)
      ->addConditionOn($form["usrilname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('usristreetno', 'Číslo popisné', 30)
      ->addConditionOn($form["usrilname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('usricity', 'Město, obec', 30)
      ->addConditionOn($form["usrilname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('usripostcode', 'PSČ', 6)
      ->addConditionOn($form["usrilname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.')
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Prosím vyplňte minimálně %d číslic', 5)
        ->addRule(Nette\Forms\Form::MAX_LENGTH, 'Prosím vyplňte maximálně %d znaků', 6);

    $form->addSelect("usricouid", "Země", $enumCountries)
      ->setTranslator(Null)
      ->addCondition(Nette\Forms\Form::EQUAL, 1)
        ->toggle("icDic");

    $form->addText('usrtel', 'Telefon', 20);

    $form->addText('usric', 'IČ', 15)
      ->setOption('description', ' 10 číslic, bez mezer');
    $form->addText('usrdic', 'DIČ', 15);

    //fakturacni adresa
    $form->addGroup('Adresa dodání');

    $form->addText('usrstlname', 'Přijmení', 30);

    $form->addText('usrstname', 'Jméno', 30)
      ->addConditionOn($form["usrstlname"], Nette\Forms\Form::FILLED)
      ->addRule(Nette\Forms\Form::FILLED, "Prosím vyplňte jméno");

    $form->addText('usrstfirname', 'Název firmy', 30)
      ->addConditionOn($form["usrstlname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "Prosím vyplňte název firmy");

    $form->addText('usrststreet', 'Ulice', 30)
      ->addConditionOn($form["usrstlname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "Prosím vyplňte ulici");

    $form->addText('usrststreetno', 'Číslo popisné', 30)
      ->addConditionOn($form["usrstlname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "Prosím vyplňte číslo popisné.");

    $form->addText('usrstcity', 'Město, obec', 30)
      ->addConditionOn($form["usrstlname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "Prosím vyplňte obec/město");

    $form->addText('usrstpostcode', 'PSČ', 6)
      ->addConditionOn($form["usrstlname"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "Prosím vyplňte PSČ")
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Prosím vyplňte minimálně %d číslic', 5)
        ->addRule(Nette\Forms\Form::MAX_LENGTH, 'Prosím vyplňte maximálně %d znaků', 6);

    $form->addSelect("usrstcouid", "Země", $enumCountries)
      ->setTranslator(Null)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte zemi.');

    $form->addGroup('Změna hesla');
    $form->addPassword('usrpassw_old', 'Původní heslo', 20)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);
    $form->addPassword('usrpassw', 'Nové heslo', 20)
      ->addConditionOn($form["usrpassw_old"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte nové heslo.')
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);
    $form->addPassword('usrpassw2', 'Heslo podruhé', 20)
      ->setRequired(false)
      ->addConditionOn($form["usrpassw_old"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Nové heslo', $form["usrpassw"])
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);

    $form->addGroup();
    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'userFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  protected function createComponentUserDeleteForm() {
    //prihlasovaci form
    $form = $this->createAppForm();

    $form->addPassword('usrpassw', 'Vaše platné přihlašovací heslo', 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addSubmit('submit', 'Zrušit účet')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'userDeleteFormSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  public function userDeleteFormSubmitted($form) {
    if ($form->isSubmitted()) {
      if ($this->userData->usrid === 0) {
        $this->flashMessage('Nejprve se prosím přihlašte.');
        $this->redirect('login');
      }
      $usrs = $this->model->getUsersModel();
      $usrid = $this->userData->usrid;
      $usr = $usrs->load($usrid);
      $formVars = $form->getValues();

      //ověřím heslo
      if (md5($formVars['usrpassw']) !== $usr->usrpassw) {
        $this->flashMessage('Zadal/a jste neplatné heslo. Účet není možné zrušit.');
        $this->redirect('edit');
      } else {
        $usrs->clearPersonalData($this->userData->usrid);
        $this->user->logout();
        $this->flashMessage("Vaše registrace byla zrušena.");
        $this->redirect('User:login');
      }
    }
  }

  protected function getVerifyCode($length = 6) {
    $base = "abcdefghjkmnpqrstwxyz123456789";
    $max = strlen($base)-1;
    $string = "";
    mt_srand((double)microtime()*1000000);
    while (strlen($string) < $length) $string .= $base[mt_rand(0,$max)];
    return $string;
  }
}
