<?php
namespace FrontModule;
use dibi;
use Model\ProductsModel;
use Nette;

final class ProductListPresenter extends BasePresenter {
  /** @persistent */
  public $id = '';
  
  /** @persistent */
  public $code = '';
  
  /** @persistent */
  public $name = '';

  /** @persistent */
  public $manid = '';
  
  /** @persistent */
  public $catid = '';
  
  /** @persistent */
  public $status = '0';

  /** @persistent */
  public $access = '0';

  /** @persistent */
  public $orderby = '';
  
  /** login */
  protected $loginNamespace;

  protected function startup() {
    parent::startup();

    $this->loginNamespace = $this->getSession('app');
    $this->loginNamespace->setExpiration(time()+3600);
  }  
  
  public function actionDoBatchOrder() {
    if ($this->userData->usrprccat != 'c' && $this->userData->usrprccat != 'd' && $this->userData->usrprccat != 'e') {
      $this->flashMessage("Zde nemáte přístup. Kontaktujte správce eshopu.", "danger");
      $this->redirect("Homepage:default");
    }
    $pros = $this->model->getProductsModel();
    $items = $_POST["i"];
    $qtyChanged = FALSE;
    foreach ($items as $id => $cnt) {
      $pro = $pros->load($id);
      if ((int)$cnt > 0) {
        if ((int)$cnt > $pro->proqty) {
          $cnt = (int)$pro->proqty;
          $qtyChanged = TRUE;
        }
        $this->basketNamespace->items[$id] = (int)$cnt;
      }
    }
    if ($qtyChanged) {
      $this->flashMessage("U některých položek bylo upraveno objednané množství.", "err");
    }
    $this->redirect("Basket:default");  
  }
  
  public function renderDefault () {
    
    if ($this->loginNamespace->login) {
      $this->id = $this->getParam("id");
      $this->code = $this->getParam("code");
      $this->name = $this->getParam("name");
      $this->manid = $this->getParam("manid");
      $this->catid = $this->getParam("catid");
      $this->status = $this->getParam("status");
      $this->orderby = $this->getParam("orderby");

      $where  = $this->getWhere();

      $where .= ($where != "" ? " AND " : "")." promasid=0 ";
      
      $product = $this->model->getProductsModel();
      $product->setPrcCat($this->userData->usrprccat); //musim nastavit cenovou kategorii prihlaseneho uzivatele
      $product->setCurrency($this->currencies, $this->curId);
      if ((int)$this->catid === 0) {
        $dataSource = $product->getDataSource($product->getSqlList($where, $this->orderby));
      } else {
        $dataSource = $product->getDataSource($product->getSqlCatalogList($this->catid, $where, $this->orderby));
      }
      $paginator = $this['paginator']->getPaginator();
      $paginator->itemsPerPage = 30;
      $paginator->itemCount = $dataSource->count();
      $this->template->productsData = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    }
    $this->template->isLoggedIn = $this->loginNamespace->login;  
  }
  
  public function renderOrderBatch() {
    
    if ($this->userData->usrprccat != 'c' && $this->userData->usrprccat != 'd' && $this->userData->usrprccat != 'e') {
      $this->flashMessage("Zde nemáte přístup. Kontaktujte správce eshopu.", "danger");
      $this->redirect("Homepage:default");
    }  
      
    $this->id = $this->getParam("id");
    $this->code = $this->getParam("code");
    $this->name = $this->getParam("name");
    $this->manid = $this->getParam("manid");
    $this->catid = $this->getParam("catid");
    $this->access = $this->getParam("access");
    $this->orderby = $this->getParam("orderby");

    $where = "pro.proaccess=$this->access AND (promas.proaccess IS NULL OR promas.proaccess = $this->access)";
    if (!empty($this->id)) {
      $where .= ($where != "" ? " AND " : "")."pro.proid=".$this->id;
    }
    if (!empty($this->code)) {
      $where .= ($where != "" ? " AND " : "")."pro.procode LIKE '%".$this->code."%'";
    }
    if (!empty($this->name)) {
      $where .= ($where != "" ? " AND " : "")."pro.proname LIKE '%".$this->name."%'";
    }
    if (!empty($this->manid)) {
      $where .= ($where != "" ? " AND " : "")."pro.promanid=".$this->manid;
    }
    if (!empty($this->catid)) {
      $where .= ($where != "" ? " AND " : "")."catid=".$this->catid;
    }

    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat); //musim nastavit cenovou kategorii prihlaseneho uzivatele
    $product->setCurrency($this->currencies, $this->curId);
    $orderby = $this->orderby;
    if ($orderby != "") $orderby = "ORDER BY pro.$orderby";
    if ($where != "") $where = " AND $where";
    $productsData = dibi::fetchAll("
      SELECT pro.proid, pro.procode, concat(pro.proname, ' ', pro.proname2) AS proname, pro.proname2, pro.prokey,
      pro.proprice".$this->curId."a AS propricea, pro.proprice".$this->curId."b AS propriceb, pro.proprice".$this->curId."c AS propricec, pro.proprice".$this->curId."d AS propriced, pro.proprice".$this->curId."e AS propricee,
      pro.proqty, pro.proaccess, pro.proaccesstext
      FROM products AS pro
      LEFT JOIN manufacturers ON (pro.promanid=manid)
      LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
      LEFT JOIN catplaces ON (capproid=coalesce(promas.proid,pro.proid))
      LEFT JOIN catalogs ON (capcatid=catid)
      WHERE pro.proprice".$this->curId."c>0 AND catstatus=0 AND pro.prostatus=0 AND pro.proismaster!=1 AND (promas.prostatus IS NULL OR promas.prostatus = 0) 
      $where
      GROUP BY pro.proid
      $orderby
    ");

    $this->template->productsData = $productsData;
    $this->template->enu_proAccess = $product->getEnumProAccess();
  }

  protected function createComponentPLSearchForm() {
    $pros = $this->model->getProductsModel();
    $form = $this->createAppForm();
    $form->addGroup();
    $form->addText("id", "Id:", 5)
      ->setDefaultValue($this->id);
      
    $form->addText("code", "Kód:", 10)
      ->setDefaultValue($this->code);
      
    $form->addText("name", "Název:", 40)
      ->setDefaultValue($this->name);
    
    $form->addSelect("manid", "Výrobce:", $pros->getEnumProManId())
      ->setPrompt("");
    if (!empty($this->manid)) $form["manid"]->setDefaultValue($this->manid);
      
    $form->addSelect("access", "Dostupnost:", $pros->getEnumProAccess())
      ->setPrompt("");
    if (!empty($this->access) || $this->access == 0) $form["access"]->setDefaultValue($this->access);
    
    //order by
    $arr = array(
      'procode ASC' => 'Kódu A-Z',
      'procode DESC' => 'Kódu Z-A',
      'proname ASC' => 'Názvu A-Z',
      'proname DESC' => 'Názvu Z-A',
      'propricecom ASC' => 'Běžné ceny 0-9',
      'propricecom DESC' => 'Běžné ceny 9-0',
      'propricea ASC' => 'Cena e-shop 0-9',
      'propricea DESC' => 'Cena e-shop 9-0',
      'propriceb ASC' => 'Cena V.I.P. 0-9',
      'propriceb DESC' => 'Cena V.I.P. 9-0',
      'propricec ASC' => 'Cena B 0-9',
      'propricec DESC' => 'Cena B 9-0',
      'propriced ASC' => 'Cena A 0-9',
      'propriced DESC' => 'Cena A 9-0',
    );
    $form->addSelect("orderby", "Seřadit podle:", $arr);
    if (!empty($this->orderby)) $form["orderby"]->setDefaultValue($this->orderby);

    $form->addSubmit('detailSearch', 'Hledej');
    $form->onSuccess[] = array($this, 'searchPLFormSubmitted');
    return $form;
  }

  public function searchPLFormSubmitted(Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $vals = $form->getValues();

      $this->id = $vals["id"];
      $this->name = $vals["name"];
      $this->code = $vals["code"];
      $this->manid = $vals["manid"];
      $this->access = $vals["access"];
      if (!empty($vals["catid"])) $this->catid = $vals["catid"];
      $this->orderby = $vals["orderby"];
      
      $this->redirect('this');

      $this->redirect('ProductList:default');
    }
  }

  protected function createComponentPLLoginForm() {
    $form = $this->createAppForm();
    
    $form->addPassword("pssw", "Heslo:", 5);
      
    $form->addSubmit('submit', 'Odeslat');
    $form->onSuccess[] = array($this, 'pLLoginSubmitted');
    return $form;
  }

  public function pLLoginSubmitted(Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $this->loginNamespace->login = ($vals["pssw"] == $this->config["PRICELIST_PASSW"]);
      if (!$this->loginNamespace->login) $this->flashMessage("Zadali jste neplatné heslo.", "err");
      $this->redirect('this');
    }
  }
  
  
  Private function getWhere() {

    $where = "";
    if (!empty($this->id)) {
      $where .= ($where != "" ? " AND " : "")."proid=".$this->id;
    }
    if (!empty($this->code)) {
      $where .= ($where != "" ? " AND " : "")."procode LIKE '%".$this->code."%'";
    }
    if (!empty($this->name)) {
      $where .= ($where != "" ? " AND " : "")."proname LIKE '%".$this->name."%'";
    }
    if (!empty($this->manid)) {
      $where .= ($where != "" ? " AND " : "")."promanid=".$this->manid;
    }
    if (!empty($this->access) || $this->access == 0) {
      $where .= ($where != "" ? " AND " : "")."proaccess=$this->access";
    }
    return $where;
  }
}