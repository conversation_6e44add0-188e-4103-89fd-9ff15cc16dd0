<?php

namespace FrontModule;
use dibi;
use Model\OrdersModel;
use Nette;
use <PERSON>\Debugger;
use <PERSON>\Logger;

final class MalPayPresenter extends BasePresenter {

  private $payUConfig;

  protected function startup() {
    parent::startup();
    $this->payUConfig = $this->neonParameters["payUApi"];
  }

  public function actionCreate($ordId, $key) {
    $ords = $this->model->getOrdersModel();
    $ord = $ords->load($ordId);
    $keyO = md5($ord->ordid . $ord->orddatec);
    if ($keyO != $key) {
      $this->flashMessage("Špatné volání stránky", "err");
    }

    try {
      $mallPay = new \MallPayApi();
      $mallPay->create($ordId);
      $this->terminate();
    } catch (Exception $e) {
      $this->flashMessage("Nastala chyba " . $e->getMessage(), "err");
      $this->redirect("default");
    }
  }

  public function renderNotify() {
    $this->terminate();
  }

  public function renderRetrieve($posOrderId) {

  }

}
