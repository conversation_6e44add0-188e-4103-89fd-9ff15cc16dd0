<?php

namespace FrontModule;

use dibi;

use Nette;
use <PERSON>\Debugger;


final class CommentPresenter extends BasePresenter {

  /** @persistent */
  public $name = '';



  /** @persistent */
  public $fulltext = '';



  /** @persistent */
  public $cat = array();



  public function renderDefault($cat=[]) {

    $cmts = $this->model->getCommentsModel();
    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);

    $and = false;

    $query[] = "
    SELECT * 
    FROM comments 
    WHERE cmtreid=0 ";

    if (!empty($this->name)) {
      array_push($query, " AND proname LIKE %~like~", $this->name);
    }

    if (!empty($this->fulltext)) {
      array_push($query, " AND cmttext LIKE %~like~", $this->fulltext);
      $and = true;
    }

    if (count($cat) > 0) {
      array_push($query, " AND (cmtcatid IN (%i)", $cat, " OR cmtcatid2 IN (%i)", $cat, ")");
      $and = true;
    }

    //if ($and == false) array_push($query, " AND cmtid=-1");

    $query[] = " ORDER BY cmtid DESC";

    $ds = $cmts->getDataSource($query);
    $paginator = $this['paginator']->getPaginator();
    $paginator->itemsPerPage = 20;
    $paginator->itemCount = $ds->count();
    //$rows = $ds->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $query[] = " LIMIT $paginator->itemsPerPage OFFSET $paginator->offset";
    $rows = dibi::fetchAll($query);

    foreach ($rows as $key => $row) {
      $rows[$key]->res = dibi::fetchAll("
        SELECT comments.*, " . $pros->getSqlFields() . " 
        FROM comments 
        LEFT JOIN products ON (cmtproid=proid) 
        WHERE cmtreid=%i", $row->cmtid, " GROUP BY cmtid ORDER BY cmtid DESC");
    }

    $this->template->rows = $rows;

    $this->template->authors = dibi::query("SELECT * FROM admins WHERE coalesce(admfunction, '') != '' ORDER BY admorder, admname")->fetchAssoc("admid");

    $this->template->enu_cmtcatid = $cmts->getEnumCmtCatId();

    $page = $paginator->page;
    if ($page > 1 || count($cat) > 0) {
      $this->template->pageRobots = "noindex,follow";
    }
  }

  protected function createComponentCommentSearchForm() {

    $cmts = $this->model->getCommentsModel();

    $enu_cmtcatid = $cmts->getEnumCmtCatId();

    unset($enu_cmtcatid[0]);

    $form = $this->createAppForm();

    $form->addGroup();

    $form->addtext("proname", "Název zboží:", 40);

    if (!empty($this->name)) $form["proname"]->setDefaultValue($this->name);

    

    $form->addtext("fulltext", "Slovní spojení:", 40);

    if (!empty($this->fulltext)) $form["fulltext"]->setDefaultValue($this->fulltext);

    

    $form->addGroup("Témata");

    $container = $form->addContainer('comcat');

    foreach ($enu_cmtcatid as $id => $name) {

      $container->addCheckbox($id, $name);

      if (isset($this->cat[$id])) $container[$id]->setDefaultValue(TRUE);

    }

    $form->addSubmit('commentSearch', 'Vyhledat')->getControlPrototype()->class('button');

    $form->onSuccess[] = array($this, 'searchFormSubmitted');

    return $form;

  }



  public function searchFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {

      $vals = $form->getValues();

      $this->name = $vals["proname"];

      $this->fulltext = $vals["fulltext"];

      $this->cat = array();

      foreach ($vals['comcat'] as $key => $val) {

        if ($val) $this->cat[$key] = $key;

      }

      $this->redirect('default');

    }

  }  

}

