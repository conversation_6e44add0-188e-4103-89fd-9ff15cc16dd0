<?php

namespace FrontModule;

use dibi;
use Exception;
use MpApi<PERSON>lient\Article\DTO\ProductRequest;
use MpApiClient\Exception\MpApiException;
use MpApiClient\Filter\Filter;
use MpApiClient\Filter\FilterItem;
use MpApiClient\Filter\FilterOperatorEnum;

final class MallPresenter extends BasePresenter {

  /**
   * @var \MallApi
   */
  private $mallApi;

  public function startup() {
    parent::startup();

    $config = $this->config;
    $config["neonParameters"] = $this->neonParameters;
    $this->mallApi = new \MallApi($config, $this->model);
  }

  public function actionGetOrders() {

    $ret = $this->mallApi->getOrders();
    //přenesu do pohody nové obj
    if ($ret && is_array($ret)) {
      foreach ($ret as $ordId) {
        $this->exportPohodaOrders($ordId);
      }
    }
    //$this->mallApi->checkBlockedToOpenStatus();
    $this->mallApi->checkBlockedToCanceledStatus();
    $this->terminate();
  }

  public function actionUpdateProductsAvailability() {
    $quick = (int)$this->getParameter("quick");
    $this->mallApi->updateProductsAvailability($quick);
    $this->terminate();
  }

  public function actionUpdateProducts() {
    $limit = (int)$this->getParameter('limit');
    $isCron = (bool)$this->getParameter('cron');
    $this->mallApi->updateProducts($limit, $isCron);
    $this->terminate();
  }

  public function actionUpdateProduct($proCode) {
    $this->mallApi->updateProduct($proCode);
    $this->terminate();
  }

  public function actionCheckProducts() {
    $this->mallApi->checkProducts();
    $this->terminate();
  }

  public function actionCheckActiveProducts() {
    //$this->mallApi->checkActiveProducts();
    die("blokovváno");
    $this->terminate();
  }

  public function actionCheckActiveProduct($proCode) {
    $this->mallApi->checkActiveProduct($proCode);
    $this->terminate();
  }

  public function actionUpdateManufacturers() {
    $this->mallApi->updateManufacturers();
    $this->terminate();
  }

  public function actionUpdateDeliveries() {
    $this->mallApi->updateDeliveries();
    $this->terminate();
  }

  public function actionRepairVat() {
    $xml = new \SimpleXMLElement(file_get_contents(APP_DIR . '/../data/products-1705341602530.xml'));

    $products15 = [];
    $products15Ids = [];
    $productsDel = [];

    foreach ($xml->PRODUCT as $product) {
      //zkusim vycistit popis zbozi od zbytecneho HTML
      $code = (string)$product->ID;
      $vat = (int)$product->VAT;

      if ($vat === 15) {
        $pro = $this->model->getProductsModel()->load($code, "code");
        if (!$pro) {
          $productsDel[$code] = $code;
          continue;
        }

        if ($pro->promasid > 1) {
          $pro = $this->model->getProductsModel()->load($pro->promasid);
        }

        if ($pro->prostatus == 1) {
          $productsDel[$code] = $pro->procode;
        } else {
          $products15[$pro->proid] = $pro;
          $products15Ids[$pro->proid] = $pro->proid;
        }
      }
    }

    foreach ($productsDel as $key => $code) {
      //$this->mallApi->deleteProductByCode($code);
    }



    $sql = "SELECT proid, promasid, procode, procode2, proismaster, provariants, protypid, protypid2, protypid3, protypid4, protypid5, propromodateto, prokey, procode, proname AS proname1, concat(proname, ' ', proname2) AS proname, proname2, pronames, prodescs, prodesc, pronutrition, proaccess, proaccesstext, proqty, proprice1com AS propricecom, proisexp, promallblock, promalllabels, propicname, propicnamevar, IF(proprice1a>0,proprice1a,proprice1a) AS proprice, IF('a' = 'a' || 'a' = 'b', progifts, NULL) AS progifts, provatid, prodelfree, pronotdisc, prostatus, proorder, manmalid, manname, prorating, catid, catpathids, catmallparams
FROM products
    INNER JOIN catplaces ON (capproid=proid)
    INNER JOIN catalogs ON ((catpathids LIKE '|84|%' or catpathids LIKE '|57|%' or catpathids LIKE '|85|%') and capcatid=catid)
    LEFT JOIN manufacturers ON (promanid=manid)
WHERE
proid IN (5519,5494,6154,8140,8065,6166,4019,6246,7033,1836,2391,8022,5152,8228,3856,1837,3237,3234,4824,6733,6765,7018,7106,7128,7131,1288,2904,3472,3487,3547,3660,3670,5479,5248,6995,7958,6564,6605,6615,6962,1864,1862,2499,993,5131,7936,8395,8396,6900,6906,7774,8212,8284,4851,7814,3107,3117,3170,3362,3436,4179,2525,3412,6992,4959,5822,1905,5076,6647,7047,7109,7227,445,8013,1304,6274,3429,3833,3834,5794,1768,5,2839,6372)
GROUP BY proid";

    $rows = dibi::fetchAll($sql);

    $this->mallApi->updateProductsFromRows($rows);

    /*
    for ($i = 0; $i <= 10; $i++) {
      $filter = new Filter();
      $filter->setLimit(1000);
      $filter->setOffset($i * 1000);
      $filter->addFilterItem(FilterItem::create('vat', '15', FilterOperatorEnum::EQUAL()));

      $response = $this->mpApiClient->article()->listProducts($filter);
      $pages[$i] = $response;
      $paging = $response->getPaging();
      if ($paging->getSize() < 1000) {
        break;
      }
    }

    $items = [];
    $cnt = 0;
    foreach ($pages as $pageItems) {
      foreach ($pageItems as $item) {
        $cnt ++;
        echo($cnt . " - " . $item->getId()) . "<br>";
      }
    }
    */
    $this->terminate();
  }

  public function actionRepairVat2() {
    for ($i = 0; $i <= 10; $i++) {
      $filter = new Filter();
      $filter->addFilterItem(FilterItem::create('_vat', 15, FilterOperatorEnum::EQUAL()));
      $filter->setLimit(1000);
      $filter->setOffset($i * 1000);

      $response = $this->mallApi->getProductsByFilter($filter);
      $pages[$i] = $response;
      $paging = $response->getPaging();
      if ($paging->getSize() < 1000) {
        break;
      }
    }

    $cnt = 0;
    foreach ($pages as $pageItems) {
      foreach ($pageItems as $item) {
        $cnt ++;
        echo($cnt . " - " . $item->getId()) . "<br>";

        $pro = $this->model->getProductsModel()->load($item->getId(), "code");
        if (!$pro) {
          echo "položka " . $item->getId() . " nenalezena<br>";
        }
        $mallProduct = $this->mallApi->getProduct($item->getId());
        $productRequest = new ProductRequest(
          $mallProduct->getId(),
          $mallProduct->getTitle(),
          $mallProduct->getShortDesc(),
          $mallProduct->getLongDesc(),
          $mallProduct->getCategoryId(),
          12,
          $mallProduct->getPriority(),
        );

        $productRequest->setPrice($pro->proprice1a);

        $productRequest->setMedia($mallProduct->getMedia());
        $productRequest->setMedia($mallProduct->getMedia());
        $article = $this->mallApi->getArticle();

        try {
          $article->updateProduct($productRequest);
        } catch (MpApiException $e) {
          echo("přeskočeno " . $e->getMessage() . "<br>");
          $article->deleteProduct($mallProduct->getId());
        }



      }
    }

    $this->terminate();
  }

    public function actionSetMallParameters() {
    $pros = $this->model->getProductsModel();
    $prps = $this->model->getProParamsModel();
    $parType = $pros->getEnumMallParNames();

    $parFlavorValues = $pros->getEnumMallParFlavorValues();
    $parTypeValues = $pros->getEnumMallParTypeValues();

    $parTypeData = array(
      'aminokyseliny' => array(
        'catid' => 4,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'YES',
        'AFTER_ACTION' => 'YES',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),
      'anabolizéry' => array(
        'catid' => 5,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'YES',
        'AFTER_ACTION' => 'YES',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),

      'arginin' => array(
        'catid' => 54,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'NO',
        'AFTER_ACTION' => 'NO',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),
      'bcaa' => array(
        'catid' => 28,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'YES',
        'AFTER_ACTION' => 'YES',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),
      //'beta-alanine',
      'energetické gely' => array(
        'catid' => 77,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'NO',
        'AFTER_ACTION' => 'NO',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),
      //'expediční strava',
      'glutamin' => array(
        'catid' => 30,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'YES',
        'AFTER_ACTION' => 'YES',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),
      'iontové nápoje' => array(
        'catid' => 1,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'YES',
        'AFTER_ACTION' => 'NO',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),
      'isotonické nápoje' => array(
        'catid' => 28,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'YES',
        'AFTER_ACTION' => 'NO',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),
      //'expediční strava',
      'kloubní výživa' => array(
        'catid' => 9,
        'BEFORE_ACTION' => 'NO',
        'DURING_ACTION' => 'NO',
        'AFTER_ACTION' => 'NO',
        'FOOD_SUPPLEMENTS' => 'YES',

      ),
      'komplexní aminokyseliny' => array(
        'catid' => 27,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'YES',
        'AFTER_ACTION' => 'YES',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),
      'kreatin' => array(
        'catid' => 7,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'YES',
        'AFTER_ACTION' => 'YES',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),
      'proteinové tyčinky' => array(
        'catid' => 44,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'YES',
        'AFTER_ACTION' => 'YES',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),
      'proteiny' => array(
        'catid' => 3,
        'BEFORE_ACTION' => 'NO',
        'DURING_ACTION' => 'NO',
        'AFTER_ACTION' => 'NO',
        'FOOD_SUPPLEMENTS' => 'YES',
      ),
      'sacharidy' => array(
        'catid' => 2,
        'BEFORE_ACTION' => 'NO',
        'DURING_ACTION' => 'NO',
        'AFTER_ACTION' => 'NO',
        'FOOD_SUPPLEMENTS' => 'YES',
      ),
      'spalovače' => array(
        'catid' => 8,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'NO',
        'AFTER_ACTION' => 'NO',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),
      //'speciální balíčky',
      'stimulanty a energizéry' => array(
        'catid' => 48,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'YES',
        'AFTER_ACTION' => 'YES',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),
      //'taurine',
      'tyčinky' => array(
        'catid' => 10,
        'BEFORE_ACTION' => 'YES',
        'DURING_ACTION' => 'YES',
        'AFTER_ACTION' => 'YES',
        'FOOD_SUPPLEMENTS' => 'NO',
      ),
      'vitamíny a minerály' => array(
        'catid' => 2,
        'BEFORE_ACTION' => 'NO',
        'DURING_ACTION' => 'NO',
        'AFTER_ACTION' => 'NO',
        'FOOD_SUPPLEMENTS' => 'YES',
      ),
      //'volné aminokyseliny'
      'zdravá výživa' => array(
        'catid' => 57,
        'BEFORE_ACTION' => 'NO',
        'DURING_ACTION' => 'NO',
        'AFTER_ACTION' => 'NO',
        'FOOD_SUPPLEMENTS' => 'YES',
      ),
    );

    //sportovní a zdravá výživa
    $products = dibi::fetchAll("
      SELECT proid, promasid, procode, proname, proforid
      FROM products
      LEFT JOIN catplaces ON (capproid=proid)
      LEFT JOIN catalogs ON (capcatid=catid)
      WHERE prostatus=0 AND (catpathids LIKE '%|84|%' OR catpathids LIKE '%|57|%' OR promasid>0)
      GROUP BY proid
    ");
    $proNotFound = array();
    foreach ($products as $pro) {
      $parValue = array();

      if ($pro->promasid > 0) {
        $proMas = $pros->load($pro->promasid);
      } else {
        $proMas = $pro;
      }

      $parValue['BODYBUILDING'] = 'YES'; //vše co je v katagorii ID: 84
      $parValue['FOOD_SUPPLEMENTS'] = 'YES'; //vše

      //načtu žařazení ve všech kategoriích kromě zlatých dnů a výprodeje
      $catPathIds = dibi::fetchAll("
        SELECT catpathids 
        FROM catalogs
        INNER JOIN catplaces ON (capcatid=catid) 
        WHERE capproid=%i", $proMas->proid, " AND catpathids NOT LIKE '%|19|%' AND  catpathids NOT LIKE '%|53|%' AND catstatus=0
        ");
      //načtu běžné parametry
      $pars = dibi::query("SELECT prpname, prpvalue from proparams WHERE prptypid=0 AND prpproid=%i", $pro->proid)->fetchAssoc("prpname");

      $parValue['BEFORE_ACTION'] = "NO";
      $parValue['DURING_ACTION'] = "NO";
      $parValue['AFTER_ACTION'] = "NO";
      $parValue['FOOD_SUPPLEMENTS'] = "YES";

      foreach ($parType as $key => $name) {
        //projdu všechny hodnoty parameru TYPE_NA161 a podle nich zařadím do
        //BEFORE_ACTION, DURING_ACTION, AFTER_ACTION pokud nenajdu ani jeden zařadím jako FOOD_SUPPLEMENTS
        if ($key === 'TYPE_NA161') {
          foreach ($parTypeValues as $typId) {
            if (!isset( $parTypeData[$typId])) {
              continue;
            }
            $data = $parTypeData[$typId];
            //zjistim jestli je v teto kategorii
            foreach ($catPathIds as $row) {
              if (strpos($row->catpathids, '|'.$data['catid'].'|')) {

                $parValue['TYPE_NA161'] = $typId;

                $parValue['BEFORE_ACTION'] = $data['BEFORE_ACTION'];
                $parValue['DURING_ACTION'] = $data['DURING_ACTION'];
                $parValue['AFTER_ACTION'] = $data['AFTER_ACTION'];
                $parValue['FOOD_SUPPLEMENTS'] = $data['FOOD_SUPPLEMENTS'];

                if ($data['catid'] === 10) {
                  //tyčinka - nastavím konzistenci
                  $parValue['CONSISTENCE'] = 'tyčinka';
                }

                if ($data['catid'] === 77) {
                  //gel - nastavím konzistenci
                  $parValue['CONSISTENCE'] = 'gel';
                }
              }
            }
          }
        } else if ($key === 'CONSISTENCE') {
          //konzistence
          //podle proforid ale úplně to nesedí
          if ($proMas->proforid === 1) {
            //sypká
            $parValue[$key] = 'prášek';
          } else if ($proMas->proforid === 2) {
            //kapsle nebo tablety
            $parValue[$key] = 'tablety';
          } else if ($proMas->proforid === 3) {
            //tekutá
            $parValue[$key] = 'nápoj';
          }
        } else if ($key === 'FLAVOR') {
          //příchuť
          if (!empty($pars["Příchuť"]->prpvalue)) {
            //je nastavena příchuť
            $flavor = strtolower(trim($pars["Příchuť"]->prpvalue));
            if (isset($parFlavorValues[$flavor])) {
              //hodnota odpovídá MALLu
              $parValue[$key] = $flavor;
            } else {
              $k = $pars["Příchuť"]->prpvalue;
              if (!isset($proNotFound[$k])) {
                $proNotFound[$k] = 1;
              } else {
                ++$proNotFound[$k];
              }
              //echo $pro->procode . ": Hodnota paramatru Příchuť neodpovídá Mall: ".$pars["Příchuť"]->prpvalue ."<br>";
            }
          }
        } else if ($key === 'NUMBER_TABLETS_PORTION') {
          //počet tablet/porcí
          if (!empty($pars["Balení"])) {
            $val = $pars["Balení"]->prpvalue;
            $arr = explode(' ', $val);
            if (count($arr) > 2) {
              if (is_numeric(trim($arr[0])) && trim($arr[1]) === 'kapslí') {
                $parValue[$key] = trim($arr[0]);
              }
              if (is_numeric(trim($arr[0])) && trim($arr[1]) === 'tbl.') {
                $parValue[$key] = trim($arr[0]);
              }

              if (is_numeric(trim($arr[0])) && trim($arr[1]) === 'tablet') {
                $parValue[$key] = trim($arr[0]);
              }
            }
            $str = trim(str_replace('kapslí', '', $val));
            if (is_numeric($str)) {
              $parValue[$key] = $str;
            }
            $str = trim(str_replace('tablet', '', $val));
            if (is_numeric($str)) {
              $parValue[$key] = $str;
            }
            $str = trim(str_replace('tbl.', '', $val));
            if (is_numeric($str)) {
              $parValue[$key] = $str;
            }
          }
        } else if ($key === 'ENDURANCE_SPORTS') {
          //??
        }
      }

      //kontrola vyplnění parametrů
      if (empty($parValue['BEFORE_ACTION']) || empty($parValue['DURING_ACTION']) || empty($parValue['AFTER_ACTION']) || empty($parValue['FOOD_SUPPLEMENTS'])) {
        echo "ProId: " . $pro->proid . "|Název: "  . $pro->proname . " - chybí základní parametry BEFORE_ACTION, DURING_ACTION, AFTER_ACTION, AFTER_ACTION, FOOD_SUPPLEMENTS <br>";
      }
      if (empty($parValue['TYPE_NA161'])) {
      }
      if (count($parValue) === 4) {
        echo "ProId: " . $pro->proid . "|Název: "  . $pro->proname . " - vyplněny jen 4 základní parametry<br>";
      }

      //aktualizuji parametry
      //nactu MALL parametry
      $malls = dibi::query("SELECT prpid, prpname, prpvalue from proparams WHERE prptypid=1 AND prpproid=%i", $pro->proid)->fetchAssoc("prpname");
      foreach ($parValue as $name => $value) {
        $vals = array(
          'prpproid' => $pro->proid,
          'prpname' => $name,
          'prpvalue' => $value,
          'prptypid' => 1,
        );
        if (!empty($malls[$name])) {
          $prps->update($malls[$name]->prpid, $vals);
        } else {
          $prps->insert($vals);
        }
      }

    }

    echo "Nenalezené příchutě:<br>";
    foreach ($proNotFound as $key => $val) {
      echo $key . "<br>";
    }

    $this->terminate();
  }

  public function actionDeleteProduct($proCode) {
    $products = $this->model->getProductsModel();
    $pro = $products->load($proCode, "code");
    //vymažu z mall
    $config = $this->config;
    $config["neonParameters"] = $this->neonParameters;
    $mallApi = new \MallApi($config, $this->model);

    try {
      $mallApi->deleteProduct($pro);
    } catch (Exception $e) {
      echo $e->getMessage();
    }

    $this->terminate();
    //$this->redirect('Product:edit');
  }

}
