<?php
namespace FrontModule;
use App\Shippings\BalikovnaApi;
use App\Shippings\DpdApi;
use App\Shippings\UlozenkaApi;
use App\Shippings\WedoApi;
use dibi;
use Model\ZasilkovnapointsModel;
use Nette;
use Tracy\Debugger;

final class AlzaPresenter extends BasePresenter {

  public function actionProductsXml() {
    $catWhere = [];

    $importCategories = \AlzaApi::getImportCategories();

    foreach ($importCategories as $catId => $data) {
      $catWhere[] = "catpathids LIKE '%|$catId|%'";
    }

    $catWhereSql = " AND (" . implode(" OR ", $catWhere) . ") ";

    $sql = "
SELECT pro.*, promas.proid AS proidmas, promas.procode AS procodemas, promas.procodep AS procodepmas, promas.propicname AS propicnamemas, manname,
catid, catpath, catpathids, COALESCE(promas.procpcheureka, pro.procpcheureka) AS procpcheureka, COALESCE(promas.procpczbozi, pro.procpczbozi) AS procpczbozi, 
IF(pro.provatid=0, ".$this->config["VATTYPE_0"].", ".$this->config["VATTYPE_1"].") AS provat
FROM products AS pro
LEFT JOIN manufacturers ON (pro.promanid=manid)
LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
LEFT JOIN catplaces ON (capproid=coalesce(promas.proid,pro.proid))
LEFT JOIN catalogs ON (capcatid=catid)
WHERE coalesce(pro.procode2, '')!='' AND coalesce(pro.procodep, '')!='' AND pro.prostatus=0 AND pro.proprice".$this->curId."a>0 AND catstatus=0 AND
" . MAIN_CATIDS . $catWhereSql . " AND  
 pro.proismaster=0 AND 
pro.proaccess=0 AND (promas.proaccess IS NULL OR promas.proaccess = 0) AND 
(promas.prostatus IS NULL OR promas.prostatus = 0) 
GROUP BY pro.proid ORDER BY promasid";

    //doplnim katalogove cesty
    $this->template->alzaCatalogs = dibi::query("SELECT catid, catname, catpathalza AS catpathagregator FROM catalogs WHERE catpathalza is not null AND catpathalza!=''")->fetchAssoc("catid");

    $this->template->masterItems = dibi::query("SELECT proid, proname FROM products AS pros WHERE EXISTS (SELECT 1 FROM products AS pro WHERE pros.proid=pro.promasid)")->fetchAssoc("proid,proname");

    //doplním všechny parametry
    $this->template->proParameters = dibi::query("SELECT prpid, prpproid, prpname, prpvalue FROM proparams WHERE prptypid=0")->fetchAssoc("prpproid,prpid");

    $this->template->rows=dibi::fetchAll($sql);

  }
}