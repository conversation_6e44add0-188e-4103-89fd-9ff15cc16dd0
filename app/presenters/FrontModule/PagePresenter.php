<?php
namespace FrontModule;
use dibi;
use Nette;

final class PagePresenter extends BasePresenter {
  /********************* view default *********************/

  /** @persistent */
  public $ti;

  public function renderDetail($id, $key) {
    $pages = $this->model->getPagesModel();
    $articles = $this->model->getArticlesModel();
    $news = $this->model->getNewsModel();
    $pageData = $pages->load($id);
    if ($pageData) {
      if ($pageData->pagid > 0) {
        $this->template->urlkey = $pageData->pagurlkey;
        $this->template->page = $pageData;
      }
    } else {
      throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    }

    if ($pageData->pagblock == 1) throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');

    //pokud nejaky specialni typ doplnim potrebna data nastavim sablonu
    switch ($pageData->pagtypid) {
      case 2:
        $dataSource = $news->getDataSource("SELECT * FROM news ORDER BY newdate DESC");

        $paginator = $this['paginator']->getPaginator();
        $paginator->itemsPerPage = 20;
        $paginator->itemCount = $dataSource->count();
        $this->template->rows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
        
        $this->setView('news');
        break;
      case 3:

        $artTypId = (int)$this->getParameter("ti");

        $artRows = dibi::dataSource("SELECT * FROM articles WHERE artstatus=0 " . ($artTypId > 0 ? " AND arttypid=$artTypId" : "") . " GROUP BY artid");

        $paginator = $this['paginator']->getPaginator();
        $paginator->itemsPerPage = 20;
        $paginator->itemCount = $artRows->count();

        $artRows->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();

        $this->template->rows = $artRows->orderBy("artdate", FALSE);

        $this->template->authors = dibi::query("SELECT * FROM admins WHERE coalesce(admfunction, '') != '' ORDER BY admorder, admname")->fetchAssoc("admid");

        $this->template->enum_arttypid = $articles->getEnumArtTypId();

        $this->template->typId = $artTypId;

        $this->setView('articles');
        break;

      case 1:
        //kontaktni stránka
        $this->template->authors = dibi::fetchAll("SELECT * FROM admins WHERE coalesce(admfunction, '') != '' ORDER BY admorder, admname");
        $this->setView('contact');
        break;

      case 4:
        //prodejna
        $products = array();
        
        $product = $this->model->getProductsModel();
        $product->setPrcCat($this->userData->usrprccat);
        $product->setCurrency($this->currencies, $this->curId);
        $proList = array();
        $proList[] = $pageData->pagtext1;
        $proList[] = $pageData->pagtext2;
        $proList[] = $pageData->pagtext3;
        $proList[] = $pageData->pagtext4;
        $proList[] = $pageData->pagtext5;
        $proList[] = $pageData->pagtext6;
        $cnt = 0;
        foreach ($proList as $val) {
          $arr = explode("\n", $val);
          $proid = (int)trim($arr[0]);
          if (!empty($proid)) {
            $item = $product->load($proid);
            if ($item) {
              $item->storename = (string)trim($arr[1]);
              $item->storeprice = (double)trim($arr[2]);
              $item->storedesc = (string)trim($arr[3]);
              $products[] = $item;
              $cnt ++;
            }
          }
          if ($cnt >= 6) break;
        }
        
        $this->template->products = $products;
        $this->template->showMenuLeft = FALSE;
        $this->setView('store');
        break;
    }
    //nactu prilohy
    $this->template->attachments = dibi::fetchAll("SELECT * FROM attachments WHERE atapagid=%i AND atatype NOT IN ('jpg', 'png', 'gif')", $pageData->pagid);
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE atapagid=%i AND atatype IN ('jpg', 'png', 'gif')", $pageData->pagid);
  }
}
