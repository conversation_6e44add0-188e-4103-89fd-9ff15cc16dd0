<?php
namespace FrontModule;
use App\Shippings\BalikovnaApi;
use App\Shippings\DpdApi;
use App\Shippings\UlozenkaApi;
use App\Shippings\WedoApi;
use dibi;
use Nette;

final class ImportPresenter extends BasePresenter {

  public function actionFitnestoreImportImages() {
    $dir = APP_DIR . '/../data/fitnestore/pic/';
    $objects = scandir($dir);
    $sizes = (array($this->config["PROPICSIZE_BIG"].'xbig', $this->config["PROPICSIZE_DETAIL"].'xdetail', $this->config["PROPICSIZE_LIST"].'xlist'));

    foreach ($objects as $object) {
      if ($object != "." && $object != "..") {
        $img = Nette\Utils\Image::fromFile($dir . "/" . $object);
        $this->saveImage($img, WWW_DIR . "/pic/product", $object, $sizes);
        unlink($dir . "/" . $object);
      }
    }
    reset($objects);
    echo "hotovo";
    $this->terminate();
  }

  public function actionFitnestoreImport() {
    dibi::getConnection()->onEvent = NULL;
    $pros = $this->model->getProductsModel();
    $log = array();
    $fileName = 'https://www.fitnestore.cz/feed/13-52e9ec63e03150b11929c4fd3ce4f6b6.xml';

    $vatIdDph = array();

    for ($i = 0; $i <= 2; $i++) {
      $key = "VATTYPE_" . $i;
      if (isset($this->config[$key])) {
        $vatIdDph[$this->config[$key]] = $i;
      }
    }

    $xml = simplexml_load_string(file_get_contents($fileName));
    foreach ($xml->SHOPITEM as $item) {
      if (isset($vatIdDph[(int)$item->VAT])) {
        $vatId = $vatIdDph[(int)$item->VAT];
      } else {
        $log[] = "Položka " . $item->PRODUCTNO . " špatné vat id";
        continue;
      }

      $proCode = 'FS_' . $item->PRODUCTNO;

      $proName = (string)$item->PRODUCT;
      if (stripos($proName, 'tunturi') === false) {
        continue;
      }

      $data = array(
        "procode" => $proCode,
        "procode2" => (string)$item->EAN,
        "proname" => $proName,
        "promanid" => 362,
        "prodescs" => strip_tags((string)$item->SHORT_DESCRIPTION),
        "prodesc" => (string)$item->DESCRIPTION,
        "proprice1com" => (double)$item->PRICE_VAT,
        "provatid" => $vatIdDph[(int)$item->VAT],
        "propicname" => Nette\Utils\Strings::webalize($proCode, NULL, FALSE),
        "prostatus" => 1,
      );

      $proId = (int)dibi::fetchSingle("SELECT proid FROM products WHERE procode=%s",  $data["procode"]);

      if ($proId === 0) {
        //nová položka

        //stáhnu obrázky

        $targetPath = APP_DIR . "/../data/fitnestore/pic/" . $data["propicname"];
        $i = 0;
        foreach ($item->IMAGES->IMG_URL as $url) {
          $url = (string)$url;
          if (!empty($url)) {
            $sufix = ($i === 0 ? "" : "_" . $i);
            file_put_contents($targetPath . $sufix . ".jpg", fopen($url, 'rb'));
            $i++;
          }
        }

        $pros->insert($data);
        $log[] = "Nová položka " . $item->PRODUCTNO . "|" . $proName;

      } else {
        unset($data["procode"]);
        //update
        //$pros->update($proId, $data);
      }

    }
    foreach ($log as $key => $msg) {
      echo $msg . "<br>";
    }

    echo "Hotovo";
    $this->terminate();
  }

  public function actionPohodaZasobyFull() {
    dibi::getConnection()->onEvent = NULL;

    $pros = $this->model->getProductsModel();
    $pacs = $this->model->getPropackagesModel();
    $log = array();
    $filePath = APP_DIR . '/../data/pohoda/IN/';
    $fileName = $filePath . 'zasoby_all.xml';

    $cntupdOK = 0;
    $cntupdER = 0;
    $cnt = 0;
    $products = array();

    if (file_exists($fileName)) {
      $dataFile = $fileName;
      //$dataFile = $filePath . '/log/export-full-'. Nette\Utils\Strings::webalize(date('Y-d-m H:i:s')).'.xml';
      copy($fileName, $filePath . '/log/export-full-'. Nette\Utils\Strings::webalize(date('Y-d-m H:i:s')).'.xml');

      $xml = simplexml_load_string(file_get_contents($dataFile));
      $ns = $xml->getNamespaces(true);
      $data = $xml->children($ns['rsp']);
      $data = $data->children($ns['lStk']);
      foreach ($data->listStock->stock as $item) {
        $stock = $item->children($ns['stk']);
        $stockHeader = $stock->children($ns['stk']);

        $codep = trim((string)$stockHeader->code);
        $products[$codep]['proidp'] = (int)$stockHeader->id;

        //zjistim store
        $stores = array(
          'Kobylisy' => 'store',
          'Strašnice' => 'shop1', //Praha 1O
          'Vinohrady' => 'shop2', //Praha 2
        );

        //zjistim sklad
        $storage = $stockHeader->storage->children($ns['typ']);
        $stoId = (string)$storage->id;
        $stoName = (string)$storage->ids;
        $arr = explode('/', $stoName);
        $store = '';
        if (!empty($stores[$arr[0]])) {
          $store = $stores[$arr[0]];
          $products[$codep]['prostoreidp_'.$store] = $stoId;
        }

        $ean = trim((string)$stockHeader->EAN);
        $products[$codep]['procodep'] = $codep;

        if (!empty($ean)) {
          $products[$codep]['procode2'] = $ean;
        }

        //pokud se jedná o set načtu z čeho se skládá
        //potřebuji pohodový kód a počet kusů
        $stockType = (string)$stockHeader->stockType;
        $products[$codep]['set'] = array();
        $products[$codep]['prostocktypep'] = $stockType;

        if ($stockType === 'set') {
          $stockDetail = $stock->stockDetail; //->children($ns['stk']);
          foreach($stockDetail->stockItem as $setItem) {
            $products[$codep]['set'][] = array(
              'setcodep' => $codep,
              'pacqty' => (double)$setItem->quantity,
              'proidp' => (string)$setItem->id
            );
          }
        }

      }
      $proIds = array();
      $proSets = array();
      foreach ($products as $item) {
        $cnt++;
        //projdu polozky a zaktualizuji stav skladu a kod z pohody
        if (empty($item['procodep'])) {
          continue;
        }

        $proid = (int)dibi::fetchSingle('SELECT proid FROM products WHERE procodep=%s', $item['procodep']);

        if ($proid === 0) {
          //$log[] = 'Kód pohody: '.$item['procodep'].'nebyl nalezen v eshopu.';
          continue;
        }

        if (count($item['set']) > 0) {
          $proSets[$proid] = $item['set'];
        }

        unset($item['procodep']);
        unset($item['set']);

        if ($pros->update($proid, $item)) {
          $cntupdOK++;
        } else {
          $cntupdER++;
          $log[] = "Chyba proid=$proid.";
        }
      }

      //kontrola zda všechny položky sestavy jsou v eshopu
      foreach ($proSets as $proid => $sets) {
        foreach ((array)$sets as $key => $set) {
          $subproid = (int)dibi::fetchSingle('SELECT proid FROM products WHERE proidp=%i', $set["proidp"]);
          if ($subproid === 0) {
            $log[] = 'ID pohody: '.$set['proidp'].', který je součástí sestavy (kod pohody:'.$set['setcodep'].') nebyl nalezen v eshopu. Pro příslušnou sestavu nemohu správně aktualizovat stav skladu.';
            unset($proSets[$proid]);
            continue 2;
          }
          $proSets[$proid][$key]['pacsubproid'] = $subproid;
          $proSets[$proid][$key]['pacproid'] = $proid;
          unset($proSets[$proid][$key]['proidp']);
          unset($proSets[$proid][$key]['setcodep']);
        }
      }
      //aktualizace setů
      $updatedSets = array();
      foreach ($proSets as $proid => $sets) {
        foreach ((array)$sets as $key => $set) {
          $updatedSets[] = $pacs->updateSet($set);
        }
      }
      //vymažu sety, které nebyly aktualizované
      $cntDeleted = dibi::query("DELETE FROM propackages WHERE pacid NOT IN (%i)", $updatedSets);

      //aktualizuji stav skladu u sestav
      $rows = dibi::fetchAll("SELECT pacproid FROM propackages GROUP BY pacproid");
      foreach ($rows as $row) {
        $pros->updatePackageQty($row->pacproid);
      }

      //aktualizuji ceny a stav skladu pro master položky
      $rows = dibi::fetchAll("SELECT promasid FROM products WHERE promasid>0 GROUP BY promasid");
      foreach ($rows as $row) {
        $pros->updateMasterStore($row->promasid);
        $pros->recalcProductVariants($row->promasid);

      }

    } else {
      $log[] = "Nepodařilo se načíst soubor $fileName";
    }

    //$data = implode("\n", $log);
    //file_put_contents($dataFile . '.log', $data);
    @unlink($fileName);

    //promažu starší soubory
    $folders = array(
      $filePath,
      $filePath.'log/',
      APP_DIR . '/../data/pohoda/OUT/objednavky/log',
    );

    foreach ($folders as $folderName) {
      if (file_exists($folderName)) {
        foreach (new \DirectoryIterator($folderName) as $fileInfo) {
          if ($fileInfo->isDot()) {
            continue;
          }
          if ($fileInfo->isFile() && time() - $fileInfo->getCTime() >= 4*24*60*60) {
            unlink($fileInfo->getRealPath());
          }
        }
      }
    }

    $pros->cacheClean();
    $cats = $this->model->getCatalogsModel();
    $cats->cacheClean();

    $this->terminate();
  }

  public function actionPohodaZasoby() {
    dibi::getConnection()->onEvent = NULL;

    $pros = $this->model->getProductsModel();
    $log = array();
    $log[] = 'Import začal v '.date('Y-d-m H:i:s');
    $filePath = APP_DIR . '/../data/pohoda/IN/';
    $fileName = $filePath . 'Export.xml';

    if (file_exists($fileName)) {
      $dataFile = $filePath . 'log/export-'. Nette\Utils\Strings::webalize(date('Y-d-m H:i:s')).'.xml';
      copy($fileName, $dataFile);
      //pro výpočet EURa
      $digits = (int)$this->neonParameters['currency'][2]['decimals'];
      $rate = (double)$this->config['PRICE2RATE'];

      $products = array();

      $xml = simplexml_load_string(file_get_contents($fileName));
      foreach ($xml->Skz as $item) {
        $codep = trim((string)$item->IDS);

        $products[$codep]['procodep'] = $codep;
        $stores = array(
          'Kobylisy' => 'store',
          'Strašnice' => 'shop1', //Praha 1O
          'Vinohrady' => 'shop2', //Praha 2
        );
        //zjistim sklad
        $stoName = (string)$item->Sklad;
        $store = '';
        if (!empty($stores[$stoName]) && isset($item->StavZ)) {
          $store = $stores[$stoName];
          $obj = (int)$item->Objednavky;
          $rez = (int)$item->Rezervace;
          $qty = (int)$item->StavZ;
          $qty -= ($obj + $rez);
          $products[$codep]['proqty_' .$store] = $qty;
        } else {
          echo "";
        }

        if (empty($store) && isset($item->StavZ)) {
          $log[] = "$codep - přeskočeno špatný sklad";
          continue;
        }

        $products[$codep]['proprice1com'] = (double)$item->MOC;
        $products[$codep]['proprice1a'] = (double)$item->Eshop;
        $products[$codep]['proprice1b'] = (double)$item->VIP;
        $products[$codep]['proprice1c'] = (double)$item->VOCB;
        $products[$codep]['proprice1d'] = (double)$item->VOCA;
        $products[$codep]['proprice1e'] = (double)$item->VOCC;
        $products[$codep]['proprice1f'] = (double)$item->VOCD;
      }

      $cntupdOK = 0;
      $cntupdER = 0;
      $cnt = 0;

      foreach ($products as $procodep => $item) {
        $data = array();
        $cnt++;

        //projdu polozky a zaktualizuji stav skladu a kod z pohody
        $product = dibi::fetch('
          SELECT 
            pro.proid, pro.promasid, pro.promanid, pro.proqty_store, pro.proqty_shop1, pro.proqty_shop2, pro.proqty,
            pro.proprice1com, pro.proprice1a, pro.proprice1b, pro.proprice1c, pro.proprice1d, pro.proprice1e, pro.proprice1f, pro.proaccess,
            pro.proprice2com, pro.proprice2a, pro.proprice2b, pro.proprice2c, pro.proprice2d, pro.proprice2e, pro.proprice2f
          FROM products as pro
          WHERE pro.procodep=%s', $procodep);

        if ($product === FALSE) {
          continue;
        }

        //nactu vyrobce a vypocitam cenu v EUR
        $koef = ($product->promanid == 1 ? 0.95 : 1);

        if (isset($item['proprice1com'])) {
          $item["proprice2com"] = round($item['proprice1com'] / $rate * $koef, $digits);
        }
        if (isset($item['proprice1a'])) {
          $item["proprice2a"] = round($item['proprice1a'] / $rate * $koef, $digits);
        }
        if (isset($item['proprice1b'])) {
          $item["proprice2b"] = round($item['proprice1b'] / $rate * $koef, $digits);
        }
        if (isset($item['proprice1c'])) {
          $item["proprice2c"] = round($item['proprice1c'] / $rate * $koef, $digits);
        }
        if (isset($item['proprice1d'])) {
          $item["proprice2d"] = round($item['proprice1d'] / $rate * $koef, $digits);
        }
        if (isset($item['proprice1e'])) {
          $item["proprice2e"] = round($item['proprice1e'] / $rate * $koef, $digits);
        }
        if (isset($item['proprice1f'])) {
          $item["proprice2f"] = round($item['proprice1f'] / $rate * $koef, $digits);
        }

        //pokud není na nějakém skladu doplním 0
        if (!isset($item['proqty_store'])) {
          $item['proqty_store'] = 0;
        }
        if (!isset($item['proqty_shop1'])) {
          $item['proqty_shop1'] = 0;
        }
        if (!isset($item['proqty_shop2'])) {
          $item['proqty_shop2'] = 0;
        }

        $proqty = $item['proqty_store'] + $item['proqty_shop1'] + $item['proqty_shop2'];
        if ($product->proqty != $proqty) {
          $data['proqty'] = $proqty;
        }

        $proaccess = ($proqty > 0 ? 0 : 100);
        if ($product->proaccess != $proaccess) {
          $data['proaccess'] = $proaccess;
        }

        //aktualizuji jen pokud se data změnily
        $updateStore = FALSE;
        if ($item['proqty_store'] != $product->proqty_store) {
          $data['proqty_store'] = $item['proqty_store'];
          $updateStore = TRUE;
        }
        if ($item['proqty_shop1'] != $product->proqty_shop1) {
          $data['proqty_shop1'] = $item['proqty_shop1'];
          $updateStore = TRUE;
        }
        if ($item['proqty_shop2'] != $product->proqty_shop2) {
          $data['proqty_shop2'] = $item['proqty_shop2'];
          $updateStore = TRUE;
        }

        if ($updateStore) {
          //pro mall nastavím datum na NULL - vyšší priorita při aktualizaci
          $data['promalldateavu'] = NULL;
        }

        //aktualizuji jen pokud se data změnily
        $updatePrice = FALSE;
        if ((double)$item['proprice1com'] !== (double)$product->proprice1com) {
          $data['proprice1com'] = $item['proprice1com'];
          $updatePrice = true;
        }
        if ((double)$item['proprice1a'] !== (double)$product->proprice1a) {
          $data['proprice1a'] = $item['proprice1a'];
          $updatePrice = true;
        }
        if ((double)$item['proprice1b'] !== (double)$product->proprice1b) {
          $data['proprice1b'] = $item['proprice1b'];
          $updatePrice = true;
        }
        if ((double)$item['proprice1c'] !== (double)$product->proprice1c) {
          $data['proprice1c'] = $item['proprice1c'];
          $updatePrice = true;
        }
        if ((double)$item['proprice1d'] !== (double)$product->proprice1d) {
          $data['proprice1d'] = $item['proprice1d'];
          $updatePrice = true;
        }
        if ((double)$item['proprice1e'] !== (double)$product->proprice1e) {
          $data['proprice1e'] = $item['proprice1e'];
          $updatePrice = true;
        }
        if ((double)$item['proprice1f'] !== (double)$product->proprice1f) {
          $data['proprice1f'] = $item['proprice1f'];
          $updatePrice = true;
        }

        if ((double)$item['proprice2com'] !== (double)$product->proprice2com) {
          $data['proprice2com'] = $item['proprice2com'];
          $updatePrice = true;
        }
        if ((double)$item['proprice2a'] !== (double)$product->proprice2a) {
          $data['proprice2a'] = $item['proprice2a'];
          $updatePrice = true;
        }
        if ((double)$item['proprice2b'] !== (double)$product->proprice2b) {
          $data['proprice2b'] = $item['proprice2b'];
          $updatePrice = true;
        }
        if ((double)$item['proprice2c'] !== (double)$product->proprice2c) {
          $data['proprice2c'] = $item['proprice2c'];
          $updatePrice = true;
        }
        if ((double)$item['proprice2d'] !== (double)$product->proprice2d) {
          $data['proprice2d'] = $item['proprice2d'];
          $updatePrice = true;
        }
        if ((double)$item['proprice2e'] !== (double)$product->proprice2e) {
          $data['proprice2e'] = $item['proprice2e'];
          $updatePrice = true;
        }
        if ((double)$item['proprice2f'] !== (double)$product->proprice2f) {
          $data['proprice2f'] = $item['proprice2f'];
          $updatePrice = true;
        }

        if ($updatePrice) {
          //pro mall nastavím datum na NULL - vyšší priorita při aktualizaci
          $data['promalldateu'] = NULL;
        }

        //pokud cena A je 0 tak neaktualizovat
        if (isset($data['proprice1a'])) {
          if ((double)$data['proprice1a'] === 0.0) {
            unset($data['proprice1a']);
            $log[] = "$procodep : cena 1a je 0 - neaktualizuji";
            continue;
          }
        }
        if (isset($data['proprice2a'])) {
          if ((double)$data['proprice2a'] === 0.0) {
            unset($data['proprice2a']);
            $log[] = "$procodep : cena 2a je 0 - neaktualizuji";
            continue;
          }
        }

        if (count($data) > 0) {
          if ($pros->update($product->proid, $data)) {
            $cntupdOK++;
          } else {
            $cntupdER++;
            $log[] = "Chyba proid=$product->proid.";
          }
        }
      }

      //aktualizuji stav skladu u sestav
      $rows = dibi::fetchAll("SELECT pacproid FROM propackages GROUP BY pacproid");
      foreach ($rows as $row) {
        $pros->updatePackageQty($row->pacproid, FALSE);
      }

      //aktualizuji ceny a stav skladu pro master položky
      $rows = dibi::fetchAll("SELECT promasid FROM products WHERE promasid>0 GROUP BY promasid");
      foreach ($rows as $row) {
        $pros->updateMasterStore($row->promasid);
        $pros->recalcProductVariants($row->promasid);

      }

      $log[] = "Celkem $cnt. Aktualizované záznamy: $cntupdOK, $cntupdER chyb";
      $log[] = 'Import dokončen v '.date('Y-d-m H:i:s');
      //$data = implode("\n", $log);
      //file_put_contents($dataFile . '.log', $data);
      unlink($fileName);
    }

    $pros->cacheClean();
    $cats = $this->model->getCatalogsModel();
    $cats->cacheClean();

    $this->terminate();
  }

  /**
   * Aktualizace GeoIP databáze
   * 
   * Akce pro stažení a aktualizaci GeoLite2 Country databáze
   */
  public function actionUpdateGeolocationDatabase() {
    // Spustíme aktualizaci databáze (true pro vynucení aktualizace)
    $result = \geoip\GeoIpService::updateDatabase(true);
    
    if ($result) {
      echo('GeoIP databáze byla úspěšně aktualizována.');
    } else {
      echo('Při aktualizaci GeoIP databáze došlo k chybě. Zkontrolujte logy.');
    }
    
	$this->terminate();
  }

  public function actionUpdateBranches($id) {
    if ($id == "ulozenka") {
      $api = new UlozenkaApi($this->neonParameters["ulozenka"], $this->model);
      $api->updateBranches();
      $api = new UlozenkaApi($this->neonParameters["ulozenka_mall"], $this->model);
      $api->updateBranches();
    } else if ($id == "balikovna") {
      $api = new BalikovnaApi($this->model);
      $api->updateBranches();
    } else if ($id == "wedo") {
      $api = new WedoApi($this->neonParameters["wedo"], $this->model);
      $api->updateBranches();
    } else if ($id == "dpd") {
      $api = new DpdApi($this->neonParameters["dpd"], $this->model);
      $api->updateBranches();
    } else if ($id == "zasilkovna") {
      $api = new \ZasilkovnaApi($this->neonParameters["zasilkovna"], $this->model);
      $api->updateBranches();  
    }
    $this->terminate();
  }
}
