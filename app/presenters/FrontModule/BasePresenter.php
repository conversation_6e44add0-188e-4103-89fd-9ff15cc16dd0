<?php
namespace FrontModule;

use dibi;
use IPub\VisualPaginator\Components as VisualPaginator;
use Model;
use Nette;

abstract class BasePresenter extends \BasePresenter {
  const LOGIN_NAMESPACE = 'user';

  /** p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ý uživatel */
  protected $userData;

  /** košík */
  protected $basketNamespace;

  /** identita */
  protected $user;
  
    /**
    * @var \IPub\MobileDetect\MobileDetect
    */
    protected $mobileDetect;

  protected function startup() {

    parent::startup();

    $this->template->rr = $this->redirected;
    $this->template->countryCodeByIp = $this->appNamespace->countryCode;

    if (!isset($this->appNamespace->fullLayout)) $this->appNamespace->fullLayout = FALSE;
    if (!isset($this->appNamespace->votes)) $this->appNamespace->votes = array();

    //ulozim si do sablony zda je prihlaseny admin
    $admin = $this->getUser();
    $admin->getStorage()->setNamespace('admin');  
    $this->template->adminLogIn = $admin->isLoggedIn();
    
    //inicializace kosiku
    $this->basketNamespace = $this->getSession('basket');
    $this->basketNamespace->setExpiration('60 days', FALSE);
    if (!isset($this->basketNamespace->items)) $this->basketNamespace->items = array();
    if (!isset($this->basketNamespace->favorites)) $this->basketNamespace->favorites = array();
    if (!isset($this->basketNamespace->priceSum)) $this->basketNamespace->priceSum = 0;
    if (!isset($this->basketNamespace->priceSumVat)) $this->basketNamespace->priceSumVat = 0;
    if (!isset($this->basketNamespace->priceSumTotal)) $this->basketNamespace->priceSumTotal = 0;
    if (!isset($this->basketNamespace->delFree)) $this->basketNamespace->delFree = FALSE;
    if (!isset($this->basketNamespace->compare)) $this->basketNamespace->compare = array();
    if (!isset($this->basketNamespace->visited)) $this->basketNamespace->visited = array();
    if (!isset($this->basketNamespace->products)) $this->basketNamespace->products = array();
    
    //nactu uzivatele
    $this->user = $this->getUser();
    $this->user->getStorage()->setNamespace(self::LOGIN_NAMESPACE);
    $this->template->identity = $this->user;

    $this->userData = false;
    if ($this->user->isLoggedIn()) {
      $users = $this->model->getUsersModel();
      $this->userData = $users->load($this->user->id);
    }

    if ($this->userData == false) {
      $this->userData = new \Dibi\Row(array('usrid'=>0, 'usrprccat'=>'a', 'usrmail'=>''));
      if ($this->user->isLoggedIn()) $this->user->logout();
    }
    $this->template->userFavorites = $this->getUserFavorites($this->userData->usrid);
    $this->template->userRow = $this->userData;

    if ($this->mobileDetect->isPhone()) $this->config["CATALOG_ROWSCNT"] = $this->config["CATALOG_ROWSCNT_MOBI"];

    if ($this->appNamespace->countryRedirectStatus == 'r') {
      $this->recalc();
      $this->appNamespace->countryRedirectStatus = 's';
    }

  }

  public function handleBasketRefresh() {
    $this->recalc();
    $this->redrawControl("basketWindow");
  }
  
  protected function beforeRender() {

    if (empty($this->template->canonicalUrl)) {
      $actualUrl = (empty($_SERVER['HTTPS']) ? 'http' : 'https') . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
      $canonicalUrl = preg_replace ('/\?.+$/', '', $actualUrl);
      $this->template->canonicalUrl = $canonicalUrl;
    }

    $product = $this->model->getProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $product->setCurrency($this->currencies, $this->curId);

    $this->template->showMenuLeft = TRUE;
    parent::beforeRender();
    $this->template->basket = $this->basketNamespace;
    $this->template->basketPriceSum = $this->basketNamespace->priceSumVat;
    $this->template->basketItemsCnt = $this->basketNamespace->itemsCnt;
    $this->template->basketProducts = $this->basketNamespace->products;
    $this->template->isFullVersion = $this->appNamespace->fullLayout;

    //nactu menu katalogu - dve urovne
    $catalog = $this->model->getCatalogsModel();
    $menuCatalog = $catalog->cacheGet('menuCatalog');
    if ($menuCatalog == FALSE) {
      //cache neni musim naplnit
      $menuCatalog = dibi::query("SELECT catid, catmasid, catlevel, catname, catkey, catclass FROM catalogs WHERE catmasid=0 AND catstatus=0 ORDER BY catorder")->fetchAssoc('catid');

      foreach ($menuCatalog as $key => $row) {
        $si = dibi::fetchAll("SELECT catid, catmasid, catlevel, catname, catkey, catclass FROM catalogs WHERE catmasid=%i AND catstatus=0 ORDER BY catorder", $row->catid);
        /*
        foreach ($si as $skey => $srow) {
          $sii = dibi::fetchAll("SELECT catid, catmasid, catlevel, catname, catkey, catclass FROM catalogs WHERE catmasid=%i AND catstatus=0 ORDER BY catorder", $srow->catid);
          if ($sii) $si[$skey]->subItems = $sii;
        }*/
        if ($si) $menuCatalog[$key]->subItems = $si;
      }
      $catalog->cacheSave('menuCatalog', $menuCatalog);
    }
    $this->template->menuCatalog = $menuCatalog;

    //zlate dny
    $this->template->goldDaysItems = dibi::fetchAll($product->getSql() . " WHERE prostatus=0 AND promasid=0 AND protypid4=1 order by proorder, proname");
    /*
    $menu = $this->model->getMenusModel();
    $this->template->menuTop = $menu->getEnumMenuTree(2);
    $menuFooter1 = $menu->cacheGet('menuFooter1');
    if ($menuFooter1 == FALSE) {
      //cache neni musim naplnit
      $menuFooter1 = dibi::fetchAll("SELECT * FROM menus INNER JOIN pages ON (pagid=mensrcid AND mensrctype='page') WHERE menmasid=2 AND menstatus=0 ORDER BY menorder");
      $menu->cacheSave('menuFooter1', $menuFooter1);
    }
    $this->template->menuFooter1 = $menuFooter1;

    $menuFooter2 = $menu->cacheGet('menuFooter2');
    if ($menuFooter2 == FALSE) {
      //cache neni musim naplnit
      $menuFooter2 = dibi::fetchAll("SELECT * FROM menus INNER JOIN pages ON (pagid=mensrcid AND mensrctype='page') WHERE menmasid=3 AND menstatus=0 ORDER BY menorder");
      $menu->cacheSave('menuFooter2', $menuFooter2);
    }
    $this->template->menuFooter2 = $menuFooter2;
    */
    
    $menu = $this->model->getMenusModel();
    $menuShopInformation = $menu->cacheGet('menuShopInformation');
    if ($menuShopInformation == FALSE) {
      //cache neni musim naplnit
      $menuShopInformation = dibi::fetchAll("SELECT * FROM menus INNER JOIN pages ON (pagid=mensrcid AND mensrctype='page') WHERE menmasid=1 AND menstatus=0 ORDER BY menorder");
      $menu->cacheSave('menuShopInformation', $menuShopInformation);
    }
    $this->template->menuShopInformation = $menuShopInformation;
    
    //nactu textove bloky
    $page = $this->model->getPagesModel();
    $textBlocks = $page->cacheGet('textBlocks');
    if ($textBlocks == FALSE) {
      //cache neni musim naplnit
      $textBlocks = dibi::query('SELECT * FROM pages WHERE pagblock=1 AND pagstatus=0')->fetchAssoc('pagurlkey');
      $page->cacheSave('textBlocks', $textBlocks);
    }
    $this->template->textBlocks = $textBlocks;
    
    //nactu clanky do menu
    $arts = $this->model->getArticlesModel();
    $menuArticle = $arts->cacheGet('menuArticle');
    if ($menuArticle == FALSE) {
      //cache neni musim naplnit
      $menuArticle = dibi::query("SELECT * FROM articles WHERE artstatus=0 ORDER BY artname")->fetchAssoc('arttop,artid');
      $arts->cacheSave('menuArticle', $menuArticle);
    }
    $this->template->menuArticle = $menuArticle;
    
    //nactu posledni clanky do paticky - Zajimavosti
    $arts = $this->model->getArticlesModel();
    $footerArticles = $arts->cacheGet('footerArticle');
    if ($footerArticles == FALSE) {
      //cache neni musim naplnit
      $footerArticles = dibi::fetchAll("SELECT * FROM articles WHERE artstatus=0 ORDER BY artdate DESC LIMIT 3");
      $arts->cacheSave('footerArticle', $footerArticles);
    }
    $this->template->footerArticles = $footerArticles;

    //nactu instagram do patičky
    $footerInstagramImages = $arts->cacheGet('footerInstagramImages');
    if ($footerInstagramImages == FALSE) {
      //cache neni musim naplnit
      $footerInstagramImages = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=267 AND atatype IN ('jpg', 'webp') ORDER BY ataid DESC LIMIT 6");
      $arts->cacheSave('footerInstagramImages', $footerInstagramImages);
    }
    $this->template->footerInstagramImages = $footerInstagramImages;

    $this->template->enum_arttypid = $arts->getEnumArtTypId();

    //nactu minimalni cenu zbozi kdy by mel narok na dopravu zdarma
    $dels = $this->model->getDeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $this->template->delFreeLimit = $dels->getDelFreeLimit($this->userData->usrprccat);
    
    //nactu vyrobce
    $mans = $this->model->getManufacturersModel();
    $manufacturers = $mans->cacheGet('manufacturers');
    if ($manufacturers === FALSE) {
      //cache neni musim naplnit
      $manufacturers = dibi::fetchAll("SELECT * FROM manufacturers WHERE manstatus=0 ORDER BY manname");
      $mans->cacheSave('manufacturers', $manufacturers);
    }
    $this->template->manufacturers = $manufacturers;
    
    //TOP 10 - nejhledanější patička
    $this->template->fulltextSearchs = explode(',', $this->config['TOPSEARCHES']);
    
    //naposledy navstivene zbozi
    $this->template->lastVisited = array();
    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);
    
    $proVisited = array();
    //prehodim poradi prvku
    $arr = array_reverse($this->basketNamespace->visited);
    foreach ($arr as $proid) {
      $p = $pros->load($proid);
      if ($p) {
        $proVisited[] = $p;
      }
      if (count($proVisited) == 4)  break;
    }
    $this->template->proVisited = $proVisited;
    $cmts = $this->model->getCommentsModel();
    $this->template->enu_cmtcatid = $cmts->getEnumCmtCatId();
    
    //načtu 3 posledni komenrare
    $footerComments = $cmts->cacheGet('footerComments');
    if ($footerComments == FALSE) {
      //cache neni musim naplnit
      $footerComments = dibi::fetchAll("SELECT * FROM comments WHERE cmtreid=0 and cmtaproved=1 ORDER BY cmtid DESC LIMIT 3");
      $mans->cacheSave('footerComments', $footerComments);
    }
    $this->template->footerComments = $footerComments;
  
    //TOP 10 - nejsledovanější patička
    $footerTopProducts = $product->cacheGet('footerTopProducts');
    if ($footerTopProducts === FALSE) {
      $footerTopProducts = array();
      //z nastaveni nactu kody zbozi
      $proCodesList = explode(',', $this->config["FOOTER_FAVPRODUCTIDS"]);
      $cnt = 0;
      foreach ($proCodesList as $proid) {
        $proid = trim($proid);
        if (!empty($proid)) {
          $item = $product->load($proid);
          if ($item) {
            $footerTopProducts[] = $item;
            $cnt ++;
          }
        }
        if ($cnt == 10) break;
      }
      $product->cacheSave('footerTopProducts', $footerTopProducts);
    }
    $this->template->footerTopProducts = $footerTopProducts;

    $this->template->delTerms = $this->getDelTerms();

    //pošlu gtmPageType do šablony
    $gtmPageType = "other";
    if ($this->getName() === 'Front:Product') {
      $gtmPageType = "product";
    } else if ($this->getName() === 'Front:Homepage') {
      $gtmPageType = "homepage";
    } else if ($this->getName() === 'Front:Search') {
      $gtmPageType = "search";
    } else if ($this->getName() === 'Front:Basket') {
      $gtmPageType = "checkout";
    } else if ($this->getName() === 'Front:Order') {
      $gtmPageType = "purchase";
    } else if ($this->getName() === 'Front:Catalog') {
      $gtmPageType = "category";
    }
    $this->template->gtmPageType = $gtmPageType;

    $loginForm = $this->getComponent("userLoginForm");
    $this->template->loginFormHasErrors = $loginForm->hasErrors() && $this->getName() !== 'Front:User';
  }

  public function mainSearchFormSubmitted (Nette\Application\UI\Form $form) {

    $vals = $form->getValues();

    if (isset($vals["fulltext"])) {

      $searchParam["name"] = $vals["fulltext"];
      $this->redirect('Search:default', $searchParam);

    }
  }

  public function handleMainSearch($query) {
    $this->template->showSearchResults = FALSE;

    if (!empty($query)) {

      //zaloguju hledany text
      if (!empty($query)) {
        $ftx = $this->model->getFulltextlogsModel();
        $ftx->insert(array('ftxtext'=>$query));
      }

      //načtu výsledky vyhledávání
      $product = $this->model->getProductsModel();
      $product->setPrcCat($this->userData->usrprccat);
      $product->setCurrency($this->currencies, $this->curId);

      $where = array();
      $where[] = " prostatus=0 AND promasid=0 ";

      //rozsekám na slova
      $words = explode(" ", trim($query));
      $cnt = count($words);
      $i = 0;

      $where[] = " AND (";

      $fields = ["CONCAT(proname,' ',manname)", "pronamecz"];
      foreach ($fields as $field) {
        if ($i >= 1) {
          $where[] = " OR ";
        }
        $i = 0;
        //sestavím dotaz pro $field
        foreach ($words as $word) {
          $i++;
          $where[] = "$field LIKE %~like~";
          $where[] = $word;
          if ($i < $cnt) {
            $where[] = " AND ";
          }
        }
      }

      $where[] = ") ";

      $sql = $product->getSqlListArr($where, " proaccess ASC,  proprioritize DESC, protypid4 DESC, protypid DESC, protypid3 DESC, proorder ASC ");
      $this->template->productsSearchData = dibi::fetchAll($sql);

      $this->template->catalogsSearchData = dibi::fetchAll("SELECT catid,catkey, catname FROM catalogs WHERE catstatus=0 AND catname LIKE %~like~", $query);

      $this->template->manufacturersSearchData = dibi::fetchAll("SELECT manid, manname FROM manufacturers WHERE manstatus=0 AND manname LIKE %~like~", $query);

      $where = [];
      $i = 0;

      $where[] = "SELECT artid, arttypid, artname, arturlkey FROM articles WHERE artstatus=0";

      $where[] = " AND (";

      $fields = ["artname", "artkeywords"];
      foreach ($fields as $field) {
        if ($i >= 1) {
          $where[] = " OR ";
        }
        $i = 0;
        //sestavím dotaz pro $field
        foreach ($words as $word) {
          $i++;
          $where[] = "$field LIKE %~like~";
          $where[] = $word;
          if ($i < $cnt) {
            $where[] = " AND ";
          }
        }
      }

      $where[] = ") ORDER BY artdate";

      $this->template->articlesSearchData = dibi::fetchAll($where);

      if ($this->isAjax()) {
        $this->template->showSearchResults = TRUE;
        $this->redrawControl("mainSearchAutocomplete");
      }

    }
  }

  protected function createComponentSearchForm() {
    $form = $this->createAppForm();

    $form->addtext("fulltext", "", 10)
      ->addRule(Nette\Forms\Form::FILLED, "Vyplňte hledaný text");
    //$form->addImage('quickSearch');
    $form->addSubmit('quickSearch', 'Hledat')
      ->setAttribute("class", "ajax");

    /*
    $button = $form['quickSearch']->getControlPrototype();
    $button->setName('button');
    */

    $form->onSuccess[] = array($this, 'mainSearchFormSubmitted');
    return $form;
  }

  protected function createComponentUserLoginForm() {
    //prihlasovaci form
    $form = $this->createAppForm();

    $from = (string)$this->getParameter("from");
    $this->template->from = $from;

    $form->addHidden("from", $from);

    $form->addText('usrmail', 'Přihlašovací jméno (Váš email)')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Vaše přihlašovací jméno (email).');
    $form->addPassword('usrpassw', 'Heslo')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo.');
    $form->addSubmit('submit', 'Přihlásit')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'loginFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  public function loginFormSubmitted($form) {
    try {
      $this->user->setExpiration(0);
      $this->user->login($form['usrmail']->getValue(), $form['usrpassw']->getValue(), self::LOGIN_NAMESPACE);
      
      $this->user = $this->getUser();
    
      if ($this->user->isLoggedIn()) {
        $users = $this->model->getUsersModel();
        $this->userData = $users->load($this->user->id);
      }

      $this->justLoggedIn();

    } catch (Nette\Security\AuthenticationException $e) {
      //$this->flashMessage($e->getMessage(), 'err');
      $form->addError($e->getMessage());
      return false;
    }

    $from = $form['from']->getValue();
    if (!empty($from)) {
      if ($from === "basket") {
        $this->redirect('Basket:orderContact');
      }
    } else {
      $this->redirect('this');
    }
  }

  protected function justLoggedIn() {
    if ($this->userData->usrid > 0) {
        //naplním favorites přihlášeného
        if (is_array($this->basketNamespace->favorites) && count($this->basketNamespace->favorites) > 0) {
          foreach ($this->basketNamespace->favorites as $proId => $val) {
            $boks = $this->model->getBookmarksModel();
            $cnt = (int)dibi::fetchSingle("SELECT COUNT(bokid) FROM bookmarks WHERE bokproid=%i", $proId, " AND bokusrid=%i", $this->userData->usrid);
            if ($cnt === 0) {
              $boks->insert(array(
                'bokproid' => $proId,
                'bokusrid' => $this->userData->usrid,
              ));
            }
          }
          $this->basketNamespace->favorites = array();
        }
        $this->template->userFavorites = $this->getUserFavorites($this->userData->usrid);
      }

      //má něco v košíku?
      $basNotEmpty = (count($this->basketNamespace->items) > 0);

      //podívám se do historie košíku
      $rows = dibi::fetchAll("SELECT * FROM basketitems WHERE basusrid=%i", $this->userData->usrid);

      $bass = $this->model->getBasketItemsModel();

      if ($rows !== FALSE) {
        $cnt = 0;
        foreach ($rows as $key => $row) {
          $qty = $row->basqty;
          $ex = 0;
          if (!empty($this->basketNamespace->items[$row->basproid])) {
            $qty += (int)$this->basketNamespace->items[$row->basproid];
            $ex = (int)dibi::fetchSingle("SELECT COUNT(basid) FROM basketitems WHERE basproid=%i", $row->basproid, " AND basusrid=%i", $this->userData->usrid, " AND basqty=%i", (int)$this->basketNamespace->items[$row->basproid]);
          }
          if ($ex === 0) {
            $cnt++;
            $this->updateBasketItem($row->basproid, $qty);
          }
        }
        if ($cnt > 0) {
          $this->redirect("Basket:default", ["add"=>1]);
        }
      }

      $this->recalc();

  }

  public function actionFullSwitch() {
    $this->appNamespace->fullLayout =!$this->appNamespace->fullLayout;
    $this->redirect("Homepage:default");
  }

  public function actionProductVote(int $proId, $vote) {
    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);

    $pro = $pros->load($proId);
    if ($pro) {
      //zjistím jestli už nehlasoval
      if (!isset($this->appNamespace->votes[$proId])) {
        //ještě nehlasoval
        $pros->update($pro->proid, array('proratingcnt' => $pro->proratingcnt + 1));
        $this->appNamespace->votes[$proId] = $vote;
        $this->flashMessage("Děkujeme za váš hlas.");
      } else {
        $this->flashMessage("Již jste hlasoval/a pro tento produkt.");
      }
      $urlkey = (!empty($pro->prokey) ? $pro->prokey : Nette\Utils\Strings::webalize($pro->proname));
      $this->redirect('Product:detail', array('id'=>$pro->proid, 'key'=>$urlkey));
    } else {
      throw new Nette\Application\BadRequestException('Položka nenalezena', '404');
    }
    $this->terminate();
  }
  
  /**
  * prepocita obsah kosiku
  */
  public function recalc() {
    //kontrola zda vsechny polozky existuji - nejsou vyrazeny z nabidky
    $priceSum = 0;
    $priceSumVat = 0;
    $priceSumTotal = 0;
    $priceSumTotalVat = 0;
    $priceSumVatDisc = 0;
    $priceSumVatProducts = 0;
    $weightSum = 0;
    $products = array();

    $vatType = (string)$this->config["PRICEVAT"];

    $items = 0;
    $this->basketNamespace->isDiscount = false;
    $this->basketNamespace->specDel = false;
    $this->basketNamespace->delFree = false;
    if (count($this->basketNamespace->items) > 0) {
      $product = $this->model->getProductsModel();
      $product->setPrcCat($this->userData->usrprccat);
      $product->setCurrency($this->currencies, $this->curId);
      $setunset = false;
      foreach ($this->basketNamespace->items as $key => $value) {
        if (empty($key)) {
          continue;
        }
        $row = $product->load($key);
        $proMas = $row;
        if ($row->promasid > 0) {
          $proMas = $product->load($row->promasid);
        }
        if (!$row) {
          unset($this->basketNamespace->items[$key]);
          continue;
        }
        if ($row->proid == $key && $row->prostatus == 0) {
          //polozka nalezna v db a je aktivni
          $products[$key] = $row;
          //zkontroluju pocet skladem
          if ($this->config["CHECK_STOCK"] == 1) {
            if ((int)$value > (int)$row->proaccess) {
              if ((int)$row->proaccess == 0) {
                unset($this->basketNamespace->items[$key]);
                $setunset = true;
              } else {
                $value = (int)$row->proaccess;
                $this->basketNamespace->items[$key] = $value;
                $this->flashMessage(array("U položky ",$row->proname, "je skladem pouze", $row->proaccess, "ks"));
              }
            }
          } else if ((int)$row->proaccess > 0 || (int)$proMas->proaccess > 0) {
            unset($this->basketNamespace->items[$key]);
            $this->flashMessage("Položka " . $row->proname . " aktuálně není skladem a nelze ji objednat. Tato položka byla z košíku odstraněna.", "err");
          } else if ((int)$row->proqty > 0) {
            if ($row->proqty < (int)$value) {
              $this->basketNamespace->items[$key] = $row->proqty;
              $this->flashMessage("U položky " . $row->proname . " je skladem pouze ".$row->proqty." ks. Počet kusů ve vaší objednávce byl snížen.", "err");
            }  
          }

          if (!isset($this->basketNamespace->items[$key])) {
            continue;
          }

          // aktualizuji počet kusů pro jistotu kdyby bykl ponížený
          $value = $this->basketNamespace->items[$key];

          $weightSum += $row->proweight * $value;
          $items += $value;
          $itemPriceSumVat = 0;
          if ($vatType == 'inclvat') {
            $itemPriceSumVat = $row->proprice * $value;
            $vatLevel = (int)$this->config["VATTYPE_".$row->provatid];
            $priceSum += $row->proprice / (1+($vatLevel / 100)) * $value;
          } else {
            $priceSum += $row->proprice * $value;
            $itemPriceSumVat = $this->getPriceVat($row->proprice, $row->provatid) * $value;
          }
          $priceSumVat += $itemPriceSumVat;
          $priceSumVatProducts += $itemPriceSumVat;
          if ($row->pronotdisc == 0) $priceSumVatDisc += $itemPriceSumVat;

          if ($row->prooffer == 1 || $row->probigsize == 1) $this->basketNamespace->specDel =  true;
        } else {
          //polozka nenalezena nebo neni aktivni
          $setunset = true;
          unset($this->basketNamespace->items[$key]);
        }
        
        //pokud ma polozka dopravu zdarma ma ji cely kosik
        if ($row->prodelfree == 1 && ($this->userData->usrprccat == 'a' || $this->userData->usrprccat == 'b') && $this->curId == 1) $this->basketNamespace->delFree = TRUE;
        //pokud je v košíku položka na kterou se nevztahuje sleva
        if ($row->pronotdisc == 1) $this->basketNamespace->isDiscount = true;
      }

      //zjistim slevu - sleva se pocita jen pro cenovou kategorii A
      $discount = (double)0;

      //zjistim slevu nastavenou zakaznikovi
      if ($this->userData->usrid > 0) {
        $discount = (double)$this->userData->usrdiscount;
      }
      //zjistim mnozstevni slevu
      $disc = (double)dibi::fetchSingle("SELECT dispercent FROM discounts WHERE distypid='volume' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, " AND $priceSumVatDisc BETWEEN disfrom AND disto AND disstatus=0");
      if ($disc > $discount) {
        $discount = $disc;
      }

      $this->basketNamespace->discountPer = $discount;
      $this->basketNamespace->discountVal = round($priceSumVatDisc * ($discount / 100));

      //zjistím následující slevu
      unset($this->basketNamespace->discountNext);
      $nextDisc = dibi::fetch("SELECT dispercent, disfrom, disfrom - ".$priceSumVatDisc." AS diff FROM discounts WHERE distypid='volume' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, " AND disstatus=0 AND disfrom > ".$priceSumVatDisc. ' ORDER BY disfrom');
      if ($nextDisc) {
        $this->basketNamespace->discountNext = array(
          'percent' => $nextDisc->dispercent,
          'disFrom' => $nextDisc->disfrom,
          'valueRem' => (double)$nextDisc->diff,
        );
      }

      if ($vatType == 'inclvat' && $this->basketNamespace->discountVal > 0) {
        $priceSumTotal = $priceSum - ($this->basketNamespace->discountVal / (1+($vatLevel / 100)));
        $priceSumTotalVat = $priceSumVat - $this->basketNamespace->discountVal;
      } else {
        $priceSumTotal = $priceSum - $this->basketNamespace->discountVal;
        $priceSumTotalVat = $priceSumVat - $this->getPriceVat($this->basketNamespace->discountVal, 0);
      }

      if(!empty($this->basketNamespace->contact["orddelid"]) && $this->basketNamespace->specDel == False) {
        //pitva orddelid
        $orddelid = $this->basketNamespace->contact["orddelid"];
        if (!is_numeric($orddelid)) {
          $arr = explode('_', $orddelid);
          $orddelid = 0;
          if (!empty($arr[1])) $orddelid = (int)$arr[1];   
        }
        
        //zjistim cenu platby
        $pay = dibi::fetch("
          SELECT delmasid, IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$priceSumVat.", 0, delprice".$this->curId.$this->userData->usrprccat.") AS delprice
          FROM deliverymodes 
          LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
          WHERE delid=%i", $orddelid
        );
        if ($pay) {
          $delprice = $pay->delprice;
          
          //zjistim cenu dopravy
          $delivery = dibi::fetch("
            SELECT delmasid, 
            delprice".$this->curId.$this->userData->usrprccat." AS delpriceoriginal, 
            ".($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$priceSumVat.", 0, delprice".$this->curId.$this->userData->usrprccat.")")." AS delprice
            FROM deliverymodes 
            LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
            WHERE delid=%i", $pay->delmasid
          );
          if ($delivery) {
            $delprice = $delivery->delprice;
          }

        }
        
        $priceSumTotal += $delprice;
        $priceSumTotalVat += $delprice;
      }

      if ($setunset) {
        $this->flashMessage("Některé položky byly z košíku odstraněny, neboť byly vyřazeny z nabídky, nebo již nejsou skladem.");
      }
    }

    $discountVal = 0;
    if (!empty($this->basketNamespace->coupon)) {
      $cous = $this->model->getCouponsModel();
      $usrEmail = "";
      if ($this->userData->usrid > 0) {
        $usrEmail = $this->userData->usrmail;
      }

      $cou = $cous->validateCoupon($this->basketNamespace->coupon->coucode, $priceSumTotalVat - (int)$this->basketNamespace->discountVal, $usrEmail);

      if ($cou["status"] != 'ok') {
        unset($this->basketNamespace->coupon);
        $this->recalc();
        $this->flashMessage($cou["text"], 'err');
        //$this->redirect("default");
      } else {
        $coupon = $cou["data"];
        if ($coupon["couvalueunit"] == 'Kč') {
          $discountVal = round((double)$coupon["couvalue"], 0);
        }  else if ($coupon["couvalueunit"] == '%') {

          if ($this->userData->usrprccat != 'a' && $this->userData->usrprccat != 'b') {
            unset($this->basketNamespace->coupon);
            $this->recalc();
            $this->flashMessage("Slevový kupón " . $coupon["coucode"] . " nemůže uplatnit zákazník s přiděleným velkoobchodním ceníkem.", 'err');
            $this->redirect("this");
          }

          $discValue = $priceSumVatProducts;

          if ($cou["data"]->coumanid > 0) {
            //slevňuji jen položky od určité značky
            //suma objednane zbozi pro vypocet % slevy z kupónu
            $discValueSum = 0;
            foreach ($products as $key => $pro) {
              if ($pro->promanid == $cou["data"]->coumanid && $pro->pronotdisc != 1) {
                $qty = $this->basketNamespace->items[$key];
                $discValueSum += ($pro->proprice * $qty);
              }
            }
            $discValue = $discValueSum;
            $discountVal = round($discValue * ($coupon["couvalue"] / 100), 0);
          } else {
            $discountVal = round(($discValue - (int)$this->basketNamespace->discountVal) * ($coupon["couvalue"] / 100), 0);
          }

          IF ($discountVal > 0) {
            //$this->basketNamespace->discountPer = 0;
            //$this->basketNamespace->discountVal = 0;
          }
        }
        $this->basketNamespace->couDiscountVal = $discountVal;
      }
    }

    $priceSumTotal = MAX($priceSumTotal - $discountVal, 0);
    $priceSumTotalVat = MAX($priceSumTotalVat - $discountVal, 0);
    $priceSumVatDisc = MAX($priceSumVatDisc - $discountVal, 0);

    $this->basketNamespace->priceSum = $priceSum;
    $this->basketNamespace->priceSumVat = $priceSumVat;
    $this->basketNamespace->priceSumTotal = $priceSumTotal;
    $this->basketNamespace->priceSumTotalVat = $priceSumTotalVat;
    $this->basketNamespace->priceSumVatDisc = $priceSumVatDisc;
    $this->basketNamespace->weightSum = $weightSum;
    $this->basketNamespace->itemsCnt = $items;
    $this->basketNamespace->products = $products;
  }
  
  public function flashMessage($message, $type = "info") {
    if (is_array($message)) {
      $cnt = 1;
      $tMessage = "";
      foreach ($message as $key => $value) {
        if ($cnt % 2 == 0) {
          $tMessage .= ' '.$value.' ';
        } else {
          $tMessage .= $this->translator->translate($value);
        }
        $cnt++;
      }
    } else {
      $tMessage = $this->translator->translate($message);
    }
    return parent::flashMessage($tMessage, $type);
  }

  protected function createAppForm() {
    $form = new Nette\Application\UI\Form();
    $form->setTranslator($this->translator);
    return $form;
  }

  public function contactFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $formVals = $form->getValues();
      unset($formVals["antispam"]);
      $id = $this->getParam('id');
      $pages = $this->model->getPagesModel();
      $pageData = $pages->load($id, 'urlkey');
      $body = 'Nový dotaz, vzkaz:<br />
      '.($pageData ? 'Stránka: '.$pageData->pagname.' ('.$this->presenter->link('//:Front:Page:detail', $id).')<br />' :'').'
      Jméno, Přijmení: '.$formVals["conname"].'<br />
      Email: '.$formVals["conmail"].'<br />
      Mobil: '.$formVals["congsm"].'<br />
      Poznámka: <br />
      '.nl2br($formVals["connote"]);
      $mail = new Nette\Mail\Message();
      $mail->setFrom($this->config["SERVER_NAME"].' <'.$this->config["SERVER_MAIL"].'>');
      $mail->addReplyTo($formVals["conmail"]);
      $mail->addTo($this->config["SERVER_MAIL"]);
      $mail->setSubject('Nový dotaz, vzkaz');
      $mail->setHtmlBody($body);
      try {
        $mailer = new Nette\Mail\SendmailMailer;
        $mailer->send($mail);
        $this->flashMessage("Váš vzkaz byl přijat. Děkujeme!");
        $this->redirect('this');
      } catch (Nette\InvalidStateException $e) {
        $someerr = true;
        $form->addError("Vzkaz se nepodařilo odeslat.");
      }
    }
  }

  protected function createComponentContactForm() {
    $form = $this->createAppForm();

    $form->addText('conname', 'Jméno, příjmení', 30);

    $form->addText('conmail', "Platná emailová adresa (nutno vyplnit)", 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte platný email')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát.');

    $form->addText('congsm', 'Telefon (mobil) pro rychlejší kontakt', 20);

    $form->addTextArea('connote', "Poznámka, dotaz či přání", 50, 8);

    $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"],  10)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte antispamové číslo')
      ->setHtmlId('antispam')
      ->setRequired('Vyplňte prosím číslo ' . $this->config["ANTISPAM_NO"] . ', jde o test proti robotum')
      ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotům', $this->config["ANTISPAM_NO"]);

    //$form->setRenderer(new ScaffoldingRenderer);

    $form->addSubmit('save', 'Odeslat')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'contactFormSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }

  protected function createComponentCommentForm() {
    $proid = 0;
    $catid = 0;
    if ($this->getName() === 'Front:Product') {
      $proid = (int)$this->getParameter('id');
    } else if ($this->getName() === 'Front:Catalog') {
      $catid = (int)$this->getParameter('id');
    }
    $usrid = $this->userData->usrid;

    $form = $this->createAppForm();

    $form->addHidden("cmtproid", $proid);
    $form->addHidden("cmtcatcatid", $catid);
    $form->addHidden("cmtusrid", $usrid);
    $form->addHidden("cmtaproved", 0);

    $form->addText("cmtnick", "Jméno/přezdívka:", 40)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Vaše jméno/přezdívku.');

    $form->addCheckbox('cmtsendreply', 'Pošlete mi odpovědi na email');

    $form->addText('cmtmail', 'Váš email')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát.');

    if ($usrid > 0) {
      $form["cmtmail"]->setDefaultValue($this->userData->usrmail);
    }

    $form["cmtmail"]
      ->addConditionOn($form["cmtsendreply"], Nette\Forms\Form::EQUAL, TRUE)
        ->addRule(Nette\Forms\Form::FILLED, 'Pokud chcete zaslat odpovědi na email, vyplňte prosím email.');

    $form->addText("cmtsubj", "Titulek:", 40)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte titulek.');

    $form->addTextArea("cmttext", "Text:", 80, 10)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte text.');

    $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"])
      ->setHtmlId('antispam')
      ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"])
      ->setRequired('Vyplňte prosím číslo ' . $this->config["ANTISPAM_NO"] . ', jde o test proti robotum');

    $form->addCheckbox('gdpr', 'Ochrana osobních údajů')
      ->addRule(Nette\Forms\Form::EQUAL, "Je třeba souhlasit se zpracováním osobních údajů", TRUE)
      ->setRequired("Je třeba souhlasit se zpracováním osobních údajů");

    $form->addSubmit('submit', 'Vložit obecný dotaz')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'commentFormSubmitted');

    return $form;
  }

  public function commentFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $couCode = $this->appNamespace->countryCode;
      if ($couCode == 'cz' || $couCode == 'sk' || $couCode == '') {
      } else {
        $form->addError("Nekorektně odeslaný formulář.");
        return false;
      }
      //ulozim informace
      $vals = $form->getValues();
      unset($vals["antispam"]);
      unset($vals["gdpr"]);

      $template = $this->createTemplate();
      $template->setFile(WWW_DIR.'/../templates/Mails/mailProductCommentNew.latte');

      $pros = $this->model->getProductsModel();
      $pros->setPrcCat($this->userData->usrprccat);
      $pros->setCurrency($this->currencies, $this->curId);

      if (!empty($vals->cmtproid)) {
        $pro = $pros->load($vals->cmtproid);
        if ((int)$pro->promasid > 0) {
          $pro = $pros->load($pro->promasid);
          $vals->cmtproid = $pro->proid;
        }

        $template->product = $pro;
      }
      //mailuji adminovi
      $vals["cmtip"] = $_SERVER["REMOTE_ADDR"];
      $coms = $this->model->getCommentsModel();
      $comid = $coms->insert($vals);
      $com = $coms->load($comid);
      $template->comment = $com;
      $this->mailSend($this->config["SERVER_MAIL"], "Nový komentář", $template);
      //$this->mailSend("<EMAIL>", "Nový komentář", $template);

      $this->flashMessage("Děkujeme za Váš dotaz.");
      $this->redirect("this#dotazy");
    }
  }

  public function exportPohodaOrders($ordId=0, $onlyNotExported=TRUE, $forceNewContact=FALSE) {
    $ords = $this->model->getOrdersModel();
    $usrs = $this->model->getUsersModel();

    $prcCats = array();
    $prcCats['a'] = 'Eshop';
    $prcCats['b'] = 'VIP';
    $prcCats['c'] = 'VOCB';
    $prcCats['d'] = 'VOCA';
    $prcCats['e'] = 'VOCC';

    //přepravy eshop -> kody Pohoda
    //payid -> kod dopravy pohody
    $delCodes = array(
      4 => '0', //Přepravní společnost DPD,Dobírka
      5 => '0.001', //Přepravní společnost DPD,PayU online platba
      6 => '0.01', //Přepravní společnost DPD,Platba převodem
      2 => '0.1', //Česká pošta,Dobírka
      8 => '0.11', //Česká pošta,PayU online platba
      7 => '0.111', //Česká pošta,Platba převodem
      33 => '0.2', //Uloženka,Dobírka
      35 => '0.21', //Uloženka,PayU online platba
      34 => '0.22', //Uloženka,Platba převodem
      11 => '0.3', //Rozvoz po Praze naší dopravou,V hotovosti
      13 => '0.31', //Rozvoz po Praze naší dopravou,PayU online platba
      12 => '0.311', //Rozvoz po Praze naší dopravou,Platba převodem
      19 => '0.4', //EXPRES dodání - do 2 hodin po Praze!,V hotovosti
      20 => '0.41', //EXPRES dodání - do 2 hodin po Praze!,PayU online platba
      14 => '0.5', //Osobní odběr - prodejna Praha 10 Strašnice,V hotovosti na prodejně
      16 => '0.51', //Osobní odběr - prodejna Praha 10 Strašnice,PayU online platba
      15 => '0.511', //Osobní odběr - prodejna Praha 10 Strašnice,Platba převodem
      17 => '0.512', //Osobní odběr - prodejna Praha 10 Strašnice,Platební kartou na prodejně
      22 => '0.52', //Osobní odběr - prodejna Praha 2 Vinohrady,V hotovosti na prodejně
      25 => '0.53', //Osobní odběr - prodejna Praha 2 Vinohrady,PayU online platba
      24 => '0.54', //Osobní odběr - prodejna Praha 2 Vinohrady,Platba převodem
      23 => '0.55', //Osobní odběr - prodejna Praha 2 Vinohrady,Platební kartou na prodejně
      27 => '0.56', //Osobní odběr - centrální sklad Praha 8,V hotovosti na prodejně
      30 => '0.57', //Osobní odběr - centrální sklad Praha 8,PayU online platba
      29 => '0.58', //Osobní odběr - centrální sklad Praha 8,Platba převodem
      28 => '0.59', //Osobní odběr - centrální sklad Praha 8,Platební kartou na prodejně
      37 => '0.112', //ČP-Balík do balíkovny-dobírka
      39 => '0.113', //ČP-Balík do balíkovny-PayU
      38 => '0.114', //ČP-Balík do balíkovny-převodem

      51 => '0.115', //Zásilkovna-dobírka
      56 => '0.116', //Zásilkovna-PayU
      57 => '0.117', //Zásilkovna-převodem

      53 => '0.118', //Zásilkovna Na adresu-dobírka
      54 => '0.119', //Zásilkovna Na adresu-PayU
      55 => '0.1191', //Zásilkovna Na adresu-převodem

      44 => '0.6', //WE|DO,Dobírka
      43 => '0.61', //WE|DO,PayU online platba
      45 => '0.611', //WE|DO,Platba převodem
      48 => '0', //Přepravní společnost DPD,Dobírka
      47 => '0.001', //Přepravní společnost DPD,PayU online platba
      49 => '0.01', //Přepravní společnost DPD,Platba převodem

    );

    $delModeCodes = [
      'CESKA_POSTA' => 'ČP-balík do ruky',
      'CESKA_POSTA_NA_POSTU' => 'ČP-Na poštu',
      'CESKA_POSTA_BALIKOVNA' => 'ČP-Balíkovna',
      'DPD' => 'DPD',
      'DPD_PICKUP' => 'DPD',
      'OSOBNE' => 'Osobní odběr',
      'ULOZENKA' => 'Uloženka',
      'VLASTNI_PREPRAVA' => 'Goldfitness',
      'WEDO' => 'WE|DO',
      'ZASILKOVNA' => 'Zásilkovna-Výdejny',
      'ZASILKOVNA_NA_ADRESU' => 'Zásilkovna-na adresu',
    ];

    //payid -> kod dopravy pohody - původní

    $payCodes = array(
      4 => 'dobírka-DPD', //Přepravní společnost DPD,Dobírka
      5 => 'payU-DPD', //Přepravní společnost DPD,PayU online platba
      6 => 'převod-DPD', //Přepravní společnost DPD,Platba převodem
      2 => 'dobírka-Pošta', //Česká pošta,Dobírka
      8 => 'payU-Pošta', //Česká pošta,PayU online platba
      7 => 'převod-Pošta', //Česká pošta,Platba převodem
      33 => 'Ulož.-dobírkou', //Uloženka,Dobírka
      35 => 'Ulož.-PayU', //Uloženka,PayU online platba
      34 => 'Ulož.-převod', //Uloženka,Platba převodem
      11 => 'Hotově', //Rozvoz po Praze naší dopravou,V hotovosti
      13 => 'PayU', //Rozvoz po Praze naší dopravou,PayU online platba
      12 => 'Příkazem', //Rozvoz po Praze naší dopravou,Platba převodem
      19 => 'Hotově', //EXPRES dodání - do 2 hodin po Praze!,V hotovosti
      20 => 'PayU', //EXPRES dodání - do 2 hodin po Praze!,PayU online platba
      14 => 'Hotově', //Osobní odběr - prodejna Praha 10 Strašnice,V hotovosti na prodejně
      16 => 'PayU', //Osobní odběr - prodejna Praha 10 Strašnice,PayU online platba
      15 => 'Příkazem', //Osobní odběr - prodejna Praha 10 Strašnice,Platba převodem
      17 => 'Plat.kartou', //Osobní odběr - prodejna Praha 10 Strašnice,Platební kartou na prodejně
      22 => 'Hotově', //Osobní odběr - prodejna Praha 2 Vinohrady,V hotovosti na prodejně
      25 => 'PayU', //Osobní odběr - prodejna Praha 2 Vinohrady,PayU online platba
      24 => 'Příkazem', //Osobní odběr - prodejna Praha 2 Vinohrady,Platba převodem
      23 => 'Plat.kartou', //Osobní odběr - prodejna Praha 2 Vinohrady,Platební kartou na prodejně
      27 => 'Hotově', //Osobní odběr - centrální sklad Praha 8,V hotovosti na prodejně
      30 => 'PayU', //Osobní odběr - centrální sklad Praha 8,PayU online platba
      29 => 'Příkazem', //Osobní odběr - centrální sklad Praha 8,Platba převodem
      28 => 'Plat.kartou', //Osobní odběr - centrální sklad Praha 8,Platební kartou na prodejně
      37 => 'B-Pošta-dobírka', //ČP-Balík do balíkovny-dobírka
      39 => 'B-Pošta-PayU', //ČP-Balík do balíkovny-PayU
      38 => 'B-Pošta-převod', //ČP-Balík do balíkovny-převodem
      43 => 'Wedo-PayU',
      44 => 'Wedo-dobírka',
      45 => 'Wedo-převod',
      48 => 'dobírka-DPD', //Přepravní společnost DPD,Dobírka
      47 => 'payU-DPD', //Přepravní společnost DPD,PayU online platba
      49 => 'převod-DPD', //Přepravní společnost DPD,Platba převodem
    );

    //payCode -> kod dopravy pohody
    $payCodes = [
      'cash' =>      'Hotově',
      'paybefore' => 'Příkazem',
      'dobirka' =>   'Dobírka',
      'payonline' => 'PayU online',
      'creditcard' =>'Platební karta',
      'mallpay' =>   'MallPay',
    ];

    $stores = array(
      '10' => 'shop1', //Praha 1O
      '21' => 'shop2', //Praha 2
      '26' => 'store' //Praha 8
    );

    $couponCodes = array(
      200 => '99996',
      250 => '99997',
      500 => '99998',
      1000 => '99999',
      2000 => '999992'
    );

    if ($ordId > 0) {
      $rows = dibi::fetchAll('SELECT * FROM orders WHERE (ordtype!=3 OR ordtype is NULL) AND ' . ($onlyNotExported ? " ordexported=0 AND " : "") . ' ordid=%i', $ordId);
    } else {
      $rows = dibi::fetchAll('SELECT * FROM orders WHERE (ordtype!=3 OR ordtype is NULL) AND ordexported=0');
    }

    foreach ($rows as $row) {

      //pokud je objednávka reg. usera, aktualizuji fakturacni udaje do uziv. účtu a pokud se něco změní změním update status
      $usrPrcCat = 'a';
      if ($row->ordusrid > 0) {
        $usr = $usrs->load($row->ordusrid);
         $usrUpd = array();
        if ($usr) {
          $usrPrcCat = $usr->usrprccat;
          $usrUpd["usriname"] = $row->ordiname;
          $usrUpd["usrilname"] = $row->ordilname;
          $usrUpd["usrifirname"] = $row->ordifirname;
          $usrUpd["usristreet"] = $row->ordistreet;
          $usrUpd["usristreetno"] = $row->ordistreetno;
          $usrUpd["usricity"] = $row->ordicity;
          $usrUpd["usripostcode"] = $row->ordipostcode;
          $usrUpd["usrtel"] = $row->ordtel;
          //$usrUpd["usrmail"] = $row->ordmail;
          $usrUpd["usric"] = $row->ordic;
          $usrUpd["usrdic"] = $row->orddic;

          if ($usr->usrexportstatus > 0) {
            $usrUpd["usrexportstatus"] = 2;
          }

          $usrs->update($usr->usrid, $usrUpd);
          $usr = $usrs->load($row->ordusrid);
          //exportuji kontaktní infirmace
          if ($usr->usrexportstatus != 1) {
            $template = $this->createTemplate();
            $template->user = $usrs->load($usr->usrid);
            $template->forceNewContact = $forceNewContact;
            $template->prcCats = $prcCats;
            $template->setFile(WWW_DIR.'/../templates/FrontModule/Export.pohodaAdresy.latte');
            $fileName = APP_DIR . '/../data/pohoda/OUT/adresy/'.$usr->usrid.'.xml';
            $body = (string)$template;
            file_put_contents($fileName, $body);
            file_put_contents(APP_DIR . '/../data/pohoda/OUT/adresy/log/'.$usr->usrid.'.xml', $body);
            if (file_exists($fileName)) {
              $usrs->update($usr->usrid, array("usrexportstatus" => 1));
            }
          }
        }
      }

      $template = $this->createTemplate();
      $template->order = $ords->load($row->ordid);
      $delModes = $this->model->getDeliveryModesModel();
      $delModes->setCurrency($this->currencies, $template->order->ordcurid);
      $payMode = $delModes->load($row->orddelid);
      $template->payMode = $payMode;
      $delMode = $delModes->load($payMode->delmasid);
      $template->delMode = $delMode;

      //pokud ULOZENKA naštu odběrné místo
      if ($template->delMode->delcode === 'ULOZENKA') {
        $template->delModeSpec = dibi::fetch("SELECT * FROM ulozenkapoints WHERE uloshortcut=%s", $row->orddelspec);
      }

      //sklad
      $store = 'store';
      if (isset($stores[$template->delMode->delid])) {
        $store = $stores[$template->delMode->delid];
      }
      $template->store = $store;

      $delIds = "";
      if (isset($delCodes[$template->order->orddelid])) {
        $delIds = $delCodes[$template->order->orddelid];
      }

      $payIds = "";
      if (isset($payCodes[$payMode->delcode])) {
        $payIds = $payCodes[$payMode->delcode];
      }

      $carrierId = "";
      if (isset($delModeCodes[$delMode->delcode])) {
        $carrierId = $delModeCodes[$delMode->delcode];
      }

      $payType = "";
      if ($payMode->delcode == 'dobirka') {
        $payType = 'delivery';
      } else if ($payMode->delcode == 'payonline') {
        $payType = 'draft';
      } else if ($payMode->delcode == 'paybefore') {
        $payType = 'draft';
      } else if ($payMode->delcode == 'cash') {
        $payType = 'cash';
      } else if ($payMode->delcode == 'creditcard') {
        $payType = 'creditcard';
      }

      $template->payIds = $payIds;
      $template->delIds = $delIds;
      $template->carrierId = $carrierId;
      $template->couponCodes = $couponCodes;
      $template->payType = $payType;

      //cenova kategorie
      $prcCat = array(
        'a' => 'Prodejní',
        'b' => 'V.I.P.',
        'c' => 'VOC - B',
        'd' => 'VOC - A',
        'e' => 'VOC - C',
      );

      $ordPrccat = $usrPrcCat;
      if (!empty($row->ordprccat)) {
        $ordPrccat = (string)$row->ordprccat;
      }
      $template->priceLevel = $prcCat[$ordPrccat];
      $template->ordItems = dibi::fetchAll("
      SELECT * 
      FROM orditems 
      LEFT JOIN products ON (oriproid=proid)
      WHERE oritypid != 3 AND oriordid=%i", $row->ordid, " ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END");
      $template->setFile(WWW_DIR.'/../templates/FrontModule/Export.pohodaOrders.latte');

      $xml = (string)$template;

      file_put_contents(APP_DIR . '/../data/pohoda/OUT/objednavky/'.$row->ordcode.'.xml', $xml);
      file_put_contents(APP_DIR . '/../data/pohoda/OUT/objednavky/log/'.$row->ordcode.'.xml', $xml);
      $ords->update($row->ordid, array('ordexported' => 1));
    }

    return TRUE;
  }

  protected function updateBasketItem($proId, $proQty) {
    $bass = $this->model->getBasketItemsModel();
    $usrId = $this->userData->usrid;
    if ($proQty === 0) {
      //vymažu položku
      unset($this->basketNamespace->items[$proId]);
      $bass->deleteProduct($usrId, $proId);
    } else {
      $this->basketNamespace->items[$proId] = $proQty;
      $bass->updateProduct($usrId, $proId, $proQty, session_id());
    }
  }

  public function sendVerification($genNewCode=TRUE) {
    $usrs = $this->model->getUsersModel();
    if (!$this->userData->usrid === 0) {
      $this->flashMessage('Nejdříve se prosím přihlašte.');
      $this->redirect('login');
    }
    if ($genNewCode) {
      $this->userData = $usrs->load($this->userData->usrid);
      if (empty($this->userData->usrmailvcode)) {
        $usrs->update($this->userData->usrid, array(
          'usrmailvcode' => $this->getVerifyCode(),
          'usrmailverified' => 0
        ));
        $this->userData = $usrs->load($this->userData->usrid);
      }
    }
    $mailTemplate = $this->createTemplate();
    $mailTemplate->user = $this->userData;
    $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/userVerify.latte');

    $this->mailSend($this->userData->usrmail, 'Ověření emailu', $mailTemplate);
    //zaloguji odeslání žádosti
    $usrs->logEvent($this->userData->usrid, Model\UsersModel::EVENT_MAIL_VERIFICATION_SEND);
    //$this->flashMessage('Ověřovací email byl úspěšně odeslán.');
  }

  public function logGdprEventsNewUser($usrId, $mailList = FALSE) {
    //zaloguji, regiistraci
    $usrs = $this->model->getUsersModel();
    $usrs->logEvent($usrId, \Model\UsersModel::EVENT_REGISTER);
    //zaloguji souhlas s ucováním údajů
    $usrs->logEvent($usrId, \Model\UsersModel::EVENT_GDPR);
    if ($mailList) {
      //zaloguji přihlášení do maillingu
      $usrs->logEvent($usrId, \Model\UsersModel::EVENT_MAILLIST_ADD);
    }
  }

  /*
  function createComponentPaginator($name){
    $vp = new \VisualPaginator($this, $name);
  }
  */

  /**
	 * Create items paginator
	 *
	 * @return VisualPaginator\Control
	 */
	protected function createComponentPaginator() {
		// Init visual paginator
		$control = new VisualPaginator\Control;
		$control->setTemplateFile(APP_DIR . '/../templates/@paginator.latte');
		$control->disableAjax();
		return $control;
	}

  /**
   * prida polozku do kosiku
   *
   * @param integer $proid id polozky
   * @param integer|null $count pocet kusu
   * @param string $show
   * @throws Nette\Application\AbortException
   */
  public function handleBasketAdd($proid, $count = NULL, $show = "true") {

    $show = (strtolower($show) === 'true');

    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);

    if ($count === NULL) {
      $count = 1;
    }

    //zjistím jestli nevkládá master položku
    $pro = $pros->load($proid);
	if (!$pro) {
	  $this->flashMessage("Položka nenalezena.", "err");
      $this->redirect('Basket:default', array('r'=>1));
    }

    if ((int)$pro->proismaster === 1) {

      if ($this->isAjax()) {
        //načtu varianty
        $this->template->modalProductMaster = $pro;
        $this->template->modalVariants = $pros->getVariants($pro->proid);
        $this->redrawControl("modalVariants");

      } else {
        //přesměruji na detail produktu
        $urlkey = (!empty($pro->prokey) ? $pro->prokey : Nette\Utils\Strings::webalize($pro->proname));
        $this->redirect('Product:detail', array('id'=>$pro->proid, 'key'=>$urlkey));
      }
    } else {


      //přidám do košíku
      if (!isset($this->basketNamespace->items[$proid])) {
        $this->basketNamespace->items[$proid] = 0;
      }

      $this->updateBasketItem($proid, (int)$count);

      if ($this->isAjax()) {

        $this->recalc();

        if (isset($this->basketNamespace->items[$proid]) && $this->basketNamespace->items[$proid] < $count) {
          $this->template->stockLimitReached = array($proid => $this->basketNamespace->items[$proid]);
        }

        if ($show) {
          $this->template->modalBasketAddProduct = $this->basketNamespace->products[$proid];
          $this->template->modalBasketAddQty = $this->basketNamespace->items[$proid];

          $this->redrawControl("modalBasketAdd");
          $this->redrawControl("modalVariants");
        }

        if ($this->getName() === "Front:Basket") {
          //$this->redrawControl("basketWindowOrder");

          if ($this->action === "default") {
            $this->redrawControl("basketContent");
          }

        } else {
          $this->redrawControl("basketWindow");
        }

      } else {

        $this->redirect('Basket:default', array('r'=>1));

      }
    }

  }

  /**
   * odstraní položku z košíku
   *
   * @param integer $proid id polozky
   * @throws Nette\Application\AbortException
   */
  public function handleBasketRem($proid) {

    //odeberu z košíku
    $this->updateBasketItem($proid, 0);

    if ($this->isAjax()) {
      $this->recalc();
      $this->redrawControl("basketWindow");
      $this->redrawControl("modalBasketAdd");
      $this->redrawControl("modalVariants");
      $this->redrawControl("basketContent");
      //$this->redrawControl("basketWindowOrder");
    } else {
      $this->redirect('Basket:default', array('r'=>1));
    }

  }

  public function handleCommentAdd($proid=0) {
    if ($this->isAjax()) {
      $this->template->showModal = TRUE;
      $this->redrawControl("modalCommentAdd");
    } else {
      $this->redirect("Comment:default");
    }
  }

  public function handleFavAdd($proid) {
    if ($this->userData->usrid > 0) {
      $boks = $this->model->getBookmarksModel();
      $cnt = (int)dibi::fetchSingle("SELECT COUNT(bokid) FROM bookmarks WHERE bokproid=%i", $proid, " AND bokusrid=%i", $this->userData->usrid);
      if ($cnt === 0) {
        $boks->insert(array(
          'bokproid' => $proid,
          'bokusrid' => $this->userData->usrid,
        ));
      }
    } else {
      $this->basketNamespace->favorites[$proid] = $proid;
    }

    if ($this->isAjax()) {
      $this->template->userFavorites = $this->getUserFavorites($this->userData->usrid);
      $this->template->redrawFired = TRUE;
      $this->redrawControl("productsList");
      $this->redrawControl("paginator");
      $this->redrawControl("favorites");
      $this->redrawControl("homepageProducts");
    } else {
      $this->redirect("Catalog:favorites");
    }
  }

  public function handleFavRem($proid) {
    if ($this->userData->usrid > 0) {
      $boks = $this->model->getBookmarksModel();
      dibi::query("DELETE FROM bookmarks WHERE bokusrid=%i", $this->userData->usrid, " AND bokproid=%i", $proid);
    } else {
      unset($this->basketNamespace->favorites[$proid]);
    }

    if ($this->isAjax()) {
      $this->template->userFavorites = $this->getUserFavorites($this->userData->usrid);
      $this->template->redrawFired = TRUE;
      $this->redrawControl("productsList");
      $this->redrawControl("paginator");
      $this->redrawControl("favorites");
      $this->redrawControl("homepageProducts");
    } else {
      $this->redirect("Catalog:favorites");
    }
  }

  protected function getUserFavorites($usrId) {
    if ($usrId > 0) {
      return dibi::fetchPairs("SELECT bokproid, bokid FROM  bookmarks WHERE bokusrid=%i", $usrId);
    } else {
      return $this->basketNamespace->favorites;
    }
  }
}