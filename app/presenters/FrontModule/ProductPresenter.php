<?php
namespace FrontModule;
use dibi;
use Nette;
use <PERSON>\Debugger;

final class ProductPresenter extends BasePresenter {

  public function renderDetailOld($id) {
    $products = $this->model->getProductsModel();
    $products->setPrcCat($this->userData->usrprccat);
    $products->setCurrency($this->currencies, $this->curId);
    //aktualni polozka katalogu
    $product = $products->load($id);


    if ($product === false) {
      throw new Nette\Application\BadRequestException('Položka nenalezena', '404');
    } else{
      //kontrola platnosti URL
      $urlkey = (!empty($product->prokey) ? $product->prokey : Nette\Utils\Strings::webalize($product->proname));
      $this->redirect(301, 'Product:detail', array('id'=>$id, 'key'=>$urlkey));
    }
  }      
  
  public function renderDetail($id, $key) {
    $products = $this->model->getProductsModel();
    $products->setPrcCat($this->userData->usrprccat);
    $products->setCurrency($this->currencies, $this->curId);
    //aktualni polozka katalogu
    $product = $products->load($id);


    if ($product === false) {
      throw new Nette\Application\BadRequestException('Položka nenalezena', '404');
    }

    //kontrola platnosti URL
    $urlkey = (!empty($product->prokey) ? $product->prokey : Nette\Utils\Strings::webalize($product->proname));

    //pokud se zmenil klic presmeruju na novy
    if ($key != $urlkey) {
      $this->redirect(301, 'Product:detail', array('id' => $id, 'key' => $urlkey));
    }

    if ($product->promasid > 0) {
      $this->basketNamespace->visited[$product->promasid] = $product->promasid;
    } else {
      $this->basketNamespace->visited[$product->proid] = $product->proid;
    }

    $productMasterData = $product;
    if ($product->promasid > 0) {
      $productMas = $products->load($product->promasid);
      if ($productMas) {
        $productMasterData = $productMas;
        //pokud je to podrizena polozka a master položka není skladem nebo není dostupná, skladovou dostupnost a status prevezmu z master polozky
        if ($productMasterData->prostatus > 0 ||  $productMasterData->proaccess > 0) {
          $product->proaccess = $productMasterData->proaccess;
          $product->prostatus = $productMasterData->prostatus;
        }
      } else {
        unset($productMas);
      }
    }

    $this->template->isOnStock = ((int)$product->proaccess === 0 && (int)$product->prostatus === 0);
    $this->template->isBlocked = ((int)$product->prostatus === 1);

    if ($this->template->isBlocked && isset($productMas) && $productMas) {
      $this->redirect('Product:detail', array('id' => $productMas->proid, 'key' => Nette\Utils\Strings::webalize($productMas->proname)));
    }

    $this->template->product = $product;
    
    $this->template->productMasterData = $productMasterData;
    $catalog = $this->model->getCatalogsModel();

    //pokud mám uloženou poslední navštívenou kategorii, podívám se jestli tento produkt do ní nespadá
    $curCatId = 0;

    if (!empty($this->appNamespace->masterCatId)) {
      $curCatId = dibi::fetchSingle("
        SELECT capcatid 
          FROM catplaces
          INNER JOIN catalogs ON (catid=capcatid)
        WHERE 
            catstatus=0 AND capproid=%i", $productMasterData->proid," AND catpathids LIKE '|%" . $this->appNamespace->masterCatId .  "|%'"
        );
    }

    if (empty($curCatId)) {
      $curCatId = dibi::fetchSingle("
        SELECT capcatid 
          FROM catplaces 
          INNER JOIN catalogs ON (catid=capcatid)
        WHERE catstatus=0 AND " . MAIN_CATIDS . " AND capproid=%i", $productMasterData->proid);
    }

    $catalogData = $catalog->load($curCatId);

    $this->template->catalog = $catalogData;
    $rootCatId = 0;
    if ($catalogData) {
      $idPath = explode('|', trim($catalogData->catpathids, '|'));
      if (isset($idPath[1])) $rootCatId = (int)$idPath[1];
      $menuCatalogL = array();
      $catalogPath = array();
      foreach ($idPath as $catid) {
        $menuCatalogL[$catid] = dibi::fetchAll("SELECT * from catalogs WHERE catmasid=$catid ORDER BY catorder");
        $catalogPath[$catid] = dibi::fetch("SELECT * from catalogs WHERE catid=$catid");
        if ($catalogData->catid == $catid) {
          break;
        }
      }
      $this->template->rootCatId = $rootCatId;
      $this->template->menuCatalogL = $menuCatalogL;
      $this->template->catalogPath = $catalogPath;
      $this->template->thisCatId = $catalogData->catid;

      //doplním nejprodávanější podle kategorie
      $this->template->saleStatsProducts = $products->getSaleStatsProducts($catalogData);

      //podrizene kategorie
      $catalogSubItems = dibi::fetchAll("SELECT * FROM catalogs WHERE catmasid=%i", $catalogData->catid, " AND catstatus=0 ORDER BY catorder");
      $this->template->catalogSubItems = $catalogSubItems;
      $menuCatalogSubItems = array();
      If ($catalogData->catlevel == 3) {
        $menuCatalogSubItems[$catalogData->catmasid] = dibi::fetchAll("SELECT * FROM catalogs WHERE catmasid=%i", $catalogData->catmasid, " AND catstatus=0 ORDER BY catorder");
      } elseIf ($catalogData->catlevel == 2) {
        $menuCatalogSubItems[$catalogData->catid] = $catalogSubItems;
      }
      $this->template->menuCatalogSubItems = $menuCatalogSubItems;

    }

    //naplnim vyrobce
    $this->template->manufacturer = dibi::fetch("SELECT manid, manname FROM manufacturers WHERE manid=%i", $productMasterData->promanid);

    //nastavim title detailu
    $title = trim($product->protitle);
    if (empty($title)) {
      $catName = !empty($catalogPath[$rootCatId]->catname) ? $catalogPath[$rootCatId]->catname : "";
      $proName = $product->proname . (!empty($product->proname2) ? " ".$product->proname2 : "");
      $manName = $this->template->manufacturer->manname;

      $title = $manName . " " . $proName; //catname vyhozeno z title . " - " .$catName;
    }
    $this->template->proTitle = trim($title);

    //naplnim access
    $this->template->enum_proaccess = $products->getEnumProAccess();
    
    //naplnim puvod zbozi
    $this->template->enum_proorigin = $products->getEnumProOrigin();

    //naplnim parametry zbozi
    $this->template->proParams = dibi::fetchAll("SELECT prpname, prpvalue FROM proparams WHERE prptypid=0 AND prpproid=%i", $id, " AND prpname!='Rozměry obalu (cm)' ORDER BY prpname");

    $this->template->subItems = array();

    //naplnim podrizene polozky
    if ($product->proismaster || isset($productMas)) {
      $pid = isset($productMas) ? $productMas->proid : $product->proid;
      $this->template->subItems = dibi::query($products->getSqlList("prostatus=0 AND promasid=".$pid, "proaccess, proprice, proorder, proname"))->fetchAssoc("proid");
    } else {
      $this->template->subItems = dibi::query($products->getSqlList("prostatus=0 AND proid=".$product->proid, "proaccess, proprice, proorder, proname"))->fetchAssoc("proid");
    }

    //naplnim prilohy
    $this->template->files = dibi::fetchAll("SELECT * FROM attachments WHERE ataproid=%i", $productMasterData->proid);

    //naplnim obrazky
    $images = array();
    $picPath = WWW_DIR."/pic/product/detail/";
    //defaultně beru obrázky z master položky
    $picName = ($productMasterData->propicname != "" ? $productMasterData->propicname : $productMasterData->procode);
    //zjistím zda má varianta svůj obrázek
    if ($product->promasid > 0) {
      $fName = ($product->propicname != "" ? $product->propicname : $product->procode).".jpg";
      //pokud má obrázek, beru obrázky z varianty
      if (file_exists($picPath.$fName)) $picName = ($product->propicname != "" ? $product->propicname : $product->procode);
    }

    for($i=1;$i<=10;$i++){
      $fName = $picName."_$i".'.jpg';
      if (file_exists($picPath.$fName)) {
        $images[$i]["name"] = $fName;
        $images[$i]["nameBody"] = $picName."_$i";
        $image = Nette\Utils\Image::fromFile($picPath.$fName);
        $w = (int)$image->getWidth();
        $h = (int)$image->getHeight();
        if ($w == 0) {
          $size = explode('x', $this->config["PROPICSIZE_LIST"]);
          $w = $size[0];
          $h = $size[1];
        }
        $images[$i]["w"] = $w;
        $images[$i]["h"] = $h;
      }
    }
    $this->template->images = $images;
    
    //naplnim komentare
    if ($product->promasid > 0) {
      $comments = dibi::fetchAll("
        SELECT * 
        FROM comments 
        WHERE (cmtproid=%i", $product->proid, "  OR cmtproid=%i", $product->promasid, ") AND 
        cmtreid = 0 
        ORDER BY cmtdatec DESC
        LIMIT 30");
    } else {
      $comments = dibi::fetchAll("SELECT * FROM comments WHERE cmtproid=%i", $product->proid, " AND cmtreid = 0 ORDER BY cmtdatec DESC LIMIT 10");
    }

    foreach ($comments as $key => $row) {
      $comments[$key]->res = dibi::fetchAll("
        SELECT comments.*, " . $products->getSqlFields() . " 
        FROM comments 
        LEFT JOIN products ON (cmtproid=proid) 
        WHERE cmtreid=%i", $row->cmtid, " ORDER BY cmtid DESC");
    }

    $this->template->comments = $comments;

    //autoři ke komentářům
    $this->template->authors = dibi::query("SELECT * FROM admins WHERE coalesce(admfunction, '') != '' ORDER BY admorder, admname")->fetchAssoc("admid");

    //podobne zbozi
    if (!empty($productMasterData->prooptionskeywords)) {
      $arr = explode(',', $productMasterData->prooptionskeywords);
      $sql = "";
      foreach ($arr as $value) {
        $value = trim($value);
        if ($value != "") $sql .= "proname LIKE '$value%' OR ";
      }
      
      if (!empty($sql)) {
        $sql = " proid !=".$productMasterData->proid." AND promasid=0 AND prostatus=0 AND (".substr($sql, 0, -4).") ORDER BY proorder, proname LIMIT 15";
        $this->template->proOptions = dibi::fetchAll($products->getSqlList($sql, ""));
      }  
    }
    
    //zakazu indexovani pokud komentare
    $reid = $this->getParameter("reid");
    if (!empty($reid)) {
      $this->template->pageRobots = "noindex,follow";
    }
  }
  
  public function renderCompare() {
    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);
    
    $products = array();
    $ids = array();
    foreach ($this->basketNamespace->compare as $key => $id) {
      //produkt
      $products[$id] = $pros->load($id);  
      //parametry
      $products[$id]["params"] = dibi::query("SELECT prpname, prpvalue FROM proparams WHERE prptypid=0 AND prpproid=%i", $id)->fetchAssoc("prpname");
      $ids[$id] = $id;
    }
    $this->template->paramsAll = dibi::query("SELECT prpname FROM proparams WHERE prptypid=0 AND prpproid IN %in", $ids)->fetchAssoc('prpname');
    $this->template->products = $products;

    $this->template->enum_proforid = $pros->getEnumProForId();
  }
  
  public function renderBetterPrice($id) {
    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);
    $product = $pros->load($id);
    if ($product === false) {
      throw new Nette\Application\BadRequestException('Položka nenalezena', '404');
    }
    $this->template->product = $product;
  }

  /**
   * prida polozku do porovnani
   *
   * @param integer $proid id polozky
   * @throws Nette\Application\AbortException
   */
  public function actionCompareAdd($proid) {
    $this->basketNamespace->compare[$proid] = $proid;
    $this->redirect("compare");
  }
  
  public function actionBookmarkAdd($proid) {
    if ($this->userData->usrid > 0) {
      $boks = $this->model->getBookmarksModel();
      $cnt = (int)dibi::fetchSingle("SELECT COUNT(bokid) FROM bookmarks WHERE bokproid=%i", $proid, " AND bokusrid=%i", $this->userData->usrid);
      if ($cnt === 0) {
        $boks->insert(array(
          'bokproid' => $proid,
          'bokusrid' => $this->userData->usrid,
        ));
      }  
    } else {
      $this->flashMessage("Záložky mohou vytářet pouze přihlášení uživatelé.");
      $this->redirect("User:login");
    }
    $this->redirect("User:bookmarks");
  }

  public function handleProFavAdd($proid) {
    if ($this->userData->usrid > 0) {
      $boks = $this->model->getBookmarksModel();
      $cnt = (int)dibi::fetchSingle("SELECT COUNT(bokid) FROM bookmarks WHERE bokproid=%i", $proid, " AND bokusrid=%i", $this->userData->usrid);
      if ($cnt === 0) {
        $boks->insert(array(
          'bokproid' => $proid,
          'bokusrid' => $this->userData->usrid,
        ));
      }
    } else {
      $this->basketNamespace->favorites[$proid] = $proid;
    }

    if ($this->isAjax()) {
      $this->template->userFavorites = $this->getUserFavorites($this->userData->usrid);
      $this->template->redrawFired = TRUE;
      $this->redrawControl("productDetail");
      $this->redrawControl("hearth");
      $this->redrawControl("productDetailImage");
      $this->redrawControl("favorites");
    } else {
      $this->redirect("User:bookmarks");
    }
  }

  public function handleProFavRem($proid) {
    if ($this->userData->usrid > 0) {
      $boks = $this->model->getBookmarksModel();
      dibi::query("DELETE FROM bookmarks WHERE bokusrid=%i", $this->userData->usrid, " AND bokproid=%i", $proid);
    } else {
      unset($this->basketNamespace->favorites[$proid]);
    }

    if ($this->isAjax()) {
      $this->template->userFavorites = $this->getUserFavorites($this->userData->usrid);
      $this->template->redrawFired = TRUE;
      $this->redrawControl("productDetail");
      $this->redrawControl("hearth");
      $this->redrawControl("productDetailImage");
      $this->redrawControl("favorites");
    } else {
      $this->redirect("User:bookmarks");
    }
  }

  public function actionBookmarkDelete($proid) {
    if ($this->userData->usrid > 0) {
      dibi::query("DELETE FROM bookmarks WHERE bokproid=%i", $proid, " AND bokusrid=%i", $this->userData->usrid);
    } else {
      $this->flashMessage("Záložky mohou mazat pouze přihlášení uživatelé.");
      $this->redirect("User:login");
    }
    $this->redirect("User:bookmarks");
  }

  /**
   * odebere polozku z porovnani
   *
   * @param integer $proid id polozky
   * @throws Nette\Application\AbortException
   */
  public function actionCompareDelete($proid) {
    unset($this->basketNamespace->compare[$proid]);
    $this->redirect("compare");
  }

  public function handleProductWatch($proid) {
    if ($this->isAjax()) {
      $this->template->showModal = TRUE;

      $pros = $this->model->getProductsModel();
      $this->template->product = $pros->load($proid);

      $this->redrawControl("modalWatch");
    } else {
      $this->redirect("this");
    }
  }

  protected function createComponentWatchDogPrice() {
  	$form = $this->createAppForm();

    $form->addText("dogmail", "Email")
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addHidden("dogprice", 1);
    $form->addHidden("dogstore", 0);
    $form->addSubmit("send", "Hlídat cenu");
    $form->onSuccess[] = array($this, 'watchDogSubmitted');
  	return $form;
  }

  protected function createComponentWatchDogStore() {
    $form = $this->createAppForm();

    $form->addText("dogmail", "Email")
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addHidden("dogprice", 0);
    $form->addHidden("dogstore", 1);
    $form->addSubmit("send", "Hlídat naskladnění");
    $form->onSuccess[] = array($this, 'watchDogSubmitted');
    return $form;
  }

  public function watchDogSubmitted($form) {
    if ($form->isSubmitted()) {

      $couCode = $this->appNamespace->countryCode;
      if ($couCode != 'cz' && $couCode != 'sk' && $couCode != '') {

        $form->addError("Nekorektně odeslaný formulář.");
        return FALSE;
      }

      $pros = $this->model->getProductsModel();
      $pros->setPrcCat($this->userData->usrprccat); //musim nastavit cenovou kategorii prihlaseneho uzivatele
      $pros->setCurrency($this->currencies, $this->curId);
      $proid = $this->getParameter("id");
      //sledovany produkt
      $pro = $pros->load($proid);
      $formVals = $form->getValues();

      Debugger::log("Kód země:" . $couCode . "|mail:" . $formVals->dogmail . "|IP:" . $_SERVER["REMOTE_ADDR"]);

      //zjistim jestli uz nema zaznam
      $cnt = (int)dibi::fetchSingle("SELECT COUNT(*) FROM watchdogs WHERE dogprice=%i", (int)$formVals->dogprice, " AND dogstore=%i", (int)$formVals->dogstore, " AND dogmail=%s", $formVals->dogmail, " AND dogproid=%i", $proid);
      $dogid = 0;
      if ($cnt == 0) {

        $dogs = $this->model->getWatchdogsModel();
        $formVals->dogproid = $proid;
        $formVals->dogusrid = $this->userData->usrid;

        if ((int)$formVals->dogprice === 1) {
          $formVals->dogpricevalue = $pro->proprice;
          $formVals->dogpricename = $this->curId.$this->userData->usrprccat;
        }
        $dogid = $dogs->insert($formVals);
      }
      $this->flashMessage("Hlídání bylo nastaveno.", "success");
      //mailuji info
      $template = $this->createTemplate();
      $template->product = $pro;
      $template->watchPrice = (int)$formVals->dogprice;
      $template->watchStore = (int)$formVals->dogstore;
      //načtu další aktivní sledování
      $template->items = dibi::fetchAll("
        SELECT * 
        FROM watchdogs
        INNER JOIN products ON (dogproid=proid)
        WHERE ".($dogid > 0 ? " dogid != $dogid AND " : "").' dogmail=%s', $formVals->dogmail
      );
      $template->setFile(WWW_DIR.'/../templates/Mails/mailWatchdogCreated.latte');
      $this->mailSend($formVals->dogmail, "Hlídání nastaveno", $template);
      $this->redirect('this');
    }
  }

  protected function createComponentRequestForm() {
    //prihlasovaci form
    $form = $this->createAppForm();
    $form->addText('usrname', 'Jméno a příjmení (nutno vyplnit)')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno a příjmení.');
    $form->addText('usrmail', 'Váš email (nutno vyplnit)')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.');
    $form->addText('usrmobil', 'Telefonní číslo');
    $form->addTextArea('usrnote', 'Poznámka')
      ->setDefaultValue('Mám zájem o zkušební jízdu, nejlépe dne:');

    $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"])
      ->setHtmlId('antispam')
      ->setRequired('Vyplňte prosím číslo ' . $this->config["ANTISPAM_NO"] . ', jde o test proti robotum')
      ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);

    $form->addSubmit('submit', 'Odeslat objednávku')->getControlPrototype()->class('button');
    $form->addHidden('isTestReq', 1);
    $form->onSuccess[] = array($this, 'requestSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  public function requestSubmitted($form) {
    if ($form->isSubmitted()) {
      //odmailuju nove heslo
      $proid = $this->getParam('id');
      $template = $this->createTemplate();
      $template->product = dibi::fetch("SELECT * FROM products WHERE proid=%s", $proid);
      $template->formVals = $form->getValues();
      if ($template->formVals["isTestReq"] == 1) {
        $template->setFile(WWW_DIR.'/../templates/Mails/mailTestRequest.latte');
        $subj = 'Zkušební jízda';
      } else {
        $subj = 'Dotaz na dostupnost';
        $template->setFile(WWW_DIR.'/../templates/Mails/mailProductRequest.latte');
      }

      if (!$this->mailSend($this->config["SERVER_MAIL"], $subj, $template)) $form->addError('Poptávku se nepodařilo odeslat');
      $this->flashMessage("Email s Vaší poptávkou byl odeslán.");
      $this->redirect('this');
    }
  }

  protected function createComponentBasketAddForm() {
    $id = (string)$this->getParameter('id');

    $form = $this->createAppForm();

    $form->addText('oricnt', 'Množství:', 3)
      ->addRule(Nette\Forms\Form::FILLED, 'Vyplňte počet kusů')
      ->addRule(Nette\Forms\Form::INTEGER, 'Počet kusů musí být celé číslo')
      ->addRule(Nette\Forms\Form::MAX_LENGTH, 'Nelze objednat více než 999 kusů', 3)
      ->setDefaultValue(1);

    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);

    $pro = $pros->load($id);

    $varId = 0;

    if ($pro->promasid > 0) {
      $pro = $pros->load($pro->promasid);
      $varId = $id;
    }

    if (!empty($pro->provariants)) {
      $arr = explode("\n", $pro->provariants);
      $itemVariants = array();
      foreach ($arr as $item) {
        $arr2 = explode("|", $item);
        if (!empty($arr2[0]) && !empty($arr2[1])) {
          $itemVariants[$arr2[0]] = $arr2[1];
        }
      }
      if (count($itemVariants) > 0) {
        $form->addSelect("proid", "Varianty:", $itemVariants);
        if ($varId == $id && isset($itemVariants[$id])) {
          $form["proid"]->setDefaultValue($id);
        }
      }
    } else {
      $form->addHidden('proid', $id);
    }

    $form->addSubmit('buy', 'Koupit');
    $form->onSuccess[] = [$this, 'basketAddFormSubmitted'];
    return $form;
  }

  public function basketAddFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $item = $form->getValues();

      if ($item->oricnt == 0) $item->oricnt = 1;
      if (!isset($this->basketNamespace->items[$item->proid])) $this->basketNamespace->items[$item->proid] = 0;

      $this->updateBasketItem($item->proid, $this->basketNamespace->items[$item->proid] + $item->oricnt);

      if ($this->isAjax()) {
        $form->setValues(array(), TRUE);
        $this->recalc();
        $this->template->invalidateBasket = TRUE;
        $this->redrawControl("basketWindow");
      } else {
        $this->redirect('Basket:default', array('r'=>1));
      }
    }
  }

  protected function createComponentBasketAddFormVar() {
    $id = (string)$this->getParam('id');

    $pros = $this->model->getProductsModel();
    $pros->setPrcCat($this->userData->usrprccat);
    $pros->setCurrency($this->currencies, $this->curId);

    $form = $this->createAppForm();
    $form->getElementPrototype()->class('ajax');
    $pro = $pros->load($id);
    $proMasAccess = 0;
    if ($pro->proismaster || $pro->promasid > 0) {
      $pid = $pro->promasid > 0 ? $pro->promasid : $pro->proid;
      $rows = dibi::fetchAll("SELECT proid,proaccess FROM products WHERE promasid=%i", $pid, " AND prostatus=0 ORDER BY proname");
      $proMas = dibi::fetch("SELECT proid,proaccess FROM products WHERE proid=%i", $pid, " AND prostatus=0");
      if ($proMas) $proMasAccess = $proMas->proaccess; 
    } else {  
      $rows = dibi::fetchAll("SELECT proid,proaccess FROM products WHERE proid=%i", $id, " AND prostatus=0 ORDER BY proname");
    }
    foreach ($rows as $key => $row) {
      if ($row->proaccess == 0 && $proMasAccess == 0) {
        $com = $form->addContainer($row->proid);
        $com->addHidden('proid', $row->proid);
        $com->addText('oricnt', 'Množství:', 3)
          ->setDefaultValue(1)
          ->setHtmlId('qty_'.$row->proid)
          ->addCondition(Nette\Forms\Form::FILLED)
            ->addRule(Nette\Forms\Form::INTEGER, 'Počet kusů musí být celé číslo')
            ->addRule(Nette\Forms\Form::MAX_LENGTH, 'Nelze objednat více než 999 kusů', 3);
        $com->addSubmit('oribuy', 'Koupit');
      }    
    }
    
    $form->addSubmit('buy', 'Přidat do košíku');
    $form->onSuccess[] = [$this, 'basketAddFormVarSubmitted'];
    return $form;
  }

  public function basketAddFormVarSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $pros = $this->model->getProductsModel();
      $pros->setPrcCat($this->userData->usrprccat);
      $pros->setCurrency($this->currencies, $this->curId);

      $pro = null;
      $qty = NULL;

      $vals = $form->getValues();
      foreach ($vals as $key => $item) {
        if ($form[$key]["oribuy"]->isSubmittedBy()) {
          if ($item->oricnt == 0) $item->oricnt = 1;
          if (!isset($this->basketNamespace->items[$item->proid])) $this->basketNamespace->items[$item->proid] = 0;
          $pro = $pros->load($item->proid);
          $qty = $item->oricnt;
          $this->updateBasketItem($item->proid, $this->basketNamespace->items[$item->proid] + $item->oricnt);
        }
      }
      if ($this->isAjax()) {  
        $form->setValues(array(), TRUE);
        $this->recalc();
        $this->template->basket = $this->basketNamespace;
        $this->template->invalidateBasket = TRUE;

        $this->template->modalBasketAddProduct = $pro;
        $this->template->modalBasketAddQty = $qty;

        $this->redrawControl("modalBasketAdd");
        $this->redrawControl("basketWindow");


      } else {                        
        $this->redirect('Basket:default', array('r'=>1));
      }
    }
  }
  
  protected function createComponentBetterPriceForm() {
    $proid = $this->getParam('id');
    $form = $this->createAppForm();
    $form->addHidden("proid", $proid);
    $form->addText('usrname', 'Jméno a příjmení (nutno vyplnit)')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte vaše jméno a příjmení.');
    $form->addText('usrmail', 'Váš email (nutno vyplnit)')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát.');
    $form->addText('usrprice', 'Nalezená lepší cena')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte nalezenou lepší cenu.');
    $form->addText('usrurl', 'URL adresa na lepší cenu')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte URL adresu na lepší cenu.');  
    $form->addTextArea('usrnote', 'Chcete něco dodat?', 60, 3);

    $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"])
      ->setHtmlId('antispam')
      ->setRequired('Vyplňte prosím číslo ' . $this->config["ANTISPAM_NO"] . ', jde o test proti robotum')
      ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);

    $form->addSubmit('submit', 'Odeslat informace')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'betterPriceSubmitted');
    return $form;
  }

  public function betterPriceSubmitted($form) {
    if ($form->isSubmitted()) {
      //odmailuju informace
      $vals = $form->getValues();
      $template = $this->createTemplate();
      $template->formVals = $vals;
      $pros = $this->model->getProductsModel();
      $pros->setPrcCat($this->userData->usrprccat);
      $pros->setCurrency($this->currencies, $this->curId);
      
      $product = $pros->load($vals->proid);
      $template->product = $product;
      $template->setFile(WWW_DIR.'/../templates/Mails/mailProductBetterPrice.latte');
      $this->mailSend($this->config["SERVER_MAIL"], "Lepší cena", $template);
      if ($form->hasErrors()) return false;
      
      $this->flashMessage("Děkujeme informace o lepší ceně byly odeslány. Budeme Vás kontaktovat.");
      $this->redirect('detail', $product->proid,  Nette\Utils\Strings::webalize($product->proname));
    }
  }

  /*
  protected function createComponentCommentForm() {
    $proid = (int)$this->getParam('id');
    $reid = (int)$this->getParam('reid');
    $re = (string)$this->getParam("subj");
    
    $usrid = $this->userData->usrid;
    
    $form = $this->createAppForm();
    
    $form->addHidden("cmtproid", $proid);
    $form->addHidden("cmtreid", $reid);
    $form->addHidden("cmtusrid", $usrid);
    
    $form->addText('cmtnick', 'Jméno / přezdívka (nutno vyplnit)')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno / přezdívku.');
    
    if ($reid == 0) {
      $form->addCheckbox('cmtsendreply', 'Pošlete mi odpovědi na email');  
    } else {
      $form->addHidden('cmtsendreply', FALSE);  
    }
    $form->addText('cmtmail', 'Váš email')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát.');
    if ($usrid > 0) {
      $form["cmtmail"]->setDefaultValue($this->userData->usrmail);
    }
    $form["cmtmail"]   
      ->addConditionOn($form["cmtsendreply"], Nette\Forms\Form::EQUAL, TRUE)
        ->addRule(Nette\Forms\Form::FILLED, 'Pokud chcete zaslat odpovědi na email, vyplňte prosím email.');
  
    $form->addText('cmtsubj', 'Titulek')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Titulek.');  
    
    if ($reid > 0) {
      $form["cmtsubj"]->setDefaultValue($re);
    }
      
    $form->addTextArea('cmttext', 'Zpráva', 60, 3)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte text zprávy.');
    $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"])
      ->setHtmlId('antispam')
      ->setRequired('Vyplňte prosím číslo ' . $this->config["ANTISPAM_NO"] . ', jde o test proti robotum')
      ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);

    $form->addSubmit('submit', 'Odeslat komentář')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'commentSubmitted');
    return $form;
  }

  public function commentSubmitted($form) {
    if ($form->isSubmitted()) {
      $couCode = $this->appNamespace->countryCode;
      if ($couCode == 'cz' || $couCode == 'sk' || $couCode == '') {
      } else {
        $form->addError("Nekorektně odeslaný formulář.");
        return false;
      }
      //ulozim informace
      $vals = $form->getValues();
      unset($vals["antispam"]);
      
      $pros = $this->model->getProductsModel();
      $pros->setPrcCat($this->userData->usrprccat);
      $pros->setCurrency($this->currencies, $this->curId);

      $pro = $pros->load($vals->cmtproid);
      if ((int)$pro->promasid > 0) {
        $pro = $pros->load($pro->promasid);
        $vals->cmtproid = $pro->proid;  
      }

      //mailuji adminovi
      $vals["cmtip"] = $_SERVER["REMOTE_ADDR"];
      $coms = $this->model->getCommentsModel();
      $comid = $coms->insert($vals);
      $com = $coms->load($comid);
      $template = $this->createTemplate();      
      $template->setFile(WWW_DIR.'/../templates/Mails/mailProductCommentNew.latte');
      $template->product = $pro;
      $template->comment = $com;
      $this->mailSend($this->config["SERVER_MAIL"], "Nový komentář", $template);

      $this->flashMessage("Děkujeme za Váš dotaz.");
      $this->redirect("this");
    }
  }
  */
}
