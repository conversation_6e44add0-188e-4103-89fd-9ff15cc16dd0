<?php

namespace FrontModule;
use dibi;
use Model\OrdersModel;
use Model\PayuLogsModel;
use Nette;
use OpenPayU_Exception;
use <PERSON>\Debugger;
use <PERSON>\Logger;

final class PayUPresenter extends BasePresenter {

  private $payUConfig;

  protected function startup() {
    parent::startup();
    $this->payUConfig = $this->neonParameters["payUApi"];
  }

  public function actionCreate($ordId, $key) {
    $ords = $this->model->getOrdersModel();
    $ord = $ords->load($ordId);
    $keyO = md5($ord->ordid . $ord->orddatec);
    if ($keyO != $key) {
      $this->flashMessage("Špatné volání stránky", "err");
    }

    try {
      //měnu vezmu z obj
      $curCode = $this->neonParameters["currency"][$ord->ordcurid]["key"];

      $payUApi = new \PayUApi($this->payUConfig, $curCode, $this->model);
      $payUApi->create($ordId, $this->link('//:Front:Order:status', $ord->ordid.substr(md5($ord->ordid.$ord->orddatec->getTimestamp()), 0, 8)));

      $this->terminate();
    } catch (\PayUException $e) {
      $this->flashMessage("Nastala chyba " . $e->getMessage() . "|" . $curCode, "err");
      $this->redirect("default");
    }
  }

  public function renderNotify() {
    //Debugger::log($_SERVER['REQUEST_METHOD']);
    $curCode = $this->curKey;
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
      $body = file_get_contents('php://input');
      $data = trim($body);

      If (!empty($data)) {
        $dataArr = json_decode($data, TRUE);

        if (!empty($dataArr["order"]["currencyCode"])) {
          $curCode = $dataArr["order"]["currencyCode"];
        }
      }

      try {
        if (!empty($data)) {
          $payUApi = new \PayUApi($this->payUConfig, $curCode, $this->model);
          $result = $payUApi->retrieveNotofication($data);
          if ($result == FALSE) {
            Debugger::log("ERR:" . $data);
            header("HTTP/1.1 400 ERR");
            echo "retrieveNotofication return FALSE";
          }
          //Debugger::log($result);
          if ($result->getResponse()->order->orderId) {
            if ($this->checkOrderPayUPayed($result->getResponse()->order->orderId)) {
              //Debugger::log("OK:" . $result->getResponse()->order->orderId);
              header("HTTP/1.1 200 OK");
            } else {
              Debugger::log("checkOrderPayUPayed error:" . $result->getResponse()->order->orderId);
              header("HTTP/1.1 400 ERR");
              echo "checkOrderPayUPayed return FALSE";
            }
          }
        }
      } catch (\Exception $e) {
        Debugger::log("ERR:" . $e->getMessage());
      }
    }

    $this->terminate();
  }
}
