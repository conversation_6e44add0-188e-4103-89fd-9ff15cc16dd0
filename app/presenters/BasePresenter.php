<?php

use geoip\GeoIpService;

abstract class BasePresenter extends Nette\Application\UI\Presenter {
  /**
   * @var MobileDetect
   */
  protected $mobileDetect;

  /**
   * @var Helpers\DeviceView
   */
  protected $deviceView;

  /** app */
  protected $appNamespace;
  
  /** nastaveni serveru */
  public $config = array();
  
  /** nastaveni z neonu */
  public $neonParameters = array();
  
  /** aktualni mena */
  public $currency = array();
  
  /** vsechny aktivni meny */
  public $currencies = array();
  
  /** id aktualni meny */
  public $curId = 0;

  /** id aktualni meny */
  public $curKey = '';

  /** kody men */
  public $curCodes = array();

  /** byl přesměrován  */
  protected $redirected;

  /** zda eshop vyuziva dve meny */
  public $secondCurrency = FALSE;
  
  /** prekladac */
  protected $translator;

  public $lang = 'cs';
  
  protected $mailer;

  /**
  * @inject
  * @var Nette\Caching\IStorage
  */
  public Nette\Caching\IStorage $storage;

  /** @var ModelFactory */
  protected ModelFactory $model;


  /**
  * @var \TemplateFilters @inject
  */
  public $myTemplateFilters;

  public function injectNeonParametersRepository(Classes\NeonParametersRepository $paramRepository) {
    $this->neonParameters = $paramRepository->getParameters();
  }
  
  public function injectMobileDetector(\IPub\MobileDetect\MobileDetect $mobileDetect, \IPub\MobileDetect\Helpers\DeviceView $deviceView) {
    $this->mobileDetect  = $mobileDetect;
    $this->deviceView  = $deviceView;
  }
  
  protected function startup() {

    parent::startup();

    //inicializace session aplikace
    $this->appNamespace = $this->getSession('app');
    $this->appNamespace->setExpiration('60 days', FALSE);

    if (!isset($this->appNamespace->countryRedirectStatus)) $this->appNamespace->countryRedirectStatus = '';
    if (!isset($this->appNamespace->countryCode)) $this->appNamespace->countryCode = $this->visitorCountryCode();
    $d = (string)$this->getParameter("d");

    if (!isset($this->appNamespace->curId)) {
      $this->appNamespace->curId = 1;
    }

    $forceRedirect = FALSE;

    //vynutil si změnu na CZ a je na SK
    if ($d === 'cz' && $this->appNamespace->curId == 2) {
      $this->appNamespace->curId = 1;
      $this->appNamespace->countryRedirectStatus = 'r';
      $forceRedirect = TRUE;
    }

    //vynutil si změnu na SK a je na CZ
    if ($d === 'sk' && $this->appNamespace->curId == 1) {
      $this->appNamespace->curId = 2;
      $this->appNamespace->countryRedirectStatus = 'r';
      $forceRedirect = TRUE;
    }

    /*
    if ($redirect) {
      $path = $this->getHttpRequest()->getUrl()->getPath();
      $url = $this->getHttpRequest()->getUrl()->getBaseUrl();
      $this->redirectUrl($url);
    }
    */

    //zjistím z jaké země přichází
    //nastavení přesměrování
    /*
     * rs = 'r' -> redirect na správný server
     * rs = 's' -> zůstat na aktuálním
     * rs = 'c' -> vymazat cookies
     */
    $rs = (string)$this->getParameter("rs");
    $couCode = $this->appNamespace->countryCode;

    $target = "";

    if ($rs == 'c') {
      $this->appNamespace->countryRedirectStatus = '';
      $rs = "";
    }

    $pathAdd = '';
    if ($this->appNamespace->curId == 1 && $couCode == 'sk') {
      //nesedí země
      if (empty($rs)) {
        //není rs cookie - nastavím na přesměrování
        if ($this->appNamespace->countryRedirectStatus == "") {
          $this->appNamespace->countryRedirectStatus = 'r';
          $pathAdd = '?rr=1';
        }
      } else {
        if (empty($this->appNamespace->countryRedirectStatus)) $this->appNamespace->countryRedirectStatus = $rs;
      }
      $target = $couCode;
    } else if ($this->appNamespace->curId == 2 && $couCode == 'cz') {
      if (empty($rs)) {
        //není rs cookie - nastavím na přesměrování
        if ($this->appNamespace->countryRedirectStatus == "") {
          $this->appNamespace->countryRedirectStatus = 'r';
          $pathAdd = '?rr=1';
        }
      } else {
        if (empty($this->appNamespace->countryRedirectStatus)) $this->appNamespace->countryRedirectStatus = $rs;
      }
      $target = $couCode;
    }

    //redirect pokud nesedí IP a země a sám si ještě nevybral
    $this->redirected = "";
    if (!empty($target) && $this->appNamespace->countryRedirectStatus == 'r' && $forceRedirect === FALSE) {
      //přesměruji
      $this->appNamespace->curId = $target === 'sk' ? 2 : 1;

      $this->redirected = "1";

      /*
      $path = $this->getHttpRequest()->getUrl()->getPath();
      $url = $this->getHttpRequest()->getUrl()->getBaseUrl();
      //$this->redirectUrl($url);

      $this->redirectUrl('https://www.goldfitness.cz/'.$path.$pathAdd);
      */
    }

    $curId = $this->appNamespace->curId;
    $this->curId = $this->appNamespace->curId;

    $this->currency = $this->neonParameters["currency"][$curId];
    $this->currencies = $this->neonParameters["currency"]; 
    $this->curId = $this->currency["id"];
    $this->curKey = $this->currency["key"];

    $this->secondCurrency = isset($this->neonParameters["currency"][2]);
    $this->curCodes[1] = $this->neonParameters["currency"][1]["code"]; 
    $this->curCodes[2] = "";
    if (isset($this->neonParameters["currency"][2]["code"])) $this->curCodes[2] = $this->neonParameters["currency"][2]["code"];

    //nastavím továrnu na model
    $this->model = new ModelFactory($this->storage);
    $this->model->setCurrency($this->currencies, $this->curId);

    //nactu nastaveni z datatabaze
    //nactu uzivatelske nastaveni do cache
    $config = $this->model->getConfigModel();
    $this->config = $config->getConfig();
    
    //pro SK jen platbu kartou
    if (isset($this->neonParameters["onlinePayTypes"]) && $this->curId == 2) {
      $payTypeOnline = $this->neonParameters["onlinePayTypes"];
      foreach ($payTypeOnline as $key => $val) {
        if ($key != 'c') unset($payTypeOnline[$key]);  
      }
      $this->neonParameters["onlinePayTypes"] = $payTypeOnline;
    }

    //nastaveni prekladani
    $this->lang = $this->config["SERVER_LANGUAGE"];
    $dic = array();
    If ($this->lang != 'cs') {
      //nactu slovnik
      //pokud neni cache vytvorim - mela by byt vzdy vytvorena
      $dic = DictionariesModel::getDictionary($this->lang);
    }
    $this->translator = new MyTranslator($this->lang, $dic);
  }

  protected function beforeRender() {
    $this->template->isProduction = IS_PRODUCTION;

    //predzvykam velikosti obrazku
    $size = explode('x', $this->config["PROPICSIZE_LIST"]);
    $this->template->picListWidth = $size[0];
    $this->template->picListHeight = $size[1];

    $size = explode('x', $this->config["PROPICSIZE_DETAIL"]);
    $this->template->picDetailWidth = $size[0];
    $this->template->picDetailHeight = $size[1];

    $size = explode('x', $this->config["PROPICSIZE_BIG"]);
    $this->template->picBigWidth = $size[0];
    $this->template->picBigHeight = $size[1];
    
    $this->template->secondCurrency = $this->secondCurrency;
    $this->template->curKey = $this->curKey;

    if (isset($this->neonParameters["labels"])) $this->template->neonLabels = $this->neonParameters["labels"];
    $this->template->curId = $this->curId;
  }

  public function exportPohodaXph($id) {
    $ords = $this->model->getOrdersModel();
    $template = $this->createTemplate();
    $template->order = $ords->load($id);
    $delModes = $this->model->getDeliveryModesModel();
    $delModes->setCurrency($this->currencies, $template->order->ordcurid);
    $template->ordItems = dibi::fetchAll("
    SELECT * 
    FROM orditems 
    LEFT JOIN products ON (oriproid=proid)
    WHERE oriordid=%i", $id, " ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END");
    $template->setFile(WWW_DIR.'/../templates/Mails/pohodaXph.latte');
    return (string)$template;
  }
  
  
  public function printOrder($id, $dest="I", $templateName='Order.latte') {
    $orders = $this->model->getOrdersModel();

    if (is_numeric($id)) {
      $ids = array($id);
    } else {
      $ids = $id;
    }

    $order = array();

    // mPDF
    $mpdf = new \Mpdf\Mpdf();
    //$mpdf->useOnlyCoreFonts = true;
    $mpdf->SetDisplayMode('real');
    //$mpdf->SetAutoFont(0);

    foreach ($ids as $ordId) {

      $order = $orders->load($ordId);

      $document = $this->fillTemplatePrintOrder($order->ordid, $templateName);
      $mpdf->AddPage('P');
      $mpdf->WriteHTML($document, 2);

      //poznačím vytištěno
      $orders->update($ordId, array("ordprinted" => 1));
    }

    $fname = "export_dokladu";
    if ($templateName=='Order.phtml') {
      $fname = $order->ordcode;
    } else if ($templateName=='Invoice.phtml') {
      $fname = $order->ordinvcode;
    } else if ($templateName=='Proforma.phtml') {
      $fname = $order->ordprocode;
    }

    if ($dest == 'P') {
      $mpdf->SetJS('window.onload = function() { window.print(); }');
      $dest="I";
    }
    if ($dest=="I" || $dest=="F") {
      $name = TEMP_DIR."/".$fname.".pdf";
    } else {
      $name = $fname.".pdf";
    }
    $mpdf->Output($name, $dest);
  }


  private function fillTemplatePrintOrder($ordId, $templateName) {
    $enums = $this->model->getEnumcatsModel();
    $delModes = $this->model->getDeliveryModesModel();
    $orders = $this->model->getOrdersModel();

    $template = $this->getTemplate();

    $template->headers = (object) NULL;
    $template->order = dibi::fetch("SELECT *, ordinvdate + INTERVAL 14 DAY AS datepay FROM orders WHERE ordid=%i", $ordId);
    //nastavim menu objednavky
    $orders->setCurrency($this->currencies, $template->order->ordcurid);
    $delModes->setCurrency($this->currencies, $template->order->ordcurid);

    $template->curId = $template->order->ordcurid;

    $ordItems = dibi::fetchAll("SELECT * FROM orditems LEFT JOIN products ON (oriproid=proid) WHERE oriordid=%i", $ordId, "  ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END, orivatid");

    $vatLev[0] = (double)$this->config["VATTYPE_0"]/100;
    $vatLev[1] = (double)$this->config["VATTYPE_1"]/100;
    $delRow = Null;
    $vatSum = 0;
    $sum = 0;
    $vatSumaryPro[0] = array();
    $vatSumaryPro[0]['price'] = 0;
    $vatSumaryPro[0]['vat'] = 0;
    $vatSumaryPro[0]['vatLevel'] = $this->config["VATTYPE_0"];
    $vatSumaryPro[0]['pricevat'] = 0;

    $vatSumaryPro[1] = array();
    $vatSumaryPro[1]['price'] = 0;
    $vatSumaryPro[1]['vat'] = 0;
    $vatSumaryPro[1]['vatLevel'] = $this->config["VATTYPE_1"];
    $vatSumaryPro[1]['pricevat'] = 0;

    foreach ($ordItems as $key => $row) {
      $priceV = (double)$ordItems[$key]->oriprice*(double)$ordItems[$key]->oriqty-(double)$ordItems[$key]->oridisc;
      if ($row->oritypid == 0) {
        //jedna se o zbozi
        $vat = round($priceV*$vatLev[$ordItems[$key]->orivatid], 2);
        $ordItems[$key]->oripricecntvat = $priceV;
        $ordItems[$key]->oripricecntnovat = $priceV - ($priceV*($row->orivatid == 1 ? 0.1304 : 0.1736));
        $ordItems[$key]->oricntvat = $ordItems[$key]->oripricecntvat - $ordItems[$key]->oripricecntnovat;
        // sumarizace DPH
        $vatSumaryPro[$row->orivatid]['price'] += $ordItems[$key]->oripricecntnovat;
        $vatSumaryPro[$row->orivatid]['vat'] += $ordItems[$key]->oricntvat;
        $vatSumaryPro[$row->orivatid]['pricevat'] += $ordItems[$key]->oripricecntvat;
        $vatSum += $ordItems[$key]->oripricecntvat;
        $sum += $ordItems[$key]->oripricecntnovat;
      } else if ($row->oritypid == 1) {
        $delRow = $row;
      }
    }
    //rozpocitam dopravu
    $vatSumaryDel[0] = array();
    $vatSumaryDel[0]['price'] = 0;
    $vatSumaryDel[0]['vat'] = 0;
    $vatSumaryDel[0]['pricevat'] = 0;
    $vatSumaryDel[1] = array();
    $vatSumaryDel[1]['price'] = 0;
    $vatSumaryDel[1]['vat'] = 0;
    $vatSumaryDel[1]['pricevat'] = 0;
    if ($vatSumaryPro[0]['pricevat'] > 0) {
      $vatSumaryDel[0]['pricevat'] = round($vatSumaryPro[0]['pricevat']/$vatSum*(double)$delRow->oriprice, 2);
      $vatSumaryDel[0]['price'] = round($vatSumaryDel[0]['pricevat'] - ($vatSumaryDel[0]['pricevat']*0.1736), 2);
      $vatSumaryDel[0]['vat'] = $vatSumaryDel[0]['pricevat']-$vatSumaryDel[0]['price'];
    }
    if ($vatSumaryPro[1]['pricevat'] > 0) {
      $vatSumaryDel[1]['pricevat'] = round($vatSumaryPro[1]['pricevat']/$vatSum*(double)$delRow->oriprice, 2);
      $vatSumaryDel[1]['price'] = round($vatSumaryDel[1]['pricevat'] - ($vatSumaryDel[1]['pricevat']*0.1304), 2);
      $vatSumaryDel[1]['vat'] = $vatSumaryDel[1]['pricevat']-$vatSumaryDel[1]['price'];
    }

    $template->ordItems = $ordItems;
    $template->vatSumaryPro = $vatSumaryPro;
    $template->vatSumaryDel = $vatSumaryDel;

    $template->ordItemDelivery = dibi::fetch("SELECT * FROM orditems WHERE oritypid=1 AND oriordid=%i", $ordId);
    $template->payDate = dibi::fetchSingle("SELECT ordinvdate + INTERVAL 14 DAY FROM orders WHERE ordid=%i", $ordId);
    $template->enum_delmode = $orders->getEnumOrdDelIdSimple();
    $template->payMode = $delModes->load($template->order->orddelid);
    $template->delMode = $delModes->load($template->payMode->delmasid);
    $template->enum_countries = $enums->getEnumCountries();
    $template->setFile(WWW_DIR.'/../templates/pdf/'.$templateName);

    $template->headers = (object) NULL;

    return (string)$template;
  }


  protected function getVerifyCode($length = 6) {
    $base = "abcdefghjkmnpqrstwxyz123456789";
    $max = strlen($base)-1;
    $string = "";
    mt_srand((double)microtime()*1000000);
    while (strlen($string) < $length) $string .= $base[mt_rand(0,$max)];
    return $string;
  }

  /**
   * kontrola úhrady P/ayU
   *
   * @param $pauId
   * @return bool
   * @throws PayUException|DibiException
   */
  protected function checkOrderPayUPayed($pauId) {
    $ords = $this->model->getOrdersModel();
    $ord = $ords->load($pauId, 'pauid');

    if ($ord === FALSE) {

      //najdu v logu pauid
      $logs = $this->model->getPayuLogsModel();
      $log = $logs->load($pauId, "payid");

      if ($log === FALSE) {
        \Tracy\Debugger::log("FALSE: záznam v logu nenalezen" . $pauId);
        return FALSE;
      }

      $ord = $ords->load($log->logordid);

      if ($ord === FALSE) {
        \Tracy\Debugger::log("FALSE: obj. v logu nenalezena" . $pauId);
        return FALSE;
      }

      $ords->update($ord->ordid, array("ordpauid"=>$log->logpayid));
    }

    $payUConfig = $this->neonParameters["payUApi"];
    //měnu vezmu z obj
    $curCode = $this->neonParameters["currency"][$ord->ordcurid]["key"];
    $payUApi = new \PayUApi($payUConfig, $curCode, $this->model);
    $payUStatus = $payUApi->retrieve($pauId);

    if ((int)$ord->ordpaystatus === 1) {
      return TRUE;
    }

    if ($payUStatus["status"] === "COMPLETED") {
      $vals = array();
      $vals["ordpaystatus"] = 1;
      $vals["ordstatus"] = 6;
      $ords->update($ord->ordid, $vals);
      $ords->logStatus($ord->ordid, 6);
      $ord = $ords->load($ord->ordid);
      //mailuju
      $mailTemplate = $this->createTemplate();
      $mailTemplate->orderRow = $ord;

      $mailTemplate->enum_ordStatus = $ords->getEnumOrdStatus();
      $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailOrderChanged.latte');

      //mailuju zakaznikovi a adminovi
      try {
        $this->mailSend($ord->ordmail, $this->translator->translate("Změna stavu objednávky č.")." ".$ord->ordcode, $mailTemplate);
        $this->mailSend($this->config["SERVER_MAILORDERS"], "Objednávka č. ".$ord->ordcode." uhrazena PayU", $mailTemplate);
      } catch (Nette\InvalidStateException $e) {
        \Tracy\Debugger::log("ERR: mailování uhrazeno");
        $this->flashMessage("Nepodařilo se odeslat informační email o změně stavu objednávky (".$e->getMessage().")".' [ID:'.$ord->ordid.']', "err");
      }
      return TRUE;
    }

    return FALSE;
  }

  protected function mailSend($mailTo, $subject, $bodyTemplate, $mailFrom="", $attachments=array()) {
    if (empty($mailTo)) {
      return (TRUE);
    }
    $mail = new Nette\Mail\Message();
    if ($mailFrom == "") {
      $mailFrom = $this->config["SERVER_MAIL"];
    }
    $mail->setFrom($mailFrom, $this->config["SERVER_NAMESHORT"]);
    $mail->addTo($mailTo);
    $mail->setSubject($subject." - ".$this->config["SERVER_NAMESHORT"]);

    $html = (string)$bodyTemplate;
    $mail->setHtmlBody($html);
    
    //doplnim prilohy pokud jsou
    foreach ($attachments as $fileName) {
      $mail->addAttachment($fileName);
    }

    try {

      $mailer = new Nette\Mail\SendmailMailer;
      /*
      $mailer = new Nette\Mail\SmtpMailer([
        'host' => 'mail.vas-hosting.cz',
        'port' => '465',
        'username' => '<EMAIL>',
        'password' => '_K8wBK4jjGKcGvTY',
        'secure' => 'ssl',
      ]);
      */

      if (IS_PRODUCTION) {
        $mailer->send($mail);
      } else {
        \Tracy\Debugger::log($html);
      }

    } catch (\Exception $e) {
      \Tracy\Debugger::log($e->getMessage());
    }
  }

  /**
   * mailuje zákazníkovi info o změně stavu obj.
   *
   * @param $order
   * @param $silent
   * @return bool
   */
  protected function mailOrderChanged($order, $silent=TRUE): bool {

    if ($order->ordstatus != 3 && $order->ordstatus != 4 && $order->ordstatus != 8 && $order->ordstatus != 9 /* && $order->ordstatus != 10*/) {
      return false;
    }

    $ords = $this->model->getOrdersModel();

    $mailTemplate = $this->createTemplate();
    $mailTemplate->orderRow = $order;

    $dels = $this->model->getDeliveryModesModel();
    $delPay = $dels->getDeliveryPaymentFromOrder($order);
    $mailTemplate->delMode = $delPay["delivery"];
    $mailTemplate->payMode = $delPay["payment"];

    if (!empty($order->ordparcode)) {
      $url = (string)$mailTemplate->delMode->delurlparcel;
      if (!empty($url)) $url = str_replace('#CODE#', $order->ordparcode, $url);
      $mailTemplate->parcelURL = $url;
    }

    $mailTemplate->enum_ordStatus = $ords->getEnumOrdStatus();
    $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailOrderChanged.latte');

    try {
      $this->mailSend($order->ordmail, $this->translator->translate("Změna stavu objednávky č.")." ".$order->ordcode, $mailTemplate);
    } catch (Nette\InvalidStateException $e) {
      $errMsg = "Nepodařilo se odeslat informační email o změně stavu objednávky (".$e->getMessage().")".' [ID:'.$order->ordid.']';
      if ($silent) {
        \Tracy\Debugger::log($errMsg);
      } else {
        $this->flashMessage($errMsg, "err");
      }
      return false;
    }
    return true;
  }

  
  protected function mailMail(Nette\Mail\Message $mail) {
    $mailer = new Nette\Mail\SendmailMailer;
    //$mailer->commandArgs = '-f <EMAIL>';
    /*
    $mailer = new Nette\Mail\SmtpMailer([
      'host' => 'mail.vas-hosting.cz',
      'port' => '465',
      'username' => '<EMAIL>',
      'password' => '_K8wBK4jjGKcGvTY',
      'secure' => 'ssl',
    ]);
    */
    $mailer->send($mail);
  }
  
  public function injectMailer(Nette\Mail\IMailer $mailer) {
    $this->mailer = $mailer;
  }

  protected function smsSend($gsm, $bodyTemplate): bool {
    include_once LIBS_DIR.'/sms/SMSlib.php';
    if (empty($gsm)) return (True);
    //if ($_SERVER["SERVER_NAME"] == '') return(TRUE);
    $text = (string)$bodyTemplate;

    $smsConfig = $this->neonParameters['sms'];
    if (!empty($smsConfig["login"])) {
      $sms = new sms($smsConfig["login"], $smsConfig["passw"]);
      $gsm = trim($gsm);
      $gsm = substr($gsm, -9);
      return ($sms->send('+420'.$gsm, $text, $this->config["SERVER_MAIL"]));
    } else {
      return(TRUE);
    }
  }

  /**
   * Formats view template file names.
   * @return array
   */
  public function formatTemplateFiles() {
    $root = WWW_DIR . '/../templates';
    $name = $this->getName();
    $presenter = substr($name, strrpos(':' . $name, ':'));
    $module = substr($name, 0, (int) strrpos($name, ':')).'Module';

    return array(
      "$root/$module/$presenter.$this->view.latte",
    );
  }

  public function getPriceVat($price, $vatid) {
    $vatLevel = (int)$this->config["VATTYPE_".$vatid];
    return($price * (1+($vatLevel / 100)));
  }

  /**
   * Formats layout template file names.
   * @return array
   */
  public function formatLayoutTemplateFiles() {

    $root = WWW_DIR . '/../templates';
    $name = $this->getName();
    $presenter = substr($name, strrpos(':' . $name, ':'));
    $module = substr($name, 0, (int) strrpos($name, ':')).'Module';
    $layout = $this->layout ? $this->layout : 'layout';
    return array(
      "$root/$module/@$layout.latte",
      "$root/@$layout.latte",
    );
  }

  public function formatPrice($price, $decimals=Null) {
    $price = (double)$price;
    if ($decimals == Null) $decimals = (int)$this->currency["decimals"];
    $formated = str_replace(" ", "\xc2\xa0", number_format($price, $decimals, ",", " "));
    if ($decimals == 1) $formated .= '0';
    return $formated." ".$this->currency["code"];
  }

  public function formatNumberMySQL($value) {
    $value = str_replace(',', '.', $value);
    $value = str_replace(' ', '', $value);
    return $value;
  }

  /**
  * formátuje datum do SQL formátu
  * 
  * @param string $date datum ve formátu dd.mm.rrr
  */
  public function formatDateMySQL($date) {
    $d = "";
    $m = "";
    $y = "";
    
    if (!empty($date) && strpos($date, '.') > 0) {
      list($d, $m, $y) = explode('.', $date);
    } else {
      return($date);
    }  
    return(trim($y).'-'.trim($m).'-'.trim($d));
  }
  
  /**
  * formátuje datum do dd.mm.rrr
  * 
  * @param string $date datum SQL formátu
  */
  public function formatDate($date) {
    $d = "";
    $m = "";
    $y = "";
    list($date, $time) = explode(' ', $date);
    if (!empty($date) && strpos($date, '-') > 0) {
      list($y, $m, $d) = explode('-', $date);
    } else {
      return($date);
    }  
    return($d.'.'.$m.'.'.$y);
  }
  
  public function createTemplate($class = NULL) {
    $template = parent::createTemplate($class);
    
    $template->addFilter(NULL, [
      $this->myTemplateFilters,
      'loader'
    ]);
    
    // Add mobile detect and its helper to template
    $template->_mobileDetect    = $this->mobileDetect;
    $template->_deviceView      = $this->deviceView;
    
    $template->config = $this->config;
    $template->curId = $this->curId;
    $template->setTranslator($this->translator);
    $template->lang = $this->lang;
    return $template;
  }
  
  /**
  * číselník na meny
  * 
  */
  public function getEnumCurr() {
    $items = array();
    if ($this->secondCurrency) {  
      $items = array(
        1 => $this->curCodes[1],
        2 => $this->curCodes[2]
      );
    }
    return $items;  
  }

  /**
  * číselník cenove kategorie
  * 
  */
  public function getEnumPrcCat() {
    $usrs = $this->model->getUsersModel();
    $labels = array();
    if (isset($this->neonParameters["labels"])) $labels = $this->neonParameters["labels"]; 
    return $usrs->getEnumUsrPrcCat($labels);
  }

  /**
  * číselínk pro filtrování Types
  *
  */
  public function getEnumTypes() {
    return array(
      'skladem' => 'Skladem',
      'akce' => 'Akce',
      'novinka' => 'Novinka',
      'tip' => 'Doporučujeme',
      'vegan' => 'Vegan',
      'zlatedny' => 'Zlaté dny',
      'dopravazdarma' => 'Doprava zdarma',
    );
  }

  function curl_get_contents($url) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
  }

  protected function getDelTerms() {
    $proAcces = 0;
    $terms = array();
    //jen zbozi skladem
    //zjistim, jestli budeme dodavat do druheho dne nebo az obden podle casu objednani
    $row = dibi::fetch("
      SELECT CURDATE() AS curdate, 
      CURTIME() AS curtime, 
      IF(CURTIME() > TIME('13:00:00'),1,0) AS delIsAfter
      ");
    $delDaysAdd = $proAcces + (int)$row->delIsAfter;

    //jedu den po dni a zjistuju jestli neni nasledujici dny svatek, za kazdy svatek pridam den
    $days = $delDaysAdd;
    if ((int)$row->delIsAfter == 0) {
      $start = 0;
      $end = $days;
    } else {
      $start = 1;
      $end = $days;
    }
    $i = $start;
    $cnt = $start;
    do {
      if ($this->isHoliday($row->curdate, $cnt)) {
        $delDaysAdd ++;
      } else {
        $i++;
      }
      $cnt++;
    } while ($i <= $end);


    if ($delDaysAdd == 0) {
      $delTerm = "dnes";
    } else if ($delDaysAdd == 1) {
      $delTerm = "zítra";
    } else {
      $d = dibi::fetchSingle("SELECT CURDATE() + INTERVAL $delDaysAdd DAY AS curdate");
      $delTerm = $this->formatDate($d);
    }

    return $delTerm;
  }

  protected function isHoliday($date, $index, $rev=FALSE) {
    $dir = ($rev ? '-' : '+');
    //zjistim datum pro ktere hlidam svatek
    $row = dibi::fetch("SELECT DATE('$date') $dir INTERVAL $index DAY AS curdate, WEEKDAY(DATE('$date') $dir INTERVAL $index DAY) as curweekday, YEAR(DATE('$date') $dir INTERVAL $index DAY) AS curyear");
    if ($row->curweekday == 5 || $row->curweekday == 6) return TRUE;
    if (isset($this->config["HOLIDAYS_FORMATED"])) {
      $holidays = $this->config["HOLIDAYS_FORMATED"];
      foreach ($holidays as $d) {
        $d = $row->curyear.'-'.$d;
        //je to svatek
        if ($d == substr($row->curdate, 0, 10)) return TRUE;
      }
    }
    return FALSE;
  }

  protected function saveImage($image, $path, $filename, $sizes) {
    //upravim a ulozim obrazek
    if (isset($image) && is_array($sizes)) {
      foreach ($sizes as $size) {
        $img = clone $image;
        $arr = explode("x", $size);
        $w = 0;
        $h = 0;
        $dir = '';
        if (isset($arr[0])) $w = (int)$arr[0];
        if (isset($arr[1])) $h = (int)$arr[1];
        if (isset($arr[2])) $dir = $arr[2];

        if ($w > 0 || $h > 0) {
          $img->resize($w, $h); // resize, který prostor vyplní a možná překročí
            //->crop('50%', '50%', $w, $h); // ořezání po stranách
          $blank = Nette\Utils\Image::fromBlank($w, $h, Nette\Utils\Image::rgb(255, 255, 255));
          $blank->place($img, '50%', '50%');

          $watermarkFile = $path.($dir!="" ? '/'.$dir : '').'/watermark.png';
          if (file_exists($watermarkFile)) {
            $watermark = Nette\Utils\Image::fromFile($watermarkFile);
            $blank->place($watermark, '50%', '50%');
          }

          $blank->save($path.($dir!="" ? '/'.$dir : '').'/'.$filename, 92, Nette\Utils\Image::JPEG);
        }
      }
    }
  }

  /**
   * projde všechny adr. v $path (kromě "src") a vymaže $fileName
   *
   * @param string $path
   * @param string $fileName
   * @param bool $fullDelete pokud TRUE vymaže i src
   */
  public function deletePic(string $path, string $fileName, bool $fullDelete=FALSE) {
    $dir = new DirectoryIterator($path);
    foreach ($dir as $fileinfo) {
      if ($fileinfo->isDir() && !$fileinfo->isDot()) {
        $dir = $fileinfo->getFilename();

        if ($dir === 'src' && $fullDelete===FALSE) {
          continue;
        }

        if ($dir === 'big' && $fullDelete===FALSE) {
          continue;
        }

        @unlink($path.$dir.'/'.$fileName);

        $fileNameWebp = substr($fileName, 0, -3) . "webp";
        @unlink($path.$dir.'/'.$fileNameWebp);
      }
    }
  }

  public function visitorCountryCode(): string {
    $ip = $_SERVER["REMOTE_ADDR"];
    if(filter_var(@$_SERVER['HTTP_X_FORWARDED_FOR'], FILTER_VALIDATE_IP))
      $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    if(filter_var(@$_SERVER['HTTP_CLIENT_IP'], FILTER_VALIDATE_IP))
      $ip = $_SERVER['HTTP_CLIENT_IP'];
    
    // Použití GeoIpService pro získání kódu země
    $countryCode = GeoIpService::getCountryCode($ip);

    // Pokud není kód země nalezen, vrátíme prázdný řetězec
    if ($countryCode === null) {
      return '';
    }
    
    return strtolower($countryCode);
  }

  /**
   * @param $postCode
   * @return bool
   * @throws \Dibi\Exception
   */
  protected function checkPostCodeFormat($postCode): bool|string {
    if (empty($postCode)) {
      return true;
    }

    $postCode = trim(str_replace(" ", "", $postCode));
    if (preg_match('#[^0-9]#',$postCode)) {
      return "PSČ obsahuje nějaké jiné znaky než číslice.";
    }

    if (strlen($postCode) > 5) {
      return "PSČ obsahuje více číslic než 5";
    }

    return true;
  }


  /**
   * kontroluje PSČ proti databázi PSČ od české pošty
   *
   * @param $postCode
   * @return bool
   * @throws \Dibi\Exception
   */
  protected function checkPostCode($postCode): bool {
    if (empty($postCode)) {
      return true;
    }

    $postCode = trim(str_replace(" ", "", $postCode));

    //kontrola s databází
    $cnt = (int)dibi::fetchSingle("SELECT count(*) FROM czechpostcodes WHERE cpccode=%i", (int)$postCode);
    if ($cnt === 0) {
      return false;
    }

    return true;
  }
}
