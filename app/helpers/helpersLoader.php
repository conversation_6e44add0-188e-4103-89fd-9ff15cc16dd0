<?php
class TemplateFilters {

    use Nette\SmartObject;

    /** @var string */
    private $wwwDir;

    /** @var array */
    private $config;

    public function __construct($wwwDir) {
      $this->wwwDir = $wwwDir;

      $this->config = [

        "PROPICSIZE_BIG" => "400x400",
        "PROPICSIZE_DETAIL" => "315x315",
        "PROPICSIZE_LIST" => "160x160",

        "currency" => [
          1 => [
            "id" => 1,
            "key" => "CZK",
            "code" => "Kč",
            "decimals" => 0,
          ],
          2 => [
            "id" => 2,
            "key" => "EUR",
            "code" => "€",
            "decimals" => 1,
          ]
        ]
      ];
    }

    /**
    * Method we will register as callback
    * in method $template->addFilter().
    */
    public function loader($helper) {
      if (method_exists($this, $helper)) {
        //return [$this, $helper];
      }
      return call_user_func_array(\Nette\Utils\Callback::closure($this, $helper), array_slice(func_get_args(), 1));
    }

    public function getDeliveryLogo($serviceCode) {
      return "img/delivery/" . strtolower($serviceCode) . ".png";
    }

    /**
    * vraci nazev obrazku správce
    *
    * @param string $size
    * @param dibirow $row
    *
    * @return string cesta k obrazku
    */
    public function getAdminPicName($row, $size) {
      $fileName = $row->admid;
      return $this->getPicFileName("admin", $fileName, $size);
    }

    /**
    * vraci nazev obrazku správce webp
    *
    * @param string $size
    * @param dibirow $row
    *
    * @return string cesta k obrazku
    */
    public function getAdminPicNameWebp($row, $size) {
      $fileName = $row->admid;
      return $this->getPicFileName("admin", $fileName, $size, FALSE, 'webp');
    }

        /**
    * vraci nazev obrazku správce
    *
    * @param string $size
    * @param dibirow $row
    *
    * @return string cesta k obrazku
    */
    public function getManufacturerPicName($row, $size) {
      $fileName = $row->manid;
      return $this->getPicFileName("manufacturer", $fileName, $size);
    }

    /**
    * vraci nazev obrazku správce webp
    *
    * @param string $size
    * @param dibirow $row
    *
    * @return string cesta k obrazku
    */
    public function getManufacturerPicNameWebp($row, $size) {
      $fileName = $row->manid;
      return $this->getPicFileName("manufacturer", $fileName, $size, FALSE, 'webp');
    }

    /**
    * vraci nazev obrazku zbozi
    *
    * @param string $size [list,detail,big]
    * @param dibirow $row
    *
    * @return string cesta k obrazku
    */
    public function getProductPicName($row, $size): string {
      return $this->getProductPicFileName($row, $size, FALSE);
    }

    public function getProductPicNameWebp($product, $size, $returnEmpty=FALSE): string {
      return $this->getProductPicFileName($product, $size, $returnEmpty, 'webp');
    }

    /**
   * vraci nazev obrazku zbozi - pokud se jedna o variantu vrati nazev obrazku master polozky
   *
   * @param string $size
   * @param dibirow $row
   *
   * @return string cesta k obrazku
   * @throws DibiException
   */
    public function getProductPicNameMaster($row, $size) {
      if ((int)$row->promasid > 0) {
        $row = dibi::fetch('SELECT procode, propicname FROM products WHERE proid=%i', $row->promasid);
      }
      return($this->getProductPicName($row, $size));
    }

        /**
   * vraci nazev obrazku zbozi - pokud se jedna o variantu vrati nazev obrazku master polozky
   *
   * @param string $size
   * @param dibirow $row
   *
   * @return string cesta k obrazku
   * @throws DibiException
   */
    public function getProductPicNameMasterWebp($row, $size): string {
      if ((int)$row->promasid > 0) {
        $row = dibi::fetch('SELECT procode, propicname FROM products WHERE proid=%i', $row->promasid);
      }
      return($this->getProductPicNameWebp($row, $size));
    }

    public function getProductPicNameUsrSize($row, $returnEmpty=FALSE): string {
      //cesta do adresáře
      $path = $this->wwwDir . "/pic/product/usrsize/";
      $fileName = $row->propicnamevar . ".jpg";

      if (file_exists($path . $fileName)) {
        return "pic/product/usrsize/" . rawurlencode($fileName);
      } else {
        if ($returnEmpty) {
          return "";
        }
        return "pic/no.jpg";
      }

    }

    /**
   * @param $row produkt
   * @param $size velikost obrázku
   * @param bool $returnEmpty vrací prázdný string pokud obrázek neexistuje
   * @return string
   */
    public function getProductPicFileName($row, $size, $returnEmpty=FALSE, $format="jpg") {

      $fileName = (!empty($row->propicname) ? trim($row->propicname) : $row->procode);

      //zjistím rozměry
      if ($size === 'big') {
        $size = $this->config["PROPICSIZE_BIG"];
      } else if ($size === 'detail') {
        $size = $this->config["PROPICSIZE_DETAIL"];
      } else if ($size === 'list') {
        $size = $this->config["PROPICSIZE_LIST"];
      }

      return $this->getPicFileName("product", $fileName, $size, $returnEmpty, $format);
    }

    Public function getProductPicByFileName($fileName, $size, $returnEmpty=FALSE, $format="jpg"): bool|string {

      if ($size === 'big') {
        $size = $this->config["PROPICSIZE_BIG"];
      } else if ($size === 'detail') {
        $size = $this->config["PROPICSIZE_DETAIL"];
      } else if ($size === 'list') {
        $size = $this->config["PROPICSIZE_LIST"];
      }

      return $this->getPicFileName("product", $fileName, $size, $returnEmpty, $format);
    }

  /**
   * @param $type
   * @param $fileName
   * @param $size velikost obrázku
   * @param bool $returnEmpty vrací prázdný string pokud obrázek neexistuje
   * @param string $format
   * @return string
   */
    public function getPicFileName($type, $fileName, $size, $returnEmpty=FALSE, $format="jpg"): bool|string {

      //obrázek nebyl nalezen nebo se ho nepodařilo vygenerovat
      $returnEmptyPicture = FALSE;

      //cesta do adresáře
      $path = $this->wwwDir . "/pic/" . $type . "/";

      $picPath = $path . "$size/";

      $srcPath = "";
      $srcPath2 = "";

      //src path
      if ($type === "product") {
        $srcPath = $path . "src/";
        $srcPath2 = $path . "big/";
      } else {
        $srcPath = $path . "src/";
      }

      if (!file_exists($picPath . $fileName . "." . $format)) {
        //obrázek neexistuje, pokud se jedná o webp zkusím vygenerovat ze src obrázku
        $srcFile = $fileName . ".jpg";

        //zjistím jestli existuje adresář s velikostí
        if (!is_dir($picPath)) {
          if (!mkdir($picPath) && !is_dir($picPath)) {
            return FALSE;
          }
        }

        if (file_exists($srcPath . '/' . $srcFile)) {
          $srcFile = $srcPath . $srcFile;
        } else {
          if (!empty($srcPath2) && file_exists($srcPath2 . '/' . $srcFile)) {
            $srcFile = $srcPath2 . $srcFile;
          } else {
            $srcFile = $this->wwwDir . '/pic/no.jpg';
          }
        }

        if (!empty($srcFile)) {

          $w = '';
          $h = '';

          if (str_contains($size, 'x')) {
            list($w, $h) = explode('x', $size);
          }

          if (empty($w) || empty($h)) {
            return FALSE;
          }

          if ($format === "webp") {
            $outputFormat = Nette\Utils\Image::WEBP;
          } else {
            $outputFormat = Nette\Utils\Image::JPEG;
          }

          //obrazek vygeneruju
          try {
            $pic = Nette\Utils\Image::fromFile($srcFile);
            $pic->resize($w, $h, Nette\Utils\Image::EXACT); // resize, co přesahuje oříznu
            $pic->save($picPath . $fileName . "." . $format, 92, $outputFormat);
          } catch (Exception $e) {
            $returnEmptyPicture = TRUE;
          }
        }

        if (!file_exists($picPath . $fileName . "." . $format)) {
          $returnEmptyPicture = TRUE;
        }
      }

      if ($returnEmptyPicture) {
        $fileName = "pic/no." . $format;
        if ($returnEmpty) $fileName = "";
        return($fileName);
      }

      $fileName = "pic/$type/$size/" . rawurlencode($fileName . "." . $format);
      return($fileName);
    }

  /**
   * @param $product array
   * @param $delfreelimit double
   * @param $user array
   * @return bool
   */
    public function isDelFree($product, $delfreelimit, $user) {
      if ($user->usrprccat === 'a' || $user->usrprccat === 'b') {
        if ((int)$product->prodelfree === 1) {
          return TRUE;
        }
      }
      return (int)$product->proprice > $delfreelimit;
    }

    /**
    * vraci nazev obrazku novinky
    *
    * @param string $size [list,detail,big]
    * @param dibirow $row
    *
    * @return string cesta k obrazku
    */
    public function getNewPicName($row, $size) {
      $path = "pic/new/$size/";
      $picPath = $this->wwwDir."/".$path;
      $fileName = 'new_'.$row->newid.'.jpg';

      if (!file_exists($picPath.$fileName)) {
        $fileName = "no.jpg";
        $path = "pic/";
      }

      $fileName = rawurlencode($fileName);
      return($path.$fileName);
    }
    
    /**
    * vraci nazev obrazku clanku
    *
    * @param string $size [sirkaxvyska]
    * @param dibirow $row
    *
    * @return string cesta k obrazku
    */
    public function getArtPicName($row, $size) {
      $fileName = "art_" . $row->artid;
      return $this->getPicFileName("art", $fileName, $size);
    }

    /**
    * vraci nazev webp obrazku clanku
    *
    * @param string $size [sirkaxvyska]
    * @param dibirow $row
    *
    * @return string cesta k obrazku
    */
    public function getArtPicNameWebp($row, $size) {
      $fileName = "art_" . $row->artid;
      return $this->getPicFileName("art", $fileName, $size, FALSE, "webp");
    }

  /**
   * vraci nazev obrazku katalogu jpg
   *
   * @param  $row
   * @param $size
   * @return string cesta k obrazku
   */
    public function getCatalogPicName($row, $size) {
      $fileName = $row->catid;
      return $this->getPicFileName("catalog", $fileName, $size);
    }

  /**
   * vraci nazev obrazku katalogu - webp
   *
   * @param  $row
   * @param $size
   * @return string cesta k obrazku
   */
    public function getCatalogPicNameWebp($row, $size) {
      $fileName = $row->catid;
      return $this->getPicFileName("catalog", $fileName, $size, FALSE, "webp");
    }

    public function getCatPath($text, $path="") {
      $key = Nette\Utils\Strings::webalize($text);
      if (!empty($path)) {
        $arr = explode("/", trim($path, "/"));
        foreach ($arr as $i => $item) {
          if (($key == "nejdrazsi" && $item == 'nejlevnejsi') || $key == "nejlevnejsi" && $item == 'nejdrazsi' || $key == "" && $item == 'nejdrazsi' || $key == "" && $item == 'nejlevnejsi') {
            unset($arr[$i]);
          }
        }
        $path = (count($arr) > 0 ? implode("/", $arr) : "");
      }
      return (!empty($path) ? $path . (!empty($key) ? "/".$key : "") : $key);
    }

    public function removeCatPath($text, $path) {
      $key = Nette\Utils\Strings::webalize($text);
      if (!empty($path)) {
        $arr = explode("/", trim($path, "/"));
        foreach ($arr as $i => $item) {
          if ($key == $item) {
            unset($arr[$i]);
            $path = implode("/", $arr);
            break;
          }
        }
      }

      return ($path);
    }
    
    /**
    * vraci URL klic zbozi
    *
    * @param dibirow $row
    * @return URL klic
    */
    public function getProKey($row) {
      return((!empty($row->prokey) ? $row->prokey : Nette\Utils\Strings::webalize($row->proname)));
    }
    
    /**
    * vraci URL klic katalogu
    *
    * @param dibirow $row
    * @return URL klic
    */
    public function getCatKey($row) {
      return((!empty($row->catkey) ? $row->catkey : Nette\Utils\Strings::webalize($row->catname)));
    }
    
    public function getProNameCatalog($row) {
      $name2 = $row->proname2;
      $arr = explode(' - ', $name2);
      if (!empty($arr[0])) $name2 = $arr[0];
      $name2 = trim($name2);
      return($row->proname1.' '.$name2); 
    }

    public function getProNameParts($row) {
      $name = $row->proname;
      $arr = explode(' - ', $name);
      if (!empty($arr[0]) && !empty($arr[1])) {
        return $arr;
      } else {
        return array($name, "");
      }
    }

    public function getDiscountInPer($pricecom, $price) {
      $disc = $this->getDiscount($pricecom, $price);
      if ($disc > 0) {
        return ($disc."%");
      } else {
        return "";
      }
    }

    public function getDiscount($pricecom, $price) {
      if ((int)$pricecom > $price) {
        return ((int)round(($pricecom - $price) / $pricecom * 100));
      } else {
        return 0;
      }
    }

    public function getUrlKey($key, $name) {
      return((!empty($key) ? $key : Nette\Utils\Strings::webalize($name)));
    }
    
    /**
    * vraci naformatovanou cenu
    * 
    * @param mixed $price
    * @param mixed $decimals
    */
    public function formatPrice($price, $curId=1, $decimals=Null) {
      //if ((double)$price == 0) return "na dotaz";
      $price = (double)$price;
      $priceCurrency = $this->config["currency"][$curId]["code"];
      if ($decimals == Null) {
        $priceDecimals = $this->config["currency"][$curId]["decimals"];
        $decimals = $priceDecimals;
      }
      $formated = str_replace(" ", "\xc2\xa0", number_format($price, $decimals, ",", " "));
      if ($decimals == 1) $formated .= '0';
      return $formated." ".$priceCurrency;
    }
    
    public function formatNumber($number, $decimals) {
      //if ((double)$price == 0) return "na dotaz";
      $number = (double)$number;
      $formated = str_replace(" ", "\xc2\xa0", number_format($number, $decimals, ",", " "));
      return $formated;
    }
    
    /**
    * vraci naformatovanou cenu
    * 
    * @param mixed $price
    * @param mixed $decimals
    */
    public function formatPriceByCurId($price, $curId) {
      //if ((double)$price == 0) return "na dotaz";
      $price = (double)$price;
      $priceCurrency = $this->config["currency"][$curId]["code"];
      $priceDecimals = $this->config["currency"][$curId]["decimals"];
      $formated = str_replace(" ", "\xc2\xa0", number_format($price, $priceDecimals, ",", " "));
      if ($priceDecimals == 1) $formated .= '0';
      return $formated." ".$priceCurrency;
    }
    
    public function commentStripTags($text, $isAdmin=FALSE) {
      $text = nl2br($text);
      if (!$isAdmin) {
        $text = strip_tags($text, '<br>');  
      }
      return $text;
    }

  /**
   * vraci jednotky podle poctu kusu
   *
   * @param $row
   * @param string $fieldSufix
   * @return string
   * @internal param int $qty pocet
   * @internal param string $unitName nazev jednotky
   */
  public static function getQtyText($row, $fieldSufix = "") {
    $qty = (int)$row["proqty".(!empty($fieldSufix) ? "_".$fieldSufix : "")];
    if ($qty > 5 && $row->proisexp == 0) {
      $arr = array(100, 50, 10, 5);
      foreach ($arr as $level) {
        if ($qty > $level) {
          return ">" . $level . "ks";
        }
      }
    } else if ($qty <= 5 && $row->proisexp == 0) {
      return "";
    } else {
      return $qty."ks";
    }
  }

  /**
   * vraci jednotky podle poctu kusu
   *
   * @param $row
   * @param string $fieldSufix
   * @return string
   * @internal param int $qty pocet
   * @internal param string $unitName nazev jednotky
   */
  public static function getQtyTextDetail($row, $fieldSufix = "") {
    $qty = (int)$row["proqty".(!empty($fieldSufix) ? "_".$fieldSufix : "")];
    if ($qty > 5 && $row->proisexp == 0) {
      $arr = array(100, 50, 10, 5);
      foreach ($arr as $level) {
        if ($qty > $level) {
          return ">" . $level . "ks";
        }
      }
    } else if ($qty <= 5 && $row->proisexp == 0 && $fieldSufix != 'shop1') {
      return "";
    } else {
      return $qty."ks";
    }
  }


  /**
   * @param $ean
   * @return string
   */
  public static function checkEan($ean) {
    return trim($ean);
  }

  /**
   * @param $cnt
   * @return string
   */
  public static function getenumText($cnt) {
    $str = "dalších produktů";
    switch ($cnt) {
      case 1:
        $str = "další produkt";
        break;
      case 2:
      case 3:
      case 4:
        $str = "další produkty";
        break;
    }
    return $cnt.' '.$str;
  }

  /**
   * vraci string v kodovani win-1250
   *
   * @param string $str
   * @return string
   */
  public static function iconv1250($str) {
    $str = iconv('utf-8', 'WINDOWS-1250', $str);
    return $str;
  }

  public static function getPicTimeStamp($fileName) {
    $fileFullPath = WWW_DIR . "/pic/product/detail/" . $fileName;
    if (file_exists($fileFullPath)) {
      $timestamp = @filemtime($fileFullPath);

      if (!empty($timestamp)) {
        return $timestamp;
      }
    }

    $date = new DateTime();
    return $date->format("W");
  }

  /**
   * vrací TRUE/FALSE zda je možné produkt zakoupit
   * @param $product
   * @return bool
   */
  public static function canBuy($product): bool {
    return ($product->proaccess < 100 && $product->prostatus == 0);
  }

  /**
   * vrací TRUE/FALSE zda je zboží v akci
   * @param $product
   * @return bool
   */
  public static function isPromo($product): bool {
    return ($product->protypid == 1 || $product->protypid4 == 1);
  }

  public static function nl2br($str) {
    return nl2br($str);
  }

  /**
   * odstraní nevhodné HTML formátování
   *
   * @param $text
   * @return string
   */
  public static function reformatHtmlText($text): string {
    $text = preg_replace('/<div(.*?)>(.*?)<\/div>/i', '<p>$2</p>', $text);
    $text = str_replace('<strong>&nbsp;</strong>', '', $text);
    $text = str_replace('<p></p>', '', $text);
    $text = str_replace('<div></div>', '', $text);
    $text = str_replace('<div>&nbsp;</div>', '', $text);
    $text = str_replace('<p>&nbsp;</p>', '', $text);

    // Regular expression to find <a> tags without class="keep_it"
    $pattern = '/<a(?![^>]*\bclass="keep_it\b")[^>]*>(.*?)<\/a>/is';

    // Perform the replacement
    $clean_text = preg_replace_callback($pattern, function ($matches) {
        return $matches[1];
    }, $text);

    $text = $clean_text;



    //$text = str_replace('<p>&nbsp;</p>', '', $text);

    $text = strip_tags($text, [ "a", "div", "p", "ul", "ol", "li", "table", "tr", "td", "th", "strong", "i", "br", "dl", "dt", "dd", "img", "iframe"]);

    return $text;
  }

  public static function getProNameAgregators($product): string {

    $proname = "";
    $arr = explode('-', $product->proname2);
    $vol = "";
    if (!empty($arr[0]) && !empty($arr[1])) {
      $vol = ' '.trim($arr[0]);
    }
    if (!empty($product->pronames)) {
      $proname = $product->manname.' '.$product->pronames;
    } else {
      if ($product->promasid > 0) {
        $proname = $product->manname.' '.$product->proname.' '.$product->proname2;
      } else {
        $proname = $product->manname.' '.$product->proname.$vol;
      }
    }
    return $proname;

  }

  public function getCatPathAgregator($catalog, $productPaths, $agregatorCatalogs) {
    if (!empty($catalog->catid)) {
      $catPathIds = explode('|', trim($catalog->catpathids, '|'));
      $catPathIds = array_reverse($catPathIds); //zacnu prohledavat od konce vetve
      If (!isset($productPaths[$catalog->catid])) {
        foreach ($catPathIds as $catid) {
          //prvni kategorii co ma cestu vezmu pro danou kategorii a vypadnu
          if (isset($agregatorCatalogs[$catid])) {
            $productPaths[$catalog->catid] = $agregatorCatalogs[$catid]->catpathagregator;
            break;
          }
        }
      }
    }
    return $productPaths;
  }

  public function getParamsHeureka($catalog, $heurekaCatalogParams) {
    if (!empty($catalog["catid"])) {
      $catPathIds = explode('|', trim($catalog->catpathids, '|'));
      $catPathIds = array_reverse($catPathIds); //zacnu prohledavat od konce vetve
      if (!isset($heurekaCatalogParams[$catalog->catid])) {
        foreach ($catPathIds as $catid) {
          //prvni kategorii co ma heureka parametry vezmu pro danou kategorii a vypadnu
          if (isset($heurekaCatalogParams[$catid])) {
            $heurekaCatalogParams[$catalog->catid] = $heurekaCatalogParams[$catid];
            break;
          }
        }
      }
    }
    return $heurekaCatalogParams;
  }

}