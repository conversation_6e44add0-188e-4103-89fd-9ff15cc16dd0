<?php
class MyValidators {
	// testuje, zda psč má správný formát
	public static function validatePostCodeFormat(\Nette\Forms\Controls\BaseControl $input, $arg): bool {
    $postCode = $input->getValue();

    if (empty($postCode)) {
      return true;
    }

    $postCode = trim(str_replace(" ", "", $postCode));
    if (preg_match('#[^0-9]#',$postCode)) {
      return false;
    }

    if (strlen($postCode) > 5) {
      return false;
    }

    return true;
	}

  protected function checkPostCode(\Nette\Forms\Controls\BaseControl $input, $arg): bool {
    $postCode = $input->getValue();

    if (empty($postCode)) {
      return true;
    }

    //kontrola s databází
    $cnt = (int)dibi::fetchSingle("SELECT count(*) FROM czechpostcodes WHERE cpccode=%s", $postCode);
    if ($cnt === 0) {
      return false;
    }

    return true;
  }
}