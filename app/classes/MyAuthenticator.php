<?php
class MyAuthenticator implements Nette\Security\IAuthenticator {
  use Nette\SmartObject;

  const NS_ADMIN = 'admin';
  const NS_USER  = 'user';

  public function authenticate(array $credentials) {
    list($username, $password, $namespace) = $credentials;
    if (empty($namespace)) throw new Nette\Security\AuthenticationException("Špatné volání authentizace.", self::IDENTITY_NOT_FOUND);

    switch ($namespace) {
       case self::NS_USER:
         $sql = "SELECT usrid AS id, usrmail AS mail, usrpassw AS password, usrstatus AS status, NULL AS role FROM users WHERE usrmail=%s";
         break;
       case self::NS_ADMIN:
         $sql = "SELECT admid AS id, admmail AS mail, admpassw AS password, admstatus AS status, admrole as role FROM admins WHERE admmail=%s";
         break;
    }
    $row = dibi::fetch($sql, $username);

    if (!$row) { // uživatel nenalezen?
        throw new Nette\Security\AuthenticationException("Uživatel nenalezen.", self::IDENTITY_NOT_FOUND);
    }

    if ($row->password !== md5($password)) { // hesla se neshodují?
        throw new Nette\Security\AuthenticationException("Špatné heslo.", self::INVALID_CREDENTIAL);
    }

    if ($row->status > 0) { // ucet je blokovany
      throw new Nette\Security\AuthenticationException("Účet je blokovaný. Kontaktujte správce.", self::NOT_APPROVED);
    }

    unset($row->password);

    return new Nette\Security\Identity($row->id, $row->role); // vrátíme identitu
  }

}
