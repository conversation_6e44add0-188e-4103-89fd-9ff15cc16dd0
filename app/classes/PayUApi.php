<?php
/**
* PayU API
* https://github.com/PayU-EMEA/openpayu_php
 *
 * http://developers.payu.com/en/overview.html
 * http://developers.payu.com/en/restapi.html
 *
* <AUTHOR>
*/
class PayUException extends \Exception { }

//require_once realpath(__DIR__) . '/../../vendor/openpayu/openpayu_php_sdk/lib/openpayu.php';

class PayUApi {

  public $errMsg = array();

  private $config;

    /**
  * @var ModelFactory
  */
  public ModelFactory $model;

  public function __construct($config, $curKey, $model) {

    if (!isset($config[$curKey])) {
      throw new PayUException("Měna " . $curKey . " není nastavena");
    }

    $this->config = $config[$curKey];

    $this->model = $model;

    OpenPayU_Configuration::setEnvironment($this->config["enviroment"]);
    OpenPayU_Configuration::setOauthGrantType(OauthGrantType::CLIENT_CREDENTIAL);

    //set POS ID and Second MD5 Key (from merchant admin panel)
    OpenPayU_Configuration::setMerchantPosId($this->config["posId"]);
    OpenPayU_Configuration::setSignatureKey($this->config["secondKey"]);

    //set Oauth Client Id and Oauth Client Secret (from merchant admin panel)
    OpenPayU_Configuration::setOauthClientId($this->config["clientId"]);
    OpenPayU_Configuration::setOauthClientSecret($this->config["clientSecret"]);
  }

  public function create($ordId, $continueUrl) {
    $ords = $this->model->getOrdersModel();
    $ord = $ords->load($ordId);

    if ($ord === FALSE) {
      throw New PayUException("Objednávka nenalezena");
    }

    $order = array();

    $order['notifyUrl'] = $this->config["notifyUrl"];
    $order['continueUrl'] = $continueUrl;

    $order['customerIp'] = $_SERVER['REMOTE_ADDR'];
    $order['merchantPosId'] = OpenPayU_Configuration::getMerchantPosId();
    $order['description'] = 'Objednávka č.' . $ord->ordcode;
    $order['currencyCode'] = $this->config["curKey"];
    $order['totalAmount'] = (int)round($ord->ordpricevat * 100);
    //$order['extOrderId'] = $ord->ordcode;

    $order['buyer']['email'] = $ord->ordmail;
    //$order['buyer']['phone'] = $ord->ordtel;
    $order['buyer']['firstName'] = $ord->ordiname;
    $order['buyer']['lastName'] = $ord->ordilname;
    $order['buyer']['language'] = $this->config["language"];



    $ordItems = dibi::fetchAll("SELECT * FROM orditems WHERE oriordid=%i", $ord->ordid);
    $i = 0;
    foreach ($ordItems as $key => $row) {
      $order['products'][$i]['name'] = trim($row->oriname);
      $order['products'][$i]['unitPrice'] = (int)round($row->oriprice * 100);
      $order['products'][$i]['quantity'] = (int)$row->oriqty;
      $i++;
    }


    //\Tracy\Debugger::log(OpenPayU_Util::buildJsonFromArray($order));

    try {
      $response = OpenPayU_Order::create($order);
      $status_desc = OpenPayU_Util::statusDesc($response->getStatus());
      if ($response->getStatus() == 'SUCCESS') {
        $pauId = (string)$response->getResponse()->orderId;
        $logs = $this->model->getPayuLogsModel();
        $logs->startLog($pauId, $ord->ordid);
        $ords->update($ord->ordid, array('ordpauid' => $pauId));
      } else {
        throw new PayUException("Nastala chyba při pokusu o otevření platební brány. ($status_desc).");
      }
    } catch (OpenPayU_Exception $e) {
      //$text = print_r($e, true);
      //if (isset($response)) \Tracy\Debugger::log($response);

      throw new PayUException("Nastala chyba při pokusu o otevření platební brány. (" . $e->getMessage() . "|" . $e->getCode() . ")");
    }

    header('Location:'.$response->getResponse()->redirectUri);
    exit;
  }

  public function retrieveNotofication($data) {
    try {
      return OpenPayU_Order::consumeNotification($data);
    } catch (OpenPayU_Exception $e) {

      return FALSE;
    }
  }


  public function retrieve($posOrdId) {
    $msg = "";
    $status = "";
    try {
      $response = OpenPayU_Order::retrieve(stripslashes($posOrdId));
      $status_desc = OpenPayU_Util::statusDesc($response->getStatus());

      if ($response->getStatus() == 'SUCCESS') {
        $order = $response->getResponse()->orders[0];
        $msg = $this->getStatusDesc($order->status);
        $status = $order->status;
      } else {
        $msg = $status_desc;
        $status = "ERROR";
      }
    } catch (OpenPayU_Exception $e) {
      $status = "ERROR";
      $msg = $e->getMessage();
    }
    return array(
      'status' => $status,
      'msg' => $msg,
    );
  }

  private function getStatusDesc($status) {
    $arr = array(
      'NEW' => 'Nová platba',
      'PENDING' => 'Platba čeká na vyřízení',
      'CANCELED' => 'Platba zrušena',
      'COMPLETED' => 'Platba úspěšně dokončena',
      'WAITING_FOR_CONFIRMATION' => 'Platba čeká na potvrzení',
    );

    if (isset($arr[$status])) {
      return $arr[$status];
    }
    return '';
  }
}