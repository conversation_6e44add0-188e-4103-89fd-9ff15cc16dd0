<?php

namespace classes;

class FbConversionApi {

  const PIXEL_ID = '1754813784783260';
  const TOKEN = 'EAAINydqn85IBOzISQKiN5OoOKrKozfwQWMLDiRa6kscPtUPrX8dz3cdI6rUhxqm2b4ZAwyRize97K5cDvlyngIbCwI70mnQRxifgeTDl9A4l3wNnmnPupiZCIDAIUmox91D22BU2jE0tkezp0WIhlCw8Qtb5o6mOFKjZChUX8DfF2IkOdRg4O8BuDwqOxBWogZDZD';

  public function postData($eventName, $dataArray) {

    $fb_access_token = self::TOKEN;
    $fb_pixel_id = self::PIXEL_ID;
    $fb_test_event_code = '';

    $timestamp = time();
    $data = array();

    if (empty($eventName)) die(); // The event name required

    // IP address
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
      $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
      $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
      $ip = $_SERVER['REMOTE_ADDR'];
    }

    $ch = curl_init();

    $data['event_name'] = $eventName;
    $data['event_time'] = $timestamp;
    if ($dataArray['event_id']) {
      $data['event_id'] = &$dataArray['event_id'];
    }
    $data['event_source_url'] = &$dataArray['url'];
    $data['action_source'] = "website";
    if ($dataArray['event_data']) {
      $data['custom_data'] = &$dataArray['event_data'];
    }
    $data['user_data']['client_ip_address'] = $ip; // ip address
    $data['user_data']['client_user_agent'] = $_SERVER['HTTP_USER_AGENT'];
    $data['user_data']['fbp'] = &$dataArray['fbp'];
    $data['user_data']['fbc'] = &$dataArray['fbclid'];
    if ($dataArray['em']) {
      $data['user_data']['em'] = &$dataArray['em']; // email
    }

    $fields = "{'data':'[".json_encode($data)."]'";
    if ($fb_test_event_code) $fields .= ",'test_event_code':'".$fb_test_event_code."'";
    $fields .= "}";

    curl_setopt_array($ch, array(
      CURLOPT_URL => 'https://graph.facebook.com/v10.0/'.$fb_pixel_id.'/events?access_token='.$fb_access_token,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => '',
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 0,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => 'POST',
      CURLOPT_POSTFIELDS => $fields,
      CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json'
      ),
    ));

    $result = curl_exec($ch);
    curl_close ($ch);
  }
}