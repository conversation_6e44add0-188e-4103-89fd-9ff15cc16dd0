<?php

/**
 * https://mpapi-docs.mallgroup.com/
 * https://github.com/mallgroup/mpapi-client-php
 */

use MpApiClient\Article\DTO\BatchAvailability;
use MpApiClient\Article\DTO\BatchAvailabilityIterator;
use MpApiClient\Article\DTO\ProductRequest;
use MpApiClient\Article\DTO\VariantRequest;
use MpApiClient\Article\DTO\VariantRequestIterator;
use MpApiClient\Article\Entity\Common\Availability;
use MpApiClient\Article\Entity\Common\Label;
use MpApiClient\Article\Entity\Common\LabelIterator;
use MpApiClient\Article\Entity\Common\Media;
use MpApiClient\Article\Entity\Common\MediaIterator;
use MpApiClient\Article\Entity\Common\Parameter;
use MpApiClient\Article\Entity\Product;
use MpApiClient\Article\Entity\Variant;
use MpApiClient\Common\Authenticators\ClientIdAuthenticator;
use MpApiClient\Exception\MpApiException;
use MpApiClient\Filter\Filter;
use MpApiClient\Filter\FilterItem;
use MpApiClient\Filter\FilterOperatorEnum;
use MpApiClient\MpApiClient;
use MpApiClient\MpApiClientOptions;
use MpApiClient\Order\DTO\StatusRequest;
use MpApiClient\Order\Entity\Order;
use MpApiClient\Order\Entity\StatusEnum;

class MallApi {

  /** @var string API ID */
  const API_ID = '11991bc47da109c5a969b039a5f545d7f40ec0f0dbf2154afb29af404561d3d6'; //produkční

  /** @var string HTTP adresa webu pro obrázky */
  const HTTP = 'https://www.goldfitness.cz/';

  /** @var string nastavení ceny */
  const PRICE_CAT = 'a';

  /** @var int nastavení měny */
  const CUR_ID = 1;

  /** @var rezerva kusy */
  const QTY_BACKUP = 1;

  const ORDER_STATUS_UNCONFIRMED = 'unconfirmed';
  const ORDER_STATUS_OPEN = 'open';
  const ORDER_STATUS_SHIPPED = 'shipped';
  const ORDER_STATUS_DELIVERED = 'delivered';
  const ORDER_STATUS_RETURNED = 'returned';
  const ORDER_STATUS_CANCELED = 'canceled';

  /** @var MpApiClient */
  private $mpApiClient;

  /** @var Product */
  private $productRequest;

  /** @var Variant */
  private $variantRequest;

    /** @var Order */
  private $orderSynchronizer;

  /** @var double */
  private $delFreeLimit;

  /** @var array */
  private $appConfig;

  /** @var array */
  private $catIds;

  /** @var array */
  private $catVariableParameters;

  /** @var string */
  private $catFilter;

  /** @var string */
  private $productFreshFilter;

  /** @var array */
  public $errors;

  /** @var array */
  public $logs;

  /** @var array */
  private $catalogData;

  /** @var array */
  private $parFlavourValues;

  /**
  * @var ModelFactory
  */
  public ModelFactory $model;

  private $allProductsIds;

  public function __construct($appConfig, $model) {
    $this->appConfig = $appConfig;

    $this->model = $model;

    require __DIR__ . '/../../vendor/autoload.php';

    $options = new MpApiClientOptions(new ClientIdAuthenticator(self::API_ID));

    // 2. Initialize MP API Client
    $this->mpApiClient = MpApiClient::createFromOptions('goldfitness.cz', $options);

    //limit pro dopravu zdarma
    //$dels = $this->model->getDeliveryModesModel();
    //$dels->setCurrency($this->appConfig["neonParameters"]["currency"], self::CUR_ID);
    //$this->delFreeLimit = $dels->getDelFreeLimit(self::PRICE_CAT);

    //www dir
    $this->wwwDir = __DIR__ . "/../../www/";

    $this->catIds = array(
      84 => 'NA161',
      57 => 'NA161',
      85 => 'NA036',
      13 => 'NM071'
    );

    $this->catVariableParameters = array(
      'NA161' => 'FLAVOR',
      'NA036' => 'SIZE',
      'NM071' => 'SIZE'
    );

    $this->catFilter = "(catpathids LIKE '|84|%' or catpathids LIKE '|57|%' or catpathids LIKE '|85|%') and ";

    $this->productFreshFilter = " AND (NOW()>promalldateu + INTERVAL 48 HOUR OR promalldateu IS NULL) ";
    $this->productFreshFilter = " ";
  }

  private function getMallCatParams($mallCatId) {
    $arr['NA161'] = array(
      "GAINER"=>"gainer",
      "PROTEINS"=>"proteiny",
      "BCAA"=>"BCAA",
      "COMPLEX AMINO ACIDS"=>"komplexní aminokyseliny",
      "MULTI-COMPONENT PROTEIN"=>"vícesložkový protein",
      "PLANT PROTEIN"=>"rostlinný protein",
      "PROTEIN DRINK"=>"proteinový drink",
      "LIQUID AMINO ACIDS"=>"tekuté aminokyseliny",
      "GLUTAMIN"=>"glutamin",
      "ARGININ"=>"arginine",
      "CREATINE"=>"kreatin",
      "KRE-ALKALYN"=>"kre-alkalyn",
      "KREA-GENIC"=>"krea-genic",
      "CREATINE MIX"=>"kreatinová směs",
      "ANABOLIZÉRY"=>"anabolizéry",
      "NITRIX OXIDE"=>"nitrix oxide",
      "TESTOSTERONE BOOST"=>"pro zvýšení testosteronu",
      "OTHER ANABOLIZERS"=>"ostatní anabolizér",
      "L-CARNITIN"=>"L-Carnitin",
      "SYNEPHRINE"=>"synephrine",
      "DIET-FATBURNER"=>"spal. do diety a náhr. stravy",
      "COMPLEX FATBURNER"=>"komplexní spalovač",
      "GEL STIMULANT"=>"stimulant ve formě gelu",
      "OTHER STIMULANTS"=>"ostatní stimulanty",
      "ION DRINKS"=>"iontové nápoje",
      "VITAMIN C"=>"vitamín C",
      "VITAMIN D"=>"vitamín D",
      "MULTIVITAMIN"=>"multivitamín",
      "MINERALS"=>"minerály",
      "ANTIOXIDANTS"=>"antioxidanty",
      "OMEGA 3"=>"omega 3 ",
      "OMEGA 6"=>"omega 6",
      "OMEGA 9"=>"omega 9",
      "JOINT NUTRITION"=>"kloubní výživa",
      "PROTEIN BAR"=>"proteinové tyčinka",
      "CARBO BAR"=>"sacharidová tyčinka",
      "GLUTEN-FREE BAR"=>"bezlepková tyčinka",
      "OTHER SUPPLEMENTS"=>"ostatní sportovní výživa",
      "OTHER AMINO"=>"ostatní aminokyseliny",
      "OTHER CREATINE"=>"ostatní formy kreatinu",
      "OTHER VITAMINS"=>"ostatní vitamíny",
      "TO 25"=>"do 25%",
      "UP 25"=>"nad 25%",
      "TO 70"=>"do 70%",
      "70 TO 80"=>"70% až 80%",
      "UP 80"=>"nad 80%",
      "BEFORE TRAINING"=>"před tréninkem",
      "AFTER TRAINING"=>"po tréninku",
      "DURING TRAININ"=> "při tréninku"
    );
    $arr['NA036'] = array();
    $arr['NM071'] = array(
      "TYPE_OF_ACTIVITY_AND"=> "fitness",
      "TYPE_OF_HANDWEAR"=> "GLOVES"
    );

    if (isset($arr[$mallCatId])) {
      return $arr[$mallCatId];
    }

    return array();
  }

  /**
   * @return Product
   */


  public function deleteProduct($pro): void {
    $products = $this->model->getProductsModel();
    if ($pro->promasid > 0) {
      $proMas = $products->load($pro->promasid);
      if ($proMas) {
        $this->mpApiClient->article()->deleteVariant($proMas->procode, $pro->procode);
      }
    } else if ((bool)$pro->proismaster){
      $varIds = $this->mpApiClient->article()->listProductVariants($pro->procode, NULL);
      foreach ($varIds as $varCode) {
        $this->mpApiClient->article()->deleteVariant($pro->procode, $varCode);
      }
      $this->mpApiClient->article()->deleteProduct($pro->procode);
    } else {
      $this->mpApiClient->article()->deleteProduct($pro->procode);
    }
  }

  public function deleteProductByCode($proCode): void {
    $this->mpApiClient->article()->deleteProduct($proCode);
  }

  public function checkActiveProduct($proCode): void {
    $pros = $this->model->getProductsModel();
    $pro = $pros->load($proCode, "code");
    if (!$pro) {
      $this->logError($proCode . " v eshopu neexistuje - produkt.");
      $this->mpApiClient->article()->deleteProduct($proCode);
    }
    $variants = $this->mpApiClient->article()->listProductVariants($proCode, NULL);
    foreach ($variants as $variant) {
      $proVar = $pros->load($variant->getId(), "code");
      if (!$proVar) {
        $this->logError($variant->getId() . " v eshopu neexistuje - varianta.");
        $this->mpApiClient->article()->deleteVariant($proCode, $variant->getId());
      }
    }
  }

  public function checkActiveProducts(): void {
    $mallApiproductIdList = $this->getAllProductsIds();
    $pros = dibi::query("SELECT procode, proismaster FROM products")->fetchAssoc("procode");
    foreach ($mallApiproductIdList as $id) {
      if (!isset($pros[$id])) {
        $this->checkActiveProduct($id);
      }
    }
  }

  public function getProduct($proCode): Product {
    return $this->mpApiClient->article()->getProduct($proCode);
  }

  private function getAllProducts(): array {
    for ($i = 0; $i <= 10; $i++) {
      $filter = new Filter();
      $filter->setLimit(1000);
      $filter->setOffset($i * 1000);

      $response = $this->getProductsByFilter($filter);
      $pages[$i] = $response;
      $paging = $response->getPaging();
      if ($paging->getSize() < 1000) {
        break;
      }
    }

    $items = [];
    foreach ($pages as $pageItems) {
      foreach ($pageItems as $item) {
        $items[$item->getId()] = $item;
      }
    }

    return $items;
  }

  public function getProductsByFilter($filter): \MpApiClient\Article\Entity\BasicProductList {
    return $this->mpApiClient->article()->listProducts($filter);
  }

  private function getAllProductsIds(): array {
    if (empty($this->allProductsIds)) {

      $items = $this->getAllProducts();

      $ids = [];
      foreach ($items as $item) {
        $ids[$item->getId()] = $item->getId();
      }

      $this->allProductsIds = $ids;
    }

    return $this->allProductsIds;
  }

  public function updateProductsAvailability($quick=0): bool {
    $pros = $this->model->getProductsModel();

    if ((int)$quick === 1) {
      $where = " promalldateu IS NOT NULL AND promalldateavu IS NULL ";
      $orderBy = " ORDER BY promalldateu ";
    } else {
      $where = " promalldateu IS NOT NULL ";
      $orderBy = " ORDER BY if(promalldateavu is null,0,1), promalldateavu ";
    }
    $rows = dibi::fetchAll($pros->getSqlMallCatalogList($this->catFilter, $where, " LIMIT 800 ", $orderBy));

    $batchAvailabilityIterator = new BatchAvailabilityIterator();

    foreach ($rows as $row) {

      dibi::query("UPDATE products set promalldateavu=NOW() WHERE proid=%s", $row->proid);

      $row->proqty = max($row->proqty - self::QTY_BACKUP, 0);

      //načtu master položku
      if ($row->promasid > 0) {
        $proMas = dibi::fetch("SELECT procode, promallblock FROM products WHERE proid=%i", $row->promasid);
        if ($proMas) {
          $row->promallblock = $proMas->promallblock;
        }
      }

      //je skladem ale je blokované pro mall -> není skladem
      If ($row->proqty > 0 && (int)$row->promallblock === 1) {
        $row->proqty = 0;
      }

      $status = $row->proqty > 0 ? \MpApiClient\Article\Entity\Common\StatusEnum::ACTIVE() : \MpApiClient\Article\Entity\Common\StatusEnum::INACTIVE();
      $batchAvailabilityIterator->add(new BatchAvailability($row->procode, $status, $row->proqty));
    }

    try {
      $this->mpApiClient->article()->updateBatchAvailability($batchAvailabilityIterator);
    } catch (MpApiException $e) {
      $this->logError($e->getMessage());
    }

		return true;
  }

  public function updateProductAvailability($product): bool {
    $product->proqty = max($product->proqty - self::QTY_BACKUP, 0);

    //načtu master položku
    if ($product->promasid > 0) {
      $proMas = dibi::fetch("SELECT procode, promallblock FROM products WHERE proid=%i", $product->promasid);
      if ($proMas) {
        $product->promallblock = $proMas->promallblock;
      }
    }

    //je skladem ale je blokované pro mall -> není skladem
    If ($product->proqty > 0 && $product->promallblock === 1) {
      $product->proqty = 0;
    }

    $status = $product->proqty > 0 ? \MpApiClient\Article\Entity\Common\StatusEnum::ACTIVE() : \MpApiClient\Article\Entity\Common\StatusEnum::INACTIVE();
    try {
      $this->mpApiClient->article()->updateBatchAvailability(new BatchAvailabilityIterator(new BatchAvailability($product->procode, $status, $product->proqty)));
      dibi::query("UPDATE products set promalldateavu=NOW() WHERE procode=%s", $product->procode);
    } catch (Exception $e) {
      $this->logError($product->procode .": ". $e->getMessage());
      return false;
    }
    return TRUE;
  }

  /**
   * aktualizuje produkty na základě sady záznamů
   *
   * @param $rows
   * @param bool $showData
   * @return bool
   * @throws MpApiException
   * @throws \Dibi\Exception
   */
  public function updateProductsFromRows($rows, bool $showData = FALSE): bool {
    $productsUpd = [];
    $productsBlocked = [];

    foreach ($rows as $row) {

      if ($row->prostatus > 0 || (int)$row->proqty === 0) {
        $productsBlocked[$row->procode] = $row->procode;
      }

      $p = $this->prepareProductData($row);
      if ($p !== FALSE) {
        $productsUpd[$p["data"]->getId()] = $p;
        if ($showData) {
          //echo print_r($p, true);
        }
      } else {
        dibi::query("UPDATE products SET promalldateu=NOW() WHERE proid=%s", $row->proid);
      }
    }

    $malIdForActivation = [];

    if (count($productsUpd) > 0) {
      //aktualizuji
      $productIds = $this->getAllProductsIds();
      foreach ($productsUpd as $malId => $product) {
        if (isset($productIds[$malId])) {
          $this->productUpdate($product);
        } else {
          if (isset($productsBlocked[$malId])) {
            //položka je blokovaná, nebudu jí ani zakládat
            continue;
          }
          $this->productCreate($product);
        }
        $malIdForActivation[] = $malId;
      }
    }

    $this->mpApiClient->article()->activateSelectedProducts(... $malIdForActivation);

    return true;
  }

  public function updateProducts($limit, $isCron=TRUE): bool {
    $pros = $this->model->getProductsModel();
    $pros->setPrcCat('a');
    $curs = [];
    $curs[1] = [
      'id' => 1,
      'key' => 'CZK',
      'code' => 'Kč',
      'decimals' => 0,
    ];
    $pros->setCurrency($curs, 1);

    //kategorie které budu importovat do mall
    $where = " prostatus=0 AND (proismaster=1 OR (proismaster=0 AND COALESCE(promasid,0)=0))";
    $where .= " AND promallblock=0 AND " . MAIN_CATIDS;


    if ($isCron) {
      $this->productFreshFilter .= " AND proaccess=0 ";
      $where .= $this->productFreshFilter;
    }

    $sql = $pros->getSqlMallCatalogList($this->catFilter, $where, " LIMIT $limit ", " ORDER BY if(promalldateu is null,0,1), promalldateu, catid DESC ");
    $rows = dibi::fetchAll($sql);

    if (count($rows) > 0) {
      return $this->updateProductsFromRows($rows);
    }
    return false;
  }

  public function checkProducts() {

    $pros = $this->model->getProductsModel();

    $where = " proismaster=1 OR (proismaster=0 AND promasid=0)";

    $where .= " AND promallblock=0 AND prostatus=0 AND proaccess=0";

    $this->productFreshFilter = " AND prostatus=0 AND proaccess=0 ";

    $sql = $pros->getSqlMallCatalogList($this->catFilter, $where, "", " ORDER BY if(promalldateu is null,0,1), promalldateu, catid DESC ");
    $rows = dibi::fetchAll($sql);
    if (count($rows) > 0) {
      foreach ($rows as $row) {
        $p = $this->prepareProductData($row);
      }
    }
    return false;
  }

  public function updateProduct($proCode) {
    $pros = $this->model->getProductsModel();
    $pro = $pros->load($proCode, 'code');
    if ($pro->promasid > 0) {
      $pro = $pros->load($pro->promasid);
      $proCode = $pro->procode;
    }
    $rows = dibi::fetchAll($pros->getSqlMallCatalogList($this->catFilter, " procode='$proCode' "));
    if (count($rows) > 0) {
      return $this->updateProductsFromRows($rows, TRUE);
    }
    return false;
  }

  public function updateManufacturers() {
    $mans = $this->model->getManufacturersModel();

    //načtu výrobce z mall
    $brands = $this->mpApiClient->brand()->list();
    $brs = [];
    foreach ($brands as $brand) {
      $brs[\Nette\Utils\Strings::webalize($brand->getTitle())] = array(
        "id" => $brand->getBrandId(),
        'name' => $brand->getTitle()
      );
    }

    //načtu výrobce co nemají manmalid a pokusím se podle názvu jej najít na mall
    $rows = dibi::fetchAll("SELECT manid, manname FROM manufacturers WHERE manstatus=0 AND manmalid is null");

    foreach ($rows as $row) {
      $name = \Nette\Utils\Strings::webalize($row->manname);
      if (isset($brs[$name])) {
        $mans->update($row->manid, array('manmalid' => $brs[$name]["id"]));
      } else {
        $this->logError($row->manname . " - neexistuje v Mall");
      }
    }
  }

  private function logError($text) {
    Tracy\Debugger::log($text);
    echo $text."<br>";
    $this->errors[] = $text;
  }

  private function log($text) {
    Tracy\Debugger::log($text);
    echo $text."<br>";
    $this->logs[] = $text;
  }

  /**
   * aktualizace stávající položky
   *
   * @param $product
   * @return bool
   * @throws \Dibi\Exception
   */
  private function productUpdate($product): bool {
    $malId = $product["data"]->getId();
    try {
      $this->mpApiClient->article()->updateProduct($product["data"]);
      dibi::query("UPDATE products SET promalldateu=NOW() WHERE procode=%s", $malId);
      echo "$malId update OK<br>";
      return true;

    } catch (\MpApiClient\Exception\BadResponseException  $e) {
      \Tracy\Debugger::barDump($e->getResponse());
      foreach ($e->getErrorCodes() as $errorCode) {
        $this->logError($malId .": ". $errorCode->getMessage());
      }
      if ($e->getCode() === 400) {
        /*
        $forceToken = NULL;
        //zjistím jestli se aktivovala ochhrana ceny
        If (!empty($forceToken)) {
          $this->logError("Varování, ochrana ceny: ".$e->getMessage());
          $product["forceToken"] = $forceToken;
          $this->productUpdate($product);
        }
        */
      } else {
        if (method_exists($e, 'getData')) {
          $errors = $e->getData();
          $this->logError($malId .": ". $errors["response"]);
        } else {
          $this->logError($malId .": ". $e->getMessage());
        }
        dibi::query("UPDATE products SET promalldateu=NULL WHERE procode=%s", $malId);
        return false;
      }
    }
    return false;
  }


  /**
   * nová položka
   *
   * @param $product
   * @return bool
   * @throws \Dibi\Exception
   */
  private function productCreate($product): bool {
    $malId = $product["data"]->getId();
    try {
      $this->mpApiClient->article()->createProduct($product["data"]);
      dibi::query("UPDATE products SET promalldateu=NOW() WHERE procode=%s", $malId);
      echo "$malId create OK<br>";
      return TRUE;
    } catch (\Exception $e) {
      if (method_exists($e, 'getData')) {
        $errors = $e->getData();
        $this->logError($malId .": ". $errors["response"]);
      } else {
        $this->logError($malId .": ". $e->getMessage());
      }

      dibi::query("UPDATE products SET promalldateu=NOW() WHERE procode=%s", $malId);
      return false;
    }
  }

  private function prepareProductData($product, $proMas=NULL): bool|array {

    $isVariant = ($product->promasid > 0);

    $pros = $this->model->getProductsModel();
    $paramSizes = $pros->getEnumMallParSizes();

    $priority = 1000 - $product->proorder;
    $priority = max($priority, 0);

    //pokud jde o varinatu načtu master položku a některá data naplním z master položky
    $proId = $product->proid;
    if ($product->promasid > 0) {
      $product->prodescs = $proMas->prodescs;
      $product->prodesc = $proMas->prodesc;
      $product->pronutrition = $proMas->pronutrition;
      $product->catpathids = $proMas->catpathids;
      $product->catmallparams = $proMas->catmallparams;
      $product->catid = $proMas->catid;
      $product->promalllabels = $proMas->promalllabels;
      $product->promallblock = $proMas->promallblock;
      $proId = $proMas->proid;
    }

    //\Tracy\Debugger::log('xx');
    //zjistim korenovou kategorii položky
    $catRootId = 0;
    $malCatId = '';
    $catPath = explode('|', trim($product->catpathids, '|'));
    if (!empty($catPath[0])) {
      $catRootId = (int)$catPath[0];
      If ($catRootId === 57) {
        //zjistim jestli není taky v kategorii 84
        $c = dibi::fetch("
          SELECT catid, catmallparams FROM catalogs
          INNER JOIN catplaces ON (capcatid=catid)
          WHERE catpathids LIKE '%|84|%' AND capproid=%i", $proId
        );
        if ($c) {
          $catRootId = 84;
          $product->catmallparams = $c->catmallparams;
          $product->catid = $c->catid;
        }
      } else if ($catRootId === 85) {
        //fitness doplňky - zjistím jestli to není rukavice catID 13
        $c = dibi::fetch("
          SELECT catid, catmallparams FROM catalogs
          INNER JOIN catplaces ON (capcatid=catid)
          WHERE catpathids LIKE '%|13|%' AND capproid=%i", $proId
        );
        if ($c) {
          $catRootId = 13;
          $product->catmallparams = $c->catmallparams;
          $product->catid = $c->catid;
        }
      }

      $malCatId = $this->catIds[$catRootId];
    }

    if (empty($catRootId) || empty($malCatId)) {
      $this->logError($product->procode . " - nepodařilo se zjistit zařazení do katalogu");
      return false;
    }

    //načtu parametry z katalogu
    $mallCatParams = $this->getMallCatParams($malCatId);
    if (!empty($product->catmallparams) && !isset($this->catalogData[(int)$product->catid])) {
      $this->catalogData[(int)$product->catid] = array();
      //analyzuji parametry mall
      $arr = explode("\n", trim($product->catmallparams, "\n"));
      foreach ($arr as $item) {
        $arri = explode("=", trim($item, "="));
        if (count($arri) == 2) {
          $arrii = explode(",", trim($arri[1], ','));
          foreach ($arrii as $pSysName) {
            $pSysName = trim($pSysName);
            if (isset($mallCatParams[$pSysName])) {
              $this->catalogData[(int)$product->catid][] = array(
                'id' => $arri[0],
                'value' => $mallCatParams[$pSysName],
              );
            }
          }
        }
      }
    }

    //nastavím parametry - zkontroluji zda jsou vyplněné povinné par.
    if ($malCatId === 'NA161') {
      $params = dibi::query("SELECT prpname,prpvalue FROM proparams WHERE prptypid=1 AND prpname NOT IN ('TYPE_NA161', 'BEFORE_ACTION' ,'DURING_ACTION', 'AFTER_ACTION', 'BODYBUILDING', 'ENDURANCE_SPORTS') AND prpproid=%i", $product->proid)->fetchPairs("prpname", "prpvalue");
    } else if ($malCatId === 'NA036' || $malCatId === 'NM071') {
      //pokud má parametr velikost nastavím
      $rows = dibi::query("SELECT prpname,prpvalue FROM proparams WHERE prptypid=0 AND prpproid=%i", $product->proid)->fetchPairs("prpname", "prpvalue");
      if (!empty($rows["Velikost"])) {
        $size = trim($rows["Velikost"]);
        if (!empty($paramSizes[$size])) {
          $params["SIZE"] = $size;
        }
      }
      //pokud je v kategorii rukavice nastavím rukavice, zbytek jiné
      if ($malCatId === 'NA036') {
        $params["FITNESS_HELP"] = 'jiné';
        if (!empty($catPath[2]) && $catPath[2] == 14) {
          $params["FITNESS_HELP"] = 'fitness rukavice';
        }
      } else if ($malCatId === 'NM071') {
        $params["TYPE_OF_ACTIVITY_AND"] = "fitness";
        $params["TYPE_OF_HANDWEAR"] = "GLOVES";
      }
    }

    if (mb_strlen($product->prodescs) > 300) {
      $arr = preg_split('/(?<=[.?!])\s+(?=[a-z])/i', $product->prodescs);
      $shortDesc = "";
      foreach ($arr as $text) {
        if ($shortDesc === "") {
          $shortDesc = $text . ". ";
        } else {
          $text = trim($text);
          $tmpStr = $shortDesc . ' ' . $text . '.';
          $tmpStr = trim($tmpStr);
          if (mb_strlen($tmpStr) < 300) {
            $shortDesc = $tmpStr;
          } else {
            break;
          }
        }
      }
    } else {
      $shortDesc = $product->prodescs;
    }

    if (mb_strlen($shortDesc) > 300) {
      $shortDesc = \Nette\Utils\Strings::truncate($shortDesc, 299, '.');
    }

    if ($shortDesc === '') {
      $this->logError("Produkt " . $product->procode . " se nepodařilo vytvořit krátký popis po celých větách nebo není vyplněný.");
      return FALSE;
    }

    //X ks rezerva
    $product->proqty = max($product->proqty - self::QTY_BACKUP, 0);

    //je skladem ale je blokované pro mall -> není skladem
    If ($product->proqty > 0 && $product->promallblock === 1) {
      $product->proqty = 0;
    }

    //nastavím popis
    $prodesc = strip_tags($product->prodesc, "<p><strong><ul><li><br>");



    //doplním parametry dávkování a balení
    $ps = dibi::fetchAll("select * from proparams where prpproid=%i", $proId, " and prpname in ('Dávkování', 'Balení') order by prpname DESC");
    if ($ps) {
      foreach ($ps as $pitem) {
        $prodesc .= '<p><strong>' . $pitem->prpname . ':</strong> ' . $pitem->prpvalue . '</p>';
      }
    }

    if (!empty($product->pronutrition)) {
      $prodesc .= strip_tags($product->pronutrition, "<p><strong><ul><li><br>");
    }

    $vat = $this->getVatInPer($product->provatid);

    if ($isVariant) {
      $p = new VariantRequest($product->procode, $this->clearText($product->proname), $this->clearText($shortDesc), $this->clearText($prodesc), $priority, $product->proprice, new MediaIterator(), new \MpApiClient\Article\Entity\Common\ParameterIterator());
    } else {
      $p = new ProductRequest($product->procode, $this->clearText($product->proname), $this->clearText($shortDesc), $this->clearText($prodesc), $malCatId, $vat, $priority);
    }

    $p->setAvailability($this->getAvailability($product->proqty));
    $p->setPrice($product->proprice);

    if (!$isVariant) {
      if (!empty($product->manmalid)) {
        $p->setBrandId($product->manmalid);
      }

      if ($product->proismaster == 1) {
        if (!empty($this->catVariableParameters[$malCatId])) {
          $p->setVariableParameters($this->catVariableParameters[$malCatId]);
        }
      }
    } else {
      if ($malCatId === 'NA161' && empty($params["FLAVOR"])) {
        //zkusim zjistit prichut
        $flav = $this->getFlavour($product->proid);
        if ($flav === FALSE) {
          $this->logError($product->procode . ": Nemá parametr příchuť");
          $p->setAvailability($this->getAvailability(0));
        } else {
          $params["FLAVOR"] = $flav;
        }
      }
    }

    $p->setDeliveryDelay(1);

    if (!empty($product->procode2)) {
      if ($this->validate_EAN13Barcode($product->procode2)) {
        $p->setBarcode($product->procode2);
      }
    }

    //nastavi parametry u zboží
    $apiParams = new \MpApiClient\Article\Entity\Common\ParameterIterator();
    foreach ($params as $key => $par) {
      if (!empty($par)) {
        $apiParam = Parameter::create($key, $par);
        $apiParams->add($apiParam);
      }
    }

    //nastavím parametry u kategorie
    if (isset($this->catalogData[(int)$product->catid]) && count($this->catalogData[(int)$product->catid]) > 0) {
      foreach ($this->catalogData[(int)$product->catid] as $item) {
        $apiParam = Parameter::create($item["id"], $item["value"]);
        $apiParams->add($apiParam);
      }
    }

    if ($apiParams->count() > 0) {
      $p->setParameters($apiParams);
    }

    $apiLabels = new LabelIterator();

    if (!empty($product->promalllabels)) {
      //parsuji
      $arr = explode("\n", trim($product->promalllabels));
      foreach ($arr as $row) {
        $label = explode(";", trim($row, ";"));
        if (count($label) === 3) {
          $label[1] = new DateTime ($this->formatDateMySQL($label[1]) . ' 00:00:00');
          $label[2] = new DateTime($this->formatDateMySQL($label[2]) . ' 23:59:59');

          $label = new Label($label[0], $label[1], $label[2]);
          $apiLabels->add($label);
        }
      }
    }

    //doprava zdarma
    $delFree = ($product->proprice >= 2000);
    if ($delFree) {
      $p->setFreeDelivery($delFree);
      $label = new Label('FDEL', new DateTime('2015-07-19 00:00:00'), new DateTime('2028-11-11 11:11:11'));
      $apiLabels->add($label);
    }

    if ($apiLabels->count() > 0) {
      $p->setLabels($apiLabels);
    }

    $apiMedia = new MediaIterator();

    //obrázky
    $path = "pic/product/mall/";
    if ($product->promasid == 0)  {
      $picName = !empty($product->propicname) ? $product->propicname : $product->procode;
      if (file_exists($this->wwwDir . $path . $picName . '.jpg')) {
        //doplnit timestamp poslední aktualizace obrázku
        $fileTime = filemtime($this->wwwDir . $path . $picName . '.jpg');

        $media = new Media(self::HTTP . $path . $picName . '.jpg?'.$fileTime, TRUE);
        $apiMedia->add($media);

      }
    } else  if ($product->promasid > 0) {
      $picName = !empty($proMas->propicname) ? $proMas->propicname : $proMas->procode;
      if (file_exists($this->wwwDir . $path . $picName . '.jpg')) {
        $fileTime = filemtime($this->wwwDir . $path . $picName . '.jpg');

        $media = new Media(self::HTTP . $path . $picName . '.jpg?'.$fileTime, TRUE);
        $apiMedia->add($media);

      }
    }

    if ($apiMedia->count() > 0) {
      for ($i = 1; $i <= 10; $i++) {
        $fileTime = filemtime($this->wwwDir . $path . $picName . '.jpg');
        if (file_exists($this->wwwDir . $path . $picName . '_' . $i . '.jpg')) {

          $media = new Media(self::HTTP . $path . $picName . '_' . $i . '.jpg?'.$fileTime, FALSE);
          $apiMedia->add($media);
        }
      }
      $p->setMedia($apiMedia);
    } else {
      $this->logError("Produkt " . $product->procode . " nemá obrázek (" . self::HTTP . $path . $picName . '.jpg' . ")");
      return FALSE;
    }

    $apiVariants = new VariantRequestIterator();
    if ($product->proismaster) {
      //doplním varianty
      $rows = dibi::fetchAll("
SELECT proid, promasid, proismaster, procode, procode2, proismaster, protypid, protypid2, protypid3, protypid4, protypid5, propromodateto, prokey, procode, proname AS proname1, concat(proname, ' ', proname2) AS proname, 
proname2, pronames, prodescs, prodesc, proaccess, proaccesstext, proqty, proprice1com AS propricecom, proisexp, promallblock,
propicname, propicnamevar, IF(proprice1a>0,proprice1a,proprice1a) AS proprice, progifts,
provatid, prodelfree, pronotdisc, prostatus, proorder, manmalid, manname, prorating
FROM products
LEFT JOIN manufacturers ON (promanid=manid)
WHERE prostatus=0 AND promasid=%i " . $this->productFreshFilter . " AND promallblock=0
GROUP BY proid", $product->proid);

      if (empty($rows)) {
        //master položka nemá žádnou varinatu k aktualizaci, neaktualizuji
        return FALSE;
      }

      foreach ($rows as $row) {
        $var = $this->prepareProductData($row, $product);
        if ($var !== FALSE) {
          $apiVariants->add($var["data"]);
        }
      }

      if ($apiVariants->count() > 0) {
        $p->setVariants($apiVariants);
      }
    }

    $data = [];
    $data["data"] = $p;
    $data["isVariant"] = $isVariant;
    $data["masCode"] = (!empty($proMas->procode) ? $proMas->procode : NULL);
    $data["forceToken"] = NULL;

    return $data;
  }

  /**
   *
   * vrací sazbu DPH v %
   *
   * @param $vatid integer
   * @return
   */
  private function getVatInPer(int $vatid): ?int {
    if (isset($this->appConfig["VATTYPE_".$vatid])) {
      return (int)$this->appConfig["VATTYPE_" . $vatid];
    }
    return NULL;
  }

  public function updateDeliveries() {
    // možná nebude vůbec třeba
    // Get partner deliveries
    $deliveries = new Deliveries($this->mpApiClient);
    $response = $deliveries->partner()->get();
    $delIds = array();
    if (isset($response['ids']) && is_array($response['ids'])) {
      $delIds = array_flip($response['ids']);
    }

    $dels = array();


    $delivery = new PartnerDelivery();
    $delivery->setCode('20');
    $delivery->setTitle('Uloženka');
    $delivery->setPrice(50);
    $delivery->setCodPrice(30);
    $delivery->setFreeLimit(2000);
    $delivery->setDeliveryDelay(2);
    $delivery->setAsPickupPoint(false);
    $delivery->setPriority(2);

    $dels[$delivery->getCode()] = $delivery;


    $delivery = new PartnerDelivery();
    $delivery->setCode('21');
    $delivery->setTitle('Uloženka');
    $delivery->setPrice(75);
    $delivery->setCodPrice(21);
    $delivery->setFreeLimit(2000);
    $delivery->setDeliveryDelay(2);
    $delivery->setAsPickupPoint(false);
    $delivery->setPriority(1);

    $dels[$delivery->getCode()] = $delivery;


    $delivery = new PartnerDelivery();
    $delivery->setCode('16');
    $delivery->setTitle('DPD');
    $delivery->setPrice(80);
    $delivery->setCodPrice(30);
    $delivery->setFreeLimit(2000);
    $delivery->setDeliveryDelay(2);
    $delivery->setAsPickupPoint(false);
    $delivery->setPriority(3);

    $dels[$delivery->getCode()] = $delivery;

    foreach ($dels as $code => $delivery) {
      try {
        if (isset($delIds[$code])) {
          $response = $deliveries->partner()->put($delivery);
        } else {
          $response = $deliveries->partner()->post($delivery);
        }
      } catch (Exception $e) {
        $this->logError($e->getMessage());
      }
    }
  }

  /**
   * @return array
   * @throws MpApiException
   */
  public function getOrders(): array {
    $importedOrdIds = array();
    $filter = new Filter();
    $filter->addFilterItem(FilterItem::create('status', StatusEnum::OPEN()->getValue(), FilterOperatorEnum::EQUAL()));
    $filter->addFilterItem(FilterItem::create('confirmed', FALSE, FilterOperatorEnum::EQUAL()));
    $filter->addSortColumn('id', Filter::DIRECTION_DESC);

    // list orders with custom filter (filter is optional)
    $orderList = $this->mpApiClient->orders()->list($filter);
    $data = $orderList->jsonSerialize();
    $this->logError("Načteno " . count($data["data"]) . " objednávek.");

    $cnt = 0;
    $cntItems = 0;

    foreach ($orderList as $mallOrder) {
      $cntItems ++;
      if ($cntItems > 50) {
        break;
      }
      try {
        $ret = $this->setNewOrder($mallOrder->getId());
        if ($ret === FALSE) {
          continue;
        }
        $importedOrdIds[] = $ret;
        $cnt++;
      } catch (Exception $e) {
        $this->logError($mallOrder->getId() . ":" . $e->getMessage());
      }
    }
    $this->logError("Přeneseno " . $cnt . " objednávek.");
    return $importedOrdIds;
  }

  public function checkBlockedToOpenStatus() {
    $ordersSynchronizer = new Orders($this->mpApiClient);
    $ords = $this->model->getOrdersModel();
    $arr = $ordersSynchronizer->get()->open();
    $ids = array_flip($arr);
    $cnt = 0;
    //blokované obj z MALL
    $rows = dibi::fetchAll("SELECT ordid, ordmalid FROM orders WHERE ordmalid IS NOT NULL AND ordstatus=0");
    foreach ($rows as $row) {
      if (isset($ids[$row->ordmalid])) {
        //blokovaná obj u nás, je na mall už otevřená
        //nastavím status otevřená
        $ords->update($row->ordid, array("ordstatus" => 1,));
        $ords->logStatus($row->ordid, 1);
        $this->setStatus($row->ordmalid, StatusEnum::OPEN()->getValue());
        $cnt++;
      }
    }
    $this->logError("Překlopeno blocked -> open " . $cnt . " objednávek.");
  }

  public function checkBlockedToCanceledStatus() {
    $ords = $this->model->getOrdersModel();

    $filter = new Filter();
    $filter->addFilterItem(FilterItem::create('status', StatusEnum::CANCELLED()->getValue(), FilterOperatorEnum::EQUAL()));
    $filter->addSortColumn('id', Filter::DIRECTION_DESC);

    // list orders with custom filter (filter is optional)
    $orderList = $this->mpApiClient->orders()->list($filter);
    $data = $orderList->jsonSerialize();
    $this->logError("Načteno " . count($data["data"]) . " objednávek.");

    $ids = [];

    foreach ($orderList as $mallOrder) {
      $ids[$mallOrder->getId()] = $mallOrder->getId();
    }

    $cnt = 0;
    $cntItems = 0;

    //blokované obj z MALL
    $rows = dibi::fetchAll("SELECT ordid, ordmalid FROM orders WHERE ordmalid IS NOT NULL AND ordstatus IN (0,1,6,8,3) ORDER BY ordid DESC");
    foreach ($rows as $row) {
      if (isset($ids[$row->ordmalid])) {
        //blokovaná obj u nás, je na mall už zrušená
        //nastavím status zrušená
        $ords->update($row->ordid, array("ordstatus" => 9));
        $ords->logStatus($row->ordid, 9);
        $cnt++;
        //break;
      }
    }
    $this->logError("Překlopeno MALL canceled " . $cnt . " objednávek.");
  }

  /**
   *
   * vrací objednávku z MALL
   *
   * @param $mallOrdId
   * @return Order|array
   * @throws MpApiException
   */
  public function getOrder($mallOrdId): Order|array {
    return $this->mpApiClient->orders()->get($mallOrdId);
  }

  public function setNewOrder($malOrdId) {
    $ords = $this->model->getOrdersModel();
    $oris = $this->model->getOrdItemsModel();

    $this->errors = array();

    //kontrola zda už neexistuje
    $cnt = (int)dibi::fetchSingle("SELECT COUNT(ordid) fROM orders WHERE ordmalid=%s", $malOrdId);
    if ($cnt > 0) {
      return FALSE;
    }

    $mallOrder = $this->getOrder($malOrdId);
    //vložím objednávku do eshopu
    $values = array();
    $values["ordmalid"] = $malOrdId;
    $customer = $mallOrder->getCustomer();

    $arr = explode(' ', $customer->getName(), 2);
    if (count($arr) === 2 ) {
      $values['ordiname'] = $arr[0];
      $values['ordilname'] = $arr[1];
    } else {
      $values['ordilname'] = $arr[0];
    }
    $values['ordifirname'] = $customer->getCompany();
    $values['ordistreet'] = $customer->getStreet();
    $values['ordicity'] = $customer->getCity();
    $values['ordipostcode'] = $customer->getZip();
    $values['ordtel'] = $customer->getPhone();
    $values['ordmail'] = $customer->getEmail();
    $curCode = $mallOrder->getCurrency();
    $values['ordcurid'] = $curCode === 'CZK' ? 1 : 2;

    $values['ordmalshipdate'] = $mallOrder->getShipDate();

    /*
    if ($customer->getCountry() === 'SK') {
      $values['ordicouid'] = 2;
    } else {
      $values['ordicouid'] = 1;
    }
    */
    $values['ordicouid'] = 1;


    //zjistim delid
    $deliveryMethod = $mallOrder->getDeliveryMethod();

    $delName = "MALL doprava";

    $cod = $mallOrder->getCod();

    //DPD
    if ($deliveryMethod == '16') {
      $delName = "DPD";
      if ((double)$cod > 0) {
        //dobirka
        $values['orddelid'] = 4;
        $delName = "DPD dobírka";
      } else {
        $values['orddelid'] = 5;
      }
    }

    if ($deliveryMethod == '20' || $deliveryMethod == '21') {
      $delName = "Uloženka";
      if (!empty($mallOrder->getDeliveryMethodId())) {
        $uloshortcut = (string)dibi::fetchSingle("SELECT uloshortcut FROM ulozenkapoints WHERE uloid2=%i", $mallOrder->getDeliveryMethodId());
        if (!empty($uloshortcut)) {
          $values['orddelspec'] = $uloshortcut;
        } else {
          $this->logError("BranchCodeNotFound:uloshortcut:".$uloshortcut . "|delivery_method_id:" . $mallOrder->getDeliveryMethodId());
        }
      }
      if ((double)$cod > 0) {
        //dobirka
        $delName = "Uloženka dobírka";
        $values['orddelid'] = 33;
      } else {
        $values['orddelid'] = 35;
      }
    }

    $values['ordstatus'] = 0;
    $values['ordpublished'] = 1; //neposílat do statistik

    try {
      $ordId = $ords->insert($values);
    } catch (Exception $e) {
      $this->logError($e->getMessage());
      return false;
    }

    //načtu sazby DPH a jejich ID
    $vats = [];
    for ($i = 1; $i <= 3; $i++) {
      if (isset($this->appConfig["VATTYPE_$i"])) {
        $vats[$this->appConfig["VATTYPE_$i"]] = $i;
      }
    }

    //polozky
    $items = $mallOrder->getItems();
    $itemsCnt = 0;
    $ordSum = 0;
    foreach ($items as $item) {
      $ordSum += (double)$item->getPrice() * (double)$item->getQuantity();

      //nactu položku z eshopu
      $pro = dibi::fetch("SELECT * FROM products WHERE procode=%s", $item->getId());
      if (!$pro) {
        $this->logError($malOrdId . "Produkt " . $item->getId() . " nenalezen ");
        continue;
      }
      $vals = array();

      $vatId = $pro->provatid;

      $vals["oriordid"] = $ordId;
      $vals["oriproid"] = $pro->proid;
      $vals["oritypid"] = 0;
      $vals["oriname"] = $pro->proname;
      $vals["oriprice"] = $item->getPrice();
      $vals["oriqty"] = $item->getQuantity();
      $vals["orivatid"] = $vatId;

      if ($oris->insert($vals)) {
        $itemsCnt++;
      }
    }

    //vložím dopravu
    $vals = array();
    $vals["oriordid"] = $ordId;
    $vals["oritypid"] = 1;
    $vals["oriname"] = $delName;
    $vals["oriprice"] = (double)$mallOrder->getDeliveryPrice() + (double)$mallOrder->getCodPrice();
    $vals["oripricemaster"] = $vals["oriprice"];
    $vals["oriqty"] = 1;
    $oris->insert($vals);

    $ordSum += $vals["oriprice"];

    //vlozim slevu pokud je
    $discount = (double)$mallOrder->getDiscount();
    if ($discount > 0) {
      //vložím slevu
      $vals = array();
      $vals["oriordid"] = $ordId;
      $vals["oritypid"] = 5; //sleva z MALL
      $vals["oriname"] = "Sleva kupón na " . $discount . " Kč";
      $vals["oriprice"] = $discount * -1;
      $vals["oriqty"] = 1;
      $oris->insert($vals);

      $ordSum -= $discount;
    }

    $ords->recalcOrder($ordId);

    //kontrola počtu položek
    $cnt = (int)dibi::fetchSingle("SELECT COUNT(oriid) FROM orditems WHERE oriordid=%i", $ordId, " AND oritypid=0");
    if ($cnt !== $itemsCnt) {
      $this->logError("Nesouhlasí počet položek");
    }

    $ord = $ords->load($ordId);

    if ((double)$ordSum !== (double)$ord->ordpricevat) {
      $this->logError("Nesouhlasí celková částka");
    }

    if ((string)$mallOrder->getPaymentType() === 'A' && (double)$cod != (double)$ordSum) {
      $this->logError("Nesouhlasí COD s celkovou částkou");
    }

    $ords->update($ordId, ["ordstatus" => 1]);
    $ords->logStatus($ordId, 1);

    //potvrdím obj
    $this->mpApiClient->orders()->confirmOrder($malOrdId);

    if (count($this->errors) > 0) {
      $log = implode("\n", $this->errors);
      $ords->update($ordId, array("ordmalllog"=>$log));
      mail("<EMAIL>", "goldfitness.cz - mall priorita", $log);
    }

    return $ordId;
  }

  /**
   * nastaví status obj.
   *
   * @param $ordId
   * @param $newStatus
   * @param bool $confirm
   * @param array $params
   */
  public function setStatus($ordId, $newStatus, bool $confirm = TRUE, array $params = array()): void {
    if ($newStatus === StatusEnum::DELIVERED()->getValue()) {
      $statusRequest = new StatusRequest(StatusEnum::DELIVERED());
      if (!empty($params["delivered_at"])) {
        $statusRequest->setDelivery(new DateTime($params["delivered_at"]), new DateTime($params["delivered_at"]));
      } else {
        throw new \RuntimeException("Datum dodání není vyplněno");
      }
    } else if ($newStatus === StatusEnum::SHIPPED()->getValue()) {
      $statusRequest = new StatusRequest(StatusEnum::SHIPPED());
      $parCode = !empty($params["ordparcode"]) ? $params["ordparcode"] : "";
      $trackingUrl = !empty($params["trackingurl"]) ? $params["trackingurl"] : "";
      $statusRequest->setTracking($parCode, $trackingUrl);
    } else {
      if (StatusEnum::SHIPPING == 'shipping') {
        $apiStatus = StatusEnum::SHIPPING();
      } else if (StatusEnum::SHIPPED == 'shipped') {
        $apiStatus = StatusEnum::SHIPPED();
      } else if (StatusEnum::CANCELLED == 'cancelled') {
        $apiStatus = StatusEnum::CANCELLED();
      } else if (StatusEnum::DELIVERED == 'delivered') {
        $apiStatus = StatusEnum::DELIVERED();
      } else if (StatusEnum::LOST == 'lost') {
        $apiStatus = StatusEnum::LOST();
      } else if (StatusEnum::RETURNED == 'returned') {
        $apiStatus = StatusEnum::RETURNED();
      }
      $statusRequest = new StatusRequest($apiStatus);
    }
    if ($confirm) {
      $statusRequest->setConfirmed(true);
    }
    $this->mpApiClient->orders()->setStatus($ordId, $statusRequest);
  }

  /**
   * nastaví status obj.
   *
   * @param $status
   * @return array
   */
  public function getOrdersByStatus($status) {
    $orders = new Orders($this->mpApiClient);
    switch ($status) {
      case self::ORDER_STATUS_DELIVERED:
        return $orders->get()->delivered();
        break;
      case self::ORDER_STATUS_RETURNED:
        return $orders->get()->returned();
        break;
      case self::ORDER_STATUS_CANCELED:
        return $orders->get()->cancelled();
        break;
      case self::ORDER_STATUS_OPEN:
        return $orders->get()->open();
        break;
      case self::ORDER_STATUS_SHIPPED:
        return $orders->get()->shipped();
        break;
    }
  }

  private function getFlavour($proId) {
    $prichut = (string)dibi::fetchSingle("SELECT prpvalue FROM proparams WHERE prpproid=%i", $proId, " AND prptypid=0 AND prpname='Příchuť'");
    if (empty($prichut)) {
      return false;
    }

    if (!isset($this->parFlavourValues)) {
      $pro = $this->model->getProductsModel();
      $this->parFlavourValues = $pro->getEnumMallParFlavorValues();
    }
    //je nastavena příchuť
    $flavor = strtolower(trim($prichut));
    if (isset($this->parFlavourValues[$flavor])) {
      //hodnota odpovídá MALLu
      return $this->parFlavourValues[$flavor];
    }
    return false;
  }

  private function clearText($text): array|string {
    $badchar=array('®', '™');
    //replace the unwanted chars
    return str_replace($badchar, '', $text);
  }

  /**
   * formátuje datum do SQL formátu
   *
   * @param string $date datum ve formátu dd.mm.rrr
   * @return string
   */
  private function formatDateMySQL($date) {
    $d = "";
    $m = "";
    $y = "";

    if ($date !== NULL && strpos($date, '.') > 0) {
      list($d, $m, $y) = explode('.', $date);
    } else {
      return($date);
    }

    $m = substr('0'.trim($m), -2);
    $d = substr('0'.trim($d), -2);

    return(trim($y).'-'.$m.'-'.$d);
  }

  private function validate_EAN13Barcode($barcode) {
    // check to see if barcode is 13 digits long
    if (!preg_match("/^[0-9]{13}$/", $barcode)) {
        return false;
    }

    $digits = $barcode;

    // 1. Add the values of the digits in the
    // even-numbered positions: 2, 4, 6, etc.
    $even_sum = $digits[1] + $digits[3] + $digits[5] +
                $digits[7] + $digits[9] + $digits[11];

    // 2. Multiply this result by 3.
    $even_sum_three = $even_sum * 3;

    // 3. Add the values of the digits in the
    // odd-numbered positions: 1, 3, 5, etc.
    $odd_sum = $digits[0] + $digits[2] + $digits[4] +
               $digits[6] + $digits[8] + $digits[10];

    // 4. Sum the results of steps 2 and 3.
    $total_sum = $even_sum_three + $odd_sum;

    // 5. The check character is the smallest number which,
    // when added to the result in step 4, produces a multiple of 10.
    $next_ten = (ceil($total_sum / 10)) * 10;
    $check_digit = $next_ten - $total_sum;

    // if the check digit and the last digit of the
    // barcode are OK return true;
    if ($check_digit == $digits[12]) {
      return true;
    }
    return false;
  }

  public function getCategories() {
    $str = file_get_contents(WWW_DIR . "/../data/mall_categories.json");
    $arr = json_decode($str, true);
    $data = [];

    if (!empty($arr["data"][0]["items"])) {
      foreach ($arr["data"][0]["items"] as $item) {

      }
    }

    return $data;
  }

  private function getAvailability($qty): Availability {
    if ($qty > 0) {
      return new Availability(\MpApiClient\Article\Entity\Common\StatusEnum::ACTIVE(), $qty);
    } else {
      return new Availability(\MpApiClient\Article\Entity\Common\StatusEnum::INACTIVE(), 0);
    }

  }

  public function getArticle() {
    return $this->mpApiClient->article();
  }
}
