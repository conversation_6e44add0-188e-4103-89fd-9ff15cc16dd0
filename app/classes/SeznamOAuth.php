<?php

class SeznamOAuth {

  const URL = 'https://login.szn.cz/api/v1/oauth/';

  private array $config;

  public function __construct($config) {
    $this->config = $config;
  }

  public function getOpenUrl() {
    $params = [
      "client_id" => $this->config["clientId"],
      "scope" => "identity",
      "response_type" => "code",
      "redirect_uri" => $this->config["redirectUri"],

    ];

    return self::URL . "auth?" . http_build_query($params);

  }

  public function getToken($code) {
    $data = [
      "grant_type" => "authorization_code",
      "code" => $code,
      "client_id" => $this->config["clientId"],
      "client_secret" => $this->config["clientSecret"],
      "redirect_uri" => $this->config["redirectUri"],
    ];

    $json = json_encode($data);

    $ch = curl_init(self::URL . "token");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response, true);


  }

  public function getUserData($code) {
    $data = $this->getToken($code);
    if (isset($data["error"])) {
      throw new Nette\Security\AuthenticationException($data["error"]);
    }

    if (isset($data["status"]) && $data["status"] != 200) {
      throw new Nette\Security\AuthenticationException($data["message"]);
    }

    return [
      "oauth_user_id" =>  $data["oauth_user_id"],
      "email" =>  $data["account_name"],
      "access_token" =>  $data["access_token"],
      "refresh_token" =>  $data["refresh_token"],
    ];
  }
}
