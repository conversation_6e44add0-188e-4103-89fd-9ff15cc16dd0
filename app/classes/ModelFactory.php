<?php

/*
 * rerplace

\= new \\Model\\(.*)Model\(\)\;
\= \$this->model->get('$1');

\= new (.*)Model\(\)\;
\= \$this->model->get('$1');


 * */

use Model\AdminsModel;
use Model\ArticlesModel;
use Model\AttachmentsModel;
use Model\BaseModel;

class ModelFactory {
  /* @var */
  public $storage;

  private $models = Array();

    /** ID aktualni meny; */
  private $curId = 1;

  /** zaokrouleni pocet des. mist aktualni meny */
  private $curDigits = 0;

  /** meny ktere eshop pouziva */
  private $currencies = array();

  private $prccat = 'a';

  public function __construct($storage) {
    $this->storage = $storage;
  }

  /**
   * nastavi aktualni menu
   * @param array $currencies
   * @param $curId
   */
  public function setCurrency($currencies, $curId) {
    $this->currencies = $currencies;
    $this->curId = (int)$curId;
    $this->curDigits = (int)$this->currencies[$this->curId]["decimals"];
  }

  /**
   * @param string $prccat
   */
  public function setPrcCat($prccat) {
    $this->prccat = $prccat;
  }

  /**
   * @param $name
   * @return mixed
   */
  private function getModel($name) {
    if (!isset($this->models[$name])) {
      $className = "\Model\\" . $name . 'Model';
      $this->models[$name] = new $className($this);
      $this->models[$name]->setCurrency($this->currencies, $this->curId);
      $this->models[$name]->setPrcCat($this->prccat);
    }

    return $this->models[$name];
  }

  /**
   * @param $name string
   * @param bool $onlyActive
   * @return array
   */
  public function getEnum($name, $onlyActive = TRUE) {
    if ($name === 'proaccess') {
      return $this->getModel('Products')->getEnumProAccess();
    }

    if ($name === 'protag1') {
      return $this->getModel('Products')->getEnumProTag1();
    }

    if ($name === 'protag2') {
      return $this->getModel('Products')->getEnumProTag2();
    }

    if ($name === 'protag3') {
      return $this->getModel('Products')->getEnumProTag3();
    }

    if ($name === 'proorigin') {
      return $this->getModel('Products')->getEnumProOrigin();
    }

    if ($name === 'manid') {
      return $this->getModel('Manufacturers')->getEnumManId();
    }

    if ($name === 'newtypid') {
      return $this->getModel('News')->getEnumNewTypId();
    }

    if ($name === 'newgrpid') {
      return $this->getModel('News')->getEnumNewGrpId();
    }

    if ($name ==='manid') {
      return $this->getModel('Manufacturers')->getEnumManId($onlyActive);
    }

    if ($name ==='ordstatus') {
      return $this->getModel('Orders')->getEnumOrdStatus();
    }

  }

  /**
   *
   * @return Model\AdminsModel
   */
  public function getAdminsModel(): Model\AdminsModel {
    return $this->getModel("Admins");
  }

  /**
   *
   * @return Model\ArticlesModel
   */
  public function getArticlesModel(): Model\ArticlesModel {
    return $this->getModel("Articles");
  }

  /**
   *
   * @return Model\AttachmentsModel
   */
  public function getAttachmentsModel(): Model\AttachmentsModel {
    return $this->getModel("Attachments");
  }

  /**
   *
   * @return Model\BalikovnapointsModel
   */
  public function getBalikovnapointsModel(): Model\BalikovnapointsModel {
    return $this->getModel("Balikovnapoints");
  }

  /**
   *
   * @return Model\BaseModel
   */
  public function getBaseModel(): Model\BaseModel {
    return $this->getModel("Base");
  }

  /**
   *
   * @return Model\BasketItemsModel
   */
  public function getBasketItemsModel(): Model\BasketItemsModel {
    return $this->getModel("BasketItems");
  }

  /**
   *
   * @return Model\BookmarksModel
   */
  public function getBookmarksModel(): Model\BookmarksModel {
    return $this->getModel("Bookmarks");
  }


  /**
   *
   * @return Model\CatalogsModel
   */
  public function getCatalogsModel(): Model\CatalogsModel {
    return $this->getModel("Catalogs");
  }


  /**
   *
   * @return Model\CatPlacesModel
   */
  public function getCatPlacesModel(): Model\CatPlacesModel {
    return $this->getModel("CatPlaces");
  }

  /**
   *
   * @return Model\CommentsModel
   */
  public function getCommentsModel(): Model\CommentsModel {
    return $this->getModel("Comments");
  }

  /**
   *
   * @return Model\ConfigModel
   */
  public function getConfigModel(): Model\ConfigModel {
    return $this->getModel("Config");
  }


  /**
   *
   * @return Model\CouponsModel
   */
  public function getCouponsModel(): Model\CouponsModel {
    return $this->getModel("Coupons");
  }

  /**
   *
   * @return Model\DeliveryModesModel
   */
  public function getDeliveryModesModel(): Model\DeliveryModesModel {
    return $this->getModel("DeliveryModes");
  }

  /**
   *
   * @return Model\DictionariesModel
   */
  public function getDictionariesModel(): Model\DictionariesModel {
    return $this->getModel("Dictionaries");
  }

  /**
   *
   * @return Model\DiscountsModel
   */
  public function getDiscountsModel(): Model\DiscountsModel {
    return $this->getModel("Discounts");
  }

  /**
   *
   * @return Model\DpdManifestsModel
   */
  public function getDpdManifestsModel(): Model\DpdManifestsModel {
    return $this->getModel("DpdManifests");
  }

  /**
   *
   * @return Model\EnumcatsModel
   */
  public function getEnumcatsModel(): Model\EnumcatsModel {
    return $this->getModel("Enumcats");
  }

  /**
   *
   * @return Model\ExportsModel
   */
  public function getExportsModel(): Model\ExportsModel {
    return $this->getModel("Exports");
  }

  /**
   *
   * @return Model\FulltextlogsModel
   */
  public function getFulltextlogsModel(): Model\FulltextlogsModel {
    return $this->getModel("Fulltextlogs");
  }

  /**
   *
   * @return Model\MailingsModel
   */
  public function getMailingsModel(): Model\MailingsModel {
    return $this->getModel("Mailings");
  }

  /**
   *
   * @return Model\MallcatalogsModel
   */
  public function getMallcatalogsModel(): Model\MallcatalogsModel {
    return $this->getModel("Mallcatalogs");
  }



  /**
   *
   * @return Model\MailingStatsModel
   */
  public function getMailingStatsModel(): Model\MailingStatsModel {
    return $this->getModel("MailingStats");
  }


  /**
   *
   * @return Model\MailsModel
   */
  public function getMailsModel(): Model\MailsModel {
    return $this->getModel("Mails");
  }


  /**
   *
   * @return Model\ManufacturersModel
   */
  public function getManufacturersModel(): Model\ManufacturersModel {
    return $this->getModel("Manufacturers");
  }


  /**
   *
   * @return Model\MenuIndexsModel
   */
  public function getMenuIndexsModel(): Model\MenuIndexsModel {
    return $this->getModel("MenuIndexs");
  }


  /**
   *
   * @return Model\MenusModel
   */
  public function getMenusModel(): Model\MenusModel {
    return $this->getModel("Menus");
  }


  /**
   *
   * @return Model\NewsModel
   */
  public function getNewsModel(): Model\NewsModel {
    return $this->getModel("News");
  }


  /**
   *
   * @return Model\OrdersModel
   */
  public function getOrdersModel(): Model\OrdersModel {
    return $this->getModel("Orders");
  }

  /**
   *
   * @return Model\OrdItemsModel
   */
  public function getOrdItemsModel(): Model\OrdItemsModel {
    return $this->getModel("OrdItems");
  }

  /**
   *
   * @return Model\PagesModel
   */
  public function getPagesModel(): Model\PagesModel {
    return $this->getModel("Pages");
  }

  /**
   *
   * @return Model\PayModesModel
   */
  public function getPayModesModel(): Model\PayModesModel {
    return $this->getModel("PayModes");
  }

  /**
   *
   * @return Model\PayuLogsModel
   */
  public function getPayuLogsModel(): Model\PayuLogsModel {
    return $this->getModel("PayuLogs");
  }

  /**
   *
   * @return Model\ProductsModel
   */
  public function getProductsModel(): Model\ProductsModel {
    return $this->getModel("Products");
  }

  /**
   *
   * @return Model\ProlinksModel
   */
  public function getProlinksModel(): Model\ProlinksModel {
    return $this->getModel("Prolinks");
  }

  /**
   *
   * @return Model\PropackagesModel
   */
  public function getPropackagesModel(): Model\PropackagesModel {
    return $this->getModel("Propackages");
  }

  /**
   *
   * @return Model\ProParamsModel
   */
  public function getProParamsModel(): Model\ProParamsModel {
    return $this->getModel("ProParams");
  }

  /**
   *
   * @return Model\SitemapsModel
   */
  public function getSitemapsModel(): Model\SitemapsModel {
    return $this->getModel("Sitemaps");
  }

  /**
   *
   * @return Model\UlozenkapointsModel
   */
  public function getUlozenkapointsModel(): Model\UlozenkapointsModel {
    return $this->getModel("Ulozenkapoints");
  }

  /**
   *
   * @return Model\WedopointsModel
   */
  public function getWedopointsModel(): Model\WedopointsModel {
    return $this->getModel("Wedopoints");
  }

  /**
   *
   * @return Model\ZasilkovnapointsModel
   */
  public function getZasilkovnapointsModel(): Model\ZasilkovnapointsModel {
    return $this->getModel("Zasilkovnapoints");
  }

  /**
   *
   * @return Model\UsersModel
   */
  public function getUsersModel(): Model\UsersModel {
    return $this->getModel("Users");
  }

  /**
   *
   * @return Model\WatchdogsModel
   */
  public function getWatchdogsModel(): Model\WatchdogsModel {
    return $this->getModel("Watchdogs");
  }

    /**
   *
   * @return Model\DpdpointsModel
   */
  public function getDpdpointsModel(): Model\DpdpointsModel {
    return $this->getModel("Dpdpoints");
  }

}
