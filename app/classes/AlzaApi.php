<?php

class AlzaApi {

  public static function getImportCategories(): array {
    return [
      //aminokyseliny
      4 => [
        "MAIN_CATEGORY_ID" => 18862654,
        "PARAMETER_0005" => "Aminokyseliny",
        "PARAMETER_16221" => "Aminokyseliny",
        "PARAMETER_16232" => "před tréninkem, po tréninku"
      ],
      //gainery a sacharidy
      2 => [
        "MAIN_CATEGORY_ID" => 18862646,
        "PARAMETER_0005" => "<PERSON>ainer",
        "PARAMETER_16318" => "all in one"
      ],
      //proteiny
      3 => [
        "MAIN_CATEGORY_ID" => 18862638,
        "PARAMETER_0005" => "Protein",
      ],
      //kreatin
      7 => [
        "MAIN_CATEGORY_ID" => 18862660,
        "PARAMETER_0005" => "Kreatin",
        "PARAMETER_16232" => "před, během i po tréninku" //doba užití
      ],
      //anabolizéry
      5 => [
        "MAIN_CATEGORY_ID" => 18862675,
        "PARAMETER_0005" => "Anabolizér",
        "PARAMETER_16232" => "před, během i po tréninku" //doba užití
      ],
      //spalovače
      8 => [
        "MAIN_CATEGORY_ID" => 18862665,
        "PARAMETER_0005" => "Spalovač tuků",
        "PARAMETER_16232" => "před, během i po tréninku" //doba užití
      ],
    ];

  }

  public static function getAlzaParamNames($catId) {
    //aminokyseliny
    $arr[4] = [
      'MAIN_CATEGORY_ID'=>'ID hlavní kategorie',
      'PARAMETER_0001'=>'Katalogová segmentace',
      'PARAMETER_0005'=>'SEO-Prefix',
      'PARAMETER_0002'=>'SEO-Název',
      'PARAMETER_0003'=>'NameExt custom',
      'PARAMETER_2015'=>'Dodavatelský kód produktu',
      'PARAMETER_1933'=>'EAN kód',
      'PARAMETER_0004'=>'Výrobce',
      'PARAMETER_5135'=>'Eshop cena s DPH',
      'PARAMETER_7152'=>'Hloubka obalu (konz.) [cm]',
      'PARAMETER_7155'=>'Výška obalu (konz.) [cm]',
      'PARAMETER_7151'=>'Šířka obalu (konz.) [cm]',
      'PARAMETER_7153'=>'Váha vč. obalu (konz.) [kg]',
      'PARAMETER_16214'=>'Příchuť',
      'PARAMETER_16214.02'=>'Příchuť',
      'PARAMETER_16214.03'=>'Příchuť',
      'PARAMETER_16215'=>'Hmotnost [kg]',
      'PARAMETER_16221'=>'Aminokyselina',
      'PARAMETER_16221.02'=>'Aminokyselina',
      'PARAMETER_16221.03'=>'Aminokyselina',
      'PARAMETER_16230'=>'Objem [l]',
      'PARAMETER_16232'=>'Doba užití',
      'PICTURE_01'=>'Obrázek (odkaz) - 01.',
      'PICTURE_02'=>'Obrázek (odkaz) - 02.',
      'PICTURE_03'=>'Obrázek (odkaz) - 03.',
      'PICTURE_04'=>'Obrázek (odkaz) - 04.',
      'PICTURE_05'=>'Obrázek (odkaz) - 05.',
      'PICTURE_06'=>'Obrázek (odkaz) - 06.',
      'PICTURE_07'=>'Obrázek (odkaz) - 07.',
      'PICTURE_08'=>'Obrázek (odkaz) - 08.',
      'PICTURE_09'=>'Obrázek (odkaz) - 09.',
      'PICTURE_10'=>'Obrázek (odkaz) - 10.',
      'SUGGESTED_ALZA_PRODUCTCODE'=>'Navrhovaný kód produktu',
      'PARAMETER_1934'=>'ProductNumber',
      'PARAMETER_5131'=>'Specifikace popisu',
      'PRICE_COMPARER_1'=>'Heureka.cz',
      'PRICE_COMPARER_3'=>'Zbozi.cz',
      'PARAMETER_6446'=>'Kalorií na 100 g [kcal]',
      'PARAMETER_6447'=>'Tuky [kg]',
      'PARAMETER_6448'=>'Cukry [kg]',
      'PARAMETER_6449'=>'Sacharidy [kg]',
      'PARAMETER_6450'=>'Vláknina [kg]',
      'PARAMETER_6451'=>'Nasycené mastné kyseliny [kg]',
      'PARAMETER_6452'=>'Kilojoulů na 100 g [kcal]',
      'PARAMETER_6453'=>'Bílkoviny [kg]',
      'PARAMETER_6454'=>'Sůl [kg]',
      'PARAMETER_16217'=>'Výrobek obsahuje alergeny',
      'PARAMETER_16217.02'=>'Výrobek obsahuje alergeny',
      'PARAMETER_16217.03'=>'Výrobek obsahuje alergeny',
      'PARAMETER_16231'=>'Forma',
      'PARAMETER_16234'=>'Počet tablet',
      'PARAMETER_16235'=>'Počet kapslí',
      'PARAMETER_16425'=>'Vlastnosti',
      'PARAMETER_16425.02'=>'Vlastnosti',
      'PARAMETER_16425.03'=>'Vlastnosti',
      'PARAMETER_17768'=>'Teplota skladování od [°C]',
      'PARAMETER_17769'=>'Teplota skladování do [°C]',
      'PARAMETER_20587'=>'Příchuť',
      'DOCUMENT_01'=>'Dokument (odkaz) - 01.',
      'DOCUMENT_COUNTRIES_01'=>'Země dokumentu - 01.',
      'DOCUMENT_02'=>'Dokument (odkaz) - 02.',
      'DOCUMENT_COUNTRIES_02'=>'Země dokumentu - 02.',
      'DOCUMENT_03'=>'Dokument (odkaz) - 03.',
      'DOCUMENT_COUNTRIES_03'=>'Země dokumentu - 03.',
    ];

    if (isset($arr[$catId])) {
      return $arr[$catId];
    } else {
      return [];
    }
  }
}
