ALTER TABLE `admins`     CHANGE `admrole` `admrole` VARCHAR(20) NOT NULL;

-- dop<PERSON><PERSON> druhe meny
ALTER TABLE `products` CHANGE `propricecom` `proprice1com` DOUBLE(11,2) NULL COMMENT 'bezna cena 1', CHANGE `propricea` `proprice1a` DOUBLE(11,2) NULL COMMENT 'cena 1 A', CHANGE `propriceb` `proprice1b` DOUBLE(11,2) NULL COMMENT 'cena 1 B', CHANGE `propricec` `proprice1c` DOUBLE(11,2) NULL COMMENT 'cena 1 C', CHANGE `propriced` `proprice1d` DOUBLE(11,2) NULL COMMENT 'cena 1 D', ADD COLUMN `proprice2com` DOUBLE NULL COMMENT 'cena bezna 2' AFTER `proprice1d`, ADD COLUMN `proprice2a` DOUBLE NULL COMMENT 'cena 2 A' AFTER `proprice2com`, ADD COLUMN `proprice2b` DOUBLE NULL COMMENT 'cena 2 B' AFTER `proprice2a`, ADD COLUMN `proprice2c` DOUBLE NULL COMMENT 'cena 2 C' AFTER `proprice2b`, ADD COLUMN `proprice2d` DOUBLE NULL COMMENT 'cena 2 D' AFTER `proprice2c`;
ALTER TABLE `orders` ADD COLUMN `ordcurr` VARCHAR(5) NULL COMMENT 'mena' AFTER `ordpricevat`;

-- vymazani nastaveni men z configu
DELETE FROM `config` WHERE `cfgid` = '80';
DELETE FROM `config` WHERE `cfgid` = '107';

-- mena v objednavce
ALTER TABLE `orders` CHANGE `ordcurr` `ordcurid` TINYINT(1) NULL COMMENT 'id meny';
INSERT INTO `config` (`cfgcode`, `cfgtypid`, `cfgvalue`, `cfgorder`, `cfgnote`) VALUES ('PRICE2RATE', '40', '25', '255', 'Měnový kurz pro výpočet ceny 2');

ALTER TABLE `products`
  ADD COLUMN `prodelfree` SMALLINT(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Doprava zdarma' AFTER `pronotdisc`;
ALTER TABLE `products`
  ADD COLUMN `prorating` VARCHAR(50) NOT NULL COMMENT 'Hodnoceni zbozi' AFTER `prodelfree`;

ALTER TABLE `products`
  ALTER `prokey` DROP DEFAULT,
  ALTER `prorating` DROP DEFAULT;
ALTER TABLE `products`
  CHANGE COLUMN `prokey` `prokey` VARCHAR(255) NULL COMMENT 'URL klic' AFTER `proid`,
  CHANGE COLUMN `prorating` `prorating` VARCHAR(50) NULL COMMENT 'Hodnoceni zbozi' AFTER `prodelfree`;

-- nova tabulka comments
-- nova tabulka watchdogs
-- nova tabulka mails
-- nova tabulka mailings
-- nova tabulka mailingstats
-- nova tabulka mailings_mailslog

-- obrazek ke zbozi
ALTER TABLE `products` ADD COLUMN `proico` TEXT NULL COMMENT 'ikona u zbozi' AFTER `provideo`;

ALTER TABLE `discounts` ADD COLUMN `discurid` TINYINT(1) NOT NULL COMMENT 'id meny' AFTER `disid`;
ALTER TABLE `discounts` ADD COLUMN `disprccat` CHAR(1) NULL COMMENT 'id cenove kategorie' AFTER `discurid`;

ALTER TABLE `comments` CHANGE `cmtproid` `cmtproid` INT(10) NULL, ADD COLUMN `cmtthmid` INT(10) NULL AFTER `cmtproid`;
ALTER TABLE `comments` ADD COLUMN `cmtcatid` INT(10) NULL AFTER `cmtthmid`, ADD COLUMN `cmtcatid2` INT(10) NULL AFTER `cmtcatid`;

ALTER TABLE `products` ADD COLUMN `progift` VARCHAR(255) NULL COMMENT 'Darek zdarma textove' AFTER `propicname`;

ALTER TABLE `discounts` ADD COLUMN `distypid` VARCHAR(10) NULL COMMENT 'typ slevy [volume|delfree]' AFTER `disid`, CHANGE `disstatus` `disstatus` TINYINT(1) DEFAULT 0 NOT NULL COMMENT 'status';

-- nová tabulka articles
DROP TABLE IF EXISTS `articles`;
CREATE TABLE `articles` (
  `artid` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `arttypid` int(10) unsigned NOT NULL DEFAULT '0',
  `arttop` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `artname` varchar(250) NOT NULL,
  `arttitle` varchar(250) DEFAULT '',
  `arturlkey` varchar(250) DEFAULT NULL,
  `artkeywords` varchar(250) DEFAULT NULL,
  `artdescription` text,
  `artbody` longtext,
  `artcnt` int(10) unsigned NOT NULL DEFAULT '0',
  `artstatus` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `artdate` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `artdatec` datetime DEFAULT NULL,
  `artdateu` datetime DEFAULT NULL,
  PRIMARY KEY (`artid`),
  KEY `i_typid_top` (`arttypid`,`arttop`),
  KEY `i_status` (`artstatus`),
  KEY `i_name` (`artname`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

ALTER TABLE `attachments` ADD COLUMN `ataartid` INT(10) UNSIGNED NULL AFTER `atapagid`;

ALTER TABLE `articles` ADD INDEX `i_typid_top` (`arttypid`, `arttop`), ADD INDEX `i_status` (`artstatus`);

ALTER TABLE `articles` ADD INDEX `i_name` (`artname`);

-- nová tabulka na logovani co kdo hledal

INSERT INTO `config` (`cfgid`, `cfgcode`, `cfgtypid`, `cfgcontroltype`, `cfgvalues`, `cfgvalue`, `cfgorder`, `cfgnote`, `cfgdatec`, `cfgdateu`) VALUES
(NULL,  'PRICELIST_PASSW',  43,  'text',  NULL,  'gold78',  255,  'Heslo pro přístup do online slovníku',  '0000-00-00 00:00:00',  '2016-02-03 10:56:34');

ALTER TABLE `orders` ADD COLUMN `ordsesid` VARCHAR(100) NULL COMMENT 'sesionid online platby' AFTER `ordpaycode`;
INSERT INTO `pages` (`pagurlkey`, `pagname`, `pagbody`, `pagblock`, `pagdatec`) VALUES ('menuleft_add', 'Reklama v levém menu - obrázek', '<a href=\"#\"><img src=\"img/collagen.jpg\" alt=\"\"></a>', '1', '2016-02-16 15:13:44');

INSERT INTO `config` (`cfgcode`, `cfgtypid`, `cfgvalue`, `cfgorder`, `cfgnote`) VALUES ('GOLDDAY_DATETO', '46', '18.2.2016', '16', 'Datum platnosti zlatých dnů do');
ALTER TABLE `products` ADD COLUMN `pronoteint` TEXT NULL COMMENT 'interni poznamka' AFTER `prostatus`;
ALTER TABLE `deliverymodes` CHANGE `delprice1` `delprice1a` DOUBLE NULL, ADD COLUMN `delprice1b` DOUBLE NULL AFTER `delprice1a`, ADD COLUMN `delprice1c` DOUBLE NULL AFTER `delprice1b`, ADD COLUMN `delprice1d` DOUBLE NULL AFTER `delprice1c`, ADD COLUMN `delprice2a` DOUBLE NULL AFTER `delprice1d`, ADD COLUMN `delprice2b` DOUBLE NULL AFTER `delprice2a`, ADD COLUMN `delprice2c` DOUBLE NULL AFTER `delprice2b`, CHANGE `delprice2` `delprice2d` DOUBLE NULL;

INSERT INTO `config` (`cfgcode`, `cfgtypid`, `cfgvalues`, `cfgvalue`, `cfgorder`, `cfgnote`) VALUES ('DISCOUNT_DISSOLVE', '60', '0,1;Ne,Ano', '1', '255', 'Zda rozpustit množstevní slevu do položek objednávky');

ALTER TABLE `deliverymodes` ADD COLUMN `delcurid` TINYINT DEFAULT 0 NOT NULL COMMENT 'ID meny [0-obe]' AFTER `delmasid`;

DROP TABLE IF EXISTS `products_salestat`;
CREATE TABLE `products_salestat` (
  `prsproid` INT(10) UNSIGNED NOT NULL DEFAULT '0',
  `prscnt` INT(10) UNSIGNED NOT NULL DEFAULT '0',
  `prscatpathids` VARCHAR(255) DEFAULT NULL,
  `prsperiod` TINYINT(4) NOT NULL DEFAULT '0' COMMENT 'pocet dni zpetne',
  `prsdatec` DATETIME DEFAULT NULL,
  `prsdateu` DATETIME DEFAULT NULL,
  KEY `i_proid` (`prsproid`),
  KEY `i_catpathids` (`prscatpathids`),
  KEY `i_period` (`prsperiod`)
) ENGINE=MYISAM DEFAULT CHARSET=utf8;

ALTER TABLE `mailings` ADD COLUMN `prodesc1` TEXT NULL COMMENT 'popisek 1' AFTER `mamproducts`, ADD COLUMN `prodesc2` TEXT NULL COMMENT 'popisek 2' AFTER `prodesc1`, ADD COLUMN `prodesc3` TEXT NULL COMMENT 'popisek 3' AFTER `prodesc2`;
ALTER TABLE `mailings` CHANGE `prodesc1` `mamdesc1` TEXT CHARSET utf8 COLLATE utf8_unicode_ci NULL COMMENT 'popisek 1', CHANGE `prodesc2` `mamdesc2` TEXT CHARSET utf8 COLLATE utf8_unicode_ci NULL COMMENT 'popisek 2', CHANGE `prodesc3` `mamdesc3` TEXT CHARSET utf8 COLLATE utf8_unicode_ci NULL COMMENT 'popisek 3';
ALTER TABLE `mailings` ADD COLUMN `malfooter` LONGTEXT NULL COMMENT 'patička mailu' AFTER `mambody`;
ALTER TABLE `mailings` CHANGE `malfooter` `mamfooter` LONGTEXT CHARSET utf8 COLLATE utf8_unicode_ci NULL COMMENT 'patička mailu';
INSERT INTO `config` (`cfgcode`, `cfgtypid`, `cfgvalue`) VALUES ('SERVER_MAILING_MAIL', '40', '<EMAIL>');
UPDATE `config` SET `cfgnote` = 'Email na který se nemají posílat odpovědi' WHERE `cfgid` = '139';

ALTER TABLE `watchdogs` DROP INDEX `ui_usridproid`, ADD UNIQUE INDEX `ui_usridproid` (`dogusrid`, `dogproid`, `dogprice`, `dogstore`);
ALTER TABLE `watchdogs` DROP INDEX `i_price_store`;

ALTER TABLE `pages` ADD COLUMN `pagtext1` TEXT NULL COMMENT 'variabilni text 1' AFTER `pagbody`, ADD COLUMN `pagtext2` TEXT NULL COMMENT 'variabilni text 2' AFTER `pagtext1`, ADD COLUMN `pagtext3` TEXT NULL COMMENT 'variabilni text 3' AFTER `pagtext2`;

INSERT INTO `config` (`cfgcode`, `cfgtypid`, `cfgvalue`, `cfgorder`, `cfgnote`) VALUES ('CATALOG_ROWSCNT_MOBI', '43', '12', '9', 'Počet položek v katalogu na jednu stránku pro mobily');

-- aktualizace stavu skladu a kodu pohody
ALTER TABLE `products` CHANGE `procode2` `procode2` VARCHAR(255) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT 'EAN', ADD COLUMN `procodep` VARCHAR(255) NULL COMMENT 'kod pohoda' AFTER `procode2`, ADD COLUMN `proqty_store` INT(0) NULL COMMENT 'množství skladem - sklad' AFTER `proqty`, ADD COLUMN `proqty_shop` INT(0) NULL COMMENT 'množství skladem - prodejna' AFTER `proqty_store`;
ALTER TABLE `products` ADD COLUMN `procpcheureka` DOUBLE NULL COMMENT 'CPC heureka' AFTER `procredit`, ADD COLUMN `procpczbozi` DOUBLE NULL COMMENT 'CPC zbozi' AFTER `procpcheureka`;
-- provedeno --

-- katalogove cesty dle zbozivych vyhl
ALTER TABLE `catalogs` ADD COLUMN `catpathheureka` VARCHAR(255) NULL COMMENT 'Katalogova cesta dle heureka.cz' AFTER `catpathids`, ADD COLUMN `catpathgoogle` VARCHAR(255) NULL COMMENT 'Katalogova cesta dle google nakupy' AFTER `catpathheureka`, ADD COLUMN `catpathzbozi` VARCHAR(255) NULL COMMENT 'Katalogova cesta dle zbozi.cz' AFTER `catpathgoogle`;
UPDATE catalogs SET catpathheureka = catparams;
UPDATE catalogs SET catparams = NULL;

ALTER TABLE `comments` ADD INDEX `i_reid` (`cmtreid`);
ALTER TABLE  `comments` ADD INDEX `i_proid` (`cmtproid`);
ALTER TABLE  `comments` ADD INDEX `i_datac` (`cmtdatec`);
ALTER TABLE `products` ADD INDEX `i_proname` (`proname`);
ALTER TABLE `fulltextlogs` ADD INDEX `i_text` (`ftxtext`);
-- provedeno --

-- hlidaci pes bez registrace
ALTER TABLE `watchdogs` ADD COLUMN `dogmail` VARCHAR(255) NULL COMMENT 'email zakaznika' AFTER `dogstore`;
ALTER TABLE `watchdogs` ADD INDEX `ui_pricestoremail` (`dogprice`, `dogstore`, `dogmail`);
ALTER TABLE `watchdogs` DROP INDEX `ui_pricestoremail`, ADD INDEX `ui_pricestoreproidmail` (`dogprice`, `dogstore`, `dogmail`, `dogproid`);
ALTER TABLE `watchdogs` DROP INDEX `ui_usridproid`;

UPDATE watchdogs INNER JOIN users ON (usrid=dogusrid) SET dogmail=usrmail;
-- provedeno --

-- popis dopravy pro SK
ALTER TABLE `deliverymodes` CHANGE `deltext` `deltext1` TEXT CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT 'Popis dopravy pro měnu ID 1', ADD COLUMN `deltext2` TEXT NULL COMMENT 'Popis dopravy pro měnu ID 2' AFTER `deltext1`;

ALTER TABLE `pages` ADD COLUMN `pagtext4` TEXT NULL COMMENT 'variabilni text 4' AFTER `pagtext3`, ADD COLUMN `pagtext5` TEXT NULL COMMENT 'variabilni text 5' AFTER `pagtext4`, ADD COLUMN `pagtext6` TEXT NULL COMMENT 'variabilni text 6' AFTER `pagtext5`;

-- doplneni bloků
INSERT INTO `catalogs` (`catid`, `catkey`, `catmasid`, `catlevel`, `catclass`, `catname`, `catkeywords`, `catsalestat`, `catdescription`, `catdesc`, `catorder`, `catcounter`, `catpath`, `catpathids`, `catpathheureka`, `catpathgoogle`, `catpathzbozi`, `catparams`, `catstatus`, `catdatec`, `catdateu`) VALUES
(83,	'',	0,	1,	'',	'Sportovní výživa',	'',	'',	'',	'',	10,	0,	'Sportovní výživa',	'|83|',	'',	'',	'',	NULL,	0,	'2016-11-24 15:02:31',	NULL);

--aktualizace všech kořenových úrovní a doplnění nadřízené kategorie
UPDATE catalogs SET catmasid=84 WHERE catmasid=0 AND catid NOT IN (84,57);
-- vymazat cache
-- znovu uložit kořenovou kategorii ať se přepočítají katalogové cesty

ALTER TABLE `mailings` ADD COLUMN `mampricea` TINYINT(1) NULL AFTER `mamid`, ADD COLUMN `mampriceb` TINYINT(1) NULL AFTER `mampricea`, ADD COLUMN `mampricec` TINYINT(1) NULL AFTER `mampriceb`, ADD COLUMN `mampriced` TINYINT(1) NULL AFTER `mampricec`;
ALTER TABLE `mailings` ADD COLUMN `mammaillist` TINYINT(1) NULL COMMENT 'jen přihlášené k maillistu' AFTER `mampriced`;

ALTER TABLE `catalogs` ADD COLUMN `catparamheureka` VARCHAR(255) NULL COMMENT 'Paramery pro vyhledávání dle heureky' AFTER `catpathheureka`;

ALTER TABLE `pages` ADD COLUMN `pagbody2` LONGTEXT NULL COMMENT 'tělo stránky 2. část' AFTER `pagbody`;

ALTER TABLE products CHANGE proqty_shop proqty_shop1 INT(11) COMMENT 'množství skladem - prodejna';
ALTER TABLE products ADD proqty_shop2 INT DEFAULT 0 NOT NULL COMMENT 'množství skladem prodejna 2';
ALTER TABLE products MODIFY COLUMN proqty_shop2 INT NOT NULL DEFAULT 0 COMMENT 'množství skladem prodejna 2' AFTER proqty_shop1;

ALTER TABLE `products` ADD INDEX `i_procodep` (`procodep`), ADD INDEX `i_procode2` (`procode2`);

ALTER TABLE products ADD proisexp TINYINT DEFAULT 0 NOT NULL COMMENT 'Příznak zda se jedná o expirační položku';
ALTER TABLE products MODIFY COLUMN proisexp TINYINT NOT NULL DEFAULT 0 COMMENT 'Příznak zda se jedná o expirační položku' AFTER probigsize;

INSERT INTO config (cfgcode, cfgtypid, cfgcontroltype, cfgvalues, cfgvalue, cfgorder, cfgnote, cfgdatec, cfgdateu) VALUES ('HOLIDAYS', 40, 'text', null, '1.1.,1.5.,8.5.,5.7.,6.7.,28.9.,28.10.,17.11.,24.12.,25.12.,26.12.', 255, 'Státní svátky - nutné pro výpočet termínu dodání, oddělte datumy čárkou ', '2017-04-28 16:35:43', '2017-04-28 16:35:43');

ALTER TABLE `products` ADD COLUMN `progifts` VARCHAR(255) NULL COMMENT 'výčet ID zboží jako dárků' AFTER `progift`;

ALTER TABLE products ADD prodesccd TEXT NULL;
ALTER TABLE products ADD prodescab TEXT NULL;
ALTER TABLE products
  MODIFY COLUMN prodescab TEXT AFTER prodesc,
  MODIFY COLUMN prodesccd TEXT AFTER prodescab;

ALTER TABLE orders ADD ordexported TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE orders MODIFY COLUMN ordexported TINYINT(1) NOT NULL DEFAULT 0 AFTER ordpaystatus;

ALTER TABLE orders ADD ordcurrate DOUBLE NULL;
ALTER TABLE orders MODIFY COLUMN ordcurrate DOUBLE AFTER ordcurid;

ALTER TABLE users ADD usrexportstatus TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE users MODIFY COLUMN usrexportstatus TINYINT(1) NOT NULL DEFAULT 0 AFTER usrstatus;

create table propackages
(
	pacid int auto_increment
		primary key,
	pacproid int not null,
	pacsubproid int not null,
	pacqty int not null,
	pacdatec datetime null,
	pacdateu datetime null
)
engine=MyISAM;

create index i_proid_subproid
	on propackages (pacproid, pacsubproid);

ALTER TABLE products ADD proidp INT NULL COMMENT 'ID pohody';
CREATE INDEX i_proidp ON products (proidp);
ALTER TABLE products
  MODIFY COLUMN proidp INT COMMENT 'ID pohody' AFTER procodep;

ALTER TABLE products ADD prostocktypep VARCHAR(100) NULL;
ALTER TABLE products
  MODIFY COLUMN prostocktypep VARCHAR(100) AFTER procodep;

ALTER TABLE products ADD prostoreidp_store VARCHAR(10) NULL;
ALTER TABLE products MODIFY COLUMN prostoreidp_store VARCHAR(10) AFTER proqty_shop2;
ALTER TABLE products ADD prostoreidp_shop1 VARCHAR(10) NULL;
ALTER TABLE products MODIFY COLUMN prostoreidp_shop1 VARCHAR(10) AFTER prostoreidp_store;
ALTER TABLE products ADD prostoreidp_shop2 VARCHAR(10) NULL;
ALTER TABLE products MODIFY COLUMN prostoreidp_shop2 VARCHAR(10) AFTER prostoreidp_shop1;

ALTER TABLE orditems ADD oripriceoriginal DOUBLE(11,2) NULL COMMENT 'originální cena';
ALTER TABLE orditems MODIFY COLUMN oripriceoriginal DOUBLE(11,2) COMMENT 'originální cena' AFTER oriprice;

ALTER TABLE orditems ADD oridiscper DOUBLE(11,2) NULL COMMENT 'Sleva v %';
ALTER TABLE orditems MODIFY COLUMN oridiscper DOUBLE(11,2) COMMENT 'Sleva v %' AFTER oridisc;

ALTER TABLE watchdogs ADD dogpricevalue DOUBLE(10,2) NULL;
ALTER TABLE watchdogs MODIFY COLUMN dogpricevalue DOUBLE(10,2) AFTER dogprice;
ALTER TABLE watchdogs ADD dogpricename VARCHAR(20) NULL;
ALTER TABLE watchdogs MODIFY COLUMN dogpricename VARCHAR(20) AFTER dogpricevalue;

delete from watchdogs where dogprice>0;
delete from watchdogs where dogprice=0 and dogstore=0;

ALTER TABLE products ADD propicnamevar VARCHAR(50) NULL;
ALTER TABLE products MODIFY COLUMN propicnamevar VARCHAR(50) AFTER propicname;

update products SET protitle=NULL;

ALTER TABLE catalogs ADD catnoindex TINYINT DEFAULT 0 NOT NULL;
ALTER TABLE catalogs MODIFY COLUMN catnoindex TINYINT NOT NULL DEFAULT 0 AFTER catstatus;

-- nová tabulak sitemap
create table sitemaps
(
	sitid int auto_increment
		primary key,
	sittypid char(3) not null,
	sitcatid int null,
	sitproid int null,
	sitcatkey varchar(100) null,
	sitkeykey varchar(100) null,
	sitkeyfor varchar(100) null,
	sitrows int null,
	sitdatec datetime null,
	sitdateu datetime null,
	sitkeytyp varchar(100) null,
	sitkeyman varchar(100) null
)
engine=InnoDB
;

create index sitemaps_sittypid_sitcatkey_sitkeykey_index
	on sitemaps (sittypid, sitcatkey, sitkeykey)
;

create index sitemaps_sittypid_sitcatkey_index
	on sitemaps (sittypid, sitcatkey)
;

create index sitemaps_sittypid_sitkeyman_sitkeytyp_sitkeyfor_index
	on sitemaps (sittypid, sitkeyman, sitkeytyp, sitkeyfor)
;

ALTER TABLE sitemaps ADD sitlastmod DATETIME NULL;
ALTER TABLE sitemaps
  MODIFY COLUMN sitdateu DATETIME AFTER sitlastmod,
  MODIFY COLUMN sitdatec DATETIME AFTER sitlastmod,
  MODIFY COLUMN sitrows INT(11) AFTER sitkeyman;

ALTER TABLE articles ADD artrobots VARCHAR(20) NULL;
ALTER TABLE articles
  MODIFY COLUMN artrobots VARCHAR(20) AFTER artname;

update articles set artrobots='index,follow';

ALTER TABLE pages ADD pagrobots VARCHAR(20) NULL;
ALTER TABLE pages
  MODIFY COLUMN pagrobots VARCHAR(20) AFTER pagname;

update pages set pagrobots='index,follow';

INSERT INTO goldfitnesscz.pages (pagid, pagtypid, pagurlkey, pagname, pagrobots, pagtitle, pagdescription, pagkeywords, pagdesc, pagbody, pagbody2, pagtext1, pagtext2, pagtext3, pagtext4, pagtext5, pagtext6, pagblock, pagstatus, pagdatec, pagdateu, `Sloupec 14`) VALUES (NULL, 0, 'basket_del_message', 'Upozornění v košíku u doprav', null, 'Možnosti plateb', '', null, null, '<p>Nabízíme různé možnosti plateb včetně online převodů a plateb kartou.</p>', null, null, null, null, null, null, null, 1, 0, '2018-03-29 10:15:59', '2018-03-29 10:18:32', null);

ALTER TABLE orders ADD ordheurekagdpr tinyint NULL;

ALTER TABLE catalogs ADD catmalid varchar(100) NULL;
ALTER TABLE catalogs MODIFY COLUMN catmalid varchar(100) AFTER catmasid;

CREATE TABLE `ulozenkapoints` (
  `uloid` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `uloid2` int(10) DEFAULT NULL,
  `uloshortcut` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `uloname` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ulostreet` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ulocity` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ulopostcode` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ulocountry` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `uloemail` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ulophone` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `uloopeninghours` text COLLATE utf8_unicode_ci,
  `ulostatus` tinyint(1) NOT NULL DEFAULT '0',
  `ulourl` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ulourlphoto` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ulourlself` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ulogpsn` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ulogpse` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ulonavigation` text COLLATE utf8_unicode_ci,
  `ulodatec` datetime DEFAULT NULL,
  `ulodateu` datetime DEFAULT NULL,
  PRIMARY KEY (`uloid`),
  KEY `id2` (`uloshortcut`)
) ENGINE=MyISAM AUTO_INCREMENT=136 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- ID mall produktu
ALTER TABLE products ADD promalid varchar(100) NULL;
ALTER TABLE products MODIFY COLUMN promalid varchar(100) AFTER prokey;

-- id MALL objednávky
ALTER TABLE orders ADD ordmallid varchar(100) NULL;
ALTER TABLE orders MODIFY COLUMN ordmallid varchar(100) AFTER ordid;

-- počet hlasování
ALTER TABLE products ADD proratingcnt int NULL;
ALTER TABLE products MODIFY COLUMN proratingcnt int(11) AFTER prorating;

-- nastavím počet hlasování
UPDATE products SET proratingcnt = (SELECT COUNT(cmtid)+50 FROM comments WHERE cmtproid=proid OR cmtproid=promasid) WHERE proratingcnt is null;

ALTER TABLE proparams ADD prptypid tinyint DEFAULT 0 NOT NULL;
ALTER TABLE proparams MODIFY COLUMN prptypid tinyint NOT NULL DEFAULT 0 AFTER prpvalue;

INSERT INTO `config` (`cfgcode`, `cfgtypid`, `cfgcontroltype`, `cfgvalues`, `cfgvalue`, `cfgorder`, `cfgnote`, `cfgdatec`, `cfgdateu`) VALUES ('TOPSEARCHES', 46, 'text', 'NULL', 'bcaa,jerky,weider,protein,šejkr,reflex,Tribulus,grenade,viridian,hmb', DEFAULT, 'Seznam nejhledanějších slov v patičce (oddělte čárkou)', Now(), NULL);

ALTER TABLE manufacturers ADD manmalid varchar(100) NULL;
ALTER TABLE products ADD promalldateu datetime NULL;
update products set promalldateu = NOW();

ALTER TABLE orders ADD ordmalshipdate date NULL COMMENT 'datum odeslání mall';
alter table orders change ordmallid ordmalid varchar(100) null;

alter table comments add cmtaproved tinyint(1) not null;

-- nový katalog mall
alter table catalogs add catmallparams text null;

-- vymazu nepotrebne parametry
delete from proparams WHERE prpname IN ('BEFORE_ACTION' ,'DURING_ACTION', 'AFTER_ACTION', 'BODYBUILDING', 'ENDURANCE_SPORTS');


CREATE TABLE `basketitems` (
  `basid` int(11) NOT NULL AUTO_INCREMENT,
  `bassesid` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `basproid` int(11) NOT NULL,
  `basqty` int(11) NOT NULL,
  `basdatec` datetime DEFAULT NULL,
  `basdateu` datetime DEFAULT NULL,
  PRIMARY KEY (`basid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='položky v košíku ';

alter table basketitems
	add basusrid int not null;

alter table articles add artadmid int null after artid;

alter table comments add cmtadmid int null after cmtid;

alter table admins add admorder int null;
alter table admins modify admorder int null after admstatus;


alter table orders add ordpauid varchar(100) null after ordpayid;


-- DPD
CREATE TABLE `dpdmanifests` (
  `dpmid` int(11) NOT NULL AUTO_INCREMENT,
  `dpmserid` varchar(4) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dpmmanid` int(11) DEFAULT NULL,
  `dpmdate` date DEFAULT NULL,
  `dpmdatec` datetime DEFAULT NULL,
  `dpmdateu` datetime DEFAULT NULL,
  PRIMARY KEY (`dpmid`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

alter table orders add ordparid int null after ordparcode;
alter table orders add ordparcode2 varchar(100) null after ordparcode;

alter table products add promalllabels text null after promallblock;

INSERT INTO config (cfgid, cfgcode, cfgtypid, cfgcontroltype, cfgvalues, cfgvalue, cfgorder, cfgnote, cfgdatec, cfgdateu) VALUES
(NULL, 'BASKET_PRODUCTLIST', 46, 'text', null, '', 16, 'Produkty v košíku (ID zboží oddělené čárkou)', Now(), NULL);

CREATE TABLE `coupons` (
  `couid` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
  `couusrid` int(10) unsigned NOT NULL COMMENT 'id zakaznika',
  `couordid` int(10) unsigned DEFAULT NULL COMMENT 'id objednavky na ktere byl kupon pouzit',
  `coumamid` int(10) unsigned DEFAULT NULL COMMENT 'id mailingu',
  `coubasid` int(11) DEFAULT NULL COMMENT 'id košíku',
  `coumail` varchar(100) COLLATE utf8_unicode_ci NOT NULL COMMENT 'email',
  `couonlyreg` tinyint(4) NOT NULL DEFAULT '0',
  `coucode` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'kod kuponu',
  `couproducts` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'výšet ID produktů a jelich slevy vázaných ke kupónu',
  `couvalue` double unsigned NOT NULL DEFAULT '0' COMMENT 'hodnota',
  `couvalueunit` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
  `couvaluelimit` double DEFAULT NULL,
  `coupayfree` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'platba zdarma',
  `coudelfree` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'doprava zdarma',
  `couvalidto` date DEFAULT NULL COMMENT 'platnost kuponu',
  `coumailcnt` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'pocitadlo odmailovani kuponu',
  `couqty` int(11) NOT NULL DEFAULT '1' COMMENT 'počet kupónů',
  `coucounter` int(11) DEFAULT NULL COMMENT 'počítadlo kolik už bylo použito',
  `coustatus` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT 'status (0 - platny, 1 - vycerpany)',
  `coudatec` datetime DEFAULT NULL,
  `coudateu` datetime DEFAULT NULL,
  PRIMARY KEY (`couid`),
  KEY `i_usrid_mamid` (`couusrid`,`coumamid`),
  KEY `i_status_code_mail` (`coumail`,`coucode`,`coustatus`)
) ENGINE=MyISAM AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

alter table orders add ordcoucode varchar(20) null after ordparid;

create index products_pronamecz_index on products (pronamecz);

alter table articles  add artprocode varchar(100) null after arttypid;

alter table products add proratingall int null comment 'Celkový dojem' after prorating;
alter table products add proratinging int null comment 'hodnocení složení' after prorating;
alter table products add proratingmis int null comment 'hodnocení rozmíchatelnost' after proratinging;
alter table products add proratingdig int null comment 'hodnocení stravitelnost' after proratingmis;
alter table products add proratingtas int null comment 'hodnocení chuť' after proratingdig;

alter table orders add orddelspectext varchar(255) null after orddelspec;

alter table comments alter column cmtcatid set default 0;
alter table comments alter column cmtcatid2 set default 0;
alter table comments add cmtcatcatid int default 0 null after cmtcatid2;
create index comments_cmtcatcatid_index on comments (cmtcatcatid);

alter table attachments modify atatype varchar(5) null;

alter table products add propromodateto date null after proorigin;


alter table orders add ordexportblock tinyint(1) default 0 not null after ordexported;
alter table orders add ordtype tinyint(1) null comment 'typ objednávky [eshop, administrace, mall, přepravní štítek]' after orddelid
alter table orders add ordpricecod double null after ordpricevat;
alter table orders add ordparcelscount tinyint null after ordpricedisc;

alter table orders add ordprccat char null after orddelid;

alter table orders_log add orldesc varchar(255) null after orlstatus;

alter table products add proprice1e double(11, 2) null comment 'Cena 1 E' after proprice1d;
alter table products add proprice2e double(11, 2) null comment 'Cena 2 E' after proprice2d;

alter table mailings add mampricee tinyint(1) null after mampriced;

alter table comments add cmtcatcatid2 int default 0 not null after cmtcatcatid;

alter table admins add admconemail varchar(100) null after admabout;

alter table admins add admcontel varchar(100) null after admconemail;

create table czechpostcodes
(
    cpcid    int auto_increment,
    cpccode  int      null,
    cpcdatec datetime null,
    cpcdateu datetime null,
    constraint czechpostcodes_pk
        primary key (cpcid)
);

create index czechpostcodes_cpccode_index
    on czechpostcodes (cpccode);

alter table orders modify ordcoucode varchar(100) null;

ALTER TABLE orders ADD ordzbozigdpr tinyint NULL;

CREATE TABLE `zasilkovnapoints` (
  `zasid` int(10) UNSIGNED NOT NULL,
  `zasid2` int(10) DEFAULT NULL,
  `zasname` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zasstreet` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zascity` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zaspostcode` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zascountry` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zasemail` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zasphone` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zasopeninghours` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `zasstatus` tinyint(1) NOT NULL DEFAULT 0,
  `zasurl` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zasurlphoto` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zasurlself` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zasgpsn` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zasgpse` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zasnavigation` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `zasdatec` datetime DEFAULT NULL,
  `zasdateu` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

ALTER TABLE `zasilkovnapoints`
  ADD PRIMARY KEY (`zasid`),
  ADD KEY `id2_i` (`zasid2`);
ALTER TABLE `zasilkovnapoints`
  MODIFY `zasid` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;
COMMIT;

alter table goldfitnesscz1.products add proheurekaoff tinyint(1) default 0 not null;

alter table catalogs add catpathalza varchar(255) null;

alter table products add provolume double null;
alter table products add provolumeunit varchar(50) null;

alter table orders alter column ordstlname set default '';
alter table orders modify ordpricecod double default 0.0 not null;
alter table orders modify ordparcelscount tinyint default 1 not null;

alter table coupons alter column couusrid set default 0;
alter table coupons alter column coumail set default '';

alter table mailings add mambuycatid int null;
alter table mailings add mambuynot tinyint default 0 null;
alter table mailings add mambuydays int null;

alter table products add proprioritize tinyint(1) default 0 not null;

-- PROD --
