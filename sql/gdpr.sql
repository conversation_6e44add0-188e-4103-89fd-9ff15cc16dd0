CREATE TABLE users_log
(
    uslid INT PRIMARY KEY AUTO_INCREMENT,
    uslusrid INT NOT NULL COMMENT 'ID uživatele',
    uslevtid TINYINT NOT NULL COMMENT 'ID ud<PERSON><PERSON>ti',
    usldatec DATETIME NOT NULL COMMENT 'datum a čas události',
    usldateu DATETIME
);
CREATE INDEX users_log_uslusrid_index ON users_log (uslusrid);

ALTER TABLE users ADD usrgdpr tinyint DEFAULT 0 NOT NULL;
ALTER TABLE orders ADD ordheurekagdpr tinyint NULL;

-- goldfitness.cz
-- kosmetika-biolage.cz
-- 24krby
-- ustiky
-- hemi-sync.cz