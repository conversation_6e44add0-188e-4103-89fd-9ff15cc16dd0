select prpname, prpvalue from proparams group by prpname order by prpname;

select count(prpvalue), prpname, prpvalue
from proparams
inner join products ON proid=prpproid
WHERE prpname IN ('p<PERSON><PERSON>chu<PERSON>', 'P<PERSON><PERSON>chut\'')
group by prpvalue
order by count(prpvalue) DESC;

-- sjednotím názvy paramertru příchuť
update proparams set prpname = 'Příchuť' WHERE prpname = 'Příchut\'';

select count(prpvalue), prpvalue
from proparams
inner join products ON proid=prpproid
WHERE prpname IN ('Balení', '1. Balení')
group by prpvalue
order by count(prpvalue) DESC;


select proname, prpname, prpvalue
from proparams
inner join products ON proid=prpproid
WHERE prpname LIKE '%Balení%' AND prpvalue LIKE '%kapsl%'