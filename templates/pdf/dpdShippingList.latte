
<h3><PERSON><PERSON><PERSON><PERSON> bal<PERSON> {$date|date:'d.m.Y H:i'}</h3>
<table cellspacing="0" cellpadding="0" width="19cm"  border="0">
	<tr>
		<td>
			<strong>Zákazník:</strong> Goldfitness s.r.o<br>
			<strong>Svozová:</strong> Zdibská 2/229, 18200 Praha, CZ<br>
			<strong>Kontakt:</strong> <EMAIL>, (+420) 736631669
		</td>
	</tr>
</table>
<hr>
<table width="100%"  style="border: black solid 1px">
	<tr>
		<th align="left">Ne</th>
		<th align="left"><PERSON><PERSON>lo balíku</th>
		<th align="left">Jméno příjemce</th>
		<th align="left">Adresa</th>
		<th align="left">PSČ</th>
		<th align="left"><PERSON><PERSON><PERSON><PERSON></th>
		<th align="left">Reference</th>
	</tr>
	{foreach $shippingListData as $row}
	<tr>
		<td>{$iterator->getCounter()}</td>
		<td>{$row["parcelNumber"]}</td>
		<td>{$row["name"]}</td>
		<td>{$row["street"]} {$row["houseNo"]}, {$row["city"]}</td>
		<td>{$row["countryCode"]} {$row["zipCode"]}</td>
		<td>{if $row["cod"]}{$row["cod"]|formatPrice}{/if}</td>
		<td>{$row["reference1"]}</td>
	</tr>
	{/foreach}
 </table>
<hr>
<strong>Počet balíků:</strong> {count($shippingListData)}<br>
<p align="center">
<br><br>
{$date|date:'d.m.Y H:i'}&nbsp;&nbsp;&nbsp;&nbsp;...........................................<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	podpis
</p>
