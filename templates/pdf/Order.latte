<table cellspacing="0" cellpadding="0" width="19cm"  border="0">
	<tr>
    <td><font size="4"><b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> číslo: {$order->ordcode}</b></font></td>
    <td><p align="right"><font size="4">{if !empty($order->ordmalid)}Mall: {$order->ordmalid}{/if}</font></p></td>
	</tr>
 </table>
		<table width="19cm" border="0" style="border-collapse:collapse;margin-top:10px;"  align="center">
				<tr>
					<td style="border: 1px solid #c0c0c0;" valign="top" width="50%"><p><font size="2">{_'Dodavatel'}:<br>
							{$presenter->config["INVOICE_VENDOR_R1"]}<br>
              {$presenter->config["INVOICE_VENDOR_R2"]}<br>
              {$presenter->config["INVOICE_VENDOR_R3"]}<br>
              {$presenter->config["INVOICE_VENDOR_R5"]}<br>
              Bankovní spojení: {$presenter->config["SERVER_ACCNO"]}<br>

              {$presenter->config["INVOICE_VENDOR_R4"]}<br>
              E-mail: {$presenter->config["SERVER_MAILORDERS"]}<br>
              </font></p></td>
					<td style="border: 1px solid #c0c0c0;" colspan="2" valign="top"><p><font size="2">{_'Odběratel'}: <br>
							{if !empty($order->ordifirname)}{$order->ordifirname}<br />{/if}
              {$order->ordiname} {$order->ordilname}<br />
							{$order->ordistreet} {$order->ordistreetno}<br />
							{$order->ordipostcode}, {$order->ordicity}<br />
							<br />
							{if !empty($order->ordic)}{_'IČ'}:{$order->ordic}{/if} {if !empty($order->orddic)}{_'DIČ'}:{$order->orddic}{/if}<br />
							E-mail: {$order->ordmail}<br />
							Tel.: {$order->ordtel}</font></p></td>
				</tr>
			</table>
<br>
			<table width="19cm" style="border-collapse:collapse;" border="0" align="center">
				<tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Způsob platby'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$payMode->delname}</font></td>
				</tr><tr>
				  <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Způsob Dodání'}:</font></td>
				  <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$delMode->delname}</font></td>
        		</tr>
			</table>
			<table style="border-collapse:collapse;margin-top: 10px;" cellspacing=0 cellpadding=0 width="19cm"  align="center">
					<tr>
						<td  valign="top"><font style="font-size:12pt" face="Arial"><b>{_'Objednané zboží'}:</b></font>
							<table style="background:#fff;border-collapse:collapse;margin-top: 10px;" width="19cm" border="0" cellspacing="0" cellpadding="2" align="center">
								<tr>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="left"><font size="2">{_'Kód'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="40%" align="left"><font size="2">{_'Název'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{_'Kusů'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{$presenter->curCodes[$curId]} / {_'kus'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{_'Sleva'}</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{_'Sazba DPH'}</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{$presenter->curCodes[$curId]} s DPH</font></td>
								</tr>
								{php $sum = 0;}
								{foreach $ordItems as $row}
								{php $sum += ($row->oriprice*$row->oriqty-$row->oridisc);}
								<tr>
                  <td style="border-bottom: 1px solid #e0e0e0;"  width="15%" align="left"><font size="2">{$row->procode}</font></td>
									<td style="border-bottom: 1px solid #e0e0e0;"  width="40%" align="left"><font size="2">{$row->oriname} {if !empty($row->orisn)}<br />SN: {php echo str_replace("|", ", ", $row->orisn)} {/if}</font></td>
									<td style="border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{$row->oriqty}</font></td>
									<td style="border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{$row->oriprice|formatPrice:$curId}</font></td>
                  <td style="border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{$row->oridisc|formatPrice:$curId}</font></td>
                  <td style="border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{$presenter->config["VATTYPE_".($row->orivatid > 0 ? $row->orivatid : 0)]}</font></td>
									<td style="border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{($row->oriprice*$row->oriqty-$row->oridisc)|formatPrice:$curId}</font></td>
								</tr>
								{/foreach}
							</table>

							<table width="19cm" style="border-collapse:collapse" border="0" cellspacing="0" cellpadding="2" align="center">
								<tr>
									<td style="font-weight: bold"  width="40%" align="left"><font size="2">{_'Celková cena s DPH'}</font></td>
									<td width="15%">&nbsp;</td>
									<td width="15%">&nbsp;</td>
									<td width="15%">&nbsp;</td>
									<td style="font-weight: bold;" width="15%" align="right"><font size="2">{$order->ordpricevat|formatPrice:$curId:2}</font></td>
								</tr>
							</table>
              </td>
					</tr>
					<tr>
						<td width="19cm"><table width="19cm" style="border-collapse:collapse;margin-bottom: 10px;" cellspacing="0" cellpadding="0">

									<tr bgcolor="#e6e6e6">
										<td style="padding: 0.5em "  align="right" width="50%"><font face="Arial" size="2"><strong><big>{_'Celkem k úhradě'}:</big></strong></font> <font face="Arial" size="2"><strong><big>{$order->ordpricevat|formatPrice:$curId:2}</big></strong></font></td>
									</tr>

							</table></td>
					</tr>
			</table>
