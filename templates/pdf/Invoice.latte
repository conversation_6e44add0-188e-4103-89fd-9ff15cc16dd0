<table cellspacing="0" cellpadding="0" width="19cm"  border="0">
	<tr>
		<td width="10cm"><img src="{$baseUri}/img/faktura-logo.jpg" height="80"></td>
    <td><p align="right"><font size="4"><b>Faktura - daňový doklad číslo: {$order->ordinvcode}</b></font></p></td>
	</tr>
 </table>
		<table width="19cm" border="0" style="border-collapse:collapse;margin-top:10px;"  align="center">
				<tr>
					<td style="border: 1px solid #c0c0c0;" valign="top" width="50%"><p><font size="2">{_'Dodavatel'}:<br>
							{$presenter->config["INVOICE_VENDOR_R1"]}<br>
              {$presenter->config["INVOICE_VENDOR_R2"]}<br>
              {$presenter->config["INVOICE_VENDOR_R3"]}<br>
              {$presenter->config["INVOICE_VENDOR_R5"]}<br>
              Bankovní spojení: {$presenter->config["SERVER_ACCNO"]}<br>

              {$presenter->config["INVOICE_VENDOR_R4"]}<br>
              E-mail: {$presenter->config["SERVER_MAILORDERS"]}<br>
              <br>
              {$presenter->config["INVOICE_VENDOR_R6"]}
              </font></p></td>
					<td style="border: 1px solid #c0c0c0;" colspan="2" valign="top"><p><font size="2">{_'Odběratel'}: <br>
							{if !empty($order->ordifirname)}{$order->ordifirname}<br />{/if}
              {$order->ordiname} {$order->ordilname}<br />
							{$order->ordistreet} {$order->ordistreetno}<br />
							{$order->ordipostcode}, {$order->ordicity}<br />
							<br />
							{if !empty($order->ordic)}{_'IČ'}:{$order->ordic}{/if} {if !empty($order->orddic)}{_'DIČ'}:{$order->orddic}{/if}<br />
							E-mail: {$order->ordmail}<br />
							Tel.: {$order->ordtel}</font></p></td>
				</tr>
			</table>
			<table width="19cm" style="border-collapse:collapse;" border="0" align="center">
				<tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Faktura číslo'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->ordinvcode}</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Datum vytvoření'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->ordinvdate|date:'d.m.Y'}</font></td>
				</tr><tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Variabilní symbol'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->ordinvcode}</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Datum splatnosti'}:</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->datepay|date:'d.m.Y'}</font></td>
				</tr><tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Způsob platby'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$payMode->delname}</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Datum zdanitelného plnění'}:</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->ordinvdate|date:'d.m.Y'}</font></td>
				</tr><tr>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Způsob Dodání'}:</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$delMode->delname}</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2"></font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2"></font></td>

        </tr>
			</table>
			<table style="border-collapse:collapse;margin-top: 10px;" cellspacing=0 cellpadding=0 width="19cm"  align="center">

					<tr>
						<td  valign="top"><font style="font-size:12pt" face="Arial"><b>{_'Objednané zboží'}:</b></font>
							<table style="background:#fff;border-collapse:collapse;margin-top: 10px;" width="19cm" border="0" cellspacing="0" cellpadding="2" align="center">
								<tr>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="left"><font size="2">{_'Kód'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="40%" align="left"><font size="2">{_'Název'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{_'Kusů'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{$presenter->config["CURR_CODE"]} / {_'kus'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{_'Sleva'}</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{_'Sazba DPH'}</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{$presenter->config["CURR_CODE"]} s DPH</font></td>
								</tr>
								{php $sum = 0;}
								{foreach $ordItems as $row}
								{php $sum += ($row->oriprice*$row->oriqty-$row->oridisc);}
								<tr>
                  <td style="border-bottom: 1px solid #e0e0e0;"  width="15%" align="left"><font size="2">{$row->procode}</font></td>
									<td style="border-bottom: 1px solid #e0e0e0;"  width="40%" align="left"><font size="2">{$row->oriname} {if !empty($row->orisn)}<br />SN: {php echo str_replace("|", ", ", $row->orisn)} {/if}</font></td>
									<td style="border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{$row->oriqty}</font></td>
									<td style="border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{$row->oriprice|formatPrice:$curId}</font></td>
                  <td style="border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{$row->oridisc|formatPrice:$curId}</font></td>
                  <td style="border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{$presenter->config["VATTYPE_".$row->orivatid]}</font></td>
									<td style="border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">{($row->oriprice*$row->oriqty-$row->oridisc)|formatPrice:$curId}</font></td>
								</tr>
								{/foreach}
							</table>

							<table width="19cm" style="border-collapse:collapse" border="0" cellspacing="0" cellpadding="2" align="center">
								<tr>
									<td style="font-weight: bold"  width="40%" align="left"><font size="2">{_'Celková cena s DPH'}</font></td>
									<td width="15%">&nbsp;</td>
									<td width="15%">&nbsp;</td>
									<td width="15%">&nbsp;</td>
									<td style="font-weight: bold;" width="15%" align="right"><font size="2">{$order->ordpricevat|formatPrice:$curId:2}</font></td>
								</tr>
							</table>
              <br />
              <p style="text-align: right"><small>Základem pro výpočet daně je cena s daní.</small></p>
              <table style="width: 200mm; text-align: center; margin-left: 90mm;" class="border">
                <tr>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="center"><font size="2">Sazba</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">bez daně</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">DPH</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;"  width="15%" align="right"><font size="2">s daní</font></td>
                </tr>
                {foreach $vatSumaryPro as $key=>$rowi}
                {php
                  $vatSumaryPro[$key]['price'] += $vatSumaryDel[$key]['price'];
                  $vatSumaryPro[$key]['pricevat'] += $vatSumaryDel[$key]['pricevat'];
                  $vatSumaryPro[$key]['vat'] += $vatSumaryDel[$key]['vat'];
                }
                <tr>
                  <td align="center"><font size="2">{$vatSumaryPro[$key]['vatLevel']}%</font></td>
                  <td align="right"><font size="2">{$vatSumaryPro[$key]['price']|formatPrice:$curId:2}</font></td>
                  <td align="right"><font size="2">{$vatSumaryPro[$key]['vat']|formatPrice:2:',':' '}</font></td>
                  <td align="right"><font size="2">{$vatSumaryPro[$key]['pricevat']|formatPrice:2:',':' '}</font></td>
                </tr>
                {/foreach}
                <tr>
                  <td align="center"><font size="2">osvobozeno</font></td>
                  <td align="right"><font size="2"></font></td>
                  <td align="right"><font size="2"></font></td>
                  <td align="right"><font size="2">{0|formatPrice:2:',':' '}</font></td>
                </tr>
                {php
                  $sumNoVat = $vatSumaryPro[0]['price'] + $vatSumaryPro[1]['price'];
                  $sumIncVat = $vatSumaryPro[0]['pricevat'] + $vatSumaryPro[1]['pricevat'];
                  $sumVat = $vatSumaryPro[0]['vat'] + $vatSumaryPro[1]['vat'];
                }
                <tr>
                  <td style="font-weight: bold;border-top: 1px solid #e0e0e0;" align="center"><font size="2"><strong>Celkem</font></td>
                  <td style="font-weight: bold;border-top: 1px solid #e0e0e0;" align="right"><font size="2">{$sumNoVat|formatPrice:$curId:2}</font></td>
                  <td style="font-weight: bold;border-top: 1px solid #e0e0e0;" align="right"><font size="2">{$sumVat|formatPrice:$curId:2}</font></td>
                  <td style="font-weight: bold;border-top: 1px solid #e0e0e0;" align="right"><font size="2">{$sumIncVat|formatPrice:$curId:2}</font></td>
                </tr>
               </table>
              </td>
					</tr>
					<tr>
						<td width="19cm"><table width="19cm" style="border-collapse:collapse;margin-bottom: 10px;" cellspacing="0" cellpadding="0">

									<tr bgcolor="#e6e6e6">
										<td style="padding:0.5em" align="left" width="50%" bgcolor="#e6e6e6"><font face="Arial" size="2"><strong><big>&nbsp; <b>Dodavatel je plátce DPH</b></big></strong></font></td>

										<td style="padding: 0.5em "  align="right" width="50%"><font face="Arial" size="2"><strong><big>{_'Celkem k úhradě'}:</big></strong></font> <font face="Arial" size="2"><strong><big>{$order->ordpricevat|formatPrice:$curId:2}</big></strong></font></td>
									</tr>

							</table></td>
					</tr>
			</table>
