{if $paginator->pageCount > 1}
<nav class="pagination">
	{if $paginator->page > 1}
	<a href="{link $handle, 'page' => $paginator->page - 1}" class="pagination__prev{if $useAjax} ajax{/if}">Předchozíí</a>
	{/if}
	{foreach $steps as $step}
	{if $step == $paginator->page}
		<span class="paginator__current">{$step}</span>
	{else}
		<a href="{link $handle, 'page' => $step}" n:class="$useAjax ? ajax">{$step}</a>
	{/if}
	{if $iterator->nextValue > $step + 1}<span class="paginator__space">…</span>{/if}
	{/foreach}
	<a href="{link $handle, 'page' => $paginator->page + 1}" class="pagination__next{if $useAjax} ajax{/if}">Následující</a>
</nav>
{/if}
Ce<PERSON><PERSON>: {$paginator->itemCount}