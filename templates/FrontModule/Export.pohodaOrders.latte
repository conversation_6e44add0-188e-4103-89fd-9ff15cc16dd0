<?xml version="1.0" encoding="utf-8"?>
<dat:dataPack id="{$order->ordid}" ico="28883748" application="eshop" version = "2.0" note="Import Objednávky"
    xmlns:dat="http://www.stormware.cz/schema/version_2/data.xsd"
	  xmlns:ord="http://www.stormware.cz/schema/version_2/order.xsd"
	  xmlns:typ="http://www.stormware.cz/schema/version_2/type.xsd"
>
{php
	$storeName = "";
	$stores = [
		'store' => 'Kobylisy',
		'shop1' => 'Strašnice',
		'shop2' => 'Vinohrady'
	];

	if (isset($stores[$store])) {
		$storeName = $stores[$store];
	}


	$contracts = [
		'store' => 'Sklad',
		'shop1' => 'P-10',
		'shop2' => 'P-2',
	];

	if (isset($contracts[$store])) {
		$contractName = $contracts[$store];
	}

	if (!empty($delModeSpec)) {
		$order->ordnote = "Ulozenka: " . $delModeSpec->uloname . ' (' . $delModeSpec->uloshortcut . ')' . (!empty($order->ordnote) ? "\nPoznámka: " : "") . $order->ordnote;
	}

	if (!empty($order->ordmalid)) {
		$order->ordnote = "MALL:" . $order->ordmalid ."\n" . $order->ordnote;
	}

	if (!empty($order->ordnote)) {
		$order->ordnote = substr($order->ordnote, 0, 235);
	}
}

<dat:dataPackItem id="{$order->ordid}" version="2.0">
		<ord:order version="2.0">
			<ord:orderHeader>
				<ord:orderType>receivedOrder</ord:orderType>
				<ord:numberOrder>{$order->ordcode}</ord:numberOrder>
				<ord:date>{$order->orddatec|date:'Y-m-d'}</ord:date>
				<ord:dateFrom>{$order->orddatec|date:'Y-m-d'}</ord:dateFrom>
				<ord:dateTo>{$order->orddatec|date:'Y-m-d'}</ord:dateTo>
				<ord:text>{$order->ordnote}</ord:text>
				<ord:partnerIdentity>
					{if $order->ordusrid > 0}
					<typ:extId>
	          <typ:ids>{$order->ordusrid}</typ:ids>
	          <typ:exSystemName>eshop</typ:exSystemName>
	          <typ:exSystemText>goldfitness.cz</typ:exSystemText>
	        </typ:extId>
					{else}
					<typ:address>
						{php
						  $company = (empty($order->ordifirname) ? $order->ordilname.' '.$order->ordiname : $order->ordifirname);
						  $company = substr($company, 0, 32);
						  $name = (!empty($order->ordifirname) ? $order->ordilname.' '.$order->ordiname : '');
						  $name = substr($name, 0, 32);
						}
						<typ:company>{$company}</typ:company>
						<typ:name>{$name}</typ:name>
						<typ:city>{$order->ordicity}</typ:city>
						<typ:street>{$order->ordistreet} {$order->ordistreetno}</typ:street>
						<typ:zip>{$order->ordipostcode}</typ:zip>
						<typ:ico>{$order->ordic}</typ:ico>
						<typ:dic>{$order->orddic}</typ:dic>
						<typ:mobilPhone>{$order->ordtel}</typ:mobilPhone>
						<typ:email>{$order->ordmail}</typ:email>
					</typ:address>
					{/if}
					{if !empty($order->ordstlname)}
					{php
						  $company = (empty($order->ordstfirname) ? $order->ordstlname.' '.$order->ordstname : $order->ordstfirname);
						  $company = substr($company, 0, 32);
						  $name = (!empty($order->ordstfirname) ? $order->ordstlname.' '.$order->ordstname : '');
						  $name = substr($name, 0, 32);
					}
					<typ:shipToAddress>
						<typ:company>{$company}</typ:company>
						<typ:name>{$name}</typ:name>
						<typ:city>{$order->ordstcity}</typ:city>
						<typ:street>{$order->ordststreet} {$order->ordststreetno}</typ:street>
						<typ:zip>{$order->ordstpostcode}</typ:zip>
						<typ:phone>{$order->ordtel}</typ:phone>
						<typ:email>{$order->ordmail}</typ:email>
					</typ:shipToAddress>
					{/if}
				</ord:partnerIdentity>
				<ord:paymentType>
					<typ:ids>{$payIds}</typ:ids>
					<typ:paymentType>{$payType}</typ:paymentType>
				</ord:paymentType>
				<ord:carrier>
					<typ:ids>{$carrierId}</typ:ids>
				</ord:carrier>
				<ord:priceLevel>
					<typ:ids>{$priceLevel}</typ:ids>
				</ord:priceLevel>
				<ord:contract>
					<typ:ids>{$contractName}</typ:ids>
				</ord:contract>
			</ord:orderHeader>
			<ord:orderDetail>
				{foreach $ordItems as $row}
				{php
					$rateVAT = 'high';
					if ((int)$row->orivatid === 1) $rateVAT = 'low';
					if ((int)$row->orivatid === 2) $rateVAT = 'third';
					if ((int)$row->orivatid === 3) $rateVAT = 'none';

					$proCode = trim($row->procodep);
				}
				{if ($row->oritypid == 1)}
					{php
						$proCode = $delIds;
					}
				{elseif ($row->oritypid == 6)}
					{php
						$priceInt = abs((int)$row->oriprice);
						$proCode = $priceInt;
						if (isset($couponCodes[$priceInt])) {
							$proCode = $couponCodes[$priceInt];
						}
					}
				{/if}

				<ord:orderItem>
					<ord:text>{$row->oriname}</ord:text>
					<ord:quantity>{$row->oriqty}</ord:quantity>
					<ord:delivered>0</ord:delivered>
					<ord:rateVAT>{$rateVAT}</ord:rateVAT>
					<ord:payVAT>true</ord:payVAT>
					{if $row->oridiscper > 0}<ord:discountPercentage>{$row->oridiscper}</ord:discountPercentage>{/if}
					{if $order->ordcurid == 1}
					<ord:homeCurrency>
						<typ:unitPrice>{$row->oriprice}</typ:unitPrice>
					</ord:homeCurrency>
					{else}
					<ord:homeCurrency>
						<typ:unitPrice>{round($row->oriprice * $order->ordcurrate)}</typ:unitPrice>
					</ord:homeCurrency>
					{/if}
					{if $proCode != ''}
					<ord:stockItem>
						<typ:store>
							<typ:ids>{$storeName}</typ:ids>
						</typ:store>
						<typ:stockItem>
							<typ:ids>{$proCode}</typ:ids>
						</typ:stockItem>
					</ord:stockItem>
					{/if}
				</ord:orderItem>
				{/foreach}
			</ord:orderDetail>
			<ord:orderSummary>
				{if $order->ordcurid == 2}
				<ord:foreignCurrency>
					<typ:currency>
						<typ:ids>EUR</typ:ids>
					</typ:currency>
					<typ:rate>{$order->ordcurrate}</typ:rate>
					<typ:amount>1</typ:amount>
					<typ:priceSum>{$order->ordpricevat}</typ:priceSum>
				</ord:foreignCurrency>
				{/if}
			</ord:orderSummary>
		</ord:order>
	</dat:dataPackItem>
</dat:dataPack>
