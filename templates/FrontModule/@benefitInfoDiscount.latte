{* SLEVA - nen<PERSON> *}
{ifset $basket->discountNext}
<p class="order-benefits__item">
  <span class="icon icon--percentage">
    <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <use xlink:href="{$baseUri}/img/icons.svg#percentage" x="0" y="0" width="100%" height="100%"></use>
    </svg>
  </span>
  {_'Sleva'} {$basket->discountNext['percent']}%
  <small>{_'objednejte ještě za'} {$basket->discountNext['valueRem']|formatPrice:$curId}</small>
</p>
{php
  $disFrom = empty($basket->discountNext['disFrom']) ? 0 : $basket->discountNext['disFrom'];
  $valRem = empty($basket->discountNext['valueRem']) ? 0 : $basket->discountNext['valueRem'];
  $per = 0;
  if ($disFrom > 0) {
    $per = round(($disFrom - $valRem) / $disFrom * 100);
  }

}
<span class="progress"><span class="progress__bar" style="width:{$per}%;"></span></span>
{/ifset}
{* SLEVA - je dosaženo *}
{if $basket->discountPer > 0}
<p class="order-benefits__item">
  <span class="icon icon--percentage">
    <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <use xlink:href="{$baseUri}/img/icons.svg#percentage" x="0" y="0" width="100%" height="100%"></use>
    </svg>
  </span>
  {_'Sleva'} {$basket->discountPer}%
  <small>{_'na váš nákup'}</small>
</p>
<span class="progress"><span class="progress__bar" style="width:100%;"></span></span>
{/if}