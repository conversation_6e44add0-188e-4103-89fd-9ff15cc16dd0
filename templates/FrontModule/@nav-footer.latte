<div class="nav-footer">

  <div class="container">

    <div class="row">

      {ifset $menuShopInformation}
      <div class="col col--6">

        <h2>Vše o nákupu</h2>

        <ul>
          {foreach $menuShopInformation as $row}
          <li><a href="{plink Page:detail, $row->pagid, ($row->pagurlkey|getUrlKey:$row->pagname)}">{$row->menname}</a></li>
          {/foreach}
        </ul>

      </div>
      {/ifset}

      {foreach $menuCatalog as $catRow}

        {if $catRow->catid != 118 && $catRow->catid != 74}
      <div class="col col--6">

        <h2>{$catRow->catname}</h2>

        <ul>
          {if isset($catRow->subItems) && count($catRow->subItems) > 0}
            {foreach $catRow->subItems as $row}
              {if $iterator->getCounter() <= 5}
          <li><a href="{plink Catalog:detail $row->catid, ($row|getCatKey), NULL}">{$row->catname|truncate:30}</a></li>
              {/if}
            {/foreach}
            {if count($catRow->subItems) > 5}
            <li><a href="{plink Catalog:detail $catRow->catid, ($catRow|getCatKey), NULL}" class="nav-footer__link">Zobrazit vše z kategorie</a></li>
            {/if}
          {/if}
        </ul>
      </div>
        {/if}

      {/foreach}

      {if !empty($footerTopProducts)}
      <div class="col col--6">

        <h2>{_'Nesledovanější'}</h2>
        <ul>
          {foreach $footerTopProducts as $key => $row}
            {if $iterator->getCounter() <= 6}
          <li><a href="{plink Product:detail, $row->proid, ($row|getProKey)}">{$row->proname|truncate:32}</a></li>
            {/if}
          {/foreach}
        </ul>

      </div>
      {/if}

    </div>

  </div>

</div>
