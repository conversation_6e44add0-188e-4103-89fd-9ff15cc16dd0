{$pageTitle  = 'Objednávka '.$order->ordcode}
{$pageRobots = "nofollow,noindex"}

{* Měření konverze seznam do head *}
{block addToHead}
{if $order->ordpublished != 1}
<script type="text/javascript" src="https://c.seznam.cz/js/rc.js"></script>
<script>
    var conversionConf = {
        zboziId: {$zboziConfig["IdMereniKonverzi"]}, // ID provozovny na Zboží
        orderId: {$order->ordcode},  // Číslo objednávky
        zboziType: "standard", // Typ měření konverzí Zboží.cz, pro testovací režim uvádějte "sandbox"
        value: {round($order->ordpricevat)}, // Hodnota objednávky v Kč (pro měření konverzí pro Sklik)
        consent: {$order->ordzbozigdpr == 1 ? 0 : 1}, // Souhlas od návštěvníka na odeslání konverzního hitu
    };

    // Ujistěte se, že metoda existuje, předtím než ji zavoláte
    if (window.rc && window.rc.conversionHit) {
        window.rc.conversionHit(conversionConf);
    }
</script>
{/if}
{/block}

{block #content}

<div class="content-header">

  <div class="container center">
    {if $order->ordpublished != 1}
      {include @gtagGa3Event.latte type=>"purchase", order=>$order}
      {include @FbCapiEvent.latte type=>"Purchase", order=>$order}

      <h1>
        <span class="badge is-active">Číslo objednávky {$order->ordcode}</span><br>
        Děkujeme za Vaši objednávku.
      </h1>

      <p class="content-header__description">
        Vaše objednávka byla přijata a již pracujeme na jejím vyřízení. Nyní prosím vyčkejte na další informace.
      </p>
    {else}
    <h1>
      <span class="badge is-active">Číslo objednávky {$order->ordcode}</span><br>
      Vaše objednávka je ve stavu: <strong>{$enum_ordstatus[$order->ordstatus]|lower}</strong>.
    </h1>

    <p class="content-header__description">
      {if $order->ordpaystatus === 1}
        Objednávka je <strong>uhrazena.</strong>
      {/if}
    </p>

    {/if}

  </div>

</div>

<section class="section section--short">

  <div class="container container--short">

    <h3>V balíčku vám příjde</h3>

    {foreach $order->items as $row}
    <article class="product product--top">

      <div class="product__image">

        {include product/@imageMaster.latte product=>$row}

      </div>

      <div class="product__content">
        {php $names = ($row|getProNameParts)}
        <h3>
          {$names[0]}
        </h3>
        <p class="product__description">
          {$names[1]}
        </p>
      </div>
      <p class="product__meta">
        <span class="product__count">
        {$row->oriqty} ks
        </span>
        <span class="product__price">
          <strong>{($row->oriprice*$row->oriqty)|formatPrice:$curId}</strong>
        </span>
      </p>

    </article>

    {/foreach}

    {if !empty($discounts)}
      {foreach $discounts as $discount}
    <article class="product product--top">

      <div class="product__image">

        <p><img src="{$baseUri}/img/gift.svg" alt="" width="80" height="80"></p>

      </div>

      <div class="product__content">
        <h3>
          {$discount->oriname}
        </h3>
      </div>
      <p class="product__meta">
        <span class="product__price">
          <strong>{$discount->oriprice|formatPrice:$curId}</strong>
        </span>
      </p>

    </article>
      {/foreach}
    {/if}

    {if !empty($ordDelRow)}
    <article class="product product--top">

      <div class="product__image">

        <p><img src="{$baseUri}/img/delivery/rozvoz-praha.png" alt="Doprava a platba"></p>

      </div>

      <div class="product__content">
        <h3>
          {$ordDelRow->oriname}
        </h3>
      </div>
      <p class="product__meta">
        <span class="product__price">
          <strong>
          {if $ordDelRow->oriprice == 0}
          zdarma
          {else}
          {$ordDelRow->oriprice|formatPrice:$curId}
          {/if}
          </strong>
        </span>
      </p>

    </article>
    {/if}

    <div class="order-summary">

      <div class="row">

        <div class="col col--2">

          <article class="product product--top product--sum">

            <div class="product__image product__image--small">

              <p><img src="{$baseUri}/{$delivery->delcode|getDeliveryLogo}" alt="{$payment->delname}"></p>

            </div>

            <div class="product__content">
              <h3>
                {$delivery->delname}
                {if !empty($order->orddelspectext)}
                <br><small>Odběrné místo: {$order->orddelspectext}</small>
                {/if}
              </h3>
            </div>
            <p class="product__meta">
              <span class="product__price">
                {*
                <strong>
                {if $delivery->delprice == 0}
                zdarma
                {else}
                {$delivery->delprice|formatPrice:$curId}
                {/if}
                </strong>
                *}
              </span>
            </p>

          </article>

          <article class="product product--top product--sum">

            <div class="product__image product__image--small">

              <p><img src="{$baseUri}/{$payment->delcode|getDeliveryLogo}" alt="{$payment->delname}"></p>

            </div>

            <div class="product__content">
              <h3>
                {$payment->delname}
              </h3>
            </div>
            <p class="product__meta">
              <span class="product__price">
                {*
                <strong>
                {if $payment->delprice == 0}
                zdarma
                {else}
                {$payment->delprice|formatPrice:$curId}
                {/if}
                </strong>
                *}
              </span>
            </p>

          </article>

        </div>

        <div class="col col--2">

          <div class="order-sum">

            <p>
              Cena celkem
              <strong>{$order->ordpricevat|formatPriceByCurId:$order->ordcurid}</strong>
            </p>

            {*
            <p class="order-sum__note">
              Cena bez DPH
              <strong>2 123 Kč</strong>
            </p>
            *}

          </div>

        </div>

      </div>

      <div class="row">
        <div class="col">
        {if $payment->delcode == 'paybefore'}
            <p>Zvolili jste platbu <strong>bankovním převodem</strong>. Zde jsou Vaše platební údaje:</p>
            <p>
            Číslo účtu: {if $order->ordcurid==1}{$presenter->config["SERVER_ACCNO"]}{/if}<br />
            {if $order->ordcurid==2}IBAN: CZ70 5500 0000 0021 0600 5044<br />{/if}
            Variabilní symbol: {$order->ordcode}<br />
            Částka: {$order->ordpricevat|formatPriceByCurId:$order->ordcurid}
            </p>
            {if $order->ordcurid!=2}
            <p>
            <img width="150" src="{link qrCode, id => $order->ordid.substr(md5($order->ordid.$order->orddatec->getTimestamp()), 0, 8)}" alt="QR platba">
            </p>
            {/if}

        {/if}
        {if $payment->delcode === 'payonline' && $order->ordpaystatus === 0}
          <br />
          <p>Zvolili jste on-line platbu <strong>PayU</strong>. Pro dokončení objednávky likněte prosím na Zaplatit. </p>
          <p><br><a style="background-color: #e9b700; color: black; font-weight: 400; text-decoration: none; font-size: 18px;padding: 10px 25px;" href="{plink PayU:create, $order->ordid, md5($order->ordid . $order->orddatec)}">Zaplatit</a><br><br></p>
            {if $order->ordpublished != 1}
            <p>Za 10s budete přesměrování na PayU automaticky.</p>
            <script type="text/JavaScript">
                setTimeout("location.href = '" + {plink //:Front:PayU:create, $order->ordid, md5($order->ordid . $order->orddatec)} + "'",10000);
            </script>
            {/if}
        {elseif $payment->delcode === 'mallpay' && $order->ordpaystatus === 0}
          <p>Zvolili jste <strong>MallPay</strong>. Pro dokončení objednávky likněte prosím na Zaplatit. </p>
          <p><br><a style="background-color: #e9b700; color: black; font-weight: 400; text-decoration: none; font-size: 18px;padding: 10px 25px;" href="{plink MallPay:create, $order->ordid, md5($order->ordid . $order->orddatec)}">Zaplatit</a><br><br></p>
          {if $order->ordpublished != 1}
            <p>Za 10s budete přesměrování na MallPay automaticky.</p>
            <script type="text/JavaScript">
                setTimeout("location.href = '" + {plink MallPay:create, $order->ordid, md5($order->ordid . $order->orddatec)} + "'",10000);
            </script>
          {/if}
        {/if}
        </div>
      </div>

      <div class="row">

        <div class="col col--4">

          <h3>Osobní údaje</h3>
          <p>
            {$order->ordiname} {$order->ordilname}<br>
            {$order->ordmail}<br>
            {$order->ordtel}
          </p>

        </div>

        {if !empty($order->ordststreet)}
        <div class="col col--4">

          <h3>Dodací adresa</h3>
          <p>
            {$order->ordststreet} {$order->ordststreetno}<br>
            {$order->ordstcity}, {$order->ordstpostcode}<br>
            {if !empty($order->ordstcouid)}
            {$enum_countries[$order->ordstcouid]}
            {/if}
          </p>

        </div>
        {/if}

        <div class="col col--4">

          <h3>Fakturační {if empty($order->ordststreet)}a dodací{/if} adresa</h3>
          <p>
            {$order->ordistreet} {$order->ordistreetno}<br>
            {$order->ordicity}, {$order->ordipostcode}<br>
            {if !empty($order->ordicouid)}
            {$enum_countries[$order->ordicouid]}
            {/if}
          </p>

        </div>

        {if !empty($order->ordifirname)}
        <div class="col col--4">

          <h3>Firemní údaje</h3>
          <p>
            {$order->ordifirname}<br>
            IČ: {$order->ordic}<br>
            DIČ: {$order->orddic}
          </p>

        </div>
        {/if}

      </div>

      {if !empty($order->ordnote)}
      <h3>Poznámka</h3>
      <p>
        {$order->ordnote}
      </p>
      {/if}
    </div>

  </div>

</section>

{include article/@articles.latte articles => $footerArticles}

{include @shop.latte}

{include @instagram.latte}

{/block}
