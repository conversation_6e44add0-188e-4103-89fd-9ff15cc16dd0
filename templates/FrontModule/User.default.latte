{$pageTitle  = '<PERSON><PERSON><PERSON>'}
{$pageRobots = "nofollow,noindex"}

{block #content}
    <div class="content-header">

      <div class="container">

        {* sestavím drobky z katalogové cesty *}
        {php
          $breadCrumbs = [];

          $breadCrumbs[] = [
            "url"   => "",
            "title" => $pageTitle
          ];
        }

        {include @breadcrumb.latte items => $breadCrumbs}

        <h1>{$pageTitle}</h1>

      </div>

    </div>

    <section class="section section--short">

      <div class="container">

        {include user/@menu.latte}

  {if $userRow->usrmailverified === 0}
        <h2>Ověření Vaší registrace</h2>
        <div class="hey">
          <p>
            K dokončení registrace je nutné ověřit Váš email. Vyhledejte naši žádost o ověření na emailové adrese, kterou jste zadali při registraci ({$userRow->usrmail}) a postupujte podle pokynů v tomto emailu.<br>
            <br>
            Pokud jste nedostali ověřovací email, nejprve zkontrolujte, zda jste zadali email správně. Pokud je email správně, klikněte na <a href="{plink sendVerification}">poslat znovu ověřovací email</a>. Email bude zaslán na adresu <strong>{$userRow->usrmail}</strong>.<br>
            <br>
            Zároveň také zkontrolujte, zda tento email nezapadl do nevyžádané pošty (SPAM).<br>
            <br>
            Procedura ověření emailu je pro Vaši bezpečnost. Děkujeme za spolupráci.
          </p>
        </div>


        {form setVerifyCodeForm class=>"form"}
          {include @formErrors.latte form=>$form}

          <p>
            {label usrmailvcode}Ověřovací kód <span class="form__required">*</span> <em>(byl Vám zaslán emailem)</em>{/label}
            {input usrmailvcode size=>6}<br>
            {input submit}
          </p>
        {/form}

  {/if}

  <h2>{_'Objednávky'}</h2>

  {if count($openedOrders) > 0}
  {foreach $openedOrders as $row}
  {if $iterator->isFirst()}
  <div class="table__wrapper">
    <table class="table" cellpadding="0" cellspacing="0">
    <tr>
      <th>Číslo</th>
      <th>Datum</th>
      <th>Cena</th>
      <th>Stav</th>
      <th></th>
    </tr>
  {/if}
    <tr>
      <td><a href="{plink order $row->ordid}">{$row->ordcode}</a></td>
      <td>{$row->orddatec|date:'%d.%m.%Y'}</td>
      <td>{$row->ordpricevat|formatPrice:$curId}</td>
      <td><span class="tag">{$enum_ordstatus[$row->ordstatus]}</span></td>
      <td> <a href="{plink copyOrder, $row->ordid}">kopírovat objednávku</a> </td>
    </tr>
  {if $iterator->isLast()}
    </table>
  </div>
  {/if}
  {/foreach}
  {else}
  <p>{_'Žádné otevřené objednávky'}</p>
  {/if}

  <h2>{_'Vyřízené objednávky'}</h2>

  {if count($closedOrders) > 0}
  {foreach $closedOrders as $row}
  {if $iterator->isFirst()}
  <div class="table__wrapper">
    <table class="table" cellpadding="0" cellspacing="0">
    <tr>
      <th>Číslo</th>
      <th>Datum</th>
      <th>Cena</th>
      <th>Stav</th>
      <th></th>
    </tr>
  {/if}
    <tr>
      <td><a href="{plink order $row->ordid}">{$row->ordcode}</a></td>
      <td>{$row->orddatec|date:'%d.%m.%Y'}</td>
      <td>{$row->ordpricevat|formatPrice:$curId}</td>
      <td><span class="tag">{$enum_ordstatus[$row->ordstatus]}</span></td>
      <td> <a href="{plink copyOrder, $row->ordid}">kopírovat objednávku</a> </td>
    </tr>
  {if $iterator->isLast()}
    </table>
  </div>
  {/if}
  {/foreach}
  {else}
  <p>{_'Žádné uzavřené objednávky'}</p>
  {/if}


    </section>

    {include article/@articles.latte articles => $footerArticles}

    {include @shop.latte}

    {include @instagram.latte}
{/block}
