{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<SHOP>
{foreach $rows as $row}
  {php
    if (!empty($row["master"])) {
      $fileName = ($row["master"]->propicname != "" ? trim($row["master"]->propicname).'.jpg' : $row["master"]->procode.'.jpg');
    } else {
      $fileName = ($row->propicname != "" ? trim($row->propicname).'.jpg' : $row->procode.'.jpg');
    }
    $fileName = rawurlencode($fileName);

    $imgVer = ($fileName|getPicTimeStamp);
  }

  {php
    $proname = ($row|getProNameAgregators);
    $proPrice = round($row->proprice1a / (1+($row->provat / 100)), 2);
  }

<SHOPITEM>
<ITEM_ID>{$row->procodep}</ITEM_ID>
<PRODUCTNO >{$row->procode}</PRODUCTNO >
{if !empty($row["master"])}<ITEMGROUP_ID>{$row["master"]->proid}</ITEMGROUP_ID>{/if}
<EAN>{$row->procode2}</EAN>
<PRODUCTNAME>{$proname}</PRODUCTNAME>
<DESCRIPTION>{$row->prodesc}</DESCRIPTION>
<MANUFACTURER>{$row->manname}</MANUFACTURER>
<URL>{plink //:Front:Product:detail, $row->proid, ($row|getProKey)}</URL>
<IMGURL>{$baseUri}/pic/product/mall/{$fileName}?v={$imgVer}</IMGURL>
<DELIVERY_DATE>{$row->proaccess}</DELIVERY_DATE>
<STOCK>{$row->proqty}</STOCK>
<PRICE_VAT>{$row->proprice1a}</PRICE_VAT>
<VAT>{$row->provat}</VAT>
<CATEGORY>{$row["params"]["MAIN_CATEGORY_ID"]}</CATEGORY>
<SEO_Prefix>{$row["params"]["PARAMETER_0005"]}</SEO_Prefix>
{foreach $row["params"] as $key => $value}
<PARAM>
<PARAM_NAME>{$key}</PARAM_NAME>
<VAL>{$value}</VAL>
</PARAM>
{/foreach}
</SHOPITEM>
{/foreach}
</SHOP>