{default $pageTitle       = 'Výsledky vyhledávání'}
{$pageDescription = $pageTitle}
{$canonicalUrl    = $presenter->link('//Search:default', array('name'=>'', 'fulltext'=>'', 's'=>'', 'o'=>'', 'm'=>array()))}
{$pageRobots = "noindex,follow"}

{block #content}

    <div class="content-header">

      <div class="container">

        {* sestavím drobky z katalogové cesty *}
        {php
          $breadCrumbs = [];
        }

        {php
          $breadCrumbs[] = [
            "url"   => "",
            "title" => $pageTitle
          ];
        }

        {include @breadcrumb.latte items => $breadCrumbs}

        <h1>{$pageTitle} {if!empty($query)}fráze "{$query}"{/if}</h1>

      </div>

    </div>

    <section class="section section--short">

      <div class="container">

        <h2>Produkty</h2>
        {snippet productsList}
        {foreach $productsData as $row}

          {if $iterator->isFirst()}
        <div class="row row--start row--align">
          {/if}

          <div class="col col--4">{include "product/@listItem.latte" product=>$row}</div>

          {if $iterator->isLast()}
        </div>
          {/if}

        {/foreach}

        {if count($productsData)===0}
        <p>Výpis neobsahuje žádné produkty.</p>
        {/if}

        <!-- pager start -->
        <div class="pager">
          {control paginator}
        </div>
        <!-- pager end -->

        {/snippet}

        {if isset($catalogsData) && count($catalogsData) > 0}
        <div class="section__search">

          <h2>Kategorie</h2>

          <ul class="row row--start row--nogap">
            {foreach $catalogsData as $row}
            <li class="col col--4">
              <a href="{plink Catalog:detail $row->catid, ($row|getCatKey)}" class="category">
                <span class="category__image">
                  {include catalog/@image.latte catalog => $row}
                </span>
                {$row->catname}
              </a>
            </li>
            {/foreach}
          </ul>

        </div>

        {/if}

      </div>

    </section>

    {include @shop.latte}

    {include @instagram.latte}
{/block}
