{default $payPrice = 0}
{default $delPrice = 0}

{foreach $productRows as $row}
  <article class="product product--top product--sum">

    <div class="product__image">

      {include product/@imageMaster.latte product=>$row, imageWidth=>80}

    </div>

    <div class="product__content">
      <h3>
        {$row|getProNameCatalog} {$row->manname}
      </h3>
      <p class="product__description">
        {include product/@stock.latte product=>$row}
      </p>
    </div>
    <p class="product__meta">
      <span class="product__price">
        {$basket->items[$row->proid]} ks<br>
        <strong>{$row->proprice * $basket->items[$row->proid]|formatPrice:$curId}</strong>
      </span>
    </p>

  </article>
{/foreach}

{if !empty($basket->coupon)}
  {* slevovy kupon *}
  <article class="product product--top product--sum">

    <div class="product__image">

      <p><img src="{$baseUri}/img/gift.svg" alt="" width="80" height="80"></p>

    </div>

    <div class="product__content">
      <h3>
        {_'Slevový kupón'} na {$basket->coupon["couvalue"]} {$basket->coupon["couvalueunit"]}
        {if $basket->coupon["coumanid"] > 0 && isset($enum_promanid[$basket->coupon["coumanid"]])} na značku {$enum_promanid[$basket->coupon["coumanid"]]} {/if}
      </h3>
      <p class="product__description">
        Zadaný kód: {$basket->coupon["coucode"]}
      </p>
    </div>
    <p class="product__meta">
      <span class="product__price">
        <strong>- {$basket->couDiscountVal|formatPrice:$curId}</strong>
      </span>
    </p>

  </article>

{/if}

{if $basket->discountVal > 0}
  {* sleva % *}
  <article class="product product--top product--sum">

    <div class="product__image">

      <p><img src="{$baseUri}/img/discount.svg" alt="" width="80" height="80"></p>

    </div>

    <div class="product__content">
      <h3>
        {_'Sleva'} {$basket->discountPer}%
      </h3>
    </div>
    <p class="product__meta">
      <span class="product__price">
        <strong>- {$basket->discountVal|formatPrice:$curId}</strong>
      </span>
    </p>

  </article>

{/if}

{* doprava *}
{if isset($delMode)}
  {php $delPrice = $delMode->delprice}

<article class="product product--top product--sum">

  <div class="product__image product__image--small">

    <p>
      <img src="{$baseUri}/{$delMode->delcode|getDeliveryLogo}" alt="{$delMode->delname}">
    </p>

  </div>

  <div class="product__content">
    <h3>
      {$delMode->delname}
      {if !empty($basket->contact["orddelspectext"])}<br><small>Odběrné místo: {$basket->contact["orddelspectext"]}</small>{/if}
    </h3>
  </div>
  <p class="product__meta">
    <span class="product__price">
      <strong>
      {if $delMode->delprice == 0}
      zdarma
      {else}
      {$delMode->delprice|formatPrice:$curId}
      {/if}
      </strong>
    </span>
  </p>

</article>
{/if}

{* platba *}
{if isset($payMode)}
  {php $payPrice = $payMode->delprice}
<article class="product product--top product--sum">

  <div class="product__image product__image--small">

    <p><img src="{$baseUri}/{$payMode->delcode|getDeliveryLogo}" alt="{$payMode->delname}"></p>

  </div>

  <div class="product__content">
    <h3>
      {$payMode->delname}
    </h3>
  </div>
  <p class="product__meta">
    <span class="product__price">

      <strong>
      {if $payMode->delprice == 0}
      zdarma
      {else}
      {$payMode->delprice|formatPrice:$curId}
      {/if}
      </strong>

    </span>
  </p>

</article>
{/if}


<div class="order-sum">

  <p>
    Cena celkem
    <strong>{MAX($basket->priceSumVat - $basket->discountVal - $basket->couDiscountVal + $payPrice + $delPrice, 0)|formatPrice:$curId}</strong>
  </p>

  {*
  <p class="order-sum__note">
    Cena bez DPH
    <strong>2 123 Kč</strong>
  </p>
  *}
</div>