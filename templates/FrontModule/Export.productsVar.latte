{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<SHOP>
{foreach $rows as $row}
<SHOPITEM>
<WARE_HOUSE>{$row->proid}</WARE_HOUSE>
<MANUFACTURER>{$row->manname}</MANUFACTURER>
<EAN>{$row->procode2|checkEan}</EAN>
<CODE>{$row->procodep}</CODE>
{php
  $proname = ($row|getProNameAgregators);
  $proPriceNoVat = round($row->$priceFieldName / (1 + $row->provat / 100), 2);
}
<PRODUCTNAME>{$proname}</PRODUCTNAME>
<PRODUCT>{$proname} {if !empty($row->progift)}({$row->progift}){/if}</PRODUCT>
<DESCRIPTION>{$row->prodescs}</DESCRIPTION>
<DESCRIPTION2>{$row->prodesc}</DESCRIPTION2>
<URL>{plink //:Front:Product:detail, $row->proid, ($row|getProKey)}</URL>
<DELIVERY>{$row->proaccess}</DELIVERY>
<PRICE_VAT>{$row->$priceFieldName}</PRICE_VAT>
<PRICE_WHOLESALE_NO_VAT>{$proPriceNoVat}</PRICE_WHOLESALE_NO_VAT>
<PRICE>{$proPriceNoVat}</PRICE>
<VAT>{$row->provat}</VAT>
{php
  if (!empty($row->proidmas)) {
    $fileName = ($row->propicnamemas != "" ? trim($row->propicnamemas).'.jpg' : $row->procodemas.'.jpg');
  } else {
    $fileName = ($row->propicname != "" ? trim($row->propicname).'.jpg' : $row->procode.'.jpg');
  }
  $fileName = rawurlencode($fileName);
}
<IMGURL>{$baseUri}/pic/product/detail/{$fileName}</IMGURL>
{php
    $catPath = "";
    if (!empty($row->catid)) {
      $catPath = str_replace('|', ' | ', $row->catpath);
    }
}
<CATEGORY>{$catPath}</CATEGORY>
{if isset($row->variants)}
  {foreach $row->variants as $variant}
  <VARIANT>
    <PRODUCTNAMEEXT>{$variant->proname}</PRODUCTNAMEEXT>
    <CODE>{$variant->procodep}</CODE>
    <EAN>{$variant->procode2|checkEan}</EAN>
    <DELIVERY>{$variant->proaccess}</DELIVERY>
  </VARIANT>
  {/foreach}
{/if}
{if isset($row->proweight) && $row->proweight > 0}
<PARAM>
  <PARAM_NAME>Hmotnost včetně obalu</PARAM_NAME>
  <VAL>{$row->proweight}</VAL>
</PARAM>
{/if}
{if !empty($proParameters[$row->proid])}
  {foreach $proParameters[$row->proid] as $param}
    {if $param->prpname == 'Rozměry obalu (cm)'}
      {php
        $arr = explode("x", $param->prpvalue);
        if (count($arr) == 3) {
          $sizes = $arr;
        }
      }
    {/if}
    {ifset $sizes}
    <PACKAGE_WIDTH>{round($sizes[0] * 10)}</PACKAGE_WIDTH>
    <PACKAGE_HEIGHT>{round($sizes[1] * 10)}</PACKAGE_HEIGHT>
    <PACKAGE_DEPTH>{round($sizes[2] * 10)}</PACKAGE_DEPTH>
    {else}
    <PARAM>
      <PARAM_NAME>{$param->prpname}</PARAM_NAME>
      <VAL>{$param->prpvalue}</VAL>
    </PARAM>
    {/ifset}
  {/foreach}
{/if}
</SHOPITEM>
{/foreach}
</SHOP>