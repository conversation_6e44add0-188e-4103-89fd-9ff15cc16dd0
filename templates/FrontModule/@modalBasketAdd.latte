{ifset $modalBasketAddProduct}
<div class="modal modal--dark is-open" id="pridano">

  <div class="modal__body">

    <h2>
      <span class="icon icon--success">
        <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <use xlink:href="{$baseUri}/img/icons.svg#success" x="0" y="0" width="100%" height="100%"></use>
        </svg>
      </span>
      Přidáno do košíku
    </h2>

    <div class="modal__close">
      <span class="icon icon--close">
        <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <use xlink:href="{$baseUri}/img/icons.svg#close" x="0" y="0" width="100%" height="100%"></use>
        </svg>
      </span>
    </div>

    <article class="product product--top">

      <div class="product__image">

        {include product/@imageMaster.latte product=>$modalBasketAddProduct, imageWidth=>80}

      </div>

      <div class="product__content">
        {php $names = ($modalBasketAddProduct|getProNameParts)}
        <h3>
          {$names[0]}
        </h3>
        <p class="product__description">
          {$modalBasketAddProduct->manname}{if !empty($names[1])} - {$names[1]}{/if}<br>
          {include product/@stock.latte product=>$modalBasketAddProduct, showQty=>TRUE}
        </p>
      </div>
      <p class="product__meta">

        <span class="product__count">

          <span class="icon icon--minus">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#minus" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>

          <input type="text" id="proQty_{$modalBasketAddProduct->proid}" class="basqty"  data-show=true data-proid={$modalBasketAddProduct->proid} value={$modalBasketAddQty}>

          <span class="icon icon--plus">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#plus" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>

        </span>

        <span class="product__price">
          <strong>{($modalBasketAddProduct->proprice*$modalBasketAddQty)|formatPrice:$curId}</strong>
        </span>

      </p>

    </article>

    {if isset($stockLimitReached[$modalBasketAddProduct->proid])}
    <div class="warn is-danger">
      Bohužel aktuálně máme skladem jen {$stockLimitReached[$modalBasketAddProduct->proid]} ks.
    </div>
    {/if}

    <div class="order-benefits order-benefits--modal">

      <div class="order-benefits__info">

        <div class="order-benefits__col">

          {include @benefitInfoDelFree.latte basket=>$basket, delfreelimit=>$delFreeLimit}

        </div>

        <div class="order-benefits__col">

          {include @benefitInfoDiscount.latte basket=>$basket}

        </div>

      </div>

    </div>

    <div class="modal__submit">

      <p>
        <a href="{$baseUri}" class="link link--back link--inverse modal__close-false">Pokračovat v nákupu</a>
      </p>

      <p>
        <a href="{plink Basket:default}" class="btn btn--big">Přejít do košíku</a>
      </p>

    </div>

  </div>

</div>

{php $gTagData[] = $modalBasketAddProduct}
{include @gtagGa3Event.latte type=>"add_to_cart", products=>$gTagData, qty=>$modalBasketAddQty}

{/ifset}
