{* nastaveni promennych *}
{$pageTitle       = $proTitle}
{$pageKeywords    = $product->prokeywords}
{$pageDescription = strip_tags($productMasterData->prodescs)}
{*$pageDescription = (empty($productMasterData->prodescription) ? strip_tags($productMasterData->prodescs) : $productMasterData->prodescription)*}
{$pageImage = $baseUri."/".($productMasterData|getProductPicName:'detail')}

{if $product->pronoindex === 1}
  {$pageRobots = 'noindex,nofollow'}
{/if}

{block #content}

{php $gTagData[] = $product}
{include @gtagGa3Event.latte type=>"view_item", products=>$gTagData}

{* ritch snippets http://schema.org/Product *}
<script type="application/ld+json">
{
  "@context": "http://schema.org",
  "@type": "Product",
  "name": {$product->proname},
  "image": {$baseUri.'/'.($productMasterData|getProductPicName:'detail')},
  "description": {$pageDescription},
  "itemCondition" : "http://schema.org/NewCondition",
  {if !empty($product->procode2)}"gtin13" : {$product->procode2},{/if}
  "manufacturer": {$manufacturer->manname},
  "url": {plink '//this'},
  "offers": {
    "@type": "Offer",
    "availability": {if $product->proaccess == 0}"http://schema.org/InStock"{else}"http://schema.org/OutOfStock"{/if},
    "price": {$product->proprice|noescape},
    "priceCurrency": "CZK"
  }
  {if !empty($product->prorating)},
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": {str_replace(',', '.', $product->prorating)|noescape},
    "reviewCount": {if empty($product->proratingcnt)}1{else}{$product->proratingcnt}{/if}
  }
  {/if}
}
</script>

    <div class="content-header content-header--product">

      <div class="container">

        {php
          $breadCrumbs = [];
        }

        {ifset $catalogPath}

          {foreach $catalogPath as $row}

            {php
              $breadCrumbs[] = [
                "url"   => $presenter->link("Catalog:detail", $row->catid, ($row|getCatKey)),
                "title" => $row->catname
              ];
            }

          {/foreach}

        {/ifset}

        {php
          $breadCrumbs[] = [
            "url"   => "",
            "title" => $product->proname
          ];
        }

        {include @breadcrumb.latte items => $breadCrumbs}

        <h1>
          {if !empty(trim($manufacturer->manname))}<a href="{plink Manufacturer:detail $manufacturer->manid, (''|getUrlKey:$manufacturer->manname)}"><span class="badge is-active">{$manufacturer->manname}</span></a><br>{/if}
          {$product->proname}
          {if $adminLogIn}<a href="{plink :Admin:Product:edit, $product->proid}" target="admin" class="admin__edit">E</a>{/if}
        </h1>

        <p class="content-header__description">
          {$productMasterData->prodescs}
        </p>

        <div class="product-photo">

          <p class="product-photo__tags">
            {include product/@tags.latte product=>$product}
          </p>

          {snippet hearth}
          {if !isset($userFavorites[$product->proid])}
          {* nemá v oblíbených - může přidat *}
          <p class="product-photo__fav">
            <a n:href="proFavAdd! $product->proid" class="ajax" rel="nofollow" title="{_'Přidat k oblíbeným'}">
              <span class="icon icon--heart">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#heart" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
            </a>
          </p>
          {else}
          {* má v oblíbených - může odebrat *}
          <p class="product-photo__fav">
            <a n:href="proFavRem! $product->proid" class="ajax" rel="nofollow" title="{_'Odebrat z oblíbených'}">
              <span class="icon icon--heart-full">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#heart-full" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
            </a>
          </p>
          {/if}
          {/snippet}

          {include product/@rating.latte product=>$product}

          {php
           $proPicVariant = "";
           if ($product->promasid > 0) $proPicVariant = ($product|getProductPicFileName:'detail':TRUE);
          }

          <p class="product-photo__main js-gallery">
            <a href="{$baseUri}/{($proPicVariant == "" ? $productMasterData : $product)|getProductPicName:'600x600'}">
              <picture>
                <source srcset="{$baseUri}/{(($proPicVariant == "" ? $productMasterData : $product)|getProductPicNameWebp:'400x400')|noescape}" type="image/webp">
                <img src="{$baseUri}/{(($proPicVariant == "" ? $productMasterData : $product)|getProductPicName:'400x400')|noescape}" alt="{$product->proname}" width="400" height="400">
              </picture>
            </a>
          </p>

          <p class="product-photo__gallery js-gallery">
            {foreach $images as $row}
            <a href="{$baseUri}/{$row["nameBody"]|getProductPicByFileName:'600x600'}">
              <picture>
                <source srcset="{$baseUri}/{$row["nameBody"]|getProductPicByFileName:'list':FALSE:'webp'}" type="image/webp">
                <img src="{$baseUri}/{$row["nameBody"]|getProductPicByFileName:'list'}" alt="{$product->proname}" width="75" height="75">
              </picture>
            </a>
            {/foreach}
            {*
            <a href="#" class="product-photo__more">
              <span class="icon icon--plus">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#plus" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
              Další fotky
            </a>
            *}
          </p>

        </div>

      </div>

    </div>

    <section class="section product-detail">

      <div class="container">

        {if count($subItems) > 0 && !$isBlocked}
          {* výpis variant *}
          {form basketAddFormVar}
          {include @formErrors.latte form=>$form}

          <h3>Vyberte variantu</h3>

          {foreach $subItems as $row}
            {php
              if ($productMasterData->proaccess == 100) $row->proaccess = $productMasterData->proaccess;
            }

            {if $iterator->getCounter() == 5}
            {* více varinat začátek *}
            <p class="product-detail__links"><a href="#varianty" class="link link--down js-reveal">Zobrazit více variant</a></p>
            <div class="reveal" id="varianty">
            {/if}

            <article class="product product--top">

              <div class="product__image">

                <p>
                  {if $row->proid != $product->proid}<a href="{plink Product:detail, $row->proid, ($row|getProKey)}">{/if}
                    {if $row->promasid > 0}
                      {php
                       $proPicVariant = ($row|getProductPicFileName:'48x48':TRUE);
                       $proPicUsrSize = ($row|getProductPicNameUsrSize:TRUE);
                      }

                      <picture>
                        <source srcset="{$baseUri}/{((empty($proPicVariant) ? $productMasterData : $row)|getProductPicNameWebp:'48x48')|noescape}" type="image/webp">
                        <img src="{$baseUri}/{((empty($proPicVariant) ? $productMasterData : $row)|getProductPicName:'48x48')|noescape}" alt="{$productMasterData->proname}" width="48" height="48">
                      </picture>


                      {if !empty($proPicUsrSize) && $proPicUsrSize !== "no.jpg"}
                        <picture class="product__flavour">
                          <img src="{$baseUri}/{($row|getProductPicNameUsrSize:'usrsize')|noescape}" alt="{$row->proname}" width="50" height="50">
                        </picture>
                      {/if}

                    {else}

                      <picture>
                        <source srcset="{$baseUri}/{($row|getProductPicNameWebp:'48x48')|noescape}" type="image/webp">
                        <img src="{$baseUri}/{($row|getProductPicName:'48x48')|noescape}" alt="{$product->proname}" width="48" height="48">
                      </picture>

                    {/if}
                  {if $row->proid != $product->proid}</a>{/if}
                </p>

              </div>

              <div class="product__content">
                <h3>
                  {$row->proname} {if $adminLogIn}<a href="{plink :Admin:Product:edit, $row->proid}" target="admin" class="admin__edit">E</a>{/if}
                </h3>
                <p class="product__description">
                  {php $cantItBuy = ($row->proaccess == 100 || $row->prostatus == 1 || $productMasterData->proaccess == 100 || $productMasterData->prostatus == 1)}

                  <a href="#dostupnost-{$row->proid}" class="store{if $cantItBuy} is-danger{else} is-success{/if} js-modal">
                    {if $cantItBuy}<strong>Není skladem{if !empty($row->proaccesstext)} ({$row->proaccesstext}){/if}</strong>{else}<strong>Skladem {$row|getQtyText}</strong>{if !empty($delTerms)}, expedujeme {$delTerms}{else} ihned k odeslání{/if}{/if}
                    <span class="icon icon--car">
                      <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="{$baseUri}/img/icons.svg#car" x="0" y="0" width="100%" height="100%"></use>
                      </svg>
                    </span>
                  </a>
                </p>
              </div>
              <p class="product__meta">
                {include product/@discount.latte product=>$row}
                <span class="product__price">
                  {if ($row|isPromo)}<del>{$row->propricecom|formatPrice:$curId}/ks</del><br>{/if}
                  <strong>{$row->proprice|formatPrice:$curId}</strong>
                </span>
                {if !$cantItBuy && $row->proaccess == 0}
                {ifset $form[$row->proid]}
                <span class="product__count">
                  <span class="icon icon--minus">
                    <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <use xlink:href="{$baseUri}/img/icons.svg#minus" x="0" y="0" width="100%" height="100%"></use>
                    </svg>
                  </span>
                  {php echo $form[$row->proid]['oricnt']->getControl()->size(3)->class('clearonfocus')}
                  <span class="icon icon--plus">
                    <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <use xlink:href="{$baseUri}/img/icons.svg#plus" x="0" y="0" width="100%" height="100%"></use>
                    </svg>
                  </span>
                </span>
                {php echo $form[$row->proid]['oribuy']->getControl()->class("btn")}
                {/ifset}
                {/if}
              </p>

            </article>

            {if $iterator->isLast() && count($subItems) > 4}
            {* více varinat konec *}
            </div>
            {/if}
          {/foreach}
          {/form basketAddFormVar}
        {/if}

        <p class="product__links">
          <a class="btn btn--soft" href="{plink Manufacturer:detail $manufacturer->manid, (''|getUrlKey:$manufacturer->manname)}"><strong>Vše od výrobce:</strong> {$manufacturer->manname}</a>
          <a class="btn btn--soft" href="{plink Catalog:detail $catalog->catid, ($catalog|getCatKey)}"><strong>Vše z kategorie:</strong> {$catalog->catname}</a>
        </p>

        {if $userRow->usrid == 0}

        <div class="hey hey--center" n:if="!$isBlocked">
          <p class="hey__icon">
            <span class="icon icon--user">
              <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <use xlink:href="{$baseUri}/img/icons.svg#user" x="0" y="0" width="100%" height="100%"></use>
              </svg>
            </span>
          </p>
          <p class="hey__info">Chcete slevu 3-10%? <a href="{plink User:add}">Zaregistrujte se</a></p>
        </div>

        {/if}

        {if $isBlocked && isset($proOptions) && count($proOptions) > 0}
          <div class="hey"><p class="hey__info">Zboží již nemáme v nabídce. Vyberte si prosím z nabídky <a href="#souvisejici">podobného zboží</a>.</p></div>
        {/if}

        <ul class="product-helper" n:snippet="productDetail" n:if="!$isBlocked">
          <li>
            <a n:href="productWatch!" class="ajax" data-naja-history="off" rel="nofollow">
              <span class="icon icon--email">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#email" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
              {_'Hlídat'}
            </a>
          </li>
          <li>
            <a href="{plink compareAdd, $product->proid}">
              <span class="icon icon--compare">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#compare" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
              {_'Porovnat'}
            </a>
          </li>
          <li>
            {if !isset($userFavorites[$product->proid])}
            {* nemá v oblíbených - může přidat *}
            <a n:href="proFavAdd! $product->proid" class="ajax" rel="nofollow" title="{_'Přidat k oblíbeným'}">
              <span class="icon icon--heart">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#heart" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
              {_'Do oblíbených'}
            </a>
            {else}
            {* má v oblíbených - může odebrat *}
            <a n:href="proFavRem! $product->proid" class="ajax" rel="nofollow" title="{_'Odebrat z oblíbených'}">
              <span class="icon icon--heart-full">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#heart-full" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
              {_'Odebrat z oblíbených'}
            </a>
            {/if}
          </li>
          <li>
            <a href="#vytisknout" class="js-print">
              <span class="icon icon--print">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#print" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
              {_'Vytisknout'}
            </a>
          </li>
        </ul>
      </div>

    </section>

    <section class="section section--nav" n:if="!$isBlocked">

      <div class="container">

        <ul class="sort">
          <li class="is-active"><a href="#popis-produktu">Popis produktu</a></li>
          {if !empty($proParams) && isset($rootCatId) && $rootCatId != 15}<li><a href="#doporucene-pouziti">Doporučené použití</a></li>{/if}
          {if !empty($productMasterData->pronutrition)}<li><a href="#nutricni-informace">Nutriční informace</a></li>{/if}
          <li><a href="#dotazy">Dotazy a komentáře</a></li>
          {ifset $proOptions}<li><a href="#souvisejici">Související</a></li>{/ifset}
        </ul>

      </div>

    </section>

    <section class="section section--detail"  n:if="!$isBlocked">

      <div class="container container--sidebar">

        <div class="section__content">
          <a class="section__anchor" id="popis-produktu"></a>
          <h2>
            {$product->proname}
            {if !empty($product->pronamecz)}
            <br><span class="badge badge--inverse">{$product->pronamecz}</span>
            {/if}
          </h2>

          {if !empty($productMasterData->prodescab) && ($userRow->usrprccat == 'a' || $userRow->usrprccat == 'b')}
            <div class="hey">{$productMasterData->prodescab|noescape}</div>
          {/if}
          {if !empty($productMasterData->prodesccd) && ($userRow->usrprccat == 'c' || $userRow->usrprccat == 'd' || $userRow->usrprccat == 'e')}
            <div class="hey">{$productMasterData->prodesccd|noescape}</div>
          {/if}
          {$productMasterData->prodesc|reformatHtmlText|noescape}

          {* youtube videa *}
          {if !empty($product->provideo)}
          <p>
            <iframe width="560" height="315" src="https://www.youtube.com/embed/{$product->provideo}" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
          </p>
          {/if}

          {if !empty($proParams) && isset($rootCatId) && $rootCatId != 15}
          <a class="section__anchor" id="doporucene-pouziti"></a>
          <h2>{_'Doporučené použití'} <em>{$product->proname}</em></h2>

          {foreach $proParams as $row}
            {if $iterator->isFirst()}
            <dl>
            {/if}
              <dt>{$row->prpname}:</dt>
              <dd>{$row->prpvalue}</dd>
            {if $iterator->isLast()}
            </dl>
            {/if}
          {/foreach}
          {/if}

          {if !empty($productMasterData->pronutrition)}
          <a class="section__anchor" id="nutricni-informace"></a>
          <h2>{_'Nutriční informace'} <em>{$product->proname}</em></h2>

          <div class="table__wrapper">
          {$productMasterData->pronutrition|reformatHtmlText|noescape}
          </div>
          {/if}

          <div class="section__divider"><hr></div>

          {if isset($proOptions) && count($proOptions) > 0}
          <a class="section__anchor" id="souvisejici"></a>
          <h2>Související zboží</h2>

          {include product/@productsTop.latte products=>$proOptions}

          <div class="section__divider"><hr></div>
          {/if}
          <a class="section__anchor" id="dotazy"></a>
          <h2>
            Dotazy a komentáře
            &nbsp;&nbsp;<a n:href="commentAdd!" class="btn ajax" data-naja-history="off">Vložit dotaz</a>
          </h2>

          {include comment/@commentList.latte comments=>$comments}

        </div>

        <aside class="sidebar">

          <h3>Potřebujete poradit?</h3>

          <div class="contact-box">
            <p>
              <span class="avatar">
                <picture>
                  <source srcset="{$baseUri}/img/main-contact.webp" type="image/webp">
                  <img src="{$baseUri}/img/main-contact.jpg" alt="" width="300" height="300" loading="lazy">
                </picture>
              </span>
            </p>
            <p>
              <strong>Michal Dimelis</strong><br>
              <a href="tel:+420603478564">+420 603 478 564</a><br>
              <small class="phone" data-start="9" data-end="17">Po-Pá 9:00 - 17:00</small><br>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
          </div>

          {if isset($saleStatsProducts) && count($saleStatsProducts)}

          <h3>Nejprodávanější</h3>

          {foreach $saleStatsProducts as $item}
            {include product/@listItem.latte product=>$item, type=>"side"}
          {/foreach}

          {/if}
        </aside>

      </div>

    </section>

    {* <!-- souvisejici zbozi start --> *}
    {if isset($proOptions) && count($proOptions) > 0}
    <a class="section__anchor" id="souvisejici"></a>
    <section class="section section--follow">

      <div class="container">

      {foreach $proOptions as $row}

        {if $iterator->isFirst()}
        <h2 class="section__title">
          {if !$isBlocked}Související zboží{else}Alternativní produkty{/if}
        </h2>

        <div class="row row--start row--align">

          {/if}
              <div class="col col--4">
                {include product/@listItem.latte product=>$row}
              </div>
          {if $iterator->isLast()}

        </div>
        {/if}

      {/foreach}

      </div>

    </section>
    {/if}

    <section class="section section--follow">

      <div class="container">

        {foreach $proVisited as $row}

          {if $iterator->isFirst()}
        <h2 class="section__title">
          Naposledy navštívené zboží
        </h2>

        <div class="splide splide--products">
          <div class="splide__track">
            <div class="splide__list">
          {/if}
              <div class="splide__slide">
                {include product/@listItem.latte product=>$row}
              </div>
          {if $iterator->isLast()}
            </div>
          </div>
        </div>
          {/if}

        {/foreach}

      </div>

    </section>

    {include article/@articles.latte articles => $footerArticles}

    {include @shop.latte}

    {include @instagram.latte}

{/block}

{* naplním modální okno s dostupností do patičky *}
{block addToFooter}
  {foreach $subItems as $row}
  <div class="modal modal--availability" id="dostupnost-{$row->proid}">
    <div class="modal__body">
    {include product/@availability.latte product=>$row}
    </div>
  </div>
  {/foreach}

  {snippet modalCommentAdd}
    {include comment/@modalAdd.latte type=>"product"}
  {/snippet}

  {snippet modalWatch}
    {include @modalWatch.latte}
  {/snippet}
{/block}