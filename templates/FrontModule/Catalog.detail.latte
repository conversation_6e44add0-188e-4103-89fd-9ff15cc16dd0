{$pageTitle       = $catNameFull.$searchWords}
{$pageKeywords    = $catalogData->catkeywords}
{$pageDescription = strip_tags($catalogData->catdesc)}

{if $catalogData->catnoindex === 1}
  {$pageRobots = 'noindex,follow'}
{/if}

{php
  $canonicalUrl = $presenter->link('//detail', array('id'=>$catalogData->catid, 'key'=> ($catalogData|getCatKey), 'path'=>$pathCanonical));
}

{php
  $o = $presenter->o;
  $filterNotEmpty = (!empty($formVals["m"]) || !empty($formVals["t"]) || !empty($formVals["f"]) || !empty($formVals["pF"]) || !empty($formVals["pT"]));
}

  {block #content}
    {php $arrs = array_merge($saleStatProducts, $productsData)}
    {include @gtagGa3Event.latte type=>"view_item_list", products=>$arrs}

    <div class="content-header"  id="tofi">

      <div class="container">

        {* sestavím drobky z katalogové cesty *}
        {php
          $breadCrumbs = [];
        }

        {foreach $catalogPath as $row}

          {php
            $breadCrumbs[] = [
              "url"   => $presenter->link("Catalog:detail", $row->catid, ($row|getCatKey)),
              "title" => $row->catname
            ];
          }
        {/foreach}

        {include @breadcrumb.latte items => $breadCrumbs}

        <h1 n:snippet="pageTitle">
          {$catalogData->catname}{if !empty($manufacturer)} - {$manufacturer->manname}{/if}
        </h1>

        <p class="content-header__description">
          {$catalogData->catdesc|noescape}
        </p>

        {if !empty($catalogSubItems)}
        <ul class="row row--start">

          {foreach $catalogSubItems as $key => $row}

          <li class="col col--4">
            <a href="{plink Catalog:detail, $row->catid, ($row|getCatKey), NULL}" class="category">
              <span class="category__image">
                {include catalog/@image.latte catalog=>$row}
              </span>
              {$row->catname}
            </a>
          </li>

          {/foreach}

        </ul>
        {/if}

      </div>

    </div>


    <section class="section section--short">

      <div class="container container--sidebar">

        <aside class="sidebar sidebar--filter" n:snippet="sideFilter">

          {form catalogSearchForm class=>"ajax"}
          {include @formErrors.latte form=>$form}

          <h3>Stav zboží</h3>

          <p>
          {foreach $form['t']->controls as $item}
            <label class="form-checkbox">
              {input $item:}
              <span class="form-checkbox__label"><a href="{plink Catalog:detail, $catalogData->catid, ($catalogData|getCatKey), ($item->caption|getCatPath:$o)}" class="js-nopass"> {$item->caption}</a></span>
              <span class="form-checkbox__checker"></span>
            </label>
            {if !$iterator->isLast()}
            <br>
            {/if}
          {/foreach}
          </p>

          <h3>Forma</h3>

          <p>
            {foreach $form['f']->controls as $item}
            <label class="form-checkbox">
              {input $item:}
              <span class="form-checkbox__label"><a href="{plink Catalog:detail, $catalogData->catid, ($catalogData|getCatKey), ($item->caption|getCatPath:$o)}" class="js-nopass"> {$item->caption}</a></span>
              <span class="form-checkbox__checker"></span>
            </label>
            {if !$iterator->isLast()}
            <br>
            {/if}
          {/foreach}
          </p>

          <h3>Výrobce</h3>

          {* TODO
          <p class="form-search">
            <input type="text" id="search" placeholder="Hledat výrobce">
            <button>
              <span class="icon icon--magnifier">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#magnifier" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
            </button>
          </p>
          *}

          <p>
            {* zjistim jestli použít reval *}
            {php $useReval = TRUE}
            {foreach $form['m']->controls as $key => $item}
              {if $iterator->getCounter() >= 5}
                {if $item->value}
                    {php $useReval = FALSE}
                {/if}
              {/if}
            {/foreach}

            {foreach $form['m']->controls as $key => $item}
              {if $iterator->getCounter() == 5 && $useReval}
            <span class="reveal" id="vyrobci">
              {/if}
              <label class="form-checkbox">
                {input $item: class=>"js-nopass"}
                <span class="form-checkbox__label"><a href="{plink Catalog:detail, $catalogData->catid, ($catalogData|getCatKey), ($item->caption|getCatPath:$o)}" class="js-nopass"> {$item->caption}</a></span>
                <span class="form-checkbox__checker"></span>
              </label>

              {if !$iterator->isLast()}
                <br>
              {else}
                {if $iterator->getCounter() >= 5 && $useReval}
            </span>
            <p class="sidebar__show">
              <a href="#vyrobci" class="link link--down js-reveal">Více možností</a>
            </p>
                {/if}
              {/if}

            {/foreach}

          </p>

          <h3>Cena</h3>
          <div class="form-range">
            <div id="range-slider" data-min="{$proMinMax->proprice_min}" data-max="{$proMinMax->proprice_max}" data-mins="{$proPriceFrom}" data-maxs="{$proPriceTo}"></div>
            <p>
              {input pF id=>"slider-min", value=>$proPriceFrom}
              až
              {input pT id=>"slider-max", value=>$proPriceTo}
              <strong>Kč</strong>
            </p>
          </div>

          {*input search class=>"btn btn--big", "value"=>"hledat"*}

          <a href="{plink this#searchForm path=>NULL, page=>1}" class="btn btn--danger btn--big" data-naja-history="replace">Vymazat filtr</a>

          {/form}
        </aside>

        <p class="filter-switch">
          <button class="btn btn--big js-filter-switch" data-switch="Skrýt">
            <em>Zobrazit</em> filtry
            <span class="icon icon--compare">
              <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <use xlink:href="{$baseUri}/img/icons.svg#compare" x="0" y="0" width="100%" height="100%"></use>
              </svg>
            </span>
          </button>
        </p>

        <div class="section__content">
          <a id="searchForm" class="section__anchor"></a>
          {if !empty($saleStatProducts)}
          <h2>{_'Nejprodávanější zboží v kategorii'} {$catNameFull.$searchWords}</h2>

          {include product/@productsTop.latte products=>$saleStatProducts, limitRows=>3}

          {/if}

          {snippet productsList}

          <ul class="sort sort--center">
            <li{if $o==''} class="is-active"{/if}>{if $o==''}Doporučujeme{else}<a href="{plink Catalog:detail, $catalogData->catid, ($catalogData|getCatKey), (''|getCatPath:$path)}#searchForm" data-naja-history="replace">Doporučujeme</a>{/if}</li>
            <li{if $o=='nejlevnejsi'} class="is-active"{/if}>{if $o=='nejlevnejsi'}Nejlevnější{else}<a href="{plink Catalog:detail, $catalogData->catid, ($catalogData|getCatKey), ('Nejlevnější'|getCatPath:$path)}#searchForm" data-naja-history="replace">Nejlevnější</a>{/if}</li>
            <li{if $o=='nejdrazsi'} class="is-active"{/if}>{if $o=='nejdrazsi'}Nejdražší{else}<a href="{plink Catalog:detail, $catalogData->catid, ($catalogData|getCatKey), ('Nejdražší'|getCatPath:$path)}#searchForm" data-naja-history="replace">Nejdražší</a>{/if}</li>
            {*<li><a href="#">Nejprodávanější</a></li>*}
          </ul>

            {foreach $productsData as $row}

              {if $iterator->isFirst()}
            <div class="row row--start row--align">
              {/if}

              <div class="col col--3">{include "product/@listItem.latte" product=>$row}</div>

              {if $iterator->isLast()}
            </div>
              {/if}

            {/foreach}
            {if count($productsData)===0}
            <p>Výpis neobsahuje žádné produkty.</p>
            {/if}

          <p class="section__pages">

          {if $isMore}<p><a n:href="nextPage! path=>$path, pages=>($page+1)" class="link link--down ajax" rel="nofollow">{$isMoreCnt|getenumText} ...</a></p>{/if}

          {if $pages > 1 || (isset($redrawFired) && $redrawFired)}
            <script>
              $(document).ready(function() { $(".product-list__image img").unveil(200); });
            </script>
          {/if}

          {include catalog/@paginator.latte}

          </p>
          {/snippet}
      </div>

    </section>

    <section class="section section--dark">

      <article class="container container--sidebar">

        <div class="section__content">
          <a class="section__anchor" id="dotazy"></a>
          <h2>
            Poradna
            &nbsp;&nbsp;<a n:href="commentAdd!" class="btn ajax" data-naja-history="off">Vložit dotaz</a>
          </h2>

          {include comment/@commentList.latte comments=>$comments}

        </div>

        <aside class="sidebar">
          {if count($saleStatProducts) > 0}
            <h2>Nejprodávanější</h2>

            {foreach $saleStatProducts as $row}
              <article class="product product--top product--side">

                <div class="product__image">

                  {include product/@image.latte product=>$row, imageWidth=>115}

                </div>

                <h3>
                  {$row|getProNameCatalog}
                </h3>
                <div class="product__meta">

                  {include product/@priceStock.latte product=>$row}

                  <p>
                    {include product/@basketAdd.latte product=>$row}
                  </p>
                </div>

              </article>
            {/foreach}
          {/if}

        </aside>

      </article>

    </section>

    {include article/@articles.latte articles => $footerArticles}

    {include @shop.latte}

    {include @instagram.latte}

  {/block}

{block addToFooter}
  {snippet modalCommentAdd}
    {include comment/@modalAdd.latte}
  {/snippet}
  <script src="{$baseUri}/js/libs/nouislider/nouislider.min.js"></script>
{/block}