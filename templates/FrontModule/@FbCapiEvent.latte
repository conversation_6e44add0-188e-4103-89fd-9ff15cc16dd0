{if !empty($type) && (!empty($products) || !empty($order))}

{php
    $items = !empty($order->items) ? $order->items : $products;
    $ids = [];
    foreach($items as $row) {
        $ids[] = $row->proid;
    }

    $productsIds = implode(',', $ids);

    // Vytvoření SHA256 hashe z emailu objednávky
    $shaEmail = !empty($order->ordmail) ? hash('sha256', strtolower(trim($order->ordmail))) : '';
}

<script>
    function CAPI(event, eventData) {

        var fbp = document.cookie.split(';').filter(function (c) {
            return c.includes('_fbp=');
        }).map(function (c) {
            return c.split('_fbp=')[1];
        });

        var fbc = document.cookie.split(';').filter(function (c) {
            return c.includes('_fbc=');
        }).map(function (c) {
            return c.split('_fbc=')[1];
        });
        fbp = fbp.length && fbp[0] || null;
        fbc = fbc.length && fbc[0] || null;
        var headers = new Headers();
        headers.append("Content-Type", "application/json");
        var body = {
            "event": event,
            "event_data": eventData,
            "fbp": fbp,
            "fbclid": fbc,
            "user_agent": navigator.userAgent,
            "url": window.location.origin + window.location.pathname,
            "em": {$shaEmail}, // SHA256 email
            "event_id": {$order->ordcode}
        };
        var options = {
            method: "POST",
            headers: headers,
            mode: "cors",
            body: JSON.stringify(body)
        };
        fetch("https://www.goldfitness.cz/fb/conversion", options);
    }

    var custom_data = {
        value: {$order->ordpricevat},
        content_ids: {$productsIds},
        currency: 'CZK',
        content_type: 'product',
    }
    CAPI ({$type}, custom_data);

    if (typeof fbq == 'function') {
        fbq('track', 'Purchase', {
            currency: 'CZK',
            content_type: 'product',
            value: {$order->ordpricevat},
            content_ids: {$productsIds},
            eventID: {$order->ordcode}
        });
    }
</script>
{/if}
