{if !empty($type) && (!empty($products) || !empty($order))}
{* GA4 Event *}
<script>
dataLayer.push({ ecommerce: null }); // Clear the previous ecommerce object
dataLayer.push({
  event: {$type},
  ecommerce: {
    currency: {$curKey},
    {if $type == 'purchase'}
    transaction_id: {$order->ordcode},
    affiliation: "goldfitnes.cz",
    value: {$order->ordpricevat},
    coupon: {$order->ordcoucode},
    {/if}
    items: [
    {if $type == 'purchase'}
    {foreach $order->items as $gRow}
      {
      item_id: {$gRow->oriproid},
      item_name: {$gRow->oriname},
      currency: {$curKey},
      price: {$gRow->oriprice},
      quantity: {$gRow->oriqty}
      }{if !$iterator->isLast()},{/if}
    {/foreach}
    {else}
    {foreach $products as $gRow}
      {
      item_id: {$gRow->proid},
      item_name: {$gRow->proname},
      currency: {$curKey},
      price: {$gRow->proprice},
      quantity: 1
      }{if !$iterator->isLast()},{/if}
    {/foreach}
    {/if}
    ]
  }
});
</script>
{/if}