{if ((bool)$basket->delFree) || $delFreeLimit <= $basket->priceSumVat}
{* POŠTOVNÉ ZDARMA - je dosaženo *}
<p class="order-benefits__item">
  <span class="icon icon--car">
    <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <use xlink:href="{$baseUri}/img/icons.svg#car" x="0" y="0" width="100%" height="100%"></use>
    </svg>
  </span>
  {_'Doprava zdarma'}
  <small>poštovné je na nás</small>
</p>
<span class="progress progress--light"><span class="progress__bar" style="width:100%;"></span></span>
{else}
{* POŠTOVNÉ ZDARMA - není dosaženo *}
<p class="order-benefits__item">
  <span class="icon icon--car">
    <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <use xlink:href="{$baseUri}/img/icons.svg#car" x="0" y="0" width="100%" height="100%"></use>
    </svg>
  </span>
  {_'Doprava zdarma'}
  <small>{_'objednejte ještě za'} {($delFreeLimit-$basket->priceSumVat)|formatPrice:$curId}</small>
</p>
<span class="progress progress--light"><span class="progress__bar" style="width:{round(($basket->priceSumVat / $delFreeLimit) * 100)}%;"></span></span>
{/if}