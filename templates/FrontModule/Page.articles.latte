{$pageTitle       = (empty($page->pagtitle) ? $page->pagname :$page->pagtitle)}
{$pageKeywords    = $page->pagkeywords}
{$pageDescription = $page->pagdescription}
{if !empty($page->pagrobots)}
  {$pageRobots = $page->pagrobots}
{/if}

{block #content}

    <div class="content-header">

      <div class="container">

        {php
          $breadCrumbs = [];

          $breadCrumbs[] = [
            "url"   => "",
            "title" => $pageTitle
          ];
        }

        {include @breadcrumb.latte items => $breadCrumbs}

        <h1><PERSON><PERSON><PERSON><PERSON></h1>

      </div>

    </div>

    <section class="section section--short">

      <div class="container">

        <ul class="sort sort--center">
          <li{if empty($typId)} class="is-active"{/if}><a href="{plink this, ti=>NULL}">Všechny články</a></li>
          {foreach $enum_arttypid as $id => $name}
            <li{if $typId == $id} class="is-active"{/if}><a href="{plink this, ti=>$id}">{$name}</a></li>
          {/foreach}
        </ul>

        {foreach $rows as $row}
          <a href="{plink Article:detail $row->artid, ($row->arturlkey|getUrlKey:$row->artname)}" class="article article--wide">
            {include article/@listItem.latte article=>$row}
          </a>
        {/foreach}

        <div class="section__pages">

          {*<p><a href="#" class="link link--down">Zobrazit další články</a></p>*}

          {control paginator}

        </div>

      </div>

    </section>

    {include @shop.latte}

    {include @instagram.latte}