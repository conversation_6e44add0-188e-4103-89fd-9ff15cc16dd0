{$pageTitle = '<PERSON><PERSON><PERSON><PERSON> jste lepš<PERSON> cenu?'}

{block #content}

  <div class="article">

    <h1>{$pageTitle}</h1>

    <p>
      <PERSON><PERSON><PERSON>v zboží: <strong>{$product->proname}</strong><br>
      <PERSON><PERSON><PERSON> cena: <strong>{$product->proprice|formatPrice:$curId}</strong>
    </p>

    {form betterPriceForm}
    {include @formErrors.latte form=>$form}

    <fieldset>
  
      <legend>Prosím vyplňte následující údaje</legend>

      <p>
        {php echo $form['usrname']->getLabel()->class('required')}:<br>
        {php echo $form['usrname']->getControl()->size(60)}
      </p>
      <p>
        {php echo $form['usrmail']->getLabel()->class('required')}:<br>
        {php echo $form['usrmail']->getControl()->size(60)}
      </p>
      <p>
        {php echo $form['usrprice']->getLabel()->class('required')}:<br>
        {php echo $form['usrprice']->getControl()->size(30)}
      </p>
      <p>
        {php echo $form['usrurl']->getLabel()->class('required')}:<br>
        {php echo $form['usrurl']->getControl()->size(60)}
      </p>
      <p class="antispam">
        {php echo $form['antispam']->getLabel()}:<br>
        {php echo $form['antispam']->getControl()}
      </p>
      <p>
        {php echo $form['usrnote']->getLabel()}<br>
        {php echo $form['usrnote']->getControl()}
      </p>

      {php echo $form['submit']->getControl()->value('Odeslat informace')}

  </fieldset>

  {/form}

  <script type="text/javascript">
    $('#antispam').val('{$presenter->config["ANTISPAM_NO"]|noescape}');
    $('.antispam').hide();
  </script>

</div>
{/block}
