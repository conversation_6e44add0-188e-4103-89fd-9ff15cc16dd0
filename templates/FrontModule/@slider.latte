<section class="slider">

	<div class="splide splide--main">
		<div class="splide__track">
			<div class="splide__list">

			{foreach $items as $row}

				{php
					$arrTexts = array();
					if (!empty($row->meidesc)){
						$arrTexts = explode("\n", $row->meidesc);
					}

					$color = "";

					$colors = [
						"žlutý" => "",
						"modrý" => "is-info",
						"zelený" => "is-success",
						"červený" => "is-danger",
					];


					if (!empty($arrTexts[4]) && !empty($colors[$arrTexts[4]])) {
						$color = $colors[$arrTexts[4]];
					}
				}

				<div class="splide__slide {$color}">
					<p class="slider__image">
						<picture>
							<source srcset="{$baseUri}/pic/menuindex/{$row->meiid}.webp" type = "image/webp" >
							<img src="{$baseUri}/pic/menuindex/{$row->meiid}.jpg" alt="" width="1920" height="465">
						</picture>
					</p>
					<div class="container">
						<div class="slider__content">
							<p class="slider__title">
								{foreach $arrTexts as $text}
									{if $iterator->getCounter() == 1}
								<small>{$text}</small>
									{elseif $iterator->getCounter() == 2}
								{$text}<br>
									{elseif $iterator->getCounter() == 3}
								{$text}
									{/if}
								{/foreach}
							</p>
							<p class="slider__link">
								{if !empty($arrTexts[3])}
								<a href="{if $row->meipagid > 0}{plink Page:detail $row->pagurlkey}{elseif $row->meicatid > 0}{plink Catalog:detail $row->catid, ($row|getCatKey)}{elseif !empty($row->meiprocode)}{plink Product:detail $row->proid, ($row|getProKey)}{else}{$row->meiurl}{/if}" class="btn">{$arrTexts[3]}</a>
								{/if}
							</p>
						</div>
					</div>
				</div>

			{/foreach}

			</div>
		</div>
	</div>

</section>