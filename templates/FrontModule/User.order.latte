{default $pageTitle='Objednávka č. '.$order->ordcode}
{default $pageRobots = "nofollow,noindex"}

{block #content}
  <div class="content-header">

      <div class="container">

        {* sestavím drobky z katalogové cesty *}
        {php
          $breadCrumbs = [];

          $breadCrumbs[] = [
            "url"   => $presenter->link("default"),
            "title" => "Můj účet"
          ];

          $breadCrumbs[] = [
            "url"   => "",
            "title" => $pageTitle
          ];
        }

        {include @breadcrumb.latte items => $breadCrumbs}

        <h1>{$pageTitle}</h1>

      </div>

    </div>

    <section class="section section--short">

      <div class="container">

        {include user/@menu.latte}
  
        <h2>{$pageTitle}</h2>

        <p><a class="btn btn--success" href="{plink copyOrder, $order->ordid}">kopírovat objednávku do košíku</a></p>

        <h3>{_'O<PERSON><PERSON><PERSON><PERSON><PERSON>'}</h3>

        <div class="table__wrapper">
          <table>
            <thead>
            <tr>
              <th>{_'Katalogové č.'}</th>
              <th>{_'Název'}</th>
              <th>{_'Kusy'}</th>
              <th>{_'Cena/kus s DPH'}</th>
              <th>{_'Sleva'}</th>
              <th>{_'Celkem s DPH'}</th>
            </tr>
            </thead>

            <tfoot>
            <tr>
              <td colspan="5">{_'Celkem s DPH'}</td>
              <td>{$order->ordpricevat|formatPrice:$curId}</td>
            </tr>
            </tfoot>

            <tbody>
            {foreach $ordItems as $row}
            <tr>
              <td>{$row->oriprocode}</td>
              <td>{$row->oriname}</td>
              <td>{$row->oriqty}</td>
            {if $row->oritypid != 1}
              <td>{$row->oriprice|formatPrice:$curId}</td>
              <td>{$row->oridisc|formatPrice:$curId}</td>
              <td>{($row->oriprice*$row->oriqty-$row->oridisc)|formatPrice:$curId}</td>
            {else}
              <td>{if $row->oriprice > 0}{$row->oriprice|formatPrice:$curId}{else}ZDARMA{/if}</td>
              <td></td>
              <td>{if $row->oriprice > 0}{$row->oriprice*$row->oriqty|formatPrice:$curId}{else}ZDARMA{/if}</td>
            {/if}
            </tr>
            {/foreach}
            </tbody>

          </table>
        </div>

        <h3>{_'Způsob platby a doprava'}:</h3>

        <ul>
          <li>{_'Zvolená doprava'}: <strong>{$delMode->delname}</strong></li>
          <li>{_'Platba'}: <strong>{$payMode->delname}</strong></li>
        </ul>

        <h3>{_'Fakturační adresa'}:</h3>
        <p>
          <strong>{$order->ordifirname}</strong><br />
          {$order->ordiname} {$order->ordilname}<br />
          {$order->ordistreet} {$order->ordistreetno}<br />
          {$order->ordipostcode} {$order->ordicity}<br />
          <br />
          Email: {$order->ordmail}<br />
          {_'Telefon'}: {$order->ordtel}<br />
          <br />
          IČ: {$order->ordic}<br />
          DIČ: {$order->orddic}
        </p>

        {if !empty($order->ordstlname)}
        <h2>{_'Dodací adresa'}:</h2>
        <p>
          <strong>{$order->ordstfirname}</strong><br />
          {$order->ordstname} {$order->ordstlname}<br />
          {$order->ordststreet} {$order->ordststreetno}<br />
          {$order->ordstpostcode} {$order->ordstcity}
        </p>
        {/if}

        <a class="link link--back" href="{plink User:default}">Zpět k přehledu objednávek</a>
      </div>
    </section>

    {include article/@articles.latte articles => $footerArticles}

    {include @shop.latte}

    {include @instagram.latte}

{/block}
