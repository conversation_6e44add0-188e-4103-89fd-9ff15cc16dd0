{$pageTitle       = (empty($man->mantitle) ? $man->manname : $man->mantitle)}
{$pageDescription = (!empty($man->mandescription) ? $man->mandescription : strip_tags($man->mandesc)) }

{block #content}

    <div class="content-header">

      <div class="container">

        {* sestavím drobky z katalogové cesty *}
        {php
          $breadCrumbs = [];
        }

        {php
          $breadCrumbs[] = [
            "url"   => "",
            "title" => $pageTitle
          ];
        }

        {include @breadcrumb.latte items => $breadCrumbs}

        <h1>{$pageTitle}</h1>

        <div class="content-header__description">
          {$man->mandesc|noescape}
        </div>

        {if !empty($catalogSubItems)}
        <ul class="row row--start">

          {foreach $catalogSubItems as $key => $row}

          <li class="col col--4">
            <a href="{plink Catalog:detail, $row->catid, ($row|getCatKey), $catPath}" class="category">
              <span class="category__image">
                {include catalog/@image.latte catalog=>$row}
              </span>
              {$row->catname}
            </a>
          </li>

          {/foreach}

        </ul>
        {/if}

      </div>

    </div>

    <section class="section section--short">

      <div class="container">

        <h2>Produkty</h2>
        {snippet productsList}
        {foreach $rootCats as $crow}
          {if isset($productsByCatId[$crow->catid])}
          {foreach $productsByCatId[$crow->catid] as $key => $row}
          {if $iterator->isFirst()}
          <h3><a href="{plink Catalog:detail, $crow->catid, ($crow|getCatKey), $catPath}">{$crow->catname}</a></h3>
          <div class="row row--start row--align">
          {/if}

          <div class="col col--4">{include "product/@listItem.latte" product=>$row}</div>

          {if $iterator->isLast()}
            </div>
          {/if}
          {/foreach}
          {/if}
        {/foreach}

        {if count($rootCats)===0}
        <p>Výpis neobsahuje žádné produkty.</p>
        {/if}

        {/snippet}


      </div>

    </section>

    {include @shop.latte}

    {include @instagram.latte}

{/block}
