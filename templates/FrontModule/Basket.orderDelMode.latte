{default $pageTitle= '<PERSON><PERSON><PERSON> - v<PERSON><PERSON><PERSON><PERSON> dopravy a platby'}
{default $pageRobots = "nofollow,noindex"}

{* blok po header  *}
{block #afterHeader}
  {include @orderProgress.latte}
{/block}

{block #content}

  {include @gtagGa3Event.latte type=>"checkout", products=>$productRows, step=>1}

  <section class="section section--short">

      <div class="container container--sidebar" n:snippet="basketDeliveryModes">

        <div class="section__content">

          <h2>Vyberte způsob dopravy</h2>

          <div class="section__p">

            {foreach $delModes as $row}

            <label class="form-radio form-radio--delivery">
                <input type="radio" class="delModeClick" data-delid="{$row->delid}" name="delid" value="{$row->delid}" {if $delId == $row->delid}checked="checked"{/if}>

                <span class="form-radio__icon">
                  <img src="{$baseUri}/{$row->delcode|getDeliveryLogo}" alt="{$row->delname}">
                </span>

                <span class="form-radio__label">
                  {if $row->delid == 18}<span class="order__express--name">EXPRES dodání</span> do 2 hodin po Praze!{else}{$row->delname}{/if}
                  <small>
                    {if !empty($row->deltext)}
                    {$row->deltext|noescape}
                    {/if}
                    {if $row->delcode == 'CESKA_POSTA_BALIKOVNA' && $delId == $row->delid}
                      <br>
                      {if !empty($ordDelSpec)}
                      Vybraná pobočka: {$ordDelSpecText} <a href="#balikovna" class="js-reveal">Změnit</a>
                      {else}

                      {/if}
                    {elseif $row->delcode == 'ULOZENKA' && $delId == $row->delid}
                      <br>
                      {if !empty($ordDelSpec)}
                      Vybraná pobočka: {$ordDelSpecText} <a href="#ulozenka" class="js-reveal">Změnit</a>
                      {else}

                      {/if}
                    {elseif $row->delcode == 'ZASILKOVNA' && $delId == $row->delid}
                      <br>
                      {if !empty($ordDelSpec)}
                      Vybraná pobočka: {$ordDelSpecText} <a href="#zasilkovna" class="js-reveal" onclick="inlineFull(document.getElementById('zasilkovna'));">Změnit</a>
                      {else}

                      {/if}
                    {elseif $row->delcode == 'DPD_PICKUP' && $delId == $row->delid}
                      <br>
                      {if !empty($ordDelSpec)}
                      Vybraná pobočka: {$ordDelSpecText} <a href="#dpd_pickup" class="js-reveal">Změnit</a>
                      {else}

                      {/if}
                    {/if}
                  </small>

                </span>

              <strong class="form-radio__price">
                {if $row->delprice == 0}
                zdarma
                {else}
                {$row->delprice|formatPrice:$curId}
                {/if}
              </strong>
              <span class="form-radio__checker"></span>
            </label>
            {if $row->delcode == 'CESKA_POSTA_BALIKOVNA' && $delId == $row->delid}
            <label class="form-radio__iframe{if empty($ordDelSpec)} is-visible{/if}" id="balikovna">
              <iframe title="Výběr místa pro vyzvednutí zásilky" src="https://b2c.cpost.cz/locations/?type=BALIKOVNY" allow="geolocation"></iframe>
            </label>
            {elseif $row->delcode == 'ULOZENKA' && $delId == $row->delid}
            <label class="form-radio__iframe{if empty($ordDelSpec)} is-visible{/if}" id="ulozenka">
              <iframe title="Výběr místa pro vyzvednutí zásilky" src="https://widget.wedo.cz/v5/widget-content?selectMode=postMessage" allow="geolocation"></iframe>
            </label>
            {elseif $row->delcode == 'ZASILKOVNA' && $delId == $row->delid}
            <script>
              document.addEventListener("DOMContentLoaded", () => {
                inlineFull(document.getElementById('zasilkovna'));
              });

              inlineFull(document.getElementById('zasilkovna'));
            </script>
            <label class="form-radio__iframe{if empty($ordDelSpec)} is-visible{/if}" id="zasilkovna">
              <div id="zasilkovna" style="height: 0; line-height: 40px; background: transparent; width: 100%"></div>
            </label>
            {elseif $row->delcode == 'DPD_PICKUP' && $delId == $row->delid}
            <label class="form-radio__iframe{if empty($ordDelSpec)} is-visible{/if}" id="dpd_pickup">
              <iframe title="Výběr místa pro vyzvednutí zásilky" src="https://api.dpd.cz/widget/latest/index.html" allow="geolocation"></iframe>
            </label>
            {/if}
            {/foreach}
          </div>

          <h2>Vyberte způsob platby</h2>

          <p>
            {foreach $payModes as $irow}
            <label class="form-radio form-radio--delivery payModeClick" data-payid="{$irow->delid}">
              <input type="radio" name="payid" value="{$irow->delid}" {if $payId == $irow->delid}checked="checked"{/if}>
              <span class="form-radio__icon">
                <img src="{$baseUri}/{$irow->delcode|getDeliveryLogo}" alt="">
              </span>
              <span class="form-radio__label">
                {$irow->delname}
                {if !empty($irow->deltext)}
                <small>
                  {$irow->deltext|noescape}
                </small>
                {/if}
              </span>
              <strong class="form-radio__price">
                {if $irow->delprice == 0}
                zdarma
                {else}
                {$irow->delprice|formatPrice:$curId}
                {/if}
              </strong>
              <span class="form-radio__checker"></span>
            </label>
            {/foreach}
          </p>

        </div>

        <aside class="sidebar sidebar--order">

          {include @orderSidebar.latte}

        </aside>

        {if !empty($delMode)}
          {if $delMode->delcode === "CESKA_POSTA_BALIKOVNA"}
          <script type="text/javascript">
            function iframeListener(event) {
              if (event.data.message === "pickerResult") {
                //sestavim popis odběrného místa
                naja.makeRequest("GET", {plink DelModeChanged!}, { sid: event.data.point.zip, stext: event.data.point.address }, { history: false });
              }
            }
            window.addEventListener("message", iframeListener)
          </script>
          {elseif $delMode->delcode === "ULOZENKA"}
          <script type="text/javascript">
              window.addEventListener("message", (event) => {
                if (event.data.selectedID.length) {
                  naja.makeRequest("GET", {plink DelModeChanged!}, { sid: event.data.selectedID, stext: event.data.selectedName }, { history: false });
                }
              }, false);
          </script>
          {elseif $delMode->delcode === "ZASILKOVNA"}
          <script type="text/javascript">
              window.addEventListener("message", (event) => {
                  var point = JSON.parse(event.data);
                if (point.packetaPoint.id.length) {
                  naja.makeRequest("GET", {plink DelModeChanged!}, { sid:point.packetaPoint.id, stext:point.packetaPoint.formatedValue }, { history: false });
                }
              }, false);
          </script>
          {elseif $delMode->delcode === "DPD_PICKUP"}
          <script type="text/javascript">
              window.addEventListener("message", (event) => {
                if(event.data.dpdWidget) {
                  naja.makeRequest("GET", {plink DelModeChanged!}, { sid: event.data.dpdWidget.id, stext: event.data.dpdWidget.pickupPointResult }, { history: false });
                }
              }, false);
          </script>
          {/if}
        {/if}
      </div>

      <div class="container">

        <div class="order-steps">

          <p>
            <a href="{plink default}" class="link link--back">Zpět do košíku</a>
          </p>

          <p>
            <a href="{plink orderContact}" class="btn btn--big">
              Pokračovat na osobní údaje
              <span class="icon icon--next">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#next" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
            </a>
          </p>

        </div>

      </div>

    </section>

{/block}

{block addToFooter}
  <script type="text/javascript">
  jQuery(document).ready(function ($) {
    $(document).on('click', '.payModeClick', function (e) {
      naja.makeRequest("GET", {plink DelModeChanged!}, { pid: $(this).data("payid") }, { history: false });
    });

    $(document).on('click', '.delModeClick', function (e) {
      naja.makeRequest("GET", {plink DelModeChanged!}, { did: $(this).data("delid") }, { history: false });
    });

    $(document).on('click', '.payModeClick a, .delModeClick a', function (e) {
      e.stopPropagation();
    });
  });

  </script>

  <script src="https://widget.packeta.com/v6/www/js/library.js"></script>
    <script>
        var packetaApiKey = 'd71b3a93840ef5dd';

        /*
        	Function which will be called when the user confirms or cancels pick-up point selection.
        	The function will receive one argument, which will be either an ExtendedPoint object
        	if pick-up point was selected, or null if selection was cancelled.
        */
        function showSelectedPickupPoint(point) {
            //this.style.height = "40px";
            //this.innerText = "Chosen pick-up point: " + (point ? point.name : "None");
        };

        function clear() {
            var elements = document.querySelectorAll('.method-detail');
            for (var i = 0; i < elements.length; i++) {
                elements[i].innerText = "";
                elements[i].style.height = "0";
            }
            Packeta.Widget.close();
        }

        function inlineFull(div) {
            clear();
            div.style.height = "600px";

            Packeta.Widget.pick(
                packetaApiKey,
                showSelectedPickupPoint.bind(div), {
                    country: "cz,sk",
                    language: "cs",
                    valueFormat: "place,city,street"
                },
                div
            );
        }

    </script>
{/block}