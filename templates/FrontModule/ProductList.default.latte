{$pageTitle = 'Nahlíženi do ceníku'}

{block #content}

  <div class="content-header">

    <div class="container">

      {* sestavím drobky z katalogové cesty *}
      {php
        $breadCrumbs = [];

        $breadCrumbs[] = [
          "url"   => "",
          "title" => $pageTitle
        ];
      }

      {include @breadcrumb.latte items => $breadCrumbs}

      <h1>{$pageTitle}</h1>

    </div>

  </div>

  <section class="section section--short">

    <div class="container">

        {if (!$isLoggedIn)}
          {form pLLoginForm class=>"form form--full"}
          {include @formErrors.latte form=>$form}
            <p>
              Zadejte heslo pro vstup:<br>
              <input type="text" n:name="pssw">
              <input type="submit" n:name="submit" class="btn btn--big">
            </p>
          {/form}
        {else}
          {form pLSearchForm class=>"form form--full"}
          {include @formErrors.latte form=>$form}
            <p>
              {label id /}
              {input id}

              {label code /}
              {input code}

              {label name /}
              {input name}
              <br>

              {label access /}
              {input access}

              {label manid /}
              {input manid}

              {label orderby /}
              {input orderby}
            </p>
            {input detailSearch class=>"btn btn--big"}
            {/form}
  
            {foreach $productsData as $key => $row}
            {if $iterator->isFirst()}
          <div class="table__wrapper">
            <table>
              <tr>
                <th>Kód</th>
                <th>Název</th>
                <th>Cena běžná</th>
                <th>Cena e-shop</th>
                <th>Cena V.I.P.</th>
                <th>Cena B</th>
                <th>Cena A</th>
                <th>Cena C</th>
              </tr>
            {/if}
              <tr>
                <td>{$row->procode}</td>
                <td><a href="{plink Product:detail, $row->proid, ($row|getProKey)}">{$row->proname}</a></td>
                <td>{$row->propricecom}</td>
                <td>{$row->propricea}</td>
                <td>{$row->propriceb}</td>
                <td>{$row->propricec}</td>
                <td>{$row->propriced}</td>
                <td>{$row->propricee}</td>
              </tr>
            {if $iterator->isLast()}
            </table>
          </div>
            {/if}
            {/foreach}

            <div class="pager">
              {control paginator}
            </div>
            {/if}

      </div>

  </section>
{/block}