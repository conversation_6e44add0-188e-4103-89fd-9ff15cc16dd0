{if !empty($type) && (!empty($products) || !empty($order))}
{* GA3 Event *}
<script>
dataLayer.push({ ecommerce: null });
dataLayer.push({
{if $type == "view_item_list"}
  "event": "category",
  "ecommerce": {
    "currencyCode": {$curKey},
    "impressions": [
      {foreach $products as $gRow}
        {
            "id": {$gRow->proid},
            "name": {$gRow->proname},
            "price": {$gRow->proprice},
            "quantity": 1,
            "position": {$iterator->getCounter()}
        }{if !$iterator->isLast()},{/if}
      {/foreach}
    ]
  }
{elseif $type == "view_item"}
  "event": "detail_product",
  "ecommerce": {
    "currencyCode": {$curKey},
    "detail": {
      "products": [
        {foreach $products as $gRow}
          {
              "id": {$gRow->proid},
              "name": {$gRow->proname},
              "price": {$gRow->proprice},
              "quantity": 1,
              "position": {$iterator->getCounter()}
          }{if !$iterator->isLast()},{/if}
        {/foreach}
      ]
    }
  }
{elseif $type == "add_to_cart"}
  "event": "addToCart",
  "ecommerce": {
    "currencyCode": {$curKey},
    "add": {
      "products": [
        {foreach $products as $gRow}
        {
          "id": {$gRow->proid},
          "name": {$gRow->proname},
          "price": {$gRow->proprice},
          "quantity": {$qty},
          "position": {$iterator->getCounter()}
        }{if !$iterator->isLast()},{/if}
        {/foreach}
      ]
    },
  }
{elseif $type == "remove_from_cart"}
  "event": "removeFromCart",
  "ecommerce": {
    "currencyCode": {$curKey},
    "remove": {
      "products": [
        {foreach $products as $gRow}
        {
          "id": {$gRow->proid},
          "name": {$gRow->proname},
          "price": {$gRow->proprice},
          "quantity": 1,
          "position": {$iterator->getCounter()}
        }{if !$iterator->isLast()},{/if}
        {/foreach}
      ]
    },
  }
{elseif $type == "checkout"}
  "event": "checkout",
  "ecommerce": {
    "currencyCode": {$curKey},
    "checkout": {
      "actionField":{
        "step":{$step},
      },
      "products": [
        {foreach $products as $gRow}
          {php
            $qty = 1;
            if (isset($basket->items[$gRow->proid])) {
              $qty = $basket->items[$gRow->proid];
            }
          }
        {
          "id": {$gRow->proid},
          "name": {$gRow->proname},
          "price": {$gRow->proprice},
          "quantity": {$qty},
          "position": {$iterator->getCounter()}
        }{if !$iterator->isLast()},{/if}
        {/foreach}
      ]
    },
  }
{elseif $type == "purchase"}
  "event":"purchase",
  "enhanced_conversion_data":{
    "email": {$order->ordmail},
    "phone_number": {$order->ordtel},
    "first_name": {$order->ordiname},
    "last_name": {$order->ordilname},
    "home_address":{
      "street": {$order->ordistreet.$order->ordistreetno},
      "city": {$order->ordicity},
      "postal_code": {$order->ordipostcode},
    }
  },
  "ecommerce":{
    "currencyCode":{$curKey},
    "purchase":{
      "actionField":{
        "id": {$order->ordcode},
        "revenue": {($order->ordpricevat-$order->orddelprice)},
        "shipping": {$order->orddelprice},
        "coupon": {$order->ordcoucode}
      },
      "products": [
        {foreach $order->items as $gRow}
        {
          "id": {$gRow->oriproid},
          "name": {$gRow->oriname},
          "price": {$gRow->oriprice},
          "quantity": {$gRow->oriqty}
        }{if !$iterator->isLast()},{/if}
        {/foreach}
      ]
    }
  }
{/if}
})
</script>
{/if}