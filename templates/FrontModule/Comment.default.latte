{$pageTitle = 'Poradna'}

{block #content}
<div class="content-header">

  <div class="container">

    {php
      $breadCrumbs = [];

      $breadCrumbs[] = [
        "url"   => "",
        "title" => $pageTitle
      ];
    }

    {include @breadcrumb.latte items => $breadCrumbs}

    <div class="content-header__search">

      <h1>Poradna</h1>

      <div class="form-search">
      {form commentSearchForm}
      {include @formErrors.latte form=>$form}
        <a n:href="commentAdd!" class="btn ajax" data-naja-history="off" rel="nofollow">Vložit dotaz</a>
        {input fulltext placeholder=>"Hledejte v poradně"}
        <button>
          <span class="icon icon--magnifier">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#magnifier" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
        </button>
      {/form}
      </div>

    </div>

  </div>

</div>

<div class="topic">

  <div class="container">

    <div class="topic__info">
      <h2>Témata</h2>
      <p><a href="{plink Comment:default []}">Zobrazit všechny</a></p>
    </div>

    <div class="topic__content">
      {php
        $cat = $presenter->getParameter("cat");
      }
      {foreach $enu_cmtcatid as $id => $name}

        {php

          $params = $cat;

          if (isset($params[$id])) {
            unset($params[$id]);
          } else {
            $params[$id] = $id;
          }
        }

        <a href="{plink this $params}" class="badge{if isset($cat[$id])} is-active{/if}">{$name}</a>
      {/foreach}

    </div>

  </div>

</div>

<section class="section">

  <div class="container" id="dotazy">
    {foreach $rows as $row}
      {include comment/@listItem.latte comment=>$row}
    {/foreach}

    {control paginator}

  </div>

</section>

{include @shop.latte}

{include @instagram.latte}

{/block}

{block addToFooter}
  {snippet modalCommentAdd}
    {include comment/@modalAdd.latte}
  {/snippet}
{/block}