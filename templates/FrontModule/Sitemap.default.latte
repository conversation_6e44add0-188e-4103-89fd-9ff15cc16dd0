{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  {if isset($products)}
  {foreach $products as $row}
  <url>
    <loc>{plink //Product:detail, $row->proid, ($row|getProKey)}</loc>
    <lastmod>{$row->moddate|date:'Y-m-d'}</lastmod>
    <changefreq>daily</changefreq>
  </url>
  {/foreach}
  {/if}
  {if isset($catalogs)}
  {foreach $catalogs as $catalog}
    {php
      $path = "";
      //bacha záleží na pořadí
      if (!empty($catalog->sitkeykey)) $path .= $catalog->sitkeykey.'/';
      if (!empty($catalog->sitkeyman)) $path .= $catalog->sitkeyman.'/';
      if (!empty($catalog->sitkeytyp)) $path .= $catalog->sitkeytyp.'/';
      if (!empty($catalog->sitkeyfor)) $path .= $catalog->sitkeyfor.'/';
      $path = trim($path,'/');
    }
    <url>
      <loc>{plink //Catalog:detail, $catalog->sitcatid, $catalog->sitcatkey, $path}</loc>
      <lastmod>{$catalog->sitlastmod|date:'Y-m-d'}</lastmod>
      <changefreq>daily</changefreq>
    </url>
  {/foreach}
  {/if}
  {if isset($manufacturers)}
  {foreach $manufacturers as $row}
    <url>
      <loc>{plink //Manufacturer:detail, $row->manid, (''|getUrlKey:$row->manname)}</loc>
      <lastmod>{$row->moddate|date:'Y-m-d'}</lastmod>
      <changefreq>daily</changefreq>
    </url>
  {/foreach}
  {/if}
  {if isset($articles)}
  {foreach $articles as $row}
    <url>
      <loc>{plink //Article:detail, $row->artid, (''|getUrlKey:$row->artname)}</loc>
      {if !empty($row->moddate)}<lastmod>{$row->moddate|date:'Y-m-d'}</lastmod>{/if}
      <changefreq>daily</changefreq>
    </url>
  {/foreach}
  {/if}
  {if isset($pages)}
  {foreach $pages as $row}
  <url>
    <loc>{plink //Page:detail, $row->pagid, ($row->pagurlkey|getUrlKey:$row->pagname)}</loc>
    {if !empty($row->moddate)}<lastmod>{$row->moddate|date:'Y-m-d'}</lastmod>{/if}
    <changefreq>daily</changefreq>
  </url>
  {/foreach}
  {/if}
</urlset>
