{default $pageTitle='Souhrn objednávky'}
{default $pageRobots = "nofollow,noindex"}

{* v kosiku drobecky nejsou *}  
{block #crumb}
{/block}

{block #content}
<div class="order order--4">

 <h1><i class="fa fa-opencart"></i> {_'Souhrn objednávky'}</h1>

  <div class="order__tabs">

    <ul>
      <li><a href="{plink default}">{_'Nákupní košík'}</a></li>
      <li><a href="{plink orderDelMode}">{_'Doprava a platba'}</a></li>
      <li><a href="{plink orderContact}">{_'Dodací údaje'}</a></li>
      <li><a href="#" class="active">{_'Souhrn objednávky'}</a></li>
    </ul>

  </div>  
 <div class="order__article">

  <h2 class="order__header"><span>{_'Souhrn informací Vaší objednávky'}</span></h2>

 <table class="grid" id="oitems">
 <thead>
    <tr>
      <th class="name">{_'Název'}</th>
      <th>{_'Kusy'}</th>
      <th>{_'Cena/kus'}</th>
      <th>{_'Cena'}</th>
    </tr>
  </thead>
  <tfoot>
    <tr class="order__sumprice">
      <td>&nbsp;</td>
      <td colspan="2">{_'Cena s DPH'}</td>
      <td colspan="2">{$basket->priceSumVat|formatPrice:$curId}</td>
    </tr>
    <tr class="order__sumprice">
      <td>&nbsp;</td>
      <td colspan="2">{$delname}</td>
      <td colspan="2">{$delprice|formatPrice:$curId}</td>
    </tr>
    <tr class="order__discount">
      <td>&nbsp;</td>
      <td colspan="2">{_'Sleva'} {if $basket->discountVal > 0}{$basket->discountPer}%{/if}</td>
      <td colspan="2">{if $basket->discountVal > 0}{$basket->discountVal|formatPrice:$curId}{else}bez slevy{/if}</td>
    </tr>
    {* slevový kupón *}
    {if !empty($basket->coupon)}
    <tr class="order__discount">
      <td>&nbsp;</td>
      <td colspan="2">{_'Slevový kupón'} {$basket->coupon["coucode"]} {if $basket->coupon["couvalue"] > 0} (sleva {$basket->coupon["couvalue"]} {$basket->coupon["couvalueunit"]}){/if}</td>
      <td colspan="2">{$basket->couDiscountVal|formatPrice:$curId}</td>
    </tr>
    {/if}
    <tr class="order__suma">
      <td>&nbsp;</td>
      <td colspan="2">{_'Cena celkem'} s DPH</td>
      <td colspan="2">{MAX($basket->priceSumVat + $delprice - $basket->discountVal - $basket->couDiscountVal, 0)|formatPrice:$curId}</td>
    </tr>
  </tfoot>
  <tbody>
  {foreach $basket->items as $id=>$value}
    <tr>
      <td class="name">{$productRows[$id]->proname}<br><em>{$productRows[$id]->manname}</em></td>
      <td>{$value}{if (int)$productRows[$id]->proqty > 0 && (int)$productRows[$id]->proqty < (int)$value}<br />jen {$productRows[$id]|getQtyText}{/if}</td>
      <td>{$productRows[$id]->proprice|formatPrice:$curId}</td>
      <td>{($productRows[$id]->proprice*$value)|formatPrice:$curId}</td>
    </tr>
  {/foreach}
    
    {if $basket->weightSum > 0}
    <tr class="background">
      <td colspan="4" class="name">{_'Hmotnost objednávky'} {$basket->weightSum|number:2:',':' '}&nbsp;Kg</td>
      <td></td>
    </tr>
    {/if}
  </tbody>
  </table> 

  <h2 class="order__header"><span>{if empty($form['ordstname'])}{_'Fakturační a současně doručovací adresa'}{else}Fakturační adresa{/if}</span></h2>

  <table>
    <tbody>
      <tr>
        <td>Jméno, příjmení:</td>
        <td>{$form['ordiname']} {$form['ordilname']}</td>
      </tr><tr>
        <td>Název firmy:</td>
        <td>{$form['ordifirname']}</td>
      </tr><tr>
        <td>Ulice:</td>
        <td>{$form['ordistreet']} {$form['ordistreetno']}</td>
      </tr><tr>
        <td>PSČ, město:</td>
        <td>{$form['ordipostcode']} {$form['ordicity']}</td>
      </tr><tr>
        <td>Země:</td>
        <td>{$enum_countries[$curId]}</td>
      </tr><tr>
        <td>Email:</td>
        <td>{$form['ordmail']}</td>
      </tr><tr>
        <td>Telefon:</td>
        <td>{$form['ordtel']}</td>
      </tr><tr>
        <td>IČ:</td>
        <td>{$form['ordic']}</td>
      </tr><tr>
        <td>DIČ:</td>
        <td>{$form['orddic']}</td>
      </tr>
    </tbody>
  </table>
  {if !empty($form['ordstname'])}    
  <h2 class="order__header"><span>{_'Adresa dodání'}</span></h2>
  <table>
    <tbody>
      <tr>
        <td>Jméno, příjmení:</td>
        <td>{$form['ordstname']} {$form['ordstlname']}</td>
      </tr><tr>
        <td>Název firmy:</td>
        <td>{$form['ordstfirname']}</td>
      </tr><tr>
        <td>Ulice:</td>
        <td>{$form['ordststreet']} {$form['ordststreetno']}</td>
      </tr><tr>
        <td>PSČ, město:</td>
        <td>{$form['ordstpostcode']} {$form['ordstcity']}</td>
      </tr><tr>
        <td>Země:</td>
        <td>{$enum_countries[$curId]}</td>
      </tr>
    </tbody>
  </table>
  {/if}
  {if !empty($form['ordnote'])}
  <h2 class="order__header"><span>{_'Vzkaz k objednávce'}</span></h2>
  <table>
      <tbody>
      <tr>
        <td>{$form['ordnote']|nl2br|noescape}</td>
      </tr>
      </tbody>
  </table>
   {/if}
  {form orderSumarizeForm}
    {include @formErrors.latte form=>$form}
    <div class="order__note">
      Odesláním formuláře potvrzujete souhlas s <a href="{$baseUri}/obchodni-podminky-t26" target="_blank" rel="noopener noreferrer">Obchodními&nbsp;podmínkami</a> obchodu {$presenter->config["SERVER_NAMESHORT"]}.<br>
      <br>
      {input ordheurekagdpr}
      {if $userRow->usrid == 0 && empty($basket->coupon)}
      <br>
      <br>
      {input ordregister} Souhlasím se <a href="https://www.goldfitness.cz/gdpr-t83" target="_blank" rel="noopener noreferrer">zpracováním osobních údajů</a>
      <div id="ordregisterpassw">
      {label ordregisterpassw /}:
      {input ordregisterpassw} <small>(přihlašovací jméno bude Váš email)</small>
      </div>
      {/if}
    </div>
    <div class="order__controls">
    <div class="order__back"><a href="{plink 'orderContact'}">{_'O krok zpět'}</a></div>  
    <div class="order__buy">
      <button type="submit" id="frm-orderSumarizeForm-submit" name="_submit">Závazně objednat <i class="fa fa-angle-right"></i></button>
    </div> 
  </div>
  {/form}
 </div>

</div>
{/block}