{$pageTitle       = 'Oblíbené produkty'}

{block #content}
    <div class="content-header">

      <div class="container">

        {* sestavím drobky z katalogové cesty *}
        {php
          $breadCrumbs = [];
          $breadCrumbs[] = [
            "url"   => "",
            "title" => $pageTitle
          ];
        }

        {include @breadcrumb.latte items => $breadCrumbs}

        <h1>Oblíbené produkty</h1>

      </div>

    </div>

    <section class="section section--short">

      <div class="container">

        {*
        <ul class="sort sort--center">
          <li class="is-active"><a href="#">Podle data přidání</a></li>
          <li><a href="#">Nejlevnější</a></li>
          <li><a href="#">Nejdražší</a></li>
          <li><a href="#">Nejprodávanější</a></li>
        </ul>
        *}
        {snippet productsList}
        <div class="row row--start row--align">

          {foreach $productsData as $row}

          <div class="col col--4">{include "product/@listItem.latte" product=>$row}</div>

          {/foreach}

        </div>
        {/snippet}

        {if count($productsData) == 0}<p class="hey">Nejsou zde žádné položky</p>{/if}

        {*
        <div class="section__pages">

          <p><a href="#" class="link link--down">Zobrazit další produkty</a></p>

          <nav class="pagination">
            <a href="#" class="pagination__prev">Předchozí</a>
            <a href="#">1</a>
            <span class="paginator__current">2</span>
            <a href="#">3</a>
            <span class="paginator__space">&hellip;</span>
            <a href="#">19</a>
            <a href="#" class="pagination__next">Následující</a>
          </nav>

        </div>
        *}

      </div>

    </section>

{/block}