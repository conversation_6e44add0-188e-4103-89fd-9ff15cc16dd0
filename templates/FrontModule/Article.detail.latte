{$pageTitle       = (empty($article->arttitle) ? $article->artname :$article->arttitle)}
{$pageKeywords    = $article->artkeywords}
{$pageDescription = strip_tags($article->artdescription)}
{$pageImage       = $baseUri."/".($article|getArtPicName:'detail')}
{if !empty($article->artrobots)}
  {$pageRobots = $article->artrobots}
{/if}

{block #content}

    <div class="content-header content-header--detail">

      <div class="container container--short">

        {php
          $breadCrumbs = [];

          $breadCrumbs[] = [
            "url"   => $presenter->link("Page:detail", 74, 'clanky'),
            "title" => "Články"
          ];

          $breadCrumbs[] = [
            "url"   => $presenter->link("Page:detail", [id=>74, key=>'clanky', ti => $article->arttypid]),
            "title" => $enum_arttypid[$article->arttypid]
          ];

          $breadCrumbs[] = [
            "url"   => "",
            "title" => $pageTitle
          ];
        }

        {include @breadcrumb.latte items => $breadCrumbs}

      </div>

    </div>

    <section class="article-detail">

      <div class="container container--short">

        <div class="article-detail__head">

          <picture>
            <source srcset="{$baseUri}/{($article|getArtPicNameWebp:'912x454')|noescape}" type = "image/webp">
            <img src="{$baseUri}/{($article|getArtPicName:'912x454')|noescape}" alt="{$pageTitle}" width="912" height="454">
          </picture>

          <div class="article-detail__meta">

            <h1>
              <small>{$enum_arttypid[$article->arttypid]}</small>
              {$pageTitle}
              <small>Vydáno: {$article->artdate|date:'d.m.Y'}</small>
              {if $adminLogIn} <a href="{plink :Admin:Article:edit, $article->artid}" target="admin">E</a>{/if}
            </h1>

            {ifset $author}
            {include article/@author.latte admin=>$author}
            {/ifset}
          </div>

        </div>

        <div class="article-detail__content">

          {$article->artbody|noescape}

          {if isset($product)}

          <article class="product product--top">

            <div class="product__image">

              {include product/@image.latte product=>$product}

            </div>

              <div class="product__content">
              <h3>
                {$product|getProNameCatalog} {$product->manname}
              </h3>
              <p class="product__description">
                {$product->prodescs|truncate:120}
              </p>
            </div>
            <div class="product__meta">
              {include product/@priceStock.latte product=>$product}
              <p>
                {include product/@basketAdd.latte product=>$product}
              </p>
            </div>

          </article>

          <h2>Bodové hodnocení</h2>

          <div class="article-detail__rating">

            <ul>
              {if !empty($product->proratinging)}<li>Složení <strong>{$product->proratinging} z 10</strong></li>{/if}
              {if !empty($product->proratingmis)}<li>Rozmíchatelnost <strong>{$product->proratingmis} z 10</strong></li>{/if}
              {if !empty($product->proratingdig)}<li>Stravitelnost <strong>{$product->proratingdig} z 10</strong></li>{/if}
              {if !empty($product->proratingtas)}<li>Chuť <strong>{$product->proratingtas} z 10</strong></li>{/if}
            </ul>

            {if !empty($product->proratingall)}
            <p class="article-detail__stars">
              Celkové hodnocení
              <br>
              {for $i = 1; $i <= 10; $i++}
                <span class="star star--{if $i <= $product->proratingall}100{else}0{/if}"></span>
              {/for}
              <br>
              <strong>{$product->proratingall} z 10</strong>
            </p>
            {/if}
          </div>

          {/if}

          <div class="article-detail__author">

            {ifset $author}
            {include article/@author.latte admin=>$author, isSmall=>FALSE}
            {/ifset}

          </div>

        </div>

      </div>

    </section>

    {include article/@articles.latte articles => $footerArticles}

    {include @shop.latte}

    {include @instagram.latte}

{/block}
