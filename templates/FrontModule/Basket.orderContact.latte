{default $pageTitle = '<PERSON><PERSON>c<PERSON> údaje'}
{default $pageRobots = "nofollow,noindex"}

{* blok po header  *}
{block #afterHeader}
  {include @orderProgress.latte}
{/block}

{block #content}

{include @gtagGa3Event.latte type=>"checkout", products=>$productRows, step=>2}

<section class="section section--short">

  {form orderContactForm}
  {include @formErrors.latte form=>$form}

  <div class="container container--sidebar">

    <div class="section__content">

      {if $userRow->usrid == 0}
      <div class="hey">

        <p class="hey__icon">
          <span class="icon icon--user">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#user" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
        </p>
        <p class="hey__info">
          <strong>Už u nás máte svůj <PERSON>?</strong><br>
          Přihlaste se a my to vyplníme za vás
        </p>
        <p><a href="{plink User:login, from=>basket}" class="btn">přihlásit</a></p>

      </div>
      {/if}

      <h2>Osobní údaje</h2>

      <div class="form">

        <p>
          <label n:name="ordiname">Jméno <span class="form__required">*</span></label><br>
          <input type="text" n:name="ordiname">
          <br>
          <label n:name="ordilname">Příjmení <span class="form__required">*</span></label><br>
          <input type="text" n:name="ordilname">
          <br>
          <label n:name="ordmail">Email <span class="form__required">*</span></label><br>
          <input type="text" n:name="ordmail">
          <br>
          <label n:name="ordtel">Telefon <span class="form__required">*</span></label><br>
          <input type="text" n:name="ordtel">
        </p>

      </div>

      <h2>Fakturační a dodací adresa</h2>

      <div class="form">

        <p>
          <label n:name="ordistreet">Ulice <span class="form__required">*</span></label><br>
          <input type="text" n:name="ordistreet">
          <br>
          <label n:name="ordistreetno">Číslo domu <span class="form__required">*</span></label><br>
          <input type="text" n:name="ordistreetno">
          <br>
          <label n:name="ordicity">Město <span class="form__required">*</span></label><br>
          <input type="text" n:name="ordicity">
          <br>
          <label n:name="ordipostcode">PSČ <span class="form__required">*</span></label><br>
          <input type="text" n:name="ordipostcode">
          <br>
          <label for="name">Země</label><br>
          {$enum_countries[$curId]}
        </p>

        <p>
          <label class="form-checkbox js-reveal" data-reveal="firma">
            <input type="checkbox" name="check2" value="value2">
            <span class="form-checkbox__label">Nakupuji na firmu</span>
            <span class="form-checkbox__checker"></span>
          </label>
          <br>
          <span class="reveal" id="firma">
            <label n:name="ordic">IČO</label><br>
            <input type="text"  n:name="ordic">
            <br>
            <label n:name="orddic">DIČ</label><br>
            <input type="text"  n:name="orddic">
            <br>
            <label n:name="ordifirname">Název firmy</label><br>
            <input type="text"  n:name="ordifirname">
            <br>
          </span>
        </p>

        <p>
          <label class="form-checkbox js-reveal" data-reveal="dorucovaci-adresa" n:name="shipto">
            <input type="checkbox" n:name="shipto">
            <span class="form-checkbox__label">Doručit na jinou adresu</span>
            <span class="form-checkbox__checker"></span>
          </label>
          <br>
          <span class="reveal{if $form["shipto"]->value} is-visible{/if}" id="dorucovaci-adresa">
            <label n:name="ordstname">Jméno <span class="form__required">*</span></label><br>
            <input type="text" n:name="ordstname">
            <br>
            <label n:name="ordstlname">Příjmení <span class="form__required">*</span></label><br>
            <input type="text" n:name="ordstlname">
            <br>
            <label n:name="ordststreet">Ulice <span class="form__required">*</span></label><br>
            <input type="text" n:name="ordststreet">
            <br>
            <label n:name="ordststreetno">Číslo domu <span class="form__required">*</span></label><br>
            <input type="text" n:name="ordststreetno">
            <br>
            <label n:name="ordstcity">Město <span class="form__required">*</span></label><br>
            <input type="text" n:name="ordstcity">
            <br>
            <label n:name="ordstpostcode">PSČ <span class="form__required">*</span></label><br>
            <input type="text" n:name="ordstpostcode">
            <br>
            <label for="name">Země</label><br>
            {$enum_countries[$curId]}
          </span>
        </p>

        <p>
          <label class="form-checkbox js-reveal" data-reveal="poznamka">
            <input type="checkbox" name="check2" value="value2">
            <span class="form-checkbox__label">Přidat poznámku</span>
            <span class="form-checkbox__checker"></span>
          </label>
          <br>
          <span class="reveal" id="poznamka">
            <label n:name="ordnote">Poznámka</label><br>
            <textarea n:name="ordnote" rows="4" cols="50"></textarea>
          </span>
        </p>

        {ifset $form["antispam"]}
        <p class="antispam">
          {label antispam /}<br>
          {input antispam}
        </p>
        {/ifset}

      </div>

      <h2></h2>
      <div class="form">
        <p>
          <label class="form-checkbox" n:name="ordheurekagdpr">
            <input type="checkbox" n:name="ordheurekagdpr">
            <span class="form-checkbox__label">Nesouhlasím se zasíláním dotazníku spokojenosti v rámci programu Heuréka Ověřeno zákazníky a Zboží.cz</span>
            <span class="form-checkbox__checker"></span>
          </label>
          {ifset $form["maillist"]}
          <br>
          <label class="form-checkbox" n:name="maillist">
            <input type="checkbox" n:name="maillist">
            <span class="form-checkbox__label">Chci dostávat novinky emailem</span>
            <span class="form-checkbox__checker"></span>
          </label>
          {/ifset}

          {ifset $form["ordregisterpassw"]}
            <label class="form-checkbox js-reveal" data-reveal="heslo">
              <input type="checkbox" name="check2" value="value2">
              <span class="form-checkbox__label">Chci se zároveň registrovat a získat výhody</span>
              <span class="form-checkbox__checker"></span>
            </label>
            <br>
            <span class="reveal" id="heslo">
            <br>
            <label n:name="ordregisterpassw">Heslo</label><br>
            <input type="text" n:name="ordregisterpassw">
            </span>
          {/ifset}

        </p>

      </div>

    </div>

    <aside class="sidebar sidebar--order">

      {include @orderSidebar.latte}

    </aside>


  </div>

  <div class="container">

    <div class="order-steps">

      <p>
        <a href="{plink orderDelMode}" class="link link--back">Předchozí krok</a>
      </p>

      <p>
        <span class="order-steps__note">
          Odesláním objednávky souhlasíte s obchodními podmínkami a podmínkami ochrany osobních údajů
        </span>
      </p>

      <p>
        <button type="submit" class="btn btn--big">
          odeslat objednávku
          <span class="icon icon--next">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#next" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
        </button>
      </p>

    </div>

  </div>

  {/form}

</section>

{/block}
