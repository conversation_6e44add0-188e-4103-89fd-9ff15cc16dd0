{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<SHOP>

{php
  $alzaPath = [];
}

{foreach $rows as $row}
<SHOPITEM>
<ITEM_ID>{$row->procodep}</ITEM_ID>
<EAN>{$row->procode2}</EAN>
{php
  if (!empty($row->proidmas)) {
    $fileName = ($row->propicnamemas != "" ? trim($row->propicnamemas).'.jpg' : $row->procodemas.'.jpg');
  } else {
    $fileName = ($row->propicname != "" ? trim($row->propicname).'.jpg' : $row->procode.'.jpg');
  }
  $fileName = rawurlencode($fileName);

  $imgVer = ($fileName|getPicTimeStamp);
}

{php
  $proname = ($row|getProNameAgregators);
  $proPrice = round($row->proprice1a / (1+($row->provat / 100)), 2);
}

<PRODUCTNAME>{$proname}</PRODUCTNAME>
<DELIVERY_DATE>{$row->proaccess}</DELIVERY_DATE>
<QTY>{$row->proqty}</QTY>
<PRICE>{$proPrice}</PRICE>
<VAT>{$row->provat}</VAT>
</SHOPITEM>
{/foreach}
</SHOP>