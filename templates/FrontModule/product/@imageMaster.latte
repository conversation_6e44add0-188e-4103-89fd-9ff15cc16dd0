{default $imageWidth = 80}
<p>
  <a href="{plink Product:detail, $product->proid, ($product|getProKey)}">
    <picture>
      <source srcset="{$baseUri}/{($product|getProductPicNameMasterWebp:$imageWidth . "x" . $imageWidth)|noescape}" type="image/webp">
      <img src="{$baseUri}/{($product|getProductPicNameMaster:$imageWidth . "x" . $imageWidth)|noescape}" alt="{$product->proname}" width="{$imageWidth}" height="{$imageWidth}" loading="lazy">
    </picture>
  </a>
</p>