<h2>
  S<PERSON>dov<PERSON> dostupnost
  <br>
  <a href="{plink Product:detail, $product->proid, ($product|getProKey)}">{$product->proname}</a>
</h2>

<div class="modal__close">
  <span class="icon icon--close">
    <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <use xlink:href="{$baseUri}/img/icons.svg#close" x="0" y="0" width="100%" height="100%"></use>
    </svg>
  </span>
</div>

<ul class="modal__availability">
  <li>
    <strong>Sklad a prodejna Praha 8</strong>
    <span class="icon icon--next">
      <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <use xlink:href="{$baseUri}/img/icons.svg#next" x="0" y="0" width="100%" height="100%"></use>
      </svg>
    </span>
    <span class="store{if $product->proqty_store > 0} is-success{else} is-danger{/if}">
      {if $product->proqty_store > 0}<strong>Skladem</strong> {$product|getQtyTextDetail:"store"}{else}<strong>Není skladem</strong>{if !empty($product->proaccesstext)} ({$product->proaccesstext}){/if}{/if}
    </span>
  </li>
  <li>
    <strong>Prodejna Praha 10</strong>
    <span class="icon icon--next">
      <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <use xlink:href="{$baseUri}/img/icons.svg#next" x="0" y="0" width="100%" height="100%"></use>
      </svg>
    </span>
    <span class="store{if $product->proqty_shop1 > 0} is-success{else} is-danger{/if}">
      {if $product->proqty_shop1 > 0}<strong>Skladem</strong> {$product|getQtyTextDetail:"shop1"}{else}<strong>Není skladem</strong>{if !empty($product->proaccesstext)} ({$product->proaccesstext}){/if}{/if}
    </span>
  </li>
</ul>