{default $limitRows = 0}
{foreach $products as $row}
  {if $limitRows == 0 || $iterator->getCounter() <= $limitRows}

    {if $limitRows == 0 && $iterator->getCounter() == 4}
    <span class="reveal" id="souvreval">
    {/if}
    <article class="product product--top">
      <p class="product__top">
        {$iterator->getCounter()}.
      </p>

      <div class="product__image">

        <p>
          <a href="{plink Product:detail, $row->proid, ($row|getProKey)}">
            <picture>
              <source srcset="{$baseUri}/{($row|getProductPicNameWebp:'80x80')|noescape}" type="image/webp">
              <img src="{$baseUri}/{($row|getProductPicName:'80x80')|noescape}" alt="{$row->proname}" width="80" height="80" loading="lazy">
            </picture>
          </a>
        </p>

      </div>

        <div class="product__content">
        <h3>
          <a href="{plink Product:detail, $row->proid, ($row|getProKey)}">{$row->proname} {$row->manname}</a>
        </h3>
        <p class="product__description">
          {$row->prodescs|truncate:120}
        </p>
      </div>
      <div class="product__meta">
        <p class="product__price">
          <strong>{$row->proprice|formatPrice:$curId}</strong>
          {if ($row|isPromo)}<del>{$row->propricecom|formatPrice:$curId}</del>{/if}
          <br>
          {include @stock.latte product=>$row}
        </p>
        <p>
          {include @basketAdd.latte product=>$row}
        </p>
      </div>
    </article>
    {if $limitRows == 0 && $iterator->isLast() && $iterator->getCounter() >= 4}
    </span>

    <div class="section__pages section__pages--full">
      <p><a href="#souvreval" class="link link--down js-reveal">Zobrazit více produktů</a></p>
    </div>
    {/if}
  {/if}

{/foreach}