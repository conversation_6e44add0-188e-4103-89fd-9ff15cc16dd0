          {if !empty($product->prorating)}
          <p class="product-photo__stars">
            {php
            $number = explode(',', $product->prorating);
            $cnt = 0;
            }
            {for $i = 1; $i <= (int)$number[0]; $i++}
            {php $cnt++;}
              <a href="{plink productVote, $product->proid, $cnt}" rel="nofollow" class="star star--100"></a>
            {/for}
            {if !empty($number[1])}
            {php $cnt++; if ($number[1] == 5) $number[1] = 50;}
            <a href="{plink productVote, $product->proid, $cnt}" rel="nofollow" class="star star--{$number[1]}"></a>
            {/if}
            {if $cnt < 5}
            {for $i = 1; $i <= 5 - $cnt; $i++}
            {php $cnt++;}
            <a href="{plink productVote, $product->proid, $cnt}" rel="nofollow" class="star star--0"></a>
            {/for}
            {/if}
          </p>
          {/if}