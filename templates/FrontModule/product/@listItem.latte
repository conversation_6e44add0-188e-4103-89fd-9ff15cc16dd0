{default $counter=0}
{default $type=""}
{default $hideFav=FALSE}
{php
  $class = "";
  $imgWidth = "222";
  if ($type === 'side') {
    $class = " product--top product--side";
    $imgWidth = 115;
  } else if ($type === 'sum') {
    $class = " product--top product--sum";
    $imgWidth = 115;
  }
}
<article class="product{$class}">

  <div class="product__image">

    {include @image.latte product=>$product, imageWidth=>$imgWidth}

    {if $type === ""}

      <p class="product__tags">
      {include @tags.latte product=>$product}
      </p>

      <p class="product__discount">
        {include @discount.latte product=>$product}
      </p>

      {if !$hideFav}
      <p class="product__fav">
        {if !isset($userFavorites[$product->proid])}
        {* nemá v oblíbených - může přidat *}

        <a n:href="favAdd! $product->proid" class="ajax" rel="nofollow" title="{_'Přidat k oblíbeným'}" data-naja-history="off">
          <span class="icon icon--heart">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#heart" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
        </a>

        {else}

        <a n:href="favRem! $product->proid" class="ajax" rel="nofollow" title="{_'Odebrat z oblíbených'}" data-naja-history="off">
          <span class="icon icon--heart-full">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#heart-full" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
        </a>

        {/if}
      </p>
      {/if}

    {/if}

  </div>

  <h3>
    <a href="{plink Product:detail, $product->proid, ($product|getProKey)}"> {$product|getProNameCatalog} {$product->manname}</a>
  </h3>

  {if $type === ""}

  <p class="product__description">
    {$product->prodescs|truncate:110}
  </p>

  {/if}

  <div class="product__meta">

    {include @priceStock.latte product=>$product}

    <p>
      {include @basketAdd.latte product=>$product}
    </p>
  </div>

</article>