<!DOCTYPE html>
<html lang="cs">

{default $urlkey = ''}
{default $lng    = ''}
{default $pageTitle       = $presenter->config["INDEX_TITLE"]}
{default $pageDescription = $presenter->config["INDEX_DESC"]}
{default $pageImage       = $baseUri."/img/goldfitness.jpg"}

<head>

  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <link rel="preload" as="font" type="font/woff2" href="{$baseUri}/fonts/barlow-v5-latin-ext_latin-regular.woff2" crossorigin="crossorigin">
  <link rel="preload" as="font" type="font/woff2" href="{$baseUri}/fonts/barlow-v5-latin-ext_latin-700.woff2" crossorigin="crossorigin">
  <link rel="preload" as="font" type="font/woff2" href="{$baseUri}/fonts/barlow-v5-latin-ext_latin-500.woff2" crossorigin="crossorigin">
  <link rel="preload" as="font" type="font/woff2" href="{$baseUri}/fonts/barlow-v5-latin-ext_latin-600.woff2" crossorigin="crossorigin">

  <meta name="description" content="{$pageDescription|strip|truncate:160}">
  {if isset($pageRobots)}
  <meta name="robots" content="{$pageRobots}">
  <meta name="googlebot" content="{$pageRobots}">
  {else}
  <meta name="robots" content="index,follow">
  <meta name="googlebot" content="index,follow,snippet,archive">
  {/if}

  <title>{$pageTitle} | Goldfitness.cz</title>

  {if isset($canonicalUrl)}
  <link rel="canonical" href="{$canonicalUrl}">
  {/if}

  <link rel="stylesheet" href="{$baseUri}/css/styles.css?v={filemtime( 'css/styles.css' )}" type="text/css">

{*<!-- sociální sítě -->*}
  <meta property="og:title" itemprop="name" name="twitter:title" content="{$pageTitle} - GOLDFITNESS">
  <meta property="og:url" name="twitter:url" content="{plink //this}">
  <meta property="og:image" itemprop="image" name="twitter:image" content="{$pageImage}">
  <meta property="og:description" itemprop="description" name="twitter:description" content="{$pageDescription|strip|truncate:160, ''}">

	<meta name="facebook-domain-verification" content="6mzmkhph2q769aicrpugpvc10zqnnt" />
	<meta name="facebook-domain-verification" content="p3f11cn9rjgt5ekrrkisu7zxvhffd0" />

  <!-- Google Tag Manager -->
  <script>(function(w,d,s,l,i){ w[l]=w[l]||[];w[l].push({ 'gtm.start':
  new Date().getTime(),event:'gtm.js' });var f=d.getElementsByTagName(s)[0],
  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
  'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','GTM-PVDL9TL');</script>
  <!-- End Google Tag Manager -->

  {include @gTagPageType.latte}

  {block addToHead}{/block}
</head>

<body>
  <!-- Google Tag Manager (noscript) -->
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-PVDL9TL" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
  <!-- End Google Tag Manager (noscript) -->

  {if $presenter->isLinkCurrent("Basket:*")}
  {* hlavička jen v košíku *}
  <header class="header header--simple">

    <div class="container">

      <div class="header__body">

        <h1>
          <a href="{$baseUri}"><img src="{$baseUri}/img/logo.svg" alt="Goldfitness" width="127" height="127"></a>
        </h1>

        {include @headerTopContact.latte}

        {*
        <div class="basket" n:snippet="basketWindowOrder">

          <a href="{plink Basket:default}">
            <span class="icon icon--basket">
              <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <use xlink:href="{$baseUri}/img/icons.svg#basket" x="0" y="0" width="100%" height="100%"></use>
              </svg>
            </span>
            {if $basket->itemsCnt > 0}
            <span class="count">{$basket->itemsCnt}</span>
            {($basket->priceSumVat-$basket->discountVal)|formatPrice:$curId}
            {else}
            <span class="count">0</span>
            {0|formatPrice:$curId}
            {/if}
          </a>

        </div>
        *}
      </div>

    </div>

  </header>

  {else}

  <header class="header">

    <div class="header-top">

      <div class="container">

        <nav class="header-top__nav">
          <ul>
            <li class="has-submenu">
              <a href="#">Vše o nákupu</a>
              <ul>
                {foreach $menuArticle[2] as $row}
                  <li><a href="{plink Article:detail $row->artid, ($row->arturlkey|getUrlKey:$row->artname)}">{$row->artname}</a></li>
                {/foreach}
              </ul>
            </li>
            <li><a href="{plink Comment:default}">Poradna</a></li>
            <li><a href="{plink Page:detail 74, 'clanky'}">Články</a></li>
            <li><a href="{plink Page:detail 62, 'kamenny-obchod'}">Prodejny</a></li>
            <li><a href="{plink Page:detail 77, 'kontakt'}">Kontakt</a></li>
          </ul>
        </nav>

        {include @headerTopContact.latte}

        <div class="header-top__lang">
        {if $presenter->curId == 1}
				  <img src="{$baseUri}/img/cs.svg" alt="česky" width="20" height="20">
				  <a href="{$baseUri}?d=sk"><img src="{$baseUri}/img/sk.svg" alt="slovensky" width="20" height="20"></a>
				  <a href="{$baseUri}?d=sk"><img src="{$baseUri}/img/eu.svg" alt="euro" width="20" height="20"></a>
				{else}
				  <a href="{$baseUri}?d=cz"><img src="{$baseUri}/img/cs.svg" alt="česky" width="20" height="20"></a>
				  <img src="{$baseUri}/img/sk.svg" alt="slovensky" width="20" height="20">
				  <img src="{$baseUri}/img/eu.svg" alt="euro" width="20" height="20">
				{/if}
        </div>

      </div>

    </div>

    <div class="container header__body">

      <div class="header__logo">
        <a href="{$baseUri}"><img src="{$baseUri}/img/logo.svg" alt="Goldfitness" width="127" height="127"></a>
      </div>

      <div class="search">

        <div class="form-search">
          {form searchForm}
          {input fulltext id=>"mainsearch", placeholder=>"Co hledáte? např. hořčík, protein..."}
          <button>
            <span class="icon icon--magnifier">
              <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <use xlink:href="{$baseUri}/img/icons.svg#magnifier" x="0" y="0" width="100%" height="100%"></use>
              </svg>
            </span>
          </button>
          {/form}
        </div>

        {snippet mainSearchAutocomplete}
        {if isset($showSearchResults) && $showSearchResults}
        <div class="search__detail{if isset($showSearchResults) && $showSearchResults} is-open{/if}">

          <div class="search__close js-search-close">
            <span class="icon icon--close">
              <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <use xlink:href="{$baseUri}/img/icons.svg#close" x="0" y="0" width="100%" height="100%"></use>
              </svg>
            </span>
          </div>

          <div class="search__content">
            {if !empty($fulltextSearchs) && count($fulltextSearchs)}
            <h2>Nejhledanějsí fráze</h2>

            <ul>
              {foreach $fulltextSearchs as $text}
                {if $iterator->getCounter()<=3}
                <li><a href="{plink Search:default, 'fulltext'=>$text, 'name'=>NULL}">{$text|truncate:32}</a></li>
                {/if}
              {/foreach}
            </ul>

            {/if}

          </div>

          <div class="search__list">

            <h2>
              Produkty
              <span class="search__number">{count($productsSearchData)}</span>
            </h2>

            {foreach $productsSearchData as $row}
              {* po 10 záznamem vypadnu *}
              {if $iterator->getCounter() > 10}
                {php break; }
              {/if}

              <article class="product product--top product--basket">

                <div class="product__image">

                  <p>
                    <a href="{plink Product:detail, $row->proid, ($row|getProKey)}">
                      <picture>
                        <source srcset="{$baseUri}/{($row|getProductPicNameWebp:'list')|noescape}" type="image/webp">
                        <img src="{$baseUri}/{($row|getProductPicName:'list')|noescape}" alt="{$row->proname}" width="50" height="50">
                      </picture>
                    </a>
                  </p>

                </div>

                <div class="product__content">
                  <h3>
                    <a href="{plink Product:detail, $row->proid, ($row|getProKey)}">{$row->proname}</a>
                  </h3>
                  <small>{$row->manname}</small>
                </div>
                <p class="product__meta">
                  <span class="product__price">
                    {if ($row|isPromo)}<del>{$row->propricecom|formatPrice:$curId}</del>{/if}
                    <strong>{$row->proprice|formatPrice:$curId}</strong>
                  </span>

                  {include product/@basketAdd.latte product=>$row}

                </p>

              </article>

            {/foreach}

            {if count($productsSearchData) === 0}
              <p>Vašemu hledání neodpovídá žádná položka</p>
            {/if}

          </div>

          {if count($catalogsSearchData)}
          <div class="search__content">

            <h2>
              Kategorie
              <span class="search__number">{count($catalogsSearchData)}</span>
            </h2>

            <ul>
              {foreach $catalogsSearchData as $row}
                {* po 10 záznamem vypadnu *}
                {if $iterator->getCounter() > 5}
                  {php break; }
                {/if}
                <li><a href="{plink Catalog:detail $row->catid, ($row|getCatKey), NULL, NULL}">{$row->catname}</a></li>
              {/foreach}
            </ul>

          </div>
          {/if}

          {if count($manufacturersSearchData)}
          <div class="search__list">

            <h2>
              Výrobci
              <span class="search__number">{count($manufacturersSearchData)}</span>
            </h2>

            {foreach $manufacturersSearchData as $row}
              {if $iterator->getCounter() > 5}
                {php break; }
              {/if}

              <article class="product product--top product--basket">

                <div class="product__image">

                  <p>
                    <a href="{plink Manufacturer:detail $row->manid, (''|getUrlKey:$row->manname)}">
                      <picture>
                        <source srcset="{$baseUri}/{($row|getManufacturerPicNameWebp:'50x50')|noescape}" type = "image/webp">
                        <img src="{$baseUri}/{($row|getManufacturerPicName:'50x50')|noescape}" alt="{$row->artname}" width="50" height="50">
                      </picture>
                    </a>
                  </p>

                </div>

                <div class="product__content">
                  <h3>
                    <a href="{plink Manufacturer:detail $row->manid, (''|getUrlKey:$row->manname)}">{$row->manname}</a>
                  </h3>
                </div>
              </article>
            {/foreach}

          </div>
          {/if}

          {if count($articlesSearchData)}
          <div class="search__list">

            <h2>
              Články
              <span class="search__number">{count($articlesSearchData)}</span>
            </h2>

            {foreach $articlesSearchData as $row}
              {if $iterator->getCounter() > 5}
                {php break; }
              {/if}
              <a href="{plink Article:detail $row->artid, ($row->arturlkey|getUrlKey:$row->artname)}" class="article article--small">

                <p class="article__image">
                  <picture>
                    <source srcset="{$baseUri}/{($row|getArtPicNameWebp:'100x50')|noescape}" type = "image/webp">
                    <img src="{$baseUri}/{($row|getArtPicName:'100x50')|noescape}" alt="{$row->artname}" width="100" height="50">
                  </picture>
                </p>

                <div class="article__content">

                  <h3>
                    <strong>{$enum_arttypid[$row->arttypid]}</strong>
                    {$row->artname}
                  </h3>

                </div>

              </a>
            {/foreach}
          </div>
          {/if}

          <p class="center">

            <button class="btn js-search-submit">zobrazit všechny výsledky</button>
            <button class="btn btn--helper js-search-close">zavřít</button>

            <script type="text/javascript">
            jQuery(document).ready(function ($) {
              $('.js-search-submit').on('click', function () {
                $('#frm-searchForm').submit();
              });

              $('.js-search-close').on('click', function () {
                $('.search__detail').removeClass('is-open');
              });
            });
            </script>

          </p>

        </div>
        {/if}
        {/snippet}
      </div>


      <div class="header__nav">

        <div class="header__search">
          <span class="icon icon--magnifier">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#magnifier" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
        </div>

        <div class="login{if $userRow->usrid > 0} is-logged{elseif isset($loginFormHasErrors) && $loginFormHasErrors} is-open{/if}">
          <a href="{plink User:default}">
            <span class="icon icon--user">
              <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <use xlink:href="{$baseUri}/img/icons.svg#user" x="0" y="0" width="100%" height="100%"></use>
              </svg>
            </span>
            {if $userRow->usrid > 0}
            <span class="count is-success">
              <span class="icon icon--next">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#next" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
            </span>
            {/if}
          </a>

          <div class="login__detail">

            <div class="login__content">

              {if $userRow->usrid > 0}
                {* je přihlášený *}

              <div class="login__benefits">

                <h2>
                  <small>Přihlášen</small>: {if !empty($userRow->usriname)}{$userRow->usriname} {$userRow->usrilname}{else}{$userRow->usrmail}{/if}
                </h2>

                <ul>

                  <li><a href="{plink User:default}" class="link link--inverse">{_'Objednávky'}</a></li>
                  {if $userRow->usrprccat == 'c' || $userRow->usrprccat == 'd' || $userRow->usrprccat == 'e'}
                  <li><a href="{plink ProductList:orderBatch}" class="link link--inverse">{_'Hromadná objednávka'}</a></li>
                  {/if}
                  <li><a href="{plink User:bookmarks}" class="link link--inverse">{_'Oblíbené produkty'}</a></li>
                  <li><a href="{plink User:edit}" class="link link--inverse">{_'Osobní údaje'}</a></li>

                </ul>

                <p><a href="{plink User:logout}" class="link link--inverse">{_'Odhlásit se'}</a></p>

              </div>

              {else}
                {* není přihlášený *}
              <div class="login__form">
                {form userLoginForm}
                {include @formErrors.latte form=>$form}
                <p>
                    {label usrmail /}<br>
                    {input usrmail}
                </p>
                <p>
                  {label usrpassw /} <a href="{plink User:sendPassword}" class="form__helper">{_'Zapomenuté heslo'}</a><br>
                  {input usrpassw}
                </p>
                <p>
                  {input submit class=>"btn btn--big"}
                </p>


                <p class="separator separator--inverse">
                  <span>Nebo přihlásit přes</span>
                </p>

                <p>
                  <a href="{plink User:initSeznamLogin}" class="btn btn--login btn--seznam">
                    <span class="icon icon--login-seznam">
                      <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="/img/icons.svg#login-seznam" x="0" y="0" width="100%" height="100%"></use>
                      </svg>
                    </span>
                    Seznam.cz
                  </a>

                  {*
                  <a href="#" class="btn btn--login btn--google">
                    <span class="icon icon--color-login-google">
                      <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="/img/icons.svg#color-login-google" x="0" y="0" width="100%" height="100%"></use>
                      </svg>
                    </span>
                    Google
                  </a>
                  <a href="#" class="btn btn--login btn--facebook">
                    <span class="icon icon--login-facebook">
                      <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="/img/icons.svg#login-facebook" x="0" y="0" width="100%" height="100%"></use>
                      </svg>
                    </span>
                    Facebook
                  </a>
                  <a href="#" class="btn btn--login btn--apple">
                    <span class="icon icon--login-apple">
                      <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="/img/icons.svg#login-apple" x="0" y="0" width="100%" height="100%"></use>
                      </svg>
                    </span>
                    Apple
                  </a>
                  *}
                </p>

                {/form}
              </div>

              <div class="login__benefits">

                {* TODO: hodit do textového bloku do DB kvůli překladu *}
                <h2>
                  Výhody registrace<br>
                  na Gold Fitness
                </h2>

                <ul>
                  <li>Každý nákup o 3-10% levněji</li>
                  <li>3x do měsíce dostanete newsletter s akcemi</li>
                  <li>Víte první o akci Zlaté dny - slevy až 30%</li>
                </ul>

                <p><a href="{plink User:add}" class="link link--inverse">{_'Zaregistrovat se'}</a></p>

              </div>
              {/if}

            </div>

            <div class="login__close">
              <span class="icon icon--close">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#close" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
            </div>

          </div>
        </div>

        <a href="{plink Catalog:favorites}" class="header__fav">
          <span class="icon icon--heart">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#heart" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
          {snippet favorites}
          <span class="count is-danger">{count($userFavorites)}</span>
          {/snippet}
        </a>

        <div class="basket">
          {snippet basketWindow}

          <a href="{plink Basket:default}">
            <span class="icon icon--basket">
              <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <use xlink:href="{$baseUri}/img/icons.svg#basket" x="0" y="0" width="100%" height="100%"></use>
              </svg>
            </span>
            {if $basket->itemsCnt > 0}
            <span class="count">{$basket->itemsCnt}</span>
            <span class="basket__price">{($basket->priceSumVat-$basket->discountVal)|formatPrice:$curId}</span>
            {else}
            <span class="count">0</span>
            <span class="basket__price">{0|formatPrice:$curId}</span>
            {/if}
          </a>

          <div class="basket__detail">

            <div class="basket__content">
              {if $basket->itemsCnt > 0}

              {var $isNotDisc = FALSE;}

              {foreach $basketProducts as $row}

                {php if ($isNotDisc === FALSE && $row->pronotdisc === 1) $isNotDisc = TRUE}


                <article class="product product--top product--basket">

                  <div class="product__image">

                    <p>
                      <a href="{plink Product:detail, $row->proid, ($row|getProKey)}">
                        <picture>
                          <source srcset="{$baseUri}/{($row|getProductPicNameMasterWebp:'50x50')|noescape}" type="image/webp">
                          <img src="{$baseUri}/{($row|getProductPicNameMaster:'50x50')|noescape}" alt="{$row->proname}" width="50" height="50">
                        </picture>
                      </a>
                    </p>

                  </div>

                  <div class="product__content">
                    <h3>
                      {$row|getProNameCatalog} {if $row->pronotdisc === 1}<span class="notice">*</span>{/if}
                    </h3>
                    <small>{$row->manname}</small>
                  </div>
                  <p class="product__meta">
                    <span class="product__count">
                      {*
                      <span class="icon icon--minus">
                        <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                          <use xlink:href="{$baseUri}/img/icons.svg#minus" x="0" y="0" width="100%" height="100%"></use>
                        </svg>
                      </span>
                      *}
                      {if isset($basket->items[$row->proid])}{$basket->items[$row->proid]}{/if}
                      {*
                      <span class="icon icon--plus">
                        <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                          <use xlink:href="{$baseUri}/img/icons.svg#plus" x="0" y="0" width="100%" height="100%"></use>
                        </svg>
                      </span>
                      *}
                    </span>
                    <span class="product__price">
                      <strong>{$row->proprice|formatPrice:$curId}</strong>
                    </span>
                    {*
                    TODO doplnit odstranění z košíku
                    <a href="{plink Basket:delete, $row->proid}" class="product__remove">
                      <span class="icon icon--delete">
                        <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                          <use xlink:href="{$baseUri}/img/icons.svg#delete" x="0" y="0" width="100%" height="100%"></use>
                        </svg>
                      </span>
                    </a>
                    *}
                  </p>

                </article>

              {/foreach}

              {if $isNotDisc}<p><span class="notice">*</span> toto zboží se nezapočítává do slev</p>{/if}

              <div class="basket__sum">
                {_'Cena celkem'} <strong>{($basket->priceSumVat-$basket->discountVal)|formatPrice:$curId}</strong>
              </div>

              {else}
                <p>{_'Košík je prázdný'}</p>
              {/if}

            </div>

            <div class="basket__footer">

              <div class="order-benefits">

                <div class="order-benefits__info">

                  {include @benefitInfoDelFree.latte basket=>$basket, delFreeLimit=>$delFreeLimit}
                  {include @benefitInfoDiscount.latte basket=>$basket}

                </div>

              </div>

              <p class="center"><a href="{plink Basket:default}" class="btn btn--big">Pokračovat do košíku</a></p>

            </div>

          </div>

          {/snippet}

        </div>

        <div class="header__switcher">MENU</div>

      </div>

    </div>

    <nav class="nav">

      <div class="nav__helper">
        <p>
          Potřebujete poradit?<br>
          <a href="tel:+420603478564">+420 603 478 564</a>
          <small class="phone" data-start="9" data-end="17">Po-Pá 9:00 - 17:00</small>
        </p>
        <p>
        {if $presenter->curId == 1}
          <a href="{$baseUri}?d=cz" class="is-active"><img src="{$baseUri}/img/cs.svg" alt="česky" width="20" height="20"></a>
          <a href="{$baseUri}?d=sk"><img src="{$baseUri}/img/sk.svg" alt="slovensky" width="20" height="20"></a>
          <a href="{$baseUri}?d=sk"><img src="{$baseUri}/img/eu.svg" alt="euro" width="20" height="20"></a>
				{else}
          <a href="{$baseUri}?d=cz"><img src="{$baseUri}/img/cs.svg" alt="česky" width="20" height="20"></a>
          <a href="{$baseUri}?d=sk" class="is-active"><img src="{$baseUri}/img/sk.svg" alt="slovensky" width="20" height="20"></a>
          <a href="{$baseUri}?d=sk" class="is-active"><img src="{$baseUri}/img/eu.svg" alt="euro" width="20" height="20"></a>
				{/if}
        </p>
      </div>

      <div class="container">

        <ul>

          {foreach $menuCatalog as $row}
            {php
              $hasSubmenu = isset($row->subItems) && count($row->subItems) > 0;
            }

          <li class="nav__main{if $hasSubmenu} has-submenu{/if}">

            <a href="{plink Catalog:detail $row->catid, ($row|getCatKey), NULL, NULL}">{$row->catname}</a>

            {if $hasSubmenu}
            <div class="nav__inner">

              <ul class="row row--start row--nogap">

              {foreach $row->subItems as $srow}
                <li class="col col--3">
                  <a href="{plink Catalog:detail $srow->catid, ($srow|getCatKey), NULL, NULL}" class="category">
                    <span class="category__image">
                      {include catalog/@image.latte catalog=>$srow}
                    </span>
                    {$srow->catname}
                  </a>
                </li>
              {/foreach}

              </ul>

            </div>
            {/if}

          </li>

          {/foreach}

          <li class="nav__main">
            <a href="{plink Manufacturer:default}">Výrobci</a>
          </li>

          <li class="nav__main nav__main--info">
            <a href="{plink Comment:default}">Poradna</a>
          </li>
          <li class="nav__main nav__main--info">
            <a href="{plink Page:detail 74, 'clanky'}">Články</a>
          </li>
          <li class="nav__main nav__main--info">
            <a href="{plink Page:detail 62, 'kamenny-obchod'}">Prodejny</a>
          </li>
          <li class="nav__main nav__main--info">
            <a href="{plink Page:detail 77, 'kontakt'}">Kontakt</a>
          </li>

        </ul>

      </div>

    </nav>

  </header>
  {/if}

  {block #afterHeader}{/block}

  {*<!-- flash messages start -->*}
  {foreach $flashes as $flash}
    {php
      if ($flash->type === "err" || $flash->type === "danger") {
        $class = "is-danger";
        $icon = "danger";
      } else if ($flash->type === "info") {
        $class = "is-info";
        $icon = "leaf";
      } else {
        $class = "is-success";
        $icon = "success";
      }
    }

  <div class="alert {$class}">
    <div class="container">

      <span class="icon icon--{$icon}">
        <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <use xlink:href="{$baseUri}/img/icons.svg#{$icon}" x="0" y="0" width="100%" height="100%"></use>
        </svg>
      </span>

      {$flash->message}

    </div>
  </div>

  {/foreach}
  {*<!-- flash messages end -->*}

  <main>

  {if !empty($textBlocks["basket_del_message"])}
  <div class="alert is-danger">
  <div class="container">

    <span class="icon icon--danger">
      <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <use xlink:href="{$baseUri}/img/icons.svg#danger" x="0" y="0" width="100%" height="100%"></use>
      </svg>
    </span>

    <strong>{$textBlocks["basket_del_message"]->pagtitle}</strong><br>
    {$textBlocks["basket_del_message"]->pagbody|noescape}

  </div>
  </div>
  {/if}

    {include #content}

  </main>

{include @footer.latte}

<script src="{$baseUri}/js/libs/jquery-3.6.0.min.js"></script>
<script src="{$baseUri}/js/libs/splide/js/splide.min.js"></script>
<script src="{$baseUri}/js/scripts.js?v={filemtime('js/scripts.js')|noescape}"></script>

{* validace formů online*}
<script src="{$baseUri}/js/libs/netteForms.min.js"></script>

{if !$presenter->isLinkCurrent("Order:status")}

{* Naja ajax  inicializace*}
<script src="{$baseUri}/js/libs/Naja.min.js"></script>
<script type="text/javascript">
  naja.initialize();
</script>

{* ajax hlavní hledání *}
<script type="text/javascript">
jQuery(document).ready(function ($) {
  // pouze po zadání tří znaků
  $('#mainsearch').on('input',function(e){
    if( $(this).val().length > 2 ) {
      naja.makeRequest("GET", {plink mainSearch!}, { query: $(this).val() }, { history: false });
    }
  });
});
</script>

{* ajax košík zněma počtu kusů *}
<script type="text/javascript">
jQuery(document).ready(function ($) {
  $(document).on('change', '.basqty', function (e) {
    naja.makeRequest("GET", {plink basketAdd!}, { proid: $(this).data("proid"), count: $(this).val(), show: Boolean($(this).data("show")) }, { history: false });
  });
});
</script>

{* ajax varianty modal vložit do košíku event *}
<script type="text/javascript">
jQuery(document).ready(function ($) {
  $(document).on('click', '.variantsBasketAdd', function (e) {
    $proId = $(this).data("proid");
    naja.makeRequest("GET", {plink basketAdd!}, { proid: $proId, count: $("#variantQty_" + $proId).val() }, { history: false });
    //dále už nic nedělej
    return false;
  });
});
</script>

{* ajax kategorie filtr *}
<script type="text/javascript">
jQuery(document).ready(function ($) {
  $(document).on('change', '#frm-catalogSearchForm input', function (event) {
    naja.uiHandler.submitForm(event.target.form);
    //window.location.href = "#searchForm";
  });
});
</script>

<script type="text/javascript">
  $('#antispam').val({$presenter->config["ANTISPAM_NO"]});
  $('.antispam').hide();
</script>

{* modální okna *}
{snippet modalVariants}
{include @modalVariants.latte}
{/snippet}
{snippet modalBasketAdd}
{include @modalBasketAdd.latte}
{/snippet}

{block addToFooter}{/block}

<!-- Lime Talk Live Chat start --> <script type="text/javascript"> var limetalk = (function () { var lc = document.createElement("script"); lc.type = "text/javascript"; lc.async = true; lc.src = "//limetalk.com/livechat/71ab521c5b5e77facdccbc369cc1e769"; document.getElementsByTagName("head")[0].appendChild(lc); var fnr = function(fn) { var l = limetalk; if (l.readyList) { l.ready(fn); } else { l.rl = l.rl || [ ]; l.rl.push(fn); } }; fnr.ready = fnr; return fnr; })(); </script> <!-- Lime Talk Live Chat end -->

{/if}

<script>// <![CDATA[

window.index_conversion = window.index_conversion || {};
window.index_conversion = {
  measurement_id: "G-D45SY3ECT2",
  api_secret: "5aZDyYuFSTGKP7lT3woWyw"
}

if ((window.location.href.includes("/order/status"))) {
    for (var i in window.dataLayer) {
        if (window.dataLayer[i].event == "purchase") {
            window.index_conversion.transaction_id = window.dataLayer[i].ecommerce.purchase.actionField.id;
            window.index_conversion.value = window.dataLayer[i].ecommerce.purchase.actionField.revenue;
            window.index_conversion.currency = window.dataLayer[i].ecommerce.currencyCode;
        }
    }
}
// ]]></script>
<script type="text/javascript" src="https://app.goldfitness.cz"></script>

</body>
</html>