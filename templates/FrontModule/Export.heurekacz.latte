{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<SHOP>

{php
  $heurekaPath = [];
  $heurekaCatalogParams = [];
}

{foreach $rows as $row}
<SHOPITEM>
<ITEM_ID>{$row->proid}</ITEM_ID>
{if $view == 'heurekaczfull'}<IS_MASTER>{if isset($masterItems[$row->proid])}1{else}0{/if}</IS_MASTER>{/if}
<MANUFACTURER>{$row->manname}</MANUFACTURER>
<EAN>{$row->procode2|checkEan}</EAN>
{php
  if (!empty($row->proidmas)) {
    $fileName = ($row->propicnamemas != "" ? trim($row->propicnamemas).'.jpg' : $row->procodemas.'.jpg');
  } else {
    $fileName = ($row->propicname != "" ? trim($row->propicname).'.jpg' : $row->procode.'.jpg');
  }
  $fileName = rawurlencode($fileName);

  $imgVer = ($fileName|getPicTimeStamp);
}

{php
  $proname = ($row|getProNameAgregators);
}

<PRODUCTNAME>{$proname}</PRODUCTNAME>
<PRODUCT>{$proname} {if !empty($row->progift)}({$row->progift}){/if}</PRODUCT>
{if $view == 'heurekaczfull'}
<DESCRIPTION>{$row->prodesc}</DESCRIPTION>
<NUTRITION>{$row->pronutrition}</NUTRITION>
{else}
<DESCRIPTION>{$row->prodescs}</DESCRIPTION>
{/if}
<URL>{plink //:Front:Product:detail, $row->proid, ($row|getProKey)}</URL>
<ITEM_TYPE>new</ITEM_TYPE>
<DELIVERY_DATE>{$row->proaccess}</DELIVERY_DATE>
{if $showQty}<QTY>{$row->proqty}</QTY>{/if}
<PRICE_VAT>{$row->proprice1a}</PRICE_VAT>
{if $view == 'heurekaczfull'}
<PRODUCTNAME_ORIGINAL>{$row->proname}</PRODUCTNAME_ORIGINAL>
<VAT>{$row->provat}</VAT>
{/if}
{if !empty($row->promasid)}
<ITEMGROUP_ID>{$row->promasid}</ITEMGROUP_ID>
{/if}
<IMGURL>{$baseUri}/pic/product/detail/{$fileName}?v={$imgVer}</IMGURL>

{php
  $heurekaPath = ($row|getCatPathAgregator:$heurekaPath:$heurekaCatalogs);
}

{php
  $heurekaCatalogParams = ($row|getParamsHeureka:$heurekaCatalogParams);
}

{if isset($heurekaPath[$row->catid]) && $view != 'heurekaczfull'} 
<CATEGORYTEXT>{$heurekaPath[$row->catid]}</CATEGORYTEXT>
{else}
  {php
    $catPath = "";
    if (!empty($row->catid)) {
      $catPath = str_replace('|', ' | ', $row->catpath);
    }
  }
<CATEGORYTEXT>{$catPath}</CATEGORYTEXT>
{/if}
{if !empty($row->proorigin)}
<PARAM>
  <PARAM_NAME>Distribuce</PARAM_NAME>
  <VAL>{$row->proorigin}</VAL>
</PARAM>
{/if}
{if !empty($heurekaCatalogParamValues[$row->proid]) && is_array($heurekaCatalogParamValues[$row->proid])}
  {foreach $heurekaCatalogParamValues[$row->proid] as $par}
<PARAM>
  <PARAM_NAME>{$par->prpname}</PARAM_NAME>
  <VAL>{$par->prpvalue}</VAL>
</PARAM>
  {/foreach}
{/if}
{* parametry všechny *}
{if !empty($proParameters[$row->proid])}
  {foreach $proParameters[$row->proid] as $key=>$param}
<PARAM>
  <PARAM_NAME>{$param->prpname}</PARAM_NAME>
  <VAL>{$param->prpvalue}</VAL>
</PARAM>
  {/foreach}
{/if}
{php
  $delCost = -1;
}
{foreach $delModes as $key=>$rowd}
  {php
    if ($rowd->delcode == 'CESKA_POSTA_BALIKOVNA') {
      $rowd->delcode = 'BALIKOVNA_DEPOTAPI';
    }
  }
  <DELIVERY>
    <DELIVERY_ID>{$rowd->delcode}</DELIVERY_ID>
    {if isset($rowd->paymodes["paybefore"])}
    {php
      if ($row->prodelfree == 1) {
        $payCost = 0;
        $delCost = 0;
      } else {  
        $payCost = ($rowd->paymodes["paybefore"]->delpricelimitfrom > 0 && $rowd->paymodes["paybefore"]->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->paymodes["paybefore"]->delprice);
        $delCost = ($rowd->delpricelimitfrom > 0 && $rowd->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->delprice);
      }  
    }
    <DELIVERY_PRICE>{$payCost+$delCost}</DELIVERY_PRICE>
    {/if}
    {if isset($rowd->paymodes["dobirka"])}
    {php
      if ($row->prodelfree == 1) {
        $payCost = 0;
        $delCost = 0;
      } else {  
        $payCost = ($rowd->paymodes["dobirka"]->delpricelimitfrom > 0 && $rowd->paymodes["dobirka"]->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->paymodes["dobirka"]->delprice);
        $delCost = ($rowd->delpricelimitfrom > 0 && $rowd->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->delprice);
      }  
    }
    <DELIVERY_PRICE_COD>{$payCost+$delCost}</DELIVERY_PRICE_COD>
    {/if}
  </DELIVERY>
{/foreach}
{if $row->procpcheureka > 0}<HEUREKA_CPC>{$row->procpcheureka|formatNumber:2}</HEUREKA_CPC>{/if}
{if $row->proheurekaoffmas == 1 || $row->proheurekaoff == 1}<HEUREKA_CART>0</HEUREKA_CART>{/if}
</SHOPITEM>
{/foreach}
</SHOP>