<nav class="breadcrumb" role="navigation">
  <a href="{$baseUri}" class="breadcrumb__home">
    <span class="icon icon--home">
      <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <use xlink:href="{$baseUri}/img/icons.svg#home" x="0" y="0" width="100%" height="100%"></use>
      </svg>
    </span>
  </a>

  {foreach $items as $item}
    {if !$iterator->last}
      <a href="{$item["url"]}">{$item["title"]}</a>
    {else}
      {$item["title"]}
    {/if}
  {/foreach}

</nav>
