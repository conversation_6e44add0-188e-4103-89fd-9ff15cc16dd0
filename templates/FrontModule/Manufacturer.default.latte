{$pageTitle       = '<PERSON><PERSON><PERSON><PERSON><PERSON>'}
{$pageKeywords    = '<PERSON><PERSON><PERSON><PERSON><PERSON>'}

{block #content}

    <div class="content-header">

      <div class="container">

        {php
          $breadCrumbs = [];

          $breadCrumbs[] = [
            "url"   => "",
            "title" => $pageTitle
          ];
        }

        {include @breadcrumb.latte items => $breadCrumbs}

        <h1>{$pageTitle}</h1>

        <p class="content-header__description">
          Nabízíme zboží pouze vybraných výrobců. Každého výrobce pro vás pečlivě vybíráme...
        </p>

      </div>

    </div>

    <section class="section section--short">

      <div class="container">

        <ul class="sort sort--center">
          {foreach $dataRows as $key => $items}
          <li><a href="#letter{$key}">{$key|upper}</a></li>
          {/foreach}
        </ul>

        {foreach $dataRows as $key => $items}
          <a class="section__anchor" id="letter{$key}"></a>
          <h2 class="section__title">
            {$key|upper}
            <a href="#top" class="link link--up">nahoru</a>
          </h2>
          {foreach $items as $row}
          <div class="manufacturer">

          <div class="manufacturer__content">

            <h3>
              <a href="{plink Manufacturer:detail $row->manid, (''|getUrlKey:$row->manname)}">{$row->manname}</a>
              {if $adminLogIn} <a href="{plink :Admin:Manufacturer:edit, $row->manid}" target="admin">E</a>{/if}
            </h3>
            {$row->mandesc|noescape}

          </div>

          <p class="manufacturer__logo">
            <a href="{plink Manufacturer:detail $row->manid, (''|getUrlKey:$row->manname)}">
              <picture>
                <source srcset="{$baseUri}/{($row|getManufacturerPicNameWebp:'150x150')|noescape}" type="image/webp">
                <img src="{$baseUri}/{($row|getManufacturerPicName:'150x150')|noescape}" alt="{$row->manname}" width="150" height="150" loading="lazy">
              </picture>
            </a>
          </p>

        </div>
          {/foreach}
        {/foreach}

      </div>

    </section>

    {include @shop.latte}

    {include @instagram.latte}