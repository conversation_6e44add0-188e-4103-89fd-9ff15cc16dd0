{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<SHOP xmlns="http://www.zbozi.cz/ns/offer/1.0">
{php
  $zboziPath = [];
}
{foreach $rows as $row}
<SHOPITEM>
<ITEM_ID>{$row->proid}</ITEM_ID>
<EAN>{$row->procode2|checkEan}</EAN>
<MANUFACTURER>{$row->manname}</MANUFACTURER>
{php
  if (!empty($row->proidmas)) {
    $fileName = ($row->propicnamemas != "" ? trim($row->propicnamemas).'.jpg' : $row->procodemas.'.jpg');
  } else {
    $fileName = ($row->propicname != "" ? trim($row->propicname).'.jpg' : $row->procode.'.jpg');
  }
  $fileName = rawurlencode($fileName);

  $imgVer = ($fileName|getPicTimeStamp);
}

{php
  $proname = ($row|getProNameAgregators);
}
{if !empty($row->promasid)}
<ITEMGROUP_ID>{$row->promasid}</ITEMGROUP_ID>
{/if}
<PRODUCTNAME>{$proname}</PRODUCTNAME>
<PRODUCT>{$proname} {if !empty($row->progift)}({$row->progift}){/if}</PRODUCT>
<DESCRIPTION>{$row->prodescs}</DESCRIPTION>
<URL>{plink //:Front:Product:detail, $row->proid, ($row|getProKey)}</URL>
<DELIVERY_DATE>{$row->proaccess}</DELIVERY_DATE>
{if $row->proqty_store > 0}<SHOP_DEPOTS>13189384</SHOP_DEPOTS>{/if}{*Praha 8*}
{if $row->proqty_shop1 > 0}<SHOP_DEPOTS>2387399</SHOP_DEPOTS>{/if}{*Praha 10*}
{if $row->proqty_shop2 > 0}<SHOP_DEPOTS>13061142</SHOP_DEPOTS>{/if}{*Praha 2*}
<IMGURL>{$baseUri}/pic/product/detail/{$fileName}?v={$imgVer}</IMGURL>
<PRICE_VAT>{$row->proprice1a}</PRICE_VAT>
{if ($row->proprice1a < $row->proprice1com && $row->proprice1com > 0)}<LIST_PRICE>{$row->proprice1com}</LIST_PRICE>{/if}
{if $row->procpczbozi > 0}<MAX_CPC>{$row->procpczbozi|formatNumber:2}</MAX_CPC>{/if}
{php
  $zboziPath = ($row|getCatPathAgregator:$zboziPath:$zboziCatalogs);
}
{if isset($zboziPath[$row->catid])} 
<CATEGORYTEXT>{$zboziPath[$row->catid]}</CATEGORYTEXT>
{/if}
{php
  $delCost = -1;
}
  {if $row->proqty > 0}
  <DELIVERY>
    <DELIVERY_ID>VLASTNI_VYDEJNI_MISTA</DELIVERY_ID>
    <DELIVERY_PRICE>0</DELIVERY_PRICE>
    <DELIVERY_PRICE_COD>0</DELIVERY_PRICE_COD>
  </DELIVERY>
  {/if}
{foreach $delModes as $key=>$rowd}
  <DELIVERY>
    <DELIVERY_ID>{$rowd->delcode}</DELIVERY_ID>
    {if isset($rowd->paymodes["paybefore"])}
    {php
      if ($row->prodelfree == 1) {
        $payCost = 0;
        $delCost = 0;
      } else {
        $payCost = ($rowd->paymodes["paybefore"]->delpricelimitfrom > 0 && $rowd->paymodes["paybefore"]->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->paymodes["paybefore"]->delprice);
        $delCost = ($rowd->delpricelimitfrom > 0 && $rowd->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->delprice);
      }
    }
    <DELIVERY_PRICE>{$payCost+$delCost}</DELIVERY_PRICE>
    {/if}
    {if isset($rowd->paymodes["dobirka"])}
    {php
      if ($row->prodelfree == 1) {
        $payCost = 0;
        $delCost = 0;
      } else {
        $payCost = ($rowd->paymodes["dobirka"]->delpricelimitfrom > 0 && $rowd->paymodes["dobirka"]->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->paymodes["dobirka"]->delprice);
        $delCost = ($rowd->delpricelimitfrom > 0 && $rowd->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->delprice);
      }
    }
    <DELIVERY_PRICE_COD>{$payCost+$delCost}</DELIVERY_PRICE_COD>
    {/if}
  </DELIVERY>
{/foreach}
{if $row->procpczbozi > 0}
  <MAX_CPC>{$row->procpczbozi|formatNumber:2}</MAX_CPC>
  <MAX_CPC_SEARCH>{$row->procpczbozi|formatNumber:2}</MAX_CPC_SEARCH>
{/if}
</SHOPITEM>
{/foreach}
</SHOP>
