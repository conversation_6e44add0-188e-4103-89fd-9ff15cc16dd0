{$pageTitle       = (empty($page->pagtitle) ? $page->pagname :$page->pagtitle)}
{$pageKeywords    = $page->pagkeywords}
{$pageDescription = $page->pagdescription}
{if !empty($page->pagrobots)}
  {$pageRobots = $page->pagrobots}
{/if}

{block #content}

  <div class="content-header">

      <div class="container">

        {php
          $breadCrumbs = [];

          $breadCrumbs[] = [
            "url"   => "",
            "title" => $pageTitle
          ];
        }

        {include @breadcrumb.latte items => $breadCrumbs}

        <h1>Kontakt</h1>

        {form contactForm class=>"form"}
        {include @formErrors.latte form=>$form}

        <h2>Napište nám</h2>

        <p>
          {php echo $form['conname']->getLabel()}:<br>
          {php echo $form['conname']->getControl()->size(60)}
          <br>
          {php echo $form['conmail']->getLabel()->class('required')}:<br>
          {php echo $form['conmail']->getControl()->size(60)}
          <br>
          {php echo $form['congsm']->getLabel()}:<br>
          {php echo $form['congsm']->getControl()->size(30)}
          <br>
          <span class="antispam">
          {php echo $form['antispam']->getLabel()}:<br>
          {php echo $form['antispam']->getControl()}
          </span>
          {php echo $form['connote']->getLabel()}<br>
          {php echo $form['connote']->getControl()}
        </p>
        <p>
          {* TODO  napojit checkbox *}
          <label class="form-checkbox">
            <input type="checkbox" name="check2" value="value2">
            <span class="form-checkbox__label">Souhlasím se <a href="#">zpracováním osobních údajů</a> <span class="form__required">*</span></span>
            <span class="form-checkbox__checker"></span>
          </label>
        </p>
        <p class="center">
          {php echo $form['save']->getControl()->class("btn btn--big")->value('Odeslat zprávu')}
          {*<button type="submit" class="btn btn--big">Odeslat zprávu</button>*}
        </p>

        {/form}

        <p class="content-header__contact">
          E-shop + velkoobchod
          <a href="tel:+420 736 631 669">+420 736 631 669</a>
          <small class="phone" data-start="9" data-end="17">Po-Pá 9:00 - 17:00</small>
        </p>

        <p class="content-header__contact">
          Dotazy na zboží a objednávky
          <a href="mailto:<EMAIL>"><EMAIL></a>
          <small>Odpovíme do 24 hodin</small>
        </p>

      </div>

    </div>

    <section class="section">

      <div class="container">

        <div class="section__contact">

          <h2>Firemní údaje</h2>
          <p>
            GOLDFITNESS s.r.o.<br>
            Jaselská 289/31<br>
            160 00 - Praha 6<br>
            IČ: ********<br>
            DIČ: CZ********
          </p>

        </div>

        <div class="section__contact">
          <h2>Bankovní spojení</h2>
          <p>
            ČR: Raiffeisenbank **********/5500<br>
            IBAN: CZ705500000000**********<br>
            SWIFT: RZBCCZPP
          </p>
        </div>

      </div>

    </section>

    <section class="section section--follow">

      <div class="container">

        <div class="shop-detail">

          <div class="shop-detail__photo">
            <p>
              <picture>
                <source srcset="{$baseUri}/img/shop/shop-1-3.webp" type="image/webp">
                <img src="{$baseUri}/img/shop/shop-1-3.jpg" alt="" width="1029" height="528" loading="lazy">
              </picture>
            </p>
            <p class="shop-detail__gallery">
              <picture>
                <source srcset="{$baseUri}/img/shop/shop-1-4.webp" type="image/webp">
                <img src="{$baseUri}/img/shop/shop-1-4.jpg" alt="" width="327" height="252" loading="lazy">
              </picture>
              <picture>
                <source srcset="{$baseUri}/img/shop/shop-1-5.webp" type="image/webp">
                <img src="{$baseUri}/img/shop/shop-1-5.jpg" alt="" width="327" height="252" loading="lazy">
              </picture>
            </p>
          </div>

          <div class="shop-detail__content">

            <h2>GOLDFITNESS Prodejna Praha 10</h2>

            <p>
              <span class="icon icon--pin">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#pin" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
              V Olšinách 78/1270, 100 00 - Praha 10 - Strašnice
            </p>
            <p>
              <span class="icon icon--clock">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#clock" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
              Pondělí - Pátek: 9:30 - 18:30 hod.
            </p>
            <p>
              <span class="icon icon--phone">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#phone" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
              +420 274 774 632
            </p>

          </div>

          <p class="shop-detail__link"><a href="https://goo.gl/maps/GeK9xvUWhtE2" class="link" target="_blank" rel="noopener noreferrer">Zobrazit na mapě</a></p>

        </div>

        <div class="shop-detail">

          <div class="shop-detail__photo">
            <p>
              <picture>
                <source srcset="{$baseUri}/img/shop/shop-2-3.webp" type="image/webp">
                <img src="{$baseUri}/img/shop/shop-2-3.jpg" alt="" width="1029" height="528" loading="lazy">
              </picture>
            </p>
            <p class="shop-detail__gallery">
              <picture>
                <source srcset="{$baseUri}/img/shop/shop-2-4.webp" type="image/webp">
                <img src="{$baseUri}/img/shop/shop-2-4.jpg" alt="" width="327" height="252" loading="lazy">
              </picture>
              <picture>
                <source srcset="{$baseUri}/img/shop/shop-2-5.webp" type="image/webp">
                <img src="{$baseUri}/img/shop/shop-2-5.jpg" alt="" width="327" height="252" loading="lazy">
              </picture>
            </p>
          </div>

          <div class="shop-detail__content">

            <h2>GOLDFITNESS Sklad a prodejna Praha 8</h2>

            <p>
              <span class="icon icon--pin">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#pin" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
              Zdibská 2/229 (areál Zdibská), 182 00 - Praha 8 - Kobylisy
            </p>
            <p>
              <span class="icon icon--clock">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#clock" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
              Pondělí - Pátek: 9 - 17 hod.
            </p>
            <p>
              <span class="icon icon--phone">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#phone" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
              +420 736 631 669
            </p>

          </div>

          <p class="shop-detail__link"><a href="https://goo.gl/maps/n4PCWEvtmkm" class="link" target="_blank" rel="noopener noreferrer">Zobrazit na mapě</a></p>

        </div>

      </div>

    </section>

    <section class="section section--short">

      <div class="container">

        <h2>Náš tým</h2>

        <div class="row">

          {php
            $cnt = count($authors);
          }

          {foreach $authors as $row}
            {if $iterator->getCounter() === 1}
            <div class="col col--2">
            {/if}

            {if $iterator->getCounter() == ceil($cnt/2) + 1}
            </div>
            <div class="col col--2">
            {/if}

              <div class="contact-box contact-box--list">
                <p>
                  <span class="avatar">
                    <picture>
                      <source srcset="{$baseUri}/{($row|getAdminPicNameWebp:'110x110')|noescape}" type="image/webp">
                      <img src="{$baseUri}/{($row|getAdminPicName:'110x110')|noescape}" alt="{$row->admname}" width="110" height="110" loading="lazy">
                    </picture>
                  </span>
                </p>
                <p>
                  <strong>{$row->admname}</strong><br>
                  {$row->admfunction}
                  {if !empty($row->admcontel)}<br>telefon: <a href="tel:{$row->admcontel}">{$row->admcontel}</a>{/if}
                  {if !empty($row->admconemail)}<br>e-mail: <a href="mailto:{$row->admconemail}">{$row->admconemail}</a>{/if}
                </p>
              </div>

            {if $iterator->isLast()}
            </div>
            {/if}
          {/foreach}

        </div>

      </div>

    </section>

		{include @instagram.latte}

{/block}
