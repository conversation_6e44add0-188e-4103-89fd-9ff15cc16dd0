{$pageTitle       = (empty($page->pagtitle) ? $page->pagname :$page->pagtitle)}
{$pageKeywords    = $page->pagkeywords}
{$pageDescription = $page->pagdescription}
{if !empty($page->pagrobots)}
  {$pageRobots = $page->pagrobots}
{/if}

{block #content}

  <div class="content-header">

    <div class="container">

      {php
        $breadCrumbs = [];

        $breadCrumbs[] = [
          "url"   => "",
          "title" => $pageTitle
        ];
      }

      {include @breadcrumb.latte items => $breadCrumbs}

      <h1>{$page->pagname}</h1>

      {*
      <p class="content-header__description">
        Ptáte se proč nakupovat právě u nás?<br>
        &hellip;zkusíme Vám co nejjednodušeji odpovědět
      </p>
      *}

    </div>

  </div>

  <section class="section section--text">

    <div class="container">

      {$page->pagbody|noescape}

    </div>

  </section>

  {include @shop.latte}

  {include @instagram.latte}

{/block}
