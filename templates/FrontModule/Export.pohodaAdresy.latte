<?xml version="1.0" encoding="utf-8"?>
<dat:dataPack id="Ad002" ico="28883748" application="eshop" version = "2.0" note="Import adres"
xmlns:dat="http://www.stormware.cz/schema/version_2/data.xsd"
xmlns:adb="http://www.stormware.cz/schema/version_2/addressbook.xsd"
xmlns:ftr="http://www.stormware.cz/schema/version_2/filter.xsd"
xmlns:typ="http://www.stormware.cz/schema/version_2/type.xsd">

	<dat:dataPackItem id="AD001" version="2.0">
		<adb:addressbook version="2.0">
			{if $user->usrexportstatus > 0 && $forceNewContact == FALSE}
			<adb:actionType>
				<!-- aktualizacni import Adresy -->
				<adb:update>
				<!-- filtr pro vyhledani zaznamu -->
				<ftr:filter>
					<ftr:extId>
	          <typ:ids>{$user->usrid}</typ:ids>
            <typ:exSystemName>eshop</typ:exSystemName>
	          <typ:exSystemText>goldfitness.cz</typ:exSystemText>
          </ftr:extId>
				</ftr:filter>
			</adb:update>
			</adb:actionType>
			{/if}
			<adb:addressbookHeader>
				<adb:identity>
	        <typ:extId>
	          <typ:ids>{$user->usrid}</typ:ids>
	          <typ:exSystemName>eshop</typ:exSystemName>
	          <typ:exSystemText>goldfitness.cz</typ:exSystemText>
	        </typ:extId>
					<typ:address>
						{php
						  $company = (empty($user->usrifirname) ? $user->usrilname.' '.$user->usriname : $user->usrifirname);
						  $company = substr($company, 0, 32);
						  $name = (!empty($user->usrifirname) ? $user->usrilname.' '.$user->usriname : '');
						  $name = substr($name, 0, 32);
						}
						<typ:company>{$company}</typ:company>
						<typ:name>{$name}</typ:name>
						<typ:city>{$user->usricity}</typ:city>
						<typ:street>{$user->usristreet} {$user->usristreetno}</typ:street>
						<typ:zip>{$user->usripostcode}</typ:zip>
						<typ:ico>{$user->usric}</typ:ico>
						<typ:dic>{$user->usrdic}</typ:dic>
					</typ:address>
				</adb:identity>
				<adb:mobil>{$user->usrtel}</adb:mobil>
				<adb:email>{$user->usrmail}</adb:email>
				<adb:priceIDS>{$prcCats[$user->usrprccat]}</adb:priceIDS>
				<adb:note>Načteno z eshopu</adb:note>
			</adb:addressbookHeader>
		</adb:addressbook>
	</dat:dataPackItem>
</dat:dataPack>
