{$pageTitle = '<PERSON><PERSON>l<PERSON> objednávka'}

{block #content}

    <div class="content-header">

    <div class="container">

      {* sestavím drobky z katalogové cesty *}
      {php
        $breadCrumbs = [];

        $breadCrumbs[] = [
          "url"   => "",
          "title" => $pageTitle
        ];
      }

      {include @breadcrumb.latte items => $breadCrumbs}

      <h1>{$pageTitle}</h1>

    </div>

  </div>

  <section class="section section--short">

    <div class="container">


      {form pLSearchForm class=>"form form--full"}

    {include @formErrors.latte form=>$form}

    {php echo $form["id"]->getLabel()}
    {php echo $form["id"]->getControl()}

    {php echo $form["code"]->getLabel()}
    {php echo $form["code"]->getControl()}

    {php echo $form["name"]->getLabel()}
    {php echo $form["name"]->getControl()}
    <br>

    {php echo $form["manid"]->getLabel()}
    {php echo $form["manid"]->getControl()}

    {php echo $form["orderby"]->getLabel()}
    {php echo $form["orderby"]->getControl()}

    {input detailSearch class=>"btn btn--big"}

  {/form}
  
      <form method="post" action="{plink doBatchOrder}" class="form form--full">
      {foreach $productsData as $key => $row}
      {if $iterator->isFirst()}
      <div class="table__wrapper">
      <table>
        <tr>
          <th>Kód</th>
          <th>Název</th>
          <th>Cena</th>
          <th>Skladem</th>
          <th></th>

        </tr>
      {/if}
        <tr>
          <td>{$row->procode}</td>
          <td><a href="{plink Product:detail, $row->proid, ($row|getProKey)}">{$row->proname}</a></td>
          <td >{$row->propricec|formatPrice:$curId}</td>
          <td>{*$enu_proAccess[$row->proaccess]*}{$row->proqty} ks</td>
          <td><input type="text" name="i[{$row->proid}]" size="3"></td>

        </tr>
      {if $iterator->isLast()}
      </table>
      </div>
      {/if}
      {/foreach}
      <input type="submit" name="order" value="Vložit do košíku">
      </form>

  </div>
</section>

{/block}