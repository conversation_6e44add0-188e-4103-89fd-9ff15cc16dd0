{$pageTitle = 'Porovnání zboží'}

{block #content}

<div class="content-header">

  <div class="container">

    <h1>{$pageTitle}</h1>

  </div>

</div>

<section class="section section--short">

  {if count($products) > 0}
  <div class="table__wrapper">
    <table>
      <tr>
      <th>Produkt</th>
      {foreach $products as $pro}
      <td>
        <a href="{plink Product:detail, $pro->proid, ($pro|getProKey)}">

          <picture>
            <source srcset="{$baseUri}/{($pro|getProductPicNameWebp:'list')|noescape}" type="image/webp">
            <img src="{$baseUri}/{($pro|getProductPicName:'list')|noescape}" alt="{$pro->proname}" width="150">
          </picture>

        </a>
        <br>
        <strong>{$pro->proname}</strong>
      </td>
      {/foreach}
      </tr>
      <tr>
        <th>Koupit</th>
          {foreach $products as $pro}
          <td>
            {include product/@basketAdd.latte product=>$pro}
            <a class="btn btn--danger" href="{plink Product:compareDelete, $pro->proid}">Vymazat</a>
          </td>
          {/foreach}
      </tr>
      <tr>
        <th>Cena</th>
            {foreach $products as $pro}
            <td><strong>{$pro->proprice|formatPrice:$curId}</strong></td>
            {/foreach}
      </tr>
      <tr>
        <th>Forma</th>
            {foreach $products as $pro}
            <td>{ifset $enum_proforid[$pro->proforid]}<strong>{$enum_proforid[$pro->proforid]}</strong>{/ifset}</td>
            {/foreach}
      </tr>
      <tr>
        <th>Popis</th>
            {foreach $products as $pro}
        <td>{$pro->prodescs}</td>
            {/foreach}
      </tr>
      {foreach $paramsAll as $row}
      <tr>
        <th>{$row->prpname}</th>
        {foreach $products as $pro}
        <td>{ifset $pro["params"][$row->prpname]["prpvalue"]}{$pro["params"][$row->prpname]["prpvalue"]}{/ifset}</td>
        {/foreach}
      </tr>
      {/foreach}


    </table>
  </div>
  {else}
  <div class="container">
    <p>Vyberte nejprve nějaké zboží k porovnání.</p>
  </div>
  {/if}

</section>

</div>
{/block}
