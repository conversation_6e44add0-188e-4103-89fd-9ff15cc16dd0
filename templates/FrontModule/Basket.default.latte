{$pageTitle  = 'Koš<PERSON>'}
{$pageRobots = "nofollow,noindex"}

{* blok po header  *}
{block #afterHeader}
  {include @orderProgress.latte}
{/block}

{block #content}
    {* info přidány položky od minulého přihlášení *}
    {if $presenter->getParameter("add") == 1}
    <div class="alert is-success">
    <div class="container">

      <span class="icon icon--basket">
        <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <use xlink:href="{$baseUri}/img/icons.svg#basket" x="0" y="0" width="100%" height="100%"></use>
        </svg>
      </span>

      Od minulého přihlášení vám zůstaly nějaké položky v košíku. Tady jsou...

      <span class="icon icon--delete">
        <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <use xlink:href="{$baseUri}/img/icons.svg#delete" x="0" y="0" width="100%" height="100%"></use>
        </svg>
      </span>

    </div>
    </div>
    {/if}

  {if count($basket->items) > 0}
    {snippet basketContent}
    {form basketForm}
    {include @formErrors.latte form=>$form}
    <section class="section section--short">

      <div class="container container--sidebar">

        <div class="section__content">

          {if isset($basket->isDiscount) && $basket->isDiscount}<p>Akční zboží označené <span class="notice"><strong>červenou hvězdičkou</strong></span> se do slev <strong>nezapočítává</strong>.</p>{/if}

            {foreach $basket->items as $id=>$value}

              <article class="product product--top">

                <div class="product__image">

                  {include product/@imageMaster.latte product=>$productRows[$id], imageWidth=>80}

                </div>

                <div class="product__content">
                  {php $names = ($productRows[$id]|getProNameParts)}
                  <h3>
                    {$names[0]} {if $productRows[$id]->pronotdisc == 1}<span class="notice">*</span>{/if}
                  </h3>
                  <p class="product__description">
                    {$productRows[$id]->manname}{if !empty($names[1])} - {$names[1]}{/if}<br>
                    {include product/@stock.latte product=>$productRows[$id], showQty=>true}
                  </p>
                </div>
                <p class="product__meta">
                  <span class="product__count">
                    <span class="icon icon--minus">
                      <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="{$baseUri}/img/icons.svg#minus" x="0" y="0" width="100%" height="100%"></use>
                      </svg>
                    </span>
                    {input "count_".$productRows[$id]->proid class=>"basqty", "data-proid"=>$productRows[$id]->proid, "data-show"=>false}
                    <span class="icon icon--plus">
                      <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="{$baseUri}/img/icons.svg#plus" x="0" y="0" width="100%" height="100%"></use>
                      </svg>
                    </span>
                  </span>
                  <span class="product__price">
                    {($productRows[$id]->proprice)|formatPrice:$curId}/ks
                  </span>
                  <span class="product__price">
                    <strong>{($productRows[$id]->proprice*$value)|formatPrice:$curId}</strong>
                  </span>
                  <a n:href="basketRem! $productRows[$id]->proid" class="product__remove ajax" data-naja-history="off">
                    <span class="icon icon--delete">
                      <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="{$baseUri}/img/icons.svg#delete" x="0" y="0" width="100%" height="100%"></use>
                      </svg>
                    </span>
                  </a>
                </p>

              </article>

              {if isset($stockLimitReached[$productRows[$id]->proid])}
              <div class="warn is-danger">
                Bohužel aktuálně máme skladem jen {$stockLimitReached[$productRows[$id]->proid]} ks.
              </div>
              {/if}

            {/foreach}

            {* sleva *}
            <article class="product product--top product--order">

              <div class="product__image">

                <p><img src="{$baseUri}/img/discount.svg" alt="" width="80" height="80"></p>

              </div>

              <div class="product__content">
                <h3>
                  Sleva
                </h3>
                <p class="product__description">
                  {if $basket->discountVal > 0}{$basket->discountPer}%{/if}
                </p>
              </div>
              <p class="product__meta">
                <span class="product__price">
                  <strong>{if $basket->discountVal > 0}- {$basket->discountVal|formatPrice:$curId}{else}bez slevy{/if}</strong>
                </span>
              </p>

            </article>

            {if !empty($basket->coupon)}
            {* slevový kupón *}
            <article class="product product--top product--order">

              <div class="product__image">

                <p><img src="{$baseUri}/img/gift.svg" alt="" width="80" height="80"></p>

              </div>

              <div class="product__content">
                <h3>
                  {_'Slevový kupón'} na {$basket->coupon["couvalue"]} {$basket->coupon["couvalueunit"]}
                  {if $basket->coupon["coumanid"] > 0 && isset($enum_promanid[$basket->coupon["coumanid"]])} na značku {$enum_promanid[$basket->coupon["coumanid"]]} {/if}
                </h3>
                <p class="product__description">
                  Zadaný kód: {$basket->coupon["coucode"]}
                </p>
              </div>
              <p class="product__meta">
                <span class="product__price">
                  <strong>- {$basket->couDiscountVal|formatPrice:$curId}</strong>
                </span>
                {*
                <a href="#" class="product__remove">
                  <span class="icon icon--delete">
                    <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <use xlink:href="{$baseUri}/img/icons.svg#delete" x="0" y="0" width="100%" height="100%"></use>
                    </svg>
                  </span>
                </a>
                *}
              </p>

            </article>
            {/if}
          <div class="order-sum order-sum--pushed">

            <p>
              Cena celkem s DPH <strong> {MAX($basket->priceSumVat - $basket->discountVal - $basket->couDiscountVal, 0)|formatPrice:$curId}</strong>
            </p>
            {*
            <p class="order-sum__note">
              Cena bez DPH
              <strong>{$basket->priceSum|formatPrice:$curId}</strong>
            </p>
            *}
          </div>

          <div class="order-benefits">

            <div class="order-benefits__info">

              {include @benefitInfoDelFree.latte basket=>$basket, delFreeLimit=>$delFreeLimit}
              {include @benefitInfoDiscount.latte basket=>$basket}

            </div>

            {if $userRow->usrid == 0}
            <div class="hey hey--vertical">

              <p>
                <span class="icon icon--user">
                  <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="{$baseUri}/img/icons.svg#user" x="0" y="0" width="100%" height="100%"></use>
                  </svg>
                </span>
              </p>

              <p class="hey__info">Chcete slevu 3-10%? <a href="{plink User:add}">Zaregistrujte se</a></p>

            </div>
            {/if}

          </div>

          {if $basket->weightSum > $presenter->config["WEIGHT_LIMIT"]}
            <div class="hey"> <strong>Vaše objednávka přesahuje váhový limit {$presenter->config["WEIGHT_LIMIT"]}Kg</strong>, pro odeslání běžnou přepravní službou. Cena za dopravu bude stanovena po odeslání objednávky na základě platného ceníku pro dodávku zboží.</div>
          {/if}

        </div>

        <aside class="sidebar sidebar--order">

          <h3>Potřebujete poradit?</h3>

          <div class="contact-box">
            <p>
              <span class="avatar">
                <picture>
                  <source srcset="{$baseUri}/img/main-contact.webp" type="image/webp">
                  <img src="{$baseUri}/img/main-contact.jpg" alt="" width="300" height="300" loading="lazy">
                </picture>
              </span>
            </p>
            <p>
              <strong>Michal Dimelis</strong><br>
              <a href="tel:+420603478564">+420 603 478 564</a><br>
              <small class="phone" data-start="9" data-end="17">Po-Pá 9:00 - 17:00</small><br>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
          </div>

        </aside>

      </div>

      <div class="container">

        <div class="order-steps">

          <p><a href="{$baseUri}/akce-k118/" class="link link--back">Zpět</a></p>

          {if empty($basket->coupon) && isset($form['coupon'])}
          <p class="order-steps__discount">
            <label class="form-checkbox js-reveal" data-reveal="poukaz">
              <input type="checkbox" name="check2" value="value2">
              <span class="form-checkbox__label">Mám dárkový poukaz</span>
              <span class="form-checkbox__checker"></span>
            </label>
            <span class="reveal reveal--inline" id="poukaz">
              <label class="form-discount" id="poukaz">
                {input coupon placeholder=>"Zadejte kód"}
                {input recalc class=>button}
              </label>
            </span>
          </p>
         {/if}

          <p>
            <a href="{plink orderDelMode}" class="btn btn--big">
              Pokračovat na volbu dopravy
              <span class="icon icon--next">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUri}/img/icons.svg#next" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
            </a>
          </p>

        </div>

      </div>

    </section>
    {/form}
    {/snippet}
  {else}
  <section class="section section--short">

      <div class="container container--sidebar">

        <div class="section__content">
          <p class="hey">{_'Košík je prázdný'}...</p>
        </div>

      </div>
  </section>
  {/if}


    {ifset $basketPromoProducts}
    <section class="section section--short">

      <div class="container">

        <h2 class="section__title">
          Mohlo by se vám líbit
        </h2>

        <div class="splide splide--products">
          <div class="splide__track">
            <div class="splide__list">

              {foreach $basketPromoProducts as $row}
                <div class="splide__slide">{include product/@listItem.latte product=>$row}</div>
              {/foreach}

            </div>
          </div>
        </div>

      </div>

    </section>
    {/ifset}
{/block}
