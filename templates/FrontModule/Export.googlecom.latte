{contentType application/xml; charset=utf-8}
<?xml version="1.0"?>
<rss version="2.0" xmlns:g="http://base.google.com/ns/1.0">
<channel>
<title>{$presenter->config["SERVER_NAME"]}</title>
<link>{$baseUri}</link>
<description>{$presenter->config["HEADER_H2"]}</description>

{php
  $googlePath = [];
}

{foreach $rows as $row}

  {php
    if (!empty($row->proidmas)) {
      $fileName = ($row->propicnamemas != "" ? trim($row->propicnamemas).'.jpg' : $row->procodemas.'.jpg');
    } else {
      $fileName = ($row->propicname != "" ? trim($row->propicname).'.jpg' : $row->procode.'.jpg');
    }
    $fileName = rawurlencode($fileName);

    $imgVer = ($fileName|getPicTimeStamp);
  }

  {php
    $proname = ($row|getProNameAgregators);
  }

<item>
<title>{$proname}</title>
<link>{plink //:Front:Product:detail, $row->proid, ($row|getProKey)}</link>
<description><![CDATA[{strip_tags($row->prodesc)|truncate:5000,''}]]></description>
<g:brand>{$row->manname}</g:brand>
{if ($row->procode2|checkEan) != ''}<g:gtin>{$row->procode2}</g:gtin>{/if}
<g:image_link>{$baseUri}/pic/product/detail/{$fileName}?v={$imgVer}</g:image_link>
{if ($row->proprice1a < $row->proprice1com && $row->proprice1com > 0)}
<g:price>{$row->proprice1com} CZK</g:price>
<g:sale_price>{$row->proprice1a} CZK</g:sale_price>
{else}
<g:price>{$row->proprice1a} CZK</g:price>
{/if}
<g:condition>new</g:condition>
<g:id>{$row->proid}</g:id>
<g:availability>{if $row->proaccess == 0}in stock{else}out of stock{/if}</g:availability>
{foreach $delModes as $key=>$rowd}
  {if isset($rowd->paymodes["paybefore"])}
    {php
      if ($row->prodelfree == 1) {
        $payCost = 0;
        $delCost = 0;
      } else {  
        $payCost = ($rowd->paymodes["paybefore"]->delpricelimitfrom > 0 && $rowd->paymodes["paybefore"]->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->paymodes["paybefore"]->delprice);
        $delCost = ($rowd->delpricelimitfrom > 0 && $rowd->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->delprice);
      }  
    }
   <g:shipping>
   <g:country>CZ</g:country>
   <g:service>{$rowd->delname} {$rowd->paymodes["paybefore"]->delname}</g:service>
   <g:price>{$payCost+$delCost} CZK</g:price>
</g:shipping>
  {/if}
  {if isset($rowd->paymodes["dobirka"])}
    {php
      if ($row->prodelfree == 1) {
        $payCost = 0;
        $delCost = 0;
      } else {  
        $payCost = ($rowd->paymodes["dobirka"]->delpricelimitfrom > 0 && $rowd->paymodes["dobirka"]->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->paymodes["dobirka"]->delprice);
        $delCost = ($rowd->delpricelimitfrom > 0 && $rowd->delpricelimitfrom <= $row->proprice1a ? 0 : $rowd->delprice);
      }  
    }<g:shipping>
   <g:country>CZ</g:country>
   <g:service>{$rowd->delname} {$rowd->paymodes["dobirka"]->delname}</g:service>
   <g:price>{$payCost+$delCost} CZK</g:price>
</g:shipping>
  {/if}
{/foreach}
{php
$catPath = str_replace('|', ' &gt; ', $row->catpath);
}
<g:product_type>{$catPath}</g:product_type>
{php
  $googlePath = ($row|getCatPathAgregator:$googlePath:$googleCatalogs);
}
{if isset($googlePath[$row->catid])} 
<g:google_product_category>{$googlePath[$row->catid]}</g:google_product_category>
{/if}
</item>
{/foreach}
</channel>
</rss>