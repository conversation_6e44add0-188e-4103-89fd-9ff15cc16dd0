{if isset($modalVariants) &&  isset($modalProductMaster)}
<div class="modal modal--variants is-open" id="varianty">

  <div class="modal__body">

    <h2>
      Vyberte variantu produktu
      <br>
      <a href="{plink Product:detail, $modalProductMaster->proid, ($modalProductMaster|getProKey)}">{$modalProductMaster|getProNameCatalog} {$modalProductMaster->manname}</a>
    </h2>

    <div class="modal__close">
      <span class="icon icon--close">
        <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <use xlink:href="{$baseUri}/img/icons.svg#close" x="0" y="0" width="100%" height="100%"></use>
        </svg>
      </span>
    </div>

    {foreach $modalVariants as $product}
      <article class="product product--top">

      <div class="product__image">

        <p>
          <a href="{plink Product:detail, $product->proid, ($product|getProKey)}">

            {if $product->promasid > 0}
              {php
               $proPicVariant = ($product|getProductPicFileName:'list':TRUE);
               $proPicUsrSize = ($product|getProductPicFileName:'usrsize':TRUE);
              }

              <picture>
                <source srcset="{$baseUri}/{((empty($proPicVariant) ? $modalProductMaster : $product)|getProductPicNameWebp:'list')|noescape}" type="image/webp">
                <img src="{$baseUri}/{((empty($proPicVariant) ? $modalProductMaster : $product)|getProductPicName:'list')|noescape}" alt="{$modalProductMaster->proname}" width="48" height="48">
              </picture>


              {if !empty($proPicUsrSize) && $proPicUsrSize !== "no.jpg"}
                <picture class="product__flavour">
                  <img src="{$baseUri}/{($product|getProductPicName:'usrsize')|noescape}" alt="{$product->proname}">
                </picture>
              {/if}

            {else}

              <picture>
                <source srcset="{$baseUri}/{($product|getProductPicNameWebp:'list')|noescape}" type="image/webp">
                <img src="{$baseUri}/{($product|getProductPicName:'list')|noescape}" alt="{$product->proname}" width="48" height="48">
              </picture>

            {/if}

          </a>
        </p>

      </div>

      <div class="product__content">
        {php $names = ($product|getProNameParts)}
        <h3>
          {$names[0]}
        </h3>
        <p class="product__description">
          {$names[1]}
          <br>
          {include product/@stock.latte product=>$product, showQty=>TRUE}
        </p>
      </div>
      <p class="product__meta">

        {include product/@discount.latte product=>$product}

        <p class="product__price">
        {include product/@price.latte product=>$product}
        </p>

        {if ($product|canBuy)}
        <span class="product__count">
          <span class="icon icon--minus">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#minus" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
          <input type="text" id="variantQty_{$product->proid}" value="1">
          <span class="icon icon--plus">
            <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <use xlink:href="{$baseUri}/img/icons.svg#plus" x="0" y="0" width="100%" height="100%"></use>
            </svg>
          </span>
        </span>

        <a href="#" class="btn variantsBasketAdd" data-proid="{$product->proid}" data-naja-history="off">do košíku</a>
        {/if}

      </p>

    </article>
    {/foreach}

  </div>

</div>
{/if}