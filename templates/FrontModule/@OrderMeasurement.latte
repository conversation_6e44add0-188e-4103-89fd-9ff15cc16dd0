{* Google Analytics eComerce *}
{php
  $dphSum = $order->ordpriceinclvat - $order->ordpricenovat;
  $proIds = array();
  $productsSum = 0;
}
{* Heureka konverze PPC *}
{if !empty($heurekaConfig["KeyMereniKonverzi"])}
  <script type="text/javascript">
      var _hrq = _hrq || [];
      _hrq.push(['setKey', '{$heurekaConfig["KeyMereniKonverzi"]|noescape}']);
      _hrq.push(['setOrderId', '{$order->ordid|noescape}']);
      {foreach $order->items as $row}
      _hrq.push(['addProduct', '{$row->oriname|noescape}', '{$row->oriprice|noescape}', '{$row->oriqty|noescape}']);
      {/foreach}
      _hrq.push(['trackOrder']);

      (function() {
          var ho = document.createElement('script'); ho.type = 'text/javascript'; ho.async = true;
          ho.src = ('https:' === document.location.protocol ? 'https://ssl' : 'http://www') + '.heureka.cz/direct/js/cache/1-roi-async.js';
          var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ho, s);
      })();
  </script>
{/if}
<!-- Google Code for Objednavka Conversion Page -->
<script type="text/javascript">
    /* <![CDATA[ */
    var google_conversion_id = 1030312203;
    var google_conversion_language = "cs";
    var google_conversion_format = "1";
    var google_conversion_color = "ffffff";
    var google_conversion_label = "ivjtCM_6rQEQi6Kl6wM";
    var google_conversion_value = {$order->ordpricevat|noescape};
    var google_conversion_currency = "CZK";
    var google_remarketing_only = false;
    /* ]]> */
</script>
<script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
</script>
<noscript>
  <div style="display:inline;">
    <img height="1" width="1" style="border-style:none;" alt="" src="//www.googleadservices.com/pagead/conversion/1030312203/?value={$order->ordpricevat|noescape}&amp;currency_code=CZK&amp;label=ivjtCM_6rQEQi6Kl6wM&amp;guid=ON&amp;script=0"/>
  </div>
</noscript>

<!-- Měřicí kód Sklik.cz -->
<script type="text/javascript">
var seznam_cId = 100021210;
var seznam_value = {$order->ordpricevat|noescape};
</script>
<script type="text/javascript" src="https://www.seznam.cz/rs/static/rc.js" async></script>

{php
  $proIdsStr = '["'.implode('","', $proIds).'"]';
}
<script>
    fbq('track', 'Purchase', {
        content_ids: {$proIdsStr|noescape},
        content_type: 'product_group',
        value: {$productsSum|noescape},
        currency: '{$curKey|noescape}'
    });
</script>