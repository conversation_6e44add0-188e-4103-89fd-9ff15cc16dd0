{contentType: text/plain; charset=WINDOWS-1250}
{foreach $rows as $row}
{php
  //pokud je jina dodaci adresa
  if (!empty($row->ordstname)) {
    $row->ordiname = $row->ordstname;
    $row->ordilname = $row->ordstlname;
    $row->ordifirname = $row->ordstfirname;
    $row->ordistreet = $row->ordststreet;
    $row->ordistreetno = $row->ordststreetno;
    $row->ordicity = $row->ordstcity;
    $row->ordipostcode = $row->ordstpostcode;
    $row->ordicoiud = $row->ordstcouid;
  }
  $row->ordipostcode = str_replace(" ", "", trim($row->ordipostcode));
  $postName = "";
  $balName = "";
  $weight = $row->ordweight;

  if ($row->delcode=='CESKA_POSTA_NA_POSTU') {
    //$postName = $cpPostCodes[$row->ordpostcode_napostu];
  } else if ($row->delcode=='CESKA_POSTA_BALIKOVNA') {
    //$balName = $enum_balikovna[$row->orddelspecbalikovna];
  }

  if ($weight <= 0) {
    $weight = 2;
  }

  $codPrice = 0;
  if ($row->paycode == 'dobirka' || $row->paycode == 'cash') {
    $codPrice = round(!empty($row->ordpricecod) ? $row->ordpricecod : $row->ordpricevat);
  }
}
"{$row->ordifirname} {$row->ordilname}";"{$row->ordiname}";{if $row->delcode=='CESKA_POSTA_NA_POSTU'}"Na poštu";{$row->ordipostcode};"{$postName}";{elseif $row->delcode=='CESKA_POSTA_BALIKOVNA'}"Balíkovna";{$row->orddelspec};"{$postName}";{else}"{$row->ordistreet} {$row->ordistreetno}";{$row->ordipostcode};"{$row->ordicity}";{/if}{$row->ordtel};{$row->ordmail};{$weight};30000;{if $codPrice>0}{$codPrice}{else}{/if};{$row->ordcode};{if $row->delcode=='CESKA_POSTA_NA_POSTU'}NP{elseif $row->delcode=='CESKA_POSTA_BALIKOVNA'}NB{else}DR{/if};CZ;45{if $codPrice>0}+41{/if};{$row->ordic};{$row->orddic}{if !$iterator->isLast()}{/if}
{/foreach}