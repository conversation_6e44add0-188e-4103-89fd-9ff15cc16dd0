{default $isShort=FALSE}

<div class="question{if $isShort} question--short{/if}" id="g{$comment->cmtid}">

  <h2>{$comment->cmtsubj} {*Dotaz u kategorie <a href="#">Proteiny</a>*}</h2>
  <p class="question__meta">
    {$comment->cmtnick}, {$comment->cmtdatec|date:'d.m.Y'} {if $adminLogIn}, <a href="{plink :Admin:Comment:edit, $comment->cmtid}" target="admin" class="admin__edit">E</a>{/if}
  </p>
  <p>
    {$comment->cmttext|commentStripTags:($comment->cmtmail==$presenter->config["SERVER_MAIL"])|noescape}
  </p>

  {if $comment->res}
    {foreach $comment->res as $skey => $srow}
  <div class="question__answer">

    <h2>Odpověď </h2>
    <p class="question__meta">
      {if $srow->cmtadmid>0 && isset($authors[$srow->cmtadmid])}
        <span class="avatar avatar--small">
          <picture>
            <source srcset="{$baseUri}/{($authors[$srow->cmtadmid]|getAdminPicNameWebp:'35x35')|noescape}" type="image/webp">
            <img src="{$baseUri}/{($authors[$srow->cmtadmid]|getAdminPicName:'35x35')|noescape}" alt="{$authors[$srow->cmtadmid]->admname}" width="35" height="35" loading="lazy">
          </picture>
        </span>
        <span>
          {$authors[$srow->cmtadmid]->admname}, {$srow->cmtdatec|date:'d.m.Y'} {if $adminLogIn}<a href="{plink :Admin:Comment:edit, $srow->cmtid}" target="admin" class="admin__edit">E</a>{/if}<br>
          <em>{$authors[$srow->cmtadmid]->admfunction}</em>
        </span>
      {else}
        {$srow->cmtnick}, {$srow->cmtdatec|date:'d.m.Y'}
      {/if}
    </p>
    <p>
      {$srow->cmttext|commentStripTags:($srow->cmtmail==$presenter->config["SERVER_MAIL"])|noescape}
    </p>

    {if !empty($srow->proid)}
    <div class="question__products">
      <a href="{plink Product:detail, $srow->proid, ($srow|getProKey)}">
        <p>
          <picture>
            <source srcset="{$baseUri}/{($srow|getProductPicNameWebp:'70x70')|noescape}" type="image/webp">
            <img src="{$baseUri}/{($srow|getProductPicName:'70x70')|noescape}" alt="{$srow->proname}" width="70" height="70" loading="lazy">
          </picture>
        </p>
        <h3>
          {$srow->proname}
          <strong>
            {$srow->proprice|formatPrice:$curId}
            {if ($srow|isPromo)}<del>{$srow->propricecom|formatPrice:$curId}</del>{/if}
          </strong>
        </h3>
      </a>
    </div>
    {/if}

  </div>
    {/foreach}
  {/if}
</div>