{if $paginator->getPageCount() > 1}
<nav class="pagination">
	{if !$paginator->isFirst()}
	<a href="{link this, path=>$presenter->path, 'page' => $paginator->getPage() - 1}" class="pagination__prev">Předchozí</a>
	{/if}

	{for $step = 1; $step <= $paginator->getPageCount(); $step++}

		{if $step == $paginator->getPage()}
			<span class="paginator__current">{$step}</span>
		{else}
			<a href="{link this, path=>$path, 'page' => $step}">{$step}</a>
		{/if}
	{/for}

	{if !$paginator->isLast()}
	<a href="{link this, path=>$path, 'page' => $paginator->getPage() + 1}" class="pagination__next">Následující</a>
	{/if}
</nav>
{/if}