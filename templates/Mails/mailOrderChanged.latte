{if $orderRow->ordstatus == 3}
<p>
Pěkn<PERSON> den, <br>
  Vaši objednávku jsme zabalili a v tuto chvíli je již na cestě k Vám! V nejbližš<PERSON>ch hodinách budete kontaktováni ze strany přepravce o detailech doručení.<br>
  <br>
  {_'Zboží bylo odesláno přepravní službou'} <strong>{$delMode->delname}</strong>.<br />
  {if !empty($orderRow->ordparcode)} {_'Vaše číslo balíku je'}: {$orderRow->ordparcode}.<br />{/if}
  {if isset($parcelURL)}{_'Balík můžete sledovat'} <a href="{$parcelURL|noescape}">{_'na stránkách přepravce'}</a> .{/if}
</p>
<p>Děkujeme a mějte hezký den!</p>
{elseif $orderRow->ordstatus == 4}
  <p>Pěkn<PERSON> den,<br>
    u Vaší objednávky došlo ke změně stavu na <strong>DORUČENO</strong>. Věříme, že jste spokojeni a v případě jakýchkoliv nejasností nás prosím kontaktujte.</p>
{elseif $orderRow->ordstatus == 8}
  <p>Pěkný den,<br>
    Vaše objednávka je připravena k vyzvednutí na naší prodejně ({$delMode->delname}). Objednávku si můžete vyzvednout po dobu 7 dnů.
  </p>
  <p>Děkujeme a mějte hezký den!</p>
{elseif $orderRow->ordstatus == 10}
  <p>Pěkný den,<br>
    u Vaší objednávky došlo ke změně stavu na <strong>Vráceno přepravcem</strong>. Prosím kontaktujte nás.
  </p>
  <p>Děkujeme a mějte hezký den!</p>
{elseif $orderRow->ordstatus == 9}
  <p>Pěkný den,<br>
    Vaše objednávka byla právě stornována. V případě jakýchkoliv  dotazů nás neváhejte kdykoliv kontaktovat.
  </p>
  <p>Děkujeme za Vaši přízeň</p>
{else}
<p>{_'Stav vaší objednávky č.'} {$orderRow->ordcode} {_'na'} {$presenter->config["SERVER_NAME"]} {_'byl změněn na'} <strong>{$enum_ordStatus[$orderRow->ordstatus]}</strong>.</p>
<p>Děkujeme a mějte hezký den!</p>
{/if}

{include 'mailFooter.latte'}
