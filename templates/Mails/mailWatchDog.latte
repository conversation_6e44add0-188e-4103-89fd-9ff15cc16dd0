<p><strong>Na goldfitness.cz jsme zlevnili nebo naskladnili zboží, k<PERSON><PERSON>ají<PERSON></strong>.</p>

{ifset $data['price']}
  <p>Nastavil(a) jste si hlídání ceny na těchto <PERSON>, kter<PERSON> jsou nyní levnějš<PERSON>. Uvedené ceny uvidíte po přihlášení na svůj účet.</p>
  {foreach $data['price'] as $row}
    {if $iterator->isFirst()}
      <p>
    {/if}
    <a href="{plink //:Front:Product:detail, $row->proid, ($row|getProKey)}">{$row->proname}</a>, cena {$row["proprice1a"]|formatPrice:$curId}<br>
    {if $iterator->isLast()}
      </p>
    {/if}
  {/foreach}
{/ifset}

{ifset $data['onstock']}
  <p>Nastavil(a) jste si hlídán<PERSON> naskladnění na těchto polo<PERSON>, kter<PERSON> jsou nyní SKLADEM.</p>
  {foreach $data['onstock'] as $row}
    {if $iterator->isFirst()}
      <p>
    {/if}
    <a href="{plink //:Front:Product:detail, $row->proid, ($row|getProKey)}">{$row->proname}</a><br>
    {if $iterator->isLast()}
      </p>
    {/if}
  {/foreach}
{/ifset}

{include 'mailFooter.latte'}