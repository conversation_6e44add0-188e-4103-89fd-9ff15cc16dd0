<p><PERSON><PERSON><PERSON><PERSON> <PERSON>,
děkujeme Vám za Vaši objednávku na {$presenter->config["SERVER_NAMESHORT"]} s číslem {$orderRow->ordcode}. Objednávka byla právě přijata k vyřízení. O jejím průb<PERSON>hu a odeslání Vás budeme informovat.<br>
<br>
<PERSON><PERSON> objedn<PERSON>ky s DPH: {$orderRow->ordpricevat|formatPrice:$curId}<br>
<br>
Informace o platbě a stavu Vaší objednávky můžete sledovat na tomto <a href="{plink //:Front:Order:status, $orderRow->ordid.substr(md5($orderRow->ordid.$orderRow->orddatec->getTimestamp()), 0, 8)}" target="_blank">odkazu</a>.
</p>

<p>
<strong>{_'Zvolená doprava'}:</strong>
    {if $delMode->delid==18}<strong style="color: green;">{$delMode->delname}</strong>{else}{$delMode->delname}{/if}
    {if $delMode->delcode=='ULOZENKA'} Odběrné místo: {$orderRow->orddelspectext}{/if}
    {if $delMode->delcode=='ZASILKOVNA'} Odběrné místo: {$orderRow->orddelspectext}{/if}
    {if $delMode->delcode=='CESKA_POSTA_BALIKOVNA'} Odběrné místo: {$orderRow->orddelspectext}{/if}
    {if $delMode->delcode=='DPD_PICKUP'} Odběrné místo: {$orderRow->orddelspectext}{/if}
    <br />
<strong>{_'Platba'}:</strong> {$payMode->delname}<br />
<strong>{_'Poznámka'}:</strong><br />
<strong style="color: red;">{$orderRow->ordnote|nl2br|noescape}</strong><br />
<br />
<strong>{$enum_usrprccat[$user->usrprccat]}</strong><br />
<br />
<strong>{_'Fakturační a současně doručovací adresa'}:</strong><br />
{_'Jméno'}: {$orderRow->ordiname}<br />
{_'Příjmení'}: {$orderRow->ordilname}<br />
{if $lang=='cs'}{_'Firma'}: {$orderRow->ordifirname}<br />{/if}
{_'Ulice'}: {$orderRow->ordistreet}<br />
{_'Číslo popisné'}: {$orderRow->ordistreetno}<br />
{_'Město, obec'}: {$orderRow->ordicity}<br />
{_'PSČ'}: {$orderRow->ordipostcode}<br />
{_'Telefon'}: {$orderRow->ordtel}<br />
{_'Email'}: <a href="mailto:{$orderRow->ordmail}">{$orderRow->ordmail}</a><br />
IČ: {$orderRow->ordic}, DIČ: {$orderRow->orddic}<br />
{if !empty($orderRow->ordstname)}
<br />
<strong>Dodací adresa:</strong><br />
Jméno, přijmení: {$orderRow->ordstname} {$orderRow->ordstlname}<br />
Firma: {$orderRow->ordstfirname}<br />
Ulice: {$orderRow->ordststreet} {$orderRow->ordststreetno}<br />
Město, obec: {$orderRow->ordstcity}<br />
PSČ: {$orderRow->ordstpostcode}<br />
{/if}
{if $payMode->delcode == 'paybefore'}
{* udaje pro platbu predem *}
<br />
<strong>Údaje pro platbu předem:</strong><br />
Číslo účtu: {if $orderRow->ordcurid==1}{$presenter->config["SERVER_ACCNO"]}{/if}<br />
{if $orderRow->ordcurid==2}IBAN: CZ70 5500 0000 0021 0600 5044<br />{/if}
Variabilní symbol: {$orderRow->ordcode}<br />
Částka: {$orderRow->ordpricevat|formatPrice:$curId}
{/if}
</p>
<strong>{_'Položky objednávky'}:</strong><br />
<table>
<tr>
  <td><strong>Katalogové číslo</strong></td>
  <td><strong>Název</strong></td>
  <td><strong>Kusy</strong></td>
  <td><strong>Sleva</strong></td>
  <td><strong>Cena s DPH</strong></td>
</tr>
{php
$sum = 0;
}
{foreach $ordItemRows as $row}
{php
  $sum += ($row->oriqty*$row->oriprice);
}

<tr>
  <td>{$row->oriprocode}<br>{$row->procodep}</td>
  <td>{$row->manname} {$row->oriname}</td>
  <td>{$row->oriqty} {_'ks'}</td>
  <td></td>
  <td>{if $row->oriprice != 0}{$row->oriprice|formatPrice:$curId}{else}ZDARMA{/if}</td>
</tr>

{/foreach}
</table>
<br />
<table>
<tr>
  <td><strong>{_'Celková cena s DPH'}: </strong></td>
  <td>{$orderRow->ordpricevat|formatPrice:$curId}</td>
</tr>
</table>
{include 'mailFooter.latte'}
