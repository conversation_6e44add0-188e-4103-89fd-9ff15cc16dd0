{layout NULL}
{php
  $color = '#c0392b';
}
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>{$mailing->mamsubject} - {$config["SERVER_NAMESHORT"]}</title>
</head>
<body style="line-height:1.3;background:white;font-family: Arial, Helvetica, sans-serif;font-size: 14px;color: #000;" link="#000000" vlink="#000000" alink="#000000">
<div style="width:630px;margin:0 auto">
  <!-- tabulka - logo -->
  <table width="630" border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td height="137" align="center" valign="top"><a href="{$baseUri}/c/0/{$mamid}/{$usrid}"><img src="{$baseUri}/img/newsletter-top.png" height="137" alt="{$config["SERVER_NAMESHORT"]}" style="border:none" ></a></td>
    </tr>
  </table>
  <!-- tabulka - logo  -->
  {if $isMail}<p align="center">Nezobrazuje-li se vám e-mail správně, klikněte prosíme <a style="text-decoration:underline" href="{$baseUri}/c/3/{$mailing->mamid}/{$usrid}/{$loKey}">na tento odkaz</a>.</p>{/if}

  {if !empty($mailing->mambody)}{$mailing->mambody|noescape}{/if}

  <!-- tabulka obalovaci zacatek -->
  <table width="630" border="0" cellspacing="0" cellpadding="0">
    {foreach $products as $key => $product}
    <tr>
      <td width="140px" align="left" valign="top">
        {php
        $picName = ($product->propicname != "" ? trim($product->propicname).'.jpg' : $product->procode.'.jpg');
        }
      <a style="display:block;width:100%;height:100%;color:black;text-decoration:none " href="{$baseUri}/c/1/{$mailing->mamid}/{$usrid}/{$product->proid}">
        <img style="border:none;padding-right:20px;" src="{$baseUri}/pic/product/list/{$picName}">
      </a>
      </td>
      <td align="left" valign="top">
      <a style="display:block;width:100%;height:100%;color:black;text-decoration:none" href="{$baseUri}/c/1/{$mailing->mamid}/{$usrid}/{$product->proid}">
        <strong><font size="3">{$product->proname1}</font></strong>
        <br><small style="padding-bottom:10px;display: inline-block;">{$product->promamdesc1}</small><br>
        {$product->promamdesc2|striptags|truncate:200}<br>
        <br>
        <strong><font color="#84000" size="4">{$product->proprice1b|formatPrice:$curId}</font> {if $product->prodelfree > 0} + DOPRAVA ZDARMA{/if}</strong>
        <br>
        <strong style="padding-bottom:10px;display: inline-block;">Běžná e-shopová cena je {$product->proprice1com|formatPrice:$curId} <font color="#84000">{if ($product->proprice1com-$product->proprice1b) > 0} - ušetříte {($product->proprice1com-$product->proprice1b)|formatPrice:$curId}{/if}</font></strong>
        <br>
        <a style="display:block;width:120px;height:25px;padding-top:8px;color:black;text-decoration:none;border-radius:3px;background-color:#ffca00;background:#ffca00;text-align:center;" href="{$baseUri}/c/1/{$mailing->mamid}/{$usrid}/{$product->proid}"><strong style="text-decoration:none;">Koupit</strong></a>
        <br>
        <br>
      </a>
      </td>
    </tr>
    {/foreach}
  </table>

  {if !empty($mailing->mamfooter)}{$mailing->mamfooter|noescape}{/if}

  <table width="100%" border="0"  cellspacing="0" cellpadding="0">
    <tr>
      <td  align="center" valign="middle">
        <a href="{$baseUri}/c/0/{$mailing->mamid}/{$usrid}}"><img src="{$baseUri}/img/newsletter-top.png" height="137" alt="{$config["SERVER_NAMESHORT"]}" style="border:none"></a>
        <br>
        <a style="text-decoration:underline" href="{$baseUri}/c/2/{$mailing->mamid}/{$usrid}/{$loKey}">Odhlásit z odběru</a></td>
    </tr>
  </table>
  <!-- paticka -->
</div>
{* zaloguju zda precetl email *}
{if $isMail}<img src="{$baseUri}/c/4/{$mamid}/{$usrid}" width="1px" height="1px" style="border:none"/>{/if}
</body>
</html>
