{* DeliveryMode.edit.latte *}



{block #content}
  {if $id > 0}
    {if $dataRow->delmasid == 0}
      <h3>Editace způsobu dopravy</h3>
      <p>[ <a href="{plink DeliveryMode:edit, 0, $dataRow->delid}">přidat nový způsob platby</a> ]</p>
    {else}
      <h3>Editace způsobu platby</h3>
    {/if}
  {else}
    <h3>{ifset $delMas}Nový způsob platby pro {$delMas->delname}{else}Nová doprava{/ifset}</h3>
  {/if}
  {control editForm}

  <h3 id="freedelivery">Doprava zdarma</h3>
  {form editDelFreeForm}
    <table class="grid">
      <tr>
        <th>Měna</th>
        <th>Cenová kategorie</th>
        <th>Cena objednávky od</th>
        <th>Stav</th>
        <th colspan="2"></th>
      </tr>
      {foreach  $form["items"]->getComponents() as $cont}
      {var $disid=$cont->name}
      {if $disid == 0}
      <tr>
        <th colspan="6">Nová položka</th>
      </tr>
      {/if}
      <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
        <td>{php echo $form["items"][$cont->name]['discurid']->control}</td>
        <td>{php echo $form["items"][$cont->name]['disprccat']->control}</td>
        <td>{php echo $form["items"][$cont->name]['disfrom']->control}</td>
        <td>{php echo $form["items"][$cont->name]['disstatus']->control}</td>
        <td>{if $disid > 0}<a href="{plink deleteDelFree, $disid, (int)$presenter->getParam('id')}" onclick="return DeleteConfirm('dopravu zdarma ID: {$disid}');"> <img src="{$baseUri}/admin/ico/delete.png" width="16" height="16" /></a>{/if}</td>
      </tr>
      {/foreach}
      <tr>
        <td colspan=6>{input save}</td>
      </tr>
    </table>  
  {/form}
{/block}
