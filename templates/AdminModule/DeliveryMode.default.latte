{*
  DeliveryMode.default.latte

  array $dataRows       - seznam položek
  array $enum_delstatus - ciselnik delstatus
*}

{block #content}
 <h3>{block #title}Způsoby dodání{/block}</h3>
 <p>[ <a href="{plink DeliveryMode:edit, 0, 0}">přidat nový způsob dodání</a> ] [ <a href="{plink DeliveryMode:default, 1}">vypsat i blokované polo<PERSON></a> ]</p>
 <table class="grid delivery">
  <tr>
    <th>Název</th>
    <th>Typ</th>
    <th>Cena v {$presenter->curCodes[1]}</th>
    {if $presenter->secondCurrency}
    <th>Cena v {$presenter->curCodes[2]}</th>
    {/if}
    <th>Pořadí</th>
    <th>Status</th>
    <th></th>

  </tr>
  {foreach $dataRows as $row}
    <tr><td colspan="8"></td></tr>
    <tr>
      <th>{$row->delname} <a href="{plink DeliveryMode:edit, 0, $row->delid}"><img src="{$baseUri}/admin/ico/add.png" width="16" height="16" alt="přidat další variantu"  title="přidat další variantu" /></a></th>
      <th>{$row->delcode}</th>
      <th>{$row->delprice1a|formatPriceByCurId:1}<br></th>
      {if $presenter->secondCurrency}
      <th>{$row->delprice2a|formatPriceByCurId:2}</th>
      {/if}
      <th>{$row->delorder}</th>
      <th>{$enum_delstatus[$row->delstatus]}</th>
      <th><a href="{plink DeliveryMode:edit, $row->delid}"><img src="{$baseUri}/admin/ico/edit.png" width="16" height="16" alt="editovat"/></a></th>
    </tr>
    {foreach $row["subItems"] as $subRow}
    <tr>
      <td>{$subRow->delname}</td>
      <td>{$subRow->delcode}</td>
      <td>{$subRow->delprice1a|formatPriceByCurId:1}</td>
      {if $presenter->secondCurrency}
      <td>{$subRow->delprice2a|formatPriceByCurId:2}</td>
      {/if}
      <td>{$subRow->delorder}</td>
      <td>{$enum_delstatus[$subRow->delstatus]}</td>
      <td><a href="{plink DeliveryMode:edit, $subRow->delid}"><img src="{$baseUri}/admin/ico/edit.png" width="16" height="16" alt="editovat"/></a></td>
    </tr>
    {/foreach}
  {/foreach}
  </table>


  <h4>Uloženka</h4>
  <p>
    Aktualizace odběrných míst: <a href="{plink //:Admin:Import:updateUlozenkaBranches}">{plink //:Admin:Import:updateUlozenkaBranches}</a><br />
    Nastavení stavu doručeno u odeslaných objednávek: <a href="{plink //:Front:Batch:CheckParcelStatus}">{plink //:Front:Batch:CheckParcelStatus}</a><br />
  </p>
{/block}
