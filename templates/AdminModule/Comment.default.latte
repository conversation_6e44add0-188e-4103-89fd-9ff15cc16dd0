{*
  Product.default.latte
  DibiRow $productRows    - seznam zbozi
*}

{block #content}
  <h3>{block #title}Komentáře{/block}</h3>
  {$control['searchForm']->render('begin')|noescape}
  <fieldset>
    <legend>Vyhledávání</legend>
    {$control['searchForm']['text']->label} {$control['searchForm']['text']->control}
    {$control['searchForm']['usrmail']->label} {$control['searchForm']['usrmail']->control}
    {$control['searchForm']['procode']->label} {$control['searchForm']['procode']->control}
    {$control['searchForm']['search']->control}
    {$control['searchForm']['clear']->control}
  </fieldset>
  {$control['searchForm']->render('end')}

  <table class="grid" width="1200">
  <tr>
    <th>Id</th>
    <th>Kód produktu</th>
    <th>Datum/čas</th>
    <th>Nick</th>
    <th>Titulek</th>
    <th>Text</th>
    <th colspan="3"></th>
  </tr>
  {foreach $comments as $row}
    <tr {if $row->cmtreid > 0}{else}style="background: #F7F0CB"{/if}>
      <td>{$row->cmtid}</td>
      <td>{$row->procode}</td>
      <td>{$row->cmtdatec|date:'d.m.Y H:i:s'}</td>
      <td>{$row->cmtnick}{if !empty($row->cmtmail)}<br>{$row->cmtmail}{/if}</td>
      <td>{$row->cmtsubj}</td>
      <td>
      <strong>{if $row->cmtcatid > 0}{$enum_cmtcatid[$row->cmtcatid]} |{/if} {if $row->cmtcatid2 > 0}{$enum_cmtcatid[$row->cmtcatid2]}{/if}</strong><br>
      {$row->cmttext|nl2br|noescape}</td>
      <td><a href="{plink Comment:edit, $row->cmtid}" title="editovat komentář"><img src="{$baseUri}/admin/ico/edit.png" width="16" height="16" /></a></td>
      <td><a href="{plink Comment:delete, $row->cmtid}" onclick="return DeleteConfirm('komentář {$row->cmtsubj|noescape}');"><img src="{$baseUri}/admin/ico/delete.png" width="16" height="16" /></a></td>
    </tr>
  {/foreach}
  </table>
  {control paginator}
{/block}
