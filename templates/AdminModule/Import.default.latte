
{block #content}
  
  <h3>{block #title}Import{/block}</h3>
  {ifset $log}
  {foreach $log as $row}
    {$row}<br>
  {/foreach}
  {/ifset}
  <p>
  <a href="{plink //:Front:Import:pohodaZasobyFull}">Aktualizace EANů a sestav.</a> |
  <a href="{plink //:Front:Import:pohodaZasoby}">Aktualizace stavů skladů a cen z Pohody.</a>
    </p>
  {*control importPohodaForm*}

  <h3>Export</h3>
  {control exportForm}

<h3>Geolokace</h3>
<p>
  <a href="{plink //:Front:Import:updateGeolocationDatabase}">Geolokace</a>

  <h3>Dodavatelé příprava XLS</h3>
  {control exportVendorForm}
  {*
  <a href="{plink Import:importProductsXml}">Produkty z XML</a><br>
  <a href="{plink Import:importProductImagesXml}">Obrázky produktů z XML</a><br>
  *}
  <a href="{plink Import:recalcProIsMaster}">Přepočítat isMaster příznak</a><br>


  <h3>Dodavatelé Import z XML feedu</h3>
  <a href="{plink :Front:Import:fitnestoreImport}">fitnesstore.cz - spustíte kliknutím zde</a>
  <p>
  Čerpá data z tohoto feedu https://www.fitnestore.cz/feed/13-52e9ec63e03150b11929c4fd3ce4f6b6.xml<br>
  Importuje jen položky co mají v názvu slovo TUNTURI a jen ty položky co ještě nejsou v eshopu<br>
  Položku se identifikují podle toho, že kód produktu má prefix "FS_" + PRODUCTNO dodavatele - pro opakovaný import je důležite kód produktu neměnit<br>
  Nová položka se importuje jako blokovaná<br>
  </p>
{/block}
