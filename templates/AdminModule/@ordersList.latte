<form method="get" id="batchform" action="{plink Order:batchAction}">
 <input type="checkbox" id="checkAll"> Zaškrtnout/odškrtnou vše
<table class="grid">
  <tr>
    <th> </th>
    <th> </th>
    <th> </th>
    <th>Č. obj.</th>
    <th> MALL </th>
    <th>Zákazník</th>
    <th>Firma</th>
    <th>Datum</th>
    <th>Cena s DPH</th>
    <th>Doprava, platba</th>
    <th>Pozn.</th>
    <th>Status</th>
    <th colspan="5"></th>
  </tr>
  {php
    $colors[0] = "#FFFFFF"; //ceka na zpracovani
    $colors[1] = "#FFFFC0"; //Vyřizuje se
    $colors[2] = "#FFBE7D"; //Čeká na platbu
    $colors[3] = "#C0C0FF"; //Odeslána
    $colors[4] = "#C0FFC0"; //Uzavřená
    $colors[5] = "#FFC0C0"; //Stornovaná
    $colors[6] = "#800080"; //Zaplaceno
    $colors[7] = "#C0C0C0"; //cerna listina
    $colors[8] = "#BCDCB8"; //připraveno k odběru
    $colors[9] = "#FFC0C0"; //storno
    $colors[10] = "#C0C0C0"; //vráceno přepravcem


    $typeColors[0] = "#FFFFFF"; //eshop
    $typeColors[1] = "#FFFFC0"; //admin
    $typeColors[2] = "#FFBE7D"; //mall
    $typeColors[3] = "#C0C0FF"; //štítek

    $sum = 0;
    $qty = 0;
  }

  {foreach $dataRows as $row}

  {php
    $sum += $row->ordpricevat;
    $qty ++;
  }
  {var $style= (!$iterator->isOdd() ? 'bgcolor="#D0D0D0"' : '')}

  <tr {$style}>
    <td {ifset $typeColors[$row->ordtype]}style="background-color: {$typeColors[$row->ordtype]|noescape};" title="{$enum_ordtype[$row->ordtype]}"{/ifset}>
      <input class="ordid_chk" type="checkbox" name="ordid[{$row->ordid}]" value="{$row->ordid}" >
    </td>
    <td>
      {if $row->delcodemas == 'ULOZENKA' && !empty($row->ordparcode)}
      <a href="https://trace.wedo.cz/?orderNumber={$row->ordparcode}" target="ulozenka">
        <img src="{$baseUri}/admin/ico/car.png" width="16" height="16" title="Už existuje balík - sledovat balík" />
      </a><br>
      {*<a href="{plink getWedoLabels $row->ordparcode}">
        <img src="{$baseUri}/admin/ico/pdf.png" width="16" height="16" title="Už existuje balík - sledovat balík" />
      </a>*}
      {elseif ($row->delcodemas == 'CESKA_POSTA' || $row->delcodemas == 'CESKA_POSTA_BALIKOVNA') && !empty($row->ordparcode)}
        {if !empty($row->ordparcode)}
        <a href="https://www.postaonline.cz/trackandtrace/-/zasilka/cislo?parcelNumbers={$row->ordparcode}" target="cp">
          <img src="{$baseUri}/admin/ico/car.png" width="16" height="16" title="Už existuje balík - sledovat balík" />
        </a>
        {/if}
      {elseif ($row->delcodemas == 'ZASILKOVNA' || $row->delcodemas == 'ZASILKOVNA_NA_ADRESU') && !empty($row->ordparcode)}
        <a href="https://tracking.packeta.com/cs_CZ/?id={$row->ordparcode}" target="zasilkovna">
          <img src="{$baseUri}/admin/ico/car.png" width="16" height="16" title="Už existuje balík - sledovat balík" />
        </a>
        {if $row->delcodemas == 'ZASILKOVNA' && isset($enum_zasilkovna) && !isset($enum_zasilkovna[$row->orddelspec])}<br><span style="color: red; font-size: large" title="Problém s odběrným místem!">&#9888;</span>{/if}
      {elseif ($row->delcodemas == 'DPD' || $row->delcodemas == 'DPD_PICKUP') && !empty($row->ordparcode)}
        {if !empty($row->ordparcode2)}{$row->ordparcode2} | {/if}
        {if !empty($row->ordparcode)}
        <a href="https://www.dpdgroup.com/cz/mydpd/my-parcels/track?parcelNumber={$row->ordparcode}" target="dpd">
          <img src="{$baseUri}/admin/ico/car.png" width="16" height="16" title="Už existuje balík - sledovat balík" />
            </a>
        {/if}
      {/if}
    </td>
    <td>
      {if !empty($row->ordpricecod) && $row->ordpricecod != $row->ordpricevat}Dobírková cena:{$row->ordpricecod|formatPriceByCurId:$row->ordcurid}{/if}
      {if !empty($row->ordparcelscount) && $row->ordparcelscount > 1}Balíků:{$row->ordparcelscount}{/if}
    </td>
    {*
    <td width="140px">
      {if $row->paycode == 'dobirka'}Dobírka:&nbsp;<input type="text" style="text-align: right" name="pricecod[{$row->ordid}]" value="{if !empty($row->ordpricecod)}{$row->ordpricecod}{else}{$row->ordpricevat}{/if}" size="5"><br>{/if}
      Balíků:&nbsp;<input type="text" style="text-align: right" name="parcelscount[{$row->ordid}]" value="{if empty($row->ordparcelscount)}1{else}{$row->ordparcelscount}{/if}" size="1">
    </td>
    *}
    <td>{$row->ordcode}</td>
    <td>
      {if !empty($row->ordmalid)}
      {$row->ordmalid}
      {/if}
      {if !empty($row->ordmalllog)}
        <br><img src="{$baseUri}/admin/ico/block.png" width="16" height="16" title="{$row->ordmalllog}" />
      {/if}
    </td>
    <td>{$row->ordiname} {$row->ordilname}|{$row->ordstname} {$row->ordstlname}</td>
    <td>{$row->ordifirname}|{$row->ordstfirname}</td>
    <td>{$row->orddatec|date:'d.m.Y H:i'}</td>
    <td style="text-align: right;white-space:nowrap">{$row->ordpricevat|formatPriceByCurId:$row->ordcurid}</td>
    <td>{$row->delnamemas}, {$row->delname}</td>
    <td>{if !empty($row->ordnote)}<a href="#" onclick="return noteSH('note_{$row->ordid}');"><img src="{$baseUri}/admin/ico/magnifier.png" width="16" height="16" /></a><div style="display:none" id="note_{$row->ordid}">{$row->ordnote|nl2br|noescape}</div>{/if}</td>
    <td style="color: black; background-color: {$colors[$row->ordstatus]|noescape};">
    {foreach $enum_ordstatus as $key => $text}
      {if $iterator->isFirst()}
      <select name="ordstatus[{$row->ordid}]">
      {/if}
      <option value="{$key}" {if $key==$row->ordstatus} selected="selected"{/if}>{$text}</option>
      {if $iterator->isLast()}
      </select>
      {/if}
    {/foreach}
    </td>
    <td><a href="{plink Order:edit, $row->ordid}"><img src="{$baseUri}/admin/ico/edit.png" width="16" height="16" /></a></td>
    <td style="text-align: center">{if $row->ordusrid > 0}<a href="{plink User:edit, $row->ordusrid}"><img src="{$baseUri}/admin/ico/user.png" width="16" height="16" /></a>{/if} {if !empty($enum_prccat[$row->ordprccat])}<br>{$enum_prccat[$row->ordprccat]}{/if}</td>
    <td>{if $row->ordexported == 0}<a href="{plink :Front:Export:pohodaOrders, $row->ordid}"><img src="{$baseUri}/admin/ico/xml.png" width="16" height="16" /></a>{/if}</td>
    <td><a href="{plink :Front:Order:status, $row->ordid.substr(md5($row->ordid.$row->orddatec->getTimestamp()), 0, 8)}" target="_blank"><img src="{$baseUri}/admin/ico/magnifier.png" width="16" height="16" /></a></td>
    <td {if $row->ordprinted === 1}style="background-color: yellowgreen"{/if}><a href="{plink Order:printOrder, $row->ordid, 'D'}" target="_blank"><img src="{$baseUri}/admin/ico/pdf.png" width="16" height="16" /></a> </td>
  </tr>
  {/foreach}


  <tr>
    <td colspan="7" ><strong>Celkem {$qty} objednávek:</strong>    </td>
    <td style="text-align: right;"><strong>{$sum|formatPrice:$curId}</strong>    </td>
    <td colspan="8" ></td>
  </tr>
  </table>
  <input type="checkbox" value="1" name="mail_send"> Neposílat info mail <input type="submit" name="change_status" value="Aktualizovat status"><br>

  <input type="submit" name="export_ulozenka" value="Export uloženka">

  {*<input type="submit" name="export_wedo" value="Export WE|DO">*}

  <input type="submit" name="export_orders" value="Objednávky do PDF">

  <input type="submit" name="export_post" value="Export Česká pošta">

  <fieldset>
  <legend>DPD</legend>
  <input type="submit" name="dpd_export" value="Odeslat balíky">&nbsp;&nbsp;&nbsp;
    <input type="submit" name="dpd_delete" value="Vymazat balíky">&nbsp;&nbsp;&nbsp;
    <input type="submit" name="dpd_print" value="Tisk soupisky">
  </fieldset>

  <fieldset>
    <legend>Zásilkovna</legend>
    <select name="formatZasilkovna">
      <option value="A7 on A4">štítek o velikosti 105x74 mm (A7) na stránce velikosti 210x297 mm (A4)</option>
      <option value="A6 on A4">štítek o velikosti 105x148 mm (A6) na stránce velikosti 210x297 mm (A4)</option>
      <option value="A7 on A7">štítek o velikosti 105x74 mm (A7) na stránce o totožné velikosti</option>
      <option value="A9 on A4">štítek o velikosti 52x37 mm (A9) na stránce velikosti 210x297 mm (A4)</option>
    </select>
    Vynechat: <select name="fpZasilkovna">
      <option value="0">0</option>
      <option value="1">1</option>
      <option value="2">2</option>
      <option value="3">3</option>
      <option value="4">4</option>
      <option value="5">6</option>
    </select>
    <input type="submit" name="export_zasilkovna" value="Export zásilkovna"> |
    <input type="submit" name="print_zasilkovna" value="Tisk štítků"><br>
  </fieldset>

  {*
  <fieldset>
  <legend>WE|DO</legend>
    <input type="submit" name="wedo_export" value="Export WE|DO">
    <input type="submit" name="wedo_print" value="Tisk štítku (jen pro jednu objednávku)">&nbsp;&nbsp;&nbsp;
    <input type="submit" name="wedo_batch" value="Generuj soupisku">&nbsp;&nbsp;&nbsp;
    <input type="submit" name="wedo_batch_print" value="Tisk soupisky">&nbsp;&nbsp;&nbsp;
    <input type="submit" name="wedo_batch_printlabels" value="Tisk štítků soupisky">
  </fieldset>
    *}
  </form>

  <script type="javascript">

  $("#checkAll").click(function(){
    $('.ordid_chk').not(this).prop('checked', this.checked);
  });

  $(document).on("keydown", "#batchform", function(event) {
    return event.key != "Enter";
  });

  $("#batchform").submit(function (e) {
    if(!$('.ordid_chk').is(":checked")) {
        window.alert("Vyberte alespoň jednu objednávku.");
        e.preventDefault();
    }
  });

  </script>