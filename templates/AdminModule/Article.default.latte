{block #content}
 <h3>{block #title}<PERSON><PERSON><PERSON><PERSON>{/block}</h3>
 <p><a href="{plink edit, 0}"><PERSON><PERSON></a></p>
 <table class="grid">
  <tr>
    <th>Název</th>
    <th><PERSON><PERSON> kl<PERSON>č</th>
    <th>Datum</th>
    <th>Zobrazení</th>
    <th>Typ</th>
    <th>Umístění</th>
    <th>Status</th>
    <th></th>
    <th></th>
    <th></th>
  </tr>
  {foreach $dataRows as $row}
    {php
        if (empty($row->arturlkey)) {
          $row->arturlkey = Nette\Utils\Strings::webalize($row->artname);
        }
    }
    <tr>
      <td>{$row->artname}</td>
      <td>{$row->arturlkey}</td>
      <td>{$row->artdate|date:'d.m.Y'}</td>
      <td>{$row->artcnt}</td>
      <td>{$enum_arttypid[$row->arttypid]}</td>
      <td>{$enum_arttop[$row->arttop]}</td>
      <td>{$enum_artstatus[$row->artstatus]}</td>
      <td><a href="{plink Article:edit, $row->artid}"><img src="{$baseUri}/admin/ico/edit.png" width="16" height="16" alt="editovat"/></a></td>
      <td><a href="{plink Article:delete, $row->artid}"><img src="{$baseUri}/admin/ico/delete.png" width="16" height="16" alt="editovat"/></a></td>
      <td><a href="{plink :Front:Article:detail, $row->artid, $row->arturlkey}"><img src="{$baseUri}/admin/ico/front.png" width="16" height="16" alt="Zobrazit"/></a></td>
    </tr>
  {/foreach}
  </table>
{/block}
