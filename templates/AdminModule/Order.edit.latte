{block #content}
  <script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts' $dataRow->ordid},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('oriname', 'oriproid');
          $("#"+newId).val(ui.item.id);
          newId = id.replace('oriname', 'oriprice');
          $("#"+newId).val(ui.item.price);
        }
        return false;
      }
    });
  });
  </script>


  <h3>{block #title}Objednávka č. {$dataRow->ordcode} ({$dataRow->ordiname} {$dataRow->ordilname} | {$dataRow->ordstname} {$dataRow->ordstlname}) {/block} {if !empty($dataRow->ordmalid)} Mall: {$dataRow->ordmalid}{/if}</h3>

  {if !empty($message)}<strong style="color:red">{$message}</strong>{/if}

  {if !empty($dataRow->ordinvcode)}
  <p><b>Faktura č.: </b>{$dataRow->ordinvcode} <a target="invoicePrint" href="{plink Order:printInvoice, $dataRow->ordid}"><img src="{$baseUri}/admin/ico/pdf.png" width="16" height="16" /></a>  | <a href="{plink Order:printInvoice, $dataRow->ordid, 'D'}"><img src="{$baseUri}/admin/ico/export.png" width="16" height="16" /></a></p>
  {else}
  <p>Není přiřazen kód faktury. <a href="{plink makeInvoice, $dataRow->ordid}">Vystavit fakturu</a></p>
  {/if}
  <p>
    Export Pohoda: {$enum_ordexported[$dataRow->ordexported]} |
    <a href="{plink :Front:Export:pohodaOrders, $dataRow->ordid, 0, 0}">{if $dataRow->ordexported == 1}exportovat znovu{else}exportovat{/if}</a>
    {if $dataRow->ordexported == 1} | <a href="{plink :Front:Export:pohodaOrders, $dataRow->ordid, 0, 1}"> exportovat znovu a vložit nový kontakt </a>{/if}<br>
    {php
      $fieldPrefix = !empty($row->ordstname) ? "ordst" : "ordi";
    }
    <a href="https://vdp.cuzk.cz/vdp/ruian/overeniadresy/vyhledej?as.nazevUl={$dataRow[$fieldPrefix . 'street']}&as.cisDom={$dataRow[$fieldPrefix . 'streetno']}&as.cisOr.cisloOrientacniText=&as.nazevCo=&as.nazevOb={$dataRow[$fieldPrefix . 'city']}&as.psc={$dataRow[$fieldPrefix . 'postcode']}&asg.sort=UZEMI&search=Vyhledat" target="_validate"> Validace adresy </a>
    {if $postCodeNotFound} | <strong style="color: red">PSČ nebylo nalezeno v seznamu PSČ</strong>{/if}
  </p>

  {if $payment->delcode == 'payonline'}
      Platby kartou:
    <a href="{plink :Front:PayU:create, $dataRow->ordid, md5($dataRow->ordid . $dataRow->orddatec)}">zaplatit</a>

    {if !empty($dataRow->ordpauid)}
     {*|
    <a href="{plink :Front:PayU:retrieve, $dataRow->ordpauid}">Stav platby</a>*}
    {/if}
  {/if}


    {control orderChangeStateForm}

  <h4 id="edititems">Položky objednávky</h4>
  {form ordItemsEditForm}
    <table class="grid">
      <tr>
        <th>ID zboží</th>
        <th>Katalogové č.</th>
        <th>název</th>
        <th>cena/sleva</th>
        <th>počet</th>
        <th colspan="2"></th>
      </tr>
      {foreach  $form['items']->getComponents() as $cont}
      {var $oriid=$form['items'][$cont->name]['oriid']->value}
      {if $cont->name != 'delivery'}
      <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
        <td>{php echo $form['items'][$cont->name]['oriproid']->control}</td>
        <td>{$ordItems[$oriid]->oriprocode}</td>
        <td>
        {php echo $form['items'][$cont->name]['oriname']->control->size(80)}<br />
        SN: {php echo $form['items'][$cont->name]['sn']->control->size(20)} {$ordItems[$oriid]->orisn}
        </td>
        <td style="text-align: center">{php echo $form['items'][$cont->name]['oriprice']->control}<br />{$ordItems[$oriid]->oridisc}</td>
        <td>{php echo $form['items'][$cont->name]['oriqty']->control}</td>
        <td>
          <a href="{plink :Front:Product:detail, $ordItems[$oriid]->oriproid, ($ordItems[$oriid]|getProKey)}"><img src="{$baseUri}/admin/ico/front.png" width="16" height="16" alt="zobrazit zboží ve veřejné části" title="zobrazit zboží ve veřejné části" /></a>
          <a href="{plink Product:edit, $ordItems[$oriid]->oriproid}"><img src="{$baseUri}/admin/ico/edit.png" width="16" height="16" alt="upravit" title="upravit" /></a>
        </td>
        <td><a href="{plink Order:deleteItem, $form['items'][$cont->name]['oriid']->value, (int)$presenter->getParam('id')}" onclick="return DeleteConfirm('položku objednávky {$form['items'][$cont->name]['oriname']->value|noescape}');"> <img src="{$baseUri}/admin/ico/delete.png" width="16" height="16" /> SMAZAT </a></td>
      </tr>
      {else}
      <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
        <td>poštovné</td>
        <td></td>
        <td>{php echo $form['items'][$cont->name]['oriname']->control->size(80)} </td>
        <td>
          Cena: {$ordItems[$oriid]->oriprice}<br />
          {php echo $form['items'][$cont->name]['oripricemaster']->control->size(3)}
        </td>
        <td>1</td>
        <td colspan="2"></td>
      </tr>
      {/if}
      {/foreach}
      {if $ordItemDisc}
      <tr>
        <th colspan="10">Sleva</th>
      </tr>
      {foreach $ordItemDisc as $item}
      <tr>
        <td></td>
        <td></td>
        <td>{$item->oriname}</td>
        <td>{$item->oriprice}</td>
        <td>1</td>
        <td></td>
        <td colspan="2"></td>
      </tr>
      {/foreach}
      {/if}
      <tr>
        <th colspan="7">nová položka</th>
      </tr>
      <tr>
        <td>{php echo $form['newitem']['oriproid']->control}</td>
        <td></td>
        <td>{php echo $form['newitem']['oriname']->control->size(80)} </td>
        <td>{php echo $form['newitem']['oriprice']->control}</td>
        <td>{php echo $form['newitem']['oriqty']->control}</td>
        <td colspan="2"></td>
      </tr>
      <tr>
        <td colspan="8">{input saveitems}</td>
      </tr>
    </table>
  {/form}

  {control orderEditForm}

  {foreach $statusLog as $row}
    {if $iterator->isFirst()}
    <h3>Historie objednávky</h3>
    <table>
    {/if}
    <tr>
    <td>{$row->orldatec|date:'d.m.Y H:i'}</td>
    <td>{$enum_ordstatus[$row->orlstatus]}</td>
    <td>{$row->orldesc}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}
<p>
    <strong>Souhlasy agregátory:</strong><br>
    Souhlas heureka.cz: {if $dataRow->ordheurekagdpr!=1}ANO{else}NE{/if}<br>
    Souhlas zbozi.cz: {if $dataRow->ordzbozigdpr!=1}ANO{else}NE{/if}<br>
    <br>
    <strong>Mailová výzva heureka.cz:</strong><br>
    blokována: {if (bool)$dataRow->ordheurekamailblock}ANO{else}NE{/if}<br>
    odeslána: {if $dataRow->ordheurekamail}{$dataRow->ordheurekamail|date:'d.m.Y'}{else}NE{/if}<br>
</p>