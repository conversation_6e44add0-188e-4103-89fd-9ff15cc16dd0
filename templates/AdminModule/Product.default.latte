{block #content}
  <h3>{block #title}Seznam zboží{/block}</h3>
  {foreach $prosNotInCatalog as $row}
    {if $iterator->isFirst()}
    <strong style="color: red;">Položky které nejsou zařazeny v katalogu</strong><br>
    {/if}
    <a href="{plink Product:edit, $row->proid}">{$row->proname}</a> <a href="{plink Product:edit, $row->proid}"><img src="{$baseUri}/admin/ico/edit.png" width="16" height="16"  alt="upravit položku" title="upravit položku" /></a><br>
    {if $iterator->isLast()}
    <br>
    {/if}
  {/foreach}

  <p><a href="{plink Product:recalcSaleStat}">Přepočítat statistiky prodejnosti</a></p>
  {form searchForm}
  <fieldset>
  <legend><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></legend>
  {label code /}: {input code}
  {label code2 /}: {input code2}
  {label codep /}: {input codep}
  {label name /}: {input name}
  {label manid /}: {input manid}<br />
  {input typid} {label typid /} {input typid2} {label typid2 /} {input typid3} {label typid3 /} {input typid4} {label typid4 /} {input typid5} {label typid5 /}  {input prioritize} {label prioritize /}
  {input codepempty} {label codepempty /} {input code2empty} {label code2empty /} {input withoutvariants} {label withoutvariants /} {input paginationoff} {label paginationoff /}<br />

  {label catid /}: {input catid}  {label status /}: {input status}
  {label orderby /}: {input orderby} {input orderbytype}
  {input search}
  {input clear}
  </fieldset>
  {/form}

 {control paginator}
{form listEditForm}
 <table class="grid">
  <tr>
    <th>Kat. č.</th>
    <th>EAN / Kód P</th>
    <th>Název</th>
    <th>Výrobce</th>
    <th>Cena</th>
    <th>Dostupnost</th>
    <th>Prodáno</th>
    <th>Pořadí</th>
    <th>Status</th>
    <th colspan="3"></th>
  </tr>
  {foreach $dataRows as $row}
    {var $container = $form[$row->proid]}
    {php
    $tabindex = 100 + $iterator->getCounter();
    }
    <tr{if !$iterator->isOdd()} bgcolor="#D0D0D0"{/if}>
      <td>{$row->procode}</td>
      <td style="text-align: left">
        EAN:{$row->procode2}<br>{php echo $container["procode2"]->getControl()->addAttributes(array('size'=>13,'tabindex'=>$tabindex))}<br>
        KódP:{$row->procodep}<br>{php echo $container["procodep"]->getControl()->addAttributes(array('size'=>13,'tabindex'=>$tabindex))}
      </td>
      <td style="{if $row->proismaster==1}background-color:#C0C0FF{elseif (int)$row->promasid > 0}background-color:#FCB633{/if}">{$row->proname} {$row->proname2}</td>
      <td>{$row->manname}</td>
      <td>{$row->proprice1a|formatPriceByCurId:1}</td>
      <td>{php echo $container["proaccess"]->getControl()->addAttributes(array('size'=>1))}</td>
      <td>{$row->prscnt} ks</td>
      <td>{$row->proorder}{php echo $container["proorder"]->getControl()}</td>
      <td>{php echo $container["prostatuschange"]->getControl()->addAttributes(array('title'=>'změnit status'))} {if $row->prostatus == 0} <img src="{$baseUri}/admin/ico/accept.png" width="16" height="16"  alt="aktivní položka" title="aktivní položka" /> {else} <img src="{$baseUri}/admin/ico/block.png" width="16" height="16"  alt="blokovaná položka" title="blokovaná položka" /> {/if}</td>
      <td><a href="{plink Product:edit, $row->proid}"><img src="{$baseUri}/admin/ico/edit.png" width="16" height="16"  alt="upravit položku" title="upravit položku" /></a></td>
      <td><a href="{plink :Front:Product:detail, $row->proid, ($row|getProKey)}"><img src="{$baseUri}/admin/ico/front.png" width="16" height="16" alt="zobrazit zboží ve veřejné části" title="zobrazit zboží ve veřejné části" /></a></td>
      <td><a href="{plink Product:delete, $row->proid}" onclick="return DeleteConfirm('položku {$row->proname|noescape}');"><img src="{$baseUri}/admin/ico/delete.png" width="16" height="16" /></a></td>
    </tr>
  {/foreach}
  </table>
  {input save}
  {/form listEditForm}
  {control paginator}
{/block}
