{block #content}
  {default $dataRow = false}
  {var $tab = $presenter->getParam('tab')}

  <h3>{block #title}{if $dataRow} {$dataRow->proname}  {else} Nová položka {/if}{/block}</h3>
  {if $dataRow}
  <p>
  <a href="{plink :Front:Product:detail, $dataRow->proid, ($dataRow|getProKey)}">Detail zboží ve veřejné <PERSON></a> |
  <a href="{plink :Front:Mall:updateProduct, $dataRow->procode}">Vynutit MALL aktualizaci</a> |
  <a href="{plink :Front:Mall:checkActiveProduct, $dataRow->procode}">Mall - kontrola zda varianty nejsou vymazané</a> |
  <a href="{plink :Front:Mall:deleteProduct, $dataRow->procode}">Vymazat produkt</a>
  </p>
  {/if}
  <script type="text/javascript">
  $(document).ready(function() {
    $(function() {
      $("#tabs").tabs();
    });
  });
  </script>

  <div id="tabs">
   <ul>
      <li><a href="#tabs_editmain">Základní údaje</a></li>
      <li><a href="#tabs_editcatalog">Zařazení do katalogu</a></li>
      <li><a href="#tabs_editdesc">Dlouhý popis</a></li>
      <li><a href="#tabs_seo">SEO</a></li>
      <li><a href="#tabs_editrem">Rozšířené údaje</a></li>
      <li><a href="#tabs_pic">Obrázky</a></li>
      <li><a href="#tabs_attachment">Přílohy</a></li>
      <li><a href="#tabs_param">Parametry</a></li>
      <li><a href="#tabs_subitems">Podřízené položky</a></li>
   </ul>
   {control productEditForm}
   {if $subItems}
   <div id="tabs_subitems">
   <h3>Podřízené položky</h3>
 <table class="grid">
  <tr>
    <th>Katalogové č.</th>
    <th>Název</th>
    <th>Cena</th>
    <th>Dostupnost</th>
    <th>Status</th>
    <th colspan="2"></th>
  </tr>
  {foreach $subItems as $row}
    <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
      <td>{$row->procode}</td>
      <td>{$row->proname}</td>
      <td>{$row->proprice1a|formatPrice:$curId}</td>
      <td>{$enum_proaccess[$row->proaccess]} {if (int)$row->proqty > 0} ({$row|getQtyText}){/if}</td>
      <td>{$enum_prostatus[$row->prostatus]}</td>
      <td><a href="{plink Product:edit, $row->proid}"><img src="{$baseUri}/admin/ico/edit.png" width="16" height="16"  alt="upravit položku" title="upravit položku" /></a></td>
      <td><a href="{plink :Front:Product:detail, $row->proid, ($row|getProKey)}"><img src="{$baseUri}/admin/ico/front.png" width="16" height="16" alt="zobrazit zboží ve veřejné části" title="zobrazit zboží ve veřejné části" /></a></td>
    </tr>
  {/foreach}
  </table>
   </div>
   {/if}
</div>
<script type="text/javascript" src="{$baseUri}/admin/js/textarea_maxlen.js"></script>

{if ($dataRow->proismaster)}
<em>{$dataRow->provariants|nl2br|noescape}</em>
{/if}

{/block}
