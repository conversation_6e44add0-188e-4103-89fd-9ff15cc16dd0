<!DOCTYPE html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta name="robots" content="nofollow,noindex" />
  <link rel="stylesheet" media="screen,projection,tv" href="{$baseUri}/admin/css/admin.css" type="text/css">
  <link rel="stylesheet" media="print" href="{$baseUri}/admin/css/admin-print.css" type="text/css">
	<title>ADMINISTRACE {$presenter->config["SERVER_NAME"]} - {block #title}{/block}</title>
  <script src="{$baseUri}/admin/js/jquery.js" type="text/javascript" language="javascript"></script>
  <script src="{$baseUri}/admin/js/jquery-ui.js" type="text/javascript" language="javascript"></script>
  <link rel="stylesheet" href="{$baseUri}/admin/css/jquery/jquery-ui.css" type="text/css">
  <script src="{$baseUri}/admin/js/netteForms.js" type="text/javascript"></script>
  <script src="{$baseUri}/admin/js/functions.js" type="text/javascript" language="javascript"></script>
  <!-- TinyMCE -->
  <script language="javascript" type="text/javascript" src="{$baseUri}/admin/js/tiny_mce/jquery.tinymce.js"></script>
  <script type="text/javascript">
  $().ready(function() {
    $('textarea.mceEditor').tinymce({
      // Location of TinyMCE script
      script_url : '{$basePath|noescape}/admin/js/tiny_mce/tiny_mce.js',
      mode : "textareas",
      theme : "advanced",
      entities : "160,nbsp,38,amp,162,cent,8364,euro,163,pound,165,yen,169,copy,174,reg,8482,trade",
      remove_linebreaks : false,
      verify_html : true,
      forced_root_block: false,
      convert_urls : false,
      html_mode_word_wrap : true,
      editor_selector : "mceEditor",
      language : "cs",
      forced_root_block : 'p',
      force_br_newlines : false,
      force_p_newlines : true,
      height : 500,
      content_css : "{$baseUri|noescape}/admin/css/tinymce_content.css",
      plugins : "table,noneditable,preview,contextmenu,visualchars,nonbreaking,paste,advimage,media,",
      theme_advanced_blockformats : "h3,h4,p,address",
      theme_advanced_buttons1 : "preview,|,formatselect,bold,italic,|,bullist,numlist,|,cut,copy,paste|,undo,redo,|,visualchars,nonbreaking,|,link,unlink,|,image,media,|,tablecontrols,|,code",
      theme_advanced_buttons2 : "cut,copy,paste,pastetext,pasteword,|,forecolor,backcolor",
      theme_advanced_buttons3 : "",
      theme_advanced_toolbar_location : "top",
      theme_advanced_toolbar_align : "left",
      {ifset $imagesListId}external_image_list_url : {plink //jsImagesList, $imagesListId},{/ifset}
      valid_elements : "+a[id|href|title|class|rel|onclick|target],-strong/-b[class],-i[class],#p[id|class|style],-ol[id|class],-ul[id|class],-li[class],br,img[class|src|alt=|title|width|height|onclick],-table[id|class],-tr[id|class|rowspan],tbody[id|class],caption[id|class],thead[id|class],tfoot[id|class],#td[class|colspan|rowspan],-th[class|colspan|rowspan],-div[id|class],span[class|style],address[id|class],-h1[id|class],-h2[id|class],-h3[id|class],-h4[id|class],hr,dd[id|class],dl[id|class],dt[id|class],-object[style|width|height],param[name|value],embed[src|type|allowscriptaccess|allowfullscreen|width|height],iframe[src|frameborder|style|scrolling|class|width|height|name|align]",
      invalid_elements : "font,em,u",
      theme_advanced_styles: 'Obtékat zprava=imgl;Obtékat zleva=imgr;Odkaz ponechat=keep_it'
    });
  });
</script>
</head>

<body>
  <div id="content">
	  {*horni menu*}
    {include '@menuTop.latte'}

    {*info box*}
    {foreach $flashes as $flash}<div class="flash {$flash->type}">{$flash->message}</div>{/foreach}

    {*obsah*}
    {include #content}

    {*paticka*}
    <div id="footer">
      <p>

	  Databáze: {$dbName} |
      <a href="{plink :Front:Homepage:default}">Veřejná část</a> |
      {if $admin->admid > 0}
      Přihlášen/a <a href="{plink Admin:edit $admin->admid}">{$admin->admname} ({$admin->admmail})</a>
      {else}
      Nepřihlášen/a
      {/if}

	  </p>
    </div>
  </div>
</body>
</html>
