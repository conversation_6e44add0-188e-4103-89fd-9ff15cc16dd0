{block #content}
  <h3>{block #title}Z<PERSON>azníci{/block}</h3>
  {$control['searchForm']->render('begin')|noescape}
  <fieldset>
  <legend>Vyhledávání</legend>
  {$control['searchForm']['ic']->label} {$control['searchForm']['ic']->control}
  {$control['searchForm']['stname']->label} {$control['searchForm']['stname']->control}
  {$control['searchForm']['firname']->label} {$control['searchForm']['firname']->control}
  {$control['searchForm']['email']->label} {$control['searchForm']['email']->control}
  {$control['searchForm']['mailingsend']->label} {$control['searchForm']['mailingsend']->control}
  {$control['searchForm']['prccat']->label} {$control['searchForm']['prccat']->control}<br>

  {$control['searchForm']['mailing']->label} {$control['searchForm']['mailing']->control}<small>pokud vyberete, ostatní nastavení filtru se bude ignorovat</small> | {$control['searchForm']['dateuntil']->label} {$control['searchForm']['dateuntil']->control}<small>ti kteří neobjednali po tomto datu</small> | {$control['searchForm']['dateafter']->label} {$control['searchForm']['dateafter']->control}<small>ti kteří objednali po tomto datu</small><br>
  {$control['searchForm']['datefrom']->label} {$control['searchForm']['datefrom']->control} {$control['searchForm']['dateto']->label} {$control['searchForm']['dateto']->control}<small>pokud vyberete, ostatní nastavení filtru se bude ignorovat</small>
  {$control['searchForm']['search']->control}
  {$control['searchForm']['clear']->control}
  Exportovat emaily: {$control['searchForm']['export']->control}  {$control['searchForm']['export_md5']->control}  {$control['searchForm']['export_ecomail']->control}
  </fieldset>
  {$control['searchForm']->render('end')}

  {* strankovani *}
  {control paginator}
  <table class="grid">
  <tr>
    <th>Jméno</th>
    <th>Firma</th>
    <th>IČ</th>
    <th>Sleva</th>
    <th>Cena</th>
    <th>Email</th>
    <th>Mobil</th>
    <th>Obrat</th>
    <th>Mailling</th>
    <th>Mail</th>
    <th>Souhlas</th>
    <th>Status</th>
    <th></th>
    <th></th>
    <th></th>
  </tr>
{foreach $dataRows as $row}
  <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
    <td>{$row->usriname} {$row->usrilname}</td>
    <td>{$row->usrifirname}</td>
    <td>{$row->usric}</td>
    <td>{$row->usrdiscount}%</td>
    <td>{$enum_usrprccat[$row->usrprccat]}</td>
    <td>{$row->usrmail}</td>
    <td>{$row->usrtel}</td>
    <td>{if isset($row->orderssum)}{$row->orderssum|formatPrice}{/if}</td>
    <td>{if $row->usrmaillist == 1} <img src="{$baseUri}/admin/ico/accept.png" width="16" height="16" /> {else} <img src="{$baseUri}/admin/ico/delete.png" width="16" height="16" /> {/if}</td>
    <td>{if $row->usrmailverified === 1}<img src="{$baseUri}/admin/ico/accept.png" width="16" height="16" />{else}<img src="{$baseUri}/admin/ico/delete.png" width="16" height="16" />{/if}</td>
    <td>{if isset($row->aprove)}{if $row->aprove === 1}<img src="{$baseUri}/admin/ico/accept.png" width="16" height="16" />{else}<img src="{$baseUri}/admin/ico/delete.png" width="16" height="16" />{/if}{/if}</td>
    <td>{$enum_usrstatus[$row->usrstatus]}</td>
    <td><a href="{plink User:edit, $row->usrid}"><img src="{$baseUri}/admin/ico/edit.png" width="16" height="16" /></a></td>
    <td><a href="{plink User:delete, $row->usrid}" onclick="return DeleteConfirm('účet {$row->usrmail}');"><img src="{$baseUri}/admin/ico/delete.png" width="16" height="16" /></a></td>
    <td><a href="{plink Order:default "sUsrId"=>$row->usrid}"><img src="{$baseUri}/admin/ico/xml.png" width="16" height="16" /></a></td>
  </tr>
{/foreach}
  </table>
  {* strankovani *}
  {control paginator}
{/block}
