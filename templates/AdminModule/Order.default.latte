{block #content}
  <script>
  function noteSH(id) {
     if ($('#'+id).is(':visible')) {
      $('#'+id).hide();
     } else {
       $('#'+id).show();
     }
     return false;
  }
  </script>
  <h3>{block #title}Seznam Objednávek{/block}</h3>
  {form searchForm}
  <fieldset>
  <legend>Vyhledávání</legend>
  {label code /} {input code}
  {label name /} {input name}
  {label admin /} {input admin}
  {label delid /} {input delid}<br>
  {label payid /} {input payid}
  {input notclosed} {label notclosed /}
  {input ordtype} {label ordtype /} {if isset($searchUser)} | Jen objednávky od {$searchUser->usrmail}{/if}<br />
  {label datefrom /} - do: {input datefrom}-{input dateto}
  {label status /} {input status}
  {label orderby /} {input orderby} {input orderbytype}
  {label rows /} {input rows size=>2}
  {input search}
  {input clear}
  {input export}
  </fieldset>
  {/form}
  {include @ordersList.latte}

  {control paginator}

  {*zpětný import balíků přepravce*}
  <h4>Zpětný import balíků přepravce</h4>
  <p>soubor musí být ve fromátu CSV (oddělovač středník)</p>
  {control postImportParcelsForm}

{/block}
