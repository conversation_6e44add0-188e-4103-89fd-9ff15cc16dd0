{block #content}
  <script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('proname', 'proid');
          $("#"+newId).val(ui.item.id);
        }
        return false;
      }
    });
  });
  </script>

  <h3>{block #title} Editace mailování {/block}</h3>
  {if $id>0}<a href="{plink :Front:Mailing:detail, $id, 1, '987e0a3b7c70ca1930d0597c059e9c65'}">Náhled</a>{/if}
  {form editForm}
  <ul class="error" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$error}</li>
  </ul>
  <table>
  <tr><th>{label mamdate /}</th><td>{input mamdate} <small><PERSON><PERSON><PERSON>te ve formátu dd.mm.rrrr</small></td></tr>
  <tr class="required"><th>{label mamsubject /}</th><td>{input mamsubject}</td></tr>
  <tr class="required"><th>{label mambody /}</th><td>{input mambody}</td></tr>
  <tr class="required"><th></th><td>{input mammaillist} {label mammaillist /}</td></tr>
  <tr class="required"><th>Cenové hladiny:</th>
    <td>
      {input mampricea} {label mampricea /}<br>
      {input mampriceb} {label mampriceb /}<br>
      {input mampricec} {label mampricec /}<br>
      {input mampriced} {label mampriced /}<br>
      {input mampricee} {label mampricee /}
    </td>
  </tr>
  <tr><th>{label mambuycatid /}</th><td>{input mambuycatid} </td></tr>
  <tr><th>{label mambuydays /}</th><td>{input mambuynot} za posledních {input mambuydays} dní</td></tr>
  <tr><th colspan="2">
  <p>Produkty <br><small>vyplňte tak, aby každý blok (označený stejnou barvou) byl celý zaplněný produkty.</small></p>
  <table class="grid">
    {for $i=0 ; $i<=11 ; $i++}
      {if ($i % 2 == 0)}
        {php $color = "#C0FFC0"}
      {else}
        {php $color = "#9FA5EC"}
      {/if}

      <tr style="background: {$color|noescape};">
        <td>{$i+1}.</td>
        <td>Id: {php echo $form["products"][$i]['proid']->control->readonly('readonly')->size(5)}</td>
        <td>
          Název: {php echo $form["products"][$i]['proname']->control->size(70) } Dodatek: {php echo $form["products"][$i]['prodesc1']->control->size(61)}<br>
          Popis: {php echo $form["products"][$i]['prodesc2']->control->size(150)}
        </td>
      </tr>
    {/for}
  </table>
  </th></tr>
  <tr class="required"><th>{label mamfooter /}</th><td>{input mamfooter}</td></tr>
  <tr><th>{label mamstatus /}</th><td>{input mamstatus}</td></tr>
  
  </table>
  {input save}
  
  {/form}
  
  {if $id>0}
  <h3>Testovací mailování</h3>
  {control mailTestForm}
  {/if}
{/block}
