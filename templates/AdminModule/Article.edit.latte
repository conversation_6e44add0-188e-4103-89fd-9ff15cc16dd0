{block #content}
  <h3>{block #title} Editace článku</h3>
  
  {control editForm}
  
  {* hlavi obrazek - pokud je *}
  {if ($mainImageName != "")}
    <img src="{$baseUri}/pic/art/list/{$mainImageName}" />
  {/if}
  
  {foreach $images as $row}
    {if $iterator->isFirst()}
     <h3>Přiložené obr<PERSON></h3>
      <table>
      <tr>
    {/if}
    <td><img src="{$baseUri}/files/{$row->atafilename}" height="180" alt="{$row->ataname}" /><br>{$row->ataname} | <a href="{plink DeleteAttachment, $row["ataid"], $id}"><img src="{$baseUri}/admin/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat {$row["ataname"]}" /></a></td>
    {if $iterator->isLast()}
      </tr>
      </table>
    {/if}
  {/foreach}
  {foreach $attachments as $row}
    {if $iterator->isFirst()}
     <h3>Ostatní přílohy</h3>
      <table>
    {/if}
    <tr>
      <td><a href="{$baseUri}/files/{$row->atafilename}">{$row->ataname}</a></td>
      <td><a href="{plink DeleteAttachment, $row["ataid"], $id}"><img src="{$baseUri}/admin/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat {$row["ataname"]}" /></a></td>
    </tr>
    {if $iterator->isLast()}
      </table>
    {/if}
  {/foreach}
  {control uploadForm}
  <script type="text/javascript" src="{$baseUri}/admin/js/textarea_maxlen.js"></script>
{/block}
