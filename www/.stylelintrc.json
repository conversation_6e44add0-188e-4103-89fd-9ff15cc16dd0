{"extends": "stylelint-config-standard-scss", "rules": {"color-hex-case": ["lower"], "declaration-empty-line-before": null, "max-empty-lines": 2, "no-extra-semicolons": true, "media-feature-name-no-vendor-prefix": true, "property-no-vendor-prefix": true, "block-no-empty": null, "selector-max-specificity": "0,4,0", "selector-pseudo-element-colon-notation": null, "function-parentheses-space-inside": "always", "no-descending-specificity": null, "selector-class-pattern": null, "shorthand-property-no-redundant-values": null, "scss/no-global-function-names": null, "number-max-precision": null, "string-quotes": null, "function-url-quotes": null, "color-function-notation": null, "alpha-value-notation": null, "font-family-name-quotes": null, "max-line-length": null, "selector-no-vendor-prefix": null, "declaration-block-no-redundant-longhand-properties": null, "at-rule-no-unknown": [true, {"ignoreAtRules": ["function", "if", "each", "include", "mixin"]}]}}