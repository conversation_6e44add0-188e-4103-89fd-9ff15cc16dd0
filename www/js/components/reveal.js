// otev<PERSON><PERSON><PERSON><PERSON> s<PERSON>ch částí
// po kliknutí na zobrazovací blok
$(document).on('click', 'a.js-reveal', function (event) {
  event.preventDefault();

  // zjištění ID z href atributu
  var revealName = $(this).attr('href');

  // zobrazení skryté části
  $(revealName).toggleClass('is-visible');

  // skrytí prvku
  $(this).hide();
});

// po změně formulářového prvku (checkbox)
$(document).on('change', '.js-reveal input', function () {
  // zjištění ID z data atributu
  var revealName = '#' + $(this).closest('.js-reveal').data('reveal');

  // zobrazení/skrytí bloku podle zaškrtnutí
  if ($(this).is(':checked')) {
    $(revealName).addClass('is-visible');
  } else {
    $(revealName).removeClass('is-visible');
  }
});
