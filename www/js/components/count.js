// přičítání počtu kusů
$(document).on('click', '.product__count .icon--plus', function () {
  // zjištění, převod na číslo a ošetření
  var quantity = $(this).parent().find('input').val();
  quantity = parseFloat(quantity);
  if (quantity <= 0 || !$.isNumeric(quantity)) {
    quantity = 0;
  }
  // přičtení
  quantity = quantity + 1;
  // nastavení čísla
  $(this).parent().find('input').val(quantity).change();
});

// odečítání počtu kusů
$(document).on('click', '.product__count .icon--minus', function () {
  // zjištění, převod na číslo a ošetření
  var quantity = $(this).parent().find('input').val();
  quantity = parseFloat(quantity);
  if (quantity <= 0 || !$.isNumeric(quantity)) {
    quantity = 1;
  }
  // odečtení
  quantity = quantity - 1;
  // nastavení čísla
  $(this).parent().find('input').val(quantity).change();
});
