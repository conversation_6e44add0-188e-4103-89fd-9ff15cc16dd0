// mobilní menu - přepínání
function switchMenu() {
  // označíme zda je menu zavřeno či nikoliv
  if ($('.nav__switcher').hasClass('is-open')) {
    $('.nav, .nav__switcher')
      .removeClass('is-open')
      .attr('aria-expanded', 'false');
  } else {
    $('.nav, .nav__switcher').addClass('is-open').attr('aria-expanded', 'true');
  }
}

// reset menu
function resetMenu() {
  $('.nav, .nav__switcher')
    .removeClass('is-open')
    .attr('aria-expanded', 'false');
}

// spouštěč
$('.nav__switcher').on('click', function () {
  switchMenu();
});

// aria atributy k menu
if (window.innerWidth < 992) {
  $('.nav, .nav__switcher').attr('aria-expanded', 'false');
}
