{"version": 3, "file": "scripts.js", "sources": ["js/scripts.js"], "sourcesContent": ["/*\n\tBy <PERSON>, www.andrerinas.de\n\tDocumentation, www.simplelightbox.de\n\tAvailable for use under the MIT License\n\t1.17.3\n*/\n!function(st,ot,E){\"use strict\";st.fn.simpleLightbox=function(N){N=st.extend({sourceAttr:\"href\",overlay:!0,spinner:!0,nav:!0,navText:[\"&lsaquo;\",\"&rsaquo;\"],captions:!0,captionDelay:0,captionSelector:\"img\",captionType:\"attr\",captionsData:\"title\",captionPosition:\"bottom\",captionClass:\"\",close:!0,closeText:\"&times;\",swipeClose:!0,showCounter:!0,fileExt:\"png|jpg|jpeg|gif|webp\",animationSlide:!0,animationSpeed:250,preloading:!0,enableKeyboard:!0,loop:!0,rel:!1,docClose:!0,swipeTolerance:50,className:\"simple-lightbox\",widthRatio:.8,heightRatio:.9,scaleImageToRatio:!1,disableRightClick:!1,disableScroll:!0,alertError:!0,alertErrorMessage:\"Image not found, next image will be loaded\",additionalHtml:!1,history:!0,throttleInterval:0,doubleTapZoom:2,maxZoom:10,htmlClass:\"has-lightbox\"},N);function P(){return o.hash.substring(1)}function p(){P();var t=\"pid=\"+(V+1),e=o.href.split(\"#\")[0]+\"#\"+t;s?history[f?\"replaceState\":\"pushState\"](\"\",E.title,e):f?o.replace(e):o.hash=t,f=!0}function t(e,a){var n;return function(){var t=arguments;n||(e.apply(this,t),n=!0,setTimeout(function(){return n=!1},a))}}function a(t){t.trigger(st.Event(\"show.simplelightbox\")),N.disableScroll&&(u=T(\"hide\")),N.htmlClass&&\"\"!=N.htmlClass&&st(\"html\").addClass(N.htmlClass),b.appendTo(\"body\"),_.appendTo(b),N.overlay&&l.appendTo(st(\"body\")),m=!0,V=$.index(t),K=st(\"<img/>\").hide().attr(\"src\",t.attr(N.sourceAttr)).attr(\"data-scale\",1).attr(\"data-translate-x\",0).attr(\"data-translate-y\",0),-1==d.indexOf(t.attr(N.sourceAttr))&&d.push(t.attr(N.sourceAttr)),_.html(\"\").attr(\"style\",\"\"),K.appendTo(_),C(),l.fadeIn(\"fast\"),st(\".sl-close\").fadeIn(\"fast\"),g.show(),J.fadeIn(\"fast\"),st(\".sl-wrapper .sl-counter .sl-current\").text(V+1),c.fadeIn(\"fast\"),tt(),N.preloading&&w(),setTimeout(function(){t.trigger(st.Event(\"shown.simplelightbox\"))},N.animationSpeed)}function Z(t,e,a){return t<e?e:a<t?a:t}function z(t,e,a){K.data(\"scale\",t),K.data(\"translate-x\",e),K.data(\"translate-y\",a)}var h,e,H=\"ontouchstart\"in ot,L=(ot.navigator.pointerEnabled||ot.navigator.msPointerEnabled,0),j=0,K=st(),i=function(){var t=E.body||E.documentElement;return\"\"===(t=t.style).WebkitTransition?\"-webkit-\":\"\"===t.MozTransition?\"-moz-\":\"\"===t.OTransition?\"-o-\":\"\"===t.transition&&\"\"},U=!1,d=[],$=N.rel&&!1!==N.rel?(e=N.rel,st(this).filter(function(){return st(this).attr(\"rel\")===e})):this,n=$.get()[0].tagName,u=(i=i(),0),B=!1!==i,s=\"pushState\"in history,f=!1,o=ot.location,Q=P(),G=\"simplelb\",l=st(\"<div>\").addClass(\"sl-overlay\"),r=st(\"<button>\").addClass(\"sl-close\").html(N.closeText),g=st(\"<div>\").addClass(\"sl-spinner\").html(\"<div></div>\"),J=st(\"<div>\").addClass(\"sl-navigation\").html('<button class=\"sl-prev\">'+N.navText[0]+'</button><button class=\"sl-next\">'+N.navText[1]+\"</button>\"),c=st(\"<div>\").addClass(\"sl-counter\").html('<span class=\"sl-current\"></span>/<span class=\"sl-total\"></span>'),m=!1,V=0,v=0,x=st(\"<div>\").addClass(\"sl-caption \"+N.captionClass+\" pos-\"+N.captionPosition),_=st(\"<div>\").addClass(\"sl-image\"),b=st(\"<div>\").addClass(\"sl-wrapper\").addClass(N.className),tt=function(o){if(K.length){var l=new Image,r=ot.innerWidth*N.widthRatio,c=ot.innerHeight*N.heightRatio;l.src=K.attr(\"src\"),K.data(\"scale\",1),K.data(\"translate-x\",0),K.data(\"translate-y\",0),at(0,0,1),st(l).on(\"error\",function(t){$.eq(V).trigger(st.Event(\"error.simplelightbox\")),U=!(m=!1),g.hide();var e=1==o||-1==o;v===V&&e?it():(N.alertError&&alert(N.alertErrorMessage),nt(e?o:1))}),l.onload=function(){void 0!==o&&$.eq(V).trigger(st.Event(\"changed.simplelightbox\")).trigger(st.Event((1===o?\"nextDone\":\"prevDone\")+\".simplelightbox\")),N.history&&(f?h=setTimeout(p,800):p()),-1==d.indexOf(K.attr(\"src\"))&&d.push(K.attr(\"src\"));var t=l.width,e=l.height;if(N.scaleImageToRatio||r<t||c<e){var a=r/c<t/e?t/r:e/c;t/=a,e/=a}st(\".sl-image\").css({top:(ot.innerHeight-e)/2+\"px\",left:(ot.innerWidth-t-u)/2+\"px\",width:t+\"px\",height:e+\"px\"}),g.hide(),K.fadeIn(\"fast\"),U=!0;var n,i=\"self\"==N.captionSelector?$.eq(V):$.eq(V).find(N.captionSelector);if(n=\"data\"==N.captionType?i.data(N.captionsData):\"text\"==N.captionType?i.html():i.prop(N.captionsData),N.loop||(0===V&&st(\".sl-prev\").hide(),V>=$.length-1&&st(\".sl-next\").hide(),0<V&&st(\".sl-prev\").show(),V<$.length-1&&st(\".sl-next\").show()),1==$.length&&st(\".sl-prev, .sl-next\").hide(),1==o||-1==o){var s={opacity:1};N.animationSlide&&(B?(et(0,100*o+\"px\"),setTimeout(function(){et(N.animationSpeed/1e3,\"0px\")},50)):s.left=parseInt(st(\".sl-image\").css(\"left\"))+100*o+\"px\"),st(\".sl-image\").animate(s,N.animationSpeed,function(){m=!1,y(n,t)})}else m=!1,y(n,t);N.additionalHtml&&0===st(\".sl-additional-html\").length&&st(\"<div>\").html(N.additionalHtml).addClass(\"sl-additional-html\").appendTo(st(\".sl-image\"))}}},y=function(t,e){\"\"!==t&&void 0!==t&&N.captions&&x.html(t).css({width:e+\"px\"}).hide().appendTo(st(\".sl-image\")).delay(N.captionDelay).fadeIn(\"fast\")},et=function(t,e){var a={};a[i+\"transform\"]=\"translateX(\"+e+\")\",a[i+\"transition\"]=i+\"transform \"+t+\"s linear\",st(\".sl-image\").css(a)},at=function(t,e,a){var n={};n[i+\"transform\"]=\"translate(\"+t+\",\"+e+\") scale(\"+a+\")\",K.css(n)},C=function(){st(ot).on(\"resize.\"+G,tt),st(\".sl-wrapper\").on(\"click.\"+G+\" touchstart.\"+G,\".sl-close\",function(t){t.preventDefault(),U&&it()}),N.history&&setTimeout(function(){st(ot).on(\"hashchange.\"+G,function(){U&&P()===Q&&it()})},40),J.on(\"click.\"+G,\"button\",t(function(t){t.preventDefault(),L=0,nt(st(this).hasClass(\"sl-next\")?1:-1)},N.throttleInterval));var e,a,n,i,s,o,l,r,c,p,h,d,u,f,g,m,v,x,b,y,C,w,T,E,S,M,I,k=0,q=0,X=0,D=0,Y=!1,A=!1,O=0,R=!1,W=Z(1,1,N.maxZoom),F=!1;_.on(\"touchstart.\"+G+\" mousedown.\"+G,function(t){if(\"A\"===t.target.tagName&&\"touchstart\"==t.type)return!0;if(\"mousedown\"==(t=t.originalEvent).type)c=t.clientX,p=t.clientY,e=_.height(),a=_.width(),s=K.height(),o=K.width(),n=_.position().left,i=_.position().top,l=parseFloat(K.data(\"translate-x\")),r=parseFloat(K.data(\"translate-y\")),R=!0;else{if(I=t.touches.length,c=t.touches[0].clientX,p=t.touches[0].clientY,e=_.height(),a=_.width(),s=K.height(),o=K.width(),n=_.position().left,i=_.position().top,1===I){if(F)return K.addClass(\"sl-transition\"),Y=Y?(z(0,0,W=1),at(\"0px\",\"0px\",W),!1):(W=N.doubleTapZoom,z(0,0,W),at(\"0px\",\"0px\",W),st(\".sl-caption\").fadeOut(200),!0),setTimeout(function(){K.removeClass(\"sl-transition\")},200),!1;F=!0,setTimeout(function(){F=!1},300),l=parseFloat(K.data(\"translate-x\")),r=parseFloat(K.data(\"translate-y\"))}else 2===I&&(h=t.touches[1].clientX,d=t.touches[1].clientY,l=parseFloat(K.data(\"translate-x\")),r=parseFloat(K.data(\"translate-y\")),C=(c+h)/2,w=(p+d)/2,u=Math.sqrt((c-h)*(c-h)+(p-d)*(p-d)));R=!0}return!!A||(B&&(O=parseInt(_.css(\"left\"))),A=!0,j=L=0,k=t.pageX||t.touches[0].pageX,X=t.pageY||t.touches[0].pageY,!1)}).on(\"touchmove.\"+G+\" mousemove.\"+G+\" MSPointerMove\",function(t){if(!A)return!0;if(t.preventDefault(),\"touchmove\"==(t=t.originalEvent).type){if(!1===R)return!1;f=t.touches[0].clientX,g=t.touches[0].clientY,I=t.touches.length,0,1<I?(m=t.touches[1].clientX,v=t.touches[1].clientY,M=Math.sqrt((f-m)*(f-m)+(g-v)*(g-v)),null===u&&(u=M),1<=Math.abs(u-M)&&(y=Z(M/u*W,1,N.maxZoom),T=(o*y-a)/2,E=(s*y-e)/2,S=y-W,x=o*y<=a?0:Z(l-(C-n-a/2-l)/(y-S)*S,-1*T,T),b=s*y<=e?0:Z(r-(w-i-e/2-r)/(y-S)*S,-1*E,E),at(x+\"px\",b+\"px\",y),1<y&&(Y=!0,st(\".sl-caption\").fadeOut(200)),u=M,W=y,l=x,r=b)):(T=(o*(y=W)-a)/2,E=(s*y-e)/2,x=o*y<=a?0:Z(f-(c-l),-1*T,T),b=s*y<=e?0:Z(g-(p-r),-1*E,E),Math.abs(x)===Math.abs(T)&&(l=x,c=f),Math.abs(b)===Math.abs(E)&&(r=b,p=g),z(W,x,b),at(x+\"px\",b+\"px\",y))}if(\"mousemove\"==t.type&&A){if(\"touchmove\"==t.type)return!0;if(!1===R)return!1;f=t.clientX,g=t.clientY,T=(o*(y=W)-a)/2,E=(s*y-e)/2,x=o*y<=a?0:Z(f-(c-l),-1*T,T),b=s*y<=e?0:Z(g-(p-r),-1*E,E),Math.abs(x)===Math.abs(T)&&(l=x,c=f),Math.abs(b)===Math.abs(E)&&(r=b,p=g),z(W,x,b),at(x+\"px\",b+\"px\",y)}Y||(q=t.pageX||t.touches[0].pageX,D=t.pageY||t.touches[0].pageY,L=k-q,j=X-D,N.animationSlide&&(B?et(0,-L+\"px\"):_.css(\"left\",O-L+\"px\")))}).on(\"touchend.\"+G+\" mouseup.\"+G+\" touchcancel.\"+G+\" mouseleave.\"+G+\" pointerup pointercancel MSPointerUp MSPointerCancel\",function(t){if(t=t.originalEvent,H&&\"touchend\"==t.type&&(0===(I=t.touches.length)?(z(W,x,b),1==W&&(Y=!1,st(\".sl-caption\").fadeIn(200)),u=null,R=!1):1===I?(c=t.touches[0].clientX,p=t.touches[0].clientY):1<I&&(u=null)),A){var e=!(A=!1);N.loop||(0===V&&L<0&&(e=!1),V>=$.length-1&&0<L&&(e=!1)),Math.abs(L)>N.swipeTolerance&&e?nt(0<L?1:-1):N.animationSlide&&(B?et(N.animationSpeed/1e3,\"0px\"):_.animate({left:O+\"px\"},N.animationSpeed/2)),N.swipeClose&&50<Math.abs(j)&&Math.abs(L)<N.swipeTolerance&&it()}}).on(\"dblclick\",function(t){return c=t.clientX,p=t.clientY,e=_.height(),a=_.width(),s=K.height(),o=K.width(),n=_.position().left,i=_.position().top,K.addClass(\"sl-transition\"),Y?(z(0,0,W=1),at(\"0px\",\"0px\",W),Y=!1,st(\".sl-caption\").fadeIn(200)):(W=N.doubleTapZoom,z(0,0,W),at(\"0px\",\"0px\",W),st(\".sl-caption\").fadeOut(200),Y=!0),setTimeout(function(){K.removeClass(\"sl-transition\")},200),!(R=!0)})},w=function(){var t=V+1<0?$.length-1:V+1>=$.length-1?0:V+1,e=V-1<0?$.length-1:V-1>=$.length-1?0:V-1;st(\"<img />\").attr(\"src\",$.eq(t).attr(N.sourceAttr)).on(\"load\",function(){-1==d.indexOf(st(this).attr(\"src\"))&&d.push(st(this).attr(\"src\")),$.eq(V).trigger(st.Event(\"nextImageLoaded.simplelightbox\"))}),st(\"<img />\").attr(\"src\",$.eq(e).attr(N.sourceAttr)).on(\"load\",function(){-1==d.indexOf(st(this).attr(\"src\"))&&d.push(st(this).attr(\"src\")),$.eq(V).trigger(st.Event(\"prevImageLoaded.simplelightbox\"))})},nt=function(e){$.eq(V).trigger(st.Event(\"change.simplelightbox\")).trigger(st.Event((1===e?\"next\":\"prev\")+\".simplelightbox\"));var t=V+e;if(!(m||(t<0||t>=$.length)&&!1===N.loop)){V=t<0?$.length-1:t>$.length-1?0:t,st(\".sl-wrapper .sl-counter .sl-current\").text(V+1);var a={opacity:0};N.animationSlide&&(B?et(N.animationSpeed/1e3,-100*e-L+\"px\"):a.left=parseInt(st(\".sl-image\").css(\"left\"))+-100*e+\"px\"),st(\".sl-image\").animate(a,N.animationSpeed,function(){setTimeout(function(){var t=$.eq(V);K.attr(\"src\",t.attr(N.sourceAttr)),-1==d.indexOf(t.attr(N.sourceAttr))&&g.show(),st(\".sl-caption\").remove(),tt(e),N.preloading&&w()},100)})}},it=function(){if(!m){var t=$.eq(V),e=!1;t.trigger(st.Event(\"close.simplelightbox\")),N.history&&(s?history.pushState(\"\",E.title,o.pathname+o.search):o.hash=\"\",clearTimeout(h)),st(\".sl-image img, .sl-overlay, .sl-close, .sl-navigation, .sl-image .sl-caption, .sl-counter\").fadeOut(\"fast\",function(){N.disableScroll&&T(\"show\"),N.htmlClass&&\"\"!=N.htmlClass&&st(\"html\").removeClass(N.htmlClass),st(\".sl-wrapper, .sl-overlay\").remove(),J.off(\"click\",\"button\"),st(\".sl-wrapper\").off(\"click.\"+G,\".sl-close\"),st(ot).off(\"resize.\"+G),st(ot).off(\"hashchange.\"+G),e||t.trigger(st.Event(\"closed.simplelightbox\")),e=!0}),K=st(),m=U=!1}},T=function(t){var e=0;if(\"hide\"==t){var a=ot.innerWidth;if(!a){var n=E.documentElement.getBoundingClientRect();a=n.right-Math.abs(n.left)}if(E.body.clientWidth<a){var i=E.createElement(\"div\"),s=parseInt(st(\"body\").css(\"padding-right\"),10);i.className=\"sl-scrollbar-measure\",st(\"body\").append(i),e=i.offsetWidth-i.clientWidth,st(E.body)[0].removeChild(i),st(\"body\").data(\"padding\",s),0<e&&st(\"body\").addClass(\"hidden-scroll\").css({\"padding-right\":s+e})}}else st(\"body\").removeClass(\"hidden-scroll\").css({\"padding-right\":st(\"body\").data(\"padding\")});return e};return N.close&&r.appendTo(b),N.showCounter&&1<$.length&&(c.appendTo(b),c.find(\".sl-total\").text($.length)),N.nav&&J.appendTo(b),N.spinner&&g.appendTo(b),$.on(\"click.\"+G,function(t){if(function(t){if(!N.fileExt)return!0;var e=st(t).attr(N.sourceAttr).match(/\\.([0-9a-z]+)(?=[?#])|(\\.)(?:[\\w]+)$/gim);return e&&st(t).prop(\"tagName\").toUpperCase()==n&&new RegExp(\".(\"+N.fileExt+\")$\",\"i\").test(e)}(this)){if(t.preventDefault(),m)return!1;var e=st(this);v=$.index(e),a(e)}}),st(E).on(\"click.\"+G+\" touchstart.\"+G,function(t){U&&N.docClose&&0===st(t.target).closest(\".sl-image\").length&&0===st(t.target).closest(\".sl-navigation\").length&&it()}),N.disableRightClick&&st(E).on(\"contextmenu\",\".sl-image img\",function(t){return!1}),N.enableKeyboard&&st(E).on(\"keyup.\"+G,t(function(t){L=0;var e=t.keyCode;m&&27==e&&(K.attr(\"src\",\"\"),m=!1,it()),U&&(t.preventDefault(),27==e&&it(),37!=e&&39!=t.keyCode||nt(39==t.keyCode?1:-1))},N.throttleInterval)),this.open=function(t){t=t||st(this[0]),v=$.index(t),a(t)},this.next=function(){nt(1)},this.prev=function(){nt(-1)},this.close=function(){it()},this.destroy=function(){st(E).off(\"click.\"+G).off(\"keyup.\"+G),it(),st(\".sl-overlay, .sl-wrapper\").remove(),this.off(\"click\")},this.refresh=function(){this.destroy(),st(this).simpleLightbox(N)},this}}(jQuery,window,document);\n\n// přičítání počtu kusů\n$(document).on('click', '.product__count .icon--plus', function () {\n  // zjištění, převod na číslo a ošetření\n  var quantity = $(this).parent().find('input').val();\n  quantity = parseFloat(quantity);\n  if (quantity <= 0 || !$.isNumeric(quantity)) {\n    quantity = 0;\n  }\n  // přičtení\n  quantity = quantity + 1;\n  // nastavení čísla\n  $(this).parent().find('input').val(quantity).change();\n});\n\n// odečítání počtu kusů\n$(document).on('click', '.product__count .icon--minus', function () {\n  // zjištění, převod na číslo a ošetření\n  var quantity = $(this).parent().find('input').val();\n  quantity = parseFloat(quantity);\n  if (quantity <= 0 || !$.isNumeric(quantity)) {\n    quantity = 1;\n  }\n  // odečtení\n  quantity = quantity - 1;\n  // nastavení čísla\n  $(this).parent().find('input').val(quantity).change();\n});\n\n// lightbox ( https://github.com/andreknieriem/simplelightbox )\njQuery(document).ready(function ($) {\n  if ($('.js-gallery').length) {\n    $('.js-gallery a').simpleLightbox({\n      showCounter: false,\n    });\n  }\n});\n\n// otevření modal okna\n$(document).on('click', '.js-modal', function (event) {\n  event.preventDefault();\n\n  // zjištění ID okna z href atributu\n  var modalName = $(this).attr('href');\n\n  // otevření okna\n  $(modalName).addClass('is-open');\n});\n\n// zavření modal okna\n$(document).on('click', '.modal__close, .modal__close-false', function (event) {\n  event.preventDefault();\n  $(this).closest('.modal').removeClass('is-open');\n});\n\n// zavření okna kliknutím na pozadí\n$(document).on('click', '.modal', function (event) {\n  event.preventDefault();\n  $(this).removeClass('is-open');\n});\n\n// zamezení zavření po kliknutí na tělo modalu\n$(document).on('click', '.modal__body', function (event) {\n  event.stopPropagation();\n});\n\n// mobilní menu - přepínání\nfunction switchMenu() {\n  // označíme zda je menu zavřeno či nikoliv\n  if ($('.nav__switcher').hasClass('is-open')) {\n    $('.nav, .nav__switcher')\n      .removeClass('is-open')\n      .attr('aria-expanded', 'false');\n  } else {\n    $('.nav, .nav__switcher').addClass('is-open').attr('aria-expanded', 'true');\n  }\n}\n\n// reset menu\nfunction resetMenu() {\n  $('.nav, .nav__switcher')\n    .removeClass('is-open')\n    .attr('aria-expanded', 'false');\n}\n\n// spouštěč\n$('.nav__switcher').on('click', function () {\n  switchMenu();\n});\n\n// aria atributy k menu\nif (window.innerWidth < 992) {\n  $('.nav, .nav__switcher').attr('aria-expanded', 'false');\n}\n\n// aktivní telefon\nif ($('.phone').length) {\n  // zjistíme aktuální hodinu\n  var now = new Date(Date.now());\n  var actualHour = now.getHours();\n  // zjistíme aktuální den\n  var actualDay = now.getDay();\n\n  $('.phone').each(function () {\n    // hodnoty z data atributů\n    var hourStart = $(this).data('start');\n    var hourEnd = $(this).data('end');\n\n    // zjištění jestli je online\n    if (\n      actualDay != 0 &&\n      actualDay != 6 &&\n      actualHour >= hourStart &&\n      actualHour < hourEnd\n    ) {\n      $(this).addClass('is-online');\n    } else {\n      $(this).addClass('is-offline');\n    }\n  });\n}\n\n// otevírání skrytých částí\n// po kliknutí na zobrazovací blok\n$(document).on('click', 'a.js-reveal', function (event) {\n  event.preventDefault();\n\n  // zjištění ID z href atributu\n  var revealName = $(this).attr('href');\n\n  // zobrazení skryté části\n  $(revealName).toggleClass('is-visible');\n\n  // skrytí prvku\n  $(this).hide();\n});\n\n// po změně formulářového prvku (checkbox)\n$(document).on('change', '.js-reveal input', function () {\n  // zjištění ID z data atributu\n  var revealName = '#' + $(this).closest('.js-reveal').data('reveal');\n\n  // zobrazení/skrytí bloku podle zaškrtnutí\n  if ($(this).is(':checked')) {\n    $(revealName).addClass('is-visible');\n  } else {\n    $(revealName).removeClass('is-visible');\n  }\n});\n\njQuery(document).ready(function ($) {\n  // obecne\n  var bodyWidth = window.innerWidth;\n\n  // události při resize okna\n  $(window).on('resize', function () {\n    // osetreni, zda se velikost zmenila\n    if (bodyWidth !== window.innerWidth) {\n      // nastavíme novou šířku\n      bodyWidth = window.innerWidth;\n      // zresetuj menu\n      resetMenu();\n    }\n  });\n\n  // mobilní menu\n  function switchMenu() {\n    // označíme zda je menu zavřeno či nikoliv\n    if ($('.header__switcher').hasClass('is-open')) {\n      $('.nav, .header__switcher')\n        .removeClass('is-open')\n        .attr('aria-expanded', 'false');\n    } else {\n      $('.nav, .header__switcher')\n        .addClass('is-open')\n        .attr('aria-expanded', 'true');\n    }\n  }\n  // při změně rozlišení resetujeme menu\n  function resetMenu() {\n    $('.nav, .header__switcher')\n      .removeClass('is-open')\n      .attr('aria-expanded', 'false');\n  }\n  // spouštěč\n  $('.header__switcher').on('click', function () {\n    switchMenu();\n  });\n  // aria atributy k menu\n  if (window.innerWidth < 992) {\n    $('.nav, .header__switcher').attr('aria-expanded', 'false');\n  }\n\n  // submenu\n  $('.nav__main.has-submenu > a').on('click', function (event) {\n    event.preventDefault();\n    $(this).parent().toggleClass('is-open');\n  });\n\n  // vyhledávání\n  $('.header__search').on('click', function () {\n    $('.search').toggleClass('is-open');\n  });\n\n  // vyhledávání\n  if ($('.js-filter-switch').length) {\n    $('.js-filter-switch').on('click', function () {\n      var switchNew = $(this).data('switch');\n      var switchOld = $(this).find('em').html();\n      $(this).find('em').text(switchNew);\n      $(this).data('switch', switchOld);\n      $('.sidebar--filter').toggleClass('is-open');\n    });\n  }\n\n  // hláška s možností zavření\n  if ($('.alert').length) {\n    $('.alert .icon--delete').on('click', function () {\n      $(this).closest('.alert').hide();\n    });\n  }\n\n  // hover na login\n  if ($('.login').length && window.innerWidth > 991) {\n    $('.login > a > .icon').on('click', function (event) {\n      event.preventDefault();\n      $('.login').toggleClass('is-open');\n    });\n  }\n\n  // zavření loginu\n  if ($('.login__close').length) {\n    $('.login__close').on('click', function (event) {\n      event.preventDefault();\n      $('.login').removeClass('is-open');\n    });\n  }\n\n  // vytisknout\n  if ($('.js-print').length) {\n    $('.js-print').on('click', function (event) {\n      event.preventDefault();\n      window.print();\n    });\n  }\n\n  // menu v patičce\n  if ($('.nav-footer').length && window.innerWidth < 992) {\n    $('.nav-footer h2').on('click', function (event) {\n      event.preventDefault();\n      $(this).next('ul').toggleClass('is-open');\n      $(this).toggleClass('is-open');\n    });\n  }\n\n  // kontrola domény a přesměrování\n  if (\n    window.location.href.indexOf('goldfitness.cz') === -1 &&\n    window.location.href.indexOf('.local') === -1 &&\n    window.location.href.indexOf('localhost') === -1\n  ) {\n    window.location.href = 'https://www.goldfitness.cz';\n  }\n\n  // hlavní slider\n  if ($('.splide--main').length && window.innerWidth > 991) {\n    var splide = new Splide('.splide--main', {\n      type: 'fade',\n      autoplay: true,\n      rewind: true,\n      interval: 4000,\n    });\n    splide.on('pagination:mounted', function (data) {\n      data.items.forEach(function (item) {\n        item.button.textContent = String(item.page + 1);\n      });\n    });\n    splide.mount();\n  }\n\n  // slider - produkty\n  if ($('.splide--products').length) {\n    new Splide('.splide--products', {\n      perPage: 4,\n      perMove: 1,\n      pagination: false,\n      breakpoints: {\n        640: {\n          perPage: 1,\n        },\n        1200: {\n          perPage: 2,\n        },\n        1600: {\n          perPage: 3,\n        },\n      },\n    }).mount();\n  }\n\n  // vlastní slider\n  if ($('#range-slider').length) {\n    // hodnoty z data atributů\n    var sliderMin = $('#range-slider').data('min');\n    var sliderMax = $('#range-slider').data('max');\n    // aktuální pozice\n    var sliderMinSelected = $('#range-slider').data('mins');\n    var sliderMaxSelected = $('#range-slider').data('maxs');\n    // ošetření min/max pozice\n    if (!sliderMinSelected) {\n      sliderMinSelected = sliderMin;\n    }\n    if (!sliderMaxSelected) {\n      sliderMaxSelected = sliderMax;\n    }\n\n    var slider = document.getElementById('range-slider');\n\n    noUiSlider.create(slider, {\n      start: [sliderMinSelected, sliderMaxSelected],\n      connect: true,\n      step: 1,\n      range: {\n        min: sliderMin,\n        max: sliderMax,\n      },\n    });\n\n    var dateValues = [\n      document.getElementById('slider-min'),\n      document.getElementById('slider-max'),\n    ];\n\n    // aktualizace hodnot v inputech\n    slider.noUiSlider.on('update', function (values, handle) {\n      dateValues[handle].value = Math.ceil(values[handle]);\n    });\n\n    // odeslání formuláře\n    slider.noUiSlider.on('set', function (values, handle) {\n      $('#slider-min').trigger('change');\n    });\n  }\n});\n\n//# sourceMappingURL=scripts.js.map\n"], "names": ["switchMenu", "$", "hasClass", "removeClass", "attr", "addClass", "resetMenu", "now", "actualHour", "actualDay", "st", "ot", "E", "fn", "simpleLightbox", "N", "P", "o", "hash", "substring", "p", "t", "V", "e", "href", "split", "s", "history", "f", "title", "replace", "a", "n", "apply", "this", "arguments", "setTimeout", "trigger", "Event", "disableScroll", "u", "T", "htmlClass", "b", "appendTo", "_", "overlay", "l", "m", "index", "K", "hide", "sourceAttr", "d", "indexOf", "push", "html", "C", "fadeIn", "g", "show", "J", "text", "c", "tt", "preloading", "w", "animationSpeed", "Z", "z", "data", "extend", "spinner", "nav", "navText", "captions", "caption<PERSON><PERSON><PERSON>", "captionSelector", "captionType", "captionsData", "captionPosition", "captionClass", "close", "closeText", "swipeClose", "showCounter", "fileExt", "animationSlide", "enableKeyboard", "loop", "rel", "docClose", "swipeTolerance", "className", "widthRatio", "heightRatio", "scaleImageToRatio", "disableRightClick", "alertError", "alertErrorMessage", "additionalHtml", "throttleInterval", "doubleTapZoom", "max<PERSON><PERSON>", "h", "H", "L", "navigator", "pointer<PERSON><PERSON>bled", "msPointer<PERSON><PERSON><PERSON>", "j", "i", "body", "documentElement", "style", "WebkitTransition", "MozTransition", "OTransition", "transition", "U", "filter", "get", "tagName", "B", "location", "Q", "G", "r", "v", "x", "length", "Image", "innerWidth", "innerHeight", "src", "at", "on", "eq", "it", "alert", "nt", "onload", "width", "height", "css", "top", "left", "find", "prop", "opacity", "et", "parseInt", "animate", "y", "delay", "preventDefault", "I", "q", "D", "k", "X", "Y", "A", "O", "R", "W", "F", "target", "type", "originalEvent", "clientX", "clientY", "position", "parseFloat", "touches", "fadeOut", "Math", "sqrt", "pageX", "pageY", "M", "abs", "S", "remove", "pushState", "pathname", "search", "clearTimeout", "off", "getBoundingClientRect", "right", "clientWidth", "createElement", "append", "offsetWidth", "<PERSON><PERSON><PERSON><PERSON>", "padding-right", "match", "toUpperCase", "RegExp", "test", "closest", "keyCode", "open", "next", "prev", "destroy", "refresh", "j<PERSON><PERSON><PERSON>", "window", "document", "quantity", "parent", "val", "isNumeric", "change", "ready", "event", "modalName", "stopPropagation", "Date", "getHours", "getDay", "each", "hourStart", "hourEnd", "reveal<PERSON>ame", "toggleClass", "is", "sliderMin", "sliderMax", "sliderMinSelected", "sliderMaxSelected", "slider", "dateV<PERSON><PERSON>", "bodyWidth", "switchNew", "switchOld", "print", "splide", "Splide", "autoplay", "rewind", "interval", "items", "for<PERSON>ach", "item", "button", "textContent", "String", "page", "mount", "perPage", "perMove", "pagination", "breakpoints", "640", "1200", "1600", "getElementById", "noUiSlider", "create", "start", "connect", "step", "range", "min", "max", "values", "handle", "value", "ceil"], "mappings": "AA0EA,SAASA,aAEHC,EAAE,gBAAgB,EAAEC,SAAS,SAAS,EACxCD,EAAE,sBAAsB,EACrBE,YAAY,SAAS,EACrBC,KAAK,gBAAiB,OAAO,EAEhCH,EAAE,sBAAsB,EAAEI,SAAS,SAAS,EAAED,KAAK,gBAAiB,MAAM,CAE9E,CAGA,SAASE,YACPL,EAAE,sBAAsB,EACrBE,YAAY,SAAS,EACrBC,KAAK,gBAAiB,OAAO,CAClC,CAaA,IAEMG,IACAC,WAEAC,UAtGN,CAAC,SAASC,GAAGC,GAAGC,GAAG,aAAaF,GAAGG,GAAGC,eAAe,SAASC,GAAstB,SAASC,IAAI,OAAOC,EAAEC,KAAKC,UAAU,CAAC,CAAC,CAAC,SAASC,IAAIJ,EAAE,EAAE,IAAIK,EAAE,QAAQC,EAAE,GAAGC,EAAEN,EAAEO,KAAKC,MAAM,GAAG,EAAE,GAAG,IAAIJ,EAAEK,EAAEC,QAAQC,EAAE,eAAe,aAAa,GAAGhB,EAAEiB,MAAMN,CAAC,EAAEK,EAAEX,EAAEa,QAAQP,CAAC,EAAEN,EAAEC,KAAKG,EAAEO,EAAE,CAAA,CAAE,CAAC,SAASP,EAAEE,EAAEQ,GAAG,IAAIC,EAAE,OAAO,WAA2BA,IAAIT,EAAEU,MAAMC,KAAtBC,SAA4B,EAAEH,EAAE,CAAA,EAAGI,WAAW,WAAW,OAAOJ,EAAE,CAAA,CAAE,EAAED,CAAC,EAAE,CAAC,CAAC,SAASA,EAAEV,GAAGA,EAAEgB,QAAQ3B,GAAG4B,MAAM,qBAAqB,CAAC,EAAEvB,EAAEwB,gBAAgBC,EAAEC,EAAE,MAAM,GAAG1B,EAAE2B,WAAW,IAAI3B,EAAE2B,WAAWhC,GAAG,MAAM,EAAEL,SAASU,EAAE2B,SAAS,EAAEC,EAAEC,SAAS,MAAM,EAAEC,EAAED,SAASD,CAAC,EAAE5B,EAAE+B,SAASC,EAAEH,SAASlC,GAAG,MAAM,CAAC,EAAEsC,EAAE,CAAA,EAAG1B,EAAErB,EAAEgD,MAAM5B,CAAC,EAAE6B,EAAExC,GAAG,QAAQ,EAAEyC,KAAK,EAAE/C,KAAK,MAAMiB,EAAEjB,KAAKW,EAAEqC,UAAU,CAAC,EAAEhD,KAAK,aAAa,CAAC,EAAEA,KAAK,mBAAmB,CAAC,EAAEA,KAAK,mBAAmB,CAAC,EAAE,CAAC,GAAGiD,EAAEC,QAAQjC,EAAEjB,KAAKW,EAAEqC,UAAU,CAAC,GAAGC,EAAEE,KAAKlC,EAAEjB,KAAKW,EAAEqC,UAAU,CAAC,EAAEP,EAAEW,KAAK,EAAE,EAAEpD,KAAK,QAAQ,EAAE,EAAE8C,EAAEN,SAASC,CAAC,EAAEY,EAAE,EAAEV,EAAEW,OAAO,MAAM,EAAEhD,GAAG,WAAW,EAAEgD,OAAO,MAAM,EAAEC,EAAEC,KAAK,EAAEC,EAAEH,OAAO,MAAM,EAAEhD,GAAG,qCAAqC,EAAEoD,KAAKxC,EAAE,CAAC,EAAEyC,EAAEL,OAAO,MAAM,EAAEM,EAAG,EAAEjD,EAAEkD,YAAYC,EAAE,EAAE9B,WAAW,WAAWf,EAAEgB,QAAQ3B,GAAG4B,MAAM,sBAAsB,CAAC,CAAC,EAAEvB,EAAEoD,cAAc,CAAC,CAAC,SAASC,EAAE/C,EAAEE,EAAEQ,GAAG,OAAOV,EAAEE,EAAEA,EAAEQ,EAAEV,EAAEU,EAAEV,CAAC,CAAC,SAASgD,EAAEhD,EAAEE,EAAEQ,GAAGmB,EAAEoB,KAAK,QAAQjD,CAAC,EAAE6B,EAAEoB,KAAK,cAAc/C,CAAC,EAAE2B,EAAEoB,KAAK,cAAcvC,CAAC,CAAC,CAA31DhB,EAAEL,GAAG6D,OAAO,CAACnB,WAAW,OAAON,QAAQ,CAAA,EAAG0B,QAAQ,CAAA,EAAGC,IAAI,CAAA,EAAGC,QAAQ,CAAC,WAAW,YAAYC,SAAS,CAAA,EAAGC,aAAa,EAAEC,gBAAgB,MAAMC,YAAY,OAAOC,aAAa,QAAQC,gBAAgB,SAASC,aAAa,GAAGC,MAAM,CAAA,EAAGC,UAAU,UAAUC,WAAW,CAAA,EAAGC,YAAY,CAAA,EAAGC,QAAQ,wBAAwBC,eAAe,CAAA,EAAGpB,eAAe,IAAIF,WAAW,CAAA,EAAGuB,eAAe,CAAA,EAAGC,KAAK,CAAA,EAAGC,IAAI,CAAA,EAAGC,SAAS,CAAA,EAAGC,eAAe,GAAGC,UAAU,kBAAkBC,WAAW,GAAGC,YAAY,GAAGC,kBAAkB,CAAA,EAAGC,kBAAkB,CAAA,EAAG1D,cAAc,CAAA,EAAG2D,WAAW,CAAA,EAAGC,kBAAkB,6CAA6CC,eAAe,CAAA,EAAGzE,QAAQ,CAAA,EAAG0E,iBAAiB,EAAEC,cAAc,EAAEC,QAAQ,GAAG7D,UAAU,cAAc,EAAE3B,CAAC,EAA2oC,IAAIyF,EAAEjF,EAAEkF,EAAE,iBAAiB9F,GAAG+F,GAAG/F,GAAGgG,UAAUC,gBAAgBjG,GAAGgG,UAAUE,iBAAiB,GAAGC,EAAE,EAAE5D,EAAExC,GAAG,EAAEqG,EAAE,WAAW,IAAI1F,EAA4B,MAAM,MAAMA,GAAtCT,EAAEoG,MAAMpG,EAAEqG,iBAAgCC,OAAOC,iBAAiB,WAAW,KAAK9F,EAAE+F,cAAc,QAAQ,KAAK/F,EAAEgG,YAAY,MAAM,KAAKhG,EAAEiG,YAAY,EAAE,EAAEC,EAAE,CAAA,EAAGlE,EAAE,GAAGpD,EAAEc,EAAE2E,KAAK,CAAA,IAAK3E,EAAE2E,KAAKnE,EAAER,EAAE2E,IAAIhF,GAAGwB,IAAI,EAAEsF,OAAO,WAAW,OAAO9G,GAAGwB,IAAI,EAAE9B,KAAK,KAAK,IAAImB,CAAC,CAAC,GAAGW,KAAKF,EAAE/B,EAAEwH,IAAI,EAAE,GAAGC,QAAQlF,GAAGuE,EAAEA,EAAE,EAAE,GAAGY,EAAE,CAAA,IAAKZ,EAAErF,EAAE,cAAcC,QAAQC,EAAE,CAAA,EAAGX,EAAEN,GAAGiH,SAASC,GAAE7G,EAAE,EAAE8G,EAAE,WAAW/E,EAAErC,GAAG,OAAO,EAAEL,SAAS,YAAY,EAAE0H,EAAErH,GAAG,UAAU,EAAEL,SAAS,UAAU,EAAEmD,KAAKzC,EAAEoE,SAAS,EAAExB,EAAEjD,GAAG,OAAO,EAAEL,SAAS,YAAY,EAAEmD,KAAK,aAAa,EAAEK,EAAEnD,GAAG,OAAO,EAAEL,SAAS,eAAe,EAAEmD,KAAK,2BAA2BzC,EAAE2D,QAAQ,GAAG,oCAAoC3D,EAAE2D,QAAQ,GAAG,WAAW,EAAEX,EAAErD,GAAG,OAAO,EAAEL,SAAS,YAAY,EAAEmD,KAAK,iEAAiE,EAAER,EAAE,CAAA,EAAG1B,EAAE,EAAE0G,EAAE,EAAEC,EAAEvH,GAAG,OAAO,EAAEL,SAAS,cAAcU,EAAEkE,aAAa,QAAQlE,EAAEiE,eAAe,EAAEnC,EAAEnC,GAAG,OAAO,EAAEL,SAAS,UAAU,EAAEsC,EAAEjC,GAAG,OAAO,EAAEL,SAAS,YAAY,EAAEA,SAASU,EAAE8E,SAAS,EAAE7B,EAAG,SAAS/C,GAAG,IAAiB8B,EAAYgF,EAA6BhE,EAAvDb,EAAEgF,SAAYnF,EAAE,IAAIoF,MAAMJ,EAAEpH,GAAGyH,WAAWrH,EAAE+E,WAAW/B,EAAEpD,GAAG0H,YAAYtH,EAAEgF,YAAYhD,EAAEuF,IAAIpF,EAAE9C,KAAK,KAAK,EAAE8C,EAAEoB,KAAK,QAAQ,CAAC,EAAEpB,EAAEoB,KAAK,cAAc,CAAC,EAAEpB,EAAEoB,KAAK,cAAc,CAAC,EAAEiE,EAAG,EAAE,EAAE,CAAC,EAAE7H,GAAGqC,CAAC,EAAEyF,GAAG,QAAQ,SAASnH,GAAGpB,EAAEwI,GAAGnH,CAAC,EAAEe,QAAQ3B,GAAG4B,MAAM,sBAAsB,CAAC,EAAEiF,EAAE,EAAEvE,EAAE,CAAA,GAAIW,EAAER,KAAK,EAAE,IAAI5B,EAAE,GAAGN,GAAG,CAAC,GAAGA,EAAE+G,IAAI1G,GAAGC,EAAEmH,EAAG,GAAG3H,EAAEmF,YAAYyC,MAAM5H,EAAEoF,iBAAiB,EAAEyC,EAAGrH,EAAEN,EAAE,CAAC,EAAE,CAAC,EAAE8B,EAAE8F,OAAO,WAAW,KAAA,IAAS5H,GAAGhB,EAAEwI,GAAGnH,CAAC,EAAEe,QAAQ3B,GAAG4B,MAAM,wBAAwB,CAAC,EAAED,QAAQ3B,GAAG4B,OAAO,IAAIrB,EAAE,WAAW,YAAY,iBAAiB,CAAC,EAAEF,EAAEY,UAAUC,EAAE4E,EAAEpE,WAAWhB,EAAE,GAAG,EAAEA,EAAE,GAAG,CAAC,GAAGiC,EAAEC,QAAQJ,EAAE9C,KAAK,KAAK,CAAC,GAAGiD,EAAEE,KAAKL,EAAE9C,KAAK,KAAK,CAAC,EAAE,IAAIiB,EAAE0B,EAAE+F,MAAMvH,EAAEwB,EAAEgG,QAAUhI,EAAEiF,mBAAmB+B,EAAE1G,GAAG0C,EAAExC,KAAyBF,GAAlBU,EAAEgG,EAAEhE,EAAE1C,EAAEE,EAAEF,EAAE0G,EAAExG,EAAEwC,EAAOxC,GAAGQ,GAAErB,GAAG,WAAW,EAAEsI,IAAI,CAACC,KAAKtI,GAAG0H,YAAY9G,GAAG,EAAE,KAAK2H,MAAMvI,GAAGyH,WAAW/G,EAAEmB,GAAG,EAAE,KAAKsG,MAAMzH,EAAE,KAAK0H,OAAOxH,EAAE,IAAI,CAAC,EAAEoC,EAAER,KAAK,EAAED,EAAEQ,OAAO,MAAM,EAAE6D,EAAE,CAAA,EAAG,IAAMR,EAAE,QAAQhG,EAAE8D,gBAAgB5E,EAAEwI,GAAGnH,CAAC,EAAErB,EAAEwI,GAAGnH,CAAC,EAAE6H,KAAKpI,EAAE8D,eAAe,EAAK7C,EAAE,QAAQjB,EAAE+D,YAAYiC,EAAEzC,KAAKvD,EAAEgE,YAAY,EAAE,QAAQhE,EAAE+D,YAAYiC,EAAEvD,KAAK,EAAEuD,EAAEqC,KAAKrI,EAAEgE,YAAY,EAAEhE,EAAE0E,OAAO,IAAInE,GAAGZ,GAAG,UAAU,EAAEyC,KAAK,EAAE7B,GAAGrB,EAAEiI,OAAO,GAAGxH,GAAG,UAAU,EAAEyC,KAAK,EAAE,EAAE7B,GAAGZ,GAAG,UAAU,EAAEkD,KAAK,EAAEtC,EAAErB,EAAEiI,OAAO,GAAGxH,GAAG,UAAU,EAAEkD,KAAK,GAAG,GAAG3D,EAAEiI,QAAQxH,GAAG,oBAAoB,EAAEyC,KAAK,EAAE,GAAGlC,GAAG,CAAC,GAAGA,GAAOS,EAAE,CAAC2H,QAAQ,CAAC,EAAEtI,EAAEwE,iBAAiBoC,GAAG2B,EAAG,EAAE,IAAIrI,EAAE,IAAI,EAAEmB,WAAW,WAAWkH,EAAGvI,EAAEoD,eAAe,IAAI,KAAK,CAAC,EAAE,EAAE,GAAGzC,EAAEwH,KAAKK,SAAS7I,GAAG,WAAW,EAAEsI,IAAI,MAAM,CAAC,EAAE,IAAI/H,EAAE,MAAMP,GAAG,WAAW,EAAE8I,QAAQ9H,EAAEX,EAAEoD,eAAe,WAAWnB,EAAE,CAAA,EAAGyG,EAAEzH,EAAEX,CAAC,CAAC,CAAC,IAAO2B,EAAE,CAAA,EAAGyG,EAAEzH,EAAEX,CAAC,GAAEN,EAAEqF,gBAAgB,IAAI1F,GAAG,qBAAqB,EAAEwH,QAAQxH,GAAG,OAAO,EAAE8C,KAAKzC,EAAEqF,cAAc,EAAE/F,SAAS,oBAAoB,EAAEuC,SAASlC,GAAG,WAAW,CAAC,CAAC,EAAE,EAAE+I,EAAE,SAASpI,EAAEE,GAAG,KAAKF,GAAG,KAAA,IAASA,GAAGN,EAAE4D,UAAUsD,EAAEzE,KAAKnC,CAAC,EAAE2H,IAAI,CAACF,MAAMvH,EAAE,IAAI,CAAC,EAAE4B,KAAK,EAAEP,SAASlC,GAAG,WAAW,CAAC,EAAEgJ,MAAM3I,EAAE6D,YAAY,EAAElB,OAAO,MAAM,CAAC,EAAE4F,EAAG,SAASjI,EAAEE,GAAG,IAAIQ,EAAE,GAAGA,EAAEgF,EAAE,aAAa,cAAcxF,EAAE,IAAIQ,EAAEgF,EAAE,cAAcA,EAAE,aAAa1F,EAAE,WAAWX,GAAG,WAAW,EAAEsI,IAAIjH,CAAC,CAAC,EAAEwG,EAAG,SAASlH,EAAEE,EAAEQ,GAAG,IAAIC,EAAE,GAAGA,EAAE+E,EAAE,aAAa,aAAa1F,EAAE,IAAIE,EAAE,WAAWQ,EAAE,IAAImB,EAAE8F,IAAIhH,CAAC,CAAC,EAAEyB,EAAE,WAAW/C,GAAGC,EAAE,EAAE6H,GAAG,UAAUV,EAAE9D,CAAE,EAAEtD,GAAG,aAAa,EAAE8H,GAAG,SAASV,EAAE,eAAeA,EAAE,YAAY,SAASzG,GAAGA,EAAEsI,eAAe,EAAEpC,GAAGmB,EAAG,CAAC,CAAC,EAAE3H,EAAEY,SAASS,WAAW,WAAW1B,GAAGC,EAAE,EAAE6H,GAAG,cAAcV,EAAE,WAAWP,GAAGvG,EAAE,IAAI6G,IAAGa,EAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE7E,EAAE2E,GAAG,SAASV,EAAE,SAASzG,EAAE,SAASA,GAAGA,EAAEsI,eAAe,EAAEjD,EAAE,EAAEkC,EAAGlI,GAAGwB,IAAI,EAAEhC,SAAS,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,EAAEa,EAAEsF,gBAAgB,CAAC,EAAE,IAAI9E,EAAEQ,EAAEC,EAAE+E,EAAErF,EAAET,EAAE8B,EAAEgF,EAAEhE,EAAE3C,EAAEoF,EAAEnD,EAAEb,EAAEZ,EAAE+B,EAAMsE,EAAEtF,EAAE8G,EAAEhG,EAAES,EAAEzB,EAAE7B,EAAMgJ,EAAMC,EAAQC,EAAZC,EAAE,EAAMC,EAAE,EAAMC,EAAE,CAAA,EAAGC,EAAE,CAAA,EAAGC,EAAE,EAAEC,EAAE,CAAA,EAAGC,EAAEjG,EAAE,EAAE,EAAErD,EAAEwF,OAAO,EAAE+D,EAAE,CAAA,EAAGzH,EAAE2F,GAAG,cAAcV,EAAE,cAAcA,EAAE,SAASzG,GAAG,GAAG,MAAMA,EAAEkJ,OAAO7C,SAAS,cAAcrG,EAAEmJ,KAAK,MAAM,CAAA,EAAG,GAAG,cAAcnJ,EAAEA,EAAEoJ,eAAeD,KAAKzG,EAAE1C,EAAEqJ,QAAQtJ,EAAEC,EAAEsJ,QAAQpJ,EAAEsB,EAAEkG,OAAO,EAAEhH,EAAEc,EAAEiG,MAAM,EAAEpH,EAAEwB,EAAE6F,OAAO,EAAE9H,EAAEiC,EAAE4F,MAAM,EAAE9G,EAAEa,EAAE+H,SAAS,EAAE1B,KAAKnC,EAAElE,EAAE+H,SAAS,EAAE3B,IAAIlG,EAAE8H,WAAW3H,EAAEoB,KAAK,aAAa,CAAC,EAAEyD,EAAE8C,WAAW3H,EAAEoB,KAAK,aAAa,CAAC,OAAY,GAAGsF,EAAEvI,EAAEyJ,QAAQ5C,OAAOnE,EAAE1C,EAAEyJ,QAAQ,GAAGJ,QAAQtJ,EAAEC,EAAEyJ,QAAQ,GAAGH,QAAQpJ,EAAEsB,EAAEkG,OAAO,EAAEhH,EAAEc,EAAEiG,MAAM,EAAEpH,EAAEwB,EAAE6F,OAAO,EAAE9H,EAAEiC,EAAE4F,MAAM,EAAE9G,EAAEa,EAAE+H,SAAS,EAAE1B,KAAKnC,EAAElE,EAAE+H,SAAS,EAAE3B,IAAI,IAAIW,EAAE,CAAC,GAAGU,EAAE,OAAOpH,EAAE7C,SAAS,eAAe,EAAE4J,EAAEA,GAAG5F,EAAE,EAAE,EAAEgG,EAAE,CAAC,EAAE9B,EAAG,MAAM,MAAM8B,CAAC,EAAE,CAAA,IAAuBhG,EAAE,EAAE,EAAtBgG,EAAEtJ,EAAEuF,aAAqB,EAAEiC,EAAG,MAAM,MAAM8B,CAAC,EAAE3J,GAAG,aAAa,EAAEqK,QAAQ,GAAG,EAAE,CAAA,GAAI3I,WAAW,WAAWc,EAAE/C,YAAY,eAAe,CAAC,EAAE,GAAG,EAAE,CAAA,EAAGmK,EAAE,CAAA,EAAGlI,WAAW,WAAWkI,EAAE,CAAA,CAAE,EAAE,GAAG,EAAEvH,EAAE8H,WAAW3H,EAAEoB,KAAK,aAAa,CAAC,EAAEyD,EAAE8C,WAAW3H,EAAEoB,KAAK,aAAa,CAAC,CAAC,MAAM,IAAIsF,IAAIpD,EAAEnF,EAAEyJ,QAAQ,GAAGJ,QAAQrH,EAAEhC,EAAEyJ,QAAQ,GAAGH,QAAQ5H,EAAE8H,WAAW3H,EAAEoB,KAAK,aAAa,CAAC,EAAEyD,EAAE8C,WAAW3H,EAAEoB,KAAK,aAAa,CAAC,EAAEb,GAAGM,EAAEyC,GAAG,EAAEtC,GAAG9C,EAAEiC,GAAG,EAAEb,EAAEwI,KAAKC,MAAMlH,EAAEyC,IAAIzC,EAAEyC,IAAIpF,EAAEiC,IAAIjC,EAAEiC,EAAE,GAAQ,OAAL+G,EAAE,CAAA,EAAS,CAAC,CAACF,IAAIvC,IAAIwC,EAAEZ,SAAS1G,EAAEmG,IAAI,MAAM,CAAC,GAAGkB,EAAE,CAAA,EAAGpD,EAAEJ,EAAE,EAAEqD,EAAE1I,EAAE6J,OAAO7J,EAAEyJ,QAAQ,GAAGI,MAAMlB,EAAE3I,EAAE8J,OAAO9J,EAAEyJ,QAAQ,GAAGK,MAAM,CAAA,EAAG,CAAC,EAAE3C,GAAG,aAAaV,EAAE,cAAcA,EAAE,iBAAiB,SAASzG,GAAG,GAAG,CAAC6I,EAAE,MAAM,CAAA,EAAG,GAAG7I,EAAEsI,eAAe,EAAE,cAActI,EAAEA,EAAEoJ,eAAeD,KAAK,CAAC,GAAG,CAAA,IAAKJ,EAAE,MAAM,CAAA,EAAGxI,EAAEP,EAAEyJ,QAAQ,GAAGJ,QAAQ/G,EAAEtC,EAAEyJ,QAAQ,GAAGH,QAA6B,GAArBf,EAAEvI,EAAEyJ,QAAQ5C,SAAclF,EAAE3B,EAAEyJ,QAAQ,GAAGJ,QAAQ1C,EAAE3G,EAAEyJ,QAAQ,GAAGH,QAAQS,EAAEJ,KAAKC,MAAMrJ,EAAEoB,IAAIpB,EAAEoB,IAAIW,EAAEqE,IAAIrE,EAAEqE,EAAE,EAAE,OAAOxF,IAAIA,EAAE4I,GAAG,GAAGJ,KAAKK,IAAI7I,EAAE4I,CAAC,IAAI3B,EAAErF,EAAEgH,EAAE5I,EAAE6H,EAAE,EAAEtJ,EAAEwF,OAAO,EAAE9D,GAAGxB,EAAEwI,EAAE1H,GAAG,EAAEnB,GAAGc,EAAE+H,EAAElI,GAAG,EAAE+J,EAAE7B,EAAEY,EAAEpC,EAAEhH,EAAEwI,GAAG1H,EAAE,EAAEqC,EAAErB,GAAGU,EAAEzB,EAAED,EAAE,EAAEgB,IAAI0G,EAAE6B,GAAGA,EAAE,CAAC,EAAE7I,EAAEA,CAAC,EAAEE,EAAEjB,EAAE+H,GAAGlI,EAAE,EAAE6C,EAAE2D,GAAG7D,EAAE6C,EAAExF,EAAE,EAAEwG,IAAI0B,EAAE6B,GAAGA,EAAE,CAAC,EAAE1K,EAAEA,CAAC,EAAE2H,EAAGN,EAAE,KAAKtF,EAAE,KAAK8G,CAAC,EAAE,EAAEA,IAAIQ,EAAE,CAAA,EAAGvJ,GAAG,aAAa,EAAEqK,QAAQ,GAAG,GAAGvI,EAAE4I,EAAEf,EAAEZ,EAAE1G,EAAEkF,EAAEF,EAAEpF,KAAKF,GAAGxB,GAAGwI,EAAEY,GAAGtI,GAAG,EAAEnB,GAAGc,EAAE+H,EAAElI,GAAG,EAAE0G,EAAEhH,EAAEwI,GAAG1H,EAAE,EAAEqC,EAAExC,GAAGmC,EAAEhB,GAAG,CAAC,EAAEN,EAAEA,CAAC,EAAEE,EAAEjB,EAAE+H,GAAGlI,EAAE,EAAE6C,EAAET,GAAGvC,EAAE2G,GAAG,CAAC,EAAEnH,EAAEA,CAAC,EAAEoK,KAAKK,IAAIpD,CAAC,IAAI+C,KAAKK,IAAI5I,CAAC,IAAIM,EAAEkF,EAAElE,EAAEnC,GAAGoJ,KAAKK,IAAI1I,CAAC,IAAIqI,KAAKK,IAAIzK,CAAC,IAAImH,EAAEpF,EAAEvB,EAAEuC,GAAGU,EAAEgG,EAAEpC,EAAEtF,CAAC,EAAE4F,EAAGN,EAAE,KAAKtF,EAAE,KAAK8G,CAAC,EAAE,CAAC,GAAG,aAAapI,EAAEmJ,MAAMN,EAAE,CAAC,GAAG,aAAa7I,EAAEmJ,KAAK,MAAM,CAAA,EAAG,GAAG,CAAA,IAAKJ,EAAE,MAAM,CAAA,EAAGxI,EAAEP,EAAEqJ,QAAQ/G,EAAEtC,EAAEsJ,QAAQlI,GAAGxB,GAAGwI,EAAEY,GAAGtI,GAAG,EAAEnB,GAAGc,EAAE+H,EAAElI,GAAG,EAAE0G,EAAEhH,EAAEwI,GAAG1H,EAAE,EAAEqC,EAAExC,GAAGmC,EAAEhB,GAAG,CAAC,EAAEN,EAAEA,CAAC,EAAEE,EAAEjB,EAAE+H,GAAGlI,EAAE,EAAE6C,EAAET,GAAGvC,EAAE2G,GAAG,CAAC,EAAEnH,EAAEA,CAAC,EAAEoK,KAAKK,IAAIpD,CAAC,IAAI+C,KAAKK,IAAI5I,CAAC,IAAIM,EAAEkF,EAAElE,EAAEnC,GAAGoJ,KAAKK,IAAI1I,CAAC,IAAIqI,KAAKK,IAAIzK,CAAC,IAAImH,EAAEpF,EAAEvB,EAAEuC,GAAGU,EAAEgG,EAAEpC,EAAEtF,CAAC,EAAE4F,EAAGN,EAAE,KAAKtF,EAAE,KAAK8G,CAAC,CAAC,CAACQ,IAAIJ,EAAExI,EAAE6J,OAAO7J,EAAEyJ,QAAQ,GAAGI,MAAMpB,EAAEzI,EAAE8J,OAAO9J,EAAEyJ,QAAQ,GAAGK,MAAMzE,EAAEqD,EAAEF,EAAE/C,EAAEkD,EAAEF,EAAE/I,EAAEwE,iBAAiBoC,EAAE2B,EAAG,EAAE,CAAC5C,EAAE,IAAI,EAAE7D,EAAEmG,IAAI,OAAOmB,EAAEzD,EAAE,IAAI,GAAG,CAAC,EAAE8B,GAAG,YAAYV,EAAE,YAAYA,EAAE,gBAAgBA,EAAE,eAAeA,EAAE,uDAAuD,SAASzG,GAAMA,EAAEA,EAAEoJ,cAAchE,GAAG,YAAYpF,EAAEmJ,OAAO,KAAKZ,EAAEvI,EAAEyJ,QAAQ5C,SAAS7D,EAAEgG,EAAEpC,EAAEtF,CAAC,EAAE,GAAG0H,IAAIJ,EAAE,CAAA,EAAGvJ,GAAG,aAAa,EAAEgD,OAAO,GAAG,GAAGlB,EAAE,KAAK4H,EAAE,CAAA,GAAI,IAAIR,GAAG7F,EAAE1C,EAAEyJ,QAAQ,GAAGJ,QAAQtJ,EAAEC,EAAEyJ,QAAQ,GAAGH,SAAS,EAAEf,IAAIpH,EAAE,OAAO0H,IAAO3I,EAAE,EAAE2I,EAAE,CAAA,GAAInJ,EAAE0E,OAAO,IAAInE,GAAGoF,EAAE,IAAInF,EAAE,CAAA,GAAID,GAAGrB,EAAEiI,OAAO,GAAG,EAAExB,IAAInF,EAAE,CAAA,IAAKyJ,KAAKK,IAAI3E,CAAC,EAAE3F,EAAE6E,gBAAgBrE,EAAEqH,EAAG,EAAElC,EAAE,EAAE,CAAC,CAAC,EAAE3F,EAAEwE,iBAAiBoC,EAAE2B,EAAGvI,EAAEoD,eAAe,IAAI,KAAK,EAAEtB,EAAE2G,QAAQ,CAACN,KAAKiB,EAAE,IAAI,EAAEpJ,EAAEoD,eAAe,CAAC,GAAGpD,EAAEqE,aAAY,GAAG4F,KAAKK,IAAIvE,CAAC,GAAGkE,KAAKK,IAAI3E,CAAC,EAAE3F,EAAE6E,gBAAgB8C,EAAG,CAAE,CAAC,EAAEF,GAAG,WAAW,SAASnH,GAAG,OAAO0C,EAAE1C,EAAEqJ,QAAQtJ,EAAEC,EAAEsJ,QAAQpJ,EAAEsB,EAAEkG,OAAO,EAAEhH,EAAEc,EAAEiG,MAAM,EAAEpH,EAAEwB,EAAE6F,OAAO,EAAE9H,EAAEiC,EAAE4F,MAAM,EAAE9G,EAAEa,EAAE+H,SAAS,EAAE1B,KAAKnC,EAAElE,EAAE+H,SAAS,EAAE3B,IAAI/F,EAAE7C,SAAS,eAAe,EAAE4J,GAAG5F,EAAE,EAAE,EAAEgG,EAAE,CAAC,EAAE9B,EAAG,MAAM,MAAM8B,CAAC,EAAEJ,EAAE,CAAA,EAAGvJ,GAAG,aAAa,EAAEgD,OAAO,GAAG,IAAsBW,EAAE,EAAE,EAAtBgG,EAAEtJ,EAAEuF,aAAqB,EAAEiC,EAAG,MAAM,MAAM8B,CAAC,EAAE3J,GAAG,aAAa,EAAEqK,QAAQ,GAAG,EAAEd,EAAE,CAAA,GAAI7H,WAAW,WAAWc,EAAE/C,YAAY,eAAe,CAAC,EAAE,GAAG,EAAE,EAAEiK,EAAE,CAAA,EAAG,CAAC,CAAC,EAAElG,EAAE,WAAW,IAAI7C,EAAEC,EAAE,EAAE,EAAErB,EAAEiI,OAAO,EAAE5G,EAAE,GAAGrB,EAAEiI,OAAO,EAAE,EAAE5G,EAAE,EAAEC,EAAED,EAAE,EAAE,EAAErB,EAAEiI,OAAO,EAAE5G,EAAE,GAAGrB,EAAEiI,OAAO,EAAE,EAAE5G,EAAE,EAAEZ,GAAG,SAAS,EAAEN,KAAK,MAAMH,EAAEwI,GAAGpH,CAAC,EAAEjB,KAAKW,EAAEqC,UAAU,CAAC,EAAEoF,GAAG,OAAO,WAAW,CAAC,GAAGnF,EAAEC,QAAQ5C,GAAGwB,IAAI,EAAE9B,KAAK,KAAK,CAAC,GAAGiD,EAAEE,KAAK7C,GAAGwB,IAAI,EAAE9B,KAAK,KAAK,CAAC,EAAEH,EAAEwI,GAAGnH,CAAC,EAAEe,QAAQ3B,GAAG4B,MAAM,gCAAgC,CAAC,CAAC,CAAC,EAAE5B,GAAG,SAAS,EAAEN,KAAK,MAAMH,EAAEwI,GAAGlH,CAAC,EAAEnB,KAAKW,EAAEqC,UAAU,CAAC,EAAEoF,GAAG,OAAO,WAAW,CAAC,GAAGnF,EAAEC,QAAQ5C,GAAGwB,IAAI,EAAE9B,KAAK,KAAK,CAAC,GAAGiD,EAAEE,KAAK7C,GAAGwB,IAAI,EAAE9B,KAAK,KAAK,CAAC,EAAEH,EAAEwI,GAAGnH,CAAC,EAAEe,QAAQ3B,GAAG4B,MAAM,gCAAgC,CAAC,CAAC,CAAC,CAAC,EAAEsG,EAAG,SAASrH,GAAGtB,EAAEwI,GAAGnH,CAAC,EAAEe,QAAQ3B,GAAG4B,MAAM,uBAAuB,CAAC,EAAED,QAAQ3B,GAAG4B,OAAO,IAAIf,EAAE,OAAO,QAAQ,iBAAiB,CAAC,EAAE,IAAIF,EAAEC,EAAEC,EAAOyB,IAAI3B,EAAE,GAAGA,GAAGpB,EAAEiI,SAAS,CAAA,IAAKnH,EAAE0E,OAAOnE,EAAED,EAAE,EAAEpB,EAAEiI,OAAO,EAAE7G,EAAEpB,EAAEiI,OAAO,EAAE,EAAE7G,EAAEX,GAAG,qCAAqC,EAAEoD,KAAKxC,EAAE,CAAC,EAAMS,EAAE,CAACsH,QAAQ,CAAC,EAAEtI,EAAEwE,iBAAiBoC,EAAE2B,EAAGvI,EAAEoD,eAAe,IAAI,CAAC,IAAI5C,EAAEmF,EAAE,IAAI,EAAE3E,EAAEmH,KAAKK,SAAS7I,GAAG,WAAW,EAAEsI,IAAI,MAAM,CAAC,EAAE,CAAC,IAAIzH,EAAE,MAAMb,GAAG,WAAW,EAAE8I,QAAQzH,EAAEhB,EAAEoD,eAAe,WAAW/B,WAAW,WAAW,IAAIf,EAAEpB,EAAEwI,GAAGnH,CAAC,EAAE4B,EAAE9C,KAAK,MAAMiB,EAAEjB,KAAKW,EAAEqC,UAAU,CAAC,EAAE,CAAC,GAAGC,EAAEC,QAAQjC,EAAEjB,KAAKW,EAAEqC,UAAU,CAAC,GAAGO,EAAEC,KAAK,EAAElD,GAAG,aAAa,EAAE6K,OAAO,EAAEvH,EAAGzC,CAAC,EAAER,EAAEkD,YAAYC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAAEwE,EAAG,WAAW,IAAWrH,EAAUE,EAAjByB,IAAO3B,EAAEpB,EAAEwI,GAAGnH,CAAC,EAAEC,EAAE,CAAA,EAAGF,EAAEgB,QAAQ3B,GAAG4B,MAAM,sBAAsB,CAAC,EAAEvB,EAAEY,UAAUD,EAAEC,QAAQ6J,UAAU,GAAG5K,EAAEiB,MAAMZ,EAAEwK,SAASxK,EAAEyK,MAAM,EAAEzK,EAAEC,KAAK,GAAGyK,aAAanF,CAAC,GAAG9F,GAAG,2FAA2F,EAAEqK,QAAQ,OAAO,WAAWhK,EAAEwB,eAAeE,EAAE,MAAM,EAAE1B,EAAE2B,WAAW,IAAI3B,EAAE2B,WAAWhC,GAAG,MAAM,EAAEP,YAAYY,EAAE2B,SAAS,EAAEhC,GAAG,0BAA0B,EAAE6K,OAAO,EAAE1H,EAAE+H,IAAI,QAAQ,QAAQ,EAAElL,GAAG,aAAa,EAAEkL,IAAI,SAAS9D,EAAE,WAAW,EAAEpH,GAAGC,EAAE,EAAEiL,IAAI,UAAU9D,CAAC,EAAEpH,GAAGC,EAAE,EAAEiL,IAAI,cAAc9D,CAAC,EAAEvG,GAAGF,EAAEgB,QAAQ3B,GAAG4B,MAAM,uBAAuB,CAAC,EAAEf,EAAE,CAAA,CAAE,CAAC,EAAE2B,EAAExC,GAAG,EAAEsC,EAAEuE,EAAE,CAAA,EAAG,EAAE9E,EAAE,SAASpB,GAAG,IAAyJ0F,EAArJxF,EAAE,EAAghB,MAA3gB,QAAQF,IAAOU,EAAEpB,GAAGyH,cAAkErG,GAA5CC,EAAEpB,EAAEqG,gBAAgB4E,sBAAsB,GAAMC,MAAMd,KAAKK,IAAIrJ,EAAEkH,IAAI,GAAKtI,EAAEoG,KAAK+E,YAAYhK,IAAOgF,EAAEnG,EAAEoL,cAAc,KAAK,EAAEtK,EAAE6H,SAAS7I,GAAG,MAAM,EAAEsI,IAAI,eAAe,EAAE,EAAE,EAAEjC,EAAElB,UAAU,uBAAuBnF,GAAG,MAAM,EAAEuL,OAAOlF,CAAC,EAAExF,EAAEwF,EAAEmF,YAAYnF,EAAEgF,YAAYrL,GAAGE,EAAEoG,IAAI,EAAE,GAAGmF,YAAYpF,CAAC,EAAErG,GAAG,MAAM,EAAE4D,KAAK,UAAU5C,CAAC,EAAE,EAAEH,IAAGb,GAAG,MAAM,EAAEL,SAAS,eAAe,EAAE2I,IAAI,CAACoD,gBAAgB1K,EAAEH,CAAC,CAAC,GAAQb,GAAG,MAAM,EAAEP,YAAY,eAAe,EAAE6I,IAAI,CAACoD,gBAAgB1L,GAAG,MAAM,EAAE4D,KAAK,SAAS,CAAC,CAAC,EAAS/C,CAAC,EAAE,OAAOR,EAAEmE,OAAO6C,EAAEnF,SAASD,CAAC,EAAE5B,EAAEsE,aAAa,EAAEpF,EAAEiI,SAASnE,EAAEnB,SAASD,CAAC,EAAEoB,EAAEoF,KAAK,WAAW,EAAErF,KAAK7D,EAAEiI,MAAM,GAAGnH,EAAE0D,KAAKZ,EAAEjB,SAASD,CAAC,EAAE5B,EAAEyD,SAASb,EAAEf,SAASD,CAAC,EAAE1C,EAAEuI,GAAG,SAASV,EAAE,SAASzG,GAAG,GAAYA,EAAyMa,KAAlMnB,CAAAA,EAAEuE,UAAqB/D,EAAEb,GAAGW,CAAC,EAAEjB,KAAKW,EAAEqC,UAAU,EAAEiJ,MAAM,yCAAyC,IAAY3L,GAAGW,CAAC,EAAE+H,KAAK,SAAS,EAAEkD,YAAY,GAAGtK,GAAG,IAAIuK,OAAO,KAAKxL,EAAEuE,QAAQ,KAAK,GAAG,EAAEkH,KAAKjL,CAAC,EAAS,CAAC,GAAGF,EAAEsI,eAAe,EAAE3G,EAAE,MAAM,CAAA,EAAOzB,EAAEb,GAAGwB,IAAI,EAAE8F,EAAE/H,EAAEgD,MAAM1B,CAAC,EAAEQ,EAAER,CAAC,CAAC,CAA1R,IAASF,EAA8BE,CAAoP,CAAC,EAAEb,GAAGE,CAAC,EAAE4H,GAAG,SAASV,EAAE,eAAeA,EAAE,SAASzG,GAAGkG,GAAGxG,EAAE4E,UAAU,IAAIjF,GAAGW,EAAEkJ,MAAM,EAAEkC,QAAQ,WAAW,EAAEvE,QAAQ,IAAIxH,GAAGW,EAAEkJ,MAAM,EAAEkC,QAAQ,gBAAgB,EAAEvE,QAAQQ,EAAG,CAAC,CAAC,EAAE3H,EAAEkF,mBAAmBvF,GAAGE,CAAC,EAAE4H,GAAG,cAAc,gBAAgB,SAASnH,GAAG,MAAM,CAAA,CAAE,CAAC,EAAEN,EAAEyE,gBAAgB9E,GAAGE,CAAC,EAAE4H,GAAG,SAASV,EAAEzG,EAAE,SAASA,GAAGqF,EAAE,EAAE,IAAInF,EAAEF,EAAEqL,QAAQ1J,GAAG,IAAIzB,IAAI2B,EAAE9C,KAAK,MAAM,EAAE,EAAE4C,EAAE,CAAA,EAAG0F,EAAG,GAAGnB,IAAIlG,EAAEsI,eAAe,EAAE,IAAIpI,GAAGmH,EAAG,EAAE,IAAInH,GAAG,IAAIF,EAAEqL,SAAS9D,EAAG,IAAIvH,EAAEqL,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE3L,EAAEsF,gBAAgB,CAAC,EAAEnE,KAAKyK,KAAK,SAAStL,GAAGA,EAAEA,GAAGX,GAAGwB,KAAK,EAAE,EAAE8F,EAAE/H,EAAEgD,MAAM5B,CAAC,EAAEU,EAAEV,CAAC,CAAC,EAAEa,KAAK0K,KAAK,WAAWhE,EAAG,CAAC,CAAC,EAAE1G,KAAK2K,KAAK,WAAWjE,EAAG,CAAC,CAAC,CAAC,EAAE1G,KAAKgD,MAAM,WAAWwD,EAAG,CAAC,EAAExG,KAAK4K,QAAQ,WAAWpM,GAAGE,CAAC,EAAEgL,IAAI,SAAS9D,CAAC,EAAE8D,IAAI,SAAS9D,CAAC,EAAEY,EAAG,EAAEhI,GAAG,0BAA0B,EAAE6K,OAAO,EAAErJ,KAAK0J,IAAI,OAAO,CAAC,EAAE1J,KAAK6K,QAAQ,WAAW7K,KAAK4K,QAAQ,EAAEpM,GAAGwB,IAAI,EAAEpB,eAAeC,CAAC,CAAC,EAAEmB,IAAI,CAAC,EAAE8K,OAAOC,OAAOC,QAAQ,EAGhtYjN,EAAEiN,QAAQ,EAAE1E,GAAG,QAAS,8BAA+B,WAErD,IAAI2E,EAAWlN,EAAEiC,IAAI,EAAEkL,OAAO,EAAEjE,KAAK,OAAO,EAAEkE,IAAI,IAE9CF,EADOtC,WAAWsC,CAAQ,IACd,GAAK,CAAClN,EAAEqN,UAAUH,CAAQ,KACxCA,EAAW,GAGbA,GAAsB,EAEtBlN,EAAEiC,IAAI,EAAEkL,OAAO,EAAEjE,KAAK,OAAO,EAAEkE,IAAIF,CAAQ,EAAEI,OAAO,CACtD,CAAC,EAGDtN,EAAEiN,QAAQ,EAAE1E,GAAG,QAAS,+BAAgC,WAEtD,IAAI2E,EAAWlN,EAAEiC,IAAI,EAAEkL,OAAO,EAAEjE,KAAK,OAAO,EAAEkE,IAAI,IAE9CF,EADOtC,WAAWsC,CAAQ,IACd,GAAK,CAAClN,EAAEqN,UAAUH,CAAQ,KACxCA,EAAW,GAGbA,GAAsB,EAEtBlN,EAAEiC,IAAI,EAAEkL,OAAO,EAAEjE,KAAK,OAAO,EAAEkE,IAAIF,CAAQ,EAAEI,OAAO,CACtD,CAAC,EAGDP,OAAOE,QAAQ,EAAEM,MAAM,SAAUvN,GAC3BA,EAAE,aAAa,EAAEiI,QACnBjI,EAAE,eAAe,EAAEa,eAAe,CAChCuE,YAAa,CAAA,CACf,CAAC,CAEL,CAAC,EAGDpF,EAAEiN,QAAQ,EAAE1E,GAAG,QAAS,YAAa,SAAUiF,GAC7CA,EAAM9D,eAAe,EAGjB+D,EAAYzN,EAAEiC,IAAI,EAAE9B,KAAK,MAAM,EAGnCH,EAAEyN,CAAS,EAAErN,SAAS,SAAS,CACjC,CAAC,EAGDJ,EAAEiN,QAAQ,EAAE1E,GAAG,QAAS,qCAAsC,SAAUiF,GACtEA,EAAM9D,eAAe,EACrB1J,EAAEiC,IAAI,EAAEuK,QAAQ,QAAQ,EAAEtM,YAAY,SAAS,CACjD,CAAC,EAGDF,EAAEiN,QAAQ,EAAE1E,GAAG,QAAS,SAAU,SAAUiF,GAC1CA,EAAM9D,eAAe,EACrB1J,EAAEiC,IAAI,EAAE/B,YAAY,SAAS,CAC/B,CAAC,EAGDF,EAAEiN,QAAQ,EAAE1E,GAAG,QAAS,eAAgB,SAAUiF,GAChDA,EAAME,gBAAgB,CACxB,CAAC,EAsBD1N,EAAE,gBAAgB,EAAEuI,GAAG,QAAS,WAC9BxI,WAAW,CACb,CAAC,EAGGiN,OAAO7E,WAAa,KACtBnI,EAAE,sBAAsB,EAAEG,KAAK,gBAAiB,OAAO,EAIrDH,EAAE,QAAQ,EAAEiI,SAEV3H,IAAM,IAAIqN,KAAKA,KAAKrN,IAAI,CAAC,EACzBC,WAAaD,IAAIsN,SAAS,EAE1BpN,UAAYF,IAAIuN,OAAO,EAE3B7N,EAAE,QAAQ,EAAE8N,KAAK,WAEf,IAAIC,EAAY/N,EAAEiC,IAAI,EAAEoC,KAAK,OAAO,EAChC2J,EAAUhO,EAAEiC,IAAI,EAAEoC,KAAK,KAAK,EAIjB,GAAb7D,WACa,GAAbA,WACcuN,GAAdxN,YACAA,WAAayN,EAEbhO,EAAEiC,IAAI,EAAE7B,SAAS,WAAW,EAE5BJ,EAAEiC,IAAI,EAAE7B,SAAS,YAAY,CAEjC,CAAC,GAKHJ,EAAEiN,QAAQ,EAAE1E,GAAG,QAAS,cAAe,SAAUiF,GAC/CA,EAAM9D,eAAe,EAGjBuE,EAAajO,EAAEiC,IAAI,EAAE9B,KAAK,MAAM,EAGpCH,EAAEiO,CAAU,EAAEC,YAAY,YAAY,EAGtClO,EAAEiC,IAAI,EAAEiB,KAAK,CACf,CAAC,EAGDlD,EAAEiN,QAAQ,EAAE1E,GAAG,SAAU,mBAAoB,WAE3C,IAAI0F,EAAa,IAAMjO,EAAEiC,IAAI,EAAEuK,QAAQ,YAAY,EAAEnI,KAAK,QAAQ,EAG9DrE,EAAEiC,IAAI,EAAEkM,GAAG,UAAU,EACvBnO,EAAEiO,CAAU,EAAE7N,SAAS,YAAY,EAEnCJ,EAAEiO,CAAU,EAAE/N,YAAY,YAAY,CAE1C,CAAC,EAED6M,OAAOE,QAAQ,EAAEM,MAAM,SAAUvN,GAE/B,IAuJMoO,EACAC,EAKCC,EAGAC,EAIDC,EAYAC,EAhLFC,EAAY1B,OAAO7E,WAGvBnI,EAAEgN,MAAM,EAAEzE,GAAG,SAAU,WAEjBmG,IAAc1B,OAAO7E,aAEvBuG,EAAY1B,OAAO7E,WAqBrBnI,EAAE,yBAAyB,EACxBE,YAAY,SAAS,EACrBC,KAAK,gBAAiB,OAAO,EAnBlC,CAAC,EAsBDH,EAAE,mBAAmB,EAAEuI,GAAG,QAAS,WAjB7BvI,EAAE,mBAAmB,EAAEC,SAAS,SAAS,EAC3CD,EAAE,yBAAyB,EACxBE,YAAY,SAAS,EACrBC,KAAK,gBAAiB,OAAO,EAEhCH,EAAE,yBAAyB,EACxBI,SAAS,SAAS,EAClBD,KAAK,gBAAiB,MAAM,CAYnC,CAAC,EAEG6M,OAAO7E,WAAa,KACtBnI,EAAE,yBAAyB,EAAEG,KAAK,gBAAiB,OAAO,EAI5DH,EAAE,4BAA4B,EAAEuI,GAAG,QAAS,SAAUiF,GACpDA,EAAM9D,eAAe,EACrB1J,EAAEiC,IAAI,EAAEkL,OAAO,EAAEe,YAAY,SAAS,CACxC,CAAC,EAGDlO,EAAE,iBAAiB,EAAEuI,GAAG,QAAS,WAC/BvI,EAAE,SAAS,EAAEkO,YAAY,SAAS,CACpC,CAAC,EAGGlO,EAAE,mBAAmB,EAAEiI,QACzBjI,EAAE,mBAAmB,EAAEuI,GAAG,QAAS,WACjC,IAAIoG,EAAY3O,EAAEiC,IAAI,EAAEoC,KAAK,QAAQ,EACjCuK,EAAY5O,EAAEiC,IAAI,EAAEiH,KAAK,IAAI,EAAE3F,KAAK,EACxCvD,EAAEiC,IAAI,EAAEiH,KAAK,IAAI,EAAErF,KAAK8K,CAAS,EACjC3O,EAAEiC,IAAI,EAAEoC,KAAK,SAAUuK,CAAS,EAChC5O,EAAE,kBAAkB,EAAEkO,YAAY,SAAS,CAC7C,CAAC,EAIClO,EAAE,QAAQ,EAAEiI,QACdjI,EAAE,sBAAsB,EAAEuI,GAAG,QAAS,WACpCvI,EAAEiC,IAAI,EAAEuK,QAAQ,QAAQ,EAAEtJ,KAAK,CACjC,CAAC,EAIClD,EAAE,QAAQ,EAAEiI,QAA8B,IAApB+E,OAAO7E,YAC/BnI,EAAE,oBAAoB,EAAEuI,GAAG,QAAS,SAAUiF,GAC5CA,EAAM9D,eAAe,EACrB1J,EAAE,QAAQ,EAAEkO,YAAY,SAAS,CACnC,CAAC,EAIClO,EAAE,eAAe,EAAEiI,QACrBjI,EAAE,eAAe,EAAEuI,GAAG,QAAS,SAAUiF,GACvCA,EAAM9D,eAAe,EACrB1J,EAAE,QAAQ,EAAEE,YAAY,SAAS,CACnC,CAAC,EAICF,EAAE,WAAW,EAAEiI,QACjBjI,EAAE,WAAW,EAAEuI,GAAG,QAAS,SAAUiF,GACnCA,EAAM9D,eAAe,EACrBsD,OAAO6B,MAAM,CACf,CAAC,EAIC7O,EAAE,aAAa,EAAEiI,QAAU+E,OAAO7E,WAAa,KACjDnI,EAAE,gBAAgB,EAAEuI,GAAG,QAAS,SAAUiF,GACxCA,EAAM9D,eAAe,EACrB1J,EAAEiC,IAAI,EAAE0K,KAAK,IAAI,EAAEuB,YAAY,SAAS,EACxClO,EAAEiC,IAAI,EAAEiM,YAAY,SAAS,CAC/B,CAAC,EAKkD,CAAC,IAApDlB,OAAOrF,SAASpG,KAAK8B,QAAQ,gBAAgB,GACF,CAAC,IAA5C2J,OAAOrF,SAASpG,KAAK8B,QAAQ,QAAQ,GACS,CAAC,IAA/C2J,OAAOrF,SAASpG,KAAK8B,QAAQ,WAAW,IAExC2J,OAAOrF,SAASpG,KAAO,8BAIrBvB,EAAE,eAAe,EAAEiI,QAA8B,IAApB+E,OAAO7E,cAClC2G,EAAS,IAAIC,OAAO,gBAAiB,CACvCxE,KAAM,OACNyE,SAAU,CAAA,EACVC,OAAQ,CAAA,EACRC,SAAU,GACZ,CAAC,GACM3G,GAAG,qBAAsB,SAAUlE,GACxCA,EAAK8K,MAAMC,QAAQ,SAAUC,GAC3BA,EAAKC,OAAOC,YAAcC,OAAOH,EAAKI,KAAO,CAAC,CAChD,CAAC,CACH,CAAC,EACDX,EAAOY,MAAM,GAIX1P,EAAE,mBAAmB,EAAEiI,QACzB,IAAI8G,OAAO,oBAAqB,CAC9BY,QAAS,EACTC,QAAS,EACTC,WAAY,CAAA,EACZC,YAAa,CACXC,IAAK,CACHJ,QAAS,CACX,EACAK,KAAM,CACJL,QAAS,CACX,EACAM,KAAM,CACJN,QAAS,CACX,CACF,CACF,CAAC,EAAED,MAAM,EAIP1P,EAAE,eAAe,EAAEiI,SAEjBmG,EAAYpO,EAAE,eAAe,EAAEqE,KAAK,KAAK,EACzCgK,EAAYrO,EAAE,eAAe,EAAEqE,KAAK,KAAK,EAKxCiK,GAAAA,EAHmBtO,EAAE,eAAe,EAAEqE,KAAK,MAAM,IAIhC+J,EAEjBG,GAAAA,EALmBvO,EAAE,eAAe,EAAEqE,KAAK,MAAM,IAMhCgK,EAGlBG,EAASvB,SAASiD,eAAe,cAAc,EAEnDC,WAAWC,OAAO5B,EAAQ,CACxB6B,MAAO,CAAC/B,EAAmBC,GAC3B+B,QAAS,CAAA,EACTC,KAAM,EACNC,MAAO,CACLC,IAAKrC,EACLsC,IAAKrC,CACP,CACF,CAAC,EAEGI,EAAa,CACfxB,SAASiD,eAAe,YAAY,EACpCjD,SAASiD,eAAe,YAAY,GAItC1B,EAAO2B,WAAW5H,GAAG,SAAU,SAAUoI,EAAQC,GAC/CnC,EAAWmC,GAAQC,MAAQ9F,KAAK+F,KAAKH,EAAOC,EAAO,CACrD,CAAC,EAGDpC,EAAO2B,WAAW5H,GAAG,MAAO,SAAUoI,EAAQC,GAC5C5Q,EAAE,aAAa,EAAEoC,QAAQ,QAAQ,CACnC,CAAC,EAEL,CAAC"}