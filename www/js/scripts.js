function switchMenu(){$(".nav__switcher").hasClass("is-open")?$(".nav, .nav__switcher").removeClass("is-open").attr("aria-expanded","false"):$(".nav, .nav__switcher").addClass("is-open").attr("aria-expanded","true")}function resetMenu(){$(".nav, .nav__switcher").removeClass("is-open").attr("aria-expanded","false")}var now,actualHour,actualDay;!function(et,nt,T){"use strict";et.fn.simpleLightbox=function(W){function O(){return o.hash.substring(1)}function r(){O();var t="pid="+(L+1),e=o.href.split("#")[0]+"#"+t;n?history[p?"replaceState":"pushState"]("",T.title,e):p?o.replace(e):o.hash=t,p=!0}function t(t,e){var n;return function(){n||(t.apply(this,arguments),n=!0,setTimeout(function(){return n=!1},e))}}function a(t){t.trigger(et.Event("show.simplelightbox")),W.disableScroll&&(h=_("hide")),W.htmlClass&&""!=W.htmlClass&&et("html").addClass(W.htmlClass),m.appendTo("body"),U.appendTo(m),W.overlay&&s.appendTo(et("body")),g=!0,L=z.index(t),H=et("<img/>").hide().attr("src",t.attr(W.sourceAttr)).attr("data-scale",1).attr("data-translate-x",0).attr("data-translate-y",0),-1==d.indexOf(t.attr(W.sourceAttr))&&d.push(t.attr(W.sourceAttr)),U.html("").attr("style",""),H.appendTo(U),y(),s.fadeIn("fast"),et(".sl-close").fadeIn("fast"),u.show(),Z.fadeIn("fast"),et(".sl-wrapper .sl-counter .sl-current").text(L+1),l.fadeIn("fast"),B(),W.preloading&&C(),setTimeout(function(){t.trigger(et.Event("shown.simplelightbox"))},W.animationSpeed)}function X(t,e,n){return t<e?e:n<t?n:t}function Y(t,e,n){H.data("scale",t),H.data("translate-x",e),H.data("translate-y",n)}W=et.extend({sourceAttr:"href",overlay:!0,spinner:!0,nav:!0,navText:["&lsaquo;","&rsaquo;"],captions:!0,captionDelay:0,captionSelector:"img",captionType:"attr",captionsData:"title",captionPosition:"bottom",captionClass:"",close:!0,closeText:"&times;",swipeClose:!0,showCounter:!0,fileExt:"png|jpg|jpeg|gif|webp",animationSlide:!0,animationSpeed:250,preloading:!0,enableKeyboard:!0,loop:!0,rel:!1,docClose:!0,swipeTolerance:50,className:"simple-lightbox",widthRatio:.8,heightRatio:.9,scaleImageToRatio:!1,disableRightClick:!1,disableScroll:!0,alertError:!0,alertErrorMessage:"Image not found, next image will be loaded",additionalHtml:!1,history:!0,throttleInterval:0,doubleTapZoom:2,maxZoom:10,htmlClass:"has-lightbox"},W);var c,e,V="ontouchstart"in nt,P=(nt.navigator.pointerEnabled||nt.navigator.msPointerEnabled,0),A=0,H=et(),i=function(){var t;return""===(t=(T.body||T.documentElement).style).WebkitTransition?"-webkit-":""===t.MozTransition?"-moz-":""===t.OTransition?"-o-":""===t.transition&&""},R=!1,d=[],z=W.rel&&!1!==W.rel?(e=W.rel,et(this).filter(function(){return et(this).attr("rel")===e})):this,v=z.get()[0].tagName,h=(i=i(),0),F=!1!==i,n="pushState"in history,p=!1,o=nt.location,tt=O(),N="simplelb",s=et("<div>").addClass("sl-overlay"),x=et("<button>").addClass("sl-close").html(W.closeText),u=et("<div>").addClass("sl-spinner").html("<div></div>"),Z=et("<div>").addClass("sl-navigation").html('<button class="sl-prev">'+W.navText[0]+'</button><button class="sl-next">'+W.navText[1]+"</button>"),l=et("<div>").addClass("sl-counter").html('<span class="sl-current"></span>/<span class="sl-total"></span>'),g=!1,L=0,f=0,w=et("<div>").addClass("sl-caption "+W.captionClass+" pos-"+W.captionPosition),U=et("<div>").addClass("sl-image"),m=et("<div>").addClass("sl-wrapper").addClass(W.className),B=function(i){var o,s,l;H.length&&(o=new Image,s=nt.innerWidth*W.widthRatio,l=nt.innerHeight*W.heightRatio,o.src=H.attr("src"),H.data("scale",1),H.data("translate-x",0),H.data("translate-y",0),K(0,0,1),et(o).on("error",function(t){z.eq(L).trigger(et.Event("error.simplelightbox")),R=!(g=!1),u.hide();var e=1==i||-1==i;f===L&&e?J():(W.alertError&&alert(W.alertErrorMessage),G(e?i:1))}),o.onload=function(){void 0!==i&&z.eq(L).trigger(et.Event("changed.simplelightbox")).trigger(et.Event((1===i?"nextDone":"prevDone")+".simplelightbox")),W.history&&(p?c=setTimeout(r,800):r()),-1==d.indexOf(H.attr("src"))&&d.push(H.attr("src"));var t=o.width,e=o.height;(W.scaleImageToRatio||s<t||l<e)&&(t/=n=s/l<t/e?t/s:e/l,e/=n),et(".sl-image").css({top:(nt.innerHeight-e)/2+"px",left:(nt.innerWidth-t-h)/2+"px",width:t+"px",height:e+"px"}),u.hide(),H.fadeIn("fast"),R=!0;var n="self"==W.captionSelector?z.eq(L):z.eq(L).find(W.captionSelector),a="data"==W.captionType?n.data(W.captionsData):"text"==W.captionType?n.html():n.prop(W.captionsData);W.loop||(0===L&&et(".sl-prev").hide(),L>=z.length-1&&et(".sl-next").hide(),0<L&&et(".sl-prev").show(),L<z.length-1&&et(".sl-next").show()),1==z.length&&et(".sl-prev, .sl-next").hide(),1==i||-1==i?(e={opacity:1},W.animationSlide&&(F?(Q(0,100*i+"px"),setTimeout(function(){Q(W.animationSpeed/1e3,"0px")},50)):e.left=parseInt(et(".sl-image").css("left"))+100*i+"px"),et(".sl-image").animate(e,W.animationSpeed,function(){g=!1,b(a,t)})):(g=!1,b(a,t)),W.additionalHtml&&0===et(".sl-additional-html").length&&et("<div>").html(W.additionalHtml).addClass("sl-additional-html").appendTo(et(".sl-image"))})},b=function(t,e){""!==t&&void 0!==t&&W.captions&&w.html(t).css({width:e+"px"}).hide().appendTo(et(".sl-image")).delay(W.captionDelay).fadeIn("fast")},Q=function(t,e){var n={};n[i+"transform"]="translateX("+e+")",n[i+"transition"]=i+"transform "+t+"s linear",et(".sl-image").css(n)},K=function(t,e,n){var a={};a[i+"transform"]="translate("+t+","+e+") scale("+n+")",H.css(a)},y=function(){et(nt).on("resize."+N,B),et(".sl-wrapper").on("click."+N+" touchstart."+N,".sl-close",function(t){t.preventDefault(),R&&J()}),W.history&&setTimeout(function(){et(nt).on("hashchange."+N,function(){R&&O()===tt&&J()})},40),Z.on("click."+N,"button",t(function(t){t.preventDefault(),P=0,G(et(this).hasClass("sl-next")?1:-1)},W.throttleInterval));var e,n,a,i,o,s,l,r,c,d,h,p,u,g,f,m,v,x,w,b,y,C,_,T,$,k=0,S=0,E=!1,D=!1,M=0,I=!1,j=X(1,1,W.maxZoom),q=!1;U.on("touchstart."+N+" mousedown."+N,function(t){if("A"===t.target.tagName&&"touchstart"==t.type)return!0;if("mousedown"==(t=t.originalEvent).type)c=t.clientX,d=t.clientY,e=U.height(),n=U.width(),o=H.height(),s=H.width(),a=U.position().left,i=U.position().top,l=parseFloat(H.data("translate-x")),r=parseFloat(H.data("translate-y"));else if(_=t.touches.length,c=t.touches[0].clientX,d=t.touches[0].clientY,e=U.height(),n=U.width(),o=H.height(),s=H.width(),a=U.position().left,i=U.position().top,1===_){if(q)return H.addClass("sl-transition"),E=E?(Y(0,0,j=1),K("0px","0px",j),!1):(Y(0,0,j=W.doubleTapZoom),K("0px","0px",j),et(".sl-caption").fadeOut(200),!0),setTimeout(function(){H.removeClass("sl-transition")},200),!1;q=!0,setTimeout(function(){q=!1},300),l=parseFloat(H.data("translate-x")),r=parseFloat(H.data("translate-y"))}else 2===_&&(h=t.touches[1].clientX,p=t.touches[1].clientY,l=parseFloat(H.data("translate-x")),r=parseFloat(H.data("translate-y")),w=(c+h)/2,b=(d+p)/2,u=Math.sqrt((c-h)*(c-h)+(d-p)*(d-p)));return I=!0,!!D||(F&&(M=parseInt(U.css("left"))),D=!0,A=P=0,k=t.pageX||t.touches[0].pageX,S=t.pageY||t.touches[0].pageY,!1)}).on("touchmove."+N+" mousemove."+N+" MSPointerMove",function(t){if(!D)return!0;if(t.preventDefault(),"touchmove"==(t=t.originalEvent).type){if(!1===I)return!1;g=t.touches[0].clientX,f=t.touches[0].clientY,1<(_=t.touches.length)?($=t.touches[1].clientX,T=t.touches[1].clientY,$=Math.sqrt((g-$)*(g-$)+(f-T)*(f-T)),null===u&&(u=$),1<=Math.abs(u-$)&&(x=X($/u*j,1,W.maxZoom),y=(s*x-n)/2,C=(o*x-e)/2,T=x-j,m=s*x<=n?0:X(l-(w-a-n/2-l)/(x-T)*T,-1*y,y),v=o*x<=e?0:X(r-(b-i-e/2-r)/(x-T)*T,-1*C,C),K(m+"px",v+"px",x),1<x&&(E=!0,et(".sl-caption").fadeOut(200)),u=$,j=x,l=m,r=v)):(y=(s*(x=j)-n)/2,C=(o*x-e)/2,m=s*x<=n?0:X(g-(c-l),-1*y,y),v=o*x<=e?0:X(f-(d-r),-1*C,C),Math.abs(m)===Math.abs(y)&&(l=m,c=g),Math.abs(v)===Math.abs(C)&&(r=v,d=f),Y(j,m,v),K(m+"px",v+"px",x))}if("mousemove"==t.type&&D){if("touchmove"==t.type)return!0;if(!1===I)return!1;g=t.clientX,f=t.clientY,y=(s*(x=j)-n)/2,C=(o*x-e)/2,m=s*x<=n?0:X(g-(c-l),-1*y,y),v=o*x<=e?0:X(f-(d-r),-1*C,C),Math.abs(m)===Math.abs(y)&&(l=m,c=g),Math.abs(v)===Math.abs(C)&&(r=v,d=f),Y(j,m,v),K(m+"px",v+"px",x)}E||(T=t.pageX||t.touches[0].pageX,$=t.pageY||t.touches[0].pageY,P=k-T,A=S-$,W.animationSlide&&(F?Q(0,-P+"px"):U.css("left",M-P+"px")))}).on("touchend."+N+" mouseup."+N+" touchcancel."+N+" mouseleave."+N+" pointerup pointercancel MSPointerUp MSPointerCancel",function(t){t=t.originalEvent,V&&"touchend"==t.type&&(0===(_=t.touches.length)?(Y(j,m,v),1==j&&(E=!1,et(".sl-caption").fadeIn(200)),u=null,I=!1):1===_?(c=t.touches[0].clientX,d=t.touches[0].clientY):1<_&&(u=null)),D&&(t=!(D=!1),W.loop||(0===L&&P<0&&(t=!1),L>=z.length-1&&0<P&&(t=!1)),Math.abs(P)>W.swipeTolerance&&t?G(0<P?1:-1):W.animationSlide&&(F?Q(W.animationSpeed/1e3,"0px"):U.animate({left:M+"px"},W.animationSpeed/2)),W.swipeClose)&&50<Math.abs(A)&&Math.abs(P)<W.swipeTolerance&&J()}).on("dblclick",function(t){return c=t.clientX,d=t.clientY,e=U.height(),n=U.width(),o=H.height(),s=H.width(),a=U.position().left,i=U.position().top,H.addClass("sl-transition"),E?(Y(0,0,j=1),K("0px","0px",j),E=!1,et(".sl-caption").fadeIn(200)):(Y(0,0,j=W.doubleTapZoom),K("0px","0px",j),et(".sl-caption").fadeOut(200),E=!0),setTimeout(function(){H.removeClass("sl-transition")},200),!(I=!0)})},C=function(){var t=L+1<0?z.length-1:L+1>=z.length-1?0:L+1,e=L-1<0?z.length-1:L-1>=z.length-1?0:L-1;et("<img />").attr("src",z.eq(t).attr(W.sourceAttr)).on("load",function(){-1==d.indexOf(et(this).attr("src"))&&d.push(et(this).attr("src")),z.eq(L).trigger(et.Event("nextImageLoaded.simplelightbox"))}),et("<img />").attr("src",z.eq(e).attr(W.sourceAttr)).on("load",function(){-1==d.indexOf(et(this).attr("src"))&&d.push(et(this).attr("src")),z.eq(L).trigger(et.Event("prevImageLoaded.simplelightbox"))})},G=function(e){z.eq(L).trigger(et.Event("change.simplelightbox")).trigger(et.Event((1===e?"next":"prev")+".simplelightbox"));var t=L+e;g||(t<0||t>=z.length)&&!1===W.loop||(L=t<0?z.length-1:t>z.length-1?0:t,et(".sl-wrapper .sl-counter .sl-current").text(L+1),t={opacity:0},W.animationSlide&&(F?Q(W.animationSpeed/1e3,-100*e-P+"px"):t.left=parseInt(et(".sl-image").css("left"))+-100*e+"px"),et(".sl-image").animate(t,W.animationSpeed,function(){setTimeout(function(){var t=z.eq(L);H.attr("src",t.attr(W.sourceAttr)),-1==d.indexOf(t.attr(W.sourceAttr))&&u.show(),et(".sl-caption").remove(),B(e),W.preloading&&C()},100)}))},J=function(){var t,e;g||(t=z.eq(L),e=!1,t.trigger(et.Event("close.simplelightbox")),W.history&&(n?history.pushState("",T.title,o.pathname+o.search):o.hash="",clearTimeout(c)),et(".sl-image img, .sl-overlay, .sl-close, .sl-navigation, .sl-image .sl-caption, .sl-counter").fadeOut("fast",function(){W.disableScroll&&_("show"),W.htmlClass&&""!=W.htmlClass&&et("html").removeClass(W.htmlClass),et(".sl-wrapper, .sl-overlay").remove(),Z.off("click","button"),et(".sl-wrapper").off("click."+N,".sl-close"),et(nt).off("resize."+N),et(nt).off("hashchange."+N),e||t.trigger(et.Event("closed.simplelightbox")),e=!0}),H=et(),g=R=!1)},_=function(t){var e,n=0;return"hide"==t?((t=nt.innerWidth)||(t=(e=T.documentElement.getBoundingClientRect()).right-Math.abs(e.left)),T.body.clientWidth<t&&(e=T.createElement("div"),t=parseInt(et("body").css("padding-right"),10),e.className="sl-scrollbar-measure",et("body").append(e),n=e.offsetWidth-e.clientWidth,et(T.body)[0].removeChild(e),et("body").data("padding",t),0<n)&&et("body").addClass("hidden-scroll").css({"padding-right":t+n})):et("body").removeClass("hidden-scroll").css({"padding-right":et("body").data("padding")}),n};return W.close&&x.appendTo(m),W.showCounter&&1<z.length&&(l.appendTo(m),l.find(".sl-total").text(z.length)),W.nav&&Z.appendTo(m),W.spinner&&u.appendTo(m),z.on("click."+N,function(t){if(e=this,!W.fileExt||(n=et(e).attr(W.sourceAttr).match(/\.([0-9a-z]+)(?=[?#])|(\.)(?:[\w]+)$/gim))&&et(e).prop("tagName").toUpperCase()==v&&new RegExp(".("+W.fileExt+")$","i").test(n)){if(t.preventDefault(),g)return!1;e=et(this);f=z.index(e),a(e)}var e,n}),et(T).on("click."+N+" touchstart."+N,function(t){R&&W.docClose&&0===et(t.target).closest(".sl-image").length&&0===et(t.target).closest(".sl-navigation").length&&J()}),W.disableRightClick&&et(T).on("contextmenu",".sl-image img",function(t){return!1}),W.enableKeyboard&&et(T).on("keyup."+N,t(function(t){P=0;var e=t.keyCode;g&&27==e&&(H.attr("src",""),g=!1,J()),R&&(t.preventDefault(),27==e&&J(),37!=e&&39!=t.keyCode||G(39==t.keyCode?1:-1))},W.throttleInterval)),this.open=function(t){t=t||et(this[0]),f=z.index(t),a(t)},this.next=function(){G(1)},this.prev=function(){G(-1)},this.close=function(){J()},this.destroy=function(){et(T).off("click."+N).off("keyup."+N),J(),et(".sl-overlay, .sl-wrapper").remove(),this.off("click")},this.refresh=function(){this.destroy(),et(this).simpleLightbox(W)},this}}(jQuery,window,document),$(document).on("click",".product__count .icon--plus",function(){var t=$(this).parent().find("input").val();((t=parseFloat(t))<=0||!$.isNumeric(t))&&(t=0),t+=1,$(this).parent().find("input").val(t).change()}),$(document).on("click",".product__count .icon--minus",function(){var t=$(this).parent().find("input").val();((t=parseFloat(t))<=0||!$.isNumeric(t))&&(t=1),t-=1,$(this).parent().find("input").val(t).change()}),jQuery(document).ready(function(t){t(".js-gallery").length&&t(".js-gallery a").simpleLightbox({showCounter:!1})}),$(document).on("click",".js-modal",function(t){t.preventDefault();t=$(this).attr("href");$(t).addClass("is-open")}),$(document).on("click",".modal__close, .modal__close-false",function(t){t.preventDefault(),$(this).closest(".modal").removeClass("is-open")}),$(document).on("click",".modal",function(t){t.preventDefault(),$(this).removeClass("is-open")}),$(document).on("click",".modal__body",function(t){t.stopPropagation()}),$(".nav__switcher").on("click",function(){switchMenu()}),window.innerWidth<992&&$(".nav, .nav__switcher").attr("aria-expanded","false"),$(".phone").length&&(now=new Date(Date.now()),actualHour=now.getHours(),actualDay=now.getDay(),$(".phone").each(function(){var t=$(this).data("start"),e=$(this).data("end");0!=actualDay&&6!=actualDay&&t<=actualHour&&actualHour<e?$(this).addClass("is-online"):$(this).addClass("is-offline")})),$(document).on("click","a.js-reveal",function(t){t.preventDefault();t=$(this).attr("href");$(t).toggleClass("is-visible"),$(this).hide()}),$(document).on("change",".js-reveal input",function(){var t="#"+$(this).closest(".js-reveal").data("reveal");$(this).is(":checked")?$(t).addClass("is-visible"):$(t).removeClass("is-visible")}),jQuery(document).ready(function(n){var t,e,a,i,o,s,l=window.innerWidth;n(window).on("resize",function(){l!==window.innerWidth&&(l=window.innerWidth,n(".nav, .header__switcher").removeClass("is-open").attr("aria-expanded","false"))}),n(".header__switcher").on("click",function(){n(".header__switcher").hasClass("is-open")?n(".nav, .header__switcher").removeClass("is-open").attr("aria-expanded","false"):n(".nav, .header__switcher").addClass("is-open").attr("aria-expanded","true")}),window.innerWidth<992&&n(".nav, .header__switcher").attr("aria-expanded","false"),n(".nav__main.has-submenu > a").on("click",function(t){t.preventDefault(),n(this).parent().toggleClass("is-open")}),n(".header__search").on("click",function(){n(".search").toggleClass("is-open")}),n(".js-filter-switch").length&&n(".js-filter-switch").on("click",function(){var t=n(this).data("switch"),e=n(this).find("em").html();n(this).find("em").text(t),n(this).data("switch",e),n(".sidebar--filter").toggleClass("is-open")}),n(".alert").length&&n(".alert .icon--delete").on("click",function(){n(this).closest(".alert").hide()}),n(".login").length&&991<window.innerWidth&&n(".login > a > .icon").on("click",function(t){t.preventDefault(),n(".login").toggleClass("is-open")}),n(".login__close").length&&n(".login__close").on("click",function(t){t.preventDefault(),n(".login").removeClass("is-open")}),n(".js-print").length&&n(".js-print").on("click",function(t){t.preventDefault(),window.print()}),n(".nav-footer").length&&window.innerWidth<992&&n(".nav-footer h2").on("click",function(t){t.preventDefault(),n(this).next("ul").toggleClass("is-open"),n(this).toggleClass("is-open")}),-1===window.location.href.indexOf("goldfitness.cz")&&-1===window.location.href.indexOf(".local")&&-1===window.location.href.indexOf("localhost")&&(window.location.href="https://www.goldfitness.cz"),n(".splide--main").length&&991<window.innerWidth&&((t=new Splide(".splide--main",{type:"fade",autoplay:!0,rewind:!0,interval:4e3})).on("pagination:mounted",function(t){t.items.forEach(function(t){t.button.textContent=String(t.page+1)})}),t.mount()),n(".splide--products").length&&new Splide(".splide--products",{perPage:4,perMove:1,pagination:!1,breakpoints:{640:{perPage:1},1200:{perPage:2},1600:{perPage:3}}}).mount(),n("#range-slider").length&&(t=n("#range-slider").data("min"),e=n("#range-slider").data("max"),a=(a=n("#range-slider").data("mins"))||t,i=(i=n("#range-slider").data("maxs"))||e,o=document.getElementById("range-slider"),noUiSlider.create(o,{start:[a,i],connect:!0,step:1,range:{min:t,max:e}}),s=[document.getElementById("slider-min"),document.getElementById("slider-max")],o.noUiSlider.on("update",function(t,e){s[e].value=Math.ceil(t[e])}),o.noUiSlider.on("set",function(t,e){n("#slider-min").trigger("change")}))});
//# sourceMappingURL=scripts.js.map
