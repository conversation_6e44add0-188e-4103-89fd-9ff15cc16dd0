jQuery(document).ready(function ($) {
  // obecne
  var bodyWidth = window.innerWidth;

  // ud<PERSON>losti při resize okna
  $(window).on('resize', function () {
    // osetreni, zda se velikost zmenila
    if (bodyWidth !== window.innerWidth) {
      // nastavíme novou šířku
      bodyWidth = window.innerWidth;
      // zresetuj menu
      resetMenu();
    }
  });

  // mobilní menu
  function switchMenu() {
    // označíme zda je menu zavřeno či nikoliv
    if ($('.header__switcher').hasClass('is-open')) {
      $('.nav, .header__switcher')
        .removeClass('is-open')
        .attr('aria-expanded', 'false');
    } else {
      $('.nav, .header__switcher')
        .addClass('is-open')
        .attr('aria-expanded', 'true');
    }
  }
  // při změně rozlišení resetujeme menu
  function resetMenu() {
    $('.nav, .header__switcher')
      .removeClass('is-open')
      .attr('aria-expanded', 'false');
  }
  // spouštěč
  $('.header__switcher').on('click', function () {
    switchMenu();
  });
  // aria atributy k menu
  if (window.innerWidth < 992) {
    $('.nav, .header__switcher').attr('aria-expanded', 'false');
  }

  // submenu
  $('.nav__main.has-submenu > a').on('click', function (event) {
    event.preventDefault();
    $(this).parent().toggleClass('is-open');
  });

  // vyhledávání
  $('.header__search').on('click', function () {
    $('.search').toggleClass('is-open');
  });

  // vyhledávání
  if ($('.js-filter-switch').length) {
    $('.js-filter-switch').on('click', function () {
      var switchNew = $(this).data('switch');
      var switchOld = $(this).find('em').html();
      $(this).find('em').text(switchNew);
      $(this).data('switch', switchOld);
      $('.sidebar--filter').toggleClass('is-open');
    });
  }

  // hláška s možností zavření
  if ($('.alert').length) {
    $('.alert .icon--delete').on('click', function () {
      $(this).closest('.alert').hide();
    });
  }

  // hover na login
  if ($('.login').length && window.innerWidth > 991) {
    $('.login > a > .icon').on('click', function (event) {
      event.preventDefault();
      $('.login').toggleClass('is-open');
    });
  }

  // zavření loginu
  if ($('.login__close').length) {
    $('.login__close').on('click', function (event) {
      event.preventDefault();
      $('.login').removeClass('is-open');
    });
  }

  // vytisknout
  if ($('.js-print').length) {
    $('.js-print').on('click', function (event) {
      event.preventDefault();
      window.print();
    });
  }

  // menu v patičce
  if ($('.nav-footer').length && window.innerWidth < 992) {
    $('.nav-footer h2').on('click', function (event) {
      event.preventDefault();
      $(this).next('ul').toggleClass('is-open');
      $(this).toggleClass('is-open');
    });
  }

  // kontrola domény a přesměrování
  if (
    window.location.href.indexOf('goldfitness.cz') === -1 &&
    window.location.href.indexOf('.local') === -1 &&
    window.location.href.indexOf('localhost') === -1
  ) {
    window.location.href = 'https://www.goldfitness.cz';
  }

  // hlavní slider
  if ($('.splide--main').length && window.innerWidth > 991) {
    var splide = new Splide('.splide--main', {
      type: 'fade',
      autoplay: true,
      rewind: true,
      interval: 4000,
    });
    splide.on('pagination:mounted', function (data) {
      data.items.forEach(function (item) {
        item.button.textContent = String(item.page + 1);
      });
    });
    splide.mount();
  }

  // slider - produkty
  if ($('.splide--products').length) {
    new Splide('.splide--products', {
      perPage: 4,
      perMove: 1,
      pagination: false,
      breakpoints: {
        640: {
          perPage: 1,
        },
        1200: {
          perPage: 2,
        },
        1600: {
          perPage: 3,
        },
      },
    }).mount();
  }

  // vlastní slider
  if ($('#range-slider').length) {
    // hodnoty z data atributů
    var sliderMin = $('#range-slider').data('min');
    var sliderMax = $('#range-slider').data('max');
    // aktuální pozice
    var sliderMinSelected = $('#range-slider').data('mins');
    var sliderMaxSelected = $('#range-slider').data('maxs');
    // ošetření min/max pozice
    if (!sliderMinSelected) {
      sliderMinSelected = sliderMin;
    }
    if (!sliderMaxSelected) {
      sliderMaxSelected = sliderMax;
    }

    var slider = document.getElementById('range-slider');

    noUiSlider.create(slider, {
      start: [sliderMinSelected, sliderMaxSelected],
      connect: true,
      step: 1,
      range: {
        min: sliderMin,
        max: sliderMax,
      },
    });

    var dateValues = [
      document.getElementById('slider-min'),
      document.getElementById('slider-max'),
    ];

    // aktualizace hodnot v inputech
    slider.noUiSlider.on('update', function (values, handle) {
      dateValues[handle].value = Math.ceil(values[handle]);
    });

    // odeslání formuláře
    slider.noUiSlider.on('set', function (values, handle) {
      $('#slider-min').trigger('change');
    });
  }
});
