!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).naja=e()}(this,(function(){"use strict";const t=t=>{"loading"===document.readyState?document.addEventListener("DOMContentLoaded",t):t()};class e extends Error{}const i=(t,i)=>{if(!t){throw new e("Assertion failed"+(void 0!==i?`: ${i}`:"."))}};class n extends EventTarget{constructor(t){super(),this.naja=t,this.selector=".ajax",this.allowedOrigins=[window.location.origin],this.handler=this.handleUI.bind(this),t.addEventListener("init",this.initialize.bind(this))}initialize(){t((()=>this.bindUI(window.document.body))),this.naja.snippetHandler.addEventListener("afterUpdate",(t=>{const{snippet:e}=t.detail;this.bindUI(e)}))}bindUI(t){const e=[`a${this.selector}`,`input[type="submit"]${this.selector}`,`input[type="image"]${this.selector}`,`button[type="submit"]${this.selector}`,`form${this.selector} input[type="submit"]`,`form${this.selector} input[type="image"]`,`form${this.selector} button[type="submit"]`].join(", "),i=t=>{t.removeEventListener("click",this.handler),t.addEventListener("click",this.handler)},n=t.querySelectorAll(e);for(let t=0;t<n.length;t++)i(n.item(t));t.matches(e)&&i(t);const s=t=>{t.removeEventListener("submit",this.handler),t.addEventListener("submit",this.handler)};t.matches(`form${this.selector}`)&&s(t);const a=t.querySelectorAll(`form${this.selector}`);for(let t=0;t<a.length;t++)s(a.item(t))}handleUI(t){const e=t;if(e.altKey||e.ctrlKey||e.shiftKey||e.metaKey||e.button)return;const i=t.currentTarget,n={},s=()=>{};"submit"===t.type?this.submitForm(i,n,t).catch(s):"click"===t.type&&this.clickElement(i,n,e).catch(s)}async clickElement(t,e={},n){var s,a,o,r,l,d;let c,h="GET",p="";if(!this.dispatchEvent(new CustomEvent("interaction",{cancelable:!0,detail:{element:t,originalEvent:n,options:e}})))return null==n||n.preventDefault(),{};if("A"===t.tagName)i(t instanceof HTMLAnchorElement),h="GET",p=t.href,c=null;else if("INPUT"===t.tagName||"BUTTON"===t.tagName){i(t instanceof HTMLInputElement||t instanceof HTMLButtonElement);const{form:e}=t;if(h=null!==(r=null!==(a=null===(s=t.getAttribute("formmethod"))||void 0===s?void 0:s.toUpperCase())&&void 0!==a?a:null===(o=null==e?void 0:e.getAttribute("method"))||void 0===o?void 0:o.toUpperCase())&&void 0!==r?r:"GET",p=null!==(d=null!==(l=t.getAttribute("formaction"))&&void 0!==l?l:null==e?void 0:e.getAttribute("action"))&&void 0!==d?d:window.location.pathname+window.location.search,c=new FormData(null!=e?e:void 0),"submit"===t.type&&""!==t.name)c.append(t.name,t.value||"");else if("image"===t.type){const e=t.getBoundingClientRect(),i=""!==t.name?`${t.name}.`:"";c.append(`${i}x`,Math.max(0,Math.floor(void 0!==n?n.pageX-e.left:0))),c.append(`${i}y`,Math.max(0,Math.floor(void 0!==n?n.pageY-e.top:0)))}}if(!this.isUrlAllowed(p))throw new Error(`Cannot dispatch async request, URL is not allowed: ${p}`);return null==n||n.preventDefault(),this.naja.makeRequest(h,p,c,e)}async submitForm(t,e={},i){var n,s,a;if(!this.dispatchEvent(new CustomEvent("interaction",{cancelable:!0,detail:{element:t,originalEvent:i,options:e}})))return null==i||i.preventDefault(),{};const o=null!==(s=null===(n=t.getAttribute("method"))||void 0===n?void 0:n.toUpperCase())&&void 0!==s?s:"GET",r=null!==(a=t.getAttribute("action"))&&void 0!==a?a:window.location.pathname+window.location.search,l=new FormData(t);if(!this.isUrlAllowed(r))throw new Error(`Cannot dispatch async request, URL is not allowed: ${r}`);return null==i||i.preventDefault(),this.naja.makeRequest(o,r,l,e)}isUrlAllowed(t){const e=new URL(t,location.href);return"null"!==e.origin&&this.allowedOrigins.includes(e.origin)}}class s{constructor(t){this.naja=t,t.addEventListener("init",this.initialize.bind(this)),t.uiHandler.addEventListener("interaction",this.processForm.bind(this))}initialize(){t((()=>this.initForms(window.document.body))),this.naja.snippetHandler.addEventListener("afterUpdate",(t=>{const{snippet:e}=t.detail;this.initForms(e)}))}initForms(t){const e=this.netteForms||window.Nette;if(e){"form"===t.tagName&&e.initForm(t);const i=t.querySelectorAll("form");for(let t=0;t<i.length;t++)e.initForm(i.item(t))}}processForm(t){const{element:e,originalEvent:i}=t.detail,n=e;void 0!==n.form&&null!==n.form&&(n.form["nette-submittedBy"]=e);const s=this.netteForms||window.Nette;"FORM"!==e.tagName&&!e.form||!s||s.validateForm(e)||(i&&(i.stopImmediatePropagation(),i.preventDefault()),t.preventDefault())}}class a extends EventTarget{constructor(t){super(),this.naja=t,t.uiHandler.addEventListener("interaction",(t=>{var e,i,n;const{element:s,options:a}=t.detail;if(s&&(s.hasAttribute("data-naja-force-redirect")||(null===(e=s.form)||void 0===e?void 0:e.hasAttribute("data-naja-force-redirect")))){const t=null!==(i=s.getAttribute("data-naja-force-redirect"))&&void 0!==i?i:null===(n=s.form)||void 0===n?void 0:n.getAttribute("data-naja-force-redirect");a.forceRedirect="off"!==t}})),t.addEventListener("success",(t=>{var e;const{payload:i,options:n}=t.detail;i.redirect&&(this.makeRedirect(i.redirect,null!==(e=n.forceRedirect)&&void 0!==e&&e,n),t.stopImmediatePropagation())})),this.locationAdapter={assign:t=>window.location.assign(t)}}makeRedirect(t,e,i={}){t instanceof URL&&(t=t.href);let n=e||!this.naja.uiHandler.isUrlAllowed(t);this.dispatchEvent(new CustomEvent("redirect",{cancelable:!0,detail:{url:t,isHardRedirect:n,setHardRedirect(t){n=!!t},options:i}}))&&(n?this.locationAdapter.assign(t):this.naja.makeRequest("GET",t,null,i))}}class o extends EventTarget{constructor(t){super(),this.naja=t,this.op={replace:(t,e)=>{t.innerHTML=e},prepend:(t,e)=>t.insertAdjacentHTML("afterbegin",e),append:(t,e)=>t.insertAdjacentHTML("beforeend",e)},t.addEventListener("success",(t=>{const{options:e,payload:i}=t.detail;i.snippets&&this.updateSnippets(i.snippets,!1,e)}))}static findSnippets(t){var e;const i={},n=window.document.querySelectorAll('[id^="snippet-"]');for(let s=0;s<n.length;s++){const a=n.item(s);(null===(e=null==t?void 0:t(a))||void 0===e||e)&&(i[a.id]=a.innerHTML)}return i}updateSnippets(t,e=!1,i={}){Object.keys(t).forEach((n=>{const s=document.getElementById(n);s&&this.updateSnippet(s,t[n],e,i)}))}updateSnippet(t,e,i,n){let s=this.op.replace;!t.hasAttribute("data-naja-snippet-prepend")&&!t.hasAttribute("data-ajax-prepend")||i?!t.hasAttribute("data-naja-snippet-append")&&!t.hasAttribute("data-ajax-append")||i||(s=this.op.append):s=this.op.prepend;this.dispatchEvent(new CustomEvent("beforeUpdate",{cancelable:!0,detail:{snippet:t,content:e,fromCache:i,operation:s,changeOperation(t){s=t},options:n}}))&&("title"===t.tagName.toLowerCase()?document.title=e:s(t,e),this.dispatchEvent(new CustomEvent("afterUpdate",{cancelable:!0,detail:{snippet:t,content:e,fromCache:i,operation:s,options:n}})))}}class r extends EventTarget{constructor(t){super(),this.naja=t,this.href=null,this.popStateHandler=this.handlePopState.bind(this),t.addEventListener("init",this.initialize.bind(this)),t.addEventListener("before",this.saveUrl.bind(this)),t.addEventListener("success",this.pushNewState.bind(this)),t.uiHandler.addEventListener("interaction",this.configureMode.bind(this)),this.historyAdapter={replaceState:(t,e,i)=>window.history.replaceState(t,e,i),pushState:(t,e,i)=>window.history.pushState(t,e,i)}}set uiCache(t){console.warn("Naja: HistoryHandler.uiCache is deprecated, use options.snippetCache instead."),this.naja.defaultOptions.snippetCache=t}initialize(e){const{defaultOptions:i}=e.detail;window.addEventListener("popstate",this.popStateHandler),t((()=>this.historyAdapter.replaceState(this.buildState(window.location.href,i),window.document.title,window.location.href)))}handlePopState(t){const{state:e}=t;if(!e)return;const i=this.naja.prepareOptions();this.dispatchEvent(new CustomEvent("restoreState",{detail:{state:e,options:i}}))}saveUrl(t){const{url:e}=t.detail;this.href=e}configureMode(t){var e,i,n;const{element:s,options:a}=t.detail;if(s&&(s.hasAttribute("data-naja-history")||(null===(e=s.form)||void 0===e?void 0:e.hasAttribute("data-naja-history")))){const t=null!==(i=s.getAttribute("data-naja-history"))&&void 0!==i?i:null===(n=s.form)||void 0===n?void 0:n.getAttribute("data-naja-history");a.history=r.normalizeMode(t)}}static normalizeMode(t){return"off"!==t&&!1!==t&&("replace"!==t||"replace")}pushNewState(t){const{payload:e,options:i}=t.detail,n=r.normalizeMode(i.history);if(!1===n)return;e.postGet&&e.url&&(this.href=e.url);const s="replace"===n?"replaceState":"pushState";this.historyAdapter[s](this.buildState(this.href,i),window.document.title,this.href),this.href=null}buildState(t,e){const i={href:t};return this.dispatchEvent(new CustomEvent("buildState",{detail:{state:i,options:e}})),i}}class l extends EventTarget{constructor(t){super(),this.naja=t,this.storages={off:new d(t),history:new c,session:new h},t.uiHandler.addEventListener("interaction",this.configureCache.bind(this)),t.historyHandler.addEventListener("buildState",this.buildHistoryState.bind(this)),t.historyHandler.addEventListener("restoreState",this.restoreHistoryState.bind(this))}resolveStorage(t){let e;return e=!0===t||void 0===t?"history":!1===t?"off":t,this.storages[e]}configureCache(t){var e,i,n,s,a,o,r;const{element:l,options:d}=t.detail;if(l&&(l.hasAttribute("data-naja-snippet-cache")||(null===(e=l.form)||void 0===e?void 0:e.hasAttribute("data-naja-snippet-cache"))||l.hasAttribute("data-naja-history-cache")||(null===(i=l.form)||void 0===i?void 0:i.hasAttribute("data-naja-history-cache")))){const t=null!==(o=null!==(a=null!==(n=l.getAttribute("data-naja-snippet-cache"))&&void 0!==n?n:null===(s=l.form)||void 0===s?void 0:s.getAttribute("data-naja-snippet-cache"))&&void 0!==a?a:l.getAttribute("data-naja-history-cache"))&&void 0!==o?o:null===(r=l.form)||void 0===r?void 0:r.getAttribute("data-naja-history-cache");d.snippetCache=t}}buildHistoryState(t){const{state:e,options:i}=t.detail;"historyUiCache"in i&&(console.warn("Naja: options.historyUiCache is deprecated, use options.snippetCache instead."),i.snippetCache=i.historyUiCache);const n=o.findSnippets((t=>!(t.hasAttribute("data-naja-history-nocache")||t.hasAttribute("data-history-nocache")||t.hasAttribute("data-naja-snippet-cache")&&"off"===t.getAttribute("data-naja-snippet-cache"))));if(!this.dispatchEvent(new CustomEvent("store",{cancelable:!0,detail:{snippets:n,state:e,options:i}})))return;const s=this.resolveStorage(i.snippetCache);e.snippets={storage:s.type,key:s.store(n)}}restoreHistoryState(t){const{state:e,options:i}=t.detail;if(void 0===e.snippets)return;if(i.snippetCache=e.snippets.storage,!this.dispatchEvent(new CustomEvent("fetch",{cancelable:!0,detail:{state:e,options:i}})))return;const n=this.resolveStorage(i.snippetCache).fetch(e.snippets.key,e,i);null!==n&&this.dispatchEvent(new CustomEvent("restore",{cancelable:!0,detail:{snippets:n,state:e,options:i}}))&&(this.naja.snippetHandler.updateSnippets(n,!0,i),this.naja.scriptLoader.loadScripts(n))}}class d{constructor(t){this.naja=t,this.type="off"}store(){return null}fetch(t,e,i){return this.naja.makeRequest("GET",e.href,null,Object.assign(Object.assign({},i),{history:!1,snippetCache:!1})),null}}class c{constructor(){this.type="history"}store(t){return t}fetch(t){return t}}class h{constructor(){this.type="session"}store(t){const e=Math.random().toString(36).substr(2,6);return window.sessionStorage.setItem(e,JSON.stringify(t)),e}fetch(t){const e=window.sessionStorage.getItem(t);return null===e?null:JSON.parse(e)}}class p{constructor(e){this.loadedScripts=new Set,e.addEventListener("init",(()=>{t((()=>{document.querySelectorAll("script[data-naja-script-id]").forEach((t=>{const e=t.getAttribute("data-naja-script-id");null!==e&&""!==e&&this.loadedScripts.add(e)}))})),e.addEventListener("success",(t=>{const{payload:e}=t.detail;e.snippets&&this.loadScripts(e.snippets)}))}))}loadScripts(t){Object.keys(t).forEach((e=>{const i=t[e];if(!/<script/i.test(i))return;const n=window.document.createElement("div");n.innerHTML=i;const s=n.querySelectorAll("script");for(let t=0;t<s.length;t++){const e=s.item(t),i=e.getAttribute("data-naja-script-id");if(null!==i&&""!==i&&this.loadedScripts.has(i))continue;const n=window.document.createElement("script");if(n.innerHTML=e.innerHTML,e.hasAttributes()){const t=e.attributes;for(let e=0;e<t.length;e++){const i=t[e].name;n.setAttribute(i,t[e].value)}}window.document.head.appendChild(n).parentNode.removeChild(n),null!==i&&""!==i&&this.loadedScripts.add(i)}}))}}class u extends EventTarget{constructor(t,e,i,d,c,h,u){super(),this.VERSION=2,this.initialized=!1,this.extensions=[],this.defaultOptions={},this.uiHandler=new(null!=t?t:n)(this),this.redirectHandler=new(null!=e?e:a)(this),this.snippetHandler=new(null!=i?i:o)(this),this.formsHandler=new(null!=d?d:s)(this),this.historyHandler=new(null!=c?c:r)(this),this.snippetCache=new(null!=h?h:l)(this),this.scriptLoader=new(null!=u?u:p)(this)}registerExtension(t){this.initialized&&t.initialize(this),this.extensions.push(t)}initialize(t={}){if(this.initialized)throw new Error("Cannot initialize Naja, it is already initialized.");this.defaultOptions=this.prepareOptions(t),this.extensions.forEach((t=>t.initialize(this))),this.dispatchEvent(new CustomEvent("init",{detail:{defaultOptions:this.defaultOptions}})),this.initialized=!0}prepareOptions(t){return Object.assign(Object.assign(Object.assign({},this.defaultOptions),t),{fetch:Object.assign(Object.assign({},this.defaultOptions.fetch),null==t?void 0:t.fetch)})}async makeRequest(t,e,i=null,n={}){"string"==typeof e&&(e=new URL(e,location.href)),n=this.prepareOptions(n);const s=new Headers(n.fetch.headers||{}),a=this.transformData(e,t,i),o=new AbortController,r=new Request(e.toString(),Object.assign(Object.assign({credentials:"same-origin"},n.fetch),{method:t,headers:s,body:a,signal:o.signal}));if(r.headers.set("X-Requested-With","XMLHttpRequest"),r.headers.set("Accept","application/json"),!this.dispatchEvent(new CustomEvent("before",{cancelable:!0,detail:{request:r,method:t,url:e.toString(),data:i,options:n}})))return{};const l=window.fetch(r);let d,c;this.dispatchEvent(new CustomEvent("start",{detail:{request:r,promise:l,abortController:o,options:n}}));try{if(d=await l,!d.ok)throw new f(d);c=await d.json()}catch(t){if("AbortError"===t.name)return this.dispatchEvent(new CustomEvent("abort",{detail:{request:r,error:t,options:n}})),this.dispatchEvent(new CustomEvent("complete",{detail:{request:r,response:d,payload:void 0,error:t,options:n}})),{};throw this.dispatchEvent(new CustomEvent("error",{detail:{request:r,response:d,error:t,options:n}})),this.dispatchEvent(new CustomEvent("complete",{detail:{request:r,response:d,payload:void 0,error:t,options:n}})),t}return this.dispatchEvent(new CustomEvent("success",{detail:{request:r,response:d,payload:c,options:n}})),this.dispatchEvent(new CustomEvent("complete",{detail:{request:r,response:d,payload:c,error:void 0,options:n}})),c}appendToQueryString(t,e,i){if(null!=i)if(Array.isArray(i)||Object.getPrototypeOf(i)===Object.prototype)for(const[n,s]of Object.entries(i))this.appendToQueryString(t,`${e}[${n}]`,s);else t.append(e,String(i))}transformData(t,e,i){const n=["GET","HEAD"].includes(e.toUpperCase());if(n&&i instanceof FormData){for(const[e,n]of i)null!=n&&t.searchParams.append(e,String(n));return null}if(null!==i&&Object.getPrototypeOf(i)===Object.prototype||Array.isArray(i)){const e=n?t.searchParams:new URLSearchParams;for(const[t,n]of Object.entries(i))this.appendToQueryString(e,t,n);return n?null:e}return i}}class f extends Error{constructor(t){const e=`HTTP ${t.status}: ${t.statusText}`;super(e),this.name=this.constructor.name,this.stack=new Error(e).stack,this.response=t}}const m=new u;return m.registerExtension(new class{constructor(){this.abortable=!0,this.abortController=null}initialize(t){t.uiHandler.addEventListener("interaction",this.checkAbortable.bind(this)),t.addEventListener("init",this.onInitialize.bind(this)),t.addEventListener("before",this.checkAbortable.bind(this)),t.addEventListener("start",this.saveAbortController.bind(this)),t.addEventListener("complete",this.clearAbortController.bind(this))}onInitialize(){document.addEventListener("keydown",(t=>{null!==this.abortController&&"Escape"===t.key&&!(t.ctrlKey||t.shiftKey||t.altKey||t.metaKey)&&this.abortable&&(this.abortController.abort(),this.abortController=null)}))}checkAbortable(t){var e,i;const{options:n}=t.detail;this.abortable="element"in t.detail?"off"!==(null!==(e=t.detail.element.getAttribute("data-naja-abort"))&&void 0!==e?e:null===(i=t.detail.element.form)||void 0===i?void 0:i.getAttribute("data-naja-abort")):!1!==n.abort,n.abort=this.abortable}saveAbortController(t){const{abortController:e}=t.detail;this.abortController=e}clearAbortController(){this.abortController=null,this.abortable=!0}}),m.registerExtension(new class{constructor(){this.abortControllers=new Map}initialize(t){t.uiHandler.addEventListener("interaction",this.checkUniqueness.bind(this)),t.addEventListener("start",this.abortPreviousRequest.bind(this)),t.addEventListener("complete",this.clearRequest.bind(this))}checkUniqueness(t){var e,i;const{element:n,options:s}=t.detail,a=null!==(e=n.getAttribute("data-naja-unique"))&&void 0!==e?e:null===(i=n.form)||void 0===i?void 0:i.getAttribute("data-naja-unique");s.unique="off"!==a&&(null!=a?a:"default")}abortPreviousRequest(t){var e,i,n;const{abortController:s,options:a}=t.detail;!1!==a.unique&&(null===(i=this.abortControllers.get(null!==(e=a.unique)&&void 0!==e?e:"default"))||void 0===i||i.abort(),this.abortControllers.set(null!==(n=a.unique)&&void 0!==n?n:"default",s))}clearRequest(t){var e;const{request:i,options:n}=t.detail;i.signal.aborted||!1===n.unique||this.abortControllers.delete(null!==(e=n.unique)&&void 0!==e?e:"default")}}),m.Naja=u,m.HttpError=f,m}));
//# sourceMappingURL=Naja.min.js.map
