/*!
 * Splide.js
 * Version  : 3.2.6
 * License  : MIT
 * Copyright: 2021 Naotoshi Fujita
 */
var t,i;t=this,i=function(){"use strict";var s="rtl",e="ttb",u={marginRight:["marginBottom","marginLeft"],autoWidth:["autoHeight"],fixedWidth:["fixedHeight"],paddingLeft:["paddingTop","paddingRight"],paddingRight:["paddingBottom","paddingLeft"],width:["height"],left:["top","right"],right:["bottom","left"],x:["y"],X:["Y"],Y:["X"],ArrowLeft:["ArrowUp","ArrowRight"],ArrowRight:["ArrowDown","ArrowLeft"]};function o(t,i,r){return{resolve:function(t,i){var n=r.direction;return u[t][n!==s||i?n===e?0:-1:1]||t},orient:function(t){return t*(r.direction===s?1:-1)}}}var n="splide",r=n,h=n+"__track",a=n+"__list",f=n+"__slide",c=f+"--clone",t=n+"__arrow",i=n+"__pagination",d="is-active",l={type:"slide",speed:400,waitForTransition:!0,perPage:1,arrows:!0,pagination:!0,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,easing:"cubic-bezier(0.25, 1, 0.5, 1)",drag:!0,direction:"ltr",slideFocus:!0,trimSpace:!0,focusableNodes:"a, button, textarea, input, select, iframe",classes:{slide:f,clone:c,arrows:n+"__arrows",arrow:t,prev:t+"--prev",next:t+"--next",pagination:i,page:i+"__page",spinner:n+"__spinner"},i18n:{prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay"}};function v(t){return null!==t&&"object"==typeof t}function p(t){return Array.isArray(t)}function g(t){return"string"==typeof t}function w(t){return p(t)?t:[t]}function _(t,i){w(t).forEach(i)}function m(t,i){return t.push.apply(t,w(i)),t}var x=Array.prototype;function y(t,i,n){return x.slice.call(t,i,n)}function b(t,i){return t?y(t.children).filter(function(t){return((t=t).msMatchesSelector||t.matches).call(t,i)}):[]}function A(t,i,n){if(t)for(var r=Object.keys(t),r=n?r.reverse():r,s=0;s<r.length;s++){var e=r[s];if("__proto__"!==e&&!1===i(t[e],e))break}return t}function L(r){return y(arguments,1).forEach(function(n){A(n,function(t,i){r[i]=n[i]})}),r}function R(n,t){return A(t,function(t,i){p(t)?n[i]=t.slice():v(t)?n[i]=R(v(n[i])?n[i]:{},t):n[i]=t}),n}function T(t){_(t,function(t){t&&t.parentNode&&t.parentNode.removeChild(t)})}function B(t){return g(t)?t:t?t+"px":""}function M(t,i){if(void 0===i&&(i=""),!t)throw new Error("["+n+"] "+i)}Math.min;var P=Math.max;Math.floor,Math.ceil,Math.abs;function W(t){return t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}var X={};function j(t){var r=t.event,s={},e=[];function i(t,i,r){u(t,i,function(i,n){e=e.filter(function(t){return!!(t[0]!==i||t[1]!==n||r&&t[2]!==r)||(i.removeEventListener(n,t[2],t[3]),!1)})})}function u(t,i,n){_(t,function(t){t&&i.split(" ").forEach(n.bind(null,t))})}function n(){e=e.filter(function(t){return i(t[0],t[1])}),r.offBy(s)}return r.on("destroy",n,s),{on:function(t,i,n){r.on(t,i,s,n)},off:function(t){r.off(t,s)},emit:r.emit,bind:function(t,i,n,r){u(t,i,function(t,i){e.push([t,i,n,r]),t.addEventListener(i,n,r)})},unbind:i,destroy:n}}var F="is-rendered",G={listTag:"ul",slideTag:"li"},O=function(){function t(t,i){this.styles={},this.id=t,this.options=i}var i=t.prototype;return i.rule=function(t,i,n,r){r=this.styles[r=r||"default"]=this.styles[r]||{};(r[t]=r[t]||{})[i]=n},i.build=function(){var n=this,i="";return this.styles.default&&(i+=this.buildSelectors(this.styles.default)),Object.keys(this.styles).sort(function(t,i){return"min"===n.options.mediaQuery?+t-+i:+i-+t}).forEach(function(t){"default"!==t&&(i+="@media screen and (max-width: "+t+"px) {",i+=n.buildSelectors(n.styles[t]),i+="}")}),i},i.buildSelectors=function(t){var n=this,r="";return A(t,function(t,i){i=("#"+n.id+" "+i).trim(),r+=i+" {",A(t,function(t,i){!t&&0!==t||(r+=i+": "+t+";")}),r+="}"}),r},t}();return function(){function t(t,i,n,r){this.slides=[],this.options={},this.breakpoints=[],R(l,r||{}),R(R(this.options,l),i||{}),this.contents=t,this.config=L({},G,n||{}),this.id=this.config.id||(n="splide")+((n=X[n]=(X[n]||0)+1)<10?"0"+n:""+n),this.Style=new O(this.id,this.options),this.Direction=o(0,0,this.options),M(this.contents.length,"Provide at least 1 content."),this.init()}t.clean=function(t){var i=j(t).on,n=t.root,t=(t="."+c,y(n.querySelectorAll(t)));i("mounted",function(){var t,i;T((t=n,(i="style")?b(t,i)[0]:t.firstElementChild))}),T(t)};var i=t.prototype;return i.init=function(){this.parseBreakpoints(),this.initSlides(),this.registerRootStyles(),this.registerTrackStyles(),this.registerSlideStyles(),this.registerListStyles()},i.initSlides=function(){var n=this;m(this.slides,this.contents.map(function(t,i){(t=g(t)?{html:t}:t).styles=t.styles||{},t.attrs=t.attrs||{},n.cover(t);i=n.options.classes.slide+" "+(0===i?d:"");return L(t.attrs,{class:(i+" "+(t.attrs.class||"")).trim(),style:n.buildStyles(t.styles)}),t})),this.isLoop()&&this.generateClones(this.slides)},i.registerRootStyles=function(){var n=this;this.breakpoints.forEach(function(t){var i=t[0],t=t[1];n.Style.rule(" ","max-width",B(t.width),i)})},i.registerTrackStyles=function(){var n=this,r=this.Style,s="."+h;this.breakpoints.forEach(function(t){var i=t[0],t=t[1];r.rule(s,n.resolve("paddingLeft"),n.cssPadding(t,!1),i),r.rule(s,n.resolve("paddingRight"),n.cssPadding(t,!0),i),r.rule(s,"height",n.cssTrackHeight(t),i)})},i.registerListStyles=function(){var n=this,r=this.Style,s="."+a;this.breakpoints.forEach(function(t){var i=t[0],t=t[1];r.rule(s,"transform",n.buildTranslate(t),i),n.cssSlideHeight(t)||r.rule(s,"aspect-ratio",n.cssAspectRatio(t),i)})},i.registerSlideStyles=function(){var n=this,r=this.Style,s="."+f;this.breakpoints.forEach(function(t){var i=t[0],t=t[1];r.rule(s,"width",n.cssSlideWidth(t),i),r.rule(s,"height",n.cssSlideHeight(t)||"100%",i),r.rule(s,n.resolve("marginRight"),B(t.gap)||"0px",i),r.rule(s+" > img","display",t.cover?"none":"inline",i)})},i.buildTranslate=function(t){var i=this.Direction,n=i.resolve,r=i.orient,i=[];return i.push(this.cssOffsetClones(t)),i.push(this.cssOffsetGaps(t)),this.isCenter(t)&&(i.push(this.buildCssValue(r(-50),"%")),i.push.apply(i,this.cssOffsetCenter(t))),i.filter(Boolean).map(function(t){return"translate"+n("X")+"("+t+")"}).join(" ")},i.cssOffsetClones=function(t){var i=this.Direction,n=i.resolve,r=i.orient,s=this.getCloneCount();if(this.isFixedWidth(t)){i=this.parseCssValue(t[n("fixedWidth")]),n=i.value,i=i.unit;return this.buildCssValue(r(n)*s,i)}return r(100*s/t.perPage)+"%"},i.cssOffsetCenter=function(t){var i=this.Direction,n=i.resolve,i=i.orient;if(this.isFixedWidth(t)){var r=this.parseCssValue(t[n("fixedWidth")]),s=r.value,e=r.unit;return[this.buildCssValue(i(s/2),e)]}r=[],s=t.perPage,e=t.gap;return r.push(i(50/s)+"%"),e&&(e=(t=this.parseCssValue(e)).value,t=t.unit,r.push(this.buildCssValue(i((e/s-e)/2),t))),r},i.cssOffsetGaps=function(t){var i=this.getCloneCount();if(i&&t.gap){var n=this.Direction.orient,r=this.parseCssValue(t.gap),s=r.value,r=r.unit;if(this.isFixedWidth(t))return this.buildCssValue(n(s*i),r);t=t.perPage;return this.buildCssValue(n(i/t*s),r)}return""},i.resolve=function(t){return W(this.Direction.resolve(t))},i.cssPadding=function(t,i){t=t.padding,i=this.Direction.resolve(i?"right":"left",!0);return t&&B(t[i]||(v(t)?0:t))||"0px"},i.cssTrackHeight=function(t){var i="";return this.isVertical()&&(M(i=this.cssHeight(t),'"height" is missing.'),i="calc("+i+" - "+this.cssPadding(t,!1)+" - "+this.cssPadding(t,!0)+")"),i},i.cssHeight=function(t){return B(t.height)},i.cssSlideWidth=function(t){return t.autoWidth?"":B(t.fixedWidth)||(this.isVertical()?"":this.cssSlideSize(t))},i.cssSlideHeight=function(t){return B(t.fixedHeight)||(this.isVertical()?t.autoHeight?"":this.cssSlideSize(t):this.cssHeight(t))},i.cssSlideSize=function(t){var i=B(t.gap);return"calc((100%"+(i&&" + "+i)+")/"+(t.perPage||1)+(i&&" - "+i)+")"},i.cssAspectRatio=function(t){t=t.heightRatio;return t?""+1/t:""},i.buildCssValue=function(t,i){return""+t+i},i.parseCssValue=function(t){return g(t)?{value:parseFloat(t)||0,unit:t.replace(/\d*(\.\d*)?/,"")||"px"}:{value:t,unit:"px"}},i.parseBreakpoints=function(){var n=this,t=this.options.breakpoints;this.breakpoints.push(["default",this.options]),t&&A(t,function(t,i){n.breakpoints.push([i,R(R({},n.options),t)])})},i.isFixedWidth=function(t){return!!t[this.Direction.resolve("fixedWidth")]},i.isLoop=function(){return"loop"===this.options.type},i.isCenter=function(t){if("center"===t.focus){if(this.isLoop())return!0;if("slide"===this.options.type)return!this.options.trimSpace}return!1},i.isVertical=function(){return this.options.direction===e},i.buildClasses=function(){var t=this.options;return[r,r+"--"+t.type,r+"--"+t.direction,t.drag&&r+"--draggable",t.isNavigation&&r+"--nav",d,!this.config.hidden&&F].filter(Boolean).join(" ")},i.buildAttrs=function(t){var n="";return A(t,function(t,i){n+=t?" "+W(i)+'="'+t+'"':""}),n.trim()},i.buildStyles=function(t){var n="";return A(t,function(t,i){n+=" "+W(i)+":"+t+";"}),n.trim()},i.renderSlides=function(){var i=this,n=this.config.slideTag;return this.slides.map(function(t){return"<"+n+" "+i.buildAttrs(t.attrs)+">"+(t.html||"")+"</"+n+">"}).join("")},i.cover=function(t){var i=t.styles,t=t.html,t=void 0===t?"":t;!this.options.cover||this.options.lazyLoad||(t=t.match(/<img.*?src\s*=\s*(['"])(.+?)\1.*?>/))&&t[2]&&(i.background="center/cover no-repeat url('"+t[2]+"')")},i.generateClones=function(r){for(var s=this.options.classes,e=this.getCloneCount(),t=r.slice();t.length<e;)m(t,t);m(t.slice(-e).reverse(),t.slice(0,e)).forEach(function(t,i){var n=L({},t.attrs,{class:t.attrs.class+" "+s.clone}),n=L({},t,{attrs:n});i<e?r.unshift(n):r.push(n)})},i.getCloneCount=function(){if(this.isLoop()){var t=this.options;return t.clones?t.clones:P.apply(void 0,this.breakpoints.map(function(t){return t[1].perPage}))*((t.flickMaxPages||1)+1)}return 0},i.renderArrows=function(){var t="";return t+='<div class="'+this.options.classes.arrows+'">',t+=this.renderArrow(!0),t+=this.renderArrow(!1),t+="</div>"},i.renderArrow=function(t){var i=this.options,n=i.classes,i=i.i18n,i={class:n.arrow+" "+(t?n.prev:n.next),type:"button",ariaLabel:t?i.prev:i.next};return"<button "+this.buildAttrs(i)+'><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40"><path d="'+(this.options.arrowPath||"m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z")+'" /></svg></button>'},i.html=function(){var t=this.config,i=t.rootClass,n=t.listTag,r=t.arrows,s=t.beforeTrack,e=t.afterTrack,u=t.slider,o=t.beforeSlider,t=t.afterSlider,h="";return h+='<div id="'+this.id+'" class="'+this.buildClasses()+" "+(i||"")+'">',h+="<style>"+this.Style.build()+"</style>",u&&(h+=o||"",h+='<div class="splide__slider">'),h+=s||"",r&&(h+=this.renderArrows()),h+='<div class="splide__track">',h+="<"+n+' class="splide__list">',h+=this.renderSlides(),h+="</"+n+">",h+="</div>",h+=e||"",u&&(h+="</div>",h+=t||""),h+="</div>"},t}()},"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):(t="undefined"!=typeof globalThis?globalThis:t||self).SplideRenderer=i();
