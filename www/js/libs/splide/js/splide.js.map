{"version": 3, "file": "splide.js", "sources": ["../../src/js/constants/project.ts", "../../src/js/constants/states.ts", "../../src/js/constants/priority.ts", "../../src/js/utils/array/empty/empty.ts", "../../src/js/utils/type/type.ts", "../../src/js/utils/array/toArray/toArray.ts", "../../src/js/utils/array/forEach/forEach.ts", "../../src/js/utils/array/includes/includes.ts", "../../src/js/utils/array/push/push.ts", "../../src/js/utils/array/index.ts", "../../src/js/utils/arrayLike/slice/slice.ts", "../../src/js/utils/arrayLike/find/find.ts", "../../src/js/utils/dom/toggleClass/toggleClass.ts", "../../src/js/utils/dom/addClass/addClass.ts", "../../src/js/utils/dom/append/append.ts", "../../src/js/utils/dom/before/before.ts", "../../src/js/utils/dom/matches/matches.ts", "../../src/js/utils/dom/children/children.ts", "../../src/js/utils/dom/child/child.ts", "../../src/js/utils/object/forOwn/forOwn.ts", "../../src/js/utils/object/assign/assign.ts", "../../src/js/utils/object/merge/merge.ts", "../../src/js/utils/dom/removeAttribute/removeAttribute.ts", "../../src/js/utils/dom/setAttribute/setAttribute.ts", "../../src/js/utils/dom/create/create.ts", "../../src/js/utils/dom/style/style.ts", "../../src/js/utils/dom/display/display.ts", "../../src/js/utils/dom/focus/focus.ts", "../../src/js/utils/dom/getAttribute/getAttribute.ts", "../../src/js/utils/dom/hasClass/hasClass.ts", "../../src/js/utils/dom/rect/rect.ts", "../../src/js/utils/dom/remove/remove.ts", "../../src/js/utils/dom/measure/measure.ts", "../../src/js/utils/dom/parseHtml/parseHtml.ts", "../../src/js/utils/dom/prevent/prevent.ts", "../../src/js/utils/dom/query/query.ts", "../../src/js/utils/dom/queryAll/queryAll.ts", "../../src/js/utils/dom/removeClass/removeClass.ts", "../../src/js/utils/dom/unit/unit.ts", "../../src/js/utils/error/assert/assert.ts", "../../src/js/utils/function/nextTick/nextTick.ts", "../../src/js/utils/function/noop/noop.ts", "../../src/js/utils/function/raf/raf.ts", "../../src/js/utils/math/math/math.ts", "../../src/js/utils/math/approximatelyEqual/approximatelyEqual.ts", "../../src/js/utils/math/between/between.ts", "../../src/js/utils/math/clamp/clamp.ts", "../../src/js/utils/math/sign/sign.ts", "../../src/js/utils/string/format/format.ts", "../../src/js/utils/string/pad/pad.ts", "../../src/js/utils/string/uniqueId/uniqueId.ts", "../../src/js/constructors/EventBus/EventBus.ts", "../../src/js/constants/events.ts", "../../src/js/constructors/EventInterface/EventInterface.ts", "../../src/js/constructors/RequestInterval/RequestInterval.ts", "../../src/js/constructors/State/State.ts", "../../src/js/constructors/Throttle/Throttle.ts", "../../src/js/components/Options/Options.ts", "../../src/js/constants/directions.ts", "../../src/js/components/Direction/Direction.ts", "../../src/js/constants/classes.ts", "../../src/js/components/Elements/Elements.ts", "../../src/js/constants/attributes.ts", "../../src/js/constants/types.ts", "../../src/js/components/Slides/Slide.ts", "../../src/js/components/Slides/Slides.ts", "../../src/js/components/Layout/Layout.ts", "../../src/js/components/Clones/Clones.ts", "../../src/js/components/Move/Move.ts", "../../src/js/components/Controller/Controller.ts", "../../src/js/components/Arrows/path.ts", "../../src/js/components/Arrows/Arrows.ts", "../../src/js/components/Autoplay/Autoplay.ts", "../../src/js/components/Cover/Cover.ts", "../../src/js/components/Scroll/constants.ts", "../../src/js/components/Scroll/Scroll.ts", "../../src/js/components/Drag/constants.ts", "../../src/js/components/Drag/Drag.ts", "../../src/js/components/Keyboard/Keyboard.ts", "../../src/js/components/LazyLoad/constants.ts", "../../src/js/components/LazyLoad/LazyLoad.ts", "../../src/js/components/Pagination/Pagination.ts", "../../src/js/components/Sync/Sync.ts", "../../src/js/components/Wheel/Wheel.ts", "../../src/js/constants/i18n.ts", "../../src/js/constants/defaults.ts", "../../src/js/transitions/Fade/Fade.ts", "../../src/js/transitions/Slide/Slide.ts", "../../src/js/core/Splide/Splide.ts"], "sourcesContent": ["/**\r\n * The project code.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const PROJECT_CODE = 'splide';\r\n\r\n/**\r\n * The data attribute prefix.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const DATA_ATTRIBUTE = `data-${ PROJECT_CODE }`;\r\n", "/**\r\n * Splide has been just created.\r\n */\r\nexport const CREATED = 1;\r\n\r\n/**\r\n * Splide has mounted components.\r\n */\r\nexport const MOUNTED = 2;\r\n\r\n/**\r\n * Splide is ready.\r\n */\r\nexport const IDLE = 3;\r\n\r\n/**\r\n * Splide is moving.\r\n */\r\nexport const MOVING = 4;\r\n\r\n/**\r\n * Splide has been destroyed.\r\n */\r\nexport const DESTROYED = 5;\r\n\r\n/**\r\n * The collection of all states.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const STATES = {\r\n  CREATED,\r\n  MOUNTED,\r\n  IDLE,\r\n  MOVING,\r\n  DESTROYED,\r\n};\r\n", "/**\r\n * The default priority for internal handlers.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const DEFAULT_EVENT_PRIORITY = 10;\r\n\r\n/**\r\n * The default priority for users' handlers.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const DEFAULT_USER_EVENT_PRIORITY = 20;\r\n", "/**\r\n * Empties the array.\r\n *\r\n * @param array - A array to empty.\r\n */\r\nexport function empty( array: any[] ): void {\r\n  array.length = 0;\r\n}\r\n", "/**\r\n * Checks if the given subject is an object or not.\r\n *\r\n * @param subject - A subject to check.\r\n *\r\n * @return `true` if the subject is an object, or otherwise `false`.\r\n */\r\nexport function isObject( subject: unknown ): subject is object {\r\n  return ! isNull( subject ) && typeof subject === 'object';\r\n}\r\n\r\n/**\r\n * Checks if the given subject is an array or not.\r\n *\r\n * @param subject - A subject to check.\r\n *\r\n * @return `true` if the subject is an array, or otherwise `false`.\r\n */\r\nexport function isArray<T>( subject: unknown ): subject is T[] {\r\n  return Array.isArray( subject );\r\n}\r\n\r\n/**\r\n * Checks if the given subject is a function or not.\r\n *\r\n * @param subject - A subject to check.\r\n *\r\n * @return `true` if the subject is a function, or otherwise `false`.\r\n */\r\nexport function isFunction( subject: unknown ): subject is ( ...args: any[] ) => any {\r\n  return typeof subject === 'function';\r\n}\r\n\r\n/**\r\n * Checks if the given subject is a string or not.\r\n *\r\n * @param subject - A subject to check.\r\n *\r\n * @return `true` if the subject is a string, or otherwise `false`.\r\n */\r\nexport function isString( subject: unknown ): subject is string {\r\n  return typeof subject === 'string';\r\n}\r\n\r\n/**\r\n * Checks if the given subject is `undefined` or not.\r\n *\r\n * @param subject - A subject to check.\r\n *\r\n * @return `true` if the subject is `undefined`, or otherwise `false`.\r\n */\r\nexport function isUndefined( subject: unknown ): subject is undefined {\r\n  return typeof subject === 'undefined';\r\n}\r\n\r\n/**\r\n * Checks if the given subject is `null` or not.\r\n *\r\n * @param subject - A subject to check.\r\n *\r\n * @return `true` if the subject is `null`, or otherwise `false`.\r\n */\r\nexport function isNull( subject: unknown ): subject is null {\r\n  return subject === null;\r\n}\r\n\r\n/**\r\n * Checks if the given subject is an HTMLElement or not.\r\n *\r\n * @param subject - A subject to check.\r\n *\r\n * @return `true` if the subject is an HTMLElement instance, or otherwise `false`.\r\n */\r\nexport function isHTMLElement( subject: unknown ): subject is HTMLElement {\r\n  return subject instanceof HTMLElement;\r\n}\r\n\r\n/**\r\n * Checks if the given subject is an HTMLButtonElement or not.\r\n *\r\n * @param subject - A subject to check.\r\n *\r\n * @return `true` if the subject is an HTMLButtonElement, or otherwise `false`.\r\n */\r\nexport function isHTMLButtonElement( subject: unknown ): subject is HTMLButtonElement {\r\n  return subject instanceof HTMLButtonElement;\r\n}\r\n", "import { isArray } from '../../type/type';\r\n\r\n\r\n/**\r\n * Push the provided value to an array if the value is not an array.\r\n *\r\n * @param value - A value to push.\r\n *\r\n * @return An array containing the value, or the value itself if it is already an array.\r\n */\r\nexport function toArray<T>( value: T | T[] ): T[] {\r\n  return isArray( value ) ? value : [ value ];\r\n}\r\n", "import { toArray } from '../toArray/toArray';\r\n\r\n\r\n/**\r\n * The extended `Array#forEach` method that accepts a single value as an argument.\r\n *\r\n * @param values   - A value or values to iterate over.\r\n * @param iteratee - An iteratee function.\r\n */\r\nexport function forEach<T>( values: T | T[], iteratee: ( value: T, index: number, array: T[] ) => void ): void {\r\n  toArray( values ).forEach( iteratee );\r\n}\r\n", "/**\r\n * Checks if the array includes the value or not.\r\n * `Array#includes` is not supported by IE.\r\n *\r\n * @param array - An array.\r\n * @param value - A value to search for.\r\n *\r\n * @return `true` if the array includes the value, or otherwise `false`.\r\n */\r\nexport function includes<T>( array: T[], value: T ): boolean {\r\n  return array.indexOf( value ) > -1;\r\n}\r\n", "import { toArray } from '../toArray/toArray';\r\n\r\n\r\n/**\r\n * Extended `Array#push()` that accepts an item or an array with items.\r\n *\r\n * @param array - An array to push items.\r\n * @param items - An item or items to push.\r\n *\r\n * @return A provided array itself.\r\n */\r\nexport function push<T>( array: T[], items: T | T[] ): T[] {\r\n  array.push( ...toArray( items ) );\r\n  return array;\r\n}\r\n", "export { empty }    from './empty/empty';\r\nexport { forEach }  from './forEach/forEach';\r\nexport { includes } from './includes/includes';\r\nexport { push }     from './push/push';\r\nexport { toArray }  from './toArray/toArray';\r\n\r\nexport const arrayProto = Array.prototype;\r\n", "import { arrayProto } from '../../array';\r\n\r\n\r\n/**\r\n * The slice method for an array-like object.\r\n *\r\n * @param arrayLike - An array-like object.\r\n * @param start     - Optional. A start index.\r\n * @param end       - Optional. A end index.\r\n *\r\n * @return An array with sliced elements.\r\n */\r\nexport function slice<T>( arrayLike: ArrayLike<T>, start?: number, end?: number ): T[] {\r\n  return arrayProto.slice.call( arrayLike, start, end );\r\n}\r\n", "import { slice } from '../slice/slice';\r\n\r\n\r\n/**\r\n * The find method for an array or array-like object, works in IE.\r\n * This method is not performant for a huge array.\r\n *\r\n * @param arrayLike - An array-like object.\r\n * @param predicate - The predicate function to test each element in the object.\r\n *\r\n * @return A found value if available, or otherwise `undefined`.\r\n */\r\nexport function find<T>(\r\n  arrayLike: ArrayLike<T>,\r\n  predicate: ( value: T, index: number, array: T[] ) => any\r\n): T | undefined {\r\n  return slice( arrayLike ).filter( predicate )[ 0 ];\r\n}\r\n", "import { forEach } from '../../array';\r\n\r\n\r\n/**\r\n * Toggles the provided class or classes by following the `add` boolean.\r\n *\r\n * @param elm     - An element whose classes are toggled.\r\n * @param classes - A class or class names.\r\n * @param add     - Whether to add or remove a class.\r\n */\r\nexport function toggleClass( elm: Element, classes: string | string[], add: boolean ): void {\r\n  if ( elm ) {\r\n    forEach( classes, name => {\r\n      if ( name ) {\r\n        elm.classList[ add ? 'add' : 'remove' ]( name );\r\n      }\r\n    } );\r\n  }\r\n}\r\n", "import { isString } from '../../type/type';\r\nimport { toggleClass } from '../toggleClass/toggleClass';\r\n\r\n\r\n/**\r\n * Adds classes to the element.\r\n *\r\n * @param elm     - An element to add classes to.\r\n * @param classes - Classes to add.\r\n */\r\nexport function addClass( elm: Element, classes: string | string[] ): void {\r\n  toggleClass( elm, isString( classes ) ? classes.split( ' ' ) : classes, true );\r\n}\r\n", "import { forEach } from '../../array';\r\n\r\n\r\n/**\r\n * Appends children to the parent element.\r\n *\r\n * @param parent   - A parent element.\r\n * @param children - A child or children to append to the parent.\r\n */\r\nexport function append( parent: Element, children: Node | Node[] ): void {\r\n  forEach( children, parent.appendChild.bind( parent ) );\r\n}\r\n", "import { forEach } from '../../array';\r\n\r\n\r\n/**\r\n * Inserts a node or nodes before the specified reference node.\r\n *\r\n * @param nodes - A node or nodes to insert.\r\n * @param ref   - A reference node.\r\n */\r\nexport function before( nodes: Node | Node[], ref: Node ): void {\r\n  forEach( nodes, node => {\r\n    const parent = ref.parentNode;\r\n\r\n    if ( parent ) {\r\n      parent.insertBefore( node, ref );\r\n    }\r\n  } );\r\n}\r\n", "/**\r\n * Checks if the element can be selected by the provided selector or not.\r\n *\r\n * @param elm      - An element to check.\r\n * @param selector - A selector to test.\r\n *\r\n * @return `true` if the selector matches the element, or otherwise `false`.\r\n */\r\nexport function matches( elm: Element, selector: string ): boolean {\r\n  return ( elm[ 'msMatchesSelector' ] || elm.matches ).call( elm, selector );\r\n}\r\n", "import { slice } from '../../arrayLike';\r\nimport { matches } from '../matches/matches';\r\n\r\n\r\n/**\r\n * Finds children that has the specified tag or class name.\r\n *\r\n * @param parent   - A parent element.\r\n * @param selector - A selector to filter children.\r\n *\r\n * @return An array with filtered children.\r\n */\r\nexport function children<E extends HTMLElement>( parent: HTMLElement, selector: string ): E[] {\r\n  return parent ? slice( parent.children ).filter( child => matches( child, selector ) ) as E[] : [];\r\n}\r\n", "import { children } from '../children/children';\r\n\r\n\r\n/**\r\n * Returns a child element that matches the specified tag or class name.\r\n *\r\n * @param parent   - A parent element.\r\n * @param selector - A selector to filter children.\r\n *\r\n * @return A matched child element if available, or otherwise `undefined`.\r\n */\r\nexport function child<E extends HTMLElement>( parent: HTMLElement, selector?: string ): E | undefined {\r\n  return selector ? children<E>( parent, selector )[ 0 ] : parent.firstElementChild as E;\r\n}\r\n", "/**\n * Iterates over the provided object by own enumerable keys with calling the iteratee function.\n *\n * @param object   - An object to iterate over.\n * @param iteratee - An iteratee function that takes the value and key as arguments.\n * @param right    - If `true`, the method iterates over the object from the end like `forEachRight()`.\n *\n * @return A provided object itself.\n */\nexport function forOwn<T extends object>(\n  object: T,\n  iteratee: ( value: T[ keyof T ], key: string ) => boolean | void,\n  right?: boolean\n): T {\n  if ( object ) {\n    let keys = Object.keys( object );\n    keys = right ? keys.reverse() : keys;\n\n    for ( let i = 0; i < keys.length; i++ ) {\n      const key = keys[ i ];\n\n      if ( key !== '__proto__' ) {\n        if ( iteratee( object[ key ], key ) === false ) {\n          break;\n        }\n      }\n    }\n  }\n\n  return object;\n}\n", "import { slice } from '../../arrayLike';\r\nimport { forOwn } from '../forOwn/forOwn';\r\n\r\n\r\n/**\r\n * Assign U to T.\r\n *\r\n * @typeParam T - An object to assign to.\r\n * @typeParam U - An object to assign.\r\n *\r\n * @return An assigned object type.\r\n */\r\nexport type Assign<T, U> = Omit<T, keyof U> & U;\r\n\r\nexport function assign<T extends object>( object: T ): T;\r\n\r\n// There is a way to type arguments recursively, but these fixed definitions are enough for this project.\r\nexport function assign<T extends object, U extends object>( object: T, source: U ): Assign<T, U>;\r\n\r\nexport function assign<T extends object, U1 extends object, U2 extends object>(\r\n  object: T, source1: U1, source2: U2\r\n): Assign<Assign<T, U1>, U2>;\r\n\r\nexport function assign<T extends object, U1 extends object, U2 extends object, U3 extends object>(\r\n  object: T, source1: U1, source2: U2, source3: U3\r\n): Assign<Assign<Assign<T, U1>, U2>, U3>;\r\n\r\n/**\r\n * Assigns all own enumerable properties of all source objects to the provided object.\r\n * `undefined` in source objects will be skipped.\r\n *\r\n * @param object - An object to assign properties to.\r\n *\r\n * @return An object assigned properties of the sources to.\r\n */\r\nexport function assign<T extends object>( object: T ): any {\r\n  // eslint-disable-next-line prefer-rest-params, prefer-spread\r\n  slice( arguments, 1 ).forEach( source => {\r\n    forOwn( source, ( value, key ) => {\r\n      object[ key ] = source[ key ];\r\n    } );\r\n  } );\r\n\r\n  return object;\r\n}\r\n", "import { isArray, isObject } from '../../type/type';\r\nimport { forOwn } from '../forOwn/forOwn';\r\n\r\n\r\n/**\r\n * Merges U to T.\r\n *\r\n * @typeParam T - An object to merge to.\r\n * @typeParam U - An object to to.\r\n *\r\n * @return An merged object type.\r\n */\r\nexport type Merge<T extends object, U extends object> = Omit<T, keyof U> & {\r\n  [ K in ( keyof T & keyof U ) ]: U[ K ] extends object\r\n    ? U[ K ] extends any[]\r\n      ? T[ K ] extends any[]\r\n        ? Array<T[ K ][ number ] | U[ K ][ number ]>\r\n        : U[ K ]\r\n      : T[ K ] extends object\r\n        ? Merge<T[ K ], U[ K ]> extends infer A ? Cast<A, object> : never\r\n        : U[ K ]\r\n    : U[ K ];\r\n} & Omit<U, keyof T>;\r\n\r\ntype Cast<T, U> = T extends U ? T : U;\r\n\r\n/**\r\n * Recursively merges source properties to the object.\r\n * Be aware that this method does not merge arrays. They are just duplicated by `slice()`.\r\n *\r\n * @param object - An object to merge properties to.\r\n * @param source - A source object to merge properties from.\r\n *\r\n * @return A new object with merged properties.\r\n */\r\nexport function merge<T extends object, U extends object>( object: T, source: U ): Merge<T, U> {\r\n  forOwn( source, ( value, key ) => {\r\n    if ( isArray( value ) ) {\r\n      object[ key ] = value.slice();\r\n    } else if ( isObject( value ) ) {\r\n      object[ key ] = merge( isObject( object[ key ] ) ? object[ key ] : {}, value );\r\n    } else {\r\n      object[ key ] = value;\r\n    }\r\n  } );\r\n\r\n  return object as Merge<T, U>;\r\n}\r\n", "import { forEach } from '../../array';\r\n\r\n\r\n/**\r\n * Removes attributes from the element.\r\n *\r\n * @param elm   - An element.\r\n * @param attrs - An attribute or attributes to remove.\r\n */\r\nexport function removeAttribute( elm: Element, attrs: string | string[] ): void {\r\n  if ( elm ) {\r\n    forEach( attrs, attr => {\r\n      elm.removeAttribute( attr );\r\n    } );\r\n  }\r\n}\r\n", "import { forOwn } from '../../object';\r\nimport { isNull, isObject } from '../../type/type';\r\nimport { removeAttribute } from '../removeAttribute/removeAttribute';\r\n\r\n\r\nexport function setAttribute( elm: Element, attr: string, value: string | number | boolean ): void;\r\nexport function setAttribute( elm: Element, attrs: Record<string, string | number | boolean> ): void;\r\n\r\nexport function setAttribute(\r\n  elm: Element,\r\n  attrs: string | Record<string, string | number | boolean>,\r\n  value?: string | number | boolean\r\n): void {\r\n  if ( isObject( attrs ) ) {\r\n    forOwn( attrs, ( value, name ) => {\r\n      setAttribute( elm, name, value );\r\n    } );\r\n  } else {\r\n    isNull( value ) ? removeAttribute( elm, attrs ) : elm.setAttribute( attrs, String( value ) );\r\n  }\r\n}\r\n", "import { isString } from '../../type/type';\r\nimport { addClass } from '../addClass/addClass';\r\nimport { append } from '../append/append';\r\nimport { setAttribute } from '../setAttribute/setAttribute';\r\n\r\n\r\nexport function create<K extends keyof HTMLElementTagNameMap>(\r\n  tag: K,\r\n  attrs?: Record<string, string | number | boolean> | string | string[],\r\n  parent?: HTMLElement\r\n): HTMLElementTagNameMap[ K ];\r\n\r\nexport function create(\r\n  tag: string,\r\n  attrs?: Record<string, string | number | boolean> | string | string[],\r\n  parent?: HTMLElement\r\n): HTMLElement;\r\n\r\n/**\r\n * Creates a HTML element.\r\n *\r\n * @param tag    - A tag name.\r\n * @param attrs  - Optional. An object with attributes to apply the created element to, or a string with classes.\r\n * @param parent - Optional. A parent element where the created element is appended.\r\n */\r\nexport function create<K extends keyof HTMLElementTagNameMap>(\r\n  tag: K,\r\n  attrs?: Record<string, string | number | boolean> | string,\r\n  parent?: HTMLElement\r\n): HTMLElementTagNameMap[ K ] {\r\n  const elm = document.createElement( tag );\r\n\r\n  if ( attrs ) {\r\n    isString( attrs ) ? addClass( elm, attrs ) : setAttribute( elm, attrs );\r\n  }\r\n\r\n  parent && append( parent, elm );\r\n\r\n  return elm;\r\n}\r\n", "import { isNull, isUndefined } from '../../type/type';\n\n\nexport function style<K extends keyof CSSStyleDeclaration>(\n  elm: HTMLElement,\n  prop: K,\n): CSSStyleDeclaration[ K ];\n\nexport function style(\n  elm: HTMLElement,\n  prop: string,\n): string;\n\nexport function style(\n  elm: HTMLElement,\n  prop: string,\n  value: string | number\n): void;\n\n\n/**\n * Applies inline styles to the provided element by an object literal.\n *\n * @param elm   - An element to apply styles to.\n * @param prop  - An object literal with styles or a property name.\n * @param value - A value to set.\n */\nexport function style(\n  elm: HTMLElement,\n  prop: string,\n  value?: string | number\n): string | void {\n  if ( isUndefined( value ) ) {\n    return getComputedStyle( elm )[ prop ];\n  }\n\n  if ( ! isNull( value ) ) {\n    const { style } = elm;\n    value = `${ value }`;\n\n    if ( style[ prop ] !== value ) {\n      style[ prop ] = value;\n    }\n  }\n}\n", "import { style } from '../style/style';\n\n\n/**\n * Sets the `display` CSS value to the element.\n *\n * @param elm     - An element to set a new value to.\n * @param display - A new `display` value.\n */\nexport function display( elm: HTMLElement, display: string ): void {\n  style( elm, 'display', display );\n}\n", "/**\n * Focuses the provided element without scrolling the ascendant element.\n *\n * @param elm - An element to focus.\n */\nexport function focus( elm: HTMLElement ): void {\n  elm[ 'setActive' ] && elm[ 'setActive' ]() || elm.focus( { preventScroll: true } );\n}\n", "/**\r\n * Returns the specified attribute value.\r\n *\r\n * @param elm  - An element.\r\n * @param attr - An attribute to get.\r\n */\r\nexport function getAttribute( elm: Element, attr: string ): string {\r\n  return elm.getAttribute( attr );\r\n}\r\n", "/**\r\n * Checks if the element contains the specified class or not.\r\n *\r\n * @param elm       - An element to check.\r\n * @param className - A class name that may be contained by the element.\r\n *\r\n * @return `true` if the element contains the class, or otherwise `false`.\r\n */\r\nexport function hasClass( elm: Element, className: string ): boolean {\r\n  return elm && elm.classList.contains( className );\r\n}\r\n", "/**\r\n * Returns a DOMRect object of the provided element.\r\n *\r\n * @param target - An element.\r\n */\r\nexport function rect( target: Element ): DOMRect {\r\n  return target.getBoundingClientRect();\r\n}\r\n", "import { forEach } from '../../array';\r\n\r\n\r\n/**\r\n * Removes the provided node from its parent.\r\n *\r\n * @param nodes - A node or nodes to remove.\r\n */\r\nexport function remove( nodes: Node | Node[] ): void {\r\n  forEach( nodes, node => {\r\n    if ( node && node.parentNode ) {\r\n      node.parentNode.removeChild( node );\r\n    }\r\n  } );\r\n}\r\n", "import { isString } from '../../type/type';\r\nimport { create } from '../create/create';\r\nimport { rect } from '../rect/rect';\r\nimport { remove } from '../remove/remove';\r\n\r\n\r\n/**\r\n * Attempts to convert the provided value to pixel as the relative value to the parent element.\r\n *\r\n * @param parent - A parent element.\r\n * @param value  - A value to convert.\r\n *\r\n * @return A converted value in pixel. Unhandled values will become 0.\r\n */\r\nexport function measure( parent: HTMLElement, value: number | string ): number {\r\n  if ( isString( value ) ) {\r\n    const div = create( 'div', { style: `width: ${ value }; position: absolute;` }, parent );\r\n    value = rect( div ).width;\r\n    remove( div );\r\n  }\r\n\r\n  return value;\r\n}\r\n", "import { child } from '../child/child';\r\n\r\n\r\n/**\r\n * Parses the provided HTML string and returns the first element.\r\n *\r\n * @param html - An HTML string to parse.\r\n *\r\n * @return An Element on success, or otherwise `undefined`.\r\n */\r\nexport function parseHtml<E extends HTMLElement>( html: string ): E | undefined {\r\n  return child<E>( new DOMParser().parseFromString( html, 'text/html' ).body );\r\n}\r\n", "/**\r\n * Call the `preventDefault()` of the provided event.\r\n *\r\n * @param e               - An Event object.\r\n * @param stopPropagation - Optional. Whether to stop the event propagation or not.\r\n */\r\nexport function prevent( e: Event, stopPropagation?: boolean ): void {\r\n  e.preventDefault();\r\n\r\n  if ( stopPropagation ) {\r\n    e.stopPropagation();\r\n    e.stopImmediatePropagation();\r\n  }\r\n}\r\n", "/**\r\n * Returns an element that matches the provided selector.\r\n *\r\n * @param parent   - A parent element to start searching from.\r\n * @param selector - A selector to query.\r\n *\r\n * @return A found element or `null`.\r\n */\r\nexport function query<E extends Element = Element>( parent: Element | Document, selector: string ): E | null {\r\n  return parent && parent.querySelector( selector );\r\n}\r\n", "import { slice } from '../../arrayLike';\r\n\r\n\r\n/**\r\n * Returns elements that match the provided selector.\r\n *\r\n * @param parent   - A parent element to start searching from.\r\n * @param selector - A selector to query.\r\n *\r\n * @return An array with matched elements.\r\n */\r\nexport function queryAll<E extends Element = Element>( parent: Element | Document, selector: string ): E[] {\r\n  return slice<E>( parent.querySelectorAll( selector ) );\r\n}\r\n", "import { toggleClass } from '../toggleClass/toggleClass';\r\n\r\n\r\n/**\r\n * Removes classes from the element.\r\n *\r\n * @param elm     - An element to remove classes from.\r\n * @param classes - Classes to remove.\r\n */\r\nexport function removeClass( elm: Element, classes: string | string[] ): void {\r\n  toggleClass( elm, classes, false );\r\n}\r\n", "import { isString } from '../../type/type';\r\n\r\n\r\n/**\r\n * Appends `px` to the provided number.\r\n * If the value is already string, just returns it.\r\n *\r\n * @param value - A value to append `px` to.\r\n *\r\n * @return A string with the CSS unit.\r\n */\r\nexport function unit( value: number | string ): string {\r\n  return isString( value ) ? value : value ? `${ value }px` : '';\r\n}\r\n", "import { PROJECT_CODE } from '../../../constants/project';\r\n\r\n\r\n/**\r\n * Throws an error if the provided condition is falsy.\r\n *\r\n * @param condition - If falsy, an error is thrown.\r\n * @param message   - Optional. A message to display.\r\n */\r\nexport function assert( condition: any, message = '' ): void {\r\n  if ( ! condition ) {\r\n    throw new Error( `[${ PROJECT_CODE }] ${ message }` );\r\n  }\r\n}\r\n", "import { AnyFunction } from '../../../types';\r\n\r\n\r\n/**\r\n * Invokes the callback on the next tick.\r\n *\r\n * @param callback - A callback function.\r\n */\r\nexport function nextTick( callback: AnyFunction ): void {\r\n  setTimeout( callback );\r\n}\r\n", "/**\r\n * No operation.\r\n */\r\nexport const noop = (): void => {}; // eslint-disable-line no-empty-function, @typescript-eslint/no-empty-function\r\n", "/**\r\n * The arias of `window.requestAnimationFrame()`.\r\n */\r\nexport function raf( func: FrameRequestCallback ): number {\r\n  return requestAnimationFrame( func );\r\n}\r\n", "export const { min, max, floor, ceil, abs } = Math;\r\n", "import { abs } from '../math/math';\r\n\r\n\r\n/**\r\n * Checks if the provided 2 numbers are approximately equal or not.\r\n *\r\n * @param x       - A number.\r\n * @param y       - Another number to compare.\r\n * @param epsilon - An accuracy that defines the approximation.\r\n *\r\n * @return `true` if 2 numbers are considered to be equal, or otherwise `false`.\r\n */\r\nexport function approximatelyEqual( x: number, y: number, epsilon: number ): boolean {\r\n  return abs( x - y ) < epsilon;\r\n}\r\n", "import { max, min } from '../math/math';\r\n\r\n\r\n/**\r\n * Checks if the subject number is between `minOrMax` and `maxOrMin`.\r\n *\r\n * @param number    - A subject number to check.\r\n * @param minOrMax  - A min or max number.\r\n * @param maxOrMin  - A max or min number.\r\n * @param exclusive - Optional. Whether to exclude `x` or `y`.\r\n */\r\nexport function between( number: number, minOrMax: number, maxOrMin: number, exclusive?: boolean ): boolean {\r\n  const minimum = min( minOrMax, maxOrMin );\r\n  const maximum = max( minOrMax, maxOrMin );\r\n  return exclusive ? minimum < number && number < maximum : minimum <= number && number <= maximum;\r\n}\r\n", "import { max, min } from '../math/math';\r\n\r\n\r\n/**\r\n * Clamps a number.\r\n *\r\n * @param number - A subject number to check.\r\n * @param x      - A min or max number.\r\n * @param y      - A min or max number.\r\n */\r\nexport function clamp( number: number, x: number, y: number ): number {\r\n  const minimum = min( x, y );\r\n  const maximum = max( x, y );\r\n  return min( max( minimum, number ), maximum );\r\n}\r\n", "/**\r\n * Returns the sign of the provided number.\r\n *\r\n * @param x - A number.\r\n *\r\n * @return `1` for positive numbers, `-1` for negative numbers, or `0` for `0`.\r\n */\r\nexport function sign( x: number ): number {\r\n  return +( x > 0 ) - +( x < 0 );\r\n}\r\n", "import { forEach } from '../../array';\r\n\r\n\r\n/**\r\n * Formats a string.\r\n *\r\n * @param string       - A string to format.\r\n * @param replacements - A replacement or replacements.\r\n *\r\n * @return A formatted string.\r\n */\r\nexport function format( string: string, replacements: string | number | Array<string | number> ): string {\r\n  forEach( replacements, replacement => {\r\n    string = string.replace( '%s', `${ replacement }` );\r\n  } );\r\n\r\n  return string;\r\n}\r\n", "/**\r\n * Pads the number with 0.\r\n *\r\n * @param number - A number to pad.\r\n *\r\n * @return string - Padded number.\r\n */\r\nexport function pad( number: number ): string {\r\n  return number < 10 ? `0${ number }` : `${ number }`;\r\n}\r\n", "import { pad } from '../pad/pad';\r\n\r\n\r\n/**\r\n * Stores unique IDs.\r\n *\r\n * @since 3.0.0\r\n */\r\nconst ids: Record<string, number> = {};\r\n\r\n/**\r\n * Returns a sequential unique ID as \"{ prefix }-{ number }\".\r\n *\r\n * @param prefix - A prefix for the ID.\r\n */\r\nexport function uniqueId( prefix: string ): string {\r\n  return `${ prefix }${ pad( ( ids[ prefix ] = ( ids[ prefix ] || 0 ) + 1 ) ) }`;\r\n}\r\n", "import { DEFAULT_EVENT_PRIORITY } from '../../constants/priority';\nimport { AnyFunction } from '../../types';\nimport { forOwn, push, slice, toArray } from '../../utils';\n\n\n/**\n * The interface for the EventBus instance.\n *\n * @since 3.0.0\n */\nexport interface EventBusObject {\n  on( events: string | string[], callback: EventBusCallback, key?: object, priority?: number ): void;\n  off( events: string | string[], key?: object ): void;\n  offBy( key: object ): void;\n  emit( event: string, ...args: any[] ): void;\n  destroy(): void;\n}\n\n/**\n * The interface for each event handler object.\n *\n * @since 3.0.0\n */\nexport interface EventHandler {\n  _event: string;\n  _callback: AnyFunction;\n  _namespace: string;\n  _priority: number;\n  _key?: object;\n}\n\n/**\n * The type for a callback function of the EventBus.\n *\n * @since 3.0.0\n */\nexport type EventBusCallback = AnyFunction;\n\n/**\n * The constructor to provided a simple event system.\n *\n * @since 3.0.0\n *\n * @return An EventBus object.\n */\nexport function EventBus(): EventBusObject {\n  /**\n   * The collection of registered handlers.\n   */\n  let handlers: Record<string, EventHandler[]> = {};\n\n  /**\n   * Registers an event handler.\n   *\n   * @param events   - An event name or names separated by spaces. Use a dot(.) to add a namespace.\n   * @param callback - A callback function to register.\n   * @param key      - Optional. An object for an identifier of the handler.\n   * @param priority - Optional. A priority number for the order in which the callbacks are invoked.\n   *                   Lower numbers correspond with earlier execution. The default value is 10.\n   */\n  function on(\n    events: string | string[],\n    callback: EventBusCallback,\n    key?: object,\n    priority = DEFAULT_EVENT_PRIORITY\n  ): void {\n    forEachEvent( events, ( event, namespace ) => {\n      handlers[ event ] = handlers[ event ] || [];\n\n      push( handlers[ event ], {\n        _event    : event,\n        _callback : callback,\n        _namespace: namespace,\n        _priority : priority,\n        _key      : key,\n      } ).sort( ( handler1, handler2 ) => handler1._priority - handler2._priority );\n    } );\n  }\n\n  /**\n   * Removes event handlers registered by `on()`.\n   * If only the event name is provided, all handlers that associate with the event are removed.\n   * If the event name and namespace are specified, handlers that associate with the event and namespace are removed.\n   *\n   * @param events - An event name or names separated by spaces. Use a dot(.) to add a namespace.\n   * @param key    - Optional. An object for an identifier of the handler.\n   */\n  function off( events: string | string[], key?: object ): void {\n    forEachEvent( events, ( event, namespace ) => {\n      const eventHandlers = handlers[ event ];\n\n      handlers[ event ] = eventHandlers && eventHandlers.filter( handler => {\n        return handler._key ? handler._key !== key : key || handler._namespace !== namespace;\n      } );\n    } );\n  }\n\n  /**\n   * Removes all handlers locked by the specified key.\n   *\n   * @param key - A key.\n   */\n  function offBy( key: object ): void {\n    forOwn( handlers, ( eventHandlers, event ) => {\n      off( event, key );\n    } );\n  }\n\n  /**\n   * Triggers callback functions.\n   * This accepts additional arguments and passes them to callbacks.\n   *\n   * @param event - An event name.\n   */\n  function emit( event: string ): void {\n    ( handlers[ event ] || [] ).forEach( handler => {\n      // eslint-disable-next-line prefer-rest-params, prefer-spread\n      handler._callback.apply( handler, slice( arguments, 1 ) );\n    } );\n  }\n\n  /**\n   * Removes all handlers.\n   */\n  function destroy(): void {\n    handlers = {};\n  }\n\n  /**\n   * Parses provided events and iterates over them.\n   *\n   * @param events   - An event or events.\n   * @param iteratee - An iteratee function.\n   */\n  function forEachEvent( events: string | string[], iteratee: ( event: string, namespace: string ) => void ): void {\n    toArray( events ).join( ' ' ).split( ' ' ).forEach( eventNS => {\n      const fragments = eventNS.split( '.' );\n      iteratee( fragments[ 0 ], fragments[ 1 ] );\n    } );\n  }\n\n  return {\n    on,\n    off,\n    offBy,\n    emit,\n    destroy,\n  };\n}\n", "export const EVENT_MOUNTED            = 'mounted';\r\nexport const EVENT_READY              = 'ready';\r\nexport const EVENT_MOVE               = 'move';\r\nexport const EVENT_MOVED              = 'moved';\r\nexport const EVENT_CLICK              = 'click';\r\nexport const EVENT_ACTIVE             = 'active';\r\nexport const EVENT_INACTIVE           = 'inactive';\r\nexport const EVENT_VISIBLE            = 'visible';\r\nexport const EVENT_HIDDEN             = 'hidden';\r\nexport const EVENT_SLIDE_KEYDOWN      = 'slide:keydown';\r\nexport const EVENT_REFRESH            = 'refresh';\r\nexport const EVENT_UPDATED            = 'updated';\r\nexport const EVENT_RESIZE             = 'resize';\r\nexport const EVENT_RESIZED            = 'resized';\r\nexport const EVENT_REPOSITIONED       = 'repositioned';\r\nexport const EVENT_DRAG               = 'drag';\r\nexport const EVENT_DRAGGING           = 'dragging';\r\nexport const EVENT_DRAGGED            = 'dragged';\r\nexport const EVENT_SCROLL             = 'scroll';\r\nexport const EVENT_SCROLLED           = 'scrolled';\r\nexport const EVENT_DESTROY            = 'destroy';\r\nexport const EVENT_ARROWS_MOUNTED     = 'arrows:mounted';\r\nexport const EVENT_ARROWS_UPDATED     = 'arrows:updated';\r\nexport const EVENT_PAGINATION_MOUNTED = 'pagination:mounted';\r\nexport const EVENT_PAGINATION_UPDATED = 'pagination:updated';\r\nexport const EVENT_NAVIGATION_MOUNTED = 'navigation:mounted';\r\nexport const EVENT_AUTOPLAY_PLAY      = 'autoplay:play';\r\nexport const EVENT_AUTOPLAY_PLAYING   = 'autoplay:playing';\r\nexport const EVENT_AUTOPLAY_PAUSE     = 'autoplay:pause';\r\nexport const EVENT_LAZYLOAD_LOADED    = 'lazyload:loaded';\r\n\r\n", "import { EVENT_DESTROY } from '../../constants/events';\nimport { Splide } from '../../core/Splide/Splide';\nimport { AnyFunction, EventMap } from '../../types';\nimport { forEach } from '../../utils';\nimport { EventBusCallback } from '../EventBus/EventBus';\n\n\n/**\n * The interface for the EventInterface object.\n *\n * @since 3.0.0\n */\nexport interface EventInterfaceObject {\n  on<K extends keyof EventMap>( event: K, callback: EventMap[ K ], priority?: number ): void;\n  on( events: string | string[], callback: EventBusCallback, priority?: number ): void;\n  off<K extends keyof EventMap>( events: K | K[] | string | string[] ): void;\n  emit<K extends keyof EventMap>( event: K, ...args: Parameters<EventMap[ K ]> ): void\n  emit( event: string, ...args: any[] ): void;\n  bind(\n    target: Element | Window | Document | Array<Element | Window | Document>,\n    events: string,\n    callback: AnyFunction,\n    options?: AddEventListenerOptions\n  ): void\n  unbind(\n    target: Element | Window | Document | Array<Element | Window | Document>,\n    events: string,\n    callback?: AnyFunction,\n  ): void;\n  destroy(): void;\n}\n\n/**\n * The type for event targets.\n *\n * @since 3.0.0\n */\ntype EventTarget = Element | Window | Document;\n\n/**\n * The function that provides interface for internal and native events.\n *\n * @since 3.0.0\n *\n * @param Splide - A Splide instance.\n *\n * @return A collection of interface functions.\n */\nexport function EventInterface( Splide: Splide ): EventInterfaceObject {\n  /**\n   * Holds the event object.\n   */\n  const { event } = Splide;\n\n  /**\n   * The key for events.\n   */\n  const key = {};\n\n  /**\n   * Stores all handlers that listen to native events.\n   */\n  let listeners: [ EventTarget, string, AnyFunction, AddEventListenerOptions? ][] = [];\n\n  /**\n   * Registers an event handler with an unique key.\n   * It can only be removed by `off()` method below.\n   *\n   * @param events   - An event name or names separated by spaces. Use a dot(.) to add a namespace.\n   * @param callback - A callback function to register.\n   * @param priority - Optional. A priority number for the order in which the callbacks are invoked.\n   *                   Lower numbers correspond with earlier execution. The default value is 10.\n   */\n  function on( events: string | string[], callback: EventBusCallback, priority?: number ): void {\n    event.on( events, callback, key, priority );\n  }\n\n  /**\n   * Removes event handlers registered by `on()`.\n   *\n   * @param events - An event name or names separated by spaces. Use a dot(.) to add a namespace.\n   */\n  function off( events: string | string[] ): void {\n    event.off( events, key );\n  }\n\n  /**\n   * Listens to native events.\n   * Splide#destory() will remove all registered listeners.\n   *\n   * @param targets  - A target element, the window object or the document object.\n   * @param events   - An event or events to listen to.\n   * @param callback - A callback function.\n   * @param options  - Optional. The options to pass to the `addEventListener` function.\n   */\n  function bind(\n    targets: EventTarget | EventTarget[],\n    events: string,\n    callback: AnyFunction,\n    options?: AddEventListenerOptions\n  ): void {\n    forEachEvent( targets, events, ( target, event ) => {\n      listeners.push( [ target, event, callback, options ] );\n      target.addEventListener( event, callback, options );\n    } );\n  }\n\n  /**\n   * Removes the event handler.\n   *\n   * @param targets  - A target element, the window object or the document object.\n   * @param events   - An event name or names to remove.\n   * @param callback - Optional. Specify the callback to remove.\n   */\n  function unbind( targets: EventTarget | EventTarget[], events: string, callback?: AnyFunction ): void {\n    forEachEvent( targets, events, ( target, event ) => {\n      listeners = listeners.filter( listener => {\n        if ( listener[ 0 ] === target && listener[ 1 ] === event && ( ! callback || listener[ 2 ] === callback ) ) {\n          target.removeEventListener( event, listener[ 2 ], listener[ 3 ] );\n          return false;\n        }\n\n        return true;\n      } );\n    } );\n  }\n\n  /**\n   * Iterates over each target and event.\n   *\n   * @param targets  - A target element, the window object or the document object.\n   * @param events   - An event name or names.\n   * @param iteratee - An iteratee function.\n   */\n  function forEachEvent(\n    targets: EventTarget | EventTarget[],\n    events: string,\n    iteratee: ( target: EventTarget, event: string ) => void\n  ): void {\n    forEach( targets, target => {\n      if ( target ) {\n        events.split( ' ' ).forEach( iteratee.bind( null, target ) );\n      }\n    } );\n  }\n\n  /**\n   * Removes all listeners.\n   */\n  function destroy(): void {\n    listeners = listeners.filter( data => unbind( data[ 0 ], data[ 1 ] ) );\n    event.offBy( key );\n  }\n\n  /**\n   * Invokes destroy when the slider is destroyed.\n   */\n  event.on( EVENT_DESTROY, destroy, key );\n\n  return {\n    on,\n    off,\n    emit: event.emit,\n    bind,\n    unbind,\n    destroy,\n  };\n}\n", "import { raf } from '../../utils';\r\n\r\n\r\n/**\r\n * The interface for the returning value of the RequestInterval.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport interface RequestIntervalInterface {\r\n  start( resume?: boolean ): void;\r\n  pause(): void;\r\n  rewind(): void;\r\n  cancel(): void;\r\n  isPaused(): boolean;\r\n}\r\n\r\n/**\r\n * Requests interval like the native `setInterval()` with using `requestAnimationFrame`.\r\n *\r\n * @since 3.0.0\r\n *\r\n * @param interval   - The interval duration in milliseconds.\r\n * @param onInterval - The callback fired on every interval.\r\n * @param onUpdate   - Optional. Called on every animation frame, taking the progress rate.\r\n * @param limit      - Optional. Limits the number of interval.\r\n */\r\nexport function RequestInterval(\r\n  interval: number,\r\n  onInterval: () => void,\r\n  onUpdate?: ( rate: number ) => void,\r\n  limit?: number\r\n): RequestIntervalInterface {\r\n  const { now } = Date;\r\n\r\n  /**\r\n   * The time when the interval starts.\r\n   */\r\n  let startTime: number;\r\n\r\n  /**\r\n   * The current progress rate.\r\n   */\r\n  let rate = 0;\r\n\r\n  /**\r\n   * The animation frame ID.\r\n   */\r\n  let id: number;\r\n\r\n  /**\r\n   * Indicates whether the interval is currently paused or not.\r\n   */\r\n  let paused = true;\r\n\r\n  /**\r\n   * The loop count. This only works when the `limit` argument is provided.\r\n   */\r\n  let count = 0;\r\n\r\n  /**\r\n   * The update function called on every animation frame.\r\n   */\r\n  function update(): void {\r\n    if ( ! paused ) {\r\n      const elapsed = now() - startTime;\r\n\r\n      if ( elapsed >= interval ) {\r\n        rate      = 1;\r\n        startTime = now();\r\n      } else {\r\n        rate = elapsed / interval;\r\n      }\r\n\r\n      if ( onUpdate ) {\r\n        onUpdate( rate );\r\n      }\r\n\r\n      if ( rate === 1 ) {\r\n        onInterval();\r\n\r\n        if ( limit && ++count >= limit ) {\r\n          return pause();\r\n        }\r\n      }\r\n\r\n      raf( update );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Starts the interval.\r\n   *\r\n   * @param resume - Optional. Whether to resume the paused progress or not.\r\n   */\r\n  function start( resume?: boolean ): void {\r\n    ! resume && cancel();\r\n    startTime = now() - ( resume ? rate * interval : 0 );\r\n    paused    = false;\r\n    raf( update );\r\n  }\r\n\r\n  /**\r\n   * Pauses the interval.\r\n   */\r\n  function pause(): void {\r\n    paused = true;\r\n  }\r\n\r\n  /**\r\n   * Rewinds the current progress.\r\n   */\r\n  function rewind(): void {\r\n    startTime = now();\r\n    rate      = 0;\r\n\r\n    if ( onUpdate ) {\r\n      onUpdate( rate );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cancels the interval.\r\n   */\r\n  function cancel() {\r\n    cancelAnimationFrame( id );\r\n    rate   = 0;\r\n    id     = 0;\r\n    paused = true;\r\n  }\r\n\r\n  /**\r\n   * Checks if the interval is paused or not.\r\n   *\r\n   * @return `true` if the interval is paused, or otherwise `false`.\r\n   */\r\n  function isPaused(): boolean {\r\n    return paused;\r\n  }\r\n\r\n  return {\r\n    start,\r\n    rewind,\r\n    pause,\r\n    cancel,\r\n    isPaused,\r\n  };\r\n}\r\n", "import { includes, toArray } from '../../utils';\r\n\r\n\r\n/**\r\n * The interface for the State object.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport interface StateObject {\r\n  set( state: number ): void;\r\n  is( states: number | number[] ): boolean;\r\n}\r\n\r\n/**\r\n * The function providing a super simple state system.\r\n *\r\n * @param initialState - Specifies the initial state.\r\n */\r\nexport function State( initialState: number ): StateObject {\r\n  /**\r\n   * The current state.\r\n   */\r\n  let state = initialState;\r\n\r\n  /**\r\n   * Sets a new state.\r\n   *\r\n   * @param value - A new state value.\r\n   */\r\n  function set( value: number ): void {\r\n    state = value;\r\n  }\r\n\r\n  /**\r\n   * Checks if the current state matches the provided one.\r\n   *\r\n   * @param states - A state to check.\r\n   *\r\n   * @return `true` if the current state is the provided one.\r\n   */\r\n  function is( states: number | number[] ): boolean {\r\n    return includes( toArray( states ), state );\r\n  }\r\n\r\n  return { set, is };\r\n}\r\n", "import { AnyFunction } from '../../types';\r\nimport { RequestInterval, RequestIntervalInterface } from '../RequestInterval/RequestInterval';\r\n\r\n\r\n/**\r\n * The interface for the returning value of the RequestInterval.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport interface ThrottleInstance<F extends AnyFunction> extends Function {\r\n  ( ...args: Parameters<F> ): void;\r\n}\r\n\r\n/**\r\n * Returns the throttled function.\r\n *\r\n * @param func     - A function to throttle.\r\n * @param duration - Optional. Throttle duration in milliseconds.\r\n *\r\n * @return A throttled function.\r\n */\r\nexport function Throttle<F extends AnyFunction>(\r\n  func: F,\r\n  duration?: number\r\n): ThrottleInstance<F> {\r\n  let interval: RequestIntervalInterface;\r\n\r\n  function throttled( this: ThisParameterType<F> ): void {\r\n    if ( ! interval ) {\r\n      interval = RequestInterval( duration || 0, () => {\r\n        // eslint-disable-next-line prefer-rest-params\r\n        func.apply( this, arguments );\r\n        interval = null;\r\n      }, null, 1 );\r\n\r\n      interval.start();\r\n    }\r\n  }\r\n\r\n  return throttled;\r\n}\r\n", "import { DATA_ATTRIBUTE } from '../../constants/project';\r\nimport { DESTROYED } from '../../constants/states';\r\nimport { Throttle } from '../../constructors';\r\nimport { Splide } from '../../core/Splide/Splide';\r\nimport { BaseComponent, Components, Options } from '../../types';\r\nimport { assert, find, getAttribute, merge } from '../../utils';\r\n\r\n\r\n/**\r\n * The interface for the Options component.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport interface OptionsComponent extends BaseComponent {\r\n}\r\n\r\n/**\r\n * The component for managing options.\r\n *\r\n * @since 3.0.0\r\n *\r\n * @param Splide     - A Splide instance.\r\n * @param Components - A collection of components.\r\n * @param options    - Options.\r\n *\r\n * @return An Options component object.\r\n */\r\nexport function Options( Splide: Splide, Components: Components, options: Options ): OptionsComponent {\r\n  /**\r\n   * The throttled `observe` function.\r\n   */\r\n  const throttledObserve = Throttle( observe );\r\n\r\n  /**\r\n   * Keeps the initial options to apply when no matched query exists.\r\n   */\r\n  let initialOptions: Options;\r\n\r\n  /**\r\n   * Stores breakpoints with the MediaQueryList object.\r\n   */\r\n  let points: [ string, MediaQueryList ][];\r\n\r\n  /**\r\n   * Holds the current breakpoint.\r\n   */\r\n  let currPoint: string | undefined;\r\n\r\n  /**\r\n   * Called when the component is constructed.\r\n   */\r\n  function setup(): void {\r\n    try {\r\n      merge( options, JSON.parse( getAttribute( Splide.root, DATA_ATTRIBUTE ) ) );\r\n    } catch ( e ) {\r\n      assert( false, e.message );\r\n    }\r\n\r\n    initialOptions = merge( {}, options );\r\n\r\n    const { breakpoints } = options;\r\n\r\n    if ( breakpoints ) {\r\n      const isMin = options.mediaQuery === 'min';\r\n\r\n      points = Object.keys( breakpoints )\r\n        .sort( ( n, m ) => isMin ? +m - +n : +n - +m )\r\n        .map( point => [\r\n          point,\r\n          matchMedia( `(${ isMin ? 'min' : 'max' }-width:${ point }px)` ),\r\n        ] );\r\n\r\n      observe();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Called when the component is mounted.\r\n   */\r\n  function mount(): void {\r\n    if ( points ) {\r\n      addEventListener( 'resize', throttledObserve );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Destroys the component.\r\n   *\r\n   * @param completely - Will be `true` for complete destruction.\r\n   */\r\n  function destroy( completely: boolean ): void {\r\n    if ( completely ) {\r\n      removeEventListener( 'resize', throttledObserve );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Observes breakpoints.\r\n   * The `currPoint` may be `undefined`.\r\n   */\r\n  function observe(): void {\r\n    const item = find( points, item => item[ 1 ].matches ) || [];\r\n\r\n    if ( item[ 0 ] !== currPoint ) {\r\n      onMatch( ( currPoint = item[ 0 ] ) );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Called when the media query matches breakpoints.\r\n   *\r\n   * @param point - A matched point, or `undefined` that means no breakpoint matches a media query.\r\n   */\r\n  function onMatch( point: string | undefined ): void {\r\n    const newOptions = options.breakpoints[ point ] || initialOptions;\r\n\r\n    if ( newOptions.destroy ) {\r\n      Splide.options = initialOptions;\r\n      Splide.destroy( newOptions.destroy === 'completely' );\r\n    } else {\r\n      if ( Splide.state.is( DESTROYED ) ) {\r\n        destroy( true );\r\n        Splide.mount();\r\n      }\r\n\r\n      Splide.options = newOptions;\r\n    }\r\n  }\r\n\r\n  return {\r\n    setup,\r\n    mount,\r\n    destroy,\r\n  };\r\n}\r\n", "/**\r\n * Enumerates slides from left to right.\r\n */\r\nexport const LTR = 'ltr';\r\n\r\n/**\r\n * Enumerates slides from right to left.\r\n */\r\nexport const RTL = 'rtl';\r\n\r\n/**\r\n * Enumerates slides in a col.\r\n */\r\nexport const TTB = 'ttb';\r\n", "import { RTL, TTB } from '../../constants/directions';\r\nimport { Splide } from '../../core/Splide/Splide';\r\nimport { BaseComponent, Components, Options } from '../../types';\r\n\r\n\r\n/**\r\n * The interface for the Direction component.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport interface DirectionComponent extends BaseComponent {\r\n  resolve( prop: string, axisOnly?: boolean ): string;\r\n  orient( value: number ): number;\r\n}\r\n\r\n/**\r\n * The translation map for directions.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const ORIENTATION_MAP = {\r\n  marginRight : [ 'marginBottom', 'marginLeft' ],\r\n  autoWidth   : [ 'autoHeight' ],\r\n  fixedWidth  : [ 'fixedHeight' ],\r\n  paddingLeft : [ 'paddingTop', 'paddingRight' ],\r\n  paddingRight: [ 'paddingBottom', 'paddingLeft' ],\r\n  width       : [ 'height' ],\r\n  left        : [ 'top', 'right' ],\r\n  right       : [ 'bottom', 'left' ],\r\n  x           : [ 'y' ],\r\n  X           : [ 'Y' ],\r\n  Y           : [ 'X' ],\r\n  ArrowLeft   : [ 'ArrowUp', 'ArrowRight' ],\r\n  ArrowRight  : [ 'ArrowDown', 'ArrowLeft' ],\r\n};\r\n\r\n/**\r\n * The component that absorbs the difference among directions.\r\n *\r\n * @since 3.0.0\r\n *\r\n * @param Splide     - A Splide instance.\r\n * @param Components - A collection of components.\r\n * @param options    - Options.\r\n *\r\n * @return A Direction component object.\r\n */\r\nexport function Direction( Splide: Splide, Components: Components, options: Options ): DirectionComponent {\r\n  /**\r\n   * Resolves the provided property name.\r\n   *\r\n   * @param prop     - A property name to translate.\r\n   * @param axisOnly - Optional. If `ture`, returns the same property for LTR and RTL.\r\n   */\r\n  function resolve( prop: string, axisOnly?: boolean ): string {\r\n    const { direction } = options;\r\n    const index = direction === RTL && ! axisOnly ? 1 : direction === TTB ? 0 : -1;\r\n    return ORIENTATION_MAP[ prop ][ index ] || prop;\r\n  }\r\n\r\n  /**\r\n   * Orients the value towards the current direction.\r\n   *\r\n   * @param value - A value to orient.\r\n   *\r\n   * @return The oriented value.\r\n   */\r\n  function orient( value: number ): number {\r\n    return value * ( options.direction === RTL ? 1 : -1 );\r\n  }\r\n\r\n  return {\r\n    resolve,\r\n    orient,\r\n  };\r\n}\r\n", "import { PROJECT_CODE } from './project';\r\n\r\n\r\nexport const CLASS_ROOT            = PROJECT_CODE;\r\nexport const CLASS_SLIDER          = `${ PROJECT_CODE }__slider`;\r\nexport const CLASS_TRACK           = `${ PROJECT_CODE }__track`;\r\nexport const CLASS_LIST            = `${ PROJECT_CODE }__list`;\r\nexport const CLASS_SLIDE           = `${ PROJECT_CODE }__slide`;\r\nexport const CLASS_CLONE           = `${ CLASS_SLIDE }--clone`;\r\nexport const CLASS_CONTAINER       = `${ CLASS_SLIDE }__container`;\r\nexport const CLASS_ARROWS          = `${ PROJECT_CODE }__arrows`;\r\nexport const CLASS_ARROW           = `${ PROJECT_CODE }__arrow`;\r\nexport const CLASS_ARROW_PREV      = `${ CLASS_ARROW }--prev`;\r\nexport const CLASS_ARROW_NEXT      = `${ CLASS_ARROW }--next`;\r\nexport const CLASS_PAGINATION      = `${ PROJECT_CODE }__pagination`;\r\nexport const CLASS_PAGINATION_PAGE = `${ CLASS_PAGINATION }__page`;\r\nexport const CLASS_PROGRESS        = `${ PROJECT_CODE }__progress`;\r\nexport const CLASS_PROGRESS_BAR    = `${ CLASS_PROGRESS }__bar`;\r\nexport const CLASS_AUTOPLAY        = `${ PROJECT_CODE }__autoplay`;\r\nexport const CLASS_PLAY            = `${ PROJECT_CODE }__play`;\r\nexport const CLASS_PAUSE           = `${ PROJECT_CODE }__pause`;\r\nexport const CLASS_SPINNER         = `${ PROJECT_CODE }__spinner`;\r\nexport const CLASS_INITIALIZED     = 'is-initialized';\r\nexport const CLASS_ACTIVE          = 'is-active';\r\nexport const CLASS_PREV            = 'is-prev';\r\nexport const CLASS_NEXT            = 'is-next';\r\nexport const CLASS_VISIBLE         = 'is-visible';\r\nexport const CLASS_LOADING         = 'is-loading';\r\n\r\n/**\r\n * The array with all status classes.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const STATUS_CLASSES = [ CLASS_ACTIVE, CLASS_VISIBLE, CLASS_PREV, CLASS_NEXT, CLASS_LOADING ];\r\n\r\n/**\r\n * The collection of classes for elements that Splide dynamically creates.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const CLASSES = {\r\n  slide     : CLASS_SLIDE,\r\n  clone     : CLASS_CLONE,\r\n  arrows    : CLASS_ARROWS,\r\n  arrow     : CLASS_ARROW,\r\n  prev      : CLASS_ARROW_PREV,\r\n  next      : CLASS_ARROW_NEXT,\r\n  pagination: CLASS_PAGINATION,\r\n  page      : CLASS_PAGINATION_PAGE,\r\n  spinner   : CLASS_SPINNER,\r\n};\r\n", "import {\n  <PERSON><PERSON><PERSON>_ACTIVE,\n  <PERSON><PERSON><PERSON>_ARROW_NEXT,\n  C<PERSON>SS_ARROW_PREV,\n  CLASS_ARROWS,\n  CLASS_AUTOPLAY,\n  CLASS_CLONE,\n  CLASS_LIST,\n  CLASS_PAUSE,\n  CLASS_PLAY,\n  CLASS_PROGRESS,\n  CLASS_PROGRESS_BAR,\n  CLASS_ROOT,\n  CLASS_SLIDE,\n  CLASS_SLIDER,\n  CLASS_TRACK,\n} from '../../constants/classes';\nimport { EVENT_REFRESH, EVENT_UPDATED } from '../../constants/events';\nimport { DEFAULT_EVENT_PRIORITY } from '../../constants/priority';\nimport { PROJECT_CODE } from '../../constants/project';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport {\n  addClass,\n  assert,\n  assign,\n  child,\n  children,\n  empty,\n  push,\n  query,\n  removeAttribute,\n  removeClass,\n  uniqueId,\n} from '../../utils';\n\n\n/**\n * The interface for elements which the slider consists of.\n *\n * @since 3.0.0\n */\nexport interface ElementCollection {\n  root: HTMLElement;\n  slider: HTMLElement;\n  track: HTMLElement;\n  list: HTMLElement;\n  slides: HTMLElement[];\n  arrows: HTMLElement;\n  prev: HTMLButtonElement;\n  next: HTMLButtonElement;\n  bar: HTMLElement;\n  autoplay: HTMLElement;\n  play: HTMLButtonElement;\n  pause: HTMLButtonElement;\n}\n\n/**\n * The interface for the Elements component.\n *\n * @since 3.0.0\n */\nexport interface ElementsComponent extends BaseComponent, ElementCollection {\n}\n\n/**\n * The component that collects and handles elements which the slider consists of.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return An Elements component object.\n */\nexport function Elements( Splide: Splide, Components: Components, options: Options ): ElementsComponent {\n  const { on } = EventInterface( Splide );\n  const { root } = Splide;\n  const elements: ElementCollection = {} as ElementCollection;\n\n  /**\n   * Stores all slide elements.\n   */\n  const slides: HTMLElement[] = [];\n\n  /**\n   * Stores all root classes.\n   */\n  let classes: string[];\n\n  /**\n   * The slider element that may be `undefined`.\n   */\n  let slider: HTMLElement;\n\n  /**\n   * The track element.\n   */\n  let track: HTMLElement;\n\n  /**\n   * The list element.\n   */\n  let list: HTMLElement;\n\n  /**\n   * Called when the component is constructed.\n   */\n  function setup(): void {\n    collect();\n    identify();\n    addClass( root, ( classes = getClasses() ) );\n  }\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    on( EVENT_REFRESH, refresh, DEFAULT_EVENT_PRIORITY - 2 );\n    on( EVENT_UPDATED, update );\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy(): void {\n    [ root, track, list ].forEach( elm => {\n      removeAttribute( elm, 'style' );\n    } );\n\n    empty( slides );\n    removeClass( root, classes );\n  }\n\n  /**\n   * Recollects slide elements.\n   */\n  function refresh(): void {\n    destroy();\n    setup();\n  }\n\n  /**\n   * Updates the status of elements.\n   */\n  function update(): void {\n    removeClass( root, classes );\n    addClass( root, ( classes = getClasses() ) );\n  }\n\n  /**\n   * Collects elements which the slider consists of.\n   */\n  function collect(): void {\n    slider = child( root, `.${ CLASS_SLIDER }` );\n    track  = query( root, `.${ CLASS_TRACK }` );\n    list   = child( track, `.${ CLASS_LIST }` );\n\n    assert( track && list, 'A track/list element is missing.' );\n\n    push( slides, children( list, `.${ CLASS_SLIDE }:not(.${ CLASS_CLONE })` ) );\n\n    const autoplay = find( `.${ CLASS_AUTOPLAY }` );\n    const arrows   = find( `.${ CLASS_ARROWS }` );\n\n    assign( elements, {\n      root,\n      slider,\n      track,\n      list,\n      slides,\n      arrows,\n      autoplay,\n      prev : query( arrows, `.${ CLASS_ARROW_PREV }` ),\n      next : query( arrows, `.${ CLASS_ARROW_NEXT }` ),\n      bar  : query( find( `.${ CLASS_PROGRESS }` ), `.${ CLASS_PROGRESS_BAR }` ),\n      play : query( autoplay, `.${ CLASS_PLAY }` ),\n      pause: query( autoplay, `.${ CLASS_PAUSE }` ),\n    } );\n  }\n\n  /**\n   * Assigns unique IDs to essential elements.\n   */\n  function identify(): void {\n    const id = root.id || uniqueId( PROJECT_CODE );\n    root.id  = id;\n    track.id = track.id || `${ id }-track`;\n    list.id  = list.id || `${ id }-list`;\n  }\n\n  /**\n   * Finds an element only in children of the root or slider element.\n   *\n   * @return {Element} - A found element or undefined.\n   */\n  function find( selector: string ): HTMLElement {\n    return child( root, selector ) || child( slider, selector );\n  }\n\n  /**\n   * Return an array with classes for the root element.\n   *\n   * @return An array with classes.\n   */\n  function getClasses(): string[] {\n    return [\n      `${ CLASS_ROOT }--${ options.type }`,\n      `${ CLASS_ROOT }--${ options.direction }`,\n      options.drag && `${ CLASS_ROOT }--draggable`,\n      options.isNavigation && `${ CLASS_ROOT }--nav`,\n      CLASS_ACTIVE,\n    ];\n  }\n\n  return assign( elements, {\n    setup,\n    mount,\n    destroy,\n  } );\n}\n", "export const ROLE             = 'role';\r\nexport const ARIA_CONTROLS    = 'aria-controls';\r\nexport const ARIA_CURRENT     = 'aria-current';\r\nexport const ARIA_LABEL       = 'aria-label';\r\nexport const ARIA_HIDDEN      = 'aria-hidden';\r\nexport const TAB_INDEX        = 'tabindex';\r\nexport const DISABLED         = 'disabled';\r\nexport const ARIA_ORIENTATION = 'aria-orientation';\r\n\r\n/**\r\n * The array with all attributes.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const ALL_ATTRIBUTES = [\r\n  ROL<PERSON>,\r\n  ARIA_CONTROLS,\r\n  ARIA_CURRENT,\r\n  ARIA_LABEL,\r\n  ARIA_HIDDEN,\r\n  ARIA_ORIENTATION,\r\n  TAB_INDEX,\r\n  DISABLED,\r\n];\r\n", "/**\r\n * The type for the regular slider.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const SLIDE = 'slide';\r\n\r\n/**\r\n * The type for the carousel slider.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const LOOP = 'loop';\r\n\r\n/**\r\n * The type for the fade slider that can not have multiple slides in a page.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const FADE = 'fade';\r\n", "import {\n  ALL_ATTRIBUTES,\n  ARIA_CONTROLS,\n  ARIA_CURRENT,\n  ARIA_HIDDEN,\n  ARIA_LABEL,\n  ROLE,\n  TAB_INDEX,\n} from '../../constants/attributes';\nimport {\n  CLASS_ACTIVE,\n  CLASS_CONTAINER,\n  CLASS_NEXT,\n  CLASS_PREV,\n  CLASS_VISIBLE,\n  STATUS_CLASSES,\n} from '../../constants/classes';\nimport {\n  EVENT_ACTIVE,\n  EVENT_CLICK,\n  EVENT_HIDDEN,\n  EVENT_INACTIVE,\n  EVENT_MOVE,\n  EVENT_MOVED,\n  EVENT_REFRESH,\n  EVENT_REPOSITIONED,\n  EVENT_SCROLLED,\n  EVENT_SLIDE_KEYDOWN,\n  EVENT_VISIBLE,\n} from '../../constants/events';\nimport { FADE, LOOP } from '../../constants/types';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent } from '../../types';\nimport {\n  abs,\n  ceil,\n  child,\n  floor,\n  format,\n  getAttribute,\n  hasClass,\n  min,\n  pad,\n  queryAll,\n  rect,\n  removeAttribute,\n  removeClass,\n  setAttribute,\n  style as _style,\n  toggleClass,\n} from '../../utils';\n\n\n/**\n * The interface for the Slide sub component.\n *\n * @since 3.0.0\n */\nexport interface  SlideComponent extends BaseComponent {\n  index: number;\n  slideIndex: number;\n  slide: HTMLElement;\n  container: HTMLElement;\n  isClone: boolean;\n  style( prop: string, value: string | number, useContainer?: boolean ): void\n  isWithin( from: number, distance: number ): boolean;\n}\n\n/**\n * The sub component for managing each slide.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param index      - A slide index.\n * @param slideIndex - A slide index for clones. This must be `-1` if the slide is not a clone.\n * @param slide      - A slide element.\n *\n * @return A Slide sub component.\n */\nexport function Slide( Splide: Splide, index: number, slideIndex: number, slide: HTMLElement ): SlideComponent {\n  const { on, emit, bind, destroy: destroyEvents } = EventInterface( Splide );\n  const { Components, root, options } = Splide;\n  const { isNavigation, updateOnMove } = options;\n  const { resolve } = Components.Direction;\n  const styles         = getAttribute( slide, 'style' );\n  const isClone        = slideIndex > -1;\n  const container      = child( slide, `.${ CLASS_CONTAINER }` );\n  const focusableNodes = options.focusableNodes && queryAll( slide, options.focusableNodes );\n\n  /**\n   * Turns into `true` when the component is destroyed.\n   */\n  let destroyed: boolean;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount( this: SlideComponent ): void {\n    init();\n\n    bind( slide, 'click keydown', e => {\n      emit( e.type === 'click' ? EVENT_CLICK : EVENT_SLIDE_KEYDOWN, this, e );\n    } );\n\n    on( [ EVENT_REFRESH, EVENT_REPOSITIONED, EVENT_MOVED, EVENT_SCROLLED ], update.bind( this ) );\n\n    if ( updateOnMove ) {\n      on( EVENT_MOVE, onMove.bind( this ) );\n    }\n  }\n\n  /**\n   * Initializes the component.\n   */\n  function init(): void {\n    if ( ! isClone ) {\n      slide.id = `${ root.id }-slide${ pad( index + 1 ) }`;\n    }\n\n    if ( isNavigation ) {\n      const idx      = isClone ? slideIndex : index;\n      const label    = format( options.i18n.slideX, idx + 1 );\n      const controls = Splide.splides.map( splide => splide.root.id ).join( ' ' );\n\n      setAttribute( slide, ARIA_LABEL, label );\n      setAttribute( slide, ARIA_CONTROLS, controls );\n      setAttribute( slide, ROLE, 'menuitem' );\n    }\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy(): void {\n    destroyed = true;\n    destroyEvents();\n    removeClass( slide, STATUS_CLASSES );\n    removeAttribute( slide, ALL_ATTRIBUTES );\n    setAttribute( slide, 'style', styles );\n  }\n\n  /**\n   * If the `updateOnMove` option is `true`, called when the slider starts moving.\n   *\n   * @param next - A next index.\n   * @param prev - A previous index.\n   * @param dest - A destination index.\n   */\n  function onMove( this: SlideComponent, next: number, prev: number, dest: number ): void {\n    if ( ! destroyed ) {\n      update.call( this );\n\n      if ( dest === index ) {\n        updateActivity.call( this, true );\n      }\n    }\n  }\n\n  /**\n   * Updates attribute and classes of the slide.\n   */\n  function update( this: SlideComponent ): void {\n    if ( ! destroyed ) {\n      const { index: currIndex } = Splide;\n\n      updateActivity.call( this, isActive() );\n      updateVisibility.call( this, isVisible() );\n\n      toggleClass( slide, CLASS_PREV, index === currIndex - 1 );\n      toggleClass( slide, CLASS_NEXT, index === currIndex + 1 );\n    }\n  }\n\n  /**\n   * Updates the status related with activity.\n   *\n   * @param active - Set `true` if the slide is active.\n   */\n  function updateActivity( this: SlideComponent, active: boolean ): void {\n    if ( active !== hasClass( slide, CLASS_ACTIVE ) ) {\n      toggleClass( slide, CLASS_ACTIVE, active );\n\n      if ( isNavigation ) {\n        setAttribute( slide, ARIA_CURRENT, active || null );\n      }\n\n      emit( active ? EVENT_ACTIVE : EVENT_INACTIVE, this );\n    }\n  }\n\n  /**\n   * Updates classes and attributes related with visibility.\n   *\n   * @param visible - Set `true` if the slide is visible.\n   */\n  function updateVisibility( this: SlideComponent, visible: boolean ): void {\n    const ariaHidden = ! visible && ! isActive();\n\n    setAttribute( slide, ARIA_HIDDEN, ariaHidden || null );\n    setAttribute( slide, TAB_INDEX, ! ariaHidden && options.slideFocus ? 0 : null );\n\n    if ( focusableNodes ) {\n      focusableNodes.forEach( node => {\n        setAttribute( node, TAB_INDEX, ariaHidden ? -1 : null );\n      } );\n    }\n\n    if ( visible !== hasClass( slide, CLASS_VISIBLE ) ) {\n      toggleClass( slide, CLASS_VISIBLE, visible );\n      emit( visible ? EVENT_VISIBLE : EVENT_HIDDEN, this );\n    }\n  }\n\n  /**\n   * Adds a CSS rule to the slider or the container.\n   *\n   * @param prop         - A property name.\n   * @param value        - A CSS value to add.\n   * @param useContainer - Optional. Determines whether to apply the rule to the container or not.\n   */\n  function style( prop: string, value: string | number, useContainer?: boolean ): void {\n    _style( ( useContainer && container ) || slide, prop, value );\n  }\n\n  /**\n   * Checks if the slide is active or not.\n   *\n   * @return `true` if the slide is active.\n   */\n  function isActive(): boolean {\n    return Splide.index === index;\n  }\n\n  /**\n   * Checks if the slide is visible or not.\n   */\n  function isVisible(): boolean {\n    if ( Splide.is( FADE ) ) {\n      return isActive();\n    }\n\n    const trackRect = rect( Components.Elements.track );\n    const slideRect = rect( slide );\n    const left      = resolve( 'left' );\n    const right     = resolve( 'right' );\n\n    return floor( trackRect[ left ] ) <= ceil( slideRect[ left ] )\n      && floor( slideRect[ right ] ) <= ceil( trackRect[ right ] );\n  }\n\n  /**\n   * Calculates how far this slide is from another slide and\n   * returns `true` if the distance is within the given number.\n   *\n   * @param from     - An index of a base slide.\n   * @param distance - `true` if the slide is within this number.\n   *\n   * @return `true` if the slide is within the `distance` from the base slide, or otherwise `false`.\n   */\n  function isWithin( from: number, distance: number ): boolean {\n    let diff = abs( from - index );\n\n    if ( ! isClone && ( options.rewind || Splide.is( LOOP ) ) ) {\n      diff = min( diff, Splide.length - diff );\n    }\n\n    return diff <= distance;\n  }\n\n  return {\n    index,\n    slideIndex,\n    slide,\n    container,\n    isClone,\n    mount,\n    destroy,\n    style,\n    isWithin,\n  };\n}\n", "import { EVENT_MOUNTED, EVENT_REFRESH, EVENT_RESIZE } from '../../constants/events';\r\nimport { EventInterface } from '../../constructors';\r\nimport { Splide } from '../../core/Splide/Splide';\r\nimport { AnyFunction, BaseComponent, Components, Options } from '../../types';\r\nimport {\r\n  addClass,\r\n  append,\r\n  before,\r\n  between,\r\n  empty,\r\n  forEach as forEachItem,\r\n  includes,\r\n  isFunction,\r\n  isHTMLElement,\r\n  isString,\r\n  matches,\r\n  parseHtml,\r\n  queryAll,\r\n  remove as removeNode,\r\n  toArray,\r\n} from '../../utils';\r\nimport { Slide, SlideComponent } from './Slide';\r\n\r\n\r\n/**\r\n * The interface for the Slides component.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport interface  SlidesComponent extends BaseComponent {\r\n  register( slide: HTMLElement, index: number, slideIndex: number ): void;\r\n  get( excludeClones?: boolean ): SlideComponent[];\r\n  getIn( page: number ): SlideComponent[];\r\n  getAt( index: number ): SlideComponent | undefined;\r\n  add( slide: string | Element | Array<string | Element>, index?: number ): void;\r\n  remove( selector: SlideMatcher ): void;\r\n  forEach( iteratee: SlidesIteratee, excludeClones?: boolean ): void;\r\n  filter( matcher: SlideMatcher ): SlideComponent[];\r\n  style( prop: string, value: string | number, useContainer?: boolean ): void\r\n  getLength( excludeClones?: boolean ): number;\r\n  isEnough(): boolean;\r\n}\r\n\r\n/**\r\n * The iteratee function for Slides.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport type SlidesIteratee = ( Slide: SlideComponent, index: number, Slides: SlideComponent[] ) => void\r\n\r\n/**\r\n * The predicate function for Slides.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport type SlidesPredicate = ( Slide: SlideComponent, index: number, Slides: SlideComponent[] ) => any\r\n\r\n/**\r\n * The type for filtering SlideComponent objects.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport type SlideMatcher = number | number[] | string | SlidesPredicate;\r\n\r\n/**\r\n * The component for managing all slides include clones.\r\n *\r\n * @since 3.0.0\r\n *\r\n * @param Splide     - A Splide instance.\r\n * @param Components - A collection of components.\r\n * @param options    - Options.\r\n *\r\n * @return An Slides component object.\r\n */\r\nexport function Slides( Splide: Splide, Components: Components, options: Options ): SlidesComponent {\r\n  const { on, emit, bind } = EventInterface( Splide );\r\n  const { slides, list } = Components.Elements;\r\n\r\n  /**\r\n   * Stores all SlideComponent objects.\r\n   */\r\n  const Slides: SlideComponent[] = [];\r\n\r\n  /**\r\n   * Called when the component is mounted.\r\n   */\r\n  function mount(): void {\r\n    init();\r\n    on( EVENT_REFRESH, refresh );\r\n    on( [ EVENT_MOUNTED, EVENT_REFRESH ], () => {\r\n      Slides.sort( ( Slide1, Slide2 ) => Slide1.index - Slide2.index );\r\n    } );\r\n  }\r\n\r\n  /**\r\n   * Initializes the component.\r\n   */\r\n  function init(): void {\r\n    slides.forEach( ( slide, index ) => { register( slide, index, -1 ) } );\r\n  }\r\n\r\n  /**\r\n   * Destroys the component.\r\n   */\r\n  function destroy(): void {\r\n    forEach( Slide => { Slide.destroy() } );\r\n    empty( Slides );\r\n  }\r\n\r\n  /**\r\n   * Discards all Slide components and regenerates them.\r\n   */\r\n  function refresh(): void {\r\n    destroy();\r\n    init();\r\n  }\r\n\r\n  /**\r\n   * Registers a slide element and creates a Slide object.\r\n   *\r\n   * @param slide      - A slide element to register.\r\n   * @param index      - A slide index.\r\n   * @param slideIndex - A slide index for clones. This must be `-1` for regular slides.\r\n   */\r\n  function register( slide: HTMLElement, index: number, slideIndex: number ): void {\r\n    const object = Slide( Splide, index, slideIndex, slide );\r\n    object.mount();\r\n    Slides.push( object );\r\n  }\r\n\r\n  /**\r\n   * Returns all Slide objects.\r\n   *\r\n   * @param excludeClones - Optional. Determines whether to exclude clones or not.\r\n   *\r\n   * @return An array with Slide objects.\r\n   */\r\n  function get( excludeClones?: boolean ): SlideComponent[] {\r\n    return excludeClones ? filter( Slide => ! Slide.isClone ) : Slides;\r\n  }\r\n\r\n  /**\r\n   * Returns slides in the specified page.\r\n   *\r\n   * @param page - A page index.\r\n   *\r\n   * @return An array with slides that belong to the page.\r\n   */\r\n  function getIn( page: number ): SlideComponent[] {\r\n    const { Controller } = Components;\r\n    const index = Controller.toIndex( page );\r\n    const max   = Controller.hasFocus() ? 1 : options.perPage;\r\n    return filter( Slide => between( Slide.index, index, index + max - 1 ) );\r\n  }\r\n\r\n  /**\r\n   * Returns a Slide object at the specified index.\r\n   *\r\n   * @param index - A slide index.\r\n   *\r\n   * @return A Slide object if available, or otherwise `undefined`.\r\n   */\r\n  function getAt( index: number ): SlideComponent | undefined {\r\n    return filter( index )[ 0 ];\r\n  }\r\n\r\n  /**\r\n   * Inserts a slide or slides at a specified index.\r\n   *\r\n   * @param items - A slide element, an HTML string or an array with them.\r\n   * @param index - Optional. An index to insert the slide at. If omitted, appends it to the list.\r\n   */\r\n  function add( items: string | Element | Array<string | Element>, index?: number ): void {\r\n    forEachItem( items, slide => {\r\n      if ( isString( slide ) ) {\r\n        slide = parseHtml( slide );\r\n      }\r\n\r\n      if ( isHTMLElement( slide ) ) {\r\n        const ref = slides[ index ];\r\n        ref ? before( slide, ref ) : append( list, slide );\r\n        addClass( slide, options.classes.slide );\r\n        observeImages( slide, emit.bind( null, EVENT_RESIZE ) );\r\n      }\r\n    } );\r\n\r\n    emit( EVENT_REFRESH );\r\n  }\r\n\r\n  /**\r\n   * Removes slides that match the matcher\r\n   * that can be an index, an array with indices, a selector, or an iteratee function.\r\n   *\r\n   * @param matcher - An index, an array with indices, a selector string, or an iteratee function.\r\n   */\r\n  function remove( matcher: SlideMatcher ): void {\r\n    removeNode( filter( matcher ).map( Slide => Slide.slide ) );\r\n    emit( EVENT_REFRESH );\r\n  }\r\n\r\n  /**\r\n   * Iterates over Slide objects by the iteratee function.\r\n   *\r\n   * @param iteratee      - An iteratee function that takes a Slide object, an index and an array with Slides.\r\n   * @param excludeClones - Optional. Determines whether to exclude clones or not.\r\n   */\r\n  function forEach( iteratee: SlidesIteratee, excludeClones?: boolean ): void {\r\n    get( excludeClones ).forEach( iteratee );\r\n  }\r\n\r\n  /**\r\n   * Filters Slides by the matcher\r\n   * that can be an index, an array with indices, a selector, or a predicate function.\r\n   *\r\n   * @param matcher - An index, an array with indices, a selector string, or a predicate function.\r\n   *\r\n   * @return An array with SlideComponent objects.\r\n   */\r\n  function filter( matcher: SlideMatcher ): SlideComponent[] {\r\n    return Slides.filter( isFunction( matcher )\r\n      ? matcher\r\n      : Slide => isString( matcher )\r\n        ? matches( Slide.slide, matcher )\r\n        : includes( toArray( matcher ), Slide.index )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Adds a CSS rule to all slides or containers.\r\n   *\r\n   * @param prop         - A property name.\r\n   * @param value        - A CSS value to add.\r\n   * @param useContainer - Optional. Determines whether to apply the rule to the container or not.\r\n   */\r\n  function style( prop: string, value: string | number, useContainer?: boolean ): void {\r\n    forEach( Slide => { Slide.style( prop, value, useContainer ) } );\r\n  }\r\n\r\n  /**\r\n   * Invokes the callback after all images in the element are loaded.\r\n   *\r\n   * @param elm      - An element that may contain images.\r\n   * @param callback - A callback function.\r\n   */\r\n  function observeImages( elm: Element, callback: AnyFunction ): void {\r\n    const images = queryAll( elm, 'img' );\r\n    let { length } = images;\r\n\r\n    if ( length ) {\r\n      images.forEach( img => {\r\n        bind( img, 'load error', () => {\r\n          if ( ! --length ) {\r\n            callback();\r\n          }\r\n        } );\r\n      } );\r\n    } else {\r\n      callback();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns the length of slides.\r\n   *\r\n   * @param excludeClones - Optional. Determines whether to exclude clones or not.\r\n   *\r\n   * @return The length of slides.\r\n   */\r\n  function getLength( excludeClones?: boolean ): number {\r\n    return excludeClones ? slides.length : Slides.length;\r\n  }\r\n\r\n  /**\r\n   * Checks if the number of slides is over than the `perPage` option, including clones.\r\n   *\r\n   * @return `true` if there are enough slides, or otherwise `false`.\r\n   */\r\n  function isEnough(): boolean {\r\n    return Slides.length > options.perPage;\r\n  }\r\n\r\n  return {\r\n    mount,\r\n    destroy,\r\n    register,\r\n    get,\r\n    getIn,\r\n    getAt,\r\n    add,\r\n    remove,\r\n    forEach,\r\n    filter,\r\n    style,\r\n    getLength,\r\n    isEnough,\r\n  };\r\n}\r\n", "import { TTB } from '../../constants/directions';\nimport { EVENT_REFRESH, EVENT_RESIZE, EVENT_RESIZED, EVENT_UPDATED } from '../../constants/events';\nimport { EventInterface, Throttle } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { abs, assert, isObject, rect, style, unit } from '../../utils';\n\n\n/**\n * The interface for the Layout component.\n *\n * @since 3.0.0\n */\nexport interface LayoutComponent extends BaseComponent {\n  listSize(): number;\n  slideSize( index: number, withoutGap?: boolean ): number;\n  sliderSize(): number;\n  totalSize( index?: number, withoutGap?: boolean ): number;\n  getPadding( right: boolean ): number;\n}\n\n/**\n * The component that layouts slider components and provides methods for dimensions.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return An Layout component object.\n */\nexport function Layout( Splide: Splide, Components: Components, options: Options ): LayoutComponent {\n  const { on, bind, emit } = EventInterface( Splide );\n  const { Slides } = Components;\n  const { resolve } = Components.Direction;\n  const { root, track, list } = Components.Elements;\n  const { getAt } = Slides;\n\n  /**\n   * Indicates whether the slider direction is vertical or not.\n   */\n  let vertical: boolean;\n\n  /**\n   * Keeps the DOMRect object of the root element.\n   */\n  let rootRect: DOMRect;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    init();\n    bind( window, 'resize load', Throttle( emit.bind( this, EVENT_RESIZE ) ) );\n    on( [ EVENT_UPDATED, EVENT_REFRESH ], init );\n    on( EVENT_RESIZE, resize );\n  }\n\n  /**\n   * Initializes the component on `mount` or `updated`.\n   * Uses `max-width` for the root to prevent the slider from exceeding the parent element.\n   */\n  function init(): void {\n    rootRect = null;\n    vertical = options.direction === TTB;\n\n    style( root, 'maxWidth', unit( options.width ) );\n    style( track, resolve( 'paddingLeft' ), cssPadding( false ) );\n    style( track, resolve( 'paddingRight' ), cssPadding( true ) );\n\n    resize();\n  }\n\n  /**\n   * Updates dimensions of some elements when the slider is resized.\n   */\n  function resize(): void {\n    const newRect = rect( root );\n\n    if ( ! rootRect || rootRect.width !== newRect.width || rootRect.height !== newRect.height ) {\n      style( track, 'height', cssTrackHeight() );\n\n      Slides.style( resolve( 'marginRight' ), unit( options.gap ) );\n      Slides.style( 'width', cssSlideWidth() || null );\n      setSlidesHeight();\n\n      rootRect = newRect;\n      emit( EVENT_RESIZED );\n    }\n  }\n\n  /**\n   * Updates the height of slides or their container elements if available.\n   */\n  function setSlidesHeight(): void {\n    Slides.style( 'height', cssSlideHeight() || null, true );\n  }\n\n  /**\n   * Parses the padding option and returns the value for each side.\n   * This method returns `paddingTop` or `paddingBottom` for the vertical slider.\n   *\n   * @param right - Determines whether to get `paddingRight/Bottom` or `paddingLeft/Top`.\n   *\n   * @return The padding value as a CSS string.\n   */\n  function cssPadding( right: boolean ): string {\n    const { padding } = options;\n    const prop = resolve( right ? 'right' : 'left' );\n    return padding && unit( padding[ prop ] || ( isObject( padding ) ? 0 : padding ) ) || '0px';\n  }\n\n  /**\n   * Returns the height of the track element as a CSS string.\n   *\n   * @return The height of the track.\n   */\n  function cssTrackHeight(): string {\n    let height = '';\n\n    if ( vertical ) {\n      height = cssHeight();\n      assert( height, 'height or heightRatio is missing.' );\n      height = `calc(${ height } - ${ cssPadding( false ) } - ${ cssPadding( true ) })`;\n    }\n\n    return height;\n  }\n\n  /**\n   * Converts options related with height to a CSS string.\n   *\n   * @return The height as a CSS string if available, or otherwise an empty string.\n   */\n  function cssHeight(): string {\n    return unit( options.height || rect( list ).width * options.heightRatio );\n  }\n\n  /**\n   * Returns the width of the slide as a CSS string.\n   *\n   * @return The width of the slide.\n   */\n  function cssSlideWidth(): string {\n    return options.autoWidth ? '' : unit( options.fixedWidth ) || ( vertical ? '' : cssSlideSize() );\n  }\n\n  /**\n   * Returns the height of the slide as a CSS string.\n   *\n   * @return The height of the slide.\n   */\n  function cssSlideHeight(): string {\n    return unit( options.fixedHeight )\n      || ( vertical ? ( options.autoHeight ? '' : cssSlideSize() ) : cssHeight() );\n  }\n\n  /**\n   * Returns the CSS string for slide width or height without gap.\n   *\n   * @return The CSS string for slide width or height.\n   */\n  function cssSlideSize(): string {\n    const gap = unit( options.gap );\n    return `calc((100%${ gap && ` + ${ gap }` })/${ options.perPage || 1 }${ gap && ` - ${ gap }` })`;\n  }\n\n  /**\n   * Returns the list width for the horizontal slider, or the height for the vertical slider.\n   *\n   * @return The size of the track element in pixel.\n   */\n  function listSize(): number {\n    return rect( list )[ resolve( 'width' ) ];\n  }\n\n  /**\n   * Returns the slide width for the horizontal slider, or the height for the vertical slider.\n   *\n   * @param index      - Optional. A slide index.\n   * @param withoutGap - Optional. Determines whether to exclude the gap amount or not.\n   *\n   * @return The size of the specified slide element in pixel.\n   */\n  function slideSize( index?: number, withoutGap?: boolean ): number {\n    const Slide = getAt( index || 0 );\n    return Slide\n      ? rect( Slide.slide )[ resolve( 'width' ) ] + ( withoutGap ? 0 : getGap() )\n      : 0;\n  }\n\n  /**\n   * Returns the total width or height of slides from the head of the slider to the specified index.\n   * This includes sizes of clones before the first slide.\n   *\n   * @param index      - A slide index. If omitted, uses the last index.\n   * @param withoutGap - Optional. Determines whether to exclude the last gap or not.\n   *\n   * @return The total width of slides in the horizontal slider, or the height in the vertical one.\n   */\n  function totalSize( index: number, withoutGap?: boolean ): number {\n    const Slide = getAt( index );\n\n    if ( Slide ) {\n      const right = rect( Slide.slide )[ resolve( 'right' ) ];\n      const left  = rect( list )[ resolve( 'left' ) ];\n      return abs( right - left ) + ( withoutGap ? 0 : getGap() );\n    }\n\n    return 0;\n  }\n\n  /**\n   * Returns the slider size without clones before the first slide.\n   *\n   * @return The width or height of the slider without clones.\n   */\n  function sliderSize(): number {\n    return totalSize( Splide.length - 1, true ) - totalSize( -1, true );\n  }\n\n  /**\n   * Returns the gap value.\n   *\n   * @return The gap value in pixel.\n   */\n  function getGap(): number {\n    const Slide = getAt( 0 );\n    return Slide && parseFloat( style( Slide.slide, resolve( 'marginRight' ) ) ) || 0;\n  }\n\n  /**\n   * Returns the padding value.\n   * This method resolves the difference of the direction.\n   *\n   * @param right - Determines whether to get `paddingRight/Bottom` or `paddingLeft/Top`.\n   *\n   * @return The padding value in pixel.\n   */\n  function getPadding( right: boolean ): number {\n    return parseFloat( style( track, resolve( `padding${ right ? 'Right' : 'Left' }` ) ) ) || 0;\n  }\n\n  return {\n    mount,\n    listSize,\n    slideSize,\n    sliderSize,\n    totalSize,\n    getPadding,\n  };\n}\n", "import { EVENT_REFRESH, EVENT_RESIZE, EVENT_UPDATED } from '../../constants/events';\r\nimport { LOOP } from '../../constants/types';\r\nimport { EventInterface } from '../../constructors';\r\nimport { Splide } from '../../core/Splide/Splide';\r\nimport { BaseComponent, Components, Options } from '../../types';\r\nimport { addClass, append, before, ceil, empty, measure, pad, push, rect, remove } from '../../utils';\r\n\r\n\r\n/**\r\n * The interface for the Clone component.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport interface ClonesComponent extends BaseComponent {\r\n}\r\n\r\n/**\r\n * The component that generates clones for the loop slider.\r\n *\r\n * @since 3.0.0\r\n *\r\n * @param Splide     - A Splide instance.\r\n * @param Components - A collection of components.\r\n * @param options    - Options.\r\n *\r\n * @return A Clones component object.\r\n */\r\nexport function Clones( Splide: Splide, Components: Components, options: Options ): ClonesComponent {\r\n  const { on, emit } = EventInterface( Splide );\r\n  const { Elements, Slides } = Components;\r\n  const { resolve } = Components.Direction;\r\n\r\n  /**\r\n   * Stores all cloned elements.\r\n   */\r\n  const clones: HTMLElement[] = [];\r\n\r\n  /**\r\n   * Keeps the current number of clones.\r\n   */\r\n  let cloneCount: number;\r\n\r\n  /**\r\n   * Called when the component is mounted.\r\n   */\r\n  function mount(): void {\r\n    init();\r\n    on( EVENT_REFRESH, refresh );\r\n    on( [ EVENT_UPDATED, EVENT_RESIZE ], observe );\r\n  }\r\n\r\n  /**\r\n   * Removes all clones if available, and generates new clones.\r\n   */\r\n  function init(): void {\r\n    if ( ( cloneCount = computeCloneCount() ) ) {\r\n      generate( cloneCount );\r\n      emit( EVENT_RESIZE );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Destroys clones.\r\n   */\r\n  function destroy(): void {\r\n    remove( clones );\r\n    empty( clones );\r\n  }\r\n\r\n  /**\r\n   * Discards all clones and regenerates them.\r\n   * Must do this before the Elements component collects slide elements.\r\n   */\r\n  function refresh(): void {\r\n    destroy();\r\n    init();\r\n  }\r\n\r\n  /**\r\n   * Observes the required clone count and refreshes the slider if necessary.\r\n   */\r\n  function observe(): void {\r\n    if ( cloneCount < computeCloneCount() ) {\r\n      emit( EVENT_REFRESH );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generates the specified number of clones.\r\n   *\r\n   * @param count - The number of clones to generate for each side.\r\n   */\r\n  function generate( count: number ): void {\r\n    const slides = Slides.get().slice();\r\n    const { length } = slides;\r\n\r\n    if ( length ) {\r\n      while ( slides.length < count ) {\r\n        push( slides, slides );\r\n      }\r\n\r\n      push( slides.slice( -count ), slides.slice( 0, count ) ).forEach( ( Slide, index ) => {\r\n        const isHead = index < count;\r\n        const clone  = cloneDeep( Slide.slide, index );\r\n        isHead ? before( clone, slides[ 0 ].slide ) : append( Elements.list, clone );\r\n        push( clones, clone );\r\n        Slides.register( clone, index - count + ( isHead ? 0 : length ), Slide.index );\r\n      } );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deeply clones the provided element with removing the ID attribute.\r\n   *\r\n   * @param elm   - An element to clone.\r\n   * @param index - An index of the clone.\r\n   *\r\n   * @return A cloned element.\r\n   */\r\n  function cloneDeep( elm: HTMLElement, index: number ): HTMLElement {\r\n    const clone = elm.cloneNode( true ) as HTMLElement;\r\n    addClass( clone, options.classes.clone );\r\n    clone.id = `${ Splide.root.id }-clone${ pad( index + 1 ) }`;\r\n    return clone;\r\n  }\r\n\r\n  /**\r\n   * Returns the number of elements to generate.\r\n   * This always returns 0 if the slider type is not `'loop'`.\r\n   *\r\n   * @return The number of clones.\r\n   */\r\n  function computeCloneCount(): number {\r\n    let { clones } = options;\r\n\r\n    if ( ! Splide.is( LOOP ) ) {\r\n      clones = 0;\r\n    } else if ( ! clones ) {\r\n      const fixedSize  = measure( Elements.list, options[ resolve( 'fixedWidth' ) ] );\r\n      const fixedCount = fixedSize && ceil( rect( Elements.track )[ resolve( 'width' ) ] / fixedSize );\r\n      const baseCount  = fixedCount || ( options[ resolve( 'autoWidth' ) ] && Splide.length ) || options.perPage;\r\n\r\n      clones = baseCount * ( options.drag ? ( options.flickMaxPages || 1 ) + 1 : 2 );\r\n    }\r\n\r\n    return clones;\r\n  }\r\n\r\n  return {\r\n    mount,\r\n    destroy,\r\n  };\r\n}\r\n", "import {\r\n  EVENT_MOUNTED,\r\n  EVENT_MOVE,\r\n  EVENT_MOVED,\r\n  EVENT_REFRESH,\r\n  EVENT_REPOSITIONED,\r\n  EVENT_RESIZED,\r\n  EVENT_UPDATED,\r\n} from '../../constants/events';\r\nimport { IDLE, MOVING } from '../../constants/states';\r\nimport { FADE, LOOP, SLIDE } from '../../constants/types';\r\nimport { EventInterface } from '../../constructors';\r\nimport { Splide } from '../../core/Splide/Splide';\r\nimport { AnyFunction, BaseComponent, Components, Options } from '../../types';\r\nimport { abs, ceil, clamp, isUndefined, rect, removeAttribute, sign } from '../../utils';\r\n\r\n\r\n/**\r\n * The interface for the Move component.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport interface MoveComponent extends BaseComponent {\r\n  move( dest: number, index: number, prev: number, callback?: AnyFunction ): void;\r\n  jump( index: number ): void;\r\n  translate( position: number, preventLoop?: boolean ): void;\r\n  shift( position: number, backwards: boolean ): number;\r\n  cancel(): void;\r\n  toIndex( position: number ): number;\r\n  toPosition( index: number, trimming?: boolean ): number;\r\n  getPosition(): number;\r\n  getLimit( max: boolean ): number;\r\n  isBusy(): boolean;\r\n  exceededLimit( max?: boolean | undefined, position?: number ): boolean;\r\n}\r\n\r\n/**\r\n * The component for moving the slider.\r\n *\r\n * @since 3.0.0\r\n *\r\n * @param Splide     - A Splide instance.\r\n * @param Components - A collection of components.\r\n * @param options    - Options.\r\n *\r\n * @return A Move component object.\r\n */\r\nexport function Move( Splide: Splide, Components: Components, options: Options ): MoveComponent {\r\n  const { on, emit } = EventInterface( Splide );\r\n  const { slideSize, getPadding, totalSize, listSize, sliderSize } = Components.Layout;\r\n  const { resolve, orient } = Components.Direction;\r\n  const { list, track } = Components.Elements;\r\n\r\n  /**\r\n   * Indicates whether the component can move the slider or not.\r\n   */\r\n  let waiting: boolean;\r\n\r\n  /**\r\n   * Called when the component is mounted.\r\n   */\r\n  function mount(): void {\r\n    on( [ EVENT_MOUNTED, EVENT_RESIZED, EVENT_UPDATED, EVENT_REFRESH ], reposition );\r\n  }\r\n\r\n  /**\r\n   * Destroys the component.\r\n   */\r\n  function destroy(): void {\r\n    removeAttribute( list, 'style' );\r\n  }\r\n\r\n  /**\r\n   * Repositions the slider.\r\n   * - This must be called before the Slide component checks the visibility.\r\n   * - Do not call `cancel()` here because LazyLoad may emit resize while transitioning.\r\n   * - iOS Safari emits window resize event while the user swipes the slider because of the bottom bar.\r\n   */\r\n  function reposition(): void {\r\n    if ( ! isBusy() ) {\r\n      Components.Scroll.cancel();\r\n      jump( Splide.index );\r\n      emit( EVENT_REPOSITIONED );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Moves the slider to the dest index with the Transition component.\r\n   *\r\n   * @param dest     - A destination index to go to, including clones'.\r\n   * @param index    - A slide index.\r\n   * @param prev     - A previous index.\r\n   * @param callback - Optional. A callback function invoked after transition ends.\r\n   */\r\n  function move( dest: number, index: number, prev: number, callback?: AnyFunction ): void {\r\n    if ( ! isBusy() ) {\r\n      const { set } = Splide.state;\r\n      const position = getPosition();\r\n      const looping  = dest !== index;\r\n\r\n      waiting = looping || options.waitForTransition;\r\n      set( MOVING );\r\n      emit( EVENT_MOVE, index, prev, dest );\r\n\r\n      Components.Transition.start( dest, () => {\r\n        looping && jump( index );\r\n        waiting = false;\r\n        set( IDLE );\r\n        emit( EVENT_MOVED, index, prev, dest );\r\n\r\n        if ( options.trimSpace === 'move' && dest !== prev && position === getPosition() ) {\r\n          Components.Controller.go( dest > prev ? '>' : '<', false, callback );\r\n        } else {\r\n          callback && callback();\r\n        }\r\n      } );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Jumps to the slide at the specified index.\r\n   *\r\n   * @param index - An index to jump to.\r\n   */\r\n  function jump( index: number ): void {\r\n    translate( toPosition( index, true ) );\r\n  }\r\n\r\n  /**\r\n   * Moves the slider to the provided position.\r\n   *\r\n   * @param position    - The position to move to.\r\n   * @param preventLoop - Optional. If `true`, sets the provided position as is.\r\n   */\r\n  function translate( position: number, preventLoop?: boolean ): void {\r\n    if ( ! Splide.is( FADE ) ) {\r\n      list.style.transform = `translate${ resolve( 'X' ) }(${ preventLoop ? position : loop( position ) }px)`;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Loops the provided position if it exceeds bounds.\r\n   *\r\n   * @param position - A position to loop.\r\n   */\r\n  function loop( position: number ): number {\r\n    if ( ! waiting && Splide.is( LOOP ) ) {\r\n      const diff        = orient( position - getPosition() );\r\n      const exceededMin = exceededLimit( false, position ) && diff < 0;\r\n      const exceededMax = exceededLimit( true, position ) && diff > 0;\r\n\r\n      if ( exceededMin || exceededMax ) {\r\n        position = shift( position, exceededMax );\r\n      }\r\n    }\r\n\r\n    return position;\r\n  }\r\n\r\n  /**\r\n   * Adds or subtracts the slider width to the provided position.\r\n   *\r\n   * @param position  - A position to shift.\r\n   * @param backwards - Determines whether to shift the slider backwards or forwards.\r\n   *\r\n   * @return The shifted position.\r\n   */\r\n  function shift( position: number, backwards: boolean ): number {\r\n    const excess = position - getLimit( backwards );\r\n    const size   = sliderSize();\r\n    position -= sign( excess ) * size * ceil( abs( excess ) / size );\r\n    return position;\r\n  }\r\n\r\n  /**\r\n   * Cancels transition.\r\n   */\r\n  function cancel(): void {\r\n    waiting = false;\r\n    translate( getPosition() );\r\n    Components.Transition.cancel();\r\n  }\r\n\r\n  /**\r\n   * Returns the closest index to the position.\r\n   *\r\n   * @param position - A position to convert.\r\n   *\r\n   * @return The closest index to the position.\r\n   */\r\n  function toIndex( position: number ): number {\r\n    const Slides = Components.Slides.get();\r\n\r\n    let index       = 0;\r\n    let minDistance = Infinity;\r\n\r\n    for ( let i = 0; i < Slides.length; i++ ) {\r\n      const slideIndex = Slides[ i ].index;\r\n      const distance   = abs( toPosition( slideIndex, true ) - position );\r\n\r\n      if ( distance <= minDistance ) {\r\n        minDistance = distance;\r\n        index       = slideIndex;\r\n      } else {\r\n        break;\r\n      }\r\n    }\r\n\r\n    return index;\r\n  }\r\n\r\n  /**\r\n   * Converts the slide index to the position.\r\n   *\r\n   * @param index    - An index to convert.\r\n   * @param trimming - Optional. Whether to trim edge spaces or not.\r\n   *\r\n   * @return The position corresponding with the index.\r\n   */\r\n  function toPosition( index: number, trimming?: boolean ): number {\r\n    const position = orient( totalSize( index - 1 ) - offset( index ) );\r\n    return trimming ? trim( position ) : position;\r\n  }\r\n\r\n  /**\r\n   * Returns the current position.\r\n   *\r\n   * @return The position of the list element.\r\n   */\r\n  function getPosition(): number {\r\n    const left = resolve( 'left' );\r\n    return rect( list )[ left ] - rect( track )[ left ] + orient( getPadding( false ) );\r\n  }\r\n\r\n  /**\r\n   * Trims spaces on the edge of the slider.\r\n   *\r\n   * @param position - A position to trim.\r\n   *\r\n   * @return A trimmed position.\r\n   */\r\n  function trim( position: number ): number {\r\n    if ( options.trimSpace && Splide.is( SLIDE ) ) {\r\n      position = clamp( position, 0, orient( sliderSize() - listSize() ) );\r\n    }\r\n\r\n    return position;\r\n  }\r\n\r\n  /**\r\n   * Returns the offset amount.\r\n   *\r\n   * @param index - An index.\r\n   */\r\n  function offset( index: number ): number {\r\n    const { focus } = options;\r\n    return focus === 'center' ? ( listSize() - slideSize( index, true ) ) / 2 : +focus * slideSize( index ) || 0;\r\n  }\r\n\r\n  /**\r\n   * Returns the limit number that the slider can move to.\r\n   *\r\n   * @param max - Determines whether to return the maximum or minimum limit.\r\n   *\r\n   * @return The border number.\r\n   */\r\n  function getLimit( max: boolean ): number {\r\n    return toPosition( max ? Components.Controller.getEnd() : 0, !! options.trimSpace );\r\n  }\r\n\r\n  /**\r\n   * Checks if the slider can move now or not.\r\n   *\r\n   * @return `true` if the slider can move, or otherwise `false`.\r\n   */\r\n  function isBusy(): boolean {\r\n    return !! waiting;\r\n  }\r\n\r\n  /**\r\n   * Checks if the provided position exceeds the minimum or maximum limit or not.\r\n   *\r\n   * @param max      - Optional. `true` for testing max, `false` for min, and `undefined` for both.\r\n   * @param position - Optional. A position to test. If omitted, tests the current position.\r\n   *\r\n   * @return `true` if the position exceeds the limit, or otherwise `false`.\r\n   */\r\n  function exceededLimit( max?: boolean | undefined, position?: number ): boolean {\r\n    position = isUndefined( position ) ? getPosition() : position;\r\n    const exceededMin = max !== true && orient( position ) < orient( getLimit( false ) );\r\n    const exceededMax = max !== false && orient( position ) > orient( getLimit( true ) );\r\n    return exceededMin || exceededMax;\r\n  }\r\n\r\n  return {\r\n    mount,\r\n    destroy,\r\n    move,\r\n    jump,\r\n    translate,\r\n    shift,\r\n    cancel,\r\n    toIndex,\r\n    toPosition,\r\n    getPosition,\r\n    getLimit,\r\n    isBusy,\r\n    exceededLimit,\r\n  };\r\n}\r\n", "import { EVENT_REFRESH, EVENT_UPDATED } from '../../constants/events';\nimport { DEFAULT_EVENT_PRIORITY } from '../../constants/priority';\nimport { LOOP, SLIDE } from '../../constants/types';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { AnyFunction, BaseComponent, Components, Options } from '../../types';\nimport { approximatelyEqual, between, clamp, floor, isString, isUndefined, max } from '../../utils';\n\n\n/**\n * The interface for the Controller component.\n *\n * @since 3.0.0\n */\nexport interface ControllerComponent extends BaseComponent {\n  go( control: number | string, allowSameIndex?: boolean, callback?: AnyFunction ): void;\n  scroll( destination: number, useIndex?: boolean, snap?: boolean, duration?: number, callback?: AnyFunction ): void;\n  getNext( destination?: boolean ): number;\n  getPrev( destination?: boolean ): number;\n  getEnd(): number;\n  setIndex( index: number ): void;\n  getIndex( prev?: boolean ): number;\n  toIndex( page: number ): number;\n  toPage( index: number ): number;\n  toDest( position: number ): number;\n  hasFocus(): boolean;\n}\n\n/**\n * The component for controlling the slider.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Controller component object.\n */\nexport function Controller( Splide: Splide, Components: Components, options: Options ): ControllerComponent {\n  const { on } = EventInterface( Splide );\n  const { Move } = Components;\n  const { getPosition, getLimit } = Move;\n  const { isEnough, getLength } = Components.Slides;\n  const isLoop  = Splide.is( LOOP );\n  const isSlide = Splide.is( SLIDE );\n\n  /**\n   * The current index.\n   */\n  let currIndex = options.start || 0;\n\n  /**\n   * The previous index.\n   */\n  let prevIndex = currIndex;\n\n  /**\n   * The latest number of slides.\n   */\n  let slideCount: number;\n\n  /**\n   * The latest `perMove` value.\n   */\n  let perMove: number;\n\n  /**\n   * The latest `perMove` value.\n   */\n  let perPage: number;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    init();\n    on( [ EVENT_UPDATED, EVENT_REFRESH ], init, DEFAULT_EVENT_PRIORITY - 1 );\n  }\n\n  /**\n   * Initializes some parameters.\n   * Needs to check the slides length since the current index may be out of the range after refresh.\n   */\n  function init(): void {\n    slideCount = getLength( true );\n    perMove    = options.perMove;\n    perPage    = options.perPage;\n    currIndex  = clamp( currIndex, 0, slideCount - 1 );\n  }\n\n  /**\n   * Moves the slider by the control pattern.\n   *\n   * @see `Splide#go()`\n   *\n   * @param control        - A control pattern.\n   * @param allowSameIndex - Optional. Determines whether to allow to go to the current index or not.\n   * @param callback       - Optional. A callback function invoked after transition ends.\n   */\n  function go( control: number | string, allowSameIndex?: boolean, callback?: AnyFunction ): void {\n    const dest = parse( control );\n\n    if ( options.useScroll ) {\n      scroll( dest, true, true, options.speed, callback );\n    } else {\n      const index = loop( dest );\n\n      if ( index > -1 && ! Move.isBusy() && ( allowSameIndex || index !== currIndex ) ) {\n        setIndex( index );\n        Move.move( dest, index, prevIndex, callback );\n      }\n    }\n  }\n\n  /**\n   * Scrolls the slider to the specified destination with updating indices.\n   *\n   * @param destination - A position or an index to scroll to.\n   * @param useIndex    - Optional. Whether to use an index as a destination or not.\n   * @param snap        - Optional. Whether to snap the closest slide or not.\n   * @param duration    - Optional. Specifies the scroll duration.\n   * @param callback    - Optional. A callback function invoked after scroll ends.\n   */\n  function scroll(\n    destination: number,\n    useIndex?: boolean,\n    snap?: boolean,\n    duration?: number,\n    callback?: AnyFunction\n  ): void {\n    const dest = useIndex ? destination : toDest( destination );\n\n    Components.Scroll.scroll( useIndex || snap ? Move.toPosition( dest, true ) : destination, duration, () => {\n      setIndex( Move.toIndex( Move.getPosition() ) );\n      callback && callback();\n    } );\n  }\n\n  /**\n   * Parses the control and returns a slide index.\n   *\n   * @param control - A control pattern to parse.\n   *\n   * @return A `dest` index.\n   */\n  function parse( control: number | string ): number {\n    let index = currIndex;\n\n    if ( isString( control ) ) {\n      const [ , indicator, number ] = control.match( /([+\\-<>])(\\d+)?/ ) || [];\n\n      if ( indicator === '+' || indicator === '-' ) {\n        index = computeDestIndex( currIndex + +`${ indicator }${ +number || 1 }`, currIndex, true );\n      } else if ( indicator === '>' ) {\n        index = number ? toIndex( +number ) : getNext( true );\n      } else if ( indicator === '<' ) {\n        index = getPrev( true );\n      }\n    } else {\n      index = isLoop ? control : clamp( control, 0, getEnd() );\n    }\n\n    return index;\n  }\n\n  /**\n   * Returns a next destination index.\n   *\n   * @param destination - Optional. Determines whether to get a destination index or a slide one.\n   *\n   * @return A next index if available, or otherwise `-1`.\n   */\n  function getNext( destination?: boolean ): number {\n    return getAdjacent( false, destination );\n  }\n\n  /**\n   * Returns a previous destination index.\n   *\n   * @param destination - Optional. Determines whether to get a destination index or a slide one.\n   *\n   * @return A previous index if available, or otherwise `-1`.\n   */\n  function getPrev( destination?: boolean ): number {\n    return getAdjacent( true, destination );\n  }\n\n  /**\n   * Returns an adjacent destination index.\n   *\n   * @param prev        - Determines whether to return a previous or next index.\n   * @param destination - Optional. Determines whether to get a destination index or a slide one.\n   *\n   * @return An adjacent index if available, or otherwise `-1`.\n   */\n  function getAdjacent( prev: boolean, destination?: boolean ): number {\n    const number = perMove || ( hasFocus() ? 1 : perPage );\n    const dest   = computeDestIndex( currIndex + number * ( prev ? -1 : 1 ), currIndex );\n\n    if ( dest === -1 && isSlide ) {\n      if ( ! approximatelyEqual( getPosition(), getLimit( ! prev ), 1 ) ) {\n        return prev ? 0 : getEnd();\n      }\n    }\n\n    return destination ? dest : loop( dest );\n  }\n\n  /**\n   * Converts the desired destination index to the valid one.\n   * - This may return clone indices if the editor is the loop mode,\n   *   or `-1` if there is no slide to go.\n   * - There are still slides where the slider can go if borders are between `from` and `dest`.\n   *\n   * @param dest        - The desired destination.\n   * @param from        - A base index.\n   * @param incremental - Optional. Whether the control is incremental or not.\n   *\n   * @return A converted destination index, including clones.\n   */\n  function computeDestIndex( dest: number, from: number, incremental?: boolean ): number {\n    if ( isEnough() ) {\n      const end = getEnd();\n\n      // Will overrun:\n      if ( dest < 0 || dest > end ) {\n        if ( between( 0, dest, from, true ) || between( end, from, dest, true ) ) {\n          dest = toIndex( toPage( dest ) );\n        } else {\n          if ( isLoop ) {\n            dest = perMove\n              ? dest\n              : dest < 0 ? - ( slideCount % perPage || perPage ) : slideCount;\n          } else if ( options.rewind ) {\n            dest = dest < 0 ? end : 0;\n          } else {\n            dest = -1;\n          }\n        }\n      } else {\n        if ( ! incremental && dest !== from ) {\n          dest = perMove ? dest : toIndex( toPage( from ) + ( dest < from ? -1 : 1 ) );\n        }\n      }\n    } else {\n      dest = -1;\n    }\n\n    return dest;\n  }\n\n  /**\n   * Returns the end index where the slider can go.\n   * For example, if the slider has 10 slides and the `perPage` option is 3,\n   * the slider can go to the slide 8 (the index is 7).\n   *\n   * @return An end index.\n   */\n  function getEnd(): number {\n    let end = slideCount - perPage;\n\n    if ( hasFocus() || ( isLoop && perMove ) ) {\n      end = slideCount - 1;\n    }\n\n    return max( end, 0 );\n  }\n\n  /**\n   * Loops the provided index only in the loop mode.\n   *\n   * @param index - An index to loop.\n   *\n   * @return A looped index.\n   */\n  function loop( index: number ): number {\n    if ( isLoop ) {\n      return isEnough() ? index % slideCount + ( index < 0 ? slideCount : 0 ) : -1;\n    }\n\n    return index;\n  }\n\n  /**\n   * Converts the page index to the slide index.\n   *\n   * @param page - A page index to convert.\n   *\n   * @return A slide index.\n   */\n  function toIndex( page: number ): number {\n    return clamp( hasFocus() ? page : perPage * page, 0, getEnd() );\n  }\n\n  /**\n   * Converts the slide index to the page index.\n   *\n   * @param index - An index to convert.\n   */\n  function toPage( index: number ): number {\n    if ( ! hasFocus() ) {\n      index = between( index, slideCount - perPage, slideCount - 1 ) ? slideCount - 1 : index;\n      index = floor( index / perPage );\n    }\n\n    return index;\n  }\n\n  /**\n   * Converts the destination position to the dest index.\n   *\n   * @param destination - A position to convert.\n   *\n   * @return A dest index.\n   */\n  function toDest( destination: number ): number {\n    const closest = Move.toIndex( destination );\n    return isSlide ? clamp( closest, 0, getEnd() ) : closest;\n  }\n\n  /**\n   * Sets a new index and retains old one.\n   *\n   * @param index - A new index to set.\n   */\n  function setIndex( index: number ): void {\n    if ( index !== currIndex ) {\n      prevIndex = currIndex;\n      currIndex = index;\n    }\n  }\n\n  /**\n   * Returns the current/previous index.\n   *\n   * @param prev - Optional. Whether to return previous index or not.\n   */\n  function getIndex( prev?: boolean ): number {\n    return prev ? prevIndex : currIndex;\n  }\n\n  /**\n   * Verifies if the focus option is available or not.\n   *\n   * @return `true` if the slider has the focus option.\n   */\n  function hasFocus(): boolean {\n    return ! isUndefined( options.focus ) || options.isNavigation;\n  }\n\n  return {\n    mount,\n    go,\n    scroll,\n    getNext,\n    getPrev,\n    getEnd,\n    setIndex,\n    getIndex,\n    toIndex,\n    toPage,\n    toDest,\n    hasFocus,\n  };\n}\n", "/**\r\n * The namespace for SVG elements.\r\n */\r\nexport const XML_NAME_SPACE = 'http://www.w3.org/2000/svg';\r\n\r\n/**\r\n * The arrow path.\r\n */\r\nexport const PATH = 'm15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z';\r\n\r\n/**\r\n * SVG width and height.\r\n */\r\nexport const SIZE = 40;\r\n", "import { ALL_ATTRIBUTES, ARIA_CONTROLS, ARIA_LABEL } from '../../constants/attributes';\r\nimport {\r\n  EVENT_ARROWS_MOUNTED,\r\n  EVENT_ARROWS_UPDATED,\r\n  EVENT_MOUNTED,\r\n  EVENT_MOVED,\r\n  EVENT_REFRESH,\r\n  EVENT_SCROLLED,\r\n  EVENT_UPDATED,\r\n} from '../../constants/events';\r\nimport { EventInterface } from '../../constructors';\r\nimport { Splide } from '../../core/Splide/Splide';\r\nimport { BaseComponent, Components, Options } from '../../types';\r\nimport { append, before, child, create, display, parseHtml, remove, removeAttribute, setAttribute } from '../../utils';\r\nimport { PATH, SIZE, XML_NAME_SPACE } from './path';\r\n\r\n\r\n/**\r\n * The interface for the Arrows component.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport interface ArrowsComponent extends BaseComponent {\r\n  arrows: { prev?: HTMLButtonElement, next?: HTMLButtonElement };\r\n}\r\n\r\n/**\r\n * The component for handling previous and next arrows.\r\n *\r\n * @since 3.0.0\r\n *\r\n * @param Splide     - A Splide instance.\r\n * @param Components - A collection of components.\r\n * @param options    - Options.\r\n *\r\n * @return An Arrows component object.\r\n */\r\nexport function Arrows( Splide: Splide, Components: Components, options: Options ): ArrowsComponent {\r\n  const { on, bind, emit } = EventInterface( Splide );\r\n  const { classes, i18n } = options;\r\n  const { Elements, Controller } = Components;\r\n\r\n  /**\r\n   * The wrapper element.\r\n   */\r\n  let wrapper = Elements.arrows;\r\n\r\n  /**\r\n   * The previous arrow element.\r\n   */\r\n  let prev = Elements.prev;\r\n\r\n  /**\r\n   * The next arrow element.\r\n   */\r\n  let next = Elements.next;\r\n\r\n  /**\r\n   * Indicates whether the component creates arrows or retrieved from the DOM.\r\n   */\r\n  let created: boolean;\r\n\r\n  /**\r\n   * An object with previous and next arrows.\r\n   */\r\n  const arrows: ArrowsComponent[ 'arrows' ] = {};\r\n\r\n  /**\r\n   * Called when the component is mounted.\r\n   */\r\n  function mount(): void {\r\n    init();\r\n    on( EVENT_UPDATED, init );\r\n  }\r\n\r\n  /**\r\n   * Initializes the component.\r\n   */\r\n  function init(): void {\r\n    if ( options.arrows ) {\r\n      if ( ! prev || ! next ) {\r\n        createArrows();\r\n      }\r\n    }\r\n\r\n    if ( prev && next ) {\r\n      if ( ! arrows.prev ) {\r\n        const { id } = Elements.track;\r\n\r\n        setAttribute( prev, ARIA_CONTROLS, id );\r\n        setAttribute( next, ARIA_CONTROLS, id );\r\n\r\n        arrows.prev = prev;\r\n        arrows.next = next;\r\n\r\n        listen();\r\n\r\n        emit( EVENT_ARROWS_MOUNTED, prev, next );\r\n      } else {\r\n        display( wrapper, options.arrows === false ? 'none' : '' );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Destroys the component.\r\n   */\r\n  function destroy(): void {\r\n    if ( created ) {\r\n      remove( wrapper );\r\n    } else {\r\n      removeAttribute( prev, ALL_ATTRIBUTES );\r\n      removeAttribute( next, ALL_ATTRIBUTES );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Listens to some events.\r\n   */\r\n  function listen(): void {\r\n    const { go } = Controller;\r\n    on( [ EVENT_MOUNTED, EVENT_MOVED, EVENT_UPDATED, EVENT_REFRESH, EVENT_SCROLLED ], update );\r\n    bind( next, 'click', () => { go( '>', true ) } );\r\n    bind( prev, 'click', () => { go( '<', true ) } );\r\n  }\r\n\r\n  /**\r\n   * Create arrows and append them to the slider.\r\n   */\r\n  function createArrows(): void {\r\n    wrapper = create( 'div', classes.arrows );\r\n    prev    = createArrow( true );\r\n    next    = createArrow( false );\r\n    created = true;\r\n\r\n    append( wrapper, [ prev, next ] );\r\n    before( wrapper, child( options.arrows === 'slider' && Elements.slider || Splide.root ) );\r\n  }\r\n\r\n  /**\r\n   * Creates an arrow button.\r\n   *\r\n   * @param prev - Determines whether to create a previous or next arrow.\r\n   *\r\n   * @return A created button element.\r\n   */\r\n  function createArrow( prev: boolean ): HTMLButtonElement {\r\n    const arrow = `<button class=\"${ classes.arrow } ${ prev ? classes.prev : classes.next }\" type=\"button\">`\r\n      +\t`<svg xmlns=\"${ XML_NAME_SPACE }\" viewBox=\"0 0 ${ SIZE } ${ SIZE }\" width=\"${ SIZE }\" height=\"${ SIZE }\">`\r\n      + `<path d=\"${ options.arrowPath || PATH }\" />`;\r\n\r\n    return parseHtml<HTMLButtonElement>( arrow );\r\n  }\r\n\r\n  /**\r\n   * Updates status of arrows, such as `disabled` and `aria-label`.\r\n   */\r\n  function update(): void {\r\n    const index     = Splide.index;\r\n    const prevIndex = Controller.getPrev();\r\n    const nextIndex = Controller.getNext();\r\n    const prevLabel = prevIndex > -1 && index < prevIndex ? i18n.last : i18n.prev;\r\n    const nextLabel = nextIndex > -1 && index > nextIndex ? i18n.first : i18n.next;\r\n\r\n    prev.disabled = prevIndex < 0;\r\n    next.disabled = nextIndex < 0;\r\n\r\n    setAttribute( prev, ARIA_LABEL, prevLabel );\r\n    setAttribute( next, ARIA_LABEL, nextLabel );\r\n\r\n    emit( EVENT_ARROWS_UPDATED, prev, next, prevIndex, nextIndex );\r\n  }\r\n\r\n  return {\r\n    arrows,\r\n    mount,\r\n    destroy,\r\n  };\r\n}\r\n", "import { ARIA_CONTROLS, ARIA_LABEL } from '../../constants/attributes';\nimport {\n  EVENT_AUTOPLAY_PAUSE,\n  EVENT_AUTOPLAY_PLAY,\n  EVENT_AUTOPLAY_PLAYING,\n  EVENT_MOVE,\n  EVENT_REFRESH,\n  EVENT_SCROLL,\n} from '../../constants/events';\nimport { EventInterface, RequestInterval } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { setAttribute, style } from '../../utils';\n\n\n/**\n * The interface for the Autoplay component.\n *\n * @since 3.0.0\n */\nexport interface AutoplayComponent extends BaseComponent {\n  play(): void;\n  pause(): void;\n  isPaused(): boolean;\n}\n\n/**\n * The component for auto playing sliders.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return An Autoplay component object.\n */\nexport function Autoplay( Splide: Splide, Components: Components, options: Options ): AutoplayComponent {\n  const { on, bind, emit } = EventInterface( Splide );\n  const { Elements } = Components;\n  const interval = RequestInterval( options.interval, Splide.go.bind( Splide, '>' ), update );\n  const { isPaused } = interval;\n\n  /**\n   * Indicates whether the slider is hovered or not.\n   */\n  let hovered: boolean;\n\n  /**\n   * Indicates whether one of slider elements has focus or not.\n   */\n  let focused: boolean;\n\n  /**\n   * Turns into `true` when autoplay is manually paused.\n   */\n  let paused: boolean;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    const { autoplay } = options;\n\n    if ( autoplay ) {\n      initButton( true );\n      initButton( false );\n      listen();\n\n      if ( autoplay !== 'pause' ) {\n        play();\n      }\n    }\n  }\n\n  /**\n   * Initializes a play/pause button.\n   *\n   * @param forPause - Determines whether to initialize a pause or play button.\n   */\n  function initButton( forPause: boolean ): void {\n    const prop   = forPause ? 'pause' : 'play';\n    const button = Elements[ prop ];\n\n    if ( button ) {\n      setAttribute( button, ARIA_CONTROLS, Elements.track.id );\n      setAttribute( button, ARIA_LABEL, options.i18n[ prop ] );\n\n      bind( button, 'click', forPause ? pause : play );\n    }\n  }\n\n  /**\n   * Listens to some events.\n   */\n  function listen(): void {\n    const { root } = Elements;\n\n    if ( options.pauseOnHover ) {\n      bind( root, 'mouseenter mouseleave', e => {\n        hovered = e.type === 'mouseenter';\n        autoToggle();\n      } );\n    }\n\n    if ( options.pauseOnFocus ) {\n      bind( root, 'focusin focusout', e => {\n        focused = e.type === 'focusin';\n        autoToggle();\n      } );\n    }\n\n    on( [ EVENT_MOVE, EVENT_SCROLL, EVENT_REFRESH ], interval.rewind );\n  }\n\n  /**\n   * Starts autoplay and clears all flags.\n   */\n  function play(): void {\n    if ( isPaused() && Components.Slides.isEnough() ) {\n      interval.start( ! options.resetProgress );\n      focused = hovered = paused = false;\n      emit( EVENT_AUTOPLAY_PLAY );\n    }\n  }\n\n  /**\n   * Pauses autoplay.\n   *\n   * @param manual - If `true`, autoplay keeps paused until `play()` is explicitly called.\n   */\n  function pause( manual = true ): void {\n    if ( ! isPaused() ) {\n      interval.pause();\n      emit( EVENT_AUTOPLAY_PAUSE );\n    }\n\n    paused = manual;\n  }\n\n  /**\n   * Toggles play/pause according to current flags.\n   * If autoplay is manually paused, this will do nothing.\n   */\n  function autoToggle(): void {\n    if ( ! paused ) {\n      if ( ! hovered && ! focused ) {\n        play();\n      } else {\n        pause( false );\n      }\n    }\n  }\n\n  /**\n   * Called on every animation frame when auto playing.\n   *\n   * @param rate - The progress rate between 0 to 1.\n   */\n  function update( rate: number ): void {\n    const { bar } = Elements;\n\n    if ( bar ) {\n      style( bar, 'width', `${ rate * 100 }%` );\n    }\n\n    emit( EVENT_AUTOPLAY_PLAYING, rate );\n  }\n\n  return {\n    mount,\n    destroy: interval.cancel,\n    play,\n    pause,\n    isPaused,\n  };\n}\n", "import { EVENT_LAZYLOAD_LOADED, EVENT_MOUNTED, EVENT_REFRESH, EVENT_UPDATED } from '../../constants/events';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { child, display } from '../../utils';\nimport { SlideComponent } from '../Slides/Slide';\n\n\n/**\n * The interface for the Cover component.\n *\n * @since 3.0.0\n */\nexport interface CoverComponent extends BaseComponent {\n}\n\n/**\n * The component for setting the image as the slide background.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Cover component object.\n */\nexport function Cover( Splide: Splide, Components: Components, options: Options ): CoverComponent {\n  const { on } = EventInterface( Splide );\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    if ( options.cover ) {\n      on( EVENT_LAZYLOAD_LOADED, ( img, Slide ) => { toggle( true, img, Slide ) } );\n      on( [ EVENT_MOUNTED, EVENT_UPDATED, EVENT_REFRESH ], apply.bind( null, true ) );\n    }\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy(): void {\n    apply( false );\n  }\n\n  /**\n   * Sets/removes the background image to/from all slides.\n   *\n   * @param cover - If `false`, removes the background image.\n   */\n  function apply( cover: boolean ): void {\n    Components.Slides.forEach( Slide => {\n      const img = child<HTMLImageElement>( Slide.container || Slide.slide, 'img' );\n\n      if ( img && img.src ) {\n        toggle( cover, img, Slide );\n      }\n    } );\n  }\n\n  /**\n   * Sets/removes the background image to/from the parent element.\n   *\n   * @param cover - If `false`, removes the background image.\n   * @param img   - A target image element.\n   * @param Slide - A SlideComponent object where the image belongs.\n   */\n  function toggle( cover: boolean, img: HTMLImageElement, Slide: SlideComponent ): void {\n    Slide.style( 'background', cover ? `center/cover no-repeat url(\"${ img.src }\")` : '', true );\n    display( img, cover ? 'none' : '' );\n  }\n\n  return {\n    mount,\n    destroy,\n  };\n}\n", "/**\r\n * Triggers the bounce effect when the diff becomes less than this value.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const BOUNCE_DIFF_THRESHOLD = 10;\r\n\r\n/**\r\n * The duration of the bounce effect.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const BOUNCE_DURATION = 600;\r\n\r\n/**\r\n * The friction factor.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const FRICTION_FACTOR = 0.6;\r\n\r\n/**\r\n * The velocity to calculate the scroll duration.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const BASE_VELOCITY = 1.5;\r\n\r\n/**\r\n * The minimum duration of scroll.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const MIN_DURATION = 800;\r\n", "import { EVENT_MOVE, EVENT_REFRESH, EVENT_SCROLL, EVENT_SCROLLED, EVENT_UPDATED } from '../../constants/events';\r\nimport { SLIDE } from '../../constants/types';\r\nimport { EventInterface, RequestInterval, RequestIntervalInterface } from '../../constructors';\r\nimport { Splide } from '../../core/Splide/Splide';\r\nimport { AnyFunction, BaseComponent, Components, Options } from '../../types';\r\nimport { abs, between, max } from '../../utils';\r\nimport { BASE_VELOCITY, BOUNCE_DIFF_THRESHOLD, BOUNCE_DURATION, FRICTION_FACTOR, MIN_DURATION } from './constants';\r\n\r\n\r\n/**\r\n * The interface for the Scroll component.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport interface ScrollComponent extends BaseComponent {\r\n  scroll( position: number, duration?: number, callback?: AnyFunction ): void;\r\n  cancel(): void;\r\n}\r\n\r\n/**\r\n * The component for scrolling the slider.\r\n *\r\n * @since 3.0.0\r\n *\r\n * @param Splide     - A Splide instance.\r\n * @param Components - A collection of components.\r\n * @param options    - Options.\r\n *\r\n * @return A Scroll component object.\r\n */\r\nexport function Scroll( Splide: Splide, Components: Components, options: Options ): ScrollComponent {\r\n  const { on, emit } = EventInterface( Splide );\r\n  const { Move } = Components;\r\n  const { getPosition, getLimit, exceededLimit } = Move;\r\n\r\n  /**\r\n   * Retains the active RequestInterval object.\r\n   */\r\n  let interval: RequestIntervalInterface;\r\n\r\n  /**\r\n   * Holds the callback function.\r\n   */\r\n  let scrollCallback: AnyFunction;\r\n\r\n  /**\r\n   * Called when the component is mounted.\r\n   */\r\n  function mount(): void {\r\n    on( EVENT_MOVE, clear );\r\n    on( [ EVENT_UPDATED, EVENT_REFRESH ], cancel );\r\n  }\r\n\r\n  /**\r\n   * Scrolls the slider to the provided destination.\r\n   *\r\n   * @param destination        - The destination to scroll to.\r\n   * @param duration           - Optional. The scroll duration. If omitted, calculates it by the distance.\r\n   * @param callback           - Optional. A callback invoked after scroll ends.\r\n   * @param suppressConstraint - Optional. Whether to suppress constraint process when the slider exceeds bounds.\r\n   */\r\n  function scroll(\r\n    destination: number,\r\n    duration?: number,\r\n    callback?: AnyFunction,\r\n    suppressConstraint?: boolean\r\n  ): void {\r\n    const start = getPosition();\r\n    let friction = 1;\r\n\r\n    duration       = duration || computeDuration( abs( destination - start ) );\r\n    scrollCallback = callback;\r\n    clear();\r\n\r\n    interval = RequestInterval( duration, onScrolled, rate => {\r\n      const position = getPosition();\r\n      const target   = start + ( destination - start ) * easing( rate );\r\n      const diff     = ( target - getPosition() ) * friction;\r\n\r\n      Move.translate( position + diff );\r\n\r\n      if ( Splide.is( SLIDE ) && ! suppressConstraint && exceededLimit() ) {\r\n        friction *= FRICTION_FACTOR;\r\n\r\n        if ( abs( diff ) < BOUNCE_DIFF_THRESHOLD ) {\r\n          bounce( exceededLimit( false ) );\r\n        }\r\n      }\r\n    }, 1 );\r\n\r\n    emit( EVENT_SCROLL );\r\n    interval.start();\r\n  }\r\n\r\n  /**\r\n   * Triggers the bounce effect when the slider reaches bounds.\r\n   *\r\n   * @param backwards - The direction the slider is going towards.\r\n   */\r\n  function bounce( backwards: boolean ): void {\r\n    scroll( getLimit( ! backwards ), BOUNCE_DURATION, null, true );\r\n  }\r\n\r\n  /**\r\n   * Called when scroll ends or has been just canceled.\r\n   */\r\n  function onScrolled(): void {\r\n    const position = getPosition();\r\n    const index = Move.toIndex( position );\r\n\r\n    if ( ! between( index, 0, Splide.length - 1 ) ) {\r\n      Move.translate( Move.shift( position, index > 0 ), true );\r\n    }\r\n\r\n    scrollCallback && scrollCallback();\r\n    emit( EVENT_SCROLLED );\r\n  }\r\n\r\n  /**\r\n   * Computes the scroll duration by the distance and the base velocity.\r\n   *\r\n   * @param distance - Distance in pixel.\r\n   *\r\n   * @return The duration for scroll.\r\n   */\r\n  function computeDuration( distance: number ): number {\r\n    return max( distance / BASE_VELOCITY, MIN_DURATION );\r\n  }\r\n\r\n  /**\r\n   * Clears the active interval.\r\n   */\r\n  function clear(): void {\r\n    if ( interval ) {\r\n      interval.cancel();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cancels the active interval and emits the `scrolled` event.\r\n   */\r\n  function cancel(): void {\r\n    if ( interval && ! interval.isPaused() ) {\r\n      clear();\r\n      onScrolled();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * The easing function.\r\n   *\r\n   * @param t - A value to ease.\r\n   *\r\n   * @return An eased value.\r\n   */\r\n  function easing( t: number ): number {\r\n    const { easingFunc } = options;\r\n    return easingFunc ? easingFunc( t ) : 1 - Math.pow( 1 - t, 4 );\r\n  }\r\n\r\n  return {\r\n    mount,\r\n    destroy: clear,\r\n    scroll,\r\n    cancel,\r\n  };\r\n}\r\n", "/**\n * The power of the friction.\n *\n * @since 3.0.0\n */\nexport const FRICTION = 5;\n\n/**\n * If the user stops dragging for this duration with keeping the pointer down, updates the base coord and time.\n *\n * @since 3.0.0\n */\nexport const LOG_INTERVAL = 200;\n\n/**\n * Start events for dragging.\n *\n * @since 3.0.0\n */\nexport const POINTER_DOWN_EVENTS = 'touchstart mousedown';\n\n/**\n * Update events for dragging.\n *\n * @since 3.0.0\n */\nexport const POINTER_MOVE_EVENTS = 'touchmove mousemove';\n\n/**\n * End events for dragging.\n *\n * @since 3.0.0\n */\nexport const POINTER_UP_EVENTS = 'touchend touchcancel mouseup';\n", "import { EVENT_DRAG, EVENT_DRAGGED, EVENT_DRAGGING, EVENT_MOUNTED, EVENT_UPDATED } from '../../constants/events';\nimport { FADE, LOOP, SLIDE } from '../../constants/types';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { abs, isHTMLElement, isObject, matches, min, noop, prevent, sign } from '../../utils';\nimport { FRICTION, LOG_INTERVAL, POINTER_DOWN_EVENTS, POINTER_MOVE_EVENTS, POINTER_UP_EVENTS } from './constants';\n\n\n/**\n * The interface for the Drag component.\n *\n * @since 3.0.0\n */\nexport interface DragComponent extends BaseComponent {\n  disable( disabled: boolean ): void;\n  isDragging(): boolean;\n}\n\n/**\n * The component for dragging the slider.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Drag component object.\n */\nexport function Drag( Splide: Splide, Components: Components, options: Options ): DragComponent {\n  const { on, emit, bind, unbind } = EventInterface( Splide );\n  const { Move, Scroll, Controller } = Components;\n  const { track } = Components.Elements;\n  const { resolve, orient } = Components.Direction;\n  const { getPosition, exceededLimit } = Move;\n  const listenerOptions = { passive: false, capture: true };\n\n  /**\n   * The base slider position to calculate the delta of coords.\n   */\n  let basePosition: number;\n\n  /**\n   * The base event object saved per specific sampling interval.\n   */\n  let baseEvent: TouchEvent | MouseEvent;\n\n  /**\n   * Holds the previous base event object.\n   */\n  let prevBaseEvent: TouchEvent | MouseEvent;\n\n  /**\n   * Keeps the last TouchEvent/MouseEvent object on pointermove.\n   */\n  let lastEvent: TouchEvent | MouseEvent;\n\n  /**\n   * Indicates whether the drag mode is `free` or not.\n   */\n  let isFree: boolean;\n\n  /**\n   * Indicates whether the user is dragging the slider or not.\n   */\n  let dragging: boolean;\n\n  /**\n   * Indicates whether the slider exceeds limits or not.\n   * This must not be `undefined` for strict comparison.\n   */\n  let hasExceeded = false;\n\n  /**\n   * Turns into `true` when the user starts dragging the slider.\n   */\n  let clickPrevented: boolean;\n\n  /**\n   * Indicates whether the drag component is now disabled or not.\n   */\n  let disabled: boolean;\n\n  /**\n   * The target element to attach listeners.\n   */\n  let target: Window | HTMLElement;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    bind( track, POINTER_MOVE_EVENTS, noop, listenerOptions );\n    bind( track, POINTER_UP_EVENTS, noop, listenerOptions );\n    bind( track, POINTER_DOWN_EVENTS, onPointerDown, listenerOptions );\n    bind( track, 'click', onClick, { capture: true } );\n    bind( track, 'dragstart', prevent );\n\n    on( [ EVENT_MOUNTED, EVENT_UPDATED ], init );\n  }\n\n  /**\n   * Initializes the component.\n   */\n  function init(): void {\n    const { drag } = options;\n    disable( ! drag );\n    isFree = drag === 'free';\n  }\n\n  /**\n   * Called when the user clicks or touches the slider.\n   * Needs to prevent the default behaviour when the slider is busy to deny any action, such as dragging images.\n   * Note that IE does not support MouseEvent and TouchEvent constructors.\n   *\n   * @param e - A TouchEvent or MouseEvent object\n   */\n  function onPointerDown( e: TouchEvent | MouseEvent ): void {\n    if ( ! disabled ) {\n      const { noDrag } = options;\n      const isTouch     = isTouchEvent( e );\n      const isDraggable = ! noDrag || ( isHTMLElement( e.target ) && ! matches( e.target, noDrag ) );\n\n      if ( isDraggable && ( isTouch || ! e.button ) ) {\n        if ( ! Move.isBusy() ) {\n          target         = isTouch ? track : window;\n          prevBaseEvent  = null;\n          lastEvent      = null;\n          clickPrevented = false;\n\n          bind( target, POINTER_MOVE_EVENTS, onPointerMove, listenerOptions );\n          bind( target, POINTER_UP_EVENTS, onPointerUp, listenerOptions );\n          Move.cancel();\n          Scroll.cancel();\n          save( e );\n        } else {\n          prevent( e, true );\n        }\n      }\n    }\n  }\n\n  /**\n   * Called while the user moves the pointer on the slider.\n   *\n   * @param e - A TouchEvent or MouseEvent object\n   */\n  function onPointerMove( e: TouchEvent | MouseEvent ): void {\n    if ( ! lastEvent ) {\n      emit( EVENT_DRAG );\n    }\n\n    lastEvent = e;\n\n    if ( e.cancelable ) {\n      const diff = coordOf( e ) - coordOf( baseEvent );\n\n      if ( dragging ) {\n        Move.translate( basePosition + constrain( diff ) );\n\n        const expired  = timeOf( e ) - timeOf( baseEvent ) > LOG_INTERVAL;\n        const exceeded = hasExceeded !== ( hasExceeded = exceededLimit() );\n\n        if ( expired || exceeded ) {\n          save( e );\n        }\n\n        emit( EVENT_DRAGGING );\n        clickPrevented = true;\n        prevent( e );\n      } else {\n        let { dragMinThreshold: thresholds } = options;\n        thresholds = isObject( thresholds ) ? thresholds : { mouse: 0, touch: +thresholds || 10 };\n        dragging   = abs( diff ) > ( isTouchEvent( e ) ? thresholds.touch : thresholds.mouse );\n\n        if ( isSliderDirection() ) {\n          prevent( e );\n        }\n      }\n    }\n  }\n\n  /**\n   * Called when the user releases pointing devices.\n   * Be aware that the TouchEvent object provided by the `touchend` does not contain `Touch` objects,\n   * which means the last touch position is not available.\n   *\n   * @param e - A TouchEvent or MouseEvent object\n   */\n  function onPointerUp( e: TouchEvent | MouseEvent ): void {\n    unbind( target, POINTER_MOVE_EVENTS, onPointerMove );\n    unbind( target, POINTER_UP_EVENTS, onPointerUp );\n\n    if ( lastEvent ) {\n      if ( dragging || ( e.cancelable && isSliderDirection() ) ) {\n        const velocity    = computeVelocity( e );\n        const destination = computeDestination( velocity );\n\n        if ( isFree ) {\n          Controller.scroll( destination );\n        } else if ( Splide.is( FADE ) ) {\n          Controller.go( Splide.index + orient( sign( velocity ) ) );\n        } else {\n          Controller.go( Controller.toDest( destination ), true );\n        }\n\n        prevent( e );\n      }\n\n      emit( EVENT_DRAGGED );\n    }\n\n    dragging = false;\n  }\n\n  /**\n   * Saves data at the specific moment.\n   *\n   * @param e  A TouchEvent or MouseEvent object\n   */\n  function save( e: TouchEvent | MouseEvent ): void {\n    prevBaseEvent = baseEvent;\n    baseEvent     = e;\n    basePosition  = getPosition();\n  }\n\n  /**\n   * Called when the track element is clicked.\n   * Disables click any elements inside it while dragging.\n   *\n   * @param e - A MouseEvent object.\n   */\n  function onClick( e: MouseEvent ): void {\n    if ( ! disabled && clickPrevented ) {\n      prevent( e, true );\n    }\n  }\n\n  /**\n   * Checks whether dragging towards the slider or scroll direction.\n   *\n   * @return `true` if going towards the slider direction, or otherwise `false`.\n   */\n  function isSliderDirection(): boolean {\n    const diffX = abs( coordOf( lastEvent ) - coordOf( baseEvent ) );\n    const diffY = abs( coordOf( lastEvent, true ) - coordOf( baseEvent, true ) );\n    return diffX > diffY;\n  }\n\n  /**\n   * Computes the drag velocity.\n   *\n   * @param e - A TouchEvent or MouseEvent object\n   *\n   * @return The drag velocity.\n   */\n  function computeVelocity( e: TouchEvent | MouseEvent ): number {\n    if ( Splide.is( LOOP ) || ! hasExceeded ) {\n      const base      = baseEvent === lastEvent && prevBaseEvent || baseEvent;\n      const diffCoord = coordOf( lastEvent ) - coordOf( base );\n      const diffTime  = timeOf( e ) - timeOf( base );\n      const isFlick   = timeOf( e ) - timeOf( lastEvent ) < LOG_INTERVAL;\n\n      if ( diffTime && isFlick ) {\n        return diffCoord / diffTime;\n      }\n    }\n\n    return 0;\n  }\n\n  /**\n   * Computes the destination by the velocity and the `flickPower` option.\n   *\n   * @param velocity - The drag velocity.\n   *\n   * @return The destination.\n   */\n  function computeDestination( velocity: number ): number {\n    return getPosition() + sign( velocity ) * min(\n      abs( velocity ) * ( options.flickPower || 600 ),\n      isFree ? Infinity : Components.Layout.listSize() * ( options.flickMaxPages || 1 )\n    );\n  }\n\n  /**\n   * Returns the `pageX` and `pageY` coordinates provided by the event.\n   * Be aware that IE does not support both TouchEvent and MouseEvent constructors.\n   *\n   * @param e          - A TouchEvent or MouseEvent object.\n   * @param orthogonal - Optional. If `true`, returns the coord of the orthogonal axis against the drag one.\n   *\n   * @return A pageX or pageY coordinate.\n   */\n  function coordOf( e: TouchEvent | MouseEvent, orthogonal?: boolean ): number {\n    return ( isTouchEvent( e ) ? e.touches[ 0 ] : e )[ `page${ resolve( orthogonal ? 'Y' : 'X' ) }` ];\n  }\n\n  /**\n   * Returns the time stamp in the provided event object.\n   *\n   * @param e - A TouchEvent or MouseEvent object.\n   *\n   * @return A time stamp.\n   */\n  function timeOf( e: TouchEvent | MouseEvent ): number {\n    return e.timeStamp;\n  }\n\n  /**\n   * Reduces the distance to move by the predefined friction.\n   * This does nothing when the slider type is not `slide`, or the position is inside borders.\n   *\n   * @param diff - Diff to constrain.\n   *\n   * @return The constrained diff.\n   */\n  function constrain( diff: number ): number {\n    return diff / ( hasExceeded && Splide.is( SLIDE ) ? FRICTION : 1 );\n  }\n\n  /**\n   * Checks if the provided event is TouchEvent or MouseEvent.\n   *\n   * @param e - An event to check.\n   *\n   * @return `true` if the `e` is TouchEvent.\n   */\n  function isTouchEvent( e: TouchEvent | MouseEvent ): e is TouchEvent {\n    return typeof TouchEvent !== 'undefined' && e instanceof TouchEvent;\n  }\n\n  /**\n   * Checks if now the user is dragging the slider or not.\n   *\n   * @return `true` if the user is dragging the slider or otherwise `false`.\n   */\n  function isDragging(): boolean {\n    return dragging;\n  }\n\n  /**\n   * Disables the component.\n   *\n   * @param value - Set `true` to disable the component.\n   */\n  function disable( value: boolean ): void {\n    disabled = value;\n  }\n\n  return {\n    mount,\n    disable,\n    isDragging,\n  };\n}\n", "import { TAB_INDEX } from '../../constants/attributes';\nimport { EVENT_UPDATED, EVENT_MOVE } from '../../constants/events';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { includes, isHTMLElement, nextTick, removeAttribute, setAttribute } from '../../utils';\n\n\n/**\n * The interface for the Keyboard component.\n *\n * @since 3.0.0\n */\nexport interface KeyboardComponent extends BaseComponent {\n}\n\n/**\n * The collection of arrow keys of IE.\n *\n * @since 3.0.0\n */\nconst IE_ARROW_KEYS = [ 'Left', 'Right', 'Up', 'Down' ];\n\n/**\n * The component for controlling the slider by keyboards.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Keyboard component object.\n */\nexport function Keyboard( Splide: Splide, Components: Components, options: Options ): KeyboardComponent {\n  const { on, bind, unbind } = EventInterface( Splide );\n  const { root } = Components.Elements;\n  const { resolve } = Components.Direction;\n\n  /**\n   * The target element of the keyboard event.\n   */\n  let target: Window | HTMLElement;\n\n  /**\n   * Indicates whether the component is currently disabled or not.\n   */\n  let disabled: boolean;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    init();\n    on( EVENT_UPDATED, onUpdated );\n    on( EVENT_MOVE, onMove );\n  }\n\n  /**\n   * Initializes the component.\n   */\n  function init(): void {\n    const { keyboard = 'global' } = options;\n\n    if ( keyboard ) {\n      if ( keyboard === 'focused' ) {\n        target = root;\n        setAttribute( root, TAB_INDEX, 0 );\n      } else {\n        target = window;\n      }\n\n      bind( target, 'keydown', onKeydown );\n    }\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy(): void {\n    unbind( target, 'keydown' );\n\n    if ( isHTMLElement( target ) ) {\n      removeAttribute( target, TAB_INDEX );\n    }\n  }\n\n  /**\n   * Called when the slider moves.\n   * To avoid the slider from moving twice, wait for a tick.\n   */\n  function onMove(): void {\n    disabled = true;\n    nextTick( () => { disabled = false } );\n  }\n\n  /**\n   * Called when options are update.\n   */\n  function onUpdated(): void {\n    destroy();\n    init();\n  }\n\n  /**\n   * Called when any key is pressed on the target.\n   *\n   * @param e - A KeyboardEvent object.\n   */\n  function onKeydown( e: KeyboardEvent ): void {\n    if ( ! disabled ) {\n      const { key } = e;\n      const normalizedKey = includes( IE_ARROW_KEYS, key ) ? `Arrow${ key }` : key;\n\n      if ( normalizedKey === resolve( 'ArrowLeft' ) ) {\n        Splide.go( '<' );\n      } else if ( normalizedKey === resolve( 'ArrowRight' ) ) {\n        Splide.go( '>' );\n      }\n    }\n  }\n\n  return {\n    mount,\n    destroy,\n  };\n}\n", "import { DATA_ATTRIBUTE } from '../../constants/project';\r\n\r\n\r\n/**\r\n * The data attribute for the src value.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const SRC_DATA_ATTRIBUTE = `${ DATA_ATTRIBUTE }-lazy`;\r\n\r\n/**\r\n * The data attribute for the srcset value.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const SRCSET_DATA_ATTRIBUTE = `${ SRC_DATA_ATTRIBUTE }-srcset`;\r\n\r\n/**\r\n * The selector string for images to load.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const IMAGE_SELECTOR = `[${ SRC_DATA_ATTRIBUTE }], [${ SRCSET_DATA_ATTRIBUTE }]`;\r\n", "import { ROLE } from '../../constants/attributes';\nimport { CLASS_LOADING } from '../../constants/classes';\nimport {\n  EVENT_LAZYLOAD_LOADED,\n  EVENT_MOUNTED,\n  EVENT_MOVED,\n  EVENT_REFRESH,\n  EVENT_RESIZE,\n} from '../../constants/events';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport {\n  addClass,\n  create,\n  display,\n  getAttribute,\n  queryAll,\n  remove,\n  removeAttribute,\n  removeClass,\n  setAttribute,\n} from '../../utils';\nimport { SlideComponent } from '../Slides/Slide';\nimport { IMAGE_SELECTOR, SRC_DATA_ATTRIBUTE, SRCSET_DATA_ATTRIBUTE } from './constants';\n\n\n/**\n * The interface for the LazyLoad component.\n *\n * @since 3.0.0\n */\nexport interface LazyLoadComponent extends BaseComponent {\n}\n\n/**\n * The interface for all components.\n *\n * @since 3.0.0\n */\nexport interface LazyLoadImagesData {\n  _img: HTMLImageElement;\n  _spinner: HTMLSpanElement;\n  _Slide: SlideComponent;\n  src: string | null;\n  srcset: string | null;\n}\n\n/**\n * The component for lazily loading images.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return An LazyLoad component object.\n */\nexport function LazyLoad( Splide: Splide, Components: Components, options: Options ): LazyLoadComponent {\n  const { on, off, bind, emit } = EventInterface( Splide );\n  const isSequential = options.lazyLoad === 'sequential';\n\n  /**\n   * Stores data of images.\n   */\n  let images: LazyLoadImagesData[] = [];\n\n  /**\n   * The current index of images.\n   */\n  let index = 0;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    if ( options.lazyLoad ) {\n      on( [ EVENT_MOUNTED, EVENT_REFRESH ], () => {\n        destroy();\n        init();\n      } );\n\n      if ( ! isSequential ) {\n        on( [ EVENT_MOUNTED, EVENT_REFRESH, EVENT_MOVED ], observe );\n      }\n    }\n  }\n\n  /**\n   * Finds images that contain specific data attributes.\n   */\n  function init() {\n    Components.Slides.forEach( _Slide => {\n      queryAll<HTMLImageElement>( _Slide.slide, IMAGE_SELECTOR ).forEach( _img => {\n        const src    = getAttribute( _img, SRC_DATA_ATTRIBUTE );\n        const srcset = getAttribute( _img, SRCSET_DATA_ATTRIBUTE );\n\n        if ( src !== _img.src || srcset !== _img.srcset ) {\n          const _spinner = create( 'span', options.classes.spinner, _img.parentElement );\n          setAttribute( _spinner, ROLE, 'presentation' );\n          images.push( { _img, _Slide, src, srcset, _spinner } );\n          ! _img.src && display( _img, 'none' );\n        }\n      } );\n    } );\n\n    if ( isSequential ) {\n      loadNext();\n    }\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy() {\n    index  = 0;\n    images = [];\n  }\n\n  /**\n   * Checks how close each image is from the active slide, and determines whether to start loading or not.\n   * The last `+1` is for the current page.\n   */\n  function observe(): void {\n    images = images.filter( data => {\n      const distance = options.perPage * ( ( options.preloadPages || 1 ) + 1 ) - 1;\n\n      if ( data._Slide.isWithin( Splide.index, distance ) ) {\n        return load( data );\n      }\n\n      return true;\n    } );\n\n    if ( ! images.length ) {\n      off( EVENT_MOVED );\n    }\n  }\n\n  /**\n   * Starts loading the image in the data.\n   *\n   * @param data - A LazyLoadImagesData object.\n   */\n  function load( data: LazyLoadImagesData ): void {\n    const { _img } = data;\n\n    addClass( data._Slide.slide, CLASS_LOADING );\n    bind( _img, 'load error', e => { onLoad( data, e.type === 'error' ) } );\n\n    [ 'src', 'srcset' ].forEach( name => {\n      if ( data[ name ] ) {\n        setAttribute( _img, name, data[ name ] );\n        removeAttribute( _img, name === 'src' ? SRC_DATA_ATTRIBUTE : SRCSET_DATA_ATTRIBUTE );\n      }\n    } );\n  }\n\n  /**\n   * Called when the image is loaded or any error occurs.\n   *\n   * @param data  - A LazyLoadImagesData object.\n   * @param error - `true` if this method is called on error.\n   */\n  function onLoad( data: LazyLoadImagesData, error: boolean ): void {\n    const { _Slide } = data;\n\n    removeClass( _Slide.slide, CLASS_LOADING );\n\n    if ( ! error ) {\n      remove( data._spinner );\n      display( data._img, '' );\n      emit( EVENT_LAZYLOAD_LOADED, data._img, _Slide );\n      emit( EVENT_RESIZE );\n    }\n\n    if ( isSequential ) {\n      loadNext();\n    }\n  }\n\n  /**\n   * Starts loading a next image.\n   */\n  function loadNext(): void {\n    if ( index < images.length ) {\n      load( images[ index++ ] );\n    }\n  }\n\n  return {\n    mount,\n    destroy,\n  };\n}\n", "import { ARIA_CONTROLS, ARIA_CURRENT, ARIA_LABEL } from '../../constants/attributes';\nimport { CLASS_ACTIVE } from '../../constants/classes';\nimport {\n  EVENT_MOVE,\n  EVENT_PAGINATION_MOUNTED,\n  EVENT_PAGINATION_UPDATED,\n  EVENT_REFRESH,\n  EVENT_SCROLLED,\n  EVENT_UPDATED,\n} from '../../constants/events';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport {\n  addClass,\n  ceil,\n  create,\n  empty,\n  focus,\n  format,\n  remove,\n  removeAttribute,\n  removeClass,\n  setAttribute,\n} from '../../utils';\n\n\n/**\n * The interface for the Pagination component.\n *\n * @since 3.0.0\n */\nexport interface PaginationComponent extends BaseComponent {\n  items: PaginationItem[];\n  getAt( index: number ): PaginationItem;\n}\n\n/**\n * The interface for data of the pagination.\n *\n * @since 3.0.0\n */\nexport interface PaginationData {\n  list: HTMLUListElement;\n  items: PaginationItem[];\n}\n\n/**\n * The interface for each pagination item.\n *\n * @since 3.0.0\n */\nexport interface PaginationItem {\n  li: HTMLLIElement;\n  button: HTMLButtonElement;\n  page: number;\n}\n\n/**\n * The component for handling previous and next arrows.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Arrows component object.\n */\nexport function Pagination( Splide: Splide, Components: Components, options: Options ): PaginationComponent {\n  const { on, emit, bind, unbind } = EventInterface( Splide );\n  const { Slides, Elements, Controller } = Components;\n  const { hasFocus, getIndex } = Controller;\n\n  /**\n   * Stores all pagination items.\n   */\n  const items: PaginationItem[] = [];\n\n  /**\n   * The pagination element.\n   */\n  let list: HTMLUListElement;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    init();\n    on( [ EVENT_UPDATED, EVENT_REFRESH ], init );\n    on( [ EVENT_MOVE, EVENT_SCROLLED ], update );\n  }\n\n  /**\n   * Initializes the pagination.\n   */\n  function init(): void {\n    destroy();\n\n    if ( options.pagination && Slides.isEnough() ) {\n      createPagination();\n      emit( EVENT_PAGINATION_MOUNTED, { list, items }, getAt( Splide.index ) );\n      update();\n    }\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy(): void {\n    if ( list ) {\n      remove( list );\n      items.forEach( item => { unbind( item.button, 'click' ) } );\n      empty( items );\n      list = null;\n    }\n  }\n\n  /**\n   * Creates the pagination element and appends it to the slider.\n   */\n  function createPagination(): void {\n    const { length } = Splide;\n    const { classes, i18n, perPage } = options;\n    const parent = options.pagination === 'slider' && Elements.slider || Elements.root;\n    const max    = hasFocus() ? length : ceil( length / perPage );\n\n    list = create( 'ul', classes.pagination, parent );\n\n    for ( let i = 0; i < max; i++ ) {\n      const li       = create( 'li', null, list );\n      const button   = create( 'button', { class: classes.page, type: 'button' }, li );\n      const controls = Slides.getIn( i ).map( Slide => Slide.slide.id );\n      const text     = ! hasFocus() && perPage > 1 ? i18n.pageX : i18n.slideX;\n\n      bind( button, 'click', onClick.bind( null, i ) );\n\n      setAttribute( button, ARIA_CONTROLS, controls.join( ' ' ) );\n      setAttribute( button, ARIA_LABEL, format( text, i + 1 ) );\n\n      items.push( { li, button, page: i } );\n    }\n  }\n\n  /**\n   * Called when the user clicks each pagination dot.\n   * Moves the focus to the active slide for accessibility.\n   *\n   * @link https://www.w3.org/WAI/tutorials/carousels/functionality/\n   *\n   * @param page - A clicked page index.\n   */\n  function onClick( page: number ): void {\n    Controller.go( `>${ page }`, true, () => {\n      const Slide = Slides.getAt( Controller.toIndex( page ) );\n      Slide && focus( Slide.slide );\n    } );\n  }\n\n  /**\n   * Returns the pagination item at the specified index.\n   *\n   * @param index - An index.\n   *\n   * @return A pagination item object if available, or otherwise `undefined`.\n   */\n  function getAt( index: number ): PaginationItem | undefined {\n    return items[ Controller.toPage( index ) ];\n  }\n\n  /**\n   * Updates the pagination status.\n   */\n  function update(): void {\n    const prev = getAt( getIndex( true ) );\n    const curr = getAt( getIndex() );\n\n    if ( prev ) {\n      removeClass( prev.button, CLASS_ACTIVE );\n      removeAttribute( prev.button, ARIA_CURRENT );\n    }\n\n    if ( curr ) {\n      addClass( curr.button, CLASS_ACTIVE );\n      setAttribute( curr.button, ARIA_CURRENT, true );\n    }\n\n    emit( EVENT_PAGINATION_UPDATED, { list, items }, prev, curr );\n  }\n\n  return {\n    items,\n    mount,\n    destroy,\n    getAt,\n  };\n}\n", "import { ALL_ATTRIBUTES, ARIA_ORIENTATION, ROLE } from '../../constants/attributes';\nimport { TTB } from '../../constants/directions';\nimport {\n  EVENT_CLICK,\n  EVENT_MOUNTED,\n  EVENT_MOVE,\n  EVENT_NAVIGATION_MOUNTED,\n  EVENT_SLIDE_KEYDOWN,\n  EVENT_UPDATED,\n} from '../../constants/events';\nimport { LOOP } from '../../constants/types';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { empty, includes, prevent, removeAttribute, setAttribute } from '../../utils';\nimport { SlideComponent } from '../Slides/Slide';\n\n\n/**\n * The interface for the Sync component.\n *\n * @since 3.0.0\n */\nexport interface SyncComponent extends BaseComponent {\n}\n\n/**\n * The keys for triggering the navigation slide.\n *\n * @since 3.0.0\n */\nconst TRIGGER_KEYS = [ ' ', 'Enter', 'Spacebar' ];\n\n/**\n * The component for syncing multiple sliders.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Sync component object.\n */\nexport function Sync( Splide: Splide, Components: Components, options: Options ): SyncComponent {\n  const { splides } = Splide;\n  const { list } = Components.Elements;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    if ( options.isNavigation ) {\n      navigate();\n    } else {\n      sync();\n    }\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy(): void {\n    removeAttribute( list, ALL_ATTRIBUTES );\n  }\n\n  /**\n   * Syncs the current index among all slides.\n   * The `processed` array prevents recursive call of handlers.\n   */\n  function sync(): void {\n    const processed: Splide[] = [];\n\n    splides.concat( Splide ).forEach( ( splide, index, instances ) => {\n      const { on } = EventInterface( splide );\n\n      on( EVENT_MOVE, ( index, prev, dest ) => {\n        instances.forEach( instance => {\n          if ( instance !== splide && ! includes( processed, splide ) ) {\n            processed.push( instance );\n            instance.Components.Move.cancel();\n            instance.go( instance.is( LOOP ) ? dest : index );\n          }\n        } );\n\n        empty( processed );\n      } );\n    } );\n  }\n\n  /**\n   * Makes slides clickable and moves the slider to the index of clicked slide.\n   * Note that the direction of `menu` is implicitly `vertical` as default.\n   */\n  function navigate(): void {\n    const { on, emit } = EventInterface( Splide );\n\n    on( EVENT_CLICK, onClick );\n    on( EVENT_SLIDE_KEYDOWN, onKeydown );\n    on( [ EVENT_MOUNTED, EVENT_UPDATED ], update );\n\n    setAttribute( list, ROLE, 'menu' );\n\n    emit( EVENT_NAVIGATION_MOUNTED, Splide.splides );\n  }\n\n  /**\n   * Update attributes.\n   */\n  function update(): void {\n    setAttribute( list, ARIA_ORIENTATION, options.direction !== TTB ? 'horizontal' : null );\n  }\n\n  /**\n   * Called when the navigation slide is clicked.\n   *\n   * @param Slide - A clicked Slide component.\n   */\n  function onClick( Slide: SlideComponent ): void {\n    Splide.go( Slide.index );\n  }\n\n  /**\n   * Called when any key is pressed on the navigation slide.\n   *\n   * @param Slide - A Slide component.\n   * @param e     - A KeyboardEvent object.\n   */\n  function onKeydown( Slide: SlideComponent, e: KeyboardEvent ): void {\n    if ( includes( TRIGGER_KEYS, e.key ) ) {\n      onClick( Slide );\n      prevent( e );\n    }\n  }\n\n  return {\n    mount,\n    destroy,\n  };\n}\n", "import { EventInterface } from '../../constructors';\r\nimport { Splide } from '../../core/Splide/Splide';\r\nimport { BaseComponent, Components, Options } from '../../types';\r\nimport { prevent } from '../../utils';\r\n\r\n\r\n/**\r\n * The interface for the Wheel component.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport interface WheelComponent extends BaseComponent {\r\n}\r\n\r\n/**\r\n * The component for observing the mouse wheel and moving the slider.\r\n *\r\n * @since 3.0.0\r\n *\r\n * @param Splide     - A Splide instance.\r\n * @param Components - A collection of components.\r\n * @param options    - Options.\r\n *\r\n * @return A Wheel component object.\r\n */\r\nexport function Wheel( Splide: Splide, Components: Components, options: Options ): WheelComponent {\r\n  const { bind } = EventInterface( Splide );\r\n\r\n  /**\r\n   * Called when the component is mounted.\r\n   */\r\n  function mount(): void {\r\n    if ( options.wheel ) {\r\n      bind( Components.Elements.track, 'wheel', onWheel, { passive: false, capture: true } );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Called when the user rotates the mouse wheel.\r\n   *\r\n   * @param e - A WheelEvent object.\r\n   */\r\n  function onWheel( e: WheelEvent ): void {\r\n    const { deltaY } = e;\r\n\r\n    if ( deltaY ) {\r\n      Splide.go( deltaY < 0 ? '<' : '>' );\r\n      prevent( e );\r\n    }\r\n  }\r\n\r\n  return {\r\n    mount,\r\n  };\r\n}\r\n", "/**\r\n * The collection of i18n strings.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport const I18N = {\r\n  prev  : 'Previous slide',\r\n  next  : 'Next slide',\r\n  first : 'Go to first slide',\r\n  last  : 'Go to last slide',\r\n  slideX: 'Go to slide %s',\r\n  pageX : 'Go to page %s',\r\n  play  : 'Start autoplay',\r\n  pause : 'Pause autoplay',\r\n};\r\n", "import { Options } from '../types';\nimport { CLASSES } from './classes';\nimport { I18N } from './i18n';\n\n\n/**\n * The collection of default options.\n * Note that this collection does not contain all options.\n *\n * @since 3.0.0\n */\nexport const DEFAULTS: Options = {\n  type             : 'slide',\n  speed            : 400,\n  waitForTransition: true,\n  perPage          : 1,\n  arrows           : true,\n  pagination       : true,\n  interval         : 5000,\n  pauseOnHover     : true,\n  pauseOnFocus     : true,\n  resetProgress    : true,\n  easing           : 'cubic-bezier(0.25, 1, 0.5, 1)',\n  drag             : true,\n  direction        : 'ltr',\n  slideFocus       : true,\n  trimSpace        : true,\n  focusableNodes   : 'a, button, textarea, input, select, iframe',\n  classes          : CLASSES,\n  i18n             : I18N,\n};\n", "import { EVENT_MOUNTED, EVENT_REFRESH } from '../../constants/events';\r\nimport { EventInterface } from '../../constructors';\r\nimport { Splide } from '../../core/Splide/Splide';\r\nimport { Components, Options, TransitionComponent } from '../../types';\r\nimport { nextTick, noop, rect, unit, style } from '../../utils';\r\n\r\n\r\n/**\r\n * The component for the fade transition.\r\n *\r\n * @since 3.0.0\r\n *\r\n * @param Splide     - A Splide instance.\r\n * @param Components - A collection of components.\r\n * @param options    - Options.\r\n *\r\n * @return A Transition component object.\r\n */\r\nexport function Fade( Splide: Splide, Components: Components, options: Options ): TransitionComponent {\r\n  const { on } = EventInterface( Splide );\r\n\r\n  /**\r\n   * Called when the component is mounted.\r\n   * The nextTick disables the initial fade transition of the first slide.\r\n   */\r\n  function mount(): void {\r\n    on( [ EVENT_MOUNTED, EVENT_REFRESH ], () => {\r\n      nextTick( () => {\r\n        Components.Slides.style( 'transition', `opacity ${ options.speed }ms ${ options.easing }` );\r\n      } );\r\n    } );\r\n  }\r\n\r\n  /**\r\n   * Starts the transition.\r\n   * Explicitly sets the track height to avoid it will collapse in Safari.\r\n   *\r\n   * @param index - A destination index.\r\n   * @param done  - The callback function that must be called after the transition ends.\r\n   */\r\n  function start( index: number, done: () => void ): void {\r\n    const { track } = Components.Elements;\r\n    style( track, 'height', unit( rect( track ).height ) );\r\n\r\n    nextTick( () => {\r\n      done();\r\n      style( track, 'height', '' );\r\n    } );\r\n  }\r\n\r\n  return {\r\n    mount,\r\n    start,\r\n    cancel: noop,\r\n  };\r\n}\r\n", "import { SLIDE } from '../../constants/types';\r\nimport { EventInterface } from '../../constructors';\r\nimport { Splide } from '../../core/Splide/Splide';\r\nimport { Components, Options, TransitionComponent } from '../../types';\r\nimport { abs, style } from '../../utils';\r\n\r\n\r\n/**\r\n * The component for the slide transition.\r\n *\r\n * @since 3.0.0\r\n *\r\n * @param Splide     - A Splide instance.\r\n * @param Components - A collection of components.\r\n * @param options    - Options.\r\n *\r\n * @return A Transition component object.\r\n */\r\nexport function Slide( Splide: Splide, Components: Components, options: Options ): TransitionComponent {\r\n  const { bind } = EventInterface( Splide );\r\n  const { Move, Controller } = Components;\r\n  const { list } = Components.Elements;\r\n\r\n  /**\r\n   * Holds the `done` callback function.\r\n   */\r\n  let endCallback: () => void;\r\n\r\n  /**\r\n   * Called when the component is mounted.\r\n   */\r\n  function mount(): void {\r\n    bind( list, 'transitionend', e => {\r\n      if ( e.target === list && endCallback ) {\r\n        cancel();\r\n        endCallback();\r\n      }\r\n    } );\r\n  }\r\n\r\n  /**\r\n   * Starts the transition.\r\n   * The Move component calls this method just before the slider moves.\r\n   *\r\n   * @param index - A destination index.\r\n   * @param done  - The callback function that must be called after the transition ends.\r\n   */\r\n  function start( index: number, done: () => void ): void {\r\n    const destination = Move.toPosition( index, true );\r\n    const position    = Move.getPosition();\r\n    const speed       = getSpeed( index );\r\n\r\n    if ( abs( destination - position ) >= 1 && speed >= 1 ) {\r\n      apply( `transform ${ speed }ms ${ options.easing }` );\r\n      Move.translate( destination, true );\r\n      endCallback = done;\r\n    } else {\r\n      Move.jump( index );\r\n      done();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cancels the transition.\r\n   */\r\n  function cancel(): void {\r\n    apply( '' );\r\n  }\r\n\r\n  /**\r\n   * Returns the transition speed.\r\n   *\r\n   * @param index - A destination index.\r\n   */\r\n  function getSpeed( index: number ): number {\r\n    const { rewindSpeed } = options;\r\n\r\n    if ( Splide.is( SLIDE ) && rewindSpeed ) {\r\n      const prev = Controller.getIndex( true );\r\n      const end  = Controller.getEnd();\r\n\r\n      if ( ( prev === 0 && index >= end ) || ( prev >= end && index === 0 ) ) {\r\n        return rewindSpeed;\r\n      }\r\n    }\r\n\r\n    return options.speed;\r\n  }\r\n\r\n  /**\r\n   * Applies the transition CSS property to the list element.\r\n   *\r\n   * @param transition - A transition CSS value.\r\n   */\r\n  function apply( transition: string ): void {\r\n    style( list, 'transition', transition );\r\n  }\r\n\r\n  return {\r\n    mount,\r\n    start,\r\n    cancel,\r\n  };\r\n}\r\n", "import * as ComponentConstructors from '../../components';\nimport { <PERSON><PERSON><PERSON>atcher } from '../../components/Slides/Slides';\nimport { CLASS_INITIALIZED } from '../../constants/classes';\nimport { DEFAULTS } from '../../constants/defaults';\nimport { EVENT_DESTROY, EVENT_MOUNTED, EVENT_READY, EVENT_REFRESH, EVENT_UPDATED } from '../../constants/events';\nimport { DEFAULT_USER_EVENT_PRIORITY } from '../../constants/priority';\nimport { CREATED, DESTROYED, IDLE, STATES } from '../../constants/states';\nimport { FADE } from '../../constants/types';\nimport { EventBus, EventBusCallback, EventBusObject, State, StateObject } from '../../constructors';\nimport { Fade, Slide } from '../../transitions';\nimport { ComponentConstructor, Components, EventMap, Options } from '../../types';\nimport { addClass, assert, assign, empty, forOwn, isString, merge, query, slice } from '../../utils';\n\n\n/**\n * The frontend class for the Splide slider.\n *\n * @since 3.0.0\n */\nexport class Splide {\n  /**\n   * Changes the default options for all Splide instances.\n   */\n  static defaults: Options = {};\n\n  /**\n   * The collection of state numbers.\n   */\n  static readonly STATES = STATES;\n\n  /**\n   * The root element where the Splide is applied.\n   */\n  readonly root: HTMLElement;\n\n  /**\n   * The EventBusObject object.\n   */\n  readonly event: EventBusObject = EventBus();\n\n  /**\n   * The collection of all component objects.\n   */\n  readonly Components: Components = {} as Components;\n\n  /**\n   * The StateObject object.\n   */\n  readonly state: StateObject = State( CREATED );\n\n  /**\n   * Splide instances to sync with.\n   */\n  readonly splides: Splide[] = [];\n\n  /**\n   * The collection of options.\n   */\n  private readonly _options: Options = {};\n\n  /**\n   * The collection of all components.\n   */\n  private _Components: Components;\n\n  /**\n   * The collection of extensions.\n   */\n  private _Extensions: Record<string, ComponentConstructor> = {};\n\n  /**\n   * The Transition component.\n   */\n  private _Transition: ComponentConstructor;\n\n  /**\n   * The Splide constructor.\n   *\n   * @param target  - The selector for the target element, or the element itself.\n   * @param options - Optional. An object with options.\n   */\n  constructor( target: string | HTMLElement, options?: Options ) {\n    const root = isString( target ) ? query<HTMLElement>( document, target ) : target;\n    assert( root, `${ root } is invalid.` );\n\n    this.root = root;\n\n    merge( DEFAULTS, Splide.defaults );\n    merge( merge( this._options, DEFAULTS ), options || {} );\n  }\n\n  /**\n   * Initializes the instance.\n   *\n   * @param Extensions - Optional. An object with extensions.\n   * @param Transition - Optional. A Transition component.\n   *\n   * @return `this`\n   */\n  mount( Extensions?: Record<string, ComponentConstructor>, Transition?: ComponentConstructor ): this {\n    const { state, Components } = this;\n    assert( state.is( [ CREATED, DESTROYED ] ), 'Already mounted!' );\n\n    state.set( CREATED );\n\n    this._Components = Components;\n    this._Transition = Transition || this._Transition || ( this.is( FADE ) ? Fade : Slide );\n    this._Extensions = Extensions || this._Extensions;\n\n    const Constructors = assign( {}, ComponentConstructors, this._Extensions, { Transition: this._Transition } );\n\n    forOwn( Constructors, ( Component, key ) => {\n      const component = Component( this, Components, this._options );\n      Components[ key ] = component;\n      component.setup && component.setup();\n    } );\n\n    forOwn( Components, component => {\n      component.mount && component.mount();\n    } );\n\n    this.emit( EVENT_MOUNTED );\n\n    addClass( this.root, CLASS_INITIALIZED );\n\n    state.set( IDLE );\n    this.emit( EVENT_READY );\n\n    return this;\n  }\n\n  /**\n   * Syncs the slider with the provided one.\n   * This method must be called before the `mount()`.\n   *\n   * @example\n   * ```ts\n   * var primary   = new Splide();\n   * var secondary = new Splide();\n   *\n   * primary.sync( secondary );\n   * primary.mount();\n   * secondary.mount();\n   * ```\n   *\n   * @param splide - A Splide instance to sync with.\n   *\n   * @return `this`\n   */\n  sync( splide: Splide ): this {\n    this.splides.push( splide );\n    splide.splides.push( this );\n    return this;\n  }\n\n  /**\n   * Moves the slider with the following control pattern.\n   *\n   * | Pattern | Description |\n   * |---|---|\n   * | `i` | Goes to the slide `i` |\n   * | `'+${i}'` | Increments the slide index by `i` |\n   * | `'-${i}'` | Decrements the slide index by `i` |\n   * | `'>'` | Goes to the next page |\n   * | `'<'` | Goes to the previous page |\n   * | `>${i}` | Goes to the page `i` |\n   *\n   * In most cases, `'>'` and `'<'` notations are enough to control the slider\n   * because they respect `perPage` and `perMove` options.\n   *\n   * @example\n   * ```ts\n   * var splide = new Splide();\n   *\n   * // Goes to the slide 1:\n   * splide.go( 1 );\n   *\n   * // Increments the index:\n   * splide.go( '+2' );\n   *\n   * // Goes to the next page:\n   * splide.go( '>' );\n   *\n   * // Goes to the page 2:\n   * splide.go( '>2' );\n   * ```\n   *\n   * @param control - A control pattern.\n   *\n   * @return `this`\n   */\n  go( control: number | string ): this {\n    this._Components.Controller.go( control );\n    return this;\n  }\n\n  /**\n   * Registers an event handler.\n   *\n   * @example\n   * ```ts\n   * var splide = new Splide();\n   *\n   * // Listens to a single event:\n   * splide.on( 'move', function() {} );\n   *\n   * // Listens to multiple events:\n   * splide.on( 'move resize', function() {} );\n   *\n   * // Appends a namespace:\n   * splide.on( 'move.myNamespace resize.myNamespace', function() {} );\n   * ```\n   *\n   * @param events   - An event name or names separated by spaces. Use a dot(.) to append a namespace.\n   * @param callback - A callback function.\n   *\n   * @return `this`\n   */\n  on<K extends keyof EventMap>( events: K, callback: EventMap[ K ] ): this;\n  on( events: string | string[], callback: EventBusCallback ): this {\n    this.event.on( events, callback, null, DEFAULT_USER_EVENT_PRIORITY );\n    return this;\n  }\n\n  /**\n   * Removes the registered all handlers for the specified event or events.\n   * If you want to only remove a particular handler, use namespace to identify it.\n   *\n   * @example\n   * ```ts\n   * var splide = new Splide();\n   *\n   * // Removes all handlers assigned to \"move\":\n   * splide.off( 'move' );\n   *\n   * // Only removes handlers that belong to the specified namespace:\n   * splide.off( 'move.myNamespace' );\n   * ```\n   *\n   * @param events - An event name or names separated by spaces. Use a dot(.) to append a namespace.\n   *\n   * @return `this`\n   */\n  off<K extends keyof EventMap>( events: K | K[] | string | string[] ): this {\n    this.event.off( events );\n    return this;\n  }\n\n  /**\n   * Emits an event and triggers registered handlers.\n   *\n   * @param event - An event name to emit.\n   * @param args  - Optional. Any number of arguments to pass to handlers.\n   *\n   * @return `this`\n   */\n  emit<K extends keyof EventMap>( event: K, ...args: Parameters<EventMap[ K ]> ): this;\n  emit( event: string, ...args: any[] ): this;\n  emit( event: string ): this {\n    // eslint-disable-next-line prefer-rest-params, prefer-spread\n    this.event.emit( event, ...slice( arguments, 1 ) );\n    return this;\n  }\n\n  /**\n   * Inserts a slide at the specified position.\n   *\n   * @example\n   * ```ts\n   * var splide = new Splide();\n   * splide.mount();\n   *\n   * // Adds the slide by the HTML:\n   * splide.add( '<li></li> );\n   *\n   * // or adds the element:\n   * splide.add( document.createElement( 'li' ) );\n   * ```\n   *\n   * @param slides - A slide element, an HTML string that represents a slide, or an array with them.\n   * @param index  - Optional. An index to insert a slide at.\n   *\n   * @return `this`\n   */\n  add( slides: string | HTMLElement | Array<string | HTMLElement>, index?: number ): this {\n    this._Components.Slides.add( slides, index );\n    return this;\n  }\n\n  /**\n   * Removes slides that match the matcher\n   * that can be an index, an array with indices, a selector, or an iteratee function.\n   *\n   * @param matcher - An index, an array with indices, a selector string, or an iteratee function.\n   */\n  remove( matcher: SlideMatcher ): this {\n    this._Components.Slides.remove( matcher );\n    return this;\n  }\n\n  /**\n   * Checks the slider type.\n   *\n   * @param type - A type to test.\n   *\n   * @return `true` if the type matches the current one, or otherwise `false`.\n   */\n  is( type: string ): boolean {\n    return this._options.type === type;\n  }\n\n  /**\n   * Refreshes the slider.\n   *\n   * @return `this`\n   */\n  refresh(): this {\n    this.emit( EVENT_REFRESH );\n    return this;\n  }\n\n  /**\n   * Destroys the slider.\n   *\n   * @param completely - Optional. If `true`, Splide will not remount the slider by breakpoints.\n   *\n   * @return `this`\n   */\n  destroy( completely = true ): this {\n    const { event, state } = this;\n\n    if ( state.is( CREATED ) ) {\n      // Postpones destruction requested before the slider becomes ready.\n      event.on( EVENT_READY, this.destroy.bind( this, completely ), this );\n    } else {\n      forOwn( this._Components, component => {\n        component.destroy && component.destroy( completely );\n      }, true );\n\n      event.emit( EVENT_DESTROY );\n      event.destroy();\n      completely && empty( this.splides );\n      state.set( DESTROYED );\n    }\n\n    return this;\n  }\n\n  /**\n   * Returns options.\n   *\n   * @return An object with the latest options.\n   */\n  get options(): Options {\n    return this._options;\n  }\n\n  /**\n   * Merges options to the current options and emits `updated` event.\n   *\n   * @param options - An object with new options.\n   */\n  set options( options: Options ) {\n    const { _options } = this;\n    merge( _options, options );\n\n    if ( ! this.state.is( CREATED ) ) {\n      this.emit( EVENT_UPDATED, _options );\n    }\n  }\n\n  /**\n   * Returns the number of slides without clones.\n   *\n   * @return The number of slides.\n   */\n  get length(): number {\n    return this._Components.Slides.getLength( true );\n  }\n\n  /**\n   * Returns the active slide index.\n   *\n   * @return The active slide index.\n   */\n  get index(): number {\n    return this._Components.Controller.getIndex();\n  }\n}\n"], "names": ["PROJECT_CODE", "DATA_ATTRIBUTE", "CREATED", "MOUNTED", "IDLE", "MOVING", "DESTROYED", "STATES", "DEFAULT_EVENT_PRIORITY", "DEFAULT_USER_EVENT_PRIORITY", "array", "length", "subject", "isNull", "Array", "isArray", "HTMLElement", "value", "values", "iteratee", "for<PERSON>ach", "indexOf", "items", "push", "toArray", "arrayProto", "prototype", "arrayLike", "start", "end", "slice", "call", "predicate", "filter", "elm", "classes", "add", "name", "classList", "isString", "split", "parent", "children", "append<PERSON><PERSON><PERSON>", "bind", "nodes", "ref", "parentNode", "insertBefore", "node", "selector", "matches", "child", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "object", "right", "keys", "Object", "reverse", "i", "key", "arguments", "source", "isObject", "merge", "attrs", "removeAttribute", "attr", "value2", "setAttribute", "String", "tag", "document", "createElement", "addClass", "append", "prop", "isUndefined", "getComputedStyle", "style", "style2", "display2", "focus", "preventScroll", "getAttribute", "className", "contains", "target", "getBoundingClientRect", "<PERSON><PERSON><PERSON><PERSON>", "div", "create", "rect", "width", "html", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "body", "e", "stopPropagation", "preventDefault", "stopImmediatePropagation", "querySelector", "querySelectorAll", "condition", "message", "Error", "callback", "noop", "func", "requestAnimationFrame", "min", "Math", "max", "floor", "ceil", "abs", "x", "y", "epsilon", "number", "minOrMax", "maxOrMin", "exclusive", "minimum", "maximum", "string", "replacements", "replace", "replacement", "ids", "prefix", "pad", "handlers", "events", "priority", "event", "namespace", "_event", "_callback", "_namespace", "_priority", "_key", "sort", "handler1", "handler2", "eventHandlers", "handler", "apply", "join", "fragments", "eventNS", "on", "off", "offBy", "emit", "destroy", "EVENT_MOUNTED", "EVENT_READY", "EVENT_MOVE", "EVENT_MOVED", "EVENT_CLICK", "EVENT_ACTIVE", "EVENT_INACTIVE", "EVENT_VISIBLE", "EVENT_HIDDEN", "EVENT_SLIDE_KEYDOWN", "EVENT_REFRESH", "EVENT_UPDATED", "EVENT_RESIZE", "EVENT_RESIZED", "EVENT_REPOSITIONED", "EVENT_DRAG", "EVENT_DRAGGING", "EVENT_DRAGGED", "EVENT_SCROLL", "EVENT_SCROLLED", "EVENT_DESTROY", "EVENT_ARROWS_MOUNTED", "EVENT_ARROWS_UPDATED", "EVENT_PAGINATION_MOUNTED", "EVENT_PAGINATION_UPDATED", "EVENT_NAVIGATION_MOUNTED", "EVENT_AUTOPLAY_PLAY", "EVENT_AUTOPLAY_PLAYING", "EVENT_AUTOPLAY_PAUSE", "EVENT_LAZYLOAD_LOADED", "Splide2", "listeners", "targets", "options", "event2", "addEventListener", "listener", "removeEventListener", "unbind", "data", "interval", "onInterval", "onUpdate", "limit", "now", "Date", "startTime", "rate", "id", "paused", "count", "elapsed", "pause", "update", "resume", "cancel", "rewind", "isPaused", "initialState", "state", "states", "includes", "set", "is", "duration", "RequestInterval", "throttled", "Components2", "throttledObserve", "<PERSON>hrottle", "observe", "initialOptions", "points", "currPoint", "JSON", "parse", "root", "breakpoints", "isMin", "mediaQuery", "n", "m", "map", "point", "matchMedia", "completely", "item", "find", "item2", "newOptions", "mount", "setup", "RTL", "TTB", "ORIENTATION_MAP", "marginRight", "autoWidth", "fixedWidth", "paddingLeft", "paddingRight", "left", "X", "Y", "ArrowLeft", "ArrowRight", "axisOnly", "direction", "index", "resolve", "orient", "CLASS_ROOT", "CLASS_SLIDER", "CLASS_TRACK", "CLASS_LIST", "CLASS_SLIDE", "CLASS_CLONE", "CLASS_CONTAINER", "CLASS_ARROWS", "CLASS_ARROW", "CLASS_ARROW_PREV", "CLASS_ARROW_NEXT", "CLASS_PAGINATION", "CLASS_PAGINATION_PAGE", "CLASS_PROGRESS", "CLASS_PROGRESS_BAR", "CLASS_AUTOPLAY", "CLASS_PLAY", "CLASS_PAUSE", "CLASS_SPINNER", "CLASS_INITIALIZED", "CLASS_ACTIVE", "CLASS_PREV", "CLASS_NEXT", "CLASS_VISIBLE", "CLASS_LOADING", "STATUS_CLASSES", "CLASSES", "slide", "clone", "arrows", "arrow", "prev", "next", "pagination", "page", "spinner", "EventInterface", "elements", "slides", "slider", "track", "list", "getClasses", "refresh", "query", "autoplay", "bar", "play", "uniqueId", "type", "drag", "isNavigation", "assign", "ROLE", "ARIA_CONTROLS", "ARIA_CURRENT", "ARIA_LABEL", "ARIA_HIDDEN", "TAB_INDEX", "DISABLED", "ARIA_ORIENTATION", "ALL_ATTRIBUTES", "SLIDE", "LOOP", "FADE", "slideIndex", "destroyEvents", "Components", "updateOnMove", "Direction", "styles", "isClone", "container", "focusableNodes", "queryAll", "destroyed", "onMove", "idx", "label", "format", "i18n", "slideX", "controls", "splides", "splide", "dest", "currIndex", "isActive", "isVisible", "active", "hasClass", "visible", "ariaHidden", "slideFocus", "useContainer", "trackRect", "Elements", "slideRect", "from", "distance", "diff", "<PERSON><PERSON><PERSON><PERSON>", "Slides2", "Slide1", "Slide2", "Slide", "excludeClones", "Controller", "toIndex", "hasFocus", "perPage", "between", "parseHtml", "isHTMLElement", "before", "matcher", "isFunction", "images", "img", "register", "get", "getIn", "getAt", "remove", "<PERSON><PERSON><PERSON><PERSON>", "isEnough", "Slides", "vertical", "rootRect", "window", "init", "resize", "unit", "cssPadding", "newRect", "height", "cssTrackHeight", "gap", "cssSlideWidth", "cssSlideHeight", "padding", "cssHeight", "heightRatio", "cssSlideSize", "fixedHeight", "autoHeight", "withoutGap", "getGap", "totalSize", "parseFloat", "listSize", "slideSize", "sliderSize", "getPadding", "clones", "cloneCount", "computeCloneCount", "isHead", "cloneDeep", "cloneNode", "clones2", "fixedSize", "measure", "fixedCount", "baseCount", "flickMaxPages", "Layout", "waiting", "reposition", "isBusy", "<PERSON><PERSON>", "position", "getPosition", "looping", "waitForTransition", "Transition", "jump", "trimSpace", "go", "toPosition", "preventLoop", "transform", "loop", "exceededMin", "exceededLimit", "exceededMax", "shift", "backwards", "excess", "getLimit", "size", "sign", "minDistance", "Infinity", "trimming", "offset", "trim", "clamp", "getEnd", "move", "translate", "Move", "isLoop", "isSlide", "prevIndex", "slideCount", "perMove", "control", "allowSameIndex", "useScroll", "speed", "destination", "useIndex", "snap", "toDest", "scroll", "match", "indicator", "computeDestIndex", "getNext", "getPrev", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "approximatelyEqual", "incremental", "toPage", "closest", "setIndex", "getIndex", "XML_NAME_SPACE", "PATH", "SIZE", "wrapper", "created", "createArrow", "prev2", "arrow<PERSON>ath", "nextIndex", "prevLabel", "last", "next<PERSON><PERSON><PERSON>", "first", "disabled", "hovered", "focused", "for<PERSON><PERSON><PERSON>", "button", "pauseOnHover", "pauseOnFocus", "resetProgress", "manual", "cover", "src", "BOUNCE_DIFF_THRESHOLD", "BOUNCE_DURATION", "FRICTION_FACTOR", "BASE_VELOCITY", "MIN_DURATION", "scrollCallback", "clear", "suppressConstraint", "friction", "computeDuration", "onScrolled", "easing", "t", "easingFunc", "pow", "FRICTION", "LOG_INTERVAL", "POINTER_DOWN_EVENTS", "POINTER_MOVE_EVENTS", "POINTER_UP_EVENTS", "listenerOptions", "passive", "capture", "basePosition", "baseEvent", "prevBaseEvent", "lastEvent", "isFree", "dragging", "hasExceeded", "clickPrevented", "onPointerDown", "onClick", "prevent", "noDrag", "is<PERSON><PERSON>ch", "isTouchEvent", "isDraggable", "onPointerMove", "onPointerUp", "cancelable", "coordOf", "constrain", "expired", "timeOf", "exceeded", "thresholds", "dragMinThreshold", "mouse", "touch", "isSliderDirection", "velocity", "computeVelocity", "computeDestination", "diffX", "diffY", "base", "diffCoord", "diffTime", "isFlick", "flickPower", "orthogonal", "touches", "timeStamp", "TouchEvent", "disable", "isDragging", "IE_ARROW_KEYS", "onUpdated", "keyboard", "onKeydown", "normalizedKey", "SRC_DATA_ATTRIBUTE", "SRCSET_DATA_ATTRIBUTE", "IMAGE_SELECTOR", "isSequential", "lazyLoad", "_Slide", "_img", "srcset", "_spinner", "parentElement", "display", "preloadPages", "load", "error", "li", "class", "text", "pageX", "curr", "TRIGGER_KEYS", "processed", "concat", "instances", "index2", "instance", "wheel", "onWheel", "deltaY", "I18N", "DEFAULTS", "done", "endCallback", "getSpeed", "rewindSpeed", "transition", "EventBus", "State", "_Splide", "defaults", "_options", "Extensions", "_Components", "_Transition", "Fade", "_Extensions", "Constructors", "ComponentConstructors", "Component", "component", "sync", "empty", "Splide"], "mappings": ";;;;;;;;;;;;;;;MAKaA,eAAe;MAOfC,2BAA0BD;MCT1BE,UAAU;MAKVC,UAAU;MAKVC,OAAO;MAKPC,SAAS;MAKTC,YAAY;MAOZC,SAAS;AACpBL,IAAAA,SAAAA,OADoB;AAEpBC,IAAAA,SAAAA,OAFoB;AAGpBC,IAAAA,MAAAA,IAHoB;AAIpBC,IAAAA,QAAAA,MAJoB;AAKpBC,IAAAA,WAAAA;AALoB;MCzBTE,yBAAyB;MAOzBC,8BAA8B;;iBCPpBC,OAAqB;UACpCC,SAAS;;;oBCCSC,SAAsC;WACvD,CAAEC,OAAQD,QAAV,IAAuB,OAAOA,OAAP,KAAmB;;;mBAUvBA,SAAmC;WACtDE,MAAMC,OAAN,CAAeH,OAAf;;;sBAUmBA,SAAyD;WAC5E,OAAOA,OAAP,KAAmB;;;oBAUFA,SAAsC;WACvD,OAAOA,OAAP,KAAmB;;;uBAUCA,SAAyC;WAC7D,OAAOA,OAAP,KAAmB;;;kBAUJA,SAAoC;WACnDA,YAAY;;;yBAUUA,SAA2C;WACjEA,mBAAmBI;;;mBChEAC,OAAsB;WACzCF,QAASE,MAAT,GAAmBA,KAAnB,GAA2B,CAAEA,KAAF;;;mBCFRC,QAAiBC,UAAkE;YACpGD,QAASE,QAASD;;;oBCDAT,OAAYO,OAAoB;WACpDP,MAAMW,OAAN,CAAeJ,KAAf,IAAyB;;;gBCCTP,OAAYY,OAAsB;UACnDC,kBAASC,QAASF;WACjBZ;;;MCPIe,aAAaX,MAAMY;;iBCMNC,WAAyBC,OAAgBC,KAAoB;WAC9EJ,WAAWK,KAAX,CAAiBC,IAAjB,CAAuBJ,SAAvB,EAAkCC,KAAlC,EAAyCC,GAAzC;;;gBCAPF,WACAK,WACe;WACRF,MAAOH,UAAP,CAAmBM,MAAnB,CAA2BD,SAA3B,EAAwC,CAAxC;;;uBCNoBE,KAAcC,SAA4BC,KAAqB;QACrFF,KAAM;cACAC,SAAS,cAAA,EAAQ;YACnBE,MAAO;cACNC,UAAWF,MAAM,QAAQ,UAAYC;;;;;;oBCJvBH,KAAcC,SAAmC;gBAC5DD,KAAKK,SAAUJ,QAAV,GAAsBA,QAAQK,KAAR,CAAe,GAAf,CAAtB,GAA6CL,SAAS;;;kBCFlDM,QAAiBC,UAAgC;YAC9DA,UAAUD,OAAOE,WAAP,CAAmBC,IAAnB,CAAyBH,MAAzB;;;kBCDGI,OAAsBC,KAAkB;YACrDD,OAAO,cAAA,EAAQ;UAChBJ,SAASK,IAAIC;;UAEdN,QAAS;eACLO,aAAcC,MAAMH;;;;;mBCNRZ,KAAcgB,UAA4B;WACxD,KAAK,wBAAyBhB,IAAIiB,OAAlC,EAA4CpB,IAA5C,CAAkDG,GAAlD,EAAuDgB,QAAvD;;;oBCGsCT,QAAqBS,UAAwB;WACrFT,SAASX,MAAOW,OAAOC,SAAd,CAAyBT,MAAzB,CAAiC,eAAA;AAAA,aAASkB,QAASC,OAAOF,SAAzB;AAAA,KAAjC,IAAgF;;;iBCFpDT,QAAqBS,UAAmC;WAC7FA,WAAWR,SAAaD,QAAQS,SAArB,CAAiC,CAAjC,IAAuCT,OAAOY;;;kBCFhEC,QACAnC,UACAoC,OACG;QACED,QAAS;UACRE,OAAOC,OAAOD,IAAP,CAAaF,MAAb;aACJC,QAAQC,KAAKE,OAAL,KAAiBF;;eAEtBG,IAAI,GAAGA,IAAIH,KAAK7C,QAAQgD,KAAM;YAChCC,MAAMJ,KAAMG;;YAEbC,QAAQ,aAAc;cACpBzC,SAAUmC,OAAQM,MAAOA,IAAzB,KAAmC,OAAQ;;;;;;;WAO/CN;;;kBCMiCA,QAAiB;UAElDO,WAAW,GAAIzC,QAAS,gBAAA,EAAU;aAC/B0C,QAAQ,UAAE7C,KAAF,EAAS2C,GAAT,EAAkB;eACxBA,OAAQE,OAAQF;;;WAIrBN;;;iBCRkDA,QAAWQ,QAAyB;WACrFA,QAAQ,UAAE7C,KAAF,EAAS2C,GAAT,EAAkB;UAC3B7C,QAASE,QAAU;eACd2C,OAAQ3C,MAAMa,KAAN;iBACNiC,SAAU9C,QAAU;eACtB2C,OAAQI,MAAOD,SAAUT,OAAQM,KAAlB,GAA4BN,OAAQM,IAApC,GAA4C,IAAI3C;aAClE;eACG2C,OAAQ3C;;;WAIbqC;;;2BCrCwBpB,KAAc+B,OAAiC;QACzE/B,KAAM;cACA+B,OAAO,cAAA,EAAQ;YAClBC,gBAAiBC;;;;;wBCHzBjC,KACA+B,OACAhD,OACM;QACD8C,SAAUE,QAAU;aACfA,OAAO,UAAEG,MAAF,EAAS/B,IAAT,EAAmB;qBAClBH,KAAKG,MAAM+B;;WAEtB;aACGnD,SAAUiD,gBAAiBhC,KAAK+B,SAAU/B,IAAImC,YAAJ,CAAkBJ,KAAlB,EAAyBK,OAAQrD,MAAjC;;;;kBCQpDsD,KACAN,OACAxB,QAC4B;QACtBP,MAAMsC,SAASC,aAAT,CAAwBF,GAAxB;;QAEPN,OAAQ;eACDA,SAAUS,SAAUxC,KAAK+B,SAAUI,aAAcnC,KAAK+B;;;cAGxDU,OAAQlC,QAAQP;WAEnBA;;;iBCVPA,KACA0C,MACA3D,OACe;QACV4D,YAAa5D,QAAU;aACnB6D,iBAAkB5C,IAAlB,CAAyB0C,IAAzB;;;QAGJ,CAAE/D,OAAQI,QAAU;mBACLiB,IAAV6C;mBACI9D;;UAEP+D,OAAOJ,KAAP,KAAkB3D,OAAQ;eACtB2D,QAAS3D;;;;;mBChCGiB,KAAkB+C,UAAwB;UAC1D/C,KAAK,WAAW+C;;;iBCLF/C,KAAyB;QACzC,gBAAiBA,IAAK,YAAL,MAAwBA,IAAIgD,KAAJ,CAAW;AAAEC,MAAAA,eAAe;AAAjB,KAAX;;;wBCAlBjD,KAAciC,MAAuB;WAC1DjC,IAAIkD,YAAJ,CAAkBjB,IAAlB;;;oBCCiBjC,KAAcmD,WAA6B;WAC5DnD,OAAOA,IAAII,SAAJ,CAAcgD,QAAd,CAAwBD,SAAxB;;;gBCJME,QAA2B;WACxCA,OAAOC,qBAAP;;;kBCEe3C,OAA6B;YAC1CA,OAAO,cAAA,EAAQ;UACjBI,QAAQA,KAAKF,YAAa;aACxBA,WAAW0C,YAAaxC;;;;;mBCGVR,QAAqBxB,OAAiC;QACxEsB,SAAUtB,QAAU;UACjByE,MAAMC,OAAQ,OAAO;AAAEZ,QAAAA,mBAAkB9D;AAApB,SAAqDwB;cACxEmD,KAAMF,IAAN,CAAYG;aACZH;;;WAGHzE;;;qBCXyC6E,MAA8B;WACvE1C,MAAU,IAAI2C,SAAJ,GAAgBC,eAAhB,CAAiCF,IAAjC,EAAuC,WAAvC,EAAqDG;;;mBCL/CC,GAAUC,iBAAkC;MACjEC;;QAEGD,iBAAkB;QACnBA;QACAE;;;;iBCH8C5D,QAA4BS,UAA6B;WACpGT,UAAUA,OAAO6D,aAAP,CAAsBpD,QAAtB;;;oBCEoCT,QAA4BS,UAAwB;WAClGpB,MAAUW,OAAO8D,gBAAP,CAAyBrD,QAAzB;;;uBCHUhB,KAAcC,SAAmC;gBAC/DD,KAAKC,SAAS;;;gBCCPlB,OAAiC;WAC9CsB,SAAUtB,MAAV,GAAoBA,KAApB,GAA4BA,QAAYA,eAAa;;;kBCHtCuF,WAAgBC,SAAqB;AAAA,QAArBA,OAAqB;AAArBA,MAAAA,OAAqB,GAAX,EAAW;AAAA;;QACtD,CAAED,WAAY;YACX,IAAIE,KAAJ,OAAgB1G,YAAhB,UAAmCyG,OAAnC;;;;oBCHgBE,UAA8B;eAC1CA;;;MCNDC,OAAO,SAAPA,IAAO,GAAY;;eCAXC,MAAqC;WACjDC,sBAAuBD;;;MCJjBE,MAA+BC,KAA/BD;MAAKE,MAA0BD,KAA1BC;MAAKC,QAAqBF,KAArBE;MAAOC,OAAcH,KAAdG;MAAMC,MAAQJ,KAARI;;8BCYFC,GAAWC,GAAWC,SAA2B;WAC5EH,IAAKC,IAAIC,EAAT,GAAeC;;;mBCFCC,QAAgBC,UAAkBC,UAAkBC,WAA+B;QACpGC,UAAUb,IAAKU,UAAUC;QACzBG,UAAUZ,IAAKQ,UAAUC;WACxBC,YAAYC,UAAUJ,MAAV,IAAoBA,SAASK,UAAUD,WAAWJ,MAAX,IAAqBA,UAAUK;;;iBCJpEL,QAAgBH,GAAWC,GAAoB;QAC9DM,UAAUb,IAAKM,GAAGC;QAClBO,UAAUZ,IAAKI,GAAGC;WACjBP,IAAKE,IAAKW,SAASJ,SAAUK;;;gBCNhBR,GAAoB;WACjC,MAAO,CAAP,IAAa,MAAO,CAAP;;;kBCGES,QAAgBC,cAAiE;YAC9FA,cAAc,qBAAA,EAAe;eAC3BD,OAAOE,OAAP,CAAgB,IAAhB,OAA0BC,WAA1B;;WAGJH;;;eCTYN,QAAyB;WACrCA,SAAS,EAAT,SAAmBA,MAAnB,QAAmCA;;;ACA5C,MAAMU,MAA8B,EAApC;;oBAO0BC,QAAyB;gBACtCA,SAAWC,IAAOF,IAAKC,OAAL,GAAkB,KAAKA,WAAY,CAAjB,IAAuB;;;sBC6B7B;QAIrCE,WAA2C;;gBAY7CC,QACA3B,UACA/C,KACA2E,UACM;AAAA,UADNA,QACM;AADNA,QAAAA,QACM,GADK/H,sBACL;AAAA;;mBACQ8H,QAAQ,UAAEE,KAAF,EAASC,SAAT,EAAwB;iBAClCD,SAAUH,SAAUG,MAAV,IAAqB;aAEnCH,SAAUG,QAAS;AACvBE,UAAAA,QAAYF,KADW;AAEvBG,UAAAA,WAAYhC,QAFW;AAGvBiC,UAAAA,YAAYH,SAHW;AAIvBI,UAAAA,WAAYN,QAJW;AAKvBO,UAAAA,MAAYlF;AALW,WAMrBmF,KAAM,UAAEC,QAAF,EAAYC,QAAZ;AAAA,iBAA0BD,SAASH,SAAT,GAAqBI,SAASJ,SAAxD;AAAA;;;;iBAYAP,QAA2B1E,KAAqB;mBAC9C0E,QAAQ,UAAEE,KAAF,EAASC,SAAT,EAAwB;YACtCS,gBAAgBb,SAAUG;iBAEtBA,SAAUU,iBAAiBA,cAAcjH,MAAd,CAAsB,iBAAA,EAAW;iBAC7DkH,QAAQL,IAAR,GAAeK,QAAQL,IAAR,KAAiBlF,GAAhC,GAAsCA,OAAOuF,QAAQP,UAAR,KAAuBH;SADxC;;;;mBAWzB7E,KAAoB;aAC1ByE,UAAU,UAAEa,aAAF,EAAiBV,KAAjB,EAA4B;YACvCA,OAAO5E;;;;kBAUD4E,OAAsB;AAAA;AACjC,gBAAUA,UAAW,EAArB,EAA0BpH,OAA1B,CAAmC,iBAAA,EAAW;gBAEtCuH,UAAUS,MAAOD,SAASrH,MAAO+B,YAAW;OAFpD;;;uBASqB;iBACZ;;;0BASUyE,QAA2BnH,UAA+D;cACtGmH,QAASe,KAAM,KAAM7G,MAAO,KAAMpB,QAAS,iBAAA,EAAW;YACvDkI,YAAYC,QAAQ/G,KAAR,CAAe,GAAf;iBACR8G,UAAW,IAAKA,UAAW;;;;WAIlC;AACLE,MAAAA,IAAAA,EADK;AAELC,MAAAA,KAAAA,GAFK;AAGLC,MAAAA,OAAAA,KAHK;AAILC,MAAAA,MAAAA,IAJK;AAKLC,MAAAA,SAAAA;AALK;;;MC7IIC,gBAA2B;MAC3BC,cAA2B;MAC3BC,aAA2B;MAC3BC,cAA2B;MAC3BC,cAA2B;MAC3BC,eAA2B;MAC3BC,iBAA2B;MAC3BC,gBAA2B;MAC3BC,eAA2B;MAC3BC,sBAA2B;MAC3BC,gBAA2B;MAC3BC,gBAA2B;MAC3BC,eAA2B;MAC3BC,gBAA2B;MAC3BC,qBAA2B;MAC3BC,aAA2B;MAC3BC,iBAA2B;MAC3BC,gBAA2B;MAC3BC,eAA2B;MAC3BC,iBAA2B;MAC3BC,gBAA2B;MAC3BC,uBAA2B;MAC3BC,uBAA2B;MAC3BC,2BAA2B;MAC3BC,2BAA2B;MAC3BC,2BAA2B;MAC3BC,sBAA2B;MAC3BC,yBAA2B;MAC3BC,uBAA2B;MAC3BC,wBAA2B;;0BCmBRC,SAAuC;QAI7DnD,QAAUmD,QAAVnD;QAKF5E,MAAM;QAKRgI,YAA8E;;gBAWrEtD,QAA2B3B,UAA4B4B,UAA0B;YACtFiB,GAAIlB,QAAQ3B,UAAU/C,KAAK2E;;;iBAQrBD,QAAkC;YACxCmB,IAAKnB,QAAQ1E;;;kBAanBiI,SACAvD,QACA3B,UACAmF,SACM;mBACQD,SAASvD,QAAQ,UAAE/C,MAAF,EAAUwG,MAAV,EAAqB;kBACxCxK,KAAM,CAAEgE,MAAF,EAAUwG,MAAV,EAAiBpF,QAAjB,EAA2BmF,OAA3B;eACTE,iBAAkBD,QAAOpF,UAAUmF;;;;oBAW7BD,SAAsCvD,QAAgB3B,UAA+B;mBACtFkF,SAASvD,QAAQ,UAAE/C,MAAF,EAAUwG,MAAV,EAAqB;oBACtCH,UAAU3J,MAAV,CAAkB,kBAAA,EAAY;cACnCgK,SAAU,EAAV,KAAkB1G,MAAlB,IAA4B0G,SAAU,EAAV,KAAkBF,MAA9C,MAA2DpF,YAAYsF,SAAU,EAAV,KAAkBtF,QAAzF,GAAsG;mBAClGuF,oBAAqBH,QAAOE,SAAU,IAAKA,SAAU;mBACrD;;;iBAGF;SANG;;;;0BAmBdJ,SACAvD,QACAnH,UACM;cACG0K,SAAS,gBAAA,EAAU;YACrBtG,QAAS;iBACL/C,MAAO,KAAMpB,QAASD,SAASyB,IAAT,CAAe,IAAf,EAAqB2C,MAArB;;;;;uBAQV;kBACXqG,UAAU3J,MAAV,CAAkB,cAAA;AAAA,eAAQkK,OAAQC,KAAM,IAAKA,KAAM,GAAjC;AAAA,OAAlB;YACN1C,MAAO9F;;;UAMT4F,GAAIyB,eAAerB,SAAShG;WAE3B;AACL4F,MAAAA,IAAAA,EADK;AAELC,MAAAA,KAAAA,GAFK;AAGLE,MAAAA,MAAMnB,MAAMmB,IAHP;AAIL/G,MAAAA,MAAAA,IAJK;AAKLuJ,MAAAA,QAAAA,MALK;AAMLvC,MAAAA,SAAAA;AANK;;;2BCpIPyC,UACAC,YACAC,UACAC,OAC0B;QAClBC,MAAQC,KAARD;QAKJE;QAKAC,OAAO;QAKPC;QAKAC,SAAS;QAKTC,QAAQ;;sBAKY;UACjB,CAAED,QAAS;YACRE,UAAUP,QAAQE;;YAEnBK,WAAWX,UAAW;iBACb;sBACAI;eACP;iBACEO,UAAUX;;;YAGdE,UAAW;mBACJK;;;YAGPA,SAAS,GAAI;;;cAGXJ,SAAS,EAAEO,KAAF,IAAWP,OAAQ;mBACxBS;;;;YAINC;;;;mBASOC,QAAyB;OACrCA,UAAUC;kBACAX,kBAAmBG,OAAOP,WAAW;eACrC;UACPa;;;qBAMgB;eACZ;;;sBAMa;kBACVT;aACA;;UAEPF,UAAW;iBACJK;;;;sBAOI;2BACMC;aACb;WACA;eACA;;;wBAQkB;aACpBC;;;WAGF;AACLlL,MAAAA,OAAAA,KADK;AAELyL,MAAAA,QAAAA,MAFK;AAGLJ,MAAAA,OAAAA,KAHK;AAILG,MAAAA,QAAAA,MAJK;AAKLE,MAAAA,UAAAA;AALK;;;iBCzHcC,cAAoC;QAIrDC,QAAQD;;iBAOEtM,OAAsB;cAC1BA;;;gBAUGwM,QAAqC;aACzCC,SAAUlM,QAASiM,SAAUD;;;WAG/B;AAAEG,MAAAA,KAAAA,GAAF;AAAOC,MAAAA,IAAAA;AAAP;;;oBCtBP/G,MACAgH,UACqB;QACjBxB;;yBAEmD;AAAA;AAAA;;UAChD,CAAEA,UAAW;mBACLyB,gBAAiBD,YAAY,GAAG,YAAM;eAE1CzE,MAAO,OAAMvF;qBACP;WACV,MAAM;iBAEAjC;;;;WAINmM;;;mBCZgBpC,SAAgBqC,aAAwBlC,SAAqC;QAI9FmC,mBAAmBC,SAAUC;QAK/BC;QAKAC;QAKAC;;qBAKmB;UACjB;cACKxC,SAASyC,KAAKC,KAAL,CAAYpJ,aAAcuG,QAAO8C,MAAMxO,eAAvC;eACRiG,GAAR;eACQ,OAAOA,EAAEO;;;uBAGFzC,MAAO,IAAI8H;UAEpB4C,cAAgB5C,QAAhB4C;;UAEHA,aAAc;YACXC,QAAQ7C,QAAQ8C,UAAR,KAAuB;iBAE5BnL,OAAOD,IAAP,CAAakL,WAAb,EACN3F,IADM,CACA,UAAE8F,CAAF,EAAKC,CAAL;AAAA,iBAAYH,QAAQ,CAACG,CAAD,GAAK,CAACD,IAAI,CAACA,CAAD,GAAK,CAACC,CAApC;AAAA,SADA,EAENC,GAFM,CAED,eAAA;AAAA,iBAAS,CACbC,KADa,EAEbC,kBAAiBN,QAAQ,QAAQ,qBAAiBK,cAFrC,CAAT;AAAA,SAFC;;;;;qBAcU;UAChBX,QAAS;yBACM,UAAUJ;;;;qBASdiB,YAA4B;UACvCA,YAAa;4BACK,UAAUjB;;;;uBAQV;UACjBkB,OAAOC,KAAMf,QAAQ,eAAA;AAAA,eAAQgB,MAAM,EAAN,CAAUlM,OAAlB;AAAA,QAAd,IAA6C;;UAErDgM,KAAM,EAAN,KAAcb,WAAY;gBAClBA,YAAYa,KAAM;;;;qBASfH,OAAkC;UAC5CM,aAAaxD,QAAQ4C,WAAR,CAAqBM,KAArB,KAAgCZ;;UAE9CkB,WAAW1F,SAAU;gBACjBkC,UAAUsC;gBACVxE,QAAS0F,WAAW1F,OAAX,KAAuB;aAClC;YACA+B,QAAO6B,KAAP,CAAaI,EAAb,CAAiBtN,SAAjB,GAA+B;kBACzB;kBACFiP;;;gBAGFzD,UAAUwD;;;;WAId;AACLE,MAAAA,OAAAA,KADK;AAELD,MAAAA,OAAAA,KAFK;AAGL3F,MAAAA,SAAAA;AAHK;;;MCzHI6F,MAAM;MAKNC,MAAM;MCONC,kBAAkB;AAC7BC,IAAAA,aAAc,CAAE,cAAF,EAAkB,YAAlB,CADe;AAE7BC,IAAAA,WAAc,CAAE,YAAF,CAFe;AAG7BC,IAAAA,YAAc,CAAE,aAAF,CAHe;AAI7BC,IAAAA,aAAc,CAAE,YAAF,EAAgB,cAAhB,CAJe;AAK7BC,IAAAA,cAAc,CAAE,eAAF,EAAmB,aAAnB,CALe;AAM7BnK,IAAAA,OAAc,CAAE,QAAF,CANe;AAO7BoK,IAAAA,MAAc,CAAE,KAAF,EAAS,OAAT,CAPe;AAQ7B1M,IAAAA,OAAc,CAAE,QAAF,EAAY,MAAZ,CARe;AAS7B8D,IAAAA,GAAc,CAAE,GAAF,CATe;AAU7B6I,IAAAA,GAAc,CAAE,GAAF,CAVe;AAW7BC,IAAAA,GAAc,CAAE,GAAF,CAXe;AAY7BC,IAAAA,WAAc,CAAE,SAAF,EAAa,YAAb,CAZe;AAa7BC,IAAAA,YAAc,CAAE,WAAF,EAAe,WAAf;AAbe;;qBA2BJ1E,SAAgBqC,aAAwBlC,SAAuC;qBAOtFlH,MAAc0L,UAA6B;UACnDC,YAAczE,QAAdyE;UACFC,QAAQD,cAAcd,GAAd,IAAqB,CAAEa,QAAvB,GAAkC,CAAlC,GAAsCC,cAAcb,GAAd,GAAoB,CAApB,GAAwB;aACrEC,gBAAiB/K,KAAjB,CAAyB4L,KAAzB,KAAoC5L;;;oBAU5B3D,OAAwB;aAChCA,iBAAkBsP,cAAcd,MAAM,IAAI;;;WAG5C;AACLgB,MAAAA,SAAAA,OADK;AAELC,MAAAA,QAAAA;AAFK;;;MCpEIC,aAAwB3Q;MACxB4Q,eAA4B5Q;MAC5B6Q,cAA4B7Q;MAC5B8Q,aAA4B9Q;MAC5B+Q,cAA4B/Q;MAC5BgR,cAA4BD;MAC5BE,kBAA4BF;MAC5BG,eAA4BlR;MAC5BmR,cAA4BnR;MAC5BoR,mBAA4BD;MAC5BE,mBAA4BF;MAC5BG,mBAA4BtR;MAC5BuR,wBAA4BD;MAC5BE,iBAA4BxR;MAC5ByR,qBAA4BD;MAC5BE,iBAA4B1R;MAC5B2R,aAA4B3R;MAC5B4R,cAA4B5R;MAC5B6R,gBAA4B7R;MAC5B8R,oBAAwB;MACxBC,eAAwB;MACxBC,aAAwB;MACxBC,aAAwB;MACxBC,gBAAwB;MACxBC,gBAAwB;MAOxBC,iBAAiB,CAAEL,YAAF,EAAgBG,aAAhB,EAA+BF,UAA/B,EAA2CC,UAA3C,EAAuDE,aAAvD;MAOjBE,UAAU;AACrBC,IAAAA,OAAYvB,WADS;AAErBwB,IAAAA,OAAYvB,WAFS;AAGrBwB,IAAAA,QAAYtB,YAHS;AAIrBuB,IAAAA,OAAYtB,WAJS;AAKrBuB,IAAAA,MAAYtB,gBALS;AAMrBuB,IAAAA,MAAYtB,gBANS;AAOrBuB,IAAAA,YAAYtB,gBAPS;AAQrBuB,IAAAA,MAAYtB,qBARS;AASrBuB,IAAAA,SAAYjB;AATS;;oBCoCGlG,SAAgBqC,aAAwBlC,SAAsC;0BACvFiH,eAAgBpH;QAAvBnC,qBAAAA;;QACAiF,OAAS9C,QAAT8C;QACFuE,WAA8B;QAK9BC,SAAwB;QAK1B9Q;QAKA+Q;QAKAC;QAKAC;;qBAKmB;;;eAGX3E,MAAQtM,UAAUkR;;;qBAMP;SACjB9I,eAAe+I,SAAS9S,yBAAyB;SACjDgK,eAAe0C;;;uBAMI;OACrBuB,MAAM0E,OAAOC,MAAOhS,QAAS,aAAA,EAAO;wBACnBc,KAAK;;YAGjB+Q;kBACMxE,MAAMtM;;;uBAMI;;;;;sBAQD;kBACTsM,MAAMtM;eACTsM,MAAQtM,UAAUkR;;;uBAML;eACdjQ,MAAOqL,YAAWmC;cAClB2C,MAAO9E,YAAWoC;aAClBzN,MAAO+P,aAAYrC;aAEpBqC,SAASC,MAAM;WAEjBH,QAAQvQ,SAAU0Q,YAAWrC,yBAAsBC;UAEnDwC,WAAWpE,WAAWsC;UACtBc,SAAWpD,WAAW8B;aAEpB8B,UAAU;AAChBvE,QAAAA,MAAAA,IADgB;AAEhByE,QAAAA,QAAAA,MAFgB;AAGhBC,QAAAA,OAAAA,KAHgB;AAIhBC,QAAAA,MAAAA,IAJgB;AAKhBH,QAAAA,QAAAA,MALgB;AAMhBT,QAAAA,QAAAA,MANgB;AAOhBgB,QAAAA,UAAAA,QAPgB;AAQhBd,QAAAA,MAAOa,MAAOf,cAAapB,iBARX;AAShBuB,QAAAA,MAAOY,MAAOf,cAAanB,iBATX;AAUhBoC,QAAAA,KAAOF,MAAOnE,WAAWoC,uBAA0BC,mBAVnC;AAWhBiC,QAAAA,MAAOH,MAAOC,gBAAe7B,WAXb;AAYhB1E,QAAAA,OAAOsG,MAAOC,gBAAe5B;AAZb;;;wBAmBM;UAClB/E,KAAK4B,KAAK5B,EAAL,IAAW8G,SAAU3T;WAC3B6M,KAAMA;YACLA,KAAKsG,MAAMtG,EAAN,IAAgBA,EAAhB;WACNA,KAAMuG,KAAKvG,EAAL,IAAeA,EAAf;;;kBAQE3J,UAAgC;aACtCE,MAAOqL,MAAMvL,SAAb,IAA2BE,MAAO8P,QAAQhQ;;;0BAQnB;aACvB,CACDyN,UADC,UACgB7E,QAAQ8H,IADxB,EAEDjD,UAFC,UAEgB7E,QAAQyE,SAFxB,EAGLzE,QAAQ+H,IAAR,IAAoBlD,UAApB,gBAHK,EAIL7E,QAAQgI,YAAR,IAA4BnD,UAA5B,UAJK,EAKLoB,YALK;;;WASFgC,OAAQf,UAAU;AACvBxD,MAAAA,OAAAA,KADuB;AAEvBD,MAAAA,OAAAA,KAFuB;AAGvB3F,MAAAA,SAAAA;AAHuB;;;MCzNdoK,OAAmB;MACnBC,gBAAmB;MACnBC,eAAmB;MACnBC,aAAmB;MACnBC,cAAmB;MACnBC,YAAmB;MACnBC,WAAmB;MACnBC,mBAAmB;MAOnBC,iBAAiB,CAC5BR,IAD4B,EAE5BC,aAF4B,EAG5BC,YAH4B,EAI5BC,UAJ4B,EAK5BC,WAL4B,EAM5BG,gBAN4B,EAO5BF,SAP4B,EAQ5BC,QAR4B;MCTjBG,QAAQ;MAORC,OAAO;MAOPC,OAAO;;mBC8DGhJ,SAAgB6E,OAAeoE,YAAoBtC,OAAqC;2BAC1DS,eAAgBpH;QAA3DnC,sBAAAA;QAAIG,wBAAAA;QAAM/G,wBAAAA;QAAeiS,iCAATjL;;QAChBkL,aAA8BnJ,QAA9BmJ;QAAYrG,OAAkB9C,QAAlB8C;QAAM3C,UAAYH,QAAZG;QAClBgI,eAA+BhI,QAA/BgI;QAAciB,eAAiBjJ,QAAjBiJ;QACdtE,UAAYqE,WAAWE,UAAvBvE;QACFwE,SAAiB7P,aAAckN,OAAO;QACtC4C,UAAiBN,aAAa;QAC9BO,YAAiB/R,MAAOkP,aAAYrB;QACpCmE,iBAAiBtJ,QAAQsJ,cAAR,IAA0BC,SAAU/C,OAAOxG,QAAQsJ;QAKtEE;;qBAKyC;AAAA;;;WAGrChD,OAAO,iBAAiB,WAAA,EAAK;aAC3BpM,EAAE0N,IAAF,KAAW,OAAX,GAAqB3J,WAArB,GAAmCK,qBAAqB,QAAMpE;;SAGlE,CAAEqE,aAAF,EAAiBI,kBAAjB,EAAqCX,WAArC,EAAkDgB,cAAlD,GAAoEkC,OAAOtK,IAAP,CAAa,IAAb;;UAEnEmS,cAAe;WACdhL,YAAYwL,OAAO3S,IAAP,CAAa,IAAb;;;;oBAOE;UACf,CAAEsS,SAAU;cACTrI,KAAS4B,KAAK5B,gBAAazE,IAAKoI,QAAQ;;;UAG3CsD,cAAe;YACZ0B,MAAWN,UAAUN,aAAapE;YAClCiF,QAAWC,OAAQ5J,QAAQ6J,IAAR,CAAaC,QAAQJ,MAAM;YAC9CK,WAAWlK,QAAOmK,OAAP,CAAe/G,GAAf,CAAoB,gBAAA;AAAA,iBAAUgH,OAAOtH,IAAP,CAAY5B,EAAtB;AAAA,SAApB,EAA+CxD,IAA/C,CAAqD,GAArD;qBAEHiJ,OAAO6B,YAAYsB;qBACnBnD,OAAO2B,eAAe4B;qBACtBvD,OAAO0B,MAAM;;;;uBAON;kBACX;;kBAEC1B,OAAOF;sBACHE,OAAOkC;mBACVlC,OAAO,SAAS2C;;;oBAUOtC,MAAcD,MAAcsD,MAAqB;UACjF,CAAEV,WAAY;eACVvT,KAAM;;YAERiU,SAASxF,OAAQ;yBACLzO,KAAM,MAAM;;;;;sBAQa;UACvC,CAAEuT,WAAY;YACFW,YAActK,QAArB6E;uBAEOzO,KAAM,MAAMmU;yBACVnU,KAAM,MAAMoU;oBAEhB7D,OAAON,YAAYxB,UAAUyF,YAAY;oBACzC3D,OAAOL,YAAYzB,UAAUyF,YAAY;;;;4BASXG,QAAwB;UAChEA,WAAWC,SAAU/D,OAAOP,eAAiB;oBACnCO,OAAOP,cAAcqE;;YAE7BtC,cAAe;uBACJxB,OAAO4B,cAAckC,UAAU;;;aAGzCA,SAASlM,eAAeC,gBAAgB;;;;8BASDmM,SAAyB;UAClEC,aAAa,CAAED,OAAF,IAAa,CAAEJ;mBAEpB5D,OAAO8B,aAAamC,cAAc;mBAClCjE,OAAO+B,WAAW,CAAEkC,UAAF,IAAgBzK,QAAQ0K,UAAxB,GAAqC,CAArC,GAAyC;;UAEpEpB,gBAAiB;uBACLhU,QAAS,cAAA,EAAQ;uBAChB6B,MAAMoR,WAAWkC,aAAa,KAAK;;;;UAIhDD,YAAYD,SAAU/D,OAAOJ,gBAAkB;oBACrCI,OAAOJ,eAAeoE;aAC7BA,UAAUlM,gBAAgBC,cAAc;;;;qBAWlCzF,MAAc3D,OAAwBwV,cAA+B;YACzEA,gBAAgBtB,SAAhB,IAA+B7C,OAAO1N,MAAM3D;;;wBAQ3B;aACpB0K,QAAO6E,KAAP,KAAiBA;;;yBAMI;UACvB7E,QAAOiC,EAAP,CAAW+G,IAAX,GAAoB;eAChBuB;;;UAGHQ,YAAY9Q,KAAMkP,WAAW6B,QAAX,CAAoBxD;UACtCyD,YAAYhR,KAAM0M;UAClBrC,OAAYQ,QAAS;UACrBlN,QAAYkN,QAAS;aAEpBvJ,MAAOwP,UAAWzG,MAAlB,IAA8B9I,KAAMyP,UAAW3G,MAA/C,IACF/I,MAAO0P,UAAWrT,OAAlB,IAA+B4D,KAAMuP,UAAWnT;;;sBAYpCsT,MAAcC,UAA4B;UACvDC,OAAO3P,IAAKyP,OAAOrG;;UAElB,CAAE0E,OAAF,aAAuB7H,UAAU1B,QAAOiC,EAAP,CAAW8G,IAAX,CAAjC,GAAuD;eACnD3N,IAAKgQ,MAAMpL,QAAOhL,MAAP,GAAgBoW;;;aAG7BA,QAAQD;;;WAGV;AACLtG,MAAAA,OAAAA,KADK;AAELoE,MAAAA,YAAAA,UAFK;AAGLtC,MAAAA,OAAAA,KAHK;AAIL6C,MAAAA,WAAAA,SAJK;AAKLD,MAAAA,SAAAA,OALK;AAML3F,MAAAA,OAAAA,KANK;AAOL3F,MAAAA,SAAAA,OAPK;aAQL7E,OARK;AASLiS,MAAAA,UAAAA;AATK;;;kBCpMerL,SAAgBqC,aAAwBlC,SAAoC;2BACvEiH,eAAgBpH;QAAnCnC,sBAAAA;QAAIG,wBAAAA;QAAM/G,wBAAAA;;gCACOoL,YAAW2I;QAA5B1D,+BAAAA;QAAQG,6BAAAA;QAKV6D,UAA2B;;qBAKV;;SAEjB1M,eAAe+I;SACf,CAAEzJ,aAAF,EAAiBU,aAAjB,GAAkC,YAAM;gBACnCxB,KAAM,UAAEmO,MAAF,EAAUC,MAAV;AAAA,iBAAsBD,OAAO1G,KAAP,GAAe2G,OAAO3G,KAA5C;AAAA;;;;oBAOK;aACbpP,QAAS,UAAEkR,KAAF,EAAS9B,KAAT,EAAoB;iBAAY8B,OAAO9B,OAAO;;;;uBAMvC;gBACd,gBAAA,EAAS;eAAQ5G;;YACnBqN;;;uBAMgB;;;;;sBAYN3E,OAAoB9B,OAAeoE,YAA2B;UACzEtR,SAAS8T,QAAOzL,SAAQ6E,OAAOoE,YAAYtC;aAC1C/C;cACAhO,KAAM+B;;;iBAUD+T,eAA4C;aACjDA,gBAAgBpV,OAAQ,gBAAA;AAAA,eAAS,CAAEkV,OAAMjC,OAAjB;AAAA,WAA6B+B;;;mBAU9CpE,MAAiC;UACvCyE,aAAetJ,YAAfsJ;UACF9G,QAAQ8G,WAAWC,OAAX,CAAoB1E,IAApB;UACR5L,MAAQqQ,WAAWE,QAAX,KAAwB,CAAxB,GAA4B1L,QAAQ2L;aAC3CxV,OAAQ,gBAAA;AAAA,eAASyV,QAASP,OAAM3G,OAAOA,OAAOA,QAAQvJ,GAAR,GAAc,EAApD;AAAA;;;mBAUDuJ,OAA4C;aACnDvO,OAAQuO,MAAR,CAAiB,CAAjB;;;iBASKlP,OAAmDkP,OAAuB;cACzElP,OAAO,eAAA,EAAS;YACtBiB,SAAU+P,QAAU;kBACfqF,UAAWrF;;;YAGhBsF,cAAetF,QAAU;cACtBxP,MAAMmQ,OAAQzC;gBACdqH,OAAQvF,OAAOxP,OAAQ6B,OAAQyO,MAAMd;mBACjCA,OAAOxG,QAAQ3J,OAAR,CAAgBmQ;wBAClBA,OAAO3I,KAAK/G,IAAL,CAAW,IAAX,EAAiB6H,YAAjB;;;WAIpBF;;;sBASSuN,SAA8B;aACjC7V,OAAQ6V,QAAR,CAAkB/I,GAAlB,CAAuB,gBAAA;AAAA,eAASoI,OAAM7E,KAAf;AAAA,OAAvB;WACN/H;;;uBASUpJ,UAA0BkW,eAAgC;UACrEA,eAAgBjW,QAASD;;;oBAWf2W,SAA0C;aAClDb,QAAOhV,MAAP,CAAe8V,WAAYD,QAAZ,GAClBA,OADkB,GAElB,gBAAA;AAAA,eAASvV,SAAUuV,QAAV,GACP3U,QAASgU,OAAM7E,OAAOwF,QADf,GAEPpK,SAAUlM,QAASsW,UAAWX,OAAM3G,MAFtC;AAAA,OAFG;;;mBAeO5L,MAAc3D,OAAwBwV,cAA+B;gBAC1E,gBAAA,EAAS;eAAQ1R,MAAOH,MAAM3D,OAAOwV;;;;2BASxBvU,KAAcyE,UAA8B;UAC5DqR,SAAS3C,SAAUnT,KAAK;UACxBvB,SAAWqX,OAAXrX;;UAEDA,QAAS;eACLS,QAAS,aAAA,EAAO;eACf6W,KAAK,cAAc,YAAM;gBACxB,CAAE,GAAEtX,QAAS;;;;;aAKjB;;;;;uBAYW0W,eAAkC;aAC7CA,gBAAgBpE,OAAOtS,SAASsW,QAAOtW;;;wBAQnB;aACpBsW,QAAOtW,MAAP,GAAgBmL,QAAQ2L;;;WAG1B;AACLlI,MAAAA,OAAAA,KADK;AAEL3F,MAAAA,SAAAA,OAFK;AAGLsO,MAAAA,UAAAA,QAHK;AAILC,MAAAA,KAAAA,GAJK;AAKLC,MAAAA,OAAAA,KALK;AAMLC,MAAAA,OAAAA,KANK;AAOLjW,MAAAA,KAAAA,GAPK;cAQLkW,QARK;eASLlX,SATK;AAULa,MAAAA,QAAAA,MAVK;AAWL8C,MAAAA,OAAAA,KAXK;AAYLwT,MAAAA,WAAAA,SAZK;AAaLC,MAAAA,UAAAA;AAbK;;;kBC1Pe7M,SAAgBqC,aAAwBlC,SAAoC;2BACvEiH,eAAgBpH;QAAnCnC,sBAAAA;QAAI5G,wBAAAA;QAAM+G,wBAAAA;;QACV8O,SAAWzK,YAAXyK;QACAhI,UAAYzC,YAAWgH,UAAvBvE;iCACsBzC,YAAW2I;QAAjClI,8BAAAA;QAAM0E,+BAAAA;QAAOC,8BAAAA;QACbiF,QAAUI,OAAVJ;QAKJK;QAKAC;;qBAKmB;;WAEfC,QAAQ,eAAe1K,SAAUvE,KAAK/G,IAAL,CAAW,IAAX,EAAiB6H,YAAjB;SACnC,CAAED,aAAF,EAAiBD,aAAjB,GAAkCsO;SAClCpO,cAAcqO;;;oBAOE;iBACT;iBACAhN,QAAQyE,SAAR,KAAsBb;YAE1BjB,MAAM,YAAYsK,KAAMjN,QAAQjG;YAChCsN,OAAO1C,QAAS,gBAAiBuI,WAAY;YAC7C7F,OAAO1C,QAAS,iBAAkBuI,WAAY;;;;sBAQ/B;UAChBC,UAAUrT,KAAM6I;;UAEjB,CAAEkK,QAAF,IAAcA,SAAS9S,KAAT,KAAmBoT,QAAQpT,KAAzC,IAAkD8S,SAASO,MAAT,KAAoBD,QAAQC,QAAS;cACnF/F,OAAO,UAAUgG;eAEjBpU,MAAO0L,QAAS,gBAAiBsI,KAAMjN,QAAQsN;eAC/CrU,MAAO,SAASsU,mBAAmB;;mBAG/BJ;aACLvO;;;;+BAOuB;aACxB3F,MAAO,UAAUuU,oBAAoB,MAAM;;;wBAW/B/V,OAAyB;UACpCgW,UAAYzN,QAAZyN;UACF3U,OAAO6L,QAASlN,QAAQ,UAAU;aACjCgW,WAAWR,KAAMQ,QAAS3U,KAAT,cAA+B2U,WAAY,IAAIA,OAA/C,EAAjB,IAA+E;;;8BAQtD;UAC5BL,SAAS;;UAERR,UAAW;iBACLc;eACDN,QAAQ;2BACEA,iBAAcF,WAAY,iBAAeA,WAAY;;;aAGlEE;;;yBAQoB;aACpBH,KAAMjN,QAAQoN,MAAR,IAAkBtT,KAAMwN,KAAN,CAAavN,KAAb,GAAqBiG,QAAQ2N;;;6BAQ7B;aACxB3N,QAAQ+D,SAAR,GAAoB,EAApB,GAAyBkJ,KAAMjN,QAAQgE,WAAd,gBAA2C,KAAK4J,cAAhD;;;8BAQA;aACzBX,KAAMjN,QAAQ6N,YAAd,gBACa7N,QAAQ8N,UAAR,GAAqB,EAArB,GAA0BF,iBAAmBF,WAD1D;;;4BASuB;UACxBJ,MAAML,KAAMjN,QAAQsN;6BACLA,eAAcA,eAAatN,QAAQ2L,OAAR,IAAmB,MAAM2B,eAAcA;;;wBAQ7D;aACnBxT,KAAMwN,KAAN,CAAc3C,QAAS,QAAvB;;;uBAWWD,OAAgBqJ,YAA+B;UAC3DzC,QAAQiB,MAAO7H,SAAS;aACvB4G,QACHxR,KAAMwR,MAAM9E,MAAZ,CAAqB7B,QAAS,QAA9B,kBAA2D,IAAIqJ,QAA/D,IACA;;;uBAYctJ,OAAeqJ,YAA+B;UAC1DzC,QAAQiB,MAAO7H;;UAEhB4G,OAAQ;YACL7T,QAAQqC,KAAMwR,MAAM9E,MAAZ,CAAqB7B,QAAS,QAA9B;YACRR,OAAQrK,KAAMwN,KAAN,CAAc3C,QAAS,OAAvB;eACPrJ,IAAK7D,QAAQ0M,KAAb,iBAAqC,IAAI6J,QAAzC;;;aAGF;;;0BAQqB;aACrBC,UAAWpO,QAAOhL,MAAP,GAAgB,GAAG,KAA9B,GAAuCoZ,UAAW,IAAI;;;sBAQrC;UAClB3C,QAAQiB,MAAO;aACdjB,SAAS4C,WAAYjV,MAAOqS,MAAM9E,OAAO7B,QAAS,gBAAlD,IAAyE;;;wBAW7DlN,OAAyB;aACrCyW,WAAYjV,MAAOoO,OAAO1C,qBAAoBlN,QAAQ,UAAU,UAAhE,IAAmF;;;WAGrF;AACLgM,MAAAA,OAAAA,KADK;AAEL0K,MAAAA,UAAAA,QAFK;AAGLC,MAAAA,WAAAA,SAHK;AAILC,MAAAA,YAAAA,UAJK;AAKLJ,MAAAA,WAAAA,SALK;AAMLK,MAAAA,YAAAA;AANK;;;kBCzNezO,SAAgBqC,aAAwBlC,SAAoC;2BAC7EiH,eAAgBpH;QAA7BnC,sBAAAA;QAAIG,wBAAAA;;QACJgN,WAAqB3I,YAArB2I;QAAU8B,SAAWzK,YAAXyK;QACVhI,UAAYzC,YAAWgH,UAAvBvE;QAKF4J,SAAwB;QAK1BC;;qBAKmB;;SAEjB/P,eAAe+I;SACf,CAAE9I,aAAF,EAAiBC,YAAjB,GAAiC0D;;;oBAMjB;UACbmM,aAAaC,qBAAwB;iBAChCD;aACJ7P;;;;uBAOe;aACf4P;YACDA;;;uBAOgB;;;;;uBAQA;UAClBC,aAAaC,qBAAsB;aAChChQ;;;;sBASSwC,OAAsB;UACjCkG,SAASwF,OAAON,GAAP,GAAarW,KAAb;UACPnB,SAAWsS,OAAXtS;;UAEHA,QAAS;eACJsS,OAAOtS,MAAP,GAAgBoM,OAAQ;eACxBkG,QAAQA;;;aAGVA,OAAOnR,KAAP,CAAc,CAACiL,KAAf,GAAwBkG,OAAOnR,KAAP,CAAc,CAAd,EAAiBiL,KAAjB,GAA2B3L,QAAS,UAAEgW,KAAF,EAAS5G,KAAT,EAAoB;cAC9EgK,SAAShK,QAAQzD;cACjBwF,QAASkI,UAAWrD,MAAM9E,OAAO9B;mBAC9BqH,OAAQtF,OAAOU,OAAQ,EAAR,CAAYX,SAAU3N,OAAQgS,SAASvD,MAAMb;eAC/D8H,QAAQ9H;iBACP2F,SAAU3F,OAAO/B,QAAQzD,KAAR,aAA2B,IAAIpM,MAA/B,GAAyCyW,MAAM5G;;;;;uBAazDtO,KAAkBsO,OAA6B;UAC3D+B,QAAQrQ,IAAIwY,SAAJ,CAAe,IAAf;eACJnI,OAAOzG,QAAQ3J,OAAR,CAAgBoQ;YAC3B1F,KAASlB,QAAO8C,IAAP,CAAY5B,gBAAazE,IAAKoI,QAAQ;aAC9C+B;;;iCAS4B;oBAClBzG,QAAXuO;;UAED,CAAE1O,QAAOiC,EAAP,CAAW8G,IAAX,GAAoB;kBAChB;iBACC,CAAEiG,SAAS;YACfC,YAAaC,QAASlE,SAASvD,MAAMtH,QAAS2E,QAAS;YACvDqK,aAAaF,aAAazT,KAAMvB,KAAM+Q,SAASxD,MAAf,CAAwB1C,QAAS,QAAjC,IAA+CmK;YAC/EG,YAAaD,cAAgBhP,QAAS2E,QAAS,aAAlB,IAAqC9E,QAAOhL,MAA5D,IAAwEmL,QAAQ2L;kBAE1FsD,qBAAsBlH,OAAS,SAAQmH,iBAAiB,CAAzB,IAA+B,IAAI;;;aAGtEL;;;WAGF;AACLpL,MAAAA,OAAAA,KADK;AAEL3F,MAAAA,SAAAA;AAFK;;;gBCrGa+B,SAAgBqC,aAAwBlC,SAAkC;2BACzEiH,eAAgBpH;QAA7BnC,sBAAAA;QAAIG,wBAAAA;;8BACuDqE,YAAWiN;QAAtEf,gCAAAA;QAAWE,iCAAAA;QAAYL,gCAAAA;QAAWE,+BAAAA;QAAUE,iCAAAA;gCACxBnM,YAAWgH;QAA/BvE,gCAAAA;QAASC,+BAAAA;iCACO1C,YAAW2I;QAA3BvD,8BAAAA;QAAMD,+BAAAA;QAKV+H;;qBAKmB;SACjB,CAAErR,aAAF,EAAiBa,aAAjB,EAAgCF,aAAhC,EAA+CD,aAA/C,GAAgE4Q;;;uBAM7C;sBACN/H,MAAM;;;0BASG;UACrB,CAAEgI,UAAW;oBACLC,OAAOjO;aACZzB,QAAO6E;aACP7F;;;;kBAYKqL,MAAcxF,OAAekC,MAAc/L,UAA+B;UAClF,CAAEyU,UAAW;YACRzN,MAAQhC,QAAO6B,MAAfG;YACF2N,WAAWC;YACXC,UAAWxF,SAASxF;kBAEhBgL,WAAW1P,QAAQ2P;YACxBpb;aACC0J,YAAYyG,OAAOkC,MAAMsD;oBAEpB0F,WAAW9Z,MAAOoU,MAAM,YAAM;qBAC5B2F,KAAMnL;oBACP;cACLpQ;eACC4J,aAAawG,OAAOkC,MAAMsD;;cAE3BlK,QAAQ8P,SAAR,KAAsB,MAAtB,IAAgC5F,SAAStD,IAAzC,IAAiD4I,aAAaC,eAAgB;wBACtEjE,WAAWuE,GAAI7F,OAAOtD,IAAP,GAAc,GAAd,GAAoB,KAAK,OAAO/L;iBACrD;wBACOA;;;;;;kBAWL6J,OAAsB;gBACxBsL,WAAYtL,OAAO;;;uBASZ8K,UAAkBS,aAA8B;UAC7D,CAAEpQ,QAAOiC,EAAP,CAAW+G,IAAX,GAAoB;aACpB5P,MAAMiX,0BAAyBvL,QAAS,cAAWsL,cAAcT,WAAWW,KAAMX;;;;kBAS5EA,UAA2B;UACnC,CAAEJ,OAAF,IAAavP,QAAOiC,EAAP,CAAW8G,IAAX,GAAoB;YAC9BqC,OAAcrG,OAAQ4K,WAAWC;YACjCW,cAAcC,cAAe,OAAOb,SAAtB,IAAoCvE,OAAO;YACzDqF,cAAcD,cAAe,MAAMb,SAArB,IAAmCvE,OAAO;;YAEzDmF,eAAeE,aAAc;qBACrBC,MAAOf,UAAUc;;;;aAIzBd;;;mBAWOA,UAAkBgB,WAA6B;UACvDC,SAASjB,WAAWkB,SAAUF;UAC9BG,OAAStC;kBACHuC,KAAMH,OAAN,GAAiBE,IAAjB,GAAwBtV,KAAMC,IAAKmV,OAAL,GAAgBE;aACnDnB;;;sBAMe;gBACZ;gBACCC;kBACAG,WAAWtO;;;qBAUNkO,UAA2B;UACrC7C,SAASzK,YAAWyK,MAAX,CAAkBN,GAAlB;UAEX3H,QAAc;UACdmM,cAAcC;;eAERjZ,IAAI,GAAGA,IAAI8U,OAAO9X,QAAQgD,KAAM;YAClCiR,aAAa6D,OAAQ9U,EAAR,CAAY6M;YACzBsG,WAAa1P,IAAK0U,WAAYlH,YAAY,KAAxB,GAAiC0G;;YAEpDxE,YAAY6F,aAAc;wBACf7F;kBACAlC;eACT;;;;;aAKFpE;;;wBAWYA,OAAeqM,UAA6B;UACzDvB,WAAW5K,OAAQqJ,UAAWvJ,QAAQ,EAAnB,GAAyBsM,OAAQtM;aACnDqM,WAAWE,KAAMzB,YAAaA;;;2BAQR;UACvBrL,OAAOQ,QAAS;aACf7K,KAAMwN,KAAN,CAAcnD,IAAd,IAAuBrK,KAAMuN,MAAN,CAAelD,IAAf,CAAvB,GAA+CS,OAAQ0J,WAAY;;;kBAU7DkB,UAA2B;UACnCxP,QAAQ8P,SAAR,IAAqBjQ,QAAOiC,EAAP,CAAW6G,KAAX,GAAqB;mBAClCuI,MAAO1B,UAAU,GAAG5K,OAAQyJ,eAAeF;;;aAGjDqB;;;oBAQQ9K,OAAwB;UAC/BtL,QAAU4G,QAAV5G;aACDA,UAAU,QAAV,GAAuB,cAAagV,UAAW1J,OAAO,KAA/B,IAA0C,CAAjE,GAAqE,CAACtL,KAAD,GAASgV,UAAW1J,MAApB,IAA+B;;;sBAU1FvJ,KAAuB;aACjC6U,WAAY7U,MAAM+G,YAAWsJ,UAAX,CAAsB2F,MAAtB,KAAiC,GAAG,CAAC,CAAEnR,QAAQ8P;;;sBAQ/C;aAClB,CAAC,CAAEV;;;2BAWYjU,KAA2BqU,UAA6B;iBACnEzW,YAAayW,SAAb,GAA0BC,aAA1B,GAA0CD;UAC/CY,cAAcjV,QAAQ,IAAR,IAAgByJ,OAAQ4K,SAAR,GAAqB5K,OAAQ8L,SAAU;UACrEJ,cAAcnV,QAAQ,KAAR,IAAiByJ,OAAQ4K,SAAR,GAAqB5K,OAAQ8L,SAAU;aACrEN,eAAeE;;;WAGjB;AACL7M,MAAAA,OAAAA,KADK;AAEL3F,MAAAA,SAAAA,OAFK;AAGLsT,MAAAA,MAAAA,IAHK;AAILvB,MAAAA,MAAAA,IAJK;AAKLwB,MAAAA,WAAAA,SALK;AAMLd,MAAAA,OAAAA,KANK;AAOLjP,MAAAA,QAAAA,MAPK;AAQLmK,MAAAA,SAAAA,OARK;AASLuE,MAAAA,YAAAA,UATK;AAULP,MAAAA,aAAAA,WAVK;AAWLiB,MAAAA,UAAAA,QAXK;AAYLpB,MAAAA,QAAAA,MAZK;AAaLe,MAAAA,eAAAA;AAbK;;;sBC/PmBxQ,SAAgBqC,aAAwBlC,SAAwC;2BAC3FiH,eAAgBpH;QAAvBnC,sBAAAA;;QACA4T,OAASpP,YAAToP;QACA7B,cAA0B6B,KAA1B7B;QAAaiB,WAAaY,KAAbZ;8BACWxO,YAAWyK;QAAnCD,+BAAAA;QAAUD,gCAAAA;QACZ8E,SAAU1R,QAAOiC,EAAP,CAAW8G,IAAX;QACV4I,UAAU3R,QAAOiC,EAAP,CAAW6G,KAAX;QAKZwB,YAAYnK,QAAQlK,KAAR,IAAiB;QAK7B2b,YAAYtH;QAKZuH;QAKAC;QAKAhG;;qBAKmB;;SAEjB,CAAEjN,aAAF,EAAiBD,aAAjB,GAAkCsO,MAAMrY,yBAAyB;;;oBAOjD;mBACP+X,UAAW;gBACXzM,QAAQ2R;gBACR3R,QAAQ2L;kBACRuF,MAAO/G,WAAW,GAAGuH,aAAa;;;gBAYpCE,SAA0BC,gBAA0BhX,UAA+B;UACxFqP,OAAOxH,MAAOkP;;UAEf5R,QAAQ8R,WAAY;eACf5H,MAAM,MAAM,MAAMlK,QAAQ+R,OAAOlX;aACpC;YACC6J,QAAQyL,KAAMjG;;YAEfxF,QAAQ,EAAR,IAAc,CAAE4M,KAAKhC,MAAL,EAAhB,uBAAqD5K,UAAUyF,SAA/D,GAA6E;mBACtEzF;eACL0M,KAAMlH,MAAMxF,OAAO+M,WAAW5W;;;;;oBAevCmX,aACAC,UACAC,MACAnQ,UACAlH,UACM;UACAqP,OAAO+H,WAAWD,cAAcG,OAAQH;kBAEnCzC,OAAO6C,OAAQH,YAAYC,IAAZ,GAAmBZ,KAAKtB,UAAL,CAAiB9F,IAAjB,EAAuB,IAAvB,CAAnB,GAAmD8H,aAAajQ,UAAU,YAAM;iBAC9FuP,KAAK7F,OAAL,CAAc6F,KAAK7B,WAAL,EAAd;oBACE5U;;;;mBAWA+W,SAAmC;UAC7ClN,QAAQyF;;UAEP1T,SAAUmb,UAAY;mBACOA,QAAQS,KAAR,CAAe,iBAAf,KAAsC;YAA5DC;YAAW5W;;YAEhB4W,cAAc,GAAd,IAAqBA,cAAc,KAAM;kBACpCC,iBAAkBpI,YAAY,OAAKmI,SAAL,IAAmB,CAAC5W,MAAD,IAAW,CAA9B,IAAoCyO,WAAW;mBAC3EmI,cAAc,KAAM;kBACtB5W,SAAS+P,QAAS,CAAC/P,UAAW8W,QAAS;mBACrCF,cAAc,KAAM;kBACtBG,QAAS;;aAEd;gBACGlB,SAASK,UAAUV,MAAOU,SAAS,GAAGT;;;aAGzCzM;;;qBAUSsN,aAAgC;aACzCU,YAAa,OAAOV;;;qBAUXA,aAAgC;aACzCU,YAAa,MAAMV;;;yBAWNpL,MAAeoL,aAAgC;UAC7DtW,SAASiW,yBAA0B,IAAIhG;UACvCzB,OAASqI,iBAAkBpI,YAAYzO,iBAAkB,KAAK,IAAKyO;;UAEpED,SAAS,EAAT,IAAesH,SAAU;YACvB,CAAEmB,mBAAoBlD,eAAeiB,SAAU,CAAE9J,OAAQ,IAAM;iBAC3DA,OAAO,IAAIuK;;;;aAIfa,cAAc9H,OAAOiG,KAAMjG;;;8BAeTA,MAAca,MAAc6H,aAAgC;UAChFlG,YAAa;YACV3W,MAAMob;;YAGPjH,OAAO,CAAP,IAAYA,OAAOnU,KAAM;cACvB6V,QAAS,GAAG1B,MAAMa,MAAM,KAAxB,IAAkCa,QAAS7V,KAAKgV,MAAMb,MAAM,OAAS;mBACjEuB,QAASoH,OAAQ3I;iBACnB;gBACAqH,QAAS;qBACLI,UACHzH,OACAA,OAAO,CAAP,GAAW,eAAiByB,WAAWA,OAA5B,CAAX,GAAmD+F;uBAC7C1R,QAAQuB,QAAS;qBACpB2I,OAAO,CAAP,GAAWnU,GAAX,GAAiB;mBACnB;qBACE;;;eAGN;cACA,CAAE6c,WAAF,IAAiB1I,SAASa,MAAO;mBAC7B4G,UAAUzH,OAAOuB,QAASoH,OAAQ9H,KAAR,WAA0BA,OAAO,KAAK,CAAtC;;;aAGhC;eACE;;;aAGFb;;;sBAUiB;UACpBnU,MAAM2b,aAAa/F;;UAElBD,cAAgB6F,UAAUI,SAAY;cACnCD,aAAa;;;aAGdvW,IAAKpF,KAAK;;;kBAUJ2O,OAAwB;UAChC6M,QAAS;eACL7E,aAAahI,QAAQgN,UAAR,YAA+B,IAAIA,aAAa,CAAhD,IAAsD;;;aAGrEhN;;;qBAUSqC,MAAuB;aAChCmK,MAAOxF,aAAa3E,OAAO4E,UAAU5E,MAAM,GAAGoK;;;oBAQtCzM,OAAwB;UAClC,CAAEgH,YAAa;gBACVE,QAASlH,OAAOgN,aAAa/F,SAAS+F,aAAa,EAAnD,GAAyDA,aAAa,CAAtE,GAA0EhN;gBAC1EtJ,MAAOsJ,QAAQiH;;;aAGlBjH;;;oBAUQsN,aAA8B;UACvCc,UAAUxB,KAAK7F,OAAL,CAAcuG,WAAd;aACTR,UAAUN,MAAO4B,SAAS,GAAG3B,YAAa2B;;;sBAQhCpO,OAAsB;UAClCA,UAAUyF,WAAY;oBACbA;oBACAzF;;;;sBASGkC,MAAyB;aACnCA,OAAO6K,YAAYtH;;;wBAQC;aACpB,CAAEpR,YAAaiH,QAAQ5G,MAAvB,IAAkC4G,QAAQgI;;;WAG5C;AACLvE,MAAAA,OAAAA,KADK;AAELsM,MAAAA,IAAAA,EAFK;AAGLqC,MAAAA,QAAAA,MAHK;AAILI,MAAAA,SAAAA,OAJK;AAKLC,MAAAA,SAAAA,OALK;AAMLtB,MAAAA,QAAAA,MANK;AAOL4B,MAAAA,UAAAA,QAPK;AAQLC,MAAAA,UAAAA,QARK;AASLvH,MAAAA,SAAAA,OATK;AAULoH,MAAAA,QAAAA,MAVK;AAWLV,MAAAA,QAAAA,MAXK;AAYLzG,MAAAA,UAAAA;AAZK;;;MC5VIuH,iBAAiB;MAKjBC,OAAO;MAKPC,OAAO;;kBCwBItT,SAAgBqC,aAAwBlC,SAAoC;2BACvEiH,eAAgBpH;QAAnCnC,sBAAAA;QAAI5G,wBAAAA;QAAM+G,wBAAAA;;QACVxH,UAAkB2J,QAAlB3J;QAASwT,OAAS7J,QAAT6J;QACTgB,WAAyB3I,YAAzB2I;QAAUW,aAAetJ,YAAfsJ;QAKd4H,UAAUvI,SAASnE;QAKnBE,OAAOiE,SAASjE;QAKhBC,OAAOgE,SAAShE;QAKhBwM;QAKE3M,SAAsC;;qBAKrB;;SAEjBhI,eAAeqO;;;oBAMC;UACf/M,QAAQ0G,QAAS;YACf,CAAEE,IAAF,IAAU,CAAEC,MAAO;;;;;UAKrBD,QAAQC,MAAO;YACb,CAAEH,OAAOE,MAAO;cACX7F,KAAO8J,SAASxD,MAAhBtG;uBAEM6F,MAAMuB,eAAepH;uBACrB8F,MAAMsB,eAAepH;iBAE5B6F,OAAOA;iBACPC,OAAOA;;eAIRzH,sBAAsBwH,MAAMC;eAC7B;kBACIuM,SAASpT,QAAQ0G,MAAR,KAAmB,KAAnB,GAA2B,MAA3B,GAAoC;;;;;uBAQnC;UAClB2M,SAAU;eACLD;aACH;wBACYxM,MAAM8B;wBACN7B,MAAM6B;;;;sBAOH;UACdqH,KAAOvE,WAAPuE;SACJ,CAAEhS,aAAF,EAAiBG,WAAjB,EAA8BQ,aAA9B,EAA6CD,aAA7C,EAA4DS,cAA5D,GAA8EkC;WAC5EyF,MAAM,SAAS,YAAM;WAAM,KAAK;;WAChCD,MAAM,SAAS,YAAM;WAAM,KAAK;;;;4BAMV;gBAClB/M,OAAQ,OAAOxD,QAAQqQ;aACvB4M,YAAa;aACbA,YAAa;gBACb;aAEFF,SAAS,CAAExM,IAAF,EAAQC,IAAR;aACTuM,SAAS9b,MAAO0I,QAAQ0G,MAAR,KAAmB,QAAnB,IAA+BmE,SAASzD,MAAxC,IAAkDvH,QAAO8C;;;yBAU7D4Q,OAAmC;UACjD5M,6BAA2BtQ,QAAQsQ,eAAW4M,QAAOld,QAAQuQ,OAAOvQ,QAAQwQ,6CAC9DoM,uCAAkCE,aAAUA,uBAAkBA,wBAAmBA,0BACpFnT,QAAQwT,SAAR,IAAqBN;aAE/BrH,UAA8BlF;;;sBAMf;UAChBjC,QAAY7E,QAAO6E;UACnB+M,YAAYjG,WAAWiH,OAAX;UACZgB,YAAYjI,WAAWgH,OAAX;UACZkB,YAAYjC,YAAY,EAAZ,IAAkB/M,QAAQ+M,SAA1B,GAAsC5H,KAAK8J,IAA3C,GAAkD9J,KAAKjD;UACnEgN,YAAYH,YAAY,EAAZ,IAAkB/O,QAAQ+O,SAA1B,GAAsC5J,KAAKgK,KAA3C,GAAmDhK,KAAKhD;WAErEiN,WAAWrC,YAAY;WACvBqC,WAAWL,YAAY;mBAEd7M,MAAMyB,YAAYqL;mBAClB7M,MAAMwB,YAAYuL;WAE1BvU,sBAAsBuH,MAAMC,MAAM4K,WAAWgC;;;WAG9C;AACL/M,MAAAA,QAAAA,MADK;AAELjD,MAAAA,OAAAA,KAFK;AAGL3F,MAAAA,SAAAA;AAHK;;;oBCxIiB+B,SAAgBqC,aAAwBlC,SAAsC;2BAC3EiH,eAAgBpH;QAAnCnC,sBAAAA;QAAI5G,wBAAAA;QAAM+G,wBAAAA;;QACVgN,WAAa3I,YAAb2I;QACFtK,WAAWyB,gBAAiBhC,QAAQO,UAAUV,QAAOkQ,EAAP,CAAUjZ,IAAV,CAAgB+I,OAAhB,EAAwB,GAAxB,GAA+BuB;QAC3EI,WAAajB,SAAbiB;QAKJuS;QAKAC;QAKAhT;;qBAKmB;UACb0G,WAAa1H,QAAb0H;;UAEHA,UAAW;mBACF;mBACA;;;YAGPA,aAAa,SAAU;;;;;;wBAWXuM,UAA0B;UACvCnb,OAASmb,WAAW,UAAU;UAC9BC,SAASrJ,SAAU/R;;UAEpBob,QAAS;qBACEA,QAAQ/L,eAAe0C,SAASxD,KAAT,CAAetG;qBACtCmT,QAAQ7L,YAAYrI,QAAQ6J,IAAR,CAAc/Q,IAAd;aAE5Bob,QAAQ,SAASD,WAAW9S,QAAQyG;;;;sBAOtB;UACdjF,OAASkI,SAATlI;;UAEH3C,QAAQmU,cAAe;aACpBxR,MAAM,yBAAyB,WAAA,EAAK;oBAC9BvI,EAAE0N,IAAF,KAAW;;;;;UAKpB9H,QAAQoU,cAAe;aACpBzR,MAAM,oBAAoB,WAAA,EAAK;oBACzBvI,EAAE0N,IAAF,KAAW;;;;;SAKrB,CAAE7J,UAAF,EAAcgB,YAAd,EAA4BR,aAA5B,GAA6C8B,SAASgB;;;oBAMtC;UACfC,cAAcU,YAAWyK,MAAX,CAAkBD,QAAlB,IAA+B;iBACvC5W,MAAO,CAAEkK,QAAQqU;kBAChBN,UAAU/S,SAAS;aACvBvB;;;;mBASM6U,QAAsB;AAAA,UAAtBA,MAAsB;AAAtBA,QAAAA,MAAsB,GAAb,IAAa;AAAA;;UAC/B,CAAE9S,YAAa;iBACTL;aACHxB;;;eAGC2U;;;0BAOiB;UACrB,CAAEtT,QAAS;YACT,CAAE+S,OAAF,IAAa,CAAEC,SAAU;;eAEvB;gBACE;;;;;oBAUIlT,MAAqB;UAC5B6G,MAAQkD,SAARlD;;UAEHA,KAAM;cACFA,KAAK,SAAa7G,OAAO;;;WAG5BpB,wBAAwBoB;;;WAGzB;AACL2C,MAAAA,OAAAA,KADK;AAEL3F,MAAAA,SAASyC,SAASe,MAFb;AAGLsG,MAAAA,MAAAA,IAHK;AAILzG,MAAAA,OAAAA,KAJK;AAKLK,MAAAA,UAAAA;AALK;;;iBC9Ic3B,SAAgBqC,aAAwBlC,SAAmC;4BACjFiH,eAAgBpH;QAAvBnC,uBAAAA;;qBAKe;UAChBsC,QAAQuU,OAAQ;WACf3U,uBAAuB,UAAEuM,GAAF,EAAOb,KAAP,EAAkB;iBAAU,MAAMa,KAAKb;;WAC9D,CAAEvN,aAAF,EAAiBW,aAAjB,EAAgCD,aAAhC,GAAiDnB,MAAMxG,IAAN,CAAY,IAAZ,EAAkB,IAAlB;;;;uBAOhC;YAChB;;;mBAQOyd,OAAuB;kBAC1B5H,OAAOrX,QAAS,eAAA,EAAS;YAC5B6W,MAAM7U,MAAyBgU,MAAMjC,SAAN,IAAmBiC,MAAM9E,OAAO;;YAEhE2F,OAAOA,IAAIqI,KAAM;iBACZD,OAAOpI,KAAKb;;;;;oBAYTiJ,OAAgBpI,KAAuBb,OAA8B;YAC9ErS,MAAO,cAAcsb,0CAAwCpI,IAAIqI,cAAW,IAAI;cAC7ErI,KAAKoI,QAAQ,SAAS;;;WAG1B;AACL9Q,MAAAA,OAAAA,KADK;AAEL3F,MAAAA,SAAAA;AAFK;;;MCrEI2W,wBAAwB;MAOxBC,kBAAkB;MAOlBC,kBAAkB;MAOlBC,gBAAgB;MAOhBC,eAAe;;kBCHJhV,SAAgBqC,aAAwBlC,SAAoC;4BAC7EiH,eAAgBpH;QAA7BnC,uBAAAA;QAAIG,yBAAAA;;QACJyT,OAASpP,YAAToP;QACA7B,cAAyC6B,KAAzC7B;QAAaiB,WAA4BY,KAA5BZ;QAAUL,gBAAkBiB,KAAlBjB;QAK3B9P;QAKAuU;;qBAKmB;SACjB7W,YAAY8W;SACZ,CAAErW,aAAF,EAAiBD,aAAjB,GAAkC6C;;;oBAYtC0Q,aACAjQ,UACAlH,UACAma,oBACM;UACAlf,QAAQ2Z;UACVwF,WAAW;iBAEElT,YAAYmT,gBAAiB5Z,IAAK0W,cAAclc;uBAChD+E;;iBAGNmH,gBAAiBD,UAAUoT,YAAY,cAAA,EAAQ;YAClD3F,WAAWC;YACXhW,SAAW3D,QAAU,eAAcA,KAAd,IAAwBsf,OAAQtU;YACrDmK,OAAa,UAASwE,aAAT,IAA2BwF;aAEzC5D,UAAW7B,WAAWvE;;YAEtBpL,QAAOiC,EAAP,CAAW6G,KAAX,KAAsB,CAAEqM,kBAAxB,IAA8C3E,iBAAkB;sBACvDsE;;cAEPrZ,IAAK2P,KAAL,GAAcwJ,uBAAwB;mBACjCpE,cAAe;;;SAG1B;WAEGpR;eACGnJ;;;oBAQM0a,WAA2B;aAClCE,SAAU,CAAEF,YAAakE,iBAAiB,MAAM;;;0BAM9B;UACpBlF,WAAWC;UACX/K,QAAQ4M,KAAK7F,OAAL,CAAc+D,QAAd;;UAET,CAAE5D,QAASlH,OAAO,GAAG7E,QAAOhL,MAAP,GAAgB,IAAM;aACzCwc,UAAWC,KAAKf,KAAL,CAAYf,QAAZ,EAAsB9K,QAAQ,CAA9B,GAAmC;;;wBAGnCoQ;WACZ5V;;;6BAUkB8L,UAA2B;aAC5C7P,IAAK6P,WAAW4J,eAAeC;;;qBAMjB;UAChBtU,UAAW;iBACLe;;;;sBAOW;UACjBf,YAAY,CAAEA,SAASiB,QAAT,IAAsB;;;;;;oBAa1B6T,GAAoB;UAC3BC,aAAetV,QAAfsV;aACDA,aAAaA,WAAYD,KAAM,IAAIna,KAAKqa,GAAL,CAAU,IAAIF,CAAd,EAAiB,CAAjB;;;WAGrC;AACL5R,MAAAA,OAAAA,KADK;AAEL3F,MAAAA,SAASiX,KAFJ;AAGL3C,MAAAA,QAAAA,MAHK;AAIL9Q,MAAAA,QAAAA;AAJK;;;MC3JIkU,WAAW;MAOXC,eAAe;MAOfC,sBAAsB;MAOtBC,sBAAsB;MAOtBC,oBAAoB;;gBCHX/V,SAAgBqC,aAAwBlC,SAAkC;4BAC3DiH,eAAgBpH;QAA3CnC,uBAAAA;QAAIG,yBAAAA;QAAM/G,yBAAAA;QAAMuJ,2BAAAA;;QAChBiR,OAA6BpP,YAA7BoP;QAAM/B,SAAuBrN,YAAvBqN;QAAQ/D,aAAetJ,YAAfsJ;QACdnE,QAAUnF,YAAW2I,SAArBxD;iCACoBnF,YAAWgH;QAA/BvE,iCAAAA;QAASC,gCAAAA;QACT6K,cAA+B6B,KAA/B7B;QAAaY,gBAAkBiB,KAAlBjB;QACfwF,kBAAkB;AAAEC,MAAAA,SAAS,KAAX;AAAkBC,MAAAA,SAAS;AAA3B;QAKpBC;QAKAC;QAKAC;QAKAC;QAKAC;QAKAC;QAMAC,cAAc;QAKdC;QAKAzC;QAKAra;;qBAKmB;WACf4N,OAAOsO,qBAAqB7a,MAAM+a;WAClCxO,OAAOuO,mBAAmB9a,MAAM+a;WAChCxO,OAAOqO,qBAAqBc,eAAeX;WAC3CxO,OAAO,SAASoP,SAAS;AAAEV,QAAAA,SAAS;AAAX;WACzB1O,OAAO,aAAaqP;SAEtB,CAAE3Y,aAAF,EAAiBW,aAAjB,GAAkCqO;;;oBAMlB;UACZhF,OAAS/H,QAAT+H;cACC,CAAEA;eACFA,SAAS;;;2BAUI3N,GAAmC;UACpD,CAAE0Z,UAAW;YACR6C,SAAW3W,QAAX2W;YACFC,UAAcC,aAAczc;YAC5B0c,cAAc,CAAEH,MAAF,IAAc7K,cAAe1R,EAAEX,OAAjB,IAA6B,CAAEpC,QAAS+C,EAAEX,QAAQkd;;YAE/EG,2BAA4B,CAAE1c,EAAE8Z,SAAW;cACzC,CAAE5C,KAAKhC,MAAL,IAAgB;qBACJsH,UAAUvP,QAAQyF;4BAClB;wBACA;6BACA;iBAEXrT,QAAQkc,qBAAqBoB,eAAelB;iBAC5Cpc,QAAQmc,mBAAmBoB,aAAanB;iBACzCvU;mBACEA;iBACDlH;iBACD;oBACIA,GAAG;;;;;;2BAWIA,GAAmC;UACpD,CAAE+b,WAAY;aACXrX;;;kBAGI1E;;UAEPA,EAAE6c,YAAa;YACZhM,OAAOiM,QAAS9c,EAAT,GAAe8c,QAASjB;;YAEhCI,UAAW;eACThF,UAAW2E,eAAemB,UAAWlM;cAEpCmM,UAAWC,OAAQjd,EAAR,GAAcid,OAAQpB,UAAtB,GAAoCR;cAC/C6B,WAAWhB,+BAAgCjG;;cAE5C+G,WAAWE,UAAW;iBACnBld;;;eAGF2E;2BACW;kBACR3E;eACJ;cACmBmd,aAAevX,QAAjCwX;uBACOvf,SAAUsf,WAAV,GAAyBA,UAAzB,GAAsC;AAAEE,YAAAA,OAAO,CAAT;AAAYC,YAAAA,OAAO,CAACH,UAAD,IAAe;AAAlC;qBACtCjc,IAAK2P,KAAL,iBAA8B7Q,KAAMmd,WAAWG,QAAQH,WAAWE,KAAlE;;cAERE,qBAAsB;oBAChBvd;;;;;;yBAaKA,GAAmC;aAC/CX,QAAQkc,qBAAqBoB;aAC7Btd,QAAQmc,mBAAmBoB;;UAE9Bb,WAAY;YACVE,YAAcjc,EAAE6c,UAAF,IAAgBU,qBAAwB;cACnDC,WAAcC,gBAAiBzd;cAC/B4X,cAAc8F,mBAAoBF;;cAEnCxB,QAAS;uBACDhE,OAAQJ;qBACTnS,QAAOiC,EAAP,CAAW+G,IAAX,GAAoB;uBACnBkH,GAAIlQ,QAAO6E,KAAP,GAAeE,OAAQgM,KAAMgH;iBACvC;uBACM7H,GAAIvE,WAAW2G,MAAX,CAAmBH,WAAnB,GAAkC;;;kBAG1C5X;;;aAGL4E;;;iBAGG;;;kBAQE5E,GAAmC;sBAChC6b;kBACA7b;qBACAqV;;;qBASArV,GAAsB;UACjC,CAAE0Z,QAAF,IAAcyC,gBAAiB;gBACzBnc,GAAG;;;;iCASsB;UAC9B2d,QAAQzc,IAAK4b,QAASf,UAAT,GAAuBe,QAASjB;UAC7C+B,QAAQ1c,IAAK4b,QAASf,WAAW,KAApB,GAA6Be,QAASjB,WAAW;aAC7D8B,QAAQC;;;6BAUS5d,GAAqC;UACxDyF,QAAOiC,EAAP,CAAW8G,IAAX,KAAqB,CAAE0N,aAAc;YAClC2B,OAAYhC,cAAcE,SAAd,IAA2BD,aAA3B,IAA4CD;YACxDiC,YAAYhB,QAASf,UAAT,GAAuBe,QAASe;YAC5CE,WAAYd,OAAQjd,EAAR,GAAcid,OAAQY;YAClCG,UAAYf,OAAQjd,EAAR,GAAcid,OAAQlB,UAAtB,GAAoCV;;YAEjD0C,YAAYC,SAAU;iBAClBF,YAAYC;;;;aAIhB;;;gCAUoBP,UAA2B;aAC/CnI,gBAAgBmB,KAAMgH,SAAN,GAAmB3c,IACxCK,IAAKsc,SAAL,YAA4BS,cAAc,GAA1C,GACAjC,SAAStF,WAAW5O,YAAWiN,MAAX,CAAkBhB,QAAlB,cAAyCe,iBAAiB,CAA1D;;;qBAaN9U,GAA4Bke,YAA+B;aAClE,cAAcle,KAAMA,EAAEme,OAAF,CAAW,CAAX,IAAiBne,CAArC,WAAkDuK,QAAS2T,aAAa,MAAM,IAA9E;;;oBAUMle,GAAqC;aAC7CA,EAAEoe;;;uBAWSvN,MAAuB;aAClCA,uBAAwBpL,QAAOiC,EAAP,CAAW6G,KAAX,IAAqB6M,WAAW;;;0BAU1Cpb,GAA8C;aAC5D,OAAOqe,UAAP,KAAsB,WAAtB,IAAqCre,aAAaqe;;;0BAQ5B;aACtBpC;;;qBAQSlhB,OAAuB;iBAC5BA;;;WAGN;AACLsO,MAAAA,OAAAA,KADK;AAELiV,MAAAA,SAAAA,OAFK;AAGLC,MAAAA,YAAAA;AAHK;;;AC1UT,MAAMC,gBAAgB,CAAE,MAAF,EAAU,OAAV,EAAmB,IAAnB,EAAyB,MAAzB,CAAtB;;oBAa0B/Y,SAAgBqC,aAAwBlC,SAAsC;4BACzEiH,eAAgBpH;QAArCnC,uBAAAA;QAAI5G,yBAAAA;QAAMuJ,2BAAAA;;QACVsC,OAAST,YAAW2I,SAApBlI;QACAgC,UAAYzC,YAAWgH,UAAvBvE;QAKJlL;QAKAqa;;qBAKmB;;SAEjBpV,eAAema;SACf5a,YAAYwL;;;oBAMI;8BACYzJ,QAAxB8Y;UAAAA,0CAAW;;UAEdA,UAAW;YACTA,aAAa,WAAY;mBACnBnW;uBACKA,MAAM4F,WAAW;eAC1B;mBACIuE;;;aAGLrT,QAAQ,WAAWsf;;;;uBAOJ;aACftf,QAAQ;;UAEXqS,cAAerS,SAAW;wBACZA,QAAQ8O;;;;sBAQL;iBACX;eACD,YAAM;mBAAa;;;;yBAMJ;;;;;uBAUPnO,GAAyB;UACtC,CAAE0Z,UAAW;YACRhc,MAAQsC,EAARtC;YACFkhB,gBAAgBpX,SAAUgX,eAAe9gB,IAAzB,aAA0CA,GAA1C,GAAmDA;;YAEpEkhB,kBAAkBrU,QAAS,cAAgB;kBACvCoL,GAAI;mBACDiJ,kBAAkBrU,QAAS,eAAiB;kBAC/CoL,GAAI;;;;;WAKV;AACLtM,MAAAA,OAAAA,KADK;AAEL3F,MAAAA,SAAAA;AAFK;;;MClHImb,qBAAyB9kB;MAOzB+kB,wBAA4BD;MAO5BE,uBAAsBF,8BAA2BC;;oBCqCpCrZ,SAAgBqC,aAAwBlC,SAAsC;4BACtEiH,eAAgBpH;QAAxCnC,uBAAAA;QAAIC,wBAAAA;QAAK7G,yBAAAA;QAAM+G,yBAAAA;;QACjBub,eAAepZ,QAAQqZ,QAAR,KAAqB;QAKtCnN,SAA+B;QAK/BxH,QAAQ;;qBAKW;UAChB1E,QAAQqZ,UAAW;WAClB,CAAEtb,aAAF,EAAiBU,aAAjB,GAAkC,YAAM;;;;;YAKvC,CAAE2a,cAAe;aAChB,CAAErb,aAAF,EAAiBU,aAAjB,EAAgCP,WAAhC,GAA+CmE;;;;;oBAQzC;kBACHsK,OAAOrX,QAAS,gBAAA,EAAU;iBACPgkB,OAAO9S,OAAO2S,gBAAiB7jB,QAAS,cAAA,EAAQ;cACpEkf,MAASlb,aAAcigB,MAAMN;cAC7BO,SAASlgB,aAAcigB,MAAML;;cAE9B1E,QAAQ+E,KAAK/E,GAAb,IAAoBgF,WAAWD,KAAKC,QAAS;gBAC1CC,WAAW5f,OAAQ,QAAQmG,QAAQ3J,OAAR,CAAgB2Q,SAASuS,KAAKG;;yBACjDD,UAAUvR,MAAM;mBACvBzS,KAAM;AAAE8jB,cAAAA,MAAAA,IAAF;AAAQD,cAAAA,QAAAA,MAAR;AAAgB9E,cAAAA,KAAAA,GAAhB;AAAqBgF,cAAAA,QAAAA,MAArB;AAA6BC,cAAAA,UAAAA;AAA7B;aACXF,KAAK/E,OAAOmF,QAASJ,MAAM;;;;;UAK9BH,cAAe;;;;;uBAQH;cACR;eACA;;;uBAOc;eACdlN,OAAO/V,MAAP,CAAe,cAAA,EAAQ;YACxB6U,WAAWhL,QAAQ2L,OAAR,aAA8BiO,gBAAgB,KAAM,CAApD,IAA0D;;YAEtEtZ,KAAKgZ,MAAL,CAAYpO,QAAZ,CAAsBrL,QAAO6E,KAA7B,EAAoCsG,QAApC,GAAiD;iBAC7C6O,KAAMvZ;;;eAGR;OAPA;;UAUJ,CAAE4L,OAAOrX,QAAS;YAChBqJ;;;;kBASMoC,MAAiC;UACtCiZ,OAASjZ,KAATiZ;eAEEjZ,KAAKgZ,MAAL,CAAY9S,OAAOH;WACvBkT,MAAM,cAAc,WAAA,EAAK;eAAUjZ,MAAMlG,EAAE0N,IAAF,KAAW;;OAExD,OAAO,UAAWxS,QAAS,cAAA,EAAQ;YAC9BgL,KAAM/J,OAAS;uBACJgjB,MAAMhjB,MAAM+J,KAAM/J;0BACfgjB,MAAMhjB,SAAS,KAAT,GAAiB0iB,kBAAjB,GAAsCC;;;;;oBAWlD5Y,MAA0BwZ,OAAuB;UACxDR,SAAWhZ,KAAXgZ;kBAEKA,OAAO9S,OAAOH;;UAEtB,CAAEyT,OAAQ;eACLxZ,KAAKmZ;gBACJnZ,KAAKiZ,MAAM;aACd3Z,uBAAuBU,KAAKiZ,MAAMD;aAClC3a;;;UAGHya,cAAe;;;;;wBAQI;UACnB1U,QAAQwH,OAAOrX,QAAS;aACrBqX,OAAQxH;;;;WAIX;AACLjB,MAAAA,OAAAA,KADK;AAEL3F,MAAAA,SAAAA;AAFK;;;sBC1HmB+B,SAAgBqC,aAAwBlC,SAAwC;4BACvEiH,eAAgBpH;QAA3CnC,uBAAAA;QAAIG,yBAAAA;QAAM/G,yBAAAA;QAAMuJ,2BAAAA;;QAChBsM,SAAiCzK,YAAjCyK;QAAQ9B,WAAyB3I,YAAzB2I;QAAUW,aAAetJ,YAAfsJ;QAClBE,WAAuBF,WAAvBE;QAAUsH,WAAaxH,WAAbwH;QAKZxd,QAA0B;QAK5B8R;;qBAKmB;;SAEjB,CAAE5I,aAAF,EAAiBD,aAAjB,GAAkCsO;SAClC,CAAE9O,UAAF,EAAciB,cAAd,GAAgCkC;;;oBAMhB;;;UAGfpB,QAAQ8G,UAAR,IAAsB6F,OAAOD,QAAP,IAAoB;;aAEvCpN,0BAA0B;AAAEgI,UAAAA,MAAAA,IAAF;AAAQ9R,UAAAA,OAAAA;AAAR,WAAiB+W,MAAO1M,QAAO6E;;;;;uBAQ1C;UAClB4C,MAAO;eACFA;cACFhS,QAAS,cAAA,EAAQ;iBAAU+N,KAAK6Q,QAAQ;;cACvC1e;eACA;;;;gCAOuB;UACxBX,SAAWgL,QAAXhL;UACAwB,UAA2B2J,QAA3B3J;UAASwT,OAAkB7J,QAAlB6J;UAAM8B,UAAY3L,QAAZ2L;UACjBhV,SAASqJ,QAAQ8G,UAAR,KAAuB,QAAvB,IAAmC+D,SAASzD,MAA5C,IAAsDyD,SAASlI;UACxExH,MAASuQ,aAAa7W,SAASwG,KAAMxG,SAAS8W;aAE7C9R,OAAQ,MAAMxD,QAAQyQ,YAAYnQ;;eAE/BkB,IAAI,GAAGA,IAAIsD,KAAKtD,KAAM;YACxBkiB,KAAWlgB,OAAQ,MAAM,MAAMyN;YAC/B4M,SAAWra,OAAQ,UAAU;AAAEmgB,UAAAA,OAAO3jB,QAAQ0Q,IAAjB;AAAuBe,UAAAA,MAAM;AAA7B,WAAyCiS;YACtEhQ,WAAW4C,OAAOL,KAAP,CAAczU,CAAd,EAAkBoL,GAAlB,CAAuB,eAAA;AAAA,iBAASqI,MAAM9E,KAAN,CAAYzF,EAArB;AAAA,SAAvB;YACXkZ,OAAW,CAAEvO,UAAF,IAAgBC,UAAU,CAA1B,GAA8B9B,KAAKqQ,KAAnC,GAA2CrQ,KAAKC;aAE3DoK,QAAQ,SAASuC,QAAQ3f,IAAR,CAAc,IAAd,EAAoBe,CAApB;qBAETqc,QAAQ/L,eAAe4B,SAASxM,IAAT,CAAe,GAAf;qBACvB2W,QAAQ7L,YAAYuB,OAAQqQ,MAAMpiB,IAAI;cAE9CpC,KAAM;AAAEskB,UAAAA,IAAAA,EAAF;AAAM7F,UAAAA,QAAAA,MAAN;AAAcnN,UAAAA,MAAMlP;AAApB;;;;qBAYEkP,MAAqB;iBAC1BgJ,SAAShJ,MAAS,MAAM,YAAM;YACjCuE,QAAQqB,OAAOJ,KAAP,CAAcf,WAAWC,OAAX,CAAoB1E,IAApB,CAAd;iBACL3N,MAAOkS,MAAM9E;;;;mBAWV9B,OAA4C;aACnDlP,MAAOgW,WAAWqH,MAAX,CAAmBnO,KAAnB;;;sBAMQ;UAChBkC,OAAO2F,MAAOyG,SAAU;UACxBmH,OAAO5N,MAAOyG;;UAEfpM,MAAO;oBACGA,KAAKsN,QAAQjO;wBACTW,KAAKsN,QAAQ9L;;;UAG3B+R,MAAO;iBACAA,KAAKjG,QAAQjO;qBACTkU,KAAKjG,QAAQ9L,cAAc;;;WAGrC7I,0BAA0B;AAAE+H,QAAAA,MAAAA,IAAF;AAAQ9R,QAAAA,OAAAA;AAAR,SAAiBoR,MAAMuT;;;WAGlD;AACL3kB,MAAAA,OAAAA,KADK;AAELiO,MAAAA,OAAAA,KAFK;AAGL3F,MAAAA,SAAAA,OAHK;AAILyO,MAAAA,OAAAA;AAJK;;;AC/JT,MAAM6N,eAAe,CAAE,GAAF,EAAO,OAAP,EAAgB,UAAhB,CAArB;;gBAasBva,SAAgBqC,aAAwBlC,SAAkC;QACtFgK,UAAYnK,QAAZmK;QACA1C,OAASpF,YAAW2I,SAApBvD;;qBAKe;UAChBtH,QAAQgI,cAAe;;aAErB;;;;;uBAQgB;sBACNV,MAAMoB;;;oBAOH;UACd2R,YAAsB;cAEpBC,OAAQza,SAASvK,QAAS,UAAE2U,MAAF,EAAUvF,KAAV,EAAiB6V,SAAjB,EAAgC;gCACjDtT,eAAgBgD;YAAvBvM,uBAAAA;;WAEJO,YAAY,UAAEuc,MAAF,EAAS5T,IAAT,EAAesD,IAAf,EAAyB;oBAC7B5U,QAAS,kBAAA,EAAY;gBACxBmlB,aAAaxQ,MAAb,IAAuB,CAAErI,SAAUyY,WAAWpQ,SAAW;wBAClDxU,KAAMglB;uBACPzR,WAAWsI,KAAKhQ;uBAChByO,GAAI0K,SAAS3Y,EAAT,CAAa8G,IAAb,IAAsBsB,IAAtB,GAA6BsQ;;;gBAIvCH;;;;;wBASa;8BACHpT,eAAgBpH;UAA7BnC,uBAAAA;UAAIG,yBAAAA;;SAERM,aAAasY;SACbjY,qBAAqBua;SACrB,CAAEhb,aAAF,EAAiBW,aAAjB,GAAkC0C;mBAExBkG,MAAMY,MAAM;WAEpB1I,0BAA0BK,QAAOmK;;;sBAMjB;mBACR1C,MAAMmB,kBAAkBzI,QAAQyE,SAAR,KAAsBb,GAAtB,GAA4B,YAA5B,GAA2C;;;qBAQjE0H,OAA8B;cACvCyE,GAAIzE,MAAM5G;;;uBASC4G,OAAuBlR,GAAyB;UAC7DwH,SAAUwY,cAAchgB,EAAEtC,MAAQ;gBAC5BwT;gBACAlR;;;;WAIN;AACLqJ,MAAAA,OAAAA,KADK;AAEL3F,MAAAA,SAAAA;AAFK;;;iBC9Gc+B,SAAgBqC,aAAwBlC,SAAmC;4BAC/EiH,eAAgBpH;QAAzB/I,yBAAAA;;qBAKe;UAChBkJ,QAAQ0a,OAAQ;aACbxY,YAAW2I,QAAX,CAAoBxD,OAAO,SAASsT,SAAS;AAAE7E,UAAAA,SAAS,KAAX;AAAkBC,UAAAA,SAAS;AAA3B;;;;qBASrC3b,GAAsB;UAC9BwgB,SAAWxgB,EAAXwgB;;UAEHA,QAAS;gBACL7K,GAAI6K,SAAS,CAAT,GAAa,GAAb,GAAmB;gBACrBxgB;;;;WAIN;AACLqJ,MAAAA,OAAAA;AADK;;;;;;;;;;;;;;;;;;;;;;;;MC9CIoX,OAAO;AAClBjU,IAAAA,MAAQ,gBADU;AAElBC,IAAAA,MAAQ,YAFU;AAGlBgN,IAAAA,OAAQ,mBAHU;AAIlBF,IAAAA,MAAQ,kBAJU;AAKlB7J,IAAAA,QAAQ,gBALU;AAMlBoQ,IAAAA,OAAQ,eANU;AAOlBtS,IAAAA,MAAQ,gBAPU;AAQlBzG,IAAAA,OAAQ;AARU;MCMP2Z,WAAoB;AAC/BhT,IAAAA,MAAmB,OADY;AAE/BiK,IAAAA,OAAmB,GAFY;AAG/BpC,IAAAA,mBAAmB,IAHY;AAI/BhE,IAAAA,SAAmB,CAJY;AAK/BjF,IAAAA,QAAmB,IALY;AAM/BI,IAAAA,YAAmB,IANY;AAO/BvG,IAAAA,UAAmB,GAPY;AAQ/B4T,IAAAA,cAAmB,IARY;AAS/BC,IAAAA,cAAmB,IATY;AAU/BC,IAAAA,eAAmB,IAVY;AAW/Be,IAAAA,QAAmB,+BAXY;AAY/BrN,IAAAA,MAAmB,IAZY;AAa/BtD,IAAAA,WAAmB,KAbY;AAc/BiG,IAAAA,YAAmB,IAdY;AAe/BoF,IAAAA,WAAmB,IAfY;AAgB/BxG,IAAAA,gBAAmB,4CAhBY;AAiB/BjT,IAAAA,SAAmBkQ,OAjBY;AAkB/BsD,IAAAA,MAAmBgR;AAlBY;;gBCOXhb,SAAgBqC,aAAwBlC,SAAwC;4BACrFiH,eAAgBpH;QAAvBnC,uBAAAA;;qBAMe;SACjB,CAAEK,aAAF,EAAiBU,aAAjB,GAAkC,YAAM;iBAChC,YAAM;sBACHkO,OAAO1T,MAAO,2BAA0B+G,QAAQ+R,gBAAa/R,QAAQoV;;;;;mBAYtE1Q,OAAeqW,MAAyB;UAC9C1T,QAAUnF,YAAW2I,SAArBxD;YACDA,OAAO,UAAU4F,KAAMnT,KAAMuN,MAAN,CAAc+F;eAElC,YAAM;;cAEP/F,OAAO,UAAU;;;;WAIrB;AACL5D,MAAAA,OAAAA,KADK;AAEL3N,MAAAA,OAAAA,KAFK;AAGLwL,MAAAA,QAAQxG;AAHH;;;iBChCc+E,SAAgBqC,aAAwBlC,SAAwC;4BACpFiH,eAAgBpH;QAAzB/I,yBAAAA;;QACAwa,OAAqBpP,YAArBoP;QAAM9F,aAAetJ,YAAfsJ;QACNlE,OAASpF,YAAW2I,SAApBvD;QAKJ0T;;qBAKmB;WACf1T,MAAM,iBAAiB,WAAA,EAAK;YAC3BlN,EAAEX,MAAF,KAAa6N,IAAb,IAAqB0T,aAAc;;;;;;;mBAc5BtW,OAAeqW,MAAyB;UAChD/I,cAAcV,KAAKtB,UAAL,CAAiBtL,KAAjB,EAAwB,IAAxB;UACd8K,WAAc8B,KAAK7B,WAAL;UACdsC,QAAckJ,SAAUvW;;UAEzBpJ,IAAK0W,cAAcxC,SAAnB,IAAiC,CAAjC,IAAsCuC,SAAS,GAAI;6BACjCA,gBAAa/R,QAAQoV;aACrC/D,UAAWW,aAAa;sBACf+I;aACT;aACAlL,KAAMnL;;;;;sBAQS;YACf;;;sBAQUA,OAAwB;UACjCwW,cAAgBlb,QAAhBkb;;UAEHrb,QAAOiC,EAAP,CAAW6G,KAAX,KAAsBuS,aAAc;YACjCtU,OAAO4E,WAAWwH,QAAX,CAAqB,IAArB;YACPjd,MAAOyV,WAAW2F,MAAX;;YAENvK,SAAS,CAAT,IAAclC,SAAS3O,GAAvB,IAAkC6Q,QAAQ7Q,GAAR,IAAe2O,UAAU,GAAM;iBAC/DwW;;;;aAIJlb,QAAQ+R;;;mBAQDoJ,YAA2B;YAClC7T,MAAM,cAAc6T;;;WAGtB;AACL1X,MAAAA,OAAAA,KADK;AAEL3N,MAAAA,OAAAA,KAFK;AAGLwL,MAAAA,QAAAA;AAHK;;;AC/EF;AA8DL,qBAAa7H,MAAb,EAA2CuG,OAA3C,EAA+D;mBA3C9Bob;wBAKC;mBAKJC,MAAOjnB;qBAKR;sBAKQ;yBAUuB;UAcpDuO,OAAOlM,SAAUgD,OAAV,GAAqBgO,MAAoB/O,UAAUe,OAAnD,GAA8DA;aACnEkJ,MAAUA;WAEbA,OAAOA;YAELmY,UAAUQ,QAAOC;YACjBrjB,MAAO,KAAKsjB,UAAUV,WAAY9a,WAAW;;;;;WAWtDyD,QAAA,eAAOgY,UAAP,EAA0D7L,UAA1D,EAAoG;AAAA;;UAC1FlO,QAAsB,KAAtBA;wBAAsB,KAAfsH;aACPtH,MAAMI,EAAN,CAAU,CAAE1N,OAAF,EAAWI,SAAX,CAAV,GAAoC;YAEtCqN,IAAKzN;WAENsnB,cAAcxZ;WACdyZ,cAAc/L,cAAc,KAAK+L,WAAnB,UAAyC7Z,GAAI+G,QAAS+S,OAAOtQ,KAA7D;WACduQ,cAAcJ,cAAc,KAAKI;UAEhCC,eAAe7T,OAAQ,IAAI8T,uBAAuB,KAAKF,aAAa;AAAEjM,QAAAA,YAAY,KAAK+L;AAAnB;aAElEG,cAAc,UAAEE,SAAF,EAAalkB,GAAb,EAAsB;YACpCmkB,YAAYD,UAAW,QAAM9Z,aAAY,OAAKsZ;oBACxC1jB,OAAQmkB;kBACVvY,SAASuY,UAAUvY,KAAV;;aAGbxB,aAAY,mBAAA,EAAa;kBACrBuB,SAASwY,UAAUxY,KAAV;;WAGhB5F,KAAME;eAED,KAAK4E,MAAMqD;YAEfnE,IAAKvN;WACNuJ,KAAMG;aAEJ;;;WAqBTke,OAAA,cAAMjS,MAAN,EAA6B;WACtBD,QAAQvU,KAAMwU;aACZD,QAAQvU,KAAM;aACd;;;WAuCTsa,KAAA,YAAI6B,OAAJ,EAAqC;WAC9B8J,YAAYlQ,WAAWuE,GAAI6B;;aACzB;;;WA0BTlU,KAAA,YAAIlB,MAAJ,EAA+B3B,QAA/B,EAAkE;WAC3D6B,MAAMgB,GAAIlB,QAAQ3B,UAAU,MAAMlG;aAChC;;;WAsBTgJ,MAAA,aAA+BnB,MAA/B,EAA2E;WACpEE,MAAMiB,IAAKnB;aACT;;;WAaTqB,OAAA,cAAMnB,KAAN,EAA4B;AAAA;;0BAErBA,OAAMmB,yBAAMnB,cAAU1G,MAAO+B,WAAW;;aACtC;;;WAuBTzB,MAAA,aAAK6Q,MAAL,EAAiEzC,KAAjE,EAAwF;WACjFgX,YAAY/O,OAAOrW,IAAK6Q,QAAQzC;;aAC9B;;;WAST8H,SAAA,gBAAQR,OAAR,EAAsC;WAC/B0P,YAAY/O,OAAOH,OAAQR;;aACzB;;;WAUTlK,KAAA,YAAIgG,IAAJ,EAA4B;aACnB,KAAK0T,QAAL,CAAc1T,IAAd,KAAuBA;;;WAQhCN,UAAA,mBAAgB;WACT3J,KAAMY;aACJ;;;WAUTX,UAAA,iBAASsF,UAAT,EAAmC;AAAA,UAA1BA,UAA0B;AAA1BA,QAAAA,UAA0B,GAAb,IAAa;AAAA;;UACzB1G,QAAiB,KAAjBA;UAAOgF,QAAU,KAAVA;;UAEVA,MAAMI,EAAN,CAAU1N,OAAV,GAAsB;cAEnBsJ,GAAIM,aAAa,KAAKF,OAAL,CAAahH,IAAb,CAAmB,IAAnB,EAAyBsM,UAAzB,GAAuC;aACzD;eACG,KAAKsY,aAAa,mBAAA,EAAa;oBAC3B5d,WAAWme,UAAUne,OAAV,CAAmBsF,UAAnB;WACpB;cAEGvF,KAAMsB;cACNrB;sBACQqe,MAAO,KAAKnS;cACpBnI,IAAKrN;;;aAGN;;;;;0BAQc;eACd,KAAKgnB;;wBAQDxb,SAAmB;YACtBwb,WAAa,KAAbA;cACDA,UAAUxb;;YAEZ,CAAE,KAAK0B,KAAL,CAAWI,EAAX,CAAe1N,OAAf,GAA2B;eAC3ByJ,KAAMa,eAAe8c;;;;;0BAST;eACZ,KAAKE,WAAL,CAAiB/O,MAAjB,CAAwBF,SAAxB,CAAmC,IAAnC;;;;0BAQW;eACX,KAAKiP,WAAL,CAAiBlQ,UAAjB,CAA4BwH,QAA5B;;;;;KA/WJ;;;AAAAoJ,EAAAA,OAIEb,QAJF,GAIsB,EAJtB;AAAAa,EAAAA,OASW3nB,MATX,GASoBA,MATpB;;"}