{"version": 3, "file": "splide.min.js", "sources": ["../../src/js/constants/project.ts", "../../src/js/constants/states.ts", "../../src/js/utils/array/empty/empty.ts", "../../src/js/utils/type/type.ts", "../../src/js/utils/array/toArray/toArray.ts", "../../src/js/utils/array/forEach/forEach.ts", "../../src/js/utils/array/includes/includes.ts", "../../src/js/utils/array/push/push.ts", "../../src/js/utils/array/index.ts", "../../src/js/utils/arrayLike/slice/slice.ts", "../../src/js/utils/arrayLike/find/find.ts", "../../src/js/utils/dom/toggleClass/toggleClass.ts", "../../src/js/utils/dom/addClass/addClass.ts", "../../src/js/utils/dom/append/append.ts", "../../src/js/utils/dom/before/before.ts", "../../src/js/utils/dom/matches/matches.ts", "../../src/js/utils/dom/children/children.ts", "../../src/js/utils/dom/child/child.ts", "../../src/js/utils/object/forOwn/forOwn.ts", "../../src/js/utils/object/assign/assign.ts", "../../src/js/utils/object/merge/merge.ts", "../../src/js/utils/dom/removeAttribute/removeAttribute.ts", "../../src/js/utils/dom/setAttribute/setAttribute.ts", "../../src/js/utils/dom/create/create.ts", "../../src/js/utils/dom/style/style.ts", "../../src/js/utils/dom/display/display.ts", "../../src/js/utils/dom/getAttribute/getAttribute.ts", "../../src/js/utils/dom/hasClass/hasClass.ts", "../../src/js/utils/dom/parseHtml/parseHtml.ts", "../../src/js/utils/dom/prevent/prevent.ts", "../../src/js/utils/dom/query/query.ts", "../../src/js/utils/dom/queryAll/queryAll.ts", "../../src/js/utils/dom/rect/rect.ts", "../../src/js/utils/dom/remove/remove.ts", "../../src/js/utils/dom/removeClass/removeClass.ts", "../../src/js/utils/dom/unit/unit.ts", "../../src/js/utils/error/assert/assert.ts", "../../src/js/utils/function/nextTick/nextTick.ts", "../../src/js/utils/function/noop/noop.ts", "../../src/js/utils/function/raf/raf.ts", "../../src/js/utils/math/between/between.ts", "../../src/js/utils/math/clamp/clamp.ts", "../../src/js/utils/math/sign/sign.ts", "../../src/js/utils/math/index.ts", "../../src/js/utils/string/format/format.ts", "../../src/js/utils/string/pad/pad.ts", "../../src/js/utils/string/uniqueId/uniqueId.ts", "../../src/js/components/Direction/Direction.ts", "../../src/js/constants/classes.ts", "../../src/js/constants/events.ts", "../../src/js/constructors/EventBus/EventBus.ts", "../../src/js/constructors/EventInterface/EventInterface.ts", "../../src/js/constructors/RequestInterval/RequestInterval.ts", "../../src/js/constructors/State/State.ts", "../../src/js/constructors/Throttle/Throttle.ts", "../../src/js/constants/attributes.ts", "../../src/js/constants/types.ts", "../../src/js/components/Slides/Slide.ts", "../../src/js/components/Drag/constants.ts", "../../src/js/components/Keyboard/Keyboard.ts", "../../src/js/components/LazyLoad/constants.ts", "../../src/js/components/Sync/Sync.ts", "../../src/js/components/Options/Options.ts", "../../src/js/constants/directions.ts", "../../src/js/components/Elements/Elements.ts", "../../src/js/components/Style/Style.ts", "../../src/js/components/Slides/Slides.ts", "../../src/js/components/Clones/Clones.ts", "../../src/js/components/Layout/Layout.ts", "../../src/js/components/Move/Move.ts", "../../src/js/components/Controller/Controller.ts", "../../src/js/components/Arrows/Arrows.ts", "../../src/js/components/Arrows/path.ts", "../../src/js/components/Autoplay/Autoplay.ts", "../../src/js/components/Cover/Cover.ts", "../../src/js/components/Scroll/Scroll.ts", "../../src/js/components/Scroll/constants.ts", "../../src/js/components/Drag/Drag.ts", "../../src/js/components/LazyLoad/LazyLoad.ts", "../../src/js/components/Pagination/Pagination.ts", "../../src/js/components/Wheel/Wheel.ts", "../../src/js/constants/defaults.ts", "../../src/js/constants/i18n.ts", "../../src/js/transitions/Fade/Fade.ts", "../../src/js/transitions/Slide/Slide.ts", "../../src/js/core/Splide/Splide.ts"], "sourcesContent": ["/**\n * The project code.\n *\n * @since 3.0.0\n */\nexport const PROJECT_CODE = 'splide';\n\n/**\n * The data attribute prefix.\n *\n * @since 3.0.0\n */\nexport const DATA_ATTRIBUTE = `data-${ PROJECT_CODE }`;\n", "/**\n * Splide has been just created.\n */\nexport const CREATED = 1;\n\n/**\n * Splide has mounted components.\n */\nexport const MOUNTED = 2;\n\n/**\n * Splide is ready.\n */\nexport const IDLE = 3;\n\n/**\n * Splide is moving.\n */\nexport const MOVING = 4;\n\n/**\n * Splide has been destroyed.\n */\nexport const DESTROYED = 5;\n\n/**\n * The collection of all states.\n *\n * @since 3.0.0\n */\nexport const STATES = {\n  CREATED,\n  MOUNTED,\n  IDLE,\n  MOVING,\n  DESTROYED,\n};\n", "/**\n * Empties the array.\n *\n * @param array - A array to empty.\n */\nexport function empty( array: any[] ): void {\n  array.length = 0;\n}\n", "/**\n * Checks if the given subject is an object or not.\n *\n * @param subject - A subject to check.\n *\n * @return `true` if the subject is an object, or otherwise `false`.\n */\nexport function isObject( subject: unknown ): subject is object {\n  return ! isNull( subject ) && typeof subject === 'object';\n}\n\n/**\n * Checks if the given subject is an array or not.\n *\n * @param subject - A subject to check.\n *\n * @return `true` if the subject is an array, or otherwise `false`.\n */\nexport function isArray<T>( subject: unknown ): subject is T[] {\n  return Array.isArray( subject );\n}\n\n/**\n * Checks if the given subject is a function or not.\n *\n * @param subject - A subject to check.\n *\n * @return `true` if the subject is a function, or otherwise `false`.\n */\nexport function isFunction( subject: unknown ): subject is ( ...args: any[] ) => any {\n  return typeof subject === 'function';\n}\n\n/**\n * Checks if the given subject is a string or not.\n *\n * @param subject - A subject to check.\n *\n * @return `true` if the subject is a string, or otherwise `false`.\n */\nexport function isString( subject: unknown ): subject is string {\n  return typeof subject === 'string';\n}\n\n/**\n * Checks if the given subject is `undefined` or not.\n *\n * @param subject - A subject to check.\n *\n * @return `true` if the subject is `undefined`, or otherwise `false`.\n */\nexport function isUndefined( subject: unknown ): subject is undefined {\n  return typeof subject === 'undefined';\n}\n\n/**\n * Checks if the given subject is `null` or not.\n *\n * @param subject - A subject to check.\n *\n * @return `true` if the subject is `null`, or otherwise `false`.\n */\nexport function isNull( subject: unknown ): subject is null {\n  return subject === null;\n}\n\n/**\n * Checks if the given subject is an HTMLElement or not.\n *\n * @param subject - A subject to check.\n *\n * @return `true` if the subject is an HTMLElement instance, or otherwise `false`.\n */\nexport function isHTMLElement( subject: unknown ): subject is HTMLElement {\n  return subject instanceof HTMLElement;\n}\n\n/**\n * Checks if the given subject is an HTMLButtonElement or not.\n *\n * @param subject - A subject to check.\n *\n * @return `true` if the subject is an HTMLButtonElement, or otherwise `false`.\n */\nexport function isHTMLButtonElement( subject: unknown ): subject is HTMLButtonElement {\n  return subject instanceof HTMLButtonElement;\n}\n", "import { isArray } from '../../type/type';\n\n\n/**\n * Push the provided value to an array if the value is not an array.\n *\n * @param value - A value to push.\n *\n * @return An array containing the value, or the value itself if it is already an array.\n */\nexport function toArray<T>( value: T | T[] ): T[] {\n  return isArray( value ) ? value : [ value ];\n}\n", "import { toArray } from '../toArray/toArray';\n\n\n/**\n * The extended `Array#forEach` method that accepts a single value as an argument.\n *\n * @param values   - A value or values to iterate over.\n * @param iteratee - An iteratee function.\n */\nexport function forEach<T>( values: T | T[], iteratee: ( value: T, index: number, array: T[] ) => void ): void {\n  toArray( values ).forEach( iteratee );\n}\n", "/**\n * Checks if the array includes the value or not.\n * `Array#includes` is not supported by IE.\n *\n * @param array - An array.\n * @param value - A value to search for.\n *\n * @return `true` if the array includes the value, or otherwise `false`.\n */\nexport function includes<T>( array: T[], value: T ): boolean {\n  return array.indexOf( value ) > -1;\n}\n", "import { toArray } from '../toArray/toArray';\n\n\n/**\n * Extended `Array#push()` that accepts an item or an array with items.\n *\n * @param array - An array to push items.\n * @param items - An item or items to push.\n *\n * @return A provided array itself.\n */\nexport function push<T>( array: T[], items: T | T[] ): T[] {\n  array.push( ...toArray( items ) );\n  return array;\n}\n", "export { empty }    from './empty/empty';\nexport { forEach }  from './forEach/forEach';\nexport { includes } from './includes/includes';\nexport { push }     from './push/push';\nexport { toArray }  from './toArray/toArray';\n\nexport const arrayProto = Array.prototype;\n", "import { arrayProto } from '../../array';\r\n\r\n\r\n/**\r\n * The slice method for an array-like object.\r\n *\r\n * @param arrayLike - An array-like object.\r\n * @param start     - Optional. A start index.\r\n * @param end       - Optional. A end index.\r\n *\r\n * @return An array with sliced elements.\r\n */\r\nexport function slice<T>( arrayLike: ArrayLike<T>, start?: number, end?: number ): T[] {\r\n  return arrayProto.slice.call( arrayLike, start, end );\r\n}\r\n", "import { slice } from '../slice/slice';\n\n\n/**\n * The find method for an array or array-like object, works in IE.\n * This method is not performant for a huge array.\n *\n * @param arrayLike - An array-like object.\n * @param predicate - The predicate function to test each element in the object.\n *\n * @return A found value if available, or otherwise `undefined`.\n */\nexport function find<T>(\n  arrayLike: ArrayLike<T>,\n  predicate: ( value: T, index: number, array: T[] ) => any\n): T | undefined {\n  return slice( arrayLike ).filter( predicate )[ 0 ];\n}\n", "import { forEach } from '../../array';\n\n\n/**\n * Toggles the provided class or classes by following the `add` boolean.\n *\n * @param elm     - An element whose classes are toggled.\n * @param classes - A class or class names.\n * @param add     - Whether to add or remove a class.\n */\nexport function toggleClass( elm: Element, classes: string | string[], add: boolean ): void {\n  if ( elm ) {\n    forEach( classes, name => {\n      if ( name ) {\n        elm.classList[ add ? 'add' : 'remove' ]( name );\n      }\n    } );\n  }\n}\n", "import { toggleClass } from '../toggleClass/toggleClass';\n\n\n/**\n * Adds classes to the element.\n *\n * @param elm     - An element to add classes to.\n * @param classes - Classes to add.\n */\nexport function addClass( elm: Element, classes: string | string[] ): void {\n  toggleClass( elm, classes, true );\n}\n", "import { forEach } from '../../array';\n\n\n/**\n * Appends children to the parent element.\n *\n * @param parent   - A parent element.\n * @param children - A child or children to append to the parent.\n */\nexport function append( parent: Element, children: Node | Node[] ): void {\n  forEach( children, parent.appendChild.bind( parent ) );\n}\n", "import { forEach } from '../../array';\n\n\n/**\n * Inserts a node or nodes before the specified reference node.\n *\n * @param nodes - A node or nodes to insert.\n * @param ref   - A reference node.\n */\nexport function before( nodes: Node | Node[], ref: Node ): void {\n  forEach( nodes, node => {\n    const parent = ref.parentNode;\n\n    if ( parent ) {\n      parent.insertBefore( node, ref );\n    }\n  } );\n}\n", "/**\n * Checks if the element can be selected by the provided selector or not.\n *\n * @param elm      - An element to check.\n * @param selector - A selector to test.\n *\n * @return `true` if the selector matches the element, or otherwise `false`.\n */\nexport function matches( elm: Element, selector: string ): boolean {\n  return ( elm[ 'msMatchesSelector' ] || elm.matches ).call( elm, selector );\n}\n", "import { slice } from '../../arrayLike';\nimport { matches } from '../matches/matches';\n\n\n/**\n * Finds children that has the specified tag or class name.\n *\n * @param parent   - A parent element.\n * @param selector - A selector to filter children.\n *\n * @return An array with filtered children.\n */\nexport function children<E extends HTMLElement>( parent: HTMLElement, selector: string ): E[] {\n  return parent ? slice( parent.children ).filter( child => matches( child, selector ) ) as E[] : [];\n}\n", "import { children } from '../children/children';\n\n\n/**\n * Returns a child element that matches the specified tag or class name.\n *\n * @param parent   - A parent element.\n * @param selector - A selector to filter children.\n *\n * @return A matched child element if available, or otherwise `undefined`.\n */\nexport function child<E extends HTMLElement>( parent: HTMLElement, selector?: string ): E | undefined {\n  return selector ? children<E>( parent, selector )[ 0 ] : parent.firstElementChild as E;\n}\n", "/**\n * Iterates over the provided object by own enumerable keys with calling the iteratee function.\n *\n * @param object   - An object to iterate over.\n * @param iteratee - An iteratee function that takes the value and key as arguments.\n *\n * @return A provided object itself.\n */\nexport function forOwn<T extends object>(\n  object: T,\n  iteratee: ( value: T[ keyof T ], key: string ) => boolean | void\n): T {\n  if ( object ) {\n    const keys = Object.keys( object );\n\n    for ( let i = 0; i < keys.length; i++ ) {\n      const key = keys[ i ];\n\n      if ( key !== '__proto__' ) {\n        if ( iteratee( object[ key ], key ) === false ) {\n          break;\n        }\n      }\n    }\n  }\n\n  return object;\n}\n", "import { forOwn } from '../forOwn/forOwn';\n\n\n/**\n * Assign U to T.\n *\n * @typeParam T - An object to assign to.\n * @typeParam U - An object to assign.\n *\n * @return An assigned object type.\n */\nexport type Assign<T, U> = Omit<T, keyof U> & U;\n\nexport function assign<T extends object>( object: T ): T;\n\n// There is a way to type arguments recursively, but these fixed definitions are enough for this project.\nexport function assign<T extends object, U extends object>( object: T, source: U ): Assign<T, U>;\n\nexport function assign<T extends object, U1 extends object, U2 extends object>(\n  object: T, source1: U1, source2: U2\n): Assign<Assign<T, U1>, U2>;\n\nexport function assign<T extends object, U1 extends object, U2 extends object, U3 extends object>(\n  object: T, source1: U1, source2: U2, source3: U3\n): Assign<Assign<Assign<T, U1>, U2>, U3>;\n\n/**\n * Assigns all own enumerable properties of all source objects to the provided object.\n * `undefined` in source objects will be skipped.\n *\n * @param object  - An object to assign properties to.\n * @param sources - Objects to assign properties from.\n *\n * @return An object assigned properties of the sources to.\n */\nexport function assign<T extends object, U extends object>( object: T, ...sources: U[] ): any {\n  sources.forEach( source => {\n    forOwn( source, ( value, key ) => {\n      object[ key ] = source[ key ];\n    } );\n  } );\n\n  return object;\n}\n", "import { isObject } from '../../type/type';\nimport { forOwn } from '../forOwn/forOwn';\n\n\n/**\n * Merges U to T.\n *\n * @typeParam T - An object to merge to.\n * @typeParam U - An object to to.\n *\n * @return An merged object type.\n */\nexport type Merge<T extends object, U extends object> = Omit<T, keyof U> & {\n  [ K in ( keyof T & keyof U ) ]: U[ K ] extends object\n    ? U[ K ] extends any[]\n      ? T[ K ] extends any[]\n        ? Array<T[ K ][ number ] | U[ K ][ number ]>\n        : U[ K ]\n      : T[ K ] extends object\n        ? Merge<T[ K ], U[ K ]> extends infer A ? Cast<A, object> : never\n        : U[ K ]\n    : U[ K ];\n} & Omit<U, keyof T>;\n\ntype Cast<T, U> = T extends U ? T : U;\n\n/**\n * Recursively merges source properties to the object.\n *\n * @param object - An object to merge properties to.\n * @param source - A source object to merge properties from.\n *\n * @return A new object with merged properties.\n */\nexport function merge<T extends object, U extends object>( object: T, source: U ): Merge<T, U> {\n  forOwn( source, ( value, key ) => {\n    object[ key ] = isObject( value ) ? merge( isObject( object[ key ] ) ? object[ key ] : {}, value ) : value;\n  } );\n\n  return object as Merge<T, U>;\n}\n", "import { forEach } from '../../array';\n\n\n/**\n * Removes attributes from the element.\n *\n * @param elm   - An element.\n * @param attrs - An attribute or attributes to remove.\n */\nexport function removeAttribute( elm: Element, attrs: string | string[] ): void {\n  if ( elm ) {\n    forEach( attrs, attr => {\n      elm.removeAttribute( attr );\n    } );\n  }\n}\n", "import { forOwn } from '../../object';\nimport { isNull, isObject } from '../../type/type';\nimport { removeAttribute } from '../removeAttribute/removeAttribute';\n\n\nexport function setAttribute( elm: Element, attr: string, value: string | number | boolean ): void;\nexport function setAttribute( elm: Element, attrs: Record<string, string | number | boolean> ): void;\n\nexport function setAttribute(\n  elm: Element,\n  attrs: string | Record<string, string | number | boolean>,\n  value?: string | number | boolean\n): void {\n  if ( isObject( attrs ) ) {\n    forOwn( attrs, ( value, name ) => {\n      setAttribute( elm, name, value );\n    } );\n  } else {\n    isNull( value ) ? removeAttribute( elm, attrs ) : elm.setAttribute( attrs, String( value ) );\n  }\n}\n", "import { isArray, isString } from '../../type/type';\nimport { addClass } from '../addClass/addClass';\nimport { append } from '../append/append';\nimport { setAttribute } from '../setAttribute/setAttribute';\n\n\n/**\n * Creates a HTML element.\n *\n * @param tag    - A tag name.\n * @param attrs  - Optional. An object with attributes to apply the created element to, or a string with classes.\n * @param parent - Optional. A parent element where the created element is appended.\n */\nexport function create<K extends keyof HTMLElementTagNameMap>(\n  tag: K,\n  attrs?: Record<string, string | number | boolean> | string | string[],\n  parent?: HTMLElement\n): HTMLElementTagNameMap[ K ] {\n  const elm = document.createElement( tag );\n\n  if ( attrs ) {\n    if ( isString( attrs ) || isArray( attrs ) ) {\n      addClass( elm, attrs );\n    } else {\n      setAttribute( elm, attrs );\n    }\n  }\n\n  if ( parent ) {\n    append( parent, elm );\n  }\n\n  return elm;\n}\n", "import { forEach } from '../../array';\nimport { forOwn } from '../../object';\nimport { isArray, isNull, isString } from '../../type/type';\n\n\n/**\n * The union for CSS style properties, such as \"padding\", \"fontSize\", etc.\n *\n * @since 0.1.0\n */\nexport type CSSStyleProperties = Exclude<keyof CSSStyleDeclaration, number>;\n\nexport function style(\n  elms: HTMLElement | HTMLElement[],\n  styles: Record<string, string | number>\n): void;\n\nexport function style<K extends CSSStyleProperties>(\n  elms: HTMLElement,\n  styles: K\n): CSSStyleDeclaration[ K ];\n\nexport function style(\n  elms: HTMLElement,\n  styles: string\n): string;\n\n\n/**\n * Applies inline styles to the provided element by an object literal.\n *\n * @param elms   - An element or elements to apply styles to.\n * @param styles - An object literal with styles.\n */\nexport function style<K extends CSSStyleProperties>(\n  elms: HTMLElement | HTMLElement[],\n  styles: Record<string, string | number> | K\n): CSSStyleDeclaration[ K ] | void {\n  if ( isString( styles ) ) {\n    return isArray( elms ) ? null : getComputedStyle( elms )[ styles ];\n  }\n\n  forOwn( styles, ( value, key ) => {\n    if ( ! isNull( value ) ) {\n      forEach( elms, elm => {\n        if ( elm ) {\n          elm.style[ key ] = `${ value }`;\n        }\n      } );\n    }\n  } );\n}\n", "import { style } from '../style/style';\n\n\n/**\n * Sets the `display` CSS value to the element.\n *\n * @param elm     - An element to set a new value to.\n * @param display - A new `display` value.\n */\nexport function display( elm: HTMLElement, display: string ): void {\n  style( elm, { display } );\n}\n", "/**\n * Returns the specified attribute value.\n *\n * @param elm  - An element.\n * @param attr - An attribute to get.\n */\nexport function getAttribute( elm: Element, attr: string ): string {\n  return elm.getAttribute( attr );\n}\n", "/**\r\n * Checks if the element contains the specified class or not.\r\n *\r\n * @param elm       - An element to check.\r\n * @param className - A class name that may be contained by the element.\r\n *\r\n * @return `true` if the element contains the class, or otherwise `false`.\r\n */\r\nexport function hasClass( elm: Element, className: string ): boolean {\r\n  return elm && elm.classList.contains( className );\r\n}\r\n", "import { child } from '../child/child';\n\n\n/**\n * Parses the provided HTML string and returns the first element.\n *\n * @param html - An HTML string to parse.\n *\n * @return An Element on success, or otherwise `undefined`.\n */\nexport function parseHtml<E extends HTMLElement>( html: string ): E | undefined {\n  return child<E>( new DOMParser().parseFromString( html, 'text/html' ).body );\n}\n", "/**\n * Call the `preventDefault()` of the provided event.\n *\n * @param e               - An Event object.\n * @param stopPropagation - Optional. Whether to stop the event propagation or not.\n */\nexport function prevent( e: Event, stopPropagation?: boolean ): void {\n  e.preventDefault();\n\n  if ( stopPropagation ) {\n    e.stopPropagation();\n    e.stopImmediatePropagation();\n  }\n}\n", "/**\n * Returns an element that matches the provided selector.\n *\n * @param parent   - A parent element to start searching from.\n * @param selector - A selector to query.\n *\n * @return A found element or `null`.\n */\nexport function query<E extends Element = Element>( parent: Element | Document, selector: string ): E | null {\n  return parent && parent.querySelector( selector );\n}\n", "import { slice } from '../../arrayLike';\n\n\n/**\n * Returns elements that match the provided selector.\n *\n * @param parent   - A parent element to start searching from.\n * @param selector - A selector to query.\n *\n * @return An array with matched elements.\n */\nexport function queryAll<E extends Element = Element>( parent: Element | Document, selector: string ): E[] {\n  return slice<E>( parent.querySelectorAll( selector ) );\n}\n", "/**\n * Returns a DOMRect object of the provided element.\n *\n * @param target - An element.\n */\nexport function rect( target: Element ): DOMRect {\n  return target.getBoundingClientRect();\n}\n", "import { forEach } from '../../array';\n\n\n/**\n * Removes the provided node from its parent.\n *\n * @param nodes - A node or nodes to remove.\n */\nexport function remove( nodes: Node | Node[] ): void {\n  forEach( nodes, node => {\n    if ( node && node.parentNode ) {\n      node.parentNode.removeChild( node );\n    }\n  } );\n}\n", "import { toggleClass } from '../toggleClass/toggleClass';\r\n\r\n\r\n/**\r\n * Removes classes from the element.\r\n *\r\n * @param elm     - An element to remove classes from.\r\n * @param classes - Classes to remove.\r\n */\r\nexport function removeClass( elm: Element, classes: string | string[] ): void {\r\n  toggleClass( elm, classes, false );\r\n}\r\n", "import { isString } from '../../type/type';\n\n\n/**\n * Appends `px` to the provided number.\n * If the value is already string, just returns it.\n *\n * @param value - A value to append `px` to.\n *\n * @return A string with the CSS unit.\n */\nexport function unit( value: number | string ): string {\n  return isString( value ) ? value : value ? `${ value }px` : '';\n}\n", "import { PROJECT_CODE } from '../../../constants/project';\r\n\r\n\r\n/**\r\n * Throws an error if the provided condition is falsy.\r\n *\r\n * @param condition - If falsy, an error is thrown.\r\n * @param message   - Optional. A message to display.\r\n */\r\nexport function assert( condition: any, message = '' ): void {\r\n  if ( ! condition ) {\r\n    throw new Error( `[${ PROJECT_CODE }] ${ message }` );\r\n  }\r\n}\r\n", "import { AnyFunction } from '../../../types';\n\n\n/**\n * Invokes the callback on the next tick.\n *\n * @param callback - A callback function.\n */\nexport function nextTick( callback: AnyFunction ): void {\n  setTimeout( callback );\n}\n", "/**\n * No operation.\n */\nexport const noop = (): void => {}; // eslint-disable-line no-empty-function, @typescript-eslint/no-empty-function\n", "/**\n * The arias of `window.requestAnimationFrame()`.\n */\nexport function raf( func: FrameRequestCallback ): number {\n  return requestAnimationFrame( func );\n}\n", "/**\r\n * Checks if the subject number is between `minOrMax` and `maxOrMin`.\r\n *\r\n * @param number    - A subject number to check.\r\n * @param minOrMax  - A min or max number.\r\n * @param maxOrMin  - A max or min number.\r\n * @param exclusive - Optional. Whether to exclude `x` or `y`.\r\n */\r\nexport function between( number: number, minOrMax: number, maxOrMin: number, exclusive?: boolean ): boolean {\r\n  const min = Math.min( minOrMax, maxOrMin );\r\n  const max = Math.max( minOrMax, maxOrMin );\r\n  return exclusive ? min < number && number < max : min <= number && number <= max;\r\n}\r\n", "const { max, min } = Math;\r\n\r\n/**\r\n * Clamps a number.\r\n *\r\n * @param number - A subject number to check.\r\n * @param x      - A min or max number.\r\n * @param y      - A min or max number.\r\n */\r\nexport function clamp( number: number, x: number, y: number ): number {\r\n  const minimum = min( x, y );\r\n  const maximum = max( x, y );\r\n  return min( max( minimum, number ), maximum );\r\n}\r\n", "/**\n * Returns the sign of the provided number.\n *\n * @param x - A number.\n *\n * @return `1` for positive numbers, `-1` for negative numbers, or `0` for `0`.\n */\nexport function sign( x: number ): number {\n  return +( x > 0 ) - +( x < 0 );\n}\n", "export { between } from './between/between';\nexport { clamp }   from './clamp/clamp';\nexport { sign }    from './sign/sign';\n\nexport const { min, max, floor, ceil, abs, round } = Math;\n", "import { forEach } from '../../array';\n\n\n/**\n * Formats a string.\n *\n * @param string       - A string to format.\n * @param replacements - A replacement or replacements.\n *\n * @return A formatted string.\n */\nexport function format( string: string, replacements: string | number | Array<string | number> ): string {\n  forEach( replacements, replacement => {\n    string = string.replace( '%s', `${ replacement }` );\n  } );\n\n  return string;\n}\n", "/**\n * Pads the number with 0.\n *\n * @param number - A number to pad.\n *\n * @return string - Padded number.\n */\nexport function pad( number: number ): string {\n  return number < 10 ? `0${ number }` : `${ number }`;\n}\n", "import { pad } from '../pad/pad';\n\n\n/**\n * Stores unique IDs.\n *\n * @since 3.0.0\n */\nconst ids: Record<string, number> = {};\n\n/**\n * Returns a sequential unique ID as \"{ prefix }-{ number }\".\n *\n * @param prefix - A prefix for the ID.\n */\nexport function uniqueId( prefix: string ): string {\n  return `${ prefix }${ pad( ( ids[ prefix ] = ( ids[ prefix ] || 0 ) + 1 ) ) }`;\n}\n", "import { RTL, TTB } from '../../constants/directions';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\n\n\n/**\n * The interface for the Direction component.\n *\n * @since 3.0.0\n */\nexport interface DirectionComponent extends BaseComponent {\n  resolve( prop: string, axisOnly?: boolean ): string;\n  orient( value: number ): number;\n}\n\n/**\n * The translation map for directions.\n *\n * @since 3.0.0\n */\nexport const ORIENTATION_MAP = {\n  marginRight : [ 'marginBottom', 'marginLeft' ],\n  width       : [ 'height' ],\n  autoWidth   : [ 'autoHeight' ],\n  fixedWidth  : [ 'fixedHeight' ],\n  paddingLeft : [ 'paddingTop', 'paddingRight' ],\n  paddingRight: [ 'paddingBottom', 'paddingLeft' ],\n  left        : [ 'top', 'right' ],\n  right       : [ 'bottom', 'left' ],\n  x           : [ 'y' ],\n  X           : [ 'Y' ],\n  pageX       : [ 'pageY' ],\n  ArrowLeft   : [ 'ArrowUp', 'ArrowRight' ],\n  ArrowRight  : [ 'ArrowDown', 'ArrowLeft' ],\n};\n\n/**\n * The component that absorbs the difference among directions.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Direction component object.\n */\nexport function Direction( Splide: Splide, Components: Components, options: Options ): DirectionComponent {\n  /**\n   * Resolves the provided property name.\n   *\n   * @param prop     - A property name to translate.\n   * @param axisOnly - Optional. If `ture`, returns the same property for LTR and RTL.\n   */\n  function resolve( prop: string, axisOnly?: boolean ): string {\n    const { direction } = options;\n    const index = direction === RTL && ! axisOnly ? 1 : direction === TTB ? 0 : -1;\n    return ORIENTATION_MAP[ prop ][ index ] || prop;\n  }\n\n  /**\n   * Orients the value towards the current direction.\n   *\n   * @param value - A value to orient.\n   *\n   * @return The oriented value.\n   */\n  function orient( value: number ): number {\n    return value * ( options.direction === RTL ? 1 : -1 );\n  }\n\n  return {\n    resolve,\n    orient,\n  };\n}\n", "import { PROJECT_CODE } from './project';\n\n\nexport const CLASS_ROOT            = PROJECT_CODE;\nexport const CLASS_SLIDER          = `${ PROJECT_CODE }__slider`;\nexport const CLASS_TRACK           = `${ PROJECT_CODE }__track`;\nexport const CLASS_LIST            = `${ PROJECT_CODE }__list`;\nexport const CLASS_SLIDE           = `${ PROJECT_CODE }__slide`;\nexport const CLASS_CLONE           = `${ CLASS_SLIDE }--clone`;\nexport const CLASS_CONTAINER       = `${ CLASS_SLIDE }__container`;\nexport const CLASS_ARROWS          = `${ PROJECT_CODE }__arrows`;\nexport const CLASS_ARROW           = `${ PROJECT_CODE }__arrow`;\nexport const CLASS_ARROW_PREV      = `${ CLASS_ARROW }--prev`;\nexport const CLASS_ARROW_NEXT      = `${ CLASS_ARROW }--next`;\nexport const CLASS_PAGINATION      = `${ PROJECT_CODE }__pagination`;\nexport const CLASS_PAGINATION_PAGE = `${ CLASS_PAGINATION }__page`;\nexport const CLASS_PROGRESS        = `${ PROJECT_CODE }__progress`;\nexport const CLASS_PROGRESS_BAR    = `${ CLASS_PROGRESS }__bar`;\nexport const CLASS_AUTOPLAY        = `${ PROJECT_CODE }__autoplay`;\nexport const CLASS_PLAY            = `${ PROJECT_CODE }__play`;\nexport const CLASS_PAUSE           = `${ PROJECT_CODE }__pause`;\nexport const CLASS_SPINNER         = `${ PROJECT_CODE }__spinner`;\nexport const CLASS_INITIALIZED     = 'is-initialized';\nexport const CLASS_ACTIVE          = 'is-active';\nexport const CLASS_PREV            = 'is-prev';\nexport const CLASS_NEXT            = 'is-next';\nexport const CLASS_VISIBLE         = 'is-visible';\nexport const CLASS_LOADING         = 'is-loading';\n\n/**\n * The array with all status classes.\n *\n * @since 3.0.0\n */\nexport const STATUS_CLASSES = [ CLASS_ACTIVE, CLASS_VISIBLE, CLASS_PREV, CLASS_NEXT, CLASS_LOADING ];\n\n/**\n * The collection of classes for elements that Splide dynamically creates.\n *\n * @since 3.0.0\n */\nexport const CLASSES = {\n  slide     : CLASS_SLIDE,\n  clone     : CLASS_CLONE,\n  arrows    : CLASS_ARROWS,\n  arrow     : CLASS_ARROW,\n  prev      : CLASS_ARROW_PREV,\n  next      : CLASS_ARROW_NEXT,\n  pagination: CLASS_PAGINATION,\n  page      : CLASS_PAGINATION_PAGE,\n  spinner   : CLASS_SPINNER,\n};\n", "export const EVENT_MOUNTED            = 'mounted';\nexport const EVENT_READY              = 'ready';\nexport const EVENT_MOVE               = 'move';\nexport const EVENT_MOVED              = 'moved';\nexport const EVENT_CLICK              = 'click';\nexport const EVENT_ACTIVE             = 'active';\nexport const EVENT_INACTIVE           = 'inactive';\nexport const EVENT_VISIBLE            = 'visible';\nexport const EVENT_HIDDEN             = 'hidden';\nexport const EVENT_SLIDE_KEYDOWN      = 'slide:keydown';\nexport const EVENT_REFRESH            = 'refresh';\nexport const EVENT_UPDATED            = 'undated';\nexport const EVENT_RESIZE             = 'resize';\nexport const EVENT_RESIZED            = 'resized';\nexport const EVENT_DRAG               = 'drag';\nexport const EVENT_DRAGGING           = 'dragging';\nexport const EVENT_DRAGGED            = 'dragged';\nexport const EVENT_SCROLL             = 'scroll';\nexport const EVENT_SCROLLED           = 'scrolled';\nexport const EVENT_DESTROY            = 'destroy';\nexport const EVENT_ARROWS_MOUNTED     = 'arrows:mounted';\nexport const EVENT_ARROWS_UPDATED     = 'arrows:updated';\nexport const EVENT_PAGINATION_MOUNTED = 'pagination:mounted';\nexport const EVENT_PAGINATION_PAGE    = 'pagination:page';\nexport const EVENT_PAGINATION_UPDATED = 'pagination:updated';\nexport const EVENT_NAVIGATION_MOUNTED = 'navigation:mounted';\nexport const EVENT_AUTOPLAY_PLAY      = 'autoplay:play';\nexport const EVENT_AUTOPLAY_PLAYING   = 'autoplay:playing';\nexport const EVENT_AUTOPLAY_PAUSE     = 'autoplay:pause';\nexport const EVENT_LAZYLOAD_LOADED    = 'lazyload:loaded';\n\n", "import { AnyFunction } from '../../types';\nimport { forOwn, push, slice, toArray } from '../../utils';\n\n\n/**\n * The interface for the EventBus instance.\n *\n * @since 3.0.0\n */\nexport interface EventBusObject {\n  on( events: string | string[], callback: EventBusCallback, key?: object, priority?: number ): void;\n  off( events: string | string[], key?: object ): void;\n  offBy( key: object ): void;\n  emit( event: string, ...args: any[] ): void;\n  destroy(): void;\n}\n\n/**\n * The interface for each event handler object.\n *\n * @since 3.0.0\n */\nexport interface EventHandler {\n  event: string;\n  callback: AnyFunction;\n  namespace: string;\n  priority: number;\n  key?: object;\n}\n\n/**\n * The type for a callback function of the EventBus.\n *\n * @since 3.0.0\n */\nexport type EventBusCallback = AnyFunction;\n\n/**\n * The constructor to provided a simple event system.\n *\n * @since 3.0.0\n *\n * @return An EventBus object.\n */\nexport function EventBus(): EventBusObject {\n  /**\n   * The collection of registered handlers.\n   */\n  let handlers: Record<string, EventHandler[]> = {};\n\n  /**\n   * Registers an event handler.\n   *\n   * @param events   - An event name or names separated by spaces. Use a dot(.) to add a namespace.\n   * @param callback - A callback function to register.\n   * @param key      - Optional. An object for an identifier of the handler.\n   * @param priority - Optional. A priority number for the order in which the callbacks are invoked.\n   *                   Lower numbers correspond with earlier execution. The default value is 10.\n   */\n  function on( events: string | string[], callback: EventBusCallback, key?: object, priority = 10 ): void {\n    forEachEvent( events, ( event, namespace ) => {\n      handlers[ event ] = handlers[ event ] || [];\n      push( handlers[ event ], { event, callback, namespace, priority, key } )\n        .sort( ( handler1, handler2 ) => handler1.priority - handler2.priority );\n    } );\n  }\n\n  /**\n   * Removes event handlers registered by `on()`.\n   * If only the event name is provided, all handlers that associate with the event are removed.\n   * If the event name and namespace are specified, handlers that associate with the event and namespace are removed.\n   *\n   * @param events - An event name or names separated by spaces. Use a dot(.) to add a namespace.\n   * @param key    - Optional. An object for an identifier of the handler.\n   */\n  function off( events: string | string[], key?: object ): void {\n    forEachEvent( events, ( event, namespace ) => {\n      const eventHandlers = handlers[ event ];\n\n      handlers[ event ] = eventHandlers && eventHandlers.filter( handler => {\n        return handler.key ? handler.key !== key : handler.namespace !== namespace;\n      } );\n    } );\n  }\n\n  /**\n   * Removes all handlers locked by the specified key.\n   *\n   * @param key - A key.\n   */\n  function offBy( key: object ): void {\n    forOwn( handlers, ( eventHandlers, event ) => {\n      off( event, key );\n    } );\n  }\n\n  /**\n   * Triggers callback functions.\n   * This accepts additional arguments and passes them to callbacks.\n   *\n   * @param event - An event name.\n   */\n  function emit( event: string ): void {\n    ( handlers[ event ] || [] ).forEach( handler => {\n      // eslint-disable-next-line prefer-rest-params, prefer-spread\n      handler.callback.apply( handler, slice( arguments, 1 ) );\n    } );\n  }\n\n  /**\n   * Removes all handlers.\n   */\n  function destroy(): void {\n    handlers = {};\n  }\n\n  /**\n   * Parses provided events and iterates over them.\n   *\n   * @param events   - An event or events.\n   * @param iteratee - An iteratee function.\n   */\n  function forEachEvent( events: string | string[], iteratee: ( event: string, namespace: string ) => void ): void {\n    toArray( events ).join( ' ' ).split( ' ' ).forEach( eventNS => {\n      const fragments = eventNS.split( '.' );\n      iteratee( fragments[ 0 ], fragments[ 1 ] );\n    } );\n  }\n\n  return {\n    on,\n    off,\n    offBy,\n    emit,\n    destroy,\n  };\n}\n", "import { EVENT_DESTROY } from '../../constants/events';\nimport { Splide } from '../../core/Splide/Splide';\nimport { AnyFunction } from '../../types';\nimport { forEach } from '../../utils';\nimport { EventBusCallback } from '../EventBus/EventBus';\n\n\n/**\n * The interface for the EventInterface object.\n *\n * @since 3.0.0\n */\nexport interface EventInterfaceObject {\n  on( events: string | string[], callback: EventBusCallback, priority?: number ): void;\n  off( events: string | string[] ): void;\n  emit( event: string, ...args: any[] ): void;\n  bind(\n    target: Element | Window | Document | Array<Element | Window | Document>,\n    events: string,\n    callback: AnyFunction,\n    options?: AddEventListenerOptions\n  ): void\n  unbind( target: Element | Window | Document | Array<Element | Window | Document>, events: string ): void;\n  destroy(): void;\n}\n\n/**\n * The type for event targets.\n *\n * @since 3.0.0\n */\ntype EventTarget = Element | Window | Document;\n\n/**\n * The function that provides interface for internal and native events.\n *\n * @since 3.0.0\n *\n * @param Splide - A Splide instance.\n *\n * @return A collection of interface functions.\n */\nexport function EventInterface( Splide: Splide ): EventInterfaceObject {\n  /**\n   * Holds the event object.\n   */\n  const { event } = Splide;\n\n  /**\n   * The key for events.\n   */\n  const key = {};\n\n  /**\n   * Stores all handlers that listen to native events.\n   */\n  let listeners: [ EventTarget, string, AnyFunction, AddEventListenerOptions? ][] = [];\n\n  /**\n   * Registers an event handler with an unique key.\n   * It can only be removed by `off()` method below.\n   *\n   * @param events   - An event name or names separated by spaces. Use a dot(.) to add a namespace.\n   * @param callback - A callback function to register.\n   * @param priority - Optional. A priority number for the order in which the callbacks are invoked.\n   *                   Lower numbers correspond with earlier execution. The default value is 10.\n   */\n  function on( events: string | string[], callback: EventBusCallback, priority?: number ): void {\n    event.on( events, callback, key, priority );\n  }\n\n  /**\n   * Removes event handlers registered by `on()`.\n   *\n   * @param events - An event name or names separated by spaces. Use a dot(.) to add a namespace.\n   */\n  function off( events: string | string[] ): void {\n    event.off( events, key );\n  }\n\n  /**\n   * Listens to native events.\n   * Splide#destory() will remove all registered listeners.\n   *\n   * @param targets  - A target element, the window object or the document object.\n   * @param events   - An event or events to listen to.\n   * @param callback - A callback function.\n   * @param options  - Optional. The options to pass to the `addEventListener` function.\n   */\n  function bind(\n    targets: EventTarget | EventTarget[],\n    events: string,\n    callback: AnyFunction,\n    options?: AddEventListenerOptions\n  ): void {\n    forEachEvent( targets, events, ( target, event ) => {\n      listeners.push( [ target, event, callback, options ] );\n      target.addEventListener( event, callback, options );\n    } );\n  }\n\n  /**\n   * Removes the event handler.\n   *\n   * @param targets - A target element, the window object or the document object.\n   * @param events  - An event name or names to remove.\n   */\n  function unbind( targets: EventTarget | EventTarget[], events: string ): void {\n    forEachEvent( targets, events, ( target, event ) => {\n      listeners = listeners.filter( listener => {\n        if ( listener[ 0 ] === target && listener[ 1 ] === event ) {\n          target.removeEventListener( event, listener[ 2 ], listener[ 3 ] );\n          return false;\n        }\n\n        return true;\n      } );\n    } );\n  }\n\n  /**\n   * Iterates over each target and event.\n   *\n   * @param targets  - A target element, the window object or the document object.\n   * @param events   - An event name or names.\n   * @param iteratee - An iteratee function.\n   */\n  function forEachEvent(\n    targets: EventTarget | EventTarget[],\n    events: string,\n    iteratee: ( target: EventTarget, event: string ) => void\n  ): void {\n    forEach( targets, target => {\n      if ( target ) {\n        events.split( ' ' ).forEach( iteratee.bind( null, target ) );\n      }\n    } );\n  }\n\n  /**\n   * Removes all listeners.\n   */\n  function destroy(): void {\n    listeners = listeners.filter( data => unbind( data[ 0 ], data[ 1 ] ) );\n    event.offBy( key );\n  }\n\n  /**\n   * Invokes destroy when the slider is destroyed.\n   */\n  event.on( EVENT_DESTROY, destroy, key );\n\n  return {\n    on,\n    off,\n    emit: event.emit,\n    bind,\n    unbind,\n    destroy,\n  };\n}\n", "import { raf } from '../../utils';\n\n\n/**\n * The interface for the returning value of the RequestInterval.\n *\n * @since 3.0.0\n */\nexport interface RequestIntervalInterface {\n  start( resume?: boolean ): void;\n  pause(): void;\n  rewind(): void;\n  cancel(): void;\n  isPaused(): boolean;\n}\n\n/**\n * Requests interval like the native `setInterval()` with using `requestAnimationFrame`.\n *\n * @since 3.0.0\n *\n * @param interval   - The interval duration in milliseconds.\n * @param onInterval - The callback fired on every interval.\n * @param onUpdate   - Optional. Called on every animation frame, taking the progress rate.\n * @param limit      - Optional. Limits the number of interval.\n */\nexport function RequestInterval(\n  interval: number,\n  onInterval: () => void,\n  onUpdate?: ( rate: number ) => void,\n  limit?: number\n): RequestIntervalInterface {\n  const { now } = Date;\n\n  /**\n   * The time when the interval starts.\n   */\n  let startTime: number;\n\n  /**\n   * The current progress rate.\n   */\n  let rate = 0;\n\n  /**\n   * The animation frame ID.\n   */\n  let id: number;\n\n  /**\n   * Indicates whether the interval is currently paused or not.\n   */\n  let paused = true;\n\n  /**\n   * The loop count. This only works when the `limit` argument is provided.\n   */\n  let count = 0;\n\n  /**\n   * The update function called on every animation frame.\n   */\n  function update(): void {\n    if ( ! paused ) {\n      const elapsed = now() - startTime;\n\n      if ( elapsed >= interval ) {\n        rate      = 1;\n        startTime = now();\n      } else {\n        rate = elapsed / interval;\n      }\n\n      if ( onUpdate ) {\n        onUpdate( rate );\n      }\n\n      if ( rate === 1 ) {\n        onInterval();\n\n        if ( limit && ++count >= limit ) {\n          pause();\n          return;\n        }\n      }\n\n      raf( update );\n    }\n  }\n\n  /**\n   * Starts the interval.\n   *\n   * @param resume - Optional. Whether to resume the paused progress or not.\n   */\n  function start( resume?: boolean ): void {\n    ! resume && cancel();\n    startTime = now() - ( resume ? rate * interval : 0 );\n    paused    = false;\n    raf( update );\n  }\n\n  /**\n   * Pauses the interval.\n   */\n  function pause(): void {\n    paused = true;\n  }\n\n  /**\n   * Rewinds the current progress.\n   */\n  function rewind(): void {\n    startTime = now();\n    rate      = 0;\n\n    if ( onUpdate ) {\n      onUpdate( rate );\n    }\n  }\n\n  /**\n   * Cancels the interval.\n   */\n  function cancel() {\n    cancelAnimationFrame( id );\n    rate   = 0;\n    id     = 0;\n    paused = true;\n  }\n\n  /**\n   * Checks if the interval is paused or not.\n   *\n   * @return `true` if the interval is paused, or otherwise `false`.\n   */\n  function isPaused(): boolean {\n    return paused;\n  }\n\n  return {\n    start,\n    rewind,\n    pause,\n    cancel,\n    isPaused,\n  };\n}\n", "import { includes, toArray } from '../../utils';\n\n\n/**\n * The interface for the State object.\n *\n * @since 3.0.0\n */\nexport interface StateObject {\n  set( state: number ): void;\n  is( states: number | number[] ): boolean;\n}\n\n/**\n * The function providing a super simple state system.\n *\n * @param initialState - Specifies the initial state.\n */\nexport function State( initialState: number ): StateObject {\n  /**\n   * The current state.\n   */\n  let state = initialState;\n\n  /**\n   * Sets a new state.\n   *\n   * @param value - A new state value.\n   */\n  function set( value: number ): void {\n    state = value;\n  }\n\n  /**\n   * Checks if the current state matches the provided one.\n   *\n   * @param states - A state to check.\n   *\n   * @return `true` if the current state is the provided one.\n   */\n  function is( states: number | number[] ): boolean {\n    return includes( toArray( states ), state );\n  }\n\n  return { set, is };\n}\n", "import { AnyFunction } from '../../types';\nimport { RequestInterval, RequestIntervalInterface } from '../RequestInterval/RequestInterval';\n\n\n/**\n * The interface for the returning value of the RequestInterval.\n *\n * @since 3.0.0\n */\nexport interface ThrottleInstance<F extends AnyFunction> extends Function {\n  ( ...args: Parameters<F> ): void;\n}\n\n/**\n * Returns the throttled function.\n *\n * @param func     - A function to throttle.\n * @param duration - Optional. Throttle duration in milliseconds.\n *\n * @return A throttled function.\n */\nexport function Throttle<F extends AnyFunction>(\n  func: F,\n  duration?: number\n): ThrottleInstance<F> {\n  let interval: RequestIntervalInterface;\n\n  function throttled( this: ThisParameterType<F> ): void {\n    if ( ! interval ) {\n      interval = RequestInterval( duration || 0, () => {\n        // eslint-disable-next-line prefer-rest-params\n        func.apply( this, arguments );\n        interval = null;\n      }, null, 1 );\n\n      interval.start();\n    }\n  }\n\n  return throttled;\n}\n", "export const ROLE           = 'role';\nexport const ARIA_CONTROLS  = 'aria-controls';\nexport const ARIA_CURRENT   = 'aria-current';\nexport const ARIA_LABEL     = 'aria-label';\nexport const ARIA_HIDDEN    = 'aria-hidden';\nexport const TAB_INDEX      = 'tabindex';\nexport const DISABLED       = 'disabled';\n\n/**\n * The array with all attributes.\n *\n * @since 3.0.0\n */\nexport const ALL_ATTRIBUTES = [\n  ROL<PERSON>,\n  ARIA_CONTROLS,\n  ARIA_CURRENT,\n  ARIA_LABEL,\n  ARIA_HIDDEN,\n  TAB_INDEX,\n  DISABLED,\n];\n", "/**\n * The type for the regular slider.\n *\n * @since 3.0.0\n */\nexport const SLIDE = 'slide';\n\n/**\n * The type for the carousel slider.\n *\n * @since 3.0.0\n */\nexport const LOOP = 'loop';\n\n/**\n * The type for the fade slider that can not have multiple slides in a page.\n *\n * @since 3.0.0\n */\nexport const FADE = 'fade';\n", "import {\n  ALL_ATTRIBUTES,\n  ARIA_CONTROLS,\n  ARIA_CURRENT,\n  ARIA_HIDDEN,\n  ARIA_LABEL,\n  ROLE,\n  TAB_INDEX,\n} from '../../constants/attributes';\nimport {\n  CLASS_ACTIVE,\n  CLASS_CONTAINER,\n  CLASS_NEXT,\n  CLASS_PREV,\n  CLASS_VISIBLE,\n  STATUS_CLASSES,\n} from '../../constants/classes';\nimport {\n  EVENT_ACTIVE,\n  EVENT_CLICK,\n  EVENT_HIDDEN,\n  EVENT_INACTIVE,\n  EVENT_MOUNTED,\n  EVENT_MOVE,\n  EVENT_MOVED,\n  EVENT_RESIZED,\n  EVENT_SCROLLED,\n  EVENT_SLIDE_KEYDOWN,\n  EVENT_UPDATED,\n  EVENT_VISIBLE,\n} from '../../constants/events';\nimport { FADE, SLIDE } from '../../constants/types';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent } from '../../types';\nimport {\n  abs,\n  ceil,\n  child,\n  floor,\n  format,\n  hasClass,\n  isHTMLButtonElement,\n  min,\n  pad,\n  rect,\n  removeAttribute,\n  removeClass,\n  setAttribute,\n  toggleClass,\n} from '../../utils';\n\n\n/**\n * The interface for the Slide sub component.\n *\n * @since 3.0.0\n */\nexport interface  SlideComponent extends BaseComponent {\n  index: number;\n  slideIndex: number;\n  slide: HTMLElement;\n  container: HTMLElement;\n  isClone: boolean;\n  rule( prop: string, value: string | number, useContainer?: boolean ): void\n  isWithin( from: number, distance: number ): boolean;\n}\n\n/**\n * The sub component for managing each slide.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param index      - A slide index.\n * @param slideIndex - A slide index for clones. This must be `-1` if the slide is not clone.\n * @param slide      - A slide element.\n *\n * @return A Slide sub component.\n */\nexport function Slide( Splide: Splide, index: number, slideIndex: number, slide: HTMLElement ): SlideComponent {\n  const { on, emit, bind, destroy: destroyEvents } = EventInterface( Splide );\n  const { Components, root, options } = Splide;\n  const { isNavigation, updateOnMove } = options;\n  const { resolve } = Components.Direction;\n  const isClone   = slideIndex > -1;\n  const container = child( slide, `.${ CLASS_CONTAINER }` );\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount( this: SlideComponent ): void {\n    init();\n\n    bind( slide, 'click keydown', e => {\n      emit( e.type === 'click' ? EVENT_CLICK : EVENT_SLIDE_KEYDOWN, this, e );\n    } );\n\n    on( EVENT_MOUNTED, onMounted.bind( this ) );\n  }\n\n  /**\n   * Called after all components are mounted.\n   * Updating the status on mount is too early to notify other components of the active slide.\n   */\n  function onMounted( this: SlideComponent ): void {\n    const boundUpdate = update.bind( this );\n    boundUpdate();\n    on( [ EVENT_MOVED, EVENT_UPDATED, EVENT_RESIZED, EVENT_SCROLLED ], boundUpdate );\n\n    if ( updateOnMove ) {\n      on( EVENT_MOVE, onMove.bind( this ) );\n    }\n  }\n\n  /**\n   * If the `updateOnMove` option is `true`, called when the slider starts moving.\n   *\n   * @param next - A next index.\n   * @param prev - A previous index.\n   * @param dest - A destination index.\n   */\n  function onMove( this: SlideComponent, next: number, prev: number, dest: number ): void {\n    if ( dest === index ) {\n      updateActivity.call( this, true );\n    }\n\n    update.call( this );\n  }\n\n  /**\n   * Initializes the component.\n   */\n  function init(): void {\n    if ( ! isClone ) {\n      slide.id = `${ root.id }-slide${ pad( index + 1 ) }`;\n    }\n\n    if ( isNavigation ) {\n      if ( ! isHTMLButtonElement( slide ) ) {\n        setAttribute( slide, ROLE, 'button' );\n      }\n\n      const idx      = isClone ? slideIndex : index;\n      const label    = format( options.i18n.slideX, idx + 1 );\n      const controls = Splide.splides.map( splide => splide.root.id ).join( ' ' );\n\n      setAttribute( slide, ARIA_LABEL, label );\n      setAttribute( slide, ARIA_CONTROLS, controls );\n    }\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy(): void {\n    destroyEvents();\n    removeClass( slide, STATUS_CLASSES );\n    removeAttribute( slide, ALL_ATTRIBUTES );\n  }\n\n  /**\n   * Updates attribute and classes of the slide.\n   */\n  function update( this: SlideComponent ): void {\n    const { index: currIndex } = Splide;\n\n    updateActivity.call( this, isActive() );\n    updateVisibility.call( this, isVisible() );\n\n    toggleClass( slide, CLASS_PREV, index === currIndex - 1 );\n    toggleClass( slide, CLASS_NEXT, index === currIndex + 1 );\n  }\n\n  /**\n   * Updates the status related with activity.\n   *\n   * @param active - Set `true` if the slide is active.\n   */\n  function updateActivity( this: SlideComponent, active: boolean ): void {\n    if ( active !== hasClass( slide, CLASS_ACTIVE ) ) {\n      toggleClass( slide, CLASS_ACTIVE, active );\n\n      if ( isNavigation ) {\n        setAttribute( slide, ARIA_CURRENT, active || null );\n      }\n\n      emit( active ? EVENT_ACTIVE : EVENT_INACTIVE, this );\n    }\n  }\n\n  /**\n   * Updates the status related with visibility.\n   *\n   * @param visible - Set `true` if the slide is visible.\n   */\n  function updateVisibility( this: SlideComponent, visible: boolean ): void {\n    setAttribute( slide, ARIA_HIDDEN, ! visible || null );\n    setAttribute( slide, TAB_INDEX, visible && options.slideFocus ? 0 : null );\n\n    if ( visible !== hasClass( slide, CLASS_VISIBLE ) ) {\n      toggleClass( slide, CLASS_VISIBLE, visible );\n      emit( visible ? EVENT_VISIBLE : EVENT_HIDDEN, this );\n    }\n  }\n\n\n  /**\n   * Adds a CSS rule to the slider or the container.\n   *\n   * @param prop         - A property name.\n   * @param value        - A CSS value to add.\n   * @param useContainer - Optional. Determines whether to apply the rule to the container or not.\n   */\n  function rule( prop: string, value: string | number, useContainer?: boolean ): void {\n    const selector = `#${ slide.id }${ container && useContainer ? ` > .${ CLASS_CONTAINER }` : '' }`;\n    Components.Style.rule( selector, prop, value );\n  }\n\n  /**\n   * Checks if the slide is active or not.\n   *\n   * @return `true` if the slide is active.\n   */\n  function isActive(): boolean {\n    return Splide.index === index;\n  }\n\n  /**\n   * Checks if the slide is visible or not.\n   */\n  function isVisible(): boolean {\n    if ( Splide.is( FADE ) ) {\n      return isActive();\n    }\n\n    const trackRect = rect( Components.Elements.track );\n    const slideRect = rect( slide );\n    const left      = resolve( 'left' );\n    const right     = resolve( 'right' );\n\n    return floor( trackRect[ left ] ) <= slideRect[ left ] && slideRect[ right ] <= ceil( trackRect[ right ] );\n  }\n\n  /**\n   * Calculates how far this slide is from another slide and\n   * returns `true` if the distance is within the given number.\n   *\n   * @param from     - An index of a base slide.\n   * @param distance - `true` if the slide is within this number.\n   *\n   * @return `true` if the slide is within the `distance` from the base slide, or otherwise `false`.\n   */\n  function isWithin( from: number, distance: number ): boolean {\n    let diff = abs( from - index );\n\n    if ( ! Splide.is( SLIDE ) && ! isClone ) {\n      diff = min( diff, Splide.length - diff );\n    }\n\n    return diff <= distance;\n  }\n\n  return {\n    index,\n    slideIndex,\n    slide,\n    container,\n    isClone,\n    mount,\n    destroy,\n    rule,\n    isWithin,\n  };\n}\n", "/**\n * The power of the friction.\n *\n * @since 3.0.0\n */\nexport const FRICTION = 5;\n\n/**\n * If the user stops dragging for this duration with keeping the pointer down, updates the base coord and time.\n *\n * @since 3.0.0\n */\nexport const SAMPLING_INTERVAL = 50;\n\n/**\n * Start events for dragging.\n *\n * @since 3.0.0\n */\nexport const POINTER_DOWN_EVENTS = 'touchstart mousedown';\n\n/**\n * Update events for dragging.\n *\n * @since 3.0.0\n */\nexport const POINTER_MOVE_EVENTS = 'touchmove mousemove';\n\n/**\n * End events for dragging.\n *\n * @since 3.0.0\n */\nexport const POINTER_UP_EVENTS = 'touchend touchcancel mouseup mouseleave';\n", "import { TAB_INDEX } from '../../constants/attributes';\nimport { EVENT_UPDATED } from '../../constants/events';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { includes, isHTMLElement, removeAttribute, setAttribute } from '../../utils';\n\n\n/**\n * The interface for the Keyboard component.\n *\n * @since 3.0.0\n */\nexport interface KeyboardComponent extends BaseComponent {\n}\n\n/**\n * The collection of arrow keys of IE.\n *\n * @since 3.0.0\n */\nconst IE_ARROW_KEYS = [ 'Left', 'Right', 'Up', 'Down' ];\n\n/**\n * The component for controlling the slider by keyboards.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Keyboard component object.\n */\nexport function Keyboard( Splide: Splide, Components: Components, options: Options ): KeyboardComponent {\n  const { on, bind, unbind } = EventInterface( Splide );\n  const { root } = Components.Elements;\n  const { resolve } = Components.Direction;\n\n  /**\n   * The target element of the keyboard event.\n   */\n  let target: Window | HTMLElement;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    init();\n\n    on( EVENT_UPDATED, () => {\n      destroy();\n      init();\n    } );\n  }\n\n  /**\n   * Initializes the component.\n   */\n  function init(): void {\n    const { keyboard = 'global' } = options;\n\n    if ( keyboard ) {\n      if ( keyboard === 'focused' ) {\n        target = root;\n        setAttribute( root, TAB_INDEX, 0 );\n      } else {\n        target = window;\n      }\n\n      bind( target, 'keydown', e => {\n        const key = normalize( e.key );\n\n        if ( key === resolve( 'ArrowLeft' ) ) {\n          Splide.go( '<' );\n        } else if ( key === resolve( 'ArrowRight' ) ) {\n          Splide.go( '>' );\n        }\n      } );\n    }\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy() {\n    if ( target ) {\n      unbind( target, 'keydown' );\n\n      if ( isHTMLElement( target ) ) {\n        removeAttribute( target, TAB_INDEX );\n      }\n    }\n  }\n\n  /**\n   * Absorbs the difference of key names among browsers.\n   *\n   * @param key - A key to normalize.\n   *\n   * @return A normalized key.\n   */\n  function normalize( key: string ): string {\n    return includes( IE_ARROW_KEYS, key ) ? `Arrow${ key }` : key;\n  }\n\n  return {\n    mount,\n    destroy,\n  };\n}\n", "import { DATA_ATTRIBUTE } from '../../constants/project';\n\n\n/**\n * The data attribute for the src value.\n *\n * @since 3.0.0\n */\nexport const SRC_DATA_ATTRIBUTE = `${ DATA_ATTRIBUTE }-lazy`;\n\n/**\n * The data attribute for the srcset value.\n *\n * @since 3.0.0\n */\nexport const SRCSET_DATA_ATTRIBUTE = `${ SRC_DATA_ATTRIBUTE }-srcset`;\n\n/**\n * The selector string for images to load.\n *\n * @since 3.0.0\n */\nexport const IMAGE_SELECTOR = `[${ SRC_DATA_ATTRIBUTE }], [${ SRCSET_DATA_ATTRIBUTE }]`;\n", "import { EVENT_CLICK, EVENT_MOVE, EVENT_NAVIGATION_MOUNTED, EVENT_SLIDE_KEYDOWN } from '../../constants/events';\nimport { LOOP } from '../../constants/types';\nimport { Splide } from '../../core/Splide/Splide';\nimport { EventInterface } from '../../constructors';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { empty, includes, prevent } from '../../utils';\n\n\n/**\n * The interface for the Sync component.\n *\n * @since 3.0.0\n */\nexport interface SyncComponent extends BaseComponent {\n}\n\n/**\n * The keys for triggering the navigation slide.\n *\n * @since 3.0.0\n */\nconst TRIGGER_KEYS = [ ' ', 'Enter', 'Spacebar' ];\n\n/**\n * The component for syncing multiple sliders.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Sync component object.\n */\nexport function Sync( Splide: Splide, Components: Components, options: Options ): SyncComponent {\n  const { splides } = Splide;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    if ( options.isNavigation ) {\n      navigate();\n    } else {\n      sync();\n    }\n  }\n\n  /**\n   * Syncs the current index among all slides.\n   * The `processed` array prevents recursive call of handlers.\n   */\n  function sync(): void {\n    const processed: Splide[] = [];\n\n    splides.concat( Splide ).forEach( ( splide, index, instances ) => {\n      EventInterface( splide ).on( EVENT_MOVE, ( index, prev, dest ) => {\n        instances.forEach( instance => {\n          if ( instance !== splide && ! includes( processed, splide ) ) {\n            processed.push( instance );\n            instance.go( instance.is( LOOP ) ? dest : index );\n          }\n        } );\n\n        empty( processed );\n      } );\n    } );\n  }\n\n  /**\n   * Makes slides clickable and moves the slider to the index of clicked slide.\n   */\n  function navigate(): void {\n    const { on, emit } = EventInterface( Splide );\n\n    on( EVENT_CLICK, Slide => {\n      Splide.go( Slide.index );\n    } );\n\n    on( EVENT_SLIDE_KEYDOWN, ( Slide, e: KeyboardEvent ) => {\n      if ( includes( TRIGGER_KEYS, e.key ) ) {\n        Splide.go( Slide.index );\n        prevent( e );\n      }\n    } );\n\n    emit( EVENT_NAVIGATION_MOUNTED, Splide.splides );\n  }\n\n  return {\n    mount,\n  };\n}\n", "import { DATA_ATTRIBUTE } from '../../constants/project';\nimport { DESTROYED } from '../../constants/states';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { assert, find, getAttribute, merge } from '../../utils';\n\n\n/**\n * The interface for the Options component.\n *\n * @since 3.0.0\n */\nexport interface OptionsComponent extends BaseComponent {\n}\n\n/**\n * The component for managing options.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return An Options component object.\n */\nexport function Options( Splide: Splide, Components: Components, options: Options ): OptionsComponent {\n  /**\n   * Keeps the initial options to apply when no matched query exists.\n   */\n  let initialOptions: Options;\n\n  /**\n   * Stores breakpoints with the MediaQueryList object.\n   */\n  let points: [ string, MediaQueryList ][];\n\n  /**\n   * Holds the current breakpoint.\n   */\n  let currPoint: string | undefined;\n\n  /**\n   * Called when the component is constructed.\n   */\n  function setup(): void {\n    try {\n      merge( options, JSON.parse( getAttribute( Splide.root, DATA_ATTRIBUTE ) ) );\n    } catch ( e ) {\n      assert( false, e.message );\n    }\n\n    initialOptions = merge( {}, options );\n  }\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    const { breakpoints } = options;\n\n    if ( breakpoints ) {\n      points = Object.keys( breakpoints )\n        .sort( ( n, m ) => +n - +m )\n        .map( point => [\n          point,\n          matchMedia( `(${ options.mediaQuery || 'max' }-width:${ point }px)` ),\n        ] );\n\n      addEventListener( 'resize', observe );\n      observe();\n    }\n  }\n\n  /**\n   * Destroys the component.\n   *\n   * @param completely - Will be `true` for complete destruction.\n   */\n  function destroy( completely: boolean ): void {\n    if ( completely ) {\n      removeEventListener( 'resize', observe );\n    }\n  }\n\n  /**\n   * Observes breakpoints.\n   * The `currPoint` may be `undefined`.\n   */\n  function observe(): void {\n    const item = find( points, item => item[ 1 ].matches ) || [];\n\n    if ( item[ 0 ] !== currPoint ) {\n      onMatch( ( currPoint = item[ 0 ] ) );\n    }\n  }\n\n  /**\n   * Called when the media query matches breakpoints.\n   *\n   * @param point - A matched point, or `undefined` that means no breakpoint matches a media query.\n   */\n  function onMatch( point: string | undefined ): void {\n    const newOptions = options.breakpoints[ point ] || initialOptions;\n\n    if ( newOptions.destroy ) {\n      Splide.options = initialOptions;\n      Splide.destroy( newOptions.destroy === 'completely' );\n    } else {\n      if ( Splide.state.is( DESTROYED ) ) {\n        destroy( true );\n        Splide.mount();\n      }\n\n      Splide.options = newOptions;\n    }\n  }\n\n  return {\n    setup,\n    mount,\n    destroy,\n  };\n}\n", "/**\n * Enumerates slides from left to right.\n */\nexport const LTR = 'ltr';\n\n/**\n * Enumerates slides from right to left.\n */\nexport const RTL = 'rtl';\n\n/**\n * Enumerates slides in a col.\n */\nexport const TTB = 'ttb';\n", "import {\n  <PERSON><PERSON><PERSON>_ACTIVE,\n  <PERSON><PERSON><PERSON>_ARROW_NEXT,\n  <PERSON>LASS_ARROW_PREV,\n  CLASS_ARROWS,\n  CLASS_AUTOPLAY,\n  CLASS_CLONE,\n  CLASS_LIST,\n  CLASS_PAUSE,\n  CLASS_PLAY,\n  CLASS_PROGRESS,\n  CLASS_PROGRESS_BAR,\n  CLASS_ROOT,\n  CLASS_SLIDE,\n  CLASS_SLIDER,\n  CLASS_TRACK,\n} from '../../constants/classes';\nimport { EVENT_REFRESH, EVENT_UPDATED } from '../../constants/events';\nimport { PROJECT_CODE } from '../../constants/project';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { addClass, assert, assign, child, children, empty, push, query, removeClass, uniqueId } from '../../utils';\n\n\n/**\n * The interface for elements which the slider consists of.\n *\n * @since 3.0.0\n */\nexport interface ElementCollection {\n  root: HTMLElement;\n  slider: HTMLElement;\n  track: HTMLElement;\n  list: HTMLElement;\n  slides: HTMLElement[];\n  arrows: HTMLElement;\n  prev: HTMLButtonElement;\n  next: HTMLButtonElement;\n  bar: HTMLElement;\n  play: HTMLElement;\n  pause: HTMLElement;\n}\n\n/**\n * The interface for the Elements component.\n *\n * @since 3.0.0\n */\nexport interface ElementsComponent extends BaseComponent, ElementCollection {\n}\n\n/**\n * The component that collects and handles elements which the slider consists of.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return An Elements component object.\n */\nexport function Elements( Splide: Splide, Components: Components, options: Options ): ElementsComponent {\n  const { on } = EventInterface( Splide );\n  const { root } = Splide;\n  const elements: ElementCollection = {} as ElementCollection;\n\n  /**\n   * Stores all slide elements.\n   */\n  const slides: HTMLElement[] = [];\n\n  /**\n   * Stores all root classes.\n   */\n  let classes: string[];\n\n  /**\n   * The slider element that may be `undefined`.\n   */\n  let slider: HTMLElement;\n\n  /**\n   * The track element.\n   */\n  let track: HTMLElement;\n\n  /**\n   * The list element.\n   */\n  let list: HTMLElement;\n\n  /**\n   * Called when the component is constructed.\n   */\n  function setup(): void {\n    collect();\n    identify();\n    addClass( root, ( classes = getClasses() ) );\n  }\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    on( EVENT_REFRESH, refresh );\n    on( EVENT_UPDATED, update );\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy(): void {\n    empty( slides );\n    removeClass( root, classes );\n  }\n\n  /**\n   * Recollects slide elements.\n   */\n  function refresh(): void {\n    destroy();\n    setup();\n  }\n\n  /**\n   * Updates the status of elements.\n   */\n  function update(): void {\n    removeClass( root, classes );\n    addClass( root, ( classes = getClasses() ) );\n  }\n\n  /**\n   * Collects elements which the slider consists of.\n   */\n  function collect(): void {\n    slider = child( root, `.${ CLASS_SLIDER }` );\n    track  = query( root, `.${ CLASS_TRACK }` );\n    list   = child( track, `.${ CLASS_LIST }` );\n\n    assert( track && list, 'Missing a track/list element.' );\n\n    push( slides, children( list, `.${ CLASS_SLIDE }:not(.${ CLASS_CLONE })` ) );\n\n    const autoplay = find( `.${ CLASS_AUTOPLAY }` );\n    const arrows   = find( `.${ CLASS_ARROWS }` );\n\n    assign( elements, {\n      root,\n      slider,\n      track,\n      list,\n      slides,\n      arrows,\n      prev : query( arrows, `.${ CLASS_ARROW_PREV }` ),\n      next : query( arrows, `.${ CLASS_ARROW_NEXT }` ),\n      bar  : query( find( `.${ CLASS_PROGRESS }` ), `.${ CLASS_PROGRESS_BAR }` ),\n      play : query( autoplay, `.${ CLASS_PLAY }` ),\n      pause: query( autoplay, `.${ CLASS_PAUSE }` ),\n    } );\n  }\n\n  /**\n   * Assigns unique IDs to essential elements.\n   */\n  function identify(): void {\n    const id = root.id || uniqueId( PROJECT_CODE );\n    root.id  = id;\n    track.id = track.id || `${ id }-track`;\n    list.id  = list.id || `${ id }-list`;\n  }\n\n  /**\n   * Finds an element only in children of the root or slider element.\n   *\n   * @return {Element} - A found element or undefined.\n   */\n  function find( selector: string ): HTMLElement {\n    return child( root, selector ) || child( slider, selector );\n  }\n\n  /**\n   * Return an array with classes for the root element.\n   *\n   * @return An array with classes.\n   */\n  function getClasses(): string[] {\n    return [\n      `${ CLASS_ROOT }--${ options.type }`,\n      `${ CLASS_ROOT }--${ options.direction }`,\n      options.drag && `${ CLASS_ROOT }--draggable`,\n      options.isNavigation && `${ CLASS_ROOT }--nav`,\n      CLASS_ACTIVE,\n    ];\n  }\n\n  return assign( elements, {\n    setup,\n    mount,\n    destroy,\n  } );\n}\n", "import { BaseComponent } from '../../types';\nimport { create, find, isHTMLElement, remove } from '../../utils';\n\n/**\n * The interface for the Style component.\n *\n * @since 3.0.0\n */\nexport interface StyleComponent extends BaseComponent {\n  rule( selector: string, prop: string, value: string | number ): void;\n  ruleBy( target: string | HTMLElement, prop: string, value: string | number ): void;\n}\n\n/**\n * The component for managing styles of the slider.\n *\n * @since 3.0.0\n *\n * @return A Style component object.\n */\nexport function Style(): StyleComponent {\n  /**\n   * The style element for the slider.\n   */\n  let style: HTMLStyleElement;\n\n  /**\n   * The CSSStyleSheet object of the created style element.\n   */\n  let sheet: CSSStyleSheet;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    style = create( 'style', {}, document.head );\n    sheet = style.sheet;\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy(): void {\n    remove( style );\n    sheet = null;\n  }\n\n  /**\n   * Registers the style for the selector.\n   *\n   * @param selector - A selector string.\n   * @param prop     - A CSS property, accepting the camel case.\n   * @param value    - A CSS value.\n   */\n  function rule( selector: string, prop: string, value: string | number ): void {\n    const { cssRules } = sheet;\n    const cssRule = find( cssRules, cssRule => isCSSStyleRule( cssRule ) && cssRule.selectorText === selector )\n      || cssRules[ sheet.insertRule( `${ selector }{}`, 0 ) ];\n\n    if ( isCSSStyleRule( cssRule ) ) {\n      cssRule.style[ prop ] = `${ value }`;\n    }\n  }\n\n  /**\n   * Registers the style by the element or the ID.\n   *\n   * @param target - A target element or ID.\n   * @param prop   - A CSS property, accepting the camel case.\n   * @param value  - A CSS value.\n   */\n  function ruleBy( target: string | HTMLElement, prop: string, value: string | number ): void {\n    rule( `#${ isHTMLElement( target ) ? target.id : target }`, prop, value );\n  }\n\n  /**\n   * Checks if the provided rule is a CSSStyleRule instance or not.\n   *\n   * @param cssRule - An instance to check.\n   *\n   * @return `true` if the cssRule is an instance of CSSStyleRule, or otherwise `false`.\n   */\n  function isCSSStyleRule( cssRule: CSSRule ): cssRule is CSSStyleRule {\n    return cssRule instanceof CSSStyleRule;\n  }\n\n  return {\n    mount,\n    destroy,\n    rule,\n    ruleBy,\n  };\n}\n", "import { EVENT_REFRESH, EVENT_RESIZE } from '../../constants/events';\nimport { Splide } from '../../core/Splide/Splide';\nimport { EventInterface } from '../../constructors';\nimport { AnyFunction, BaseComponent, Components, Options } from '../../types';\nimport {\n  addClass,\n  append,\n  before,\n  between,\n  empty,\n  forEach as forEachItem,\n  includes,\n  isFunction,\n  isHTMLElement,\n  isString,\n  matches,\n  parseHtml,\n  queryAll,\n  remove as removeNode,\n  toArray,\n} from '../../utils';\nimport { Slide, SlideComponent } from './Slide';\n\n\n/**\n * The interface for the Slides component.\n *\n * @since 3.0.0\n */\nexport interface  SlidesComponent extends BaseComponent {\n  register( slide: HTMLElement, index: number, slideIndex: number ): void;\n  get( excludeClones?: boolean ): SlideComponent[];\n  getIn( page: number ): SlideComponent[];\n  getAt( index: number ): SlideComponent | undefined;\n  add( slide: string | Element | Array<string | Element>, index?: number, callback?: AnyFunction ): void;\n  remove( selector: SlideMatcher ): void;\n  forEach( iteratee: SlidesIteratee, excludeClones?: boolean ): void;\n  filter( matcher: SlideMatcher ): SlideComponent[];\n  rule( prop: string, value: string | number, useContainer?: boolean ): void\n  getLength( excludeClones?: boolean ): number;\n  isEnough(): boolean;\n}\n\n/**\n * The iteratee function for Slides.\n *\n * @since 3.0.0\n */\nexport type SlidesIteratee = ( Slide: SlideComponent, index: number, Slides: SlideComponent[] ) => void\n\n/**\n * The predicate function for Slides.\n *\n * @since 3.0.0\n */\nexport type SlidesPredicate = ( Slide: SlideComponent, index: number, Slides: SlideComponent[] ) => any\n\n/**\n * The type for filtering SlideComponent objects.\n *\n * @since 3.0.0\n */\nexport type SlideMatcher = number | number[] | string | SlidesPredicate;\n\n/**\n * The component for managing all slides include clones.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return An Slides component object.\n */\nexport function Slides( Splide: Splide, Components: Components, options: Options ): SlidesComponent {\n  const { on, emit, bind } = EventInterface( Splide );\n  const { slides, list } = Components.Elements;\n\n  /**\n   * Stores all SlideComponent objects.\n   */\n  const Slides: SlideComponent[] = [];\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    init();\n    on( EVENT_REFRESH, refresh );\n  }\n\n  /**\n   * Initializes the component.\n   */\n  function init(): void {\n    slides.forEach( ( slide, index ) => { register( slide, index, -1 ) } );\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy(): void {\n    forEach( Slide => { Slide.destroy() } );\n    empty( Slides );\n  }\n\n  /**\n   * Discards all Slide components and regenerates them.\n   */\n  function refresh(): void {\n    destroy();\n    init();\n  }\n\n  /**\n   * Registers a slide element and creates a Slide object.\n   *\n   * @param slide      - A slide element to register.\n   * @param index      - A slide index.\n   * @param slideIndex - A slide index for clones. This must be `-1` for regular slides.\n   */\n  function register( slide: HTMLElement, index: number, slideIndex: number ): void {\n    const object = Slide( Splide, index, slideIndex, slide );\n    object.mount();\n    Slides.push( object );\n  }\n\n  /**\n   * Returns all Slide objects.\n   *\n   * @param excludeClones - Optional. Determines whether to exclude clones or not.\n   *\n   * @return An array with Slide objects.\n   */\n  function get( excludeClones?: boolean ): SlideComponent[] {\n    return excludeClones ? filter( Slide => ! Slide.isClone ) : Slides;\n  }\n\n  /**\n   * Returns slides in the specified page.\n   *\n   * @param page - A page index.\n   *\n   * @return An array with slides that belong to the page.\n   */\n  function getIn( page: number ): SlideComponent[] {\n    const { Controller } = Components;\n    const index = Controller.toIndex( page );\n    const max   = Controller.hasFocus() ? 1 : options.perPage;\n    return filter( Slide => between( Slide.index, index, index + max - 1 ) );\n  }\n\n  /**\n   * Returns a Slide object at the specified index.\n   *\n   * @param index - A slide index.\n   *\n   * @return A Slide object if available, or otherwise `undefined`.\n   */\n  function getAt( index: number ): SlideComponent | undefined {\n    return filter( index )[ 0 ];\n  }\n\n  /**\n   * Inserts a slide or slides at a specified index.\n   *\n   * @param items - A slide element, an HTML string or an array with them.\n   * @param index - Optional. An index to insert the slide at. If omitted, appends it to the list.\n   */\n  function add( items: string | Element | Array<string | Element>, index?: number ): void {\n    forEachItem( items, slide => {\n      if ( isString( slide ) ) {\n        slide = parseHtml( slide );\n      }\n\n      if ( isHTMLElement( slide ) ) {\n        const ref = slides[ index ];\n        ref ? before( slide, ref ) : append( list, slide );\n        addClass( slide, options.classes.slide );\n        observeImages( slide, emit.bind( null, EVENT_RESIZE ) );\n      }\n    } );\n\n    emit( EVENT_REFRESH );\n  }\n\n  /**\n   * Removes slides that match the matcher\n   * that can be an index, an array with indices, a selector, or an iteratee function.\n   *\n   * @param matcher - An index, an array with indices, a selector string, or an iteratee function.\n   */\n  function remove( matcher: SlideMatcher ): void {\n    removeNode( filter( matcher ).map( Slide => Slide.slide ) );\n    emit( EVENT_REFRESH );\n  }\n\n  /**\n   * Iterates over Slide objects by the iteratee function.\n   *\n   * @param iteratee      - An iteratee function that takes a Slide object, an index and an array with Slides.\n   * @param excludeClones - Optional. Determines whether to exclude clones or not.\n   */\n  function forEach( iteratee: SlidesIteratee, excludeClones?: boolean ): void {\n    get( excludeClones ).forEach( iteratee );\n  }\n\n  /**\n   * Filters Slides by the matcher\n   * that can be an index, an array with indices, a selector, or an predicate function.\n   *\n   * @param matcher - An index, an array with indices, a selector string, or an predicate function.\n   *\n   * @return An array with SlideComponent objects.\n   */\n  function filter( matcher: SlideMatcher ): SlideComponent[] {\n    return Slides.filter( isFunction( matcher )\n      ? matcher\n      : Slide => isString( matcher )\n        ? matches( Slide.slide, matcher )\n        : includes( toArray( matcher ), Slide.index )\n    );\n  }\n\n  /**\n   * Adds a CSS rule to all slides or containers.\n   *\n   * @param prop         - A property name.\n   * @param value        - A CSS value to add.\n   * @param useContainer - Optional. Determines whether to apply the rule to the container or not.\n   */\n  function rule( prop: string, value: string | number, useContainer?: boolean ): void {\n    forEach( Slide => { Slide.rule( prop, value, useContainer ) } );\n  }\n\n  /**\n   * Invokes the callback after all images in the element are loaded.\n   *\n   * @param elm      - An element that may contain images.\n   * @param callback - A callback function.\n   */\n  function observeImages( elm: Element, callback: AnyFunction ): void {\n    const images = queryAll( elm, 'img' );\n    let { length } = images;\n\n    if ( length ) {\n      images.forEach( img => {\n        bind( img, 'load error', () => {\n          if ( ! --length ) {\n            callback();\n          }\n        } );\n      } );\n    } else {\n      callback();\n    }\n  }\n\n  /**\n   * Returns the length of slides.\n   *\n   * @param excludeClones - Optional. Determines whether to exclude clones or not.\n   *\n   * @return The length of slides.\n   */\n  function getLength( excludeClones?: boolean ): number {\n    return excludeClones ? slides.length : Slides.length;\n  }\n\n  /**\n   * Checks if the number of slides is over than the `perPage` option, including clones.\n   *\n   * @return `true` if there are enough slides, or otherwise `false`.\n   */\n  function isEnough(): boolean {\n    return Slides.length > options.perPage;\n  }\n\n  return {\n    mount,\n    destroy,\n    register,\n    get,\n    getIn,\n    getAt,\n    add,\n    remove,\n    forEach,\n    filter,\n    rule,\n    getLength,\n    isEnough,\n  };\n}\n", "import { EVENT_REFRESH, EVENT_RESIZE, EVENT_UPDATED } from '../../constants/events';\nimport { LOOP } from '../../constants/types';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { addClass, append, before, ceil, empty, pad, push, rect, remove } from '../../utils';\n\n\n/**\n * The interface for the Clone component.\n *\n * @since 3.0.0\n */\nexport interface CloneComponent extends BaseComponent {\n}\n\n/**\n * The component that generates clones for the loop slider.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Clones component object.\n */\nexport function Clones( Splide: Splide, Components: Components, options: Options ): CloneComponent {\n  const { on, emit } = EventInterface( Splide );\n  const { Elements, Slides } = Components;\n  const { resolve } = Components.Direction;\n  const clones: HTMLElement[] = [];\n\n  /**\n   * Keeps the current number of clones.\n   */\n  let cloneCount: number;\n\n  /**\n   * The index used for generating IDs.\n   */\n  let cloneIndex: number;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    init();\n    on( EVENT_REFRESH, refresh );\n    on( [ EVENT_UPDATED, EVENT_RESIZE ], observe );\n  }\n\n  /**\n   * Removes all clones if available, and generates new clones.\n   */\n  function init(): void {\n    if ( ( cloneCount = computeCloneCount() ) ) {\n      generate( cloneCount );\n    }\n  }\n\n  /**\n   * Destroys clones.\n   */\n  function destroy(): void {\n    remove( clones );\n    empty( clones );\n  }\n\n  /**\n   * Discards all clones and regenerates them.\n   * Must do this before the Elements component collects slide elements.\n   */\n  function refresh(): void {\n    destroy();\n    init();\n  }\n\n  /**\n   * Observes the required clone count and refreshes the slider if necessary.\n   */\n  function observe(): void {\n    if ( cloneCount !== computeCloneCount() ) {\n      emit( EVENT_REFRESH );\n    }\n  }\n\n  /**\n   * Generates the specified number of clones.\n   *\n   * @param count - The number of clones to generate for each side.\n   */\n  function generate( count: number ): void {\n    const slides = Slides.get().slice();\n    const { length } = slides;\n\n    if ( length ) {\n      cloneIndex = 0;\n\n      while ( slides.length < count ) {\n        push( slides, slides );\n      }\n\n      slides.slice( -count ).concat( slides.slice( 0, count ) ).forEach( ( Slide, index ) => {\n        const isHead = index < count;\n        const clone  = cloneDeep( Slide.slide );\n        isHead ? before( clone, slides[ 0 ].slide ) : append( Elements.list, clone );\n        push( clones, clone );\n        Slides.register( clone, index - count + ( isHead ? 0 : length ), Slide.index );\n      } );\n    }\n  }\n\n  /**\n   * Deeply clones the provided element with removing the ID attribute.\n   *\n   * @param elm - An element to clone.\n   *\n   * @return A cloned element.\n   */\n  function cloneDeep( elm: HTMLElement ): HTMLElement {\n    const clone = elm.cloneNode( true ) as HTMLElement;\n    addClass( clone, options.classes.clone );\n    clone.id = `${ Splide.root.id }-clone${ pad( ++cloneIndex ) }`;\n    return clone;\n  }\n\n  /**\n   * Returns the number of elements to generate.\n   * This always returns 0 if the slider type is not `'loop'`.\n   *\n   * @return The number of clones.\n   */\n  function computeCloneCount(): number {\n    let { clones } = options;\n\n    if ( ! Splide.is( LOOP ) ) {\n      clones = 0;\n    } else if ( ! clones ) {\n      const fixedSize  = options[ resolve( 'fixedWidth' ) ];\n      const fixedCount = fixedSize && ceil( rect( Elements.track )[ resolve( 'width' ) ] / fixedSize );\n      const baseCount  = fixedCount || ( options[ resolve( 'autoWidth' ) ] && Splide.length ) || options.perPage;\n\n      clones = baseCount * ( options.drag ? ( options.flickMaxPages || 1 ) + 1 : 2 );\n    }\n\n    return clones;\n  }\n\n  return {\n    mount,\n    destroy,\n  };\n}\n", "import { TTB } from '../../constants/directions';\nimport { EVENT_REFRESH, EVENT_RESIZE, EVENT_RESIZED, EVENT_UPDATED } from '../../constants/events';\nimport { EventInterface, Throttle } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { abs, assert, isObject, rect, style, unit } from '../../utils';\n\n\n/**\n * The interface for the Layout component.\n *\n * @since 3.0.0\n */\nexport interface LayoutComponent extends BaseComponent {\n  listSize(): number;\n  slideSize( index: number, withoutGap?: boolean ): number;\n  sliderSize(): number;\n  totalSize( index?: number, withoutGap?: boolean ): number;\n  getPadding( right: boolean ): number;\n}\n\n/**\n * The component that layouts slider components and provides methods for dimensions.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return An Layout component object.\n */\nexport function Layout( Splide: Splide, Components: Components, options: Options ): LayoutComponent {\n  const { on, bind, emit } = EventInterface( Splide );\n  const { Slides } = Components;\n  const { ruleBy } = Components.Style;\n  const { resolve } = Components.Direction;\n  const { root, track, list } = Components.Elements;\n  const { getAt } = Slides;\n  const vertical = options.direction === TTB;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    init();\n\n    bind( window, 'resize load', Throttle( emit.bind( this, EVENT_RESIZE ) ) );\n    on( [ EVENT_UPDATED, EVENT_REFRESH ], init );\n    on( EVENT_RESIZE, resize );\n  }\n\n  /**\n   * Initializes the component on `mount` or `updated`.\n   * Uses `max-width` for the root to prevent the slider from exceeding the parent element.\n   */\n  function init(): void {\n    ruleBy( root, 'maxWidth', unit( options.width ) );\n    ruleBy( track, resolve( 'paddingLeft' ), cssPadding( false ) );\n    ruleBy( track, resolve( 'paddingRight' ), cssPadding( true ) );\n\n    Slides.rule( resolve( 'marginRight' ), unit( options.gap ) );\n    Slides.rule( 'width', cssSlideWidth() );\n\n    setSlidesHeight();\n    resize();\n  }\n\n  /**\n   * Updates dimensions of some elements when the slider is resized.\n   */\n  function resize(): void {\n    ruleBy( track, 'height', cssTrackHeight() );\n    options.heightRatio && setSlidesHeight();\n    emit( EVENT_RESIZED );\n  }\n\n  /**\n   * Updates the height of slides or their container elements if available.\n   */\n  function setSlidesHeight(): void {\n    Slides.rule( 'height', cssSlideHeight(), true );\n  }\n\n  /**\n   * Parses the padding option and returns the value for each side.\n   * This method returns `paddingTop` or `paddingBottom` for the vertical slider.\n   *\n   * @param right - Determines whether to get `paddingRight/Bottom` or `paddingLeft/Top`.\n   *\n   * @return The padding value as a CSS string.\n   */\n  function cssPadding( right: boolean ): string {\n    const { padding } = options;\n    const prop = resolve( right ? 'right' : 'left', true );\n    return padding ? unit( padding[ prop ] || ( isObject( padding ) ? '0' : padding ) ) : '';\n  }\n\n  /**\n   * Returns the height of the track element as a CSS string.\n   *\n   * @return The height of the track.\n   */\n  function cssTrackHeight(): string {\n    let height = '';\n\n    if ( vertical ) {\n      height = cssHeight();\n      assert( height, '\"height\" or \"heightRatio\" is missing.' );\n\n      const paddingTop    = cssPadding( false );\n      const paddingBottom = cssPadding( true );\n\n      if ( paddingTop || paddingBottom ) {\n        height = `calc(${ height }`;\n        height += `${ paddingTop ? ` - ${ paddingTop }` : '' }${ paddingBottom ? ` - ${ paddingBottom }` : '' })`;\n      }\n    }\n\n    return height;\n  }\n\n  /**\n   * Converts options related with height to a CSS string.\n   *\n   * @return The height as a CSS string if available, or otherwise an empty string.\n   */\n  function cssHeight(): string {\n    return unit( options.height || rect( list ).width * options.heightRatio );\n  }\n\n  /**\n   * Returns the width of the slide as a CSS string.\n   *\n   * @return The width of the slide.\n   */\n  function cssSlideWidth(): string {\n    return options.autoWidth ? '' : unit( options.fixedWidth ) || ( vertical ? '' : cssSlideSize() );\n  }\n\n  /**\n   * Returns the height of the slide as a CSS string.\n   *\n   * @return The height of the slide.\n   */\n  function cssSlideHeight(): string {\n    return unit( options.fixedHeight )\n      || ( vertical ? ( options.autoHeight ? '' : cssSlideSize() ) : cssHeight() );\n  }\n\n  /**\n   * Returns the CSS string for slide width or height without gap.\n   *\n   * @return The CSS string for slide width or height.\n   */\n  function cssSlideSize(): string {\n    const gap = unit( options.gap );\n    return `calc((100%${ gap && ` + ${ gap }` })/${ options.perPage || 1 }${ gap && ` - ${ gap }` })`;\n  }\n\n  /**\n   * Returns the list width for the horizontal slider, or the height for the vertical slider.\n   *\n   * @return The size of the track element in pixel.\n   */\n  function listSize(): number {\n    return rect( list )[ resolve( 'width' ) ];\n  }\n\n  /**\n   * Returns the slide width for the horizontal slider, or the height for the vertical slider.\n   *\n   * @param index      - Optional. A slide index.\n   * @param withoutGap - Optional. Determines whether to exclude the gap amount or not.\n   *\n   * @return The size of the specified slide element in pixel.\n   */\n  function slideSize( index?: number, withoutGap?: boolean ): number {\n    const Slide = getAt( index || 0 );\n    return Slide\n      ? rect( Slide.slide )[ resolve( 'width' ) ] + ( withoutGap ? 0 : getGap() )\n      : 0;\n  }\n\n  /**\n   * Returns the total width or height of slides from 0 to the specified index.\n   *\n   * @param index      - A slide index. If omitted, uses the last index.\n   * @param withoutGap - Optional. Determines whether to exclude the last gap or not.\n   *\n   * @return The total width of slides in the horizontal slider, or the height in the vertical one.\n   */\n  function totalSize( index: number, withoutGap?: boolean ): number {\n    const Slide = getAt( index );\n\n    if ( Slide ) {\n      const right = rect( Slide.slide )[ resolve( 'right' ) ];\n      const left  = rect( list )[ resolve( 'left' ) ];\n      return abs( right - left ) + ( withoutGap ? 0 : getGap() );\n    }\n\n    return 0;\n  }\n\n  /**\n   * Returns the slider size without clones.\n   *\n   * @return The slider size.\n   */\n  function sliderSize(): number {\n    const firstSlide = getAt( 0 );\n    const lastSlide  = getAt( Slides.getLength( true ) - 1 );\n\n    if ( firstSlide && lastSlide ) {\n      return rect( lastSlide.slide )[ resolve( 'right' ) ] - rect( firstSlide.slide )[ resolve( 'left' ) ];\n    }\n\n    return 0;\n  }\n\n  /**\n   * Returns the gap value.\n   *\n   *\n   * @return The gap value in pixel.\n   */\n  function getGap(): number {\n    const Slide = getAt( 0 );\n    return Slide ? parseFloat( style( Slide.slide, resolve( 'marginRight' ) ) ) || 0 : 0;\n  }\n\n  /**\n   * Returns the padding value.\n   *\n   * @param right - Determines whether to get `paddingRight/Bottom` or `paddingLeft/Top`.\n   *\n   * @return The padding value in pixel.\n   */\n  function getPadding( right: boolean ): number {\n    return parseFloat( style( track, resolve( `padding${ right ? 'Right' : 'Left' }`, true ) ) ) || 0;\n  }\n\n  return {\n    mount,\n    listSize,\n    slideSize,\n    sliderSize,\n    totalSize,\n    getPadding,\n  };\n}\n", "import { EVENT_MOVE, EVENT_MOVED, EVENT_REFRESH, EVENT_RESIZE, EVENT_UPDATED } from '../../constants/events';\nimport { IDLE, MOVING } from '../../constants/states';\nimport { LOOP, SLIDE } from '../../constants/types';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { abs, clamp, rect } from '../../utils';\n\n\n/**\n * The interface for the Move component.\n *\n * @since 3.0.0\n */\nexport interface MoveComponent extends BaseComponent {\n  move( dest: number, index: number, prev: number ): void;\n  jump( index: number ): void;\n  translate( position: number ): void;\n  cancel(): void;\n  toIndex( position: number ): number;\n  toPosition( index: number, trimming?: boolean ): number;\n  getPosition(): number;\n  getLimit( max: boolean ): number;\n  isBusy(): boolean;\n  isExceeded(): boolean;\n  isExceededMin( position: number, offset?: number ): boolean;\n  isExceededMax( position: number, offset?: number ): boolean;\n}\n\n/**\n * The component for moving the slider.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Move component object.\n */\nexport function Move( Splide: Splide, Components: Components, options: Options ): MoveComponent {\n  const { on, emit } = EventInterface( Splide );\n  const { slideSize, getPadding, totalSize, listSize, sliderSize } = Components.Layout;\n  const { resolve, orient } = Components.Direction;\n  const { list, track } = Components.Elements;\n\n  /**\n   * Indicates whether the slider is just looping or not.\n   */\n  let looping: boolean;\n\n  /**\n   * Indicates whether the component can move the slider or not.\n   */\n  let waiting: boolean;\n\n  /**\n   * Keeps the current position.\n   */\n  let currPosition = 0;\n\n  /**\n   * Keeps the rate of position to the slider width.\n   */\n  let positionRate = 0;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    on( [ EVENT_RESIZE, EVENT_UPDATED, EVENT_REFRESH ], reposition );\n  }\n\n  /**\n   * Repositions the slider.\n   */\n  function reposition(): void {\n    if ( options.drag !== 'free' ) {\n      jump( Splide.index );\n    } else {\n      if ( ! options[ resolve( 'fixedWidth' ) ] && ! options[ resolve( 'autoWidth' ) ] ) {\n        translate( listSize() * positionRate );\n      }\n\n      if ( isExceededMax( currPosition ) ) {\n        translate( getLimit( true ) );\n      }\n    }\n  }\n\n  /**\n   * Goes to the slide at the specified index with the Transition component.\n   *\n   * @param dest  - A destination index to go to.\n   * @param index - A slide index.\n   * @param prev  - A previous index.\n   */\n  function move( dest: number, index: number, prev: number ): void {\n    if ( ! isBusy() ) {\n      const position = getPosition();\n\n      looping = dest !== index;\n      waiting = options.waitForTransition;\n\n      Splide.state.set( MOVING );\n      emit( EVENT_MOVE, index, prev, dest );\n\n      Components.Transition.start( dest, () => {\n        onMoved( dest, index, prev, position );\n      } );\n    }\n  }\n\n  /**\n   * Called after the transition ends.\n   *\n   * @param dest        - A destination index to go to.\n   * @param index       - A slide index.\n   * @param prev        - A previous index.\n   * @param oldPosition - An old position.\n   */\n  function onMoved( dest: number, index: number, prev: number, oldPosition: number ) {\n    if ( looping ) {\n      jump( index );\n      looping = false;\n    }\n\n    waiting = false;\n    Splide.state.set( IDLE );\n    emit( EVENT_MOVED, index, prev, dest );\n\n    if ( options.trimSpace === 'move' && dest !== prev && oldPosition === getPosition() ) {\n      Components.Controller.go( dest > prev ? '>' : '<' );\n    }\n  }\n\n  /**\n   * Jumps to the slide at the specified index.\n   *\n   * @param index - An index to jump to.\n   */\n  function jump( index: number ): void {\n    translate( toPosition( index, true ) );\n  }\n\n  /**\n   * Moves the slider to the specified position.\n   *\n   * @param position - The destination.\n   */\n  function translate( position: number ): void {\n    currPosition = loop( position );\n    positionRate = currPosition / listSize();\n    Components.Style.ruleBy( list, 'transform', `translate${ resolve( 'X' ) }(${ currPosition }px)` );\n  }\n\n  /**\n   * Loops the provided position if it exceeds limits.\n   *\n   * @param position - A position to loop.\n   */\n  function loop( position: number ): number {\n    if ( ! looping && Splide.is( LOOP ) ) {\n      const diff        = position - currPosition;\n      const exceededMin = isExceededMin( position );\n      const exceededMax = isExceededMax( position );\n\n      if ( ( exceededMin && diff > 0 ) || ( exceededMax && diff < 0 ) ) {\n        position += orient( sliderSize() * ( exceededMin ? 1 : -1 ) );\n      }\n    }\n\n    return position;\n  }\n\n  /**\n   * Cancels transition.\n   */\n  function cancel(): void {\n    translate( getPosition() );\n    Components.Transition.cancel();\n  }\n\n  /**\n   * Returns the closest index to the position.\n   *\n   * @param position - A position to convert.\n   *\n   * @return The closest index to the position.\n   */\n  function toIndex( position: number ): number {\n    const Slides = Components.Slides.get();\n\n    let index       = 0;\n    let minDistance = Infinity;\n\n    for ( let i = 0; i < Slides.length; i++ ) {\n      const slideIndex = Slides[ i ].index;\n      const distance   = abs( toPosition( slideIndex ) - position );\n\n      if ( distance < minDistance ) {\n        minDistance = distance;\n        index       = slideIndex;\n      } else {\n        break;\n      }\n    }\n\n    return index;\n  }\n\n  /**\n   * Converts the slide index to the position.\n   *\n   * @param index    - An index to convert.\n   * @param trimming - Optional. Whether to trim edge spaces or not.\n   *\n   * @return The position corresponding with the index.\n   */\n  function toPosition( index: number, trimming?: boolean ): number {\n    const position = orient( totalSize( index - 1 ) - offset( index ) );\n    return trimming ? trim( position ) : position;\n  }\n\n  /**\n   * Returns the current position.\n   *\n   * @return The position of the list element.\n   */\n  function getPosition(): number {\n    const left = resolve( 'left' );\n    return rect( list )[ left ] - rect( track )[ left ] + orient( getPadding( false ) );\n  }\n\n  /**\n   * Trims spaces on the edge of the slider.\n   *\n   * @param position - A position to trim.\n   *\n   * @return A trimmed position.\n   */\n  function trim( position: number ): number {\n    if ( options.trimSpace && Splide.is( SLIDE ) ) {\n      position = clamp( position, 0, orient( sliderSize() - listSize() ) );\n    }\n\n    return position;\n  }\n\n  /**\n   * Returns the offset amount.\n   *\n   * @param index - An index.\n   */\n  function offset( index: number ): number {\n    const { focus } = options;\n\n    if ( focus === 'center' ) {\n      return ( listSize() - slideSize( index, true ) ) / 2;\n    }\n\n    return ( +focus || 0 ) * slideSize( index );\n  }\n\n  /**\n   * Returns the limit number that the slider can move to.\n   *\n   * @param max - Determines whether to return the maximum or minimum limit.\n   *\n   * @return The border number.\n   */\n  function getLimit( max: boolean ): number {\n    const trimming = !! options.trimSpace;\n    return max ? toPosition( Components.Controller.getEnd(), trimming ) : toPosition( 0, trimming );\n  }\n\n  /**\n   * Checks if the slider can move now or not.\n   *\n   * @return `true` if the slider can move, or otherwise `false`.\n   */\n  function isBusy(): boolean {\n    return !! ( looping || waiting );\n  }\n\n  /**\n   * Checks if the provided position exceeds the minimum limit or not.\n   *\n   * @param position - A position to test.\n   * @param offset   - Optional. Offsets the limit in pixel.\n   *\n   * @return `true` if the position exceeds the limit, or otherwise `false`.\n   */\n  function isExceededMin( position: number, offset?: number ): boolean {\n    return orient( position ) + ( offset || 0 ) < orient( getLimit( false ) );\n  }\n\n  /**\n   * Checks if the provided position exceeds the maximum limit or not.\n   *\n   * @param position - A position to test.\n   * @param offset   - Optional. Offsets the limit in pixel.\n   *\n   * @return `true` if the position exceeds the limit, or otherwise `false`.\n   */\n  function isExceededMax( position: number, offset?: number ): boolean {\n    return orient( position ) + ( offset || 0 ) > orient( getLimit( true ) );\n  }\n\n  /**\n   * Checks if the slider position exceeds borders or not.\n   *\n   * @return `true` if the position is over borders, or otherwise `false`.\n   */\n  function isExceeded(): boolean {\n    return isExceededMin( currPosition ) || isExceededMax( currPosition );\n  }\n\n  return {\n    mount,\n    move,\n    jump,\n    translate,\n    cancel,\n    toIndex,\n    toPosition,\n    getPosition,\n    getLimit,\n    isBusy,\n    isExceededMin,\n    isExceededMax,\n    isExceeded,\n  };\n}\n", "import { EVENT_REFRESH, EVENT_SCROLLED, EVENT_UPDATED } from '../../constants/events';\nimport { LOOP } from '../../constants/types';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { between, clamp, floor, isString, isUndefined, max } from '../../utils';\n\n\n/**\n * The interface for the Controller component.\n *\n * @since 3.0.0\n */\nexport interface ControllerComponent extends BaseComponent {\n  go( control: number | string, allowSameIndex?: boolean ): void;\n  getNext( destination?: boolean ): number;\n  getPrev( destination?: boolean ): number;\n  getEnd(): number;\n  setIndex( index: number ): void;\n  getIndex( prev?: boolean ): number;\n  toIndex( page: number ): number;\n  toPage( index: number ): number;\n  hasFocus(): boolean;\n}\n\n/**\n * The component for controlling the slider.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Controller component object.\n */\nexport function Controller( Splide: Splide, Components: Components, options: Options ): ControllerComponent {\n  const { on } = EventInterface( Splide );\n  const { Move } = Components;\n  const { isEnough, getLength } = Components.Slides;\n  const isLoop = Splide.is( LOOP );\n\n  /**\n   * The current index.\n   */\n  let currIndex = options.start || 0;\n\n  /**\n   * The previous index.\n   */\n  let prevIndex = currIndex;\n\n  /**\n   * The latest number of slides.\n   */\n  let slideCount: number;\n\n  /**\n   * The latest `perMove` value.\n   */\n  let perMove: number;\n\n  /**\n   * The latest `perMove` value.\n   */\n  let perPage: number;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    init();\n    Move.jump( currIndex );\n\n    on( [ EVENT_UPDATED, EVENT_REFRESH ], init );\n\n    on( EVENT_SCROLLED, () => {\n      setIndex( Move.toIndex( Move.getPosition() ) );\n    }, 0 );\n  }\n\n  /**\n   * Initializes the component.\n   */\n  function init(): void {\n    slideCount = getLength( true );\n    perMove    = options.perMove;\n    perPage    = options.perPage;\n  }\n\n  /**\n   * Moves the slider by the control pattern.\n   *\n   * @see `Splide#go()`\n   *\n   * @param control        - A control pattern.\n   * @param allowSameIndex - Optional. Determines whether to allow to go to the current index or not.\n   */\n  function go( control: number | string, allowSameIndex?: boolean ): void {\n    const dest  = parse( control );\n    const index = loop( dest );\n\n    if ( index > -1 && ! Move.isBusy() && ( allowSameIndex || index !== currIndex ) ) {\n      setIndex( index );\n      Move.move( dest, index, prevIndex );\n    }\n  }\n\n  /**\n   * Parses the control and returns a slide index.\n   *\n   * @param control - A control pattern to parse.\n   */\n  function parse( control: number | string ): number {\n    let index = currIndex;\n\n    if ( isString( control ) ) {\n      const [ , indicator, number ] = control.match( /([+\\-<>])(\\d+)?/ ) || [];\n\n      if ( indicator === '+' || indicator === '-' ) {\n        index = computeDestIndex( currIndex + +`${ indicator }${ +number || 1 }`, currIndex, true );\n      } else if ( indicator === '>' ) {\n        index = number ? toIndex( +number ) : getNext( true );\n      } else if ( indicator === '<' ) {\n        index = getPrev( true );\n      }\n    } else {\n      if ( isLoop ) {\n        index = clamp( control, -perPage, slideCount + perPage - 1 );\n      } else {\n        index = clamp( control, 0, getEnd() );\n      }\n    }\n\n    return index;\n  }\n\n  /**\n   * Returns a next destination index.\n   *\n   * @param destination - Optional. Determines whether to get a destination index or a slide one.\n   *\n   * @return A next index if available, or otherwise `-1`.\n   */\n  function getNext( destination?: boolean ): number {\n    return getAdjacent( false, destination );\n  }\n\n  /**\n   * Returns a previous destination index.\n   *\n   * @param destination - Optional. Determines whether to get a destination index or a slide one.\n   *\n   * @return A previous index if available, or otherwise `-1`.\n   */\n  function getPrev( destination?: boolean ): number {\n    return getAdjacent( true, destination );\n  }\n\n  /**\n   * Returns an adjacent destination index.\n   *\n   * @param prev        - Determines whether to return a previous or next index.\n   * @param destination - Optional. Determines whether to get a destination index or a slide one.\n   *\n   * @return An adjacent index if available, or otherwise `-1`.\n   */\n  function getAdjacent( prev: boolean, destination?: boolean ): number {\n    const dest = computeDestIndex( currIndex + getPerMove() * ( prev ? -1 : 1 ), currIndex );\n    return destination ? dest : loop( dest );\n  }\n\n  /**\n   * Converts the desired destination index to the valid one.\n   * - This may return clone indices if the editor is the loop mode,\n   *   or `-1` if there is no slide to go.\n   * - There are still slides where the slider can go if borders are between `from` and `dest`.\n   *\n   * @param dest        - The desired destination.\n   * @param from        - A base index.\n   * @param incremental - Optional. Whether the control is incremental or not.\n   *\n   * @return A converted destination index, including clones.\n   */\n  function computeDestIndex( dest: number, from: number, incremental?: boolean ): number {\n    if ( isEnough() ) {\n      const end = getEnd();\n\n      // Will overrun:\n      if ( dest < 0 || dest > end ) {\n        if ( between( 0, dest, from, true ) || between( end, from, dest, true ) ) {\n          dest = toIndex( toPage( dest ) );\n        } else {\n          if ( isLoop ) {\n            dest = perMove\n              ? dest\n              : dest < 0 ? - ( slideCount % perPage || perPage ) : slideCount;\n          } else if ( options.rewind ) {\n            dest = dest < 0 ? end : 0;\n          } else {\n            dest = -1;\n          }\n        }\n      } else {\n        if ( ! isLoop && ! incremental && dest !== from ) {\n          dest = toIndex( toPage( from ) + ( dest < from ? -1 : 1 ) );\n        }\n      }\n    } else {\n      dest = -1;\n    }\n\n    return dest;\n  }\n\n  /**\n   * Returns the end index where the slider can go.\n   * For example, if the slider has 10 slides and the `perPage` option is 3,\n   * the slider can go to the slide 8 (the index is 7).\n   *\n   * @return An end index.\n   */\n  function getEnd(): number {\n    let end = slideCount - perPage;\n\n    if ( hasFocus() || ( isLoop && perMove ) ) {\n      end = slideCount - 1;\n    }\n\n    return max( end, 0 );\n  }\n\n  /**\n   * Loops the provided index only in the loop mode.\n   *\n   * @param index - An index to loop.\n   *\n   * @return A looped index.\n   */\n  function loop( index: number ): number {\n    if ( isLoop ) {\n      return isEnough() ? index % slideCount + ( index < 0 ? slideCount : 0 ) : -1;\n    }\n\n    return index;\n  }\n\n  /**\n   * Converts the page index to the slide index.\n   *\n   * @param page - A page index to convert.\n   *\n   * @return A slide index.\n   */\n  function toIndex( page: number ): number {\n    return clamp( hasFocus() ? page : perPage * page, 0, getEnd() );\n  }\n\n  /**\n   * Converts the slide index to the page index.\n   *\n   * @param index - An index to convert.\n   */\n  function toPage( index: number ): number {\n    if ( ! hasFocus() ) {\n      index = between( index, slideCount - perPage, slideCount - 1 ) ? slideCount - 1 : index;\n      index = floor( index / perPage );\n    }\n\n    return index;\n  }\n\n  /**\n   * Returns the number of slides to move for '>' and '<'.\n   *\n   * @return The number of slides to move.\n   */\n  function getPerMove(): number {\n    return perMove || hasFocus() ? 1 : perPage;\n  }\n\n  /**\n   * Sets a new index and retains old one.\n   *\n   * @param index - A new index to set.\n   */\n  function setIndex( index: number ): void {\n    if ( index !== currIndex ) {\n      prevIndex = currIndex;\n      currIndex = index;\n    }\n  }\n\n  /**\n   * Returns the current/previous index slide index.\n   *\n   * @param prev - Optional. Whether to return previous index or not.\n   */\n  function getIndex( prev?: boolean ): number {\n    return prev ? prevIndex : currIndex;\n  }\n\n  /**\n   * Verifies if the focus option is available or not.\n   *\n   * @return `true` if the slider has the focus option.\n   */\n  function hasFocus(): boolean {\n    return ! isUndefined( options.focus ) || options.isNavigation;\n  }\n\n  return {\n    mount,\n    go,\n    getNext,\n    getPrev,\n    getEnd,\n    setIndex,\n    getIndex,\n    toIndex,\n    toPage,\n    hasFocus,\n  };\n}\n", "import { ALL_ATTRIBUTES, ARIA_CONTROLS, ARIA_LABEL } from '../../constants/attributes';\nimport {\n  EVENT_ARROWS_MOUNTED,\n  EVENT_ARROWS_UPDATED,\n  EVENT_MOUNTED,\n  EVENT_MOVE,\n  EVENT_REFRESH,\n  EVENT_SCROLLED,\n  EVENT_UPDATED,\n} from '../../constants/events';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { append, before, child, create, display, parseHtml, remove, removeAttribute, setAttribute } from '../../utils';\nimport { PATH, SIZE, XML_NAME_SPACE } from './path';\n\n\n/**\n * The interface for the Arrows component.\n *\n * @since 3.0.0\n */\nexport interface ArrowsComponent extends BaseComponent {\n  arrows: { prev?: HTMLButtonElement, next?: HTMLButtonElement };\n}\n\n/**\n * The component for handling previous and next arrows.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return An Arrows component object.\n */\nexport function Arrows( Splide: Splide, Components: Components, options: Options ): ArrowsComponent {\n  const { on, bind, emit } = EventInterface( Splide );\n  const { classes, i18n } = options;\n  const { Elements, Controller } = Components;\n  const { slider, track } = Elements;\n\n  /**\n   * The wrapper element.\n   */\n  let wrapper = Elements.arrows;\n\n  /**\n   * The previous arrow element.\n   */\n  let prev = Elements.prev;\n\n  /**\n   * The next arrow element.\n   */\n  let next = Elements.next;\n\n  /**\n   * Indicates whether the component creates arrows or retrieved from the DOM.\n   */\n  let created: boolean;\n\n  /**\n   * An object with previous and next arrows.\n   */\n  const arrows: ArrowsComponent[ 'arrows' ] = {};\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    init();\n    on( EVENT_UPDATED, init );\n  }\n\n  /**\n   * Initializes the component.\n   */\n  function init(): void {\n    if ( options.arrows ) {\n      if ( ! prev || ! next ) {\n        createArrows();\n      }\n    }\n\n    if ( prev && next ) {\n      if ( ! arrows.prev ) {\n        setAttribute( prev, ARIA_CONTROLS, track.id );\n        setAttribute( next, ARIA_CONTROLS, track.id );\n\n        arrows.prev = prev;\n        arrows.next = next;\n\n        listen();\n\n        emit( EVENT_ARROWS_MOUNTED, prev, next );\n      } else {\n        display( wrapper, options.arrows === false ? 'none' : '' );\n      }\n    }\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy(): void {\n    if ( created ) {\n      remove( wrapper );\n    } else {\n      removeAttribute( prev, ALL_ATTRIBUTES );\n      removeAttribute( next, ALL_ATTRIBUTES );\n    }\n  }\n\n  /**\n   * Listens to some events.\n   */\n  function listen(): void {\n    const { go } = Controller;\n    on( [ EVENT_MOUNTED, EVENT_MOVE, EVENT_UPDATED, EVENT_REFRESH, EVENT_SCROLLED ], update );\n    bind( next, 'click', () => { go( '>' ) } );\n    bind( prev, 'click', () => { go( '<' ) } );\n  }\n\n  /**\n   * Create arrows and append them to the slider.\n   */\n  function createArrows(): void {\n    const parent = options.arrows === 'slider' && slider ? slider : Splide.root;\n\n    wrapper = create( 'div', classes.arrows );\n    prev    = createArrow( true );\n    next    = createArrow( false );\n    created = true;\n\n    append( wrapper, [ prev, next ] );\n    before( wrapper, child( parent ) );\n  }\n\n  /**\n   * Creates an arrow button.\n   *\n   * @param prev - Determines whether to create a previous or next arrow.\n   *\n   * @return A created button element.\n   */\n  function createArrow( prev: boolean ): HTMLButtonElement {\n    const arrow = `<button class=\"${ classes.arrow } ${ prev ? classes.prev : classes.next }\" type=\"button\">`\n      +\t`<svg xmlns=\"${ XML_NAME_SPACE }\" viewBox=\"0 0 ${ SIZE } ${ SIZE }\" width=\"${ SIZE }\" height=\"${ SIZE }\">`\n      + `<path d=\"${ options.arrowPath || PATH }\" />`;\n\n    return parseHtml<HTMLButtonElement>( arrow );\n  }\n\n  /**\n   * Updates status of arrows, such as `disabled` and `aria-label`.\n   */\n  function update(): void {\n    const index     = Splide.index;\n    const prevIndex = Controller.getPrev();\n    const nextIndex = Controller.getNext();\n    const prevLabel = prevIndex > -1 && index < prevIndex ? i18n.last : i18n.prev;\n    const nextLabel = nextIndex > -1 && index > nextIndex ? i18n.first : i18n.next;\n\n    prev.disabled = prevIndex < 0;\n    next.disabled = nextIndex < 0;\n\n    setAttribute( prev, ARIA_LABEL, prevLabel );\n    setAttribute( next, ARIA_LABEL, nextLabel );\n\n    emit( EVENT_ARROWS_UPDATED, prev, next, prevIndex, nextIndex );\n  }\n\n  return {\n    arrows,\n    mount,\n    destroy,\n  };\n}\n", "/**\n * The namespace for SVG elements.\n */\nexport const XML_NAME_SPACE = 'http://www.w3.org/2000/svg';\n\n/**\n * The arrow path.\n */\nexport const PATH = 'm15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z';\n\n/**\n * SVG width and height.\n */\nexport const SIZE = 40;\n", "import { ARIA_CONTROLS, ARIA_LABEL, ROLE } from '../../constants/attributes';\nimport {\n  EVENT_AUTOPLAY_PAUSE,\n  EVENT_AUTOPLAY_PLAY,\n  EVENT_AUTOPLAY_PLAYING,\n  EVENT_MOVE,\n  EVENT_REFRESH,\n  EVENT_SCROLL,\n} from '../../constants/events';\nimport { EventInterface, RequestInterval } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { isHTMLButtonElement, setAttribute, style } from '../../utils';\n\n\n/**\n * The interface for the Autoplay component.\n *\n * @since 3.0.0\n */\nexport interface AutoplayComponent extends BaseComponent {\n  play(): void;\n  pause(): void;\n  isPaused(): boolean;\n}\n\n/**\n * The component for auto playing sliders.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return An Autoplay component object.\n */\nexport function Autoplay( Splide: Splide, Components: Components, options: Options ): AutoplayComponent {\n  const { on, bind, emit } = EventInterface( Splide );\n  const { root, track, bar, play: playButton, pause: pauseButton } = Components.Elements;\n  const interval = RequestInterval( options.interval, Splide.go.bind( Splide, '>' ), update );\n  const { isPaused } = interval;\n\n  /**\n   * Indicates whether the slider is hovered or not.\n   */\n  let hovered: boolean;\n\n  /**\n   * Indicates whether one of slider elements has focus or not.\n   */\n  let focused: boolean;\n\n  /**\n   * Turns into `true` when autoplay is manually paused.\n   */\n  let paused: boolean;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    const { autoplay } = options;\n\n    if ( autoplay ) {\n      initButton( true );\n      initButton( false );\n      listen();\n\n      if ( autoplay !== 'pause' ) {\n        play();\n      }\n    }\n  }\n\n  /**\n   * Initializes a play/pause button.\n   *\n   * @param forPause - Determines whether to initialize a pause or play button.\n   */\n  function initButton( forPause: boolean ): void {\n    const button = forPause ? pauseButton : playButton;\n\n    if ( button ) {\n      if ( ! isHTMLButtonElement( button ) ) {\n        setAttribute( button, ROLE, 'button' );\n      }\n\n      setAttribute( button, ARIA_CONTROLS, track.id );\n      setAttribute( button, ARIA_LABEL, options.i18n[ forPause ? 'pause' : 'play' ] );\n\n      bind( button, 'click', forPause ? pause : play );\n    }\n  }\n\n  /**\n   * Listens to some events.\n   */\n  function listen(): void {\n    if ( options.pauseOnHover ) {\n      bind( root, 'mouseenter mouseleave', e => {\n        hovered = e.type === 'mouseenter';\n        autoToggle();\n      } );\n    }\n\n    if ( options.pauseOnFocus ) {\n      bind( root, 'focusin focusout', e => {\n        focused = e.type === 'focusin';\n        autoToggle();\n      } );\n    }\n\n    on( [ EVENT_MOVE, EVENT_SCROLL, EVENT_REFRESH ], interval.rewind );\n  }\n\n  /**\n   * Starts autoplay and clears all flags.\n   */\n  function play(): void {\n    if ( isPaused() && Components.Slides.isEnough() ) {\n      interval.start( ! options.resetProgress );\n      focused = false;\n      hovered = false;\n      emit( EVENT_AUTOPLAY_PLAY );\n    }\n  }\n\n  /**\n   * Pauses autoplay.\n   *\n   * @param manual - If `true`, autoplay keeps paused until `play()` is explicitly called.\n   */\n  function pause( manual = true ): void {\n    if ( ! isPaused() ) {\n      interval.pause();\n      emit( EVENT_AUTOPLAY_PAUSE );\n    }\n\n    paused = manual;\n  }\n\n  /**\n   * Toggles play/pause according to current flags.\n   * If autoplay is manually paused, this will do nothing.\n   */\n  function autoToggle(): void {\n    if ( ! paused ) {\n      if ( ! hovered && ! focused ) {\n        play();\n      } else {\n        pause( false );\n      }\n    }\n  }\n\n  /**\n   * Called on every animation frame when auto playing.\n   *\n   * @param rate - The progress rate between 0 to 1.\n   */\n  function update( rate: number ): void {\n    emit( EVENT_AUTOPLAY_PLAYING, rate );\n\n    if ( bar ) {\n      style( bar, { width: `${ rate * 100 }%` } );\n    }\n  }\n\n  return {\n    mount,\n    destroy: interval.cancel,\n    play,\n    pause,\n    isPaused,\n  };\n}\n", "import { EVENT_LAZYLOAD_LOADED, EVENT_MOUNTED, EVENT_REFRESH, EVENT_UPDATED } from '../../constants/events';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { child, display } from '../../utils';\nimport { SlideComponent } from '../Slides/Slide';\n\n\n/**\n * The interface for the Cover component.\n *\n * @since 3.0.0\n */\nexport interface CoverComponent extends BaseComponent {\n}\n\n/**\n * The component for setting the image as the slide background.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Cover component object.\n */\nexport function Cover( Splide: Splide, Components: Components, options: Options ): CoverComponent {\n  const { on } = EventInterface( Splide );\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    if ( options.cover ) {\n      on( EVENT_LAZYLOAD_LOADED, ( img, Slide ) => { toggle( true, img, Slide ) } );\n      on( [ EVENT_MOUNTED, EVENT_UPDATED, EVENT_REFRESH ], apply.bind( null, true ) );\n    }\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy(): void {\n    apply( false );\n  }\n\n  /**\n   * Sets/removes the background image to/from all slides.\n   *\n   * @param cover - If `false`, removes the background image.\n   */\n  function apply( cover: boolean ): void {\n    Components.Slides.forEach( Slide => {\n      const img = child<HTMLImageElement>( Slide.container || Slide.slide, 'img' );\n\n      if ( img && img.src ) {\n        toggle( cover, img, Slide );\n      }\n    } );\n  }\n\n  /**\n   * Sets/removes the background image to/from the parent element.\n   *\n   * @param cover - If `false`, removes the background image.\n   * @param img   - A target image element.\n   * @param Slide - A SlideComponent object where the image belongs.\n   */\n  function toggle( cover: boolean, img: HTMLImageElement, Slide: SlideComponent ): void {\n    Slide.rule( 'background', cover ? `center/cover no-repeat url(\"${ img.src }\")` : '', true );\n    display( img, cover ? 'none' : '' );\n  }\n\n  return {\n    mount,\n    destroy,\n  };\n}\n", "import { EVENT_MOVE, EVENT_REFRESH, EVENT_SCROLL, EVENT_SCROLLED, EVENT_UPDATED } from '../../constants/events';\nimport { SLIDE } from '../../constants/types';\nimport { EventInterface, RequestInterval, RequestIntervalInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { abs, max } from '../../utils';\nimport { BASE_VELOCITY, BOUNCE_DIFF_THRESHOLD, BOUNCE_DURATION, FRICTION_FACTOR, MIN_DURATION } from './constants';\n\n\n/**\n * The interface for the Scroll component.\n *\n * @since 3.0.0\n */\nexport interface ScrollComponent extends BaseComponent {\n  scroll( position: number, duration?: number ): void;\n  cancel(): void;\n}\n\n/**\n * The component for scrolling the slider.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Scroll component object.\n */\nexport function Scroll( Splide: Splide, Components: Components, options: Options ): ScrollComponent {\n  const { on, emit } = EventInterface( Splide );\n  const { Move } = Components;\n  const { getPosition, getLimit } = Move;\n\n  /**\n   * Retains the active RequestInterval object.\n   */\n  let interval: RequestIntervalInterface;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    on( EVENT_MOVE, clear );\n    on( [ EVENT_UPDATED, EVENT_REFRESH ], cancel );\n  }\n\n  /**\n   * Scrolls the slider to the provided destination.\n   *\n   * @param destination        - The destination to scroll to.\n   * @param duration           - Optional. The scroll duration. If omitted, calculates it by the distance.\n   * @param suppressConstraint - Optional. Whether to suppress constraint process when the slider exceeds bounds.\n   */\n  function scroll( destination: number, duration?: number, suppressConstraint?: boolean ): void {\n    const start = getPosition();\n    let friction = 1;\n\n    duration = duration || computeDuration( abs( destination - start ) );\n    clear();\n\n    interval = RequestInterval( duration, onScrolled, rate => {\n      const position = getPosition();\n      const target   = start + ( destination - start ) * easing( rate );\n      const diff     = ( target - getPosition() ) * friction;\n\n      Move.translate( position + diff );\n\n      if ( Splide.is( SLIDE ) && ! suppressConstraint && Move.isExceeded() ) {\n        friction *= FRICTION_FACTOR;\n\n        if ( abs( diff ) < BOUNCE_DIFF_THRESHOLD ) {\n          bounce( Move.isExceededMin( getPosition() ) );\n        }\n      }\n    }, 1 );\n\n    emit( EVENT_SCROLL );\n    interval.start();\n  }\n\n  /**\n   * Triggers the bounce effect when the slider reaches bounds.\n   *\n   * @param backwards - The direction the slider is going towards.\n   */\n  function bounce( backwards: boolean ): void {\n    scroll( getLimit( ! backwards ), BOUNCE_DURATION, true );\n  }\n\n  /**\n   * Called when scroll ends or is canceled.\n   */\n  function onScrolled(): void {\n    emit( EVENT_SCROLLED );\n  }\n\n  /**\n   * Computes the scroll duration by the distance and the base velocity.\n   *\n   * @param distance - Distance in pixel.\n   *\n   * @return The duration for scroll.\n   */\n  function computeDuration( distance: number ): number {\n    return max( distance / BASE_VELOCITY, MIN_DURATION );\n  }\n\n  /**\n   * Clears the active interval.\n   */\n  function clear(): void {\n    if ( interval ) {\n      interval.cancel();\n    }\n  }\n\n  /**\n   * Cancels the active interval and emits the `scrolled` event.\n   */\n  function cancel(): void {\n    if ( interval && ! interval.isPaused() ) {\n      clear();\n      onScrolled();\n    }\n  }\n\n  /**\n   * The easing function.\n   *\n   * @param t - A value to ease.\n   *\n   * @return An eased value.\n   */\n  function easing( t: number ): number {\n    const { easingFunc } = options;\n    return easingFunc ? easingFunc( t ) : 1 - Math.pow( 1 - t, 4 );\n  }\n\n  return {\n    mount,\n    destroy: clear,\n    scroll,\n    cancel,\n  };\n}\n", "/**\n * Triggers the bounce effect when the diff becomes less than this value.\n *\n * @since 3.0.0\n */\nexport const BOUNCE_DIFF_THRESHOLD = 10;\n\n/**\n * The duration of the bounce effect.\n *\n * @since 3.0.0\n */\nexport const BOUNCE_DURATION = 600;\n\n/**\n * The friction factor.\n *\n * @since 3.0.0\n */\nexport const FRICTION_FACTOR = 0.6;\n\n/**\n * The velocity to calculate the scroll duration.\n *\n * @since 3.0.0\n */\nexport const BASE_VELOCITY = 1.2;\n\n/**\n * The minimum duration of scroll.\n *\n * @since 3.0.0\n */\nexport const MIN_DURATION = 800;\n", "import { EVENT_DRAG, EVENT_DRAGGED, EVENT_DRAGGING } from '../../constants/events';\r\nimport { FADE, LOOP, SLIDE } from '../../constants/types';\r\nimport { EventInterface } from '../../constructors';\r\nimport { Splide } from '../../core/Splide/Splide';\r\nimport { BaseComponent, Components, Options } from '../../types';\r\nimport { abs, clamp, min, prevent, sign } from '../../utils';\r\nimport { FRICTION, POINTER_DOWN_EVENTS, POINTER_MOVE_EVENTS, POINTER_UP_EVENTS, SAMPLING_INTERVAL } from './constants';\r\n\r\n\r\n/**\r\n * The interface for the Drag component.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport interface DragComponent extends BaseComponent {\r\n}\r\n\r\n/**\r\n * The component for dragging the slider.\r\n *\r\n * @since 3.0.0\r\n *\r\n * @param Splide     - A Splide instance.\r\n * @param Components - A collection of components.\r\n * @param options    - Options.\r\n *\r\n * @return A Drag component object.\r\n */\r\nexport function Drag( Splide: Splide, Components: Components, options: Options ): DragComponent {\r\n  const { emit, bind, unbind } = EventInterface( Splide );\r\n  const { track } = Components.Elements;\r\n  const { resolve, orient } = Components.Direction;\r\n  const { listSize } = Components.Layout;\r\n  const { go, getEnd } = Components.Controller;\r\n  const { Move, Scroll } = Components;\r\n  const { translate, toIndex, getPosition, isExceeded } = Move;\r\n  const isSlide = Splide.is( SLIDE );\r\n  const isFade  = Splide.is( FADE );\r\n  const isFree  = options.drag === 'free';\r\n\r\n  /**\r\n   * The coord where a pointer becomes active.\r\n   */\r\n  let startCoord: number;\r\n\r\n  /**\r\n   * Keeps the last time when the component detects dragging.\r\n   */\r\n  let lastTime: number;\r\n\r\n  /**\r\n   * The base slider position where the diff of coords is applied.\r\n   */\r\n  let basePosition: number;\r\n\r\n  /**\r\n   * The base coord to calculate the diff of coords.\r\n   */\r\n  let baseCoord: number;\r\n\r\n  /**\r\n   * The base time when the base position and the base coord are saved.\r\n   */\r\n  let baseTime: number;\r\n\r\n  /**\r\n   * Keeps the last TouchEvent/MouseEvent object.\r\n   */\r\n  let lastEvent: TouchEvent | MouseEvent;\r\n\r\n  /**\r\n   * Indicates whether the user is dragging the slider or not.\r\n   */\r\n  let moving: boolean;\r\n\r\n  /**\r\n   * Indicates whether the user drags the slider by the mouse or not.\r\n   */\r\n  let isMouse: boolean;\r\n\r\n  /**\r\n   * The target element to attach listeners.\r\n   */\r\n  let target: Window | HTMLElement;\r\n\r\n  /**\r\n   * Indicates whether the slider exceeds borders or not.\r\n   */\r\n  let exceeded: boolean;\r\n\r\n  /**\r\n   * Called when the component is mounted.\r\n   */\r\n  function mount(): void {\r\n    if ( options.drag ) {\r\n      bind( track, POINTER_DOWN_EVENTS, onPointerDown );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Called when the user clicks or touches the slider.\r\n   *\r\n   * @param e - A TouchEvent or MouseEvent object\r\n   */\r\n  function onPointerDown( e: TouchEvent | MouseEvent ): void {\r\n    isMouse = e.type === 'mousedown';\r\n    target  = isMouse ? window : track;\r\n\r\n    if ( ! ( isMouse && ( e as MouseEvent ).button ) ) {\r\n      if ( ! Move.isBusy() ) {\r\n        bind( target, POINTER_MOVE_EVENTS, onPointerMove );\r\n        bind( target, POINTER_UP_EVENTS, onPointerUp );\r\n        Move.cancel();\r\n        Scroll.cancel();\r\n        startCoord = getCoord( e );\r\n      } else {\r\n        prevent( e );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Called while the user moves the pointer on the slider.\r\n   *\r\n   * @param e - A TouchEvent or MouseEvent object\r\n   */\r\n  function onPointerMove( e: TouchEvent | MouseEvent ): void {\r\n    if ( e.cancelable ) {\r\n      const min = options.dragMinThreshold || 15;\r\n\r\n      if ( isMouse || abs( getCoord( e ) - startCoord ) > min ) {\r\n        moving = true;\r\n        onDrag();\r\n      }\r\n\r\n      if ( moving ) {\r\n        onDragging( e );\r\n        prevent( e, true );\r\n      }\r\n    } else {\r\n      onPointerUp( e );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Called when the user releases pointing devices.\r\n   * Be aware that the TouchEvent object provided by the `touchend` does not contain `Touch` objects,\r\n   * which means the last touch position is not available.\r\n   *\r\n   * @param e - A TouchEvent or MouseEvent object\r\n   */\r\n  function onPointerUp( e: TouchEvent | MouseEvent ): void {\r\n    unbind( target, `${ POINTER_MOVE_EVENTS } ${ POINTER_UP_EVENTS }` );\r\n    moving = false;\r\n\r\n    if ( lastEvent ) {\r\n      onDragged( e );\r\n      lastEvent = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Called when the user starts dragging the slider.\r\n   */\r\n  function onDrag(): void {\r\n    bind( track, 'click', e => {\r\n      unbind( track, 'click' );\r\n      prevent( e, true );\r\n    }, { capture: true } );\r\n\r\n    emit( EVENT_DRAG );\r\n  }\r\n\r\n  /**\r\n   * Called while the user is dragging the slider.\r\n   *\r\n   * @param e - A TouchEvent or MouseEvent object\r\n   */\r\n  function onDragging( e: TouchEvent | MouseEvent ): void {\r\n    const { timeStamp } = e;\r\n    const expired = ! lastTime || ( timeStamp - lastTime > SAMPLING_INTERVAL );\r\n\r\n    if ( expired || isExceeded() !== exceeded ) {\r\n      basePosition = getPosition();\r\n      baseCoord    = getCoord( e );\r\n      baseTime     = timeStamp;\r\n    }\r\n\r\n    exceeded  = isExceeded();\r\n    lastTime  = timeStamp;\r\n    lastEvent = e;\r\n\r\n    if ( ! isFade ) {\r\n      translate( basePosition + constrain( getCoord( e ) - baseCoord ) );\r\n    }\r\n\r\n    emit( EVENT_DRAGGING );\r\n  }\r\n\r\n  /**\r\n   * Called when the user finishes dragging.\r\n   *\r\n   * @param e - A TouchEvent or MouseEvent object\r\n   */\r\n  function onDragged( e: TouchEvent | MouseEvent ): void {\r\n    const velocity = computeVelocity( e );\r\n\r\n    if ( isFade ) {\r\n      go( Splide.index + orient( sign( velocity ) ) );\r\n    } else {\r\n      const destination = computeDestination( velocity );\r\n\r\n      if ( isFree ) {\r\n        Scroll.scroll( destination );\r\n      } else {\r\n        go( computeIndex( destination ), true );\r\n      }\r\n    }\r\n\r\n    lastTime = 0;\r\n    emit( EVENT_DRAGGED );\r\n  }\r\n\r\n  /**\r\n   * Computes the drag velocity.\r\n   *\r\n   * @param e - A TouchEvent or MouseEvent object\r\n   *\r\n   * @return The drag velocity.\r\n   */\r\n  function computeVelocity( e: TouchEvent | MouseEvent ): number {\r\n    if ( Splide.is( LOOP ) || ! isExceeded() ) {\r\n      const diffCoord = getCoord( lastEvent ) - baseCoord;\r\n      const diffTime  = lastEvent.timeStamp - baseTime;\r\n      const isFlick   = e.timeStamp - lastTime < SAMPLING_INTERVAL;\r\n\r\n      if ( diffTime && isFlick ) {\r\n        return diffCoord / diffTime;\r\n      }\r\n    }\r\n\r\n    return 0;\r\n  }\r\n\r\n  /**\r\n   * Computes the destination by the velocity and the `flickPower` option.\r\n   *\r\n   * @param velocity - The drag velocity.\r\n   *\r\n   * @return The destination.\r\n   */\r\n  function computeDestination( velocity: number ): number {\r\n    const flickPower = options.flickPower || 600;\r\n\r\n    return getPosition() + sign( velocity ) * min(\r\n      abs( velocity ) * flickPower,\r\n      isFree ? Infinity : listSize() * ( options.flickMaxPages || 1 )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Converts the destination to the slide index.\r\n   *\r\n   * @param destination - The target destination.\r\n   *\r\n   * @return The destination index.\r\n   */\r\n  function computeIndex( destination: number ): number {\r\n    const dest = toIndex( destination );\r\n    return isSlide ? clamp( dest, 0, getEnd() ) : dest;\r\n  }\r\n\r\n  /**\r\n   * Returns the `pageX` and `pageY` coordinates provided by the event.\r\n   * Be aware that IE does not support both TouchEvent and MouseEvent constructors.\r\n   *\r\n   * @param e - A TouchEvent or MouseEvent object.\r\n   *\r\n   * @return A pageX or pageY coordinate.\r\n   */\r\n  function getCoord( e: TouchEvent | MouseEvent ): number {\r\n    return ( isMouse ? e : ( e as TouchEvent ).touches[ 0 ] )[ resolve( 'pageX' ) ];\r\n  }\r\n\r\n  /**\r\n   * Reduces the distance to move by the predefined friction.\r\n   * This does nothing when the slider type is not `slide`, or the position is inside borders.\r\n   *\r\n   * @param diff - Diff to constrain.\r\n   *\r\n   * @return The constrained diff.\r\n   */\r\n  function constrain( diff: number ): number {\r\n    return diff / ( exceeded && isSlide ? FRICTION : 1 );\r\n  }\r\n\r\n  return {\r\n    mount,\r\n  };\r\n}\r\n", "import { ROLE } from '../../constants/attributes';\nimport { CLASS_LOADING } from '../../constants/classes';\nimport {\n  EVENT_LAZYLOAD_LOADED,\n  EVENT_MOUNTED,\n  EVENT_MOVED,\n  EVENT_REFRESH,\n  EVENT_RESIZE,\n} from '../../constants/events';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { BaseComponent, Components, Options } from '../../types';\nimport {\n  addClass,\n  create,\n  display,\n  getAttribute,\n  queryAll,\n  remove,\n  removeAttribute,\n  removeClass,\n  setAttribute,\n} from '../../utils';\nimport { SlideComponent } from '../Slides/Slide';\nimport { IMAGE_SELECTOR, SRC_DATA_ATTRIBUTE, SRCSET_DATA_ATTRIBUTE } from './constants';\n\n\n/**\n * The interface for the LazyLoad component.\n *\n * @since 3.0.0\n */\nexport interface LazyLoadComponent extends BaseComponent {\n}\n\n/**\n * The interface for all components.\n *\n * @since 3.0.0\n */\nexport interface LazyLoadImagesData {\n  img: HTMLImageElement;\n  spinner: HTMLSpanElement;\n  Slide: SlideComponent;\n  src: string | null;\n  srcset: string | null;\n}\n\n/**\n * The component for lazily loading images.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return An LazyLoad component object.\n */\nexport function LazyLoad( Splide: Splide, Components: Components, options: Options ): LazyLoadComponent {\n  const { on, off, bind, emit } = EventInterface( Splide );\n  const isSequential = options.lazyLoad === 'sequential';\n\n  /**\n   * Stores data of images.\n   */\n  let images: LazyLoadImagesData[] = [];\n\n  /**\n   * The current index of images.\n   */\n  let index = 0;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    if ( options.lazyLoad ) {\n      on( [ EVENT_MOUNTED, EVENT_REFRESH ], () => {\n        destroy();\n        init();\n      } );\n\n      if ( ! isSequential ) {\n        on( [ EVENT_MOUNTED, EVENT_REFRESH, EVENT_MOVED ], observe );\n      }\n    }\n  }\n\n  /**\n   * Finds images that contain specific data attributes.\n   */\n  function init() {\n    Components.Slides.forEach( Slide => {\n      queryAll<HTMLImageElement>( Slide.slide, IMAGE_SELECTOR ).forEach( img => {\n        const src    = getAttribute( img, SRC_DATA_ATTRIBUTE );\n        const srcset = getAttribute( img, SRCSET_DATA_ATTRIBUTE );\n\n        if ( src !== img.src || srcset !== img.srcset ) {\n          const spinner = create( 'span', options.classes.spinner, img.parentElement );\n          setAttribute( spinner, ROLE, 'presentation' );\n          images.push( { img, Slide, src, srcset, spinner } );\n          display( img, 'none' );\n        }\n      } );\n    } );\n\n    if ( isSequential ) {\n      loadNext();\n    }\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy() {\n    index  = 0;\n    images = [];\n  }\n\n  /**\n   * Checks how close each image is from the active slide, and determines whether to start loading or not.\n   * The last `+1` is for the current page.\n   */\n  function observe(): void {\n    images = images.filter( data => {\n      if ( data.Slide.isWithin( Splide.index, options.perPage * ( ( options.preloadPages || 1 ) + 1 ) ) ) {\n        return load( data );\n      }\n\n      return true;\n    } );\n\n    if ( ! images.length ) {\n      off( EVENT_MOVED );\n    }\n  }\n\n  /**\n   * Starts loading the image in the data.\n   *\n   * @param data - A LazyLoadImagesData object.\n   */\n  function load( data: LazyLoadImagesData ): void {\n    const { img } = data;\n\n    addClass( data.Slide.slide, CLASS_LOADING );\n    bind( img, 'load error', e => { onLoad( data, e.type === 'error' ) } );\n\n    [ 'src', 'srcset' ].forEach( name => {\n      if ( data[ name ] ) {\n        setAttribute( img, name, data[ name ] );\n        removeAttribute( img, name === 'src' ? SRC_DATA_ATTRIBUTE : SRCSET_DATA_ATTRIBUTE );\n      }\n    } );\n  }\n\n  /**\n   * Called when the image is loaded or any error occurs.\n   *\n   * @param data  - A LazyLoadImagesData object.\n   * @param error - `true` if this method is called on error.\n   */\n  function onLoad( data: LazyLoadImagesData, error: boolean ): void {\n    const { Slide } = data;\n\n    removeClass( Slide.slide, CLASS_LOADING );\n\n    if ( ! error ) {\n      remove( data.spinner );\n      display( data.img, '' );\n      emit( EVENT_LAZYLOAD_LOADED, data.img, Slide );\n      emit( EVENT_RESIZE );\n    }\n\n    if ( isSequential ) {\n      loadNext();\n    }\n  }\n\n  /**\n   * Starts loading a next image.\n   */\n  function loadNext(): void {\n    if ( index < images.length ) {\n      load( images[ index++ ] );\n    }\n  }\n\n  return {\n    mount,\n    destroy,\n  };\n}\n", "import { ARIA_CONTROLS, ARIA_CURRENT, ARIA_LABEL } from '../../constants/attributes';\nimport { CLASS_ACTIVE } from '../../constants/classes';\nimport {\n  EVENT_MOVE, EVENT_PAGINATION_MOUNTED,\n  EVENT_PAGINATION_PAGE, EVENT_PAGINATION_UPDATED,\n  EVENT_REFRESH,\n  EVENT_SCROLLED,\n  EVENT_UPDATED,\n} from '../../constants/events';\nimport { Splide } from '../../core/Splide/Splide';\nimport { EventInterface } from '../../constructors';\nimport { BaseComponent, Components, Options } from '../../types';\nimport { addClass, ceil, create, empty, remove, removeAttribute, removeClass, setAttribute } from '../../utils';\nimport { format } from '../../utils';\n\n\n/**\n * The interface for the Pagination component.\n *\n * @since 3.0.0\n */\nexport interface PaginationComponent extends BaseComponent {\n  items: PaginationItem[];\n  getAt( index: number ): PaginationItem;\n}\n\n/**\n * The interface for each pagination item.\n *\n * @since 3.0.0\n */\nexport interface PaginationItem {\n  li: HTMLLIElement;\n  button: HTMLButtonElement;\n  page: number;\n}\n\n/**\n * The component for handling previous and next arrows.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Arrows component object.\n */\nexport function Pagination( Splide: Splide, Components: Components, options: Options ): PaginationComponent {\n  const { on, emit, bind, unbind } = EventInterface( Splide );\n  const { Slides } = Components;\n  const { go, toPage, hasFocus, getIndex } = Components.Controller;\n\n  /**\n   * Stores all pagination items.\n   */\n  const items: PaginationItem[] = [];\n\n  /**\n   * The pagination element.\n   */\n  let list: HTMLUListElement;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    init();\n    on( [ EVENT_UPDATED, EVENT_REFRESH ], init );\n    on( [ EVENT_MOVE, EVENT_SCROLLED ], update );\n  }\n\n  /**\n   * Initializes the pagination.\n   */\n  function init(): void {\n    destroy();\n\n    if ( options.pagination && Slides.isEnough() ) {\n      createPagination();\n      emit( EVENT_PAGINATION_MOUNTED, { list, items }, getAt( Splide.index ) );\n      update();\n    }\n  }\n\n  /**\n   * Destroys the component.\n   */\n  function destroy(): void {\n    if ( list ) {\n      remove( list );\n      items.forEach( item => { unbind( item.button, 'click' ) } );\n      empty( items );\n      list = null;\n    }\n  }\n\n  /**\n   * Creates the pagination element and appends it to the slider.\n   */\n  function createPagination(): void {\n    const { length } = Splide;\n    const { classes, i18n, perPage } = options;\n    const { slider, root } = Components.Elements;\n    const parent = options.pagination === 'slider' && slider ? slider : root;\n    const max    = hasFocus() ? length : ceil( length / perPage );\n\n    list = create( 'ul', classes.pagination, parent );\n\n    for ( let i = 0; i < max; i++ ) {\n      const li       = create( 'li', null, list );\n      const button   = create( 'button', { class: classes.page, type: 'button' }, li );\n      const controls = Slides.getIn( i ).map( Slide => Slide.slide.id );\n      const text     = ! hasFocus() && perPage > 1 ? i18n.pageX : i18n.slideX;\n\n      bind( button, 'click', () => { go( `>${ i }` ) } );\n\n      setAttribute( button, ARIA_CONTROLS, controls.join( ' ' ) );\n      setAttribute( button, ARIA_LABEL, format( text, i + 1 ) );\n\n      emit( EVENT_PAGINATION_PAGE, list, li, button, i );\n\n      items.push( { li, button, page: i } );\n    }\n  }\n\n  /**\n   * Returns the pagination item at the specified index.\n   *\n   * @param index - An index.\n   *\n   * @return A pagination item object if available, or otherwise `undefined`.\n   */\n  function getAt( index: number ): PaginationItem | undefined {\n    return items[ toPage( index ) ];\n  }\n\n  /**\n   * Updates the pagination status.\n   */\n  function update(): void {\n    const prev = getAt( getIndex( true ) );\n    const curr = getAt( getIndex() );\n\n    if ( prev ) {\n      removeClass( prev.button, CLASS_ACTIVE );\n      removeAttribute( prev.button, ARIA_CURRENT );\n    }\n\n    if ( curr ) {\n      addClass( curr.button, CLASS_ACTIVE );\n      setAttribute( curr.button, ARIA_CURRENT, true );\n    }\n\n    emit( EVENT_PAGINATION_UPDATED, { list, items }, prev, curr );\n  }\n\n  return {\n    items,\n    mount,\n    destroy,\n    getAt,\n  };\n}\n", "import { EventInterface } from '../../constructors';\r\nimport { Splide } from '../../core/Splide/Splide';\r\nimport { BaseComponent, Components, Options } from '../../types';\r\nimport { prevent } from '../../utils';\r\n\r\n\r\n/**\r\n * The interface for the Wheel component.\r\n *\r\n * @since 3.0.0\r\n */\r\nexport interface WheelComponent extends BaseComponent {\r\n}\r\n\r\n/**\r\n * The component for observing the mouse wheel and moving the slider.\r\n *\r\n * @since 3.0.0\r\n *\r\n * @param Splide     - A Splide instance.\r\n * @param Components - A collection of components.\r\n * @param options    - Options.\r\n *\r\n * @return A Wheel component object.\r\n */\r\nexport function Wheel( Splide: Splide, Components: Components, options: Options ): WheelComponent {\r\n  const { bind } = EventInterface( Splide );\r\n\r\n  /**\r\n   * Called when the component is mounted.\r\n   */\r\n  function mount(): void {\r\n    if ( options.wheel ) {\r\n      bind( Components.Elements.track, 'wheel', onWheel );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Called when the user rotates the mouse wheel.\r\n   *\r\n   * @param e - A WheelEvent object.\r\n   */\r\n  function onWheel( e: WheelEvent ): void {\r\n    const { deltaY } = e;\r\n\r\n    if ( deltaY ) {\r\n      Splide.go( deltaY < 0 ? '<' : '>' );\r\n      prevent( e );\r\n    }\r\n  }\r\n\r\n  return {\r\n    mount,\r\n  };\r\n}\r\n", "import { Options } from '../types';\nimport { CLASSES } from './classes';\nimport { I18N } from './i18n';\n\n\n/**\n * The collection of default options.\n * Note that this collection does not contain all options.\n *\n * @since 3.0.0\n */\nexport const DEFAULTS: Options = {\n  type             : 'slide',\n  speed            : 400,\n  waitForTransition: true,\n  perPage          : 1,\n  arrows           : true,\n  pagination       : true,\n  interval         : 5000,\n  pauseOnHover     : true,\n  pauseOnFocus     : true,\n  resetProgress    : true,\n  easing           : 'cubic-bezier(.42,.65,.27,.99)',\n  drag             : true,\n  direction        : 'ltr',\n  slideFocus       : true,\n  trimSpace        : true,\n  classes          : CLASSES,\n  i18n             : I18N,\n};\n", "/**\n * The collection of i18n strings.\n *\n * @since 3.0.0\n */\nexport const I18N = {\n  prev  : 'Previous slide',\n  next  : 'Next slide',\n  first : 'Go to first slide',\n  last  : 'Go to last slide',\n  slideX: 'Go to slide %s',\n  pageX : 'Go to page %s',\n  play  : 'Start autoplay',\n  pause : 'Pause autoplay',\n};\n", "import { EVENT_MOUNTED, EVENT_REFRESH } from '../../constants/events';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { Components, Options, TransitionComponent } from '../../types';\nimport { nextTick, noop, rect, unit } from '../../utils';\n\n\n/**\n * The component for the fade transition.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Transition component object.\n */\nexport function Fade( Splide: Splide, Components: Components, options: Options ): TransitionComponent {\n  const { on } = EventInterface( Splide );\n  const { ruleBy } = Components.Style;\n\n  /**\n   * Called when the component is mounted.\n   * The nextTick disables the initial fade transition of the first slide.\n   */\n  function mount(): void {\n    on( [ EVENT_MOUNTED, EVENT_REFRESH ], () => {\n      nextTick( () => {\n        Components.Slides.forEach( Slide => {\n          ruleBy( Slide.slide, 'transition', `opacity ${ options.speed }ms ${ options.easing }` );\n        } );\n      } );\n    } );\n  }\n\n  /**\n   * Starts the transition.\n   * Explicitly sets the track height to avoid it will collapse in Safari.\n   *\n   * @param index - A destination index.\n   * @param done  - The callback function that must be called after the transition ends.\n   */\n  function start( index: number, done: () => void ): void {\n    const { track } = Components.Elements;\n    ruleBy( track, 'height', unit( rect( track ).height ) );\n\n    nextTick( () => {\n      done();\n      ruleBy( track, 'height', '' );\n    } );\n  }\n\n  return {\n    mount,\n    start,\n    cancel: noop,\n  };\n}\n", "import { SLIDE } from '../../constants/types';\nimport { EventInterface } from '../../constructors';\nimport { Splide } from '../../core/Splide/Splide';\nimport { Components, Options, TransitionComponent } from '../../types';\nimport { abs } from '../../utils';\n\n\n/**\n * The component for the slide transition.\n *\n * @since 3.0.0\n *\n * @param Splide     - A Splide instance.\n * @param Components - A collection of components.\n * @param options    - Options.\n *\n * @return A Transition component object.\n */\nexport function Slide( Splide: Splide, Components: Components, options: Options ): TransitionComponent {\n  const { bind } = EventInterface( Splide );\n  const { Move, Controller } = Components;\n  const { list } = Components.Elements;\n\n  /**\n   * Holds the `done` callback function.\n   */\n  let endCallback: () => void;\n\n  /**\n   * Called when the component is mounted.\n   */\n  function mount(): void {\n    bind( list, 'transitionend', e => {\n      if ( e.target === list && endCallback ) {\n        cancel();\n        endCallback();\n      }\n    } );\n  }\n\n  /**\n   * Starts the transition.\n   * The Move component calls this method just before the slider moves.\n   *\n   * @param index - A destination index.\n   * @param done  - The callback function that must be called after the transition ends.\n   */\n  function start( index: number, done: () => void ): void {\n    const destination = Move.toPosition( index, true );\n    const position    = Move.getPosition();\n    const speed       = getSpeed( index );\n\n    if ( abs( destination - position ) >= 1 && speed >= 1 ) {\n      apply( `transform ${ speed }ms ${ options.easing }` );\n      Move.translate( destination );\n      endCallback = done;\n    } else {\n      Move.jump( index );\n      done();\n    }\n  }\n\n  /**\n   * Cancels the transition.\n   */\n  function cancel(): void {\n    apply( '' );\n  }\n\n  /**\n   * Returns the transition speed.\n   *\n   * @param index - A destination index.\n   */\n  function getSpeed( index: number ): number {\n    const { rewindSpeed } = options;\n\n    if ( Splide.is( SLIDE ) && rewindSpeed ) {\n      const prev = Controller.getIndex( true );\n      const end  = Controller.getEnd();\n\n      if ( ( prev === 0 && index >= end ) || ( prev >= end && index === 0 ) ) {\n        return rewindSpeed;\n      }\n    }\n\n    return options.speed;\n  }\n\n  /**\n   * Applies the transition CSS property to the list element.\n   *\n   * @param transition - A transition CSS value.\n   */\n  function apply( transition: string ): void {\n    Components.Style.ruleBy( list, 'transition', transition );\n  }\n\n  return {\n    mount,\n    start,\n    cancel,\n  };\n}\n", "import * as ComponentConstructors from '../../components';\nimport { <PERSON><PERSON><PERSON>atcher } from '../../components/Slides/Slides';\nimport { CLASS_INITIALIZED } from '../../constants/classes';\nimport { DEFAULTS } from '../../constants/defaults';\nimport { EVENT_DESTROY, EVENT_MOUNTED, EVENT_READY, EVENT_REFRESH, EVENT_UPDATED } from '../../constants/events';\nimport { CREATED, DESTROYED, IDLE, STATES } from '../../constants/states';\nimport { FADE } from '../../constants/types';\nimport { EventBus, EventBusCallback, EventBusObject, State, StateObject } from '../../constructors';\nimport { Fade, Slide } from '../../transitions';\nimport { ComponentConstructor, Components, Options } from '../../types';\nimport { addClass, assert, assign, empty, forOwn, isString, merge, query } from '../../utils';\n\n\n/**\n * The frontend class for the Splide slider.\n *\n * @since 3.0.0\n */\nexport class Splide {\n  /**\n   * Changes the default options for all Splide instances.\n   */\n  static defaults: Options = {};\n\n  /**\n   * The collection of state numbers.\n   */\n  static readonly STATES = STATES;\n\n  /**\n   * The root element where the Splide is applied.\n   */\n  readonly root: HTMLElement;\n\n  /**\n   * The EventBusObject object.\n   */\n  readonly event: EventBusObject = EventBus();\n\n  /**\n   * The collection of all component objects.\n   */\n  readonly Components: Components = {} as Components;\n\n  /**\n   * The StateObject object.\n   */\n  readonly state: StateObject = State( CREATED );\n\n  /**\n   * Splide instances to sync with.\n   */\n  readonly splides: Splide[] = [];\n\n  /**\n   * The collection of options.\n   */\n  private readonly opts: Options = {};\n\n  /**\n   * The collection of extensions.\n   */\n  private Extensions: Record<string, ComponentConstructor> = {};\n\n  /**\n   * The Transition component.\n   */\n  private Transition: ComponentConstructor;\n\n  /**\n   * The Splide constructor.\n   *\n   * @param target  - The selector for the target element, or the element itself.\n   * @param options - Optional. An object with options.\n   */\n  constructor( target: string | HTMLElement, options?: Options ) {\n    const root = isString( target ) ? query<HTMLElement>( document, target ) : target;\n    assert( root, `${ root } is invalid.` );\n\n    this.root = root;\n\n    merge( DEFAULTS, Splide.defaults );\n    merge( merge( this.opts, DEFAULTS ), options || {} );\n  }\n\n  /**\n   * Initializes the instance.\n   *\n   * @param Extensions - Optional. An object with extensions.\n   * @param Transition - Optional. A Transition component.\n   *\n   * @return `this`\n   */\n  mount( Extensions?: Record<string, ComponentConstructor>, Transition?: ComponentConstructor ): this {\n    this.state.set( CREATED );\n\n    this.Transition = Transition || this.Transition || ( this.is( FADE ) ? Fade : Slide );\n    this.Extensions = Extensions || this.Extensions;\n\n    const Constructors = assign( {}, ComponentConstructors, this.Extensions, { Transition: this.Transition } );\n    const { Components } = this;\n\n    forOwn( Constructors, ( Component, key ) => {\n      const component = Component( this, this.Components, this.opts );\n      Components[ key ] = component;\n      component.setup && component.setup();\n    } );\n\n    forOwn( Components, component => {\n      component.mount && component.mount();\n    } );\n\n    forOwn( Components, component => {\n      component.mounted && component.mounted();\n    } );\n\n    this.emit( EVENT_MOUNTED );\n\n    addClass( this.root, CLASS_INITIALIZED );\n\n    this.state.set( IDLE );\n    this.emit( EVENT_READY );\n\n    return this;\n  }\n\n  /**\n   * Syncs the slider with the provided one.\n   * This method must be called before the `mount()`.\n   *\n   * @example\n   * ```ts\n   * var primary   = new Splide();\n   * var secondary = new Splide();\n   *\n   * primary.sync( secondary );\n   * primary.mount();\n   * secondary.mount();\n   * ```\n   *\n   * @param splide - A Splide instance to sync with.\n   *\n   * @return `this`\n   */\n  sync( splide: Splide ): this {\n    this.splides.push( splide );\n    splide.splides.push( this );\n    return this;\n  }\n\n  /**\n   * Moves the slider with the following control pattern.\n   *\n   * | Pattern | Description |\n   * |---|---|\n   * | `i` | Goes to the slide `i` |\n   * | `'+${i}'` | Increments the slide index by `i` |\n   * | `'-${i}'` | Decrements the slide index by `i` |\n   * | `'>'` | Goes to the next page |\n   * | `'<'` | Goes to the previous page |\n   * | `>${i}` | Goes to the page `i` |\n   *\n   * In most cases, `'>'` and `'<'` notations are enough to control the slider\n   * because they respect `perPage` and `perMove` options.\n   *\n   * @example\n   * ```ts\n   * var splide = new Splide();\n   *\n   * // Goes to the slide 1:\n   * splide.go( 1 );\n   *\n   * // Increments the index:\n   * splide.go( '+2' );\n   *\n   * // Goes to the next page:\n   * splide.go( '>' );\n   *\n   * // Goes to the page 2:\n   * splide.go( '>2' );\n   * ```\n   *\n   * @param control\n   */\n  go( control: number | string ): void {\n    this.Components.Controller.go( control );\n  }\n\n  /**\n   * Registers an event handler.\n   *\n   * @example\n   * ```ts\n   * var splide = new Splide();\n   *\n   * // Listens to a single event:\n   * splide.on( 'move', function() {} );\n   *\n   * // Listens to multiple events:\n   * splide.on( 'move resize', function() {} );\n   *\n   * // Appends a namespace:\n   * splide.on( 'move.myNamespace resize.myNamespace', function() {} );\n   * ```\n   *\n   * @param events   - An event name or names separated by spaces. Use a dot(.) to append a namespace.\n   * @param callback - A callback function.\n   *\n   * @return `this`\n   */\n  on( events: string, callback: EventBusCallback ): this {\n    this.event.on( events, callback );\n    return this;\n  }\n\n  /**\n   * Removes the registered all handlers for the specified event or events.\n   * If you want to only remove a particular handler, use namespace to identify it.\n   *\n   * @example\n   * ```ts\n   * var splide = new Splide();\n   *\n   * // Removes all handlers assigned to \"move\":\n   * splide.off( 'move' );\n   *\n   * // Only removes handlers that belong to the specified namespace:\n   * splide.off( 'move.myNamespace' );\n   * ```\n   *\n   * @param events - An event name or names separated by spaces. Use a dot(.) to append a namespace.\n   *\n   * @return `this`\n   */\n  off( events: string ): this {\n    this.event.off( events );\n    return this;\n  }\n\n  /**\n   * Emits an event and triggers registered handlers.\n   *\n   * @param event - An event name to emit.\n   * @param args  - Optional. Any number of arguments to pass to handlers.\n   *\n   * @return `this`\n   */\n  emit( event: string, ...args: any[] ): this {\n    this.event.emit( event, ...args );\n    return this;\n  }\n\n  /**\n   * Inserts a slide at the specified position.\n   *\n   * @example\n   * ```ts\n   * var splide = new Splide();\n   * splide.mount();\n   *\n   * // Adds the slide by the HTML:\n   * splide.add( '<li></li> );\n   *\n   * // or adds the element:\n   * splide.add( document.createElement( 'li' ) );\n   * ```\n   *\n   * @param slides - A slide element, an HTML string that represents a slide, or an array with them.\n   * @param index  - Optional. An index to insert a slide at.\n   *\n   * @return `this`\n   */\n  add( slides: string | HTMLElement | Array<string | HTMLElement>, index?: number ): this {\n    this.Components.Slides.add( slides, index );\n    return this;\n  }\n\n  /**\n   * Removes slides that match the matcher\n   * that can be an index, an array with indices, a selector, or an iteratee function.\n   *\n   * @param matcher - An index, an array with indices, a selector string, or an iteratee function.\n   */\n  remove( matcher: SlideMatcher ): this {\n    this.Components.Slides.remove( matcher );\n    return this;\n  }\n\n  /**\n   * Checks the slider type.\n   *\n   * @param type - A type to test.\n   *\n   * @return `true` if the type matches the current one, or otherwise `false`.\n   */\n  is( type: string ): boolean {\n    return this.opts.type === type;\n  }\n\n  /**\n   * Refreshes the slider.\n   *\n   * @return `this`\n   */\n  refresh(): this {\n    this.emit( EVENT_REFRESH );\n    return this;\n  }\n\n  /**\n   * Destroys the slider.\n   *\n   * @param completely - Optional. If `true`, Splide will not remount the slider by breakpoints.\n   *\n   * @return `this`\n   */\n  destroy( completely?: boolean ): this {\n    const { event, state } = this;\n\n    if ( state.is( CREATED ) ) {\n      // Postpones destruction requested before the slider becomes ready.\n      event.on( EVENT_READY, this.destroy.bind( this, completely ), this );\n    } else {\n      forOwn( this.Components, component => {\n        component.destroy && component.destroy( completely );\n      } );\n\n      event.emit( EVENT_DESTROY );\n      event.destroy();\n      empty( this.splides );\n      state.set( DESTROYED );\n    }\n\n    return this;\n  }\n\n  /**\n   * Returns options.\n   *\n   * @return An object with the latest options.\n   */\n  get options(): Options {\n    return this.opts;\n  }\n\n  /**\n   * Merges options to the current options and emits `updated` event.\n   *\n   * @param options - An object with new options.\n   */\n  set options( options: Options ) {\n    const { opts } = this;\n    merge( opts, options );\n\n    if ( ! this.state.is( CREATED ) ) {\n      this.emit( EVENT_UPDATED, opts );\n    }\n  }\n\n  /**\n   * Returns the number of slides without clones.\n   *\n   * @return The number of slides.\n   */\n  get length(): number {\n    return this.Components.Slides.getLength( true );\n  }\n\n  /**\n   * Returns the active slide index.\n   *\n   * @return The active slide index.\n   */\n  get index(): number {\n    return this.Components.Controller.getIndex();\n  }\n}\n\n"], "names": ["PROJECT_CODE", "DATA_ATTRIBUTE", "STATES", "CREATED", "MOUNTED", "IDLE", "MOVING", "DESTROYED", "array", "length", "subject", "isNull", "Array", "isArray", "HTMLElement", "HTMLButtonElement", "value", "values", "iteratee", "for<PERSON>ach", "indexOf", "items", "push", "toArray", "arrayProto", "prototype", "arrayLike", "start", "end", "slice", "call", "predicate", "filter", "elm", "classes", "add", "name", "classList", "parent", "children", "append<PERSON><PERSON><PERSON>", "bind", "nodes", "ref", "parentNode", "insertBefore", "node", "selector", "matches", "child", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "object", "keys", "Object", "i", "key", "sources", "source", "isObject", "merge", "attrs", "removeAttribute", "attr", "value2", "setAttribute", "String", "tag", "document", "createElement", "isString", "elms", "styles", "getComputedStyle", "style", "display2", "display", "getAttribute", "className", "contains", "html", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "body", "e", "stopPropagation", "preventDefault", "stopImmediatePropagation", "querySelector", "querySelectorAll", "target", "getBoundingClientRect", "<PERSON><PERSON><PERSON><PERSON>", "condition", "message", "Error", "callback", "noop", "func", "requestAnimationFrame", "number", "minOrMax", "maxOrMin", "exclusive", "min", "Math", "max", "x", "y", "minimum", "maximum", "floor", "ceil", "abs", "round", "string", "replacements", "replace", "replacement", "ids", "ORIENTATION_MAP", "marginRight", "width", "autoWidth", "fixedWidth", "paddingLeft", "paddingRight", "left", "right", "X", "pageX", "ArrowLeft", "ArrowRight", "CLASS_ROOT", "CLASS_SLIDER", "CLASS_TRACK", "CLASS_LIST", "CLASS_SLIDE", "CLASS_CLONE", "CLASS_CONTAINER", "CLASS_ARROWS", "CLASS_ARROW", "CLASS_ARROW_PREV", "CLASS_ARROW_NEXT", "CLASS_PAGINATION", "CLASS_PROGRESS", "CLASS_PROGRESS_BAR", "CLASS_AUTOPLAY", "CLASS_PLAY", "CLASS_PAUSE", "CLASS_ACTIVE", "CLASS_PREV", "CLASS_NEXT", "CLASS_VISIBLE", "CLASS_LOADING", "STATUS_CLASSES", "EVENT_MOUNTED", "EVENT_MOVE", "EVENT_MOVED", "EVENT_CLICK", "EVENT_ACTIVE", "EVENT_INACTIVE", "EVENT_VISIBLE", "EVENT_HIDDEN", "EVENT_SLIDE_KEYDOWN", "EVENT_REFRESH", "EVENT_UPDATED", "EVENT_RESIZE", "EVENT_RESIZED", "EVENT_SCROLLED", "EVENT_DESTROY", "EVENT_LAZYLOAD_LOADED", "handlers", "events", "event", "namespace", "eventHandlers", "handler", "join", "split", "fragments", "eventNS", "on", "priority", "sort", "handler1", "handler2", "off", "offBy", "emit", "apply", "arguments", "destroy", "Splide2", "listeners", "targets", "event2", "listener", "removeEventListener", "unbind", "data", "options", "addEventListener", "interval", "onInterval", "onUpdate", "limit", "startTime", "id", "now", "Date", "rate", "paused", "count", "elapsed", "update", "resume", "cancel", "rewind", "pause", "isPaused", "initialState", "state", "set", "is", "states", "includes", "duration", "RequestInterval", "_this", "ROLE", "ARIA_CONTROLS", "ARIA_CURRENT", "ARIA_LABEL", "ARIA_HIDDEN", "TAB_INDEX", "ALL_ATTRIBUTES", "SLIDE", "LOOP", "FADE", "index", "slideIndex", "slide", "EventInterface", "destroyEvents", "Components", "root", "isNavigation", "updateOnMove", "resolve", "Direction", "isClone", "container", "currIndex", "this", "isActive", "visible", "slideFocus", "hasClass", "trackRect", "rect", "Elements", "track", "slideRect", "isVisible", "active", "mount", "pad", "label", "controls", "isHTMLButtonElement", "idx", "format", "i18n", "slideX", "splides", "map", "splide", "type", "_this2", "boundUpdate", "next", "prev", "dest", "rule", "prop", "useContainer", "Style", "<PERSON><PERSON><PERSON><PERSON>", "from", "distance", "diff", "POINTER_MOVE_EVENTS", "POINTER_UP_EVENTS", "IE_ARROW_KEYS", "SRC_DATA_ATTRIBUTE", "SRCSET_DATA_ATTRIBUTE", "IMAGE_SELECTOR", "TRIGGER_KEYS", "Components2", "initialOptions", "points", "currPoint", "completely", "observe", "item", "find", "item2", "point", "newOptions", "breakpoints", "setup", "JSON", "parse", "n", "m", "matchMedia", "mediaQuery", "axisOnly", "direction", "orient", "slider", "list", "elements", "slides", "query", "autoplay", "arrows", "bar", "play", "prefix", "uniqueId", "getClasses", "drag", "assign", "refresh", "sheet", "cssRules", "cssRule", "isCSSStyleRule", "cssRule2", "selectorText", "insertRule", "CSSStyleRule", "create", "head", "ruleBy", "isHTMLElement", "Slides2", "Slide", "excludeClones", "Slide2", "matcher", "register", "get", "getIn", "page", "Controller", "toIndex", "hasFocus", "perPage", "between", "getAt", "parseHtml", "before", "append", "images", "queryAll", "img", "<PERSON><PERSON><PERSON><PERSON>", "isEnough", "cloneCount", "cloneIndex", "Slides", "clones", "computeCloneCount", "concat", "isHead", "clone", "cloneNode", "cloneDeep", "fixedSize", "clones2", "flickMaxPages", "vertical", "unit", "cssPadding", "gap", "cssSlideSize", "height", "paddingTop", "paddingBottom", "cssHeight", "cssTrackHeight", "heightRatio", "setSlidesHeight", "fixedHeight", "autoHeight", "padding", "parseFloat", "window", "<PERSON>hrottle", "init", "resize", "listSize", "slideSize", "withoutGap", "getGap", "sliderSize", "firstSlide", "lastSlide", "totalSize", "getPadding", "looping", "waiting", "Layout", "currPosition", "positionRate", "isExceededMax", "getLimit", "toPosition", "position", "exceededMin", "exceededMax", "isExceededMin", "loop", "trimming", "focus", "trimSpace", "clamp", "trim", "getEnd", "offset2", "reposition", "move", "isBusy", "getPosition", "waitForTransition", "Transition", "oldPosition", "go", "jump", "translate", "minDistance", "Infinity", "isExceeded", "slideCount", "perMove", "Move", "isLoop", "prevIndex", "destination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "computeDestIndex", "incremental", "toPage", "control", "allowSameIndex", "indicator", "match", "getNext", "getPrev", "setIndex", "getIndex", "created", "wrapper", "createArrow", "prev2", "arrow", "arrow<PERSON>ath", "nextIndex", "prevLabel", "last", "next<PERSON><PERSON><PERSON>", "first", "disabled", "hovered", "focused", "playButton", "pauseButton", "for<PERSON><PERSON><PERSON>", "button", "resetProgress", "manual", "pauseOnHover", "pauseOnFocus", "cover", "src", "suppressConstraint", "friction", "onScrolled", "t", "easingFunc", "pow", "backwards", "clear", "scroll", "startCoord", "lastTime", "basePosition", "baseCoord", "baseTime", "lastEvent", "moving", "isMouse", "exceeded", "<PERSON><PERSON>", "isSlide", "isFade", "isFree", "onPointerMove", "onPointerUp", "getCoord", "min2", "cancelable", "dragMinThreshold", "capture", "timeStamp", "constrain", "velocity", "diffCoord", "diffTime", "isFlick", "computeVelocity", "sign", "flickPower", "computeDestination", "computeIndex", "touches", "onPointerDown", "keyboard", "isSequential", "lazyLoad", "preloadPages", "load", "error", "spinner", "srcset", "parentElement", "pagination", "li", "class", "text", "curr", "processed", "instances", "index2", "instance", "deltaY", "wheel", "onWheel", "DEFAULTS", "speed", "easing", "done", "endCallback", "transition", "rewindSpeed", "getSpeed", "EventBus", "State", "_Splide", "defaults", "opts", "Extensions", "Fade", "Constructors", "ComponentConstructors", "Component", "component", "_this3", "mounted", "sync", "args", "remove", "Splide"], "mappings": ";;;;;;;0OAKaA,EAAe,SAOfC,UAA0BD,ECkB1BE,EAAS,CACpBC,QA5BqB,EA6BrBC,QAxBqB,EAyBrBC,KApBkB,EAqBlBC,OAhBoB,EAiBpBC,UAZuB,cClBFC,KACfC,OAAS,aCCSC,UACfC,EAAQD,IAAgC,iBAAZA,aAUXA,UACnBE,MAAMC,QAASH,cAqBEA,SACE,iBAAZA,aAqBQA,UACH,OAAZA,aAUsBA,UACtBA,aAAmBI,uBAUSJ,UAC5BA,aAAmBK,6BC3EAC,UACnBH,EAASG,GAAUA,EAAQ,CAAEA,cCFVC,EAAiBC,KAClCD,GAASE,QAASD,cCDAV,EAAYQ,YAChCR,EAAMY,QAASJ,cCCCR,EAAYa,YAC7BC,aAASC,EAASF,IACjBb,MCPIgB,EAAaZ,MAAMa,qBCMNC,EAAyBC,EAAgBC,UAC1DJ,EAAWK,MAAMC,KAAMJ,EAAWC,EAAOC,cCAhDF,EACAK,UAEOF,EAAOH,GAAYM,OAAQD,GAAa,cCNpBE,EAAcC,EAA4BC,GAChEF,KACMC,EAAS,YACXE,KACCC,UAAWF,EAAM,MAAQ,UAAYC,gBCLvBH,EAAcC,KACzBD,EAAKC,GAAS,cCDLI,EAAiBC,KAC9BA,EAAUD,EAAOE,YAAYC,KAAMH,eCDtBI,EAAsBC,KACnCD,EAAO,gBACRJ,EAASK,EAAIC,WAEdN,KACIO,aAAcC,EAAMH,gBCNRV,EAAcc,+BACEd,EAAIe,SAAUlB,KAAMG,EAAKc,cCGjBT,EAAqBS,UAC7DT,EAAST,EAAOS,EAAOC,UAAWP,OAAQ,mBAASgB,EAASC,EAAOF,KAAsB,cCFpDT,EAAqBS,UAC1DA,EAAWR,EAAaD,EAAQS,GAAY,GAAMT,EAAOY,6BCHhEC,EACAjC,MAEKiC,UACGC,EAAOC,OAAOD,KAAMD,GAEhBG,EAAI,EAAGA,EAAIF,EAAK3C,OAAQ6C,IAAM,KAChCC,EAAMH,EAAME,MAEL,cAARC,IACqC,IAAnCrC,EAAUiC,EAAQI,GAAOA,gBAO7BJ,aCSmDA,8BAAcK,mCAAAA,6BAChErC,QAAS,cACPsC,EAAQ,SAAEzC,EAAOuC,KACfA,GAAQE,EAAQF,OAIrBJ,aCRkDA,EAAWM,YAC5DA,EAAQ,SAAEzC,EAAOuC,KACfA,GAAQG,EAAU1C,GAAU2C,EAAOD,EAAUP,EAAQI,IAAUJ,EAAQI,GAAQ,GAAIvC,GAAUA,IAGhGmC,aC9BwBlB,EAAc2B,GACxC3B,KACM2B,EAAO,cACVC,gBAAiBC,gBCHzB7B,EACA2B,EACA5C,GAEK0C,EAAUE,KACLA,EAAO,SAAEG,EAAO3B,KACRH,EAAKG,EAAM2B,OAGnB/C,GAAU6C,EAAiB5B,EAAK2B,GAAU3B,EAAI+B,aAAcJ,EAAOK,OAAQjD,eCJrFkD,EACAN,EACAtB,GAEML,EAAMkC,SAASC,cAAeF,UAE/BN,IACES,EAAUT,IAAW/C,EAAS+C,QACvB3B,EAAK2B,GAMdtB,KACKA,EAAQL,GAGXA,aCGPqC,EACAC,MAEKF,EAAUE,UACN1D,EAASyD,GAAS,KAAOE,iBAAkBF,GAAQC,KAGpDA,EAAQ,SAAEvD,EAAOuC,GAChB5C,EAAQK,MACJsD,EAAM,YACRrC,MACCwC,MAAOlB,MAAYvC,kBCrCRiB,EAAkByC,KAClCzC,EAAK,CAAE0C,uBCJc1C,EAAc6B,UACnC7B,EAAI2C,aAAcd,cCCD7B,EAAc4C,UAC/B5C,GAAOA,EAAII,UAAUyC,SAAUD,cCCUE,UACzC9B,GAAU,IAAI+B,WAAYC,gBAAiBF,EAAM,aAAcG,iBCL/CC,EAAUC,KAC/BC,iBAEGD,MACDA,oBACAE,uCCH8ChD,EAA4BS,UACvET,GAAUA,EAAOiD,cAAexC,cCEcT,EAA4BS,UAC1ElB,EAAUS,EAAOkD,iBAAkBzC,eCPtB0C,UACbA,EAAOC,mCCEQhD,KACbA,EAAO,YACTI,GAAQA,EAAKF,cACXA,WAAW+C,YAAa7C,gBCFNb,EAAcC,KAC5BD,EAAKC,GAAS,cCCPlB,UACbqD,EAAUrD,GAAUA,EAAQA,EAAYA,OAAa,cCHtC4E,EAAgBC,eAAAA,IAAAA,EAAU,KACzCD,QACC,IAAIE,UAAY9F,OAAmB6F,cCHnBE,cACZA,GCNM,SAAPC,gBCAQC,UACZC,sBAAuBD,cCIPE,EAAgBC,EAAkBC,EAAkBC,OACrEC,EAAMC,KAAKD,IAAKH,EAAUC,GAC1BI,EAAMD,KAAKC,IAAKL,EAAUC,UACzBC,EAAYC,EAAMJ,GAAUA,EAASM,EAAMF,GAAOJ,GAAUA,GAAUM,MCXvEA,EAAaD,SAARD,EAAQC,oBASEL,EAAgBO,EAAWC,OAC1CC,EAAUL,EAAKG,EAAGC,GAClBE,EAAUJ,EAAKC,EAAGC,UACjBJ,EAAKE,EAAKG,EAAST,GAAUU,cCLhBH,UACN,QAAa,OCJdH,EAAsCC,KAAtCD,IAAKE,GAAiCD,KAAjCC,IAAKK,GAA4BN,KAA5BM,MAAOC,GAAqBP,KAArBO,KAAMC,GAAeR,KAAfQ,IAAeR,KAAVS,kBCOnBC,EAAgBC,YAC7BA,EAAc,cACZD,EAAOE,QAAS,QAAUC,KAG9BH,cCTYf,UACZA,EAAS,OAAUA,KAAgBA,MCAtCmB,GAA8B,OCYvBC,EAAkB,CAC7BC,YAAc,CAAE,eAAgB,cAChCC,MAAc,CAAE,UAChBC,UAAc,CAAE,cAChBC,WAAc,CAAE,eAChBC,YAAc,CAAE,aAAc,gBAC9BC,aAAc,CAAE,gBAAiB,eACjCC,KAAc,CAAE,MAAO,SACvBC,MAAc,CAAE,SAAU,QAC1BrB,EAAc,CAAE,KAChBsB,EAAc,CAAE,KAChBC,MAAc,CAAE,SAChBC,UAAc,CAAE,UAAW,cAC3BC,WAAc,CAAE,YAAa,kBC9BlBC,GAAwBpI,EACxBqI,GAA4BrI,aAC5BsI,GAA4BtI,YAC5BuI,GAA4BvI,WAC5BwI,GAA4BxI,YAC5ByI,GAA4BD,aAC5BE,GAA4BF,iBAC5BG,GAA4B3I,aAC5B4I,EAA4B5I,YAC5B6I,GAA4BD,WAC5BE,GAA4BF,WAC5BG,GAA4B/I,iBAE5BgJ,GAA4BhJ,eAC5BiJ,GAA4BD,WAC5BE,GAA4BlJ,eAC5BmJ,GAA4BnJ,WAC5BoJ,GAA4BpJ,YAG5BqJ,GAAwB,YACxBC,GAAwB,UACxBC,GAAwB,UACxBC,GAAwB,aACxBC,GAAwB,aAOxBC,GAAiB,CAAEL,GAAcG,GAAeF,GAAYC,GAAYE,IClCxEE,GAA2B,UAE3BC,GAA2B,OAC3BC,GAA2B,QAC3BC,GAA2B,QAC3BC,GAA2B,SAC3BC,GAA2B,WAC3BC,GAA2B,UAC3BC,GAA2B,SAC3BC,GAA2B,gBAC3BC,GAA2B,UAC3BC,GAA2B,UAC3BC,GAA2B,SAC3BC,GAA2B,UAK3BC,GAA2B,WAC3BC,GAA2B,UAU3BC,GAA2B,oCCmBlCC,EAA2C,cA2BjCC,EAA2BrH,KACzBqH,EAAQ,SAAEC,EAAOC,OACvBC,EAAgBJ,EAAUE,KAEtBA,GAAUE,GAAiBA,EAAc/I,OAAQ,mBAClDgJ,EAAQzH,IAAMyH,EAAQzH,MAAQA,EAAMyH,EAAQF,YAAcA,iBA0ChDF,EAA2B1J,KACvC0J,GAASK,KAAM,KAAMC,MAAO,KAAM/J,QAAS,YAC5CgK,EAAYC,EAAQF,MAAO,OACvBC,EAAW,GAAKA,EAAW,YAIlC,CACLE,YAvEWT,EAA2B7E,EAA4BxC,EAAc+H,YAAAA,IAAAA,EAAW,MAC7EV,EAAQ,SAAEC,EAAOC,KACnBD,GAAUF,EAAUE,IAAW,KACnCF,EAAUE,GAAS,CAAEA,MAAAA,EAAO9E,SAAAA,EAAU+E,UAAAA,EAAWQ,SAAAA,EAAU/H,IAAAA,IAC9DgI,KAAM,SAAEC,EAAUC,UAAcD,EAASF,SAAWG,EAASH,cAoElEI,IAAAA,EACAC,eA1CcpI,KACNoH,EAAU,SAAEI,EAAeF,KAC5BA,EAAOtH,MAyCdqI,cA/Baf,sBACDA,IAAW,IAAK1J,QAAS,cAE3B4E,SAAS8F,MAAOb,EAASnJ,EAAOiK,EAAW,OA6BrDC,qBArBW,iBCvEiBC,OAItBnB,EAAUmB,EAAVnB,MAKFtH,EAAM,GAKR0I,EAA8E,cAmDjEC,EAAsCtB,KACvCsB,EAAStB,EAAQ,SAAEnF,EAAQ0G,KAC3BF,EAAUjK,OAAQ,mBACvBoK,EAAU,KAAQ3G,GAAU2G,EAAU,KAAQD,MAC1CE,oBAAqBF,EAAOC,EAAU,GAAKA,EAAU,KACrD,kBAgBbF,EACAtB,EACA1J,KAESgL,EAAS,YACXzG,KACIyF,MAAO,KAAM/J,QAASD,EAASuB,KAAM,KAAMgD,qBAS1CwG,EAAUjK,OAAQ,mBAAQsK,EAAQC,EAAM,GAAKA,EAAM,QACzDZ,MAAOpI,YAMT8H,GAAIZ,GAAesB,EAASxI,GAE3B,CACL8H,YAtFWT,EAA2B7E,EAA4BuF,KAC5DD,GAAIT,EAAQ7E,EAAUxC,EAAK+H,IAsFjCI,aA9EYd,KACNc,IAAKd,EAAQrH,IA8EnBqI,KAAMf,EAAMe,KACZnJ,cAlEAyJ,EACAtB,EACA7E,EACAyG,KAEcN,EAAStB,EAAQ,SAAEnF,EAAQ0G,KAC7B7K,KAAM,CAAEmE,EAAQ0G,EAAOpG,EAAUyG,MACpCC,iBAAkBN,EAAOpG,EAAUyG,MA4D5CF,OAAAA,EACAP,QAAAA,eCnIFW,EACAC,EACAC,EACAC,OAOIC,EAUAC,EAfIC,EAAQC,KAARD,IAUJE,EAAO,EAUPC,GAAS,EAKTC,EAAQ,mBAOFC,EADDF,IACCE,EAAUL,IAAQF,EAERJ,GAAXW,KACS,IACAL,OAELK,EAAUX,EAGdE,KACOM,GAGE,IAATA,QAGEL,KAAWO,GAASP,SAMtBS,oBAoBE,oCAmBaP,WACb,SAcJ,CACLpL,eA9Cc4L,GACZA,GAAUC,MACAR,OAAmBE,EAAOR,EAAW,MACrC,IACPY,IA2CLG,oBA7BYT,MACA,EAEPJ,KACOM,IA0BZQ,MAAAA,EACAF,OAAAA,EACAG,2BAROR,gBCvHYS,OAIjBC,EAAQD,QAsBL,CAAEE,aAfK9M,KACJA,GAcI+M,YAJDC,UACJC,EAAU1M,EAASyM,GAAUH,iBCnBtC5H,EACAiI,OAEIxB,2CAGKA,MACMyB,GAAiBD,GAAY,EAAG,aAEpCrC,MAAOuC,EAAMtC,KACP,MACV,KAAM,IAEAnK,aCnCF0M,GAAiB,OACjBC,GAAiB,gBACjBC,GAAiB,eACjBC,GAAiB,aACjBC,GAAiB,cACjBC,GAAiB,WAQjBC,GAAiB,CAC5BN,GACAC,GACAC,GACAC,GACAC,GACAC,GAb4B,YCDjBE,GAAQ,QAORC,GAAO,OAOPC,GAAO,mBC6DG9C,EAAgB+C,EAAeC,EAAoBC,SACrBC,GAAgBlD,GAA3DX,IAAAA,GAAIO,IAAAA,KAAMnJ,IAAAA,KAAe0M,IAATpD,QAChBqD,EAA8BpD,EAA9BoD,WAAYC,EAAkBrD,EAAlBqD,KAAM7C,EAAYR,EAAZQ,QAClB8C,EAA+B9C,EAA/B8C,aAAcC,EAAiB/C,EAAjB+C,aACdC,EAAYJ,EAAWK,UAAvBD,QACFE,KAAYV,EACZW,EAAY1M,EAAOgM,MAAYvG,qBA+EpBkH,EAAc5D,EAArB+C,QAEOjN,KAAM+N,KAAMC,cA6BoBC,KACjCd,EAAOR,IAAesB,GAAW,QACjCd,EAAOP,GAAWqB,GAAWvD,EAAQwD,WAAa,EAAI,MAE/DD,IAAYE,EAAUhB,EAAOzF,QACnByF,EAAOzF,GAAeuG,KAC7BA,EAAU9F,GAAgBC,GAAc2F,QAlC/B/N,KAAM+N,mBAgElB7D,EAAO+B,GAAIe,WACPgB,QAGHI,EAAYC,EAAMf,EAAWgB,SAASC,OACtCC,EAAYH,EAAMlB,GAClBnH,EAAY0H,EAAS,QACrBzH,EAAYyH,EAAS,gBAEpB1I,GAAOoJ,EAAWpI,KAAYwI,EAAWxI,IAAUwI,EAAWvI,IAAWhB,GAAMmJ,EAAWnI,IAzEpEwI,MAEhBtB,EAAO3F,GAAYyF,IAAUa,EAAY,KACzCX,EAAO1F,GAAYwF,IAAUa,EAAY,cAQTY,GACxCA,IAAWP,EAAUhB,EAAO5F,QAClB4F,EAAO5F,GAAcmH,GAE7BlB,KACWL,EAAOV,GAAciC,GAAU,QAGzCA,EAASzG,GAAeC,GAAgB6F,2BAsCzC7D,EAAO+C,QAAUA,QAsCnB,CACLA,MAAAA,EACAC,WAAAA,EACAC,MAAAA,EACAU,UAAAA,EACAD,QAAAA,EACAe,wCAvIOf,MACC3C,GAASsC,EAAKtC,YAAa2D,GAAK3B,EAAQ,SASxC4B,EACAC,EAPHtB,IACIuB,EAAqB5B,MACZA,EAAOZ,GAAM,UAGvByC,EAAWpB,EAAUV,EAAaD,EAClC4B,EAAWI,GAAQvE,EAAQwE,KAAKC,OAAQH,EAAM,GAC9CF,EAAW5E,EAAOkF,QAAQC,IAAK,mBAAUC,EAAO/B,KAAKtC,KAAK9B,KAAM,OAExDgE,EAAOT,GAAYmC,KACnB1B,EAAOX,GAAesC,UAtDhC3B,EAAO,gBAAiB,cACX,UAAX9J,EAAEkM,KAAmBvH,GAAcK,GAAqBmH,EAAMnM,OAGlEwE,kBAQE4H,EAAcjE,EAAO7K,KAAMoN,YAE7B,CAAEhG,GAAaQ,GAAeE,GAAeC,IAAkB+G,GAE9DhC,KACC3F,YAW+B4H,EAAcC,EAAcC,GAC5DA,IAAS3C,KACGjN,KAAM+N,MAAM,KAGtB/N,KAAM+N,OAhBYpN,KAAMoN,QAbFpN,KAAMoN,QA4KnC9D,yBAjHakD,EAAOvF,MACHuF,EAAON,KAiHxBgD,cAzDaC,EAAc5Q,EAAwB6Q,GAC7C9O,MAAgBkM,EAAMlC,IAAO4C,GAAakC,SAAuBnJ,GAAqB,MACjFoJ,MAAMH,KAAM5O,EAAU6O,EAAM5Q,IAwDvC+Q,kBAnBiBC,EAAcC,UAC3BC,EAAOlL,GAAKgL,EAAOjD,OAEhB/C,EAAO+B,GAAIa,MAAac,EACtBnJ,EAAK2L,EAAMlG,EAAOvL,OAASyR,GAG7BA,IAAQD,QC1ONE,GAAsB,sBAOtBC,GAAoB,8CCZ3BC,GAAgB,CAAE,OAAQ,QAAS,KAAM,YCblCC,GAAyBrS,UAOzBsS,GAA4BD,aAO5BE,OAAsBF,UAA2BC,WCDxDE,GAAe,CAAE,IAAK,QAAS,kECKZzG,EAAgB0G,EAAwBlG,OAI3DmG,EAKAC,EAKAC,aAuCcC,GACXA,uBACkB,SAAUC,oBAS3BC,EAAOC,EAAML,EAAQ,mBAAQM,EAAM,GAAIlQ,WAAa,GAErDgQ,EAAM,KAAQH,YAUHM,GACVC,EAAa5G,EAAQ6G,YAAaF,IAAWR,EAE9CS,EAAWrH,WACPS,QAAUmG,IACV5G,QAAgC,eAAvBqH,EAAWrH,WAEtBC,EAAO6B,MAAME,G7DtFC,Q6DuFR,KACF0C,WAGFjE,QAAU4G,IArBNP,EAAYG,EAAM,UAyB1B,CACLM,uBAxES9G,EAAS+G,KAAKC,MAAO5O,EAAcoH,EAAOqD,KAAMpP,WAC/CkF,MACA,EAAOA,EAAEU,WAGFlC,EAAO,GAAI6I,IAoE5BiE,qBA7DQ4C,EAAgB7G,EAAhB6G,YAEHA,MACMhQ,OAAOD,KAAMiQ,GACnB9H,KAAM,SAAEkI,EAAGC,UAAQD,GAAKC,IACxBvC,IAAK,kBAAS,CACbgC,EACAQ,gBAAiBnH,EAAQoH,YAAc,iBAAiBT,6BAG1C,SAAUJ,SAoD9BhH,QAAAA,uBf1EuBC,EAAgB0G,EAAwBlG,SAwB1D,CACLgD,iBAlBgBoC,EAAciC,OACtBC,EAActH,EAAdsH,iBAEDvM,EAAiBqK,GgBjDT,QhBgDDkC,GAAuBD,EgB3CtB,QhB2CqCC,EAAoB,KAAxB,IACLlC,GAgB3CmC,gBANe/S,UACRA,GgB5DQ,UhB4DU8S,UAAoB,2BiBLvB9H,EAAgB0G,EAAwBlG,OAa5DtK,EAKA8R,EAKA3D,EAKA4D,EA3BI5I,EAAO6D,GAAgBlD,GAAvBX,GACAgE,EAASrD,EAATqD,KACF6E,EAA8B,GAK9BC,EAAwB,oBAiGtBpH,gBA9BG9J,EAAOoM,MAAWhH,MAClB+L,EAAO/E,MAAW/G,MAClBrF,EAAOoN,MAAY9H,MAEpB8H,GAAS4D,EAAM,mCAEjBE,EAAQ5R,EAAU0R,MAAWzL,YAAsBC,aAEnD4L,EAAWpB,MAAW/J,IACtBoL,EAAWrB,MAAWtK,MAEpBuL,EAAU,CAChB7E,KAAAA,EACA2E,OAAAA,EACA3D,MAAAA,EACA4D,KAAAA,EACAE,OAAAA,EACAG,OAAAA,EACA7C,KAAO2C,EAAOE,MAAazL,IAC3B2I,KAAO4C,EAAOE,MAAaxL,IAC3ByL,IAAOH,EAAOnB,MAAWjK,QAA0BC,IACnDuL,KAAOJ,EAAOC,MAAelL,IAC7BuE,MAAO0G,EAAOC,MAAejL,SAQzB2D,EAAKsC,EAAKtC,alBzJM0H,YACbA,EAAW/D,GAAOpJ,GAAKmN,OAAkBA,IAAY,GAAM,GkBwJ9CC,CAAU1U,KAC3B+M,GAAMA,IACLA,GAAKsD,EAAMtD,IAAUA,aACtBA,GAAMkH,EAAKlH,IAAUA,YAxEhBsC,EAAQnN,EAAUyS,oBAerBR,KACM9E,EAAMnN,uCAeNmN,EAAMnN,KACTmN,EAAQnN,EAAUyS,gBAgDf5R,UACNE,EAAOoM,EAAMtM,IAAcE,EAAO+Q,EAAQjR,sBAS1C,CACDqF,QAAiBoE,EAAQ6E,KACzBjJ,QAAiBoE,EAAQsH,UAC7BtH,EAAQoI,MAAYxM,iBACpBoE,EAAQ8C,cAAoBlH,WAC5BiB,WAIGwL,EAAQX,EAAU,CACvBZ,MAAAA,EACA7C,mBA9FIrG,GAAe0K,KACfzK,GAAeiD,IA8FnBvB,QAAAA,0BCjLEtH,EAKAsQ,aAyBWhS,EAAkB6O,EAAc5Q,OACrCgU,EAAaD,EAAbC,SACFC,EAAUhC,EAAM+B,EAAU,mBAAWE,EAAgBC,IAAaA,EAAQC,eAAiBrS,KAC5FiS,EAAUD,EAAMM,WAAgBtS,OAAe,IAE/CmS,EAAgBD,OACXxQ,MAAOmN,MAAa5Q,cAsBPiU,UAChBA,aAAmBK,mBAGrB,CACL7E,mBApDQ8E,EAAQ,QAAS,GAAIpR,SAASqR,QAC9B/Q,EAAMsQ,OAoDdhJ,qBA7CQtH,KACA,MA6CRkN,KAAAA,EACA8D,gBAnBehQ,EAA8BmM,EAAc5Q,UAChD0U,EAAejQ,GAAWA,EAAOsH,GAAKtH,GAAWmM,EAAM5Q,sBCG9CgL,EAAgB0G,EAAwBlG,SACnC0C,GAAgBlD,GAAnCX,IAAAA,GAAIO,IAAAA,KAAMnJ,IAAAA,KACV0R,KAAiBzB,EAAWtC,UAA5B+D,OAAQF,IAAAA,KAKV0B,EAA2B,kBAcxBxU,QAAS,SAAE8N,EAAOF,KAAuBE,EAAOF,uBAO9C,cAAiBhD,cACnB4J,mCAkBU1G,EAAoBF,EAAeC,GAC9C7L,EAASyS,GAAO5J,EAAQ+C,EAAOC,EAAYC,KAC1CwB,UACAnP,KAAM6B,cAUD0S,UACLA,EAAgB7T,EAAQ,mBAAW8T,EAAMpG,UAAYiG,aAoE5CzU,EAA0B2U,KACrCA,GAAgB1U,QAASD,cAWf6U,UACRJ,EAAO3T,O/D3LU,mB+D2LU+T,EAC9BA,EACA,mBAAS1R,EAAU0R,GACjB/S,EAAS8S,EAAM7G,MAAO8G,GACtB9H,EAAU1M,EAASwU,GAAWD,EAAM/G,eA0DrC,CACL0B,uBA/LIrG,GAAe0K,IAgMnB/I,QAAAA,EACAiK,SAAAA,EACAC,IAAAA,EACAC,eA1IcC,OACNC,EAAe1D,EAAf0D,WACFrH,EAAQqH,EAAWC,QAASF,GAC5B1P,EAAQ2P,EAAWE,WAAa,EAAI9J,EAAQ+J,eAC3CvU,EAAQ,mBAASwU,EAASV,EAAM/G,MAAOA,EAAOA,EAAQtI,EAAM,MAuInEgQ,eA7Hc1H,UACP/M,EAAQ+M,GAAS,IA6HxB5M,aApHYd,EAAmD0N,KAClD1N,EAAO,gBAMVsB,EAiE0BoD,EAE9BtF,EApECiV,IAJArR,EAAU4K,GACLyH,EAAWzH,GAGDA,MACZtM,EAAMwR,EAAQpF,IACd4H,EAAQ1H,EAAOtM,GAAQiU,EAAQ3C,EAAMhF,KACjCA,EAAOzC,EAAQtK,QAAQ+M,OA+DfhN,EA9DHgN,EA8DiBlJ,EA9DV6F,EAAKnJ,KAAM,KAAM6H,IA+DrCuM,EAASC,EAAU7U,EAAK,QACxBxB,EAAWoW,EAAXpW,UAGGU,QAAS,cACR4V,EAAK,aAAc,aACdtW,oBAjET2J,qBASS2L,KACH/T,EAAQ+T,GAAU5E,IAAK,mBAAS2E,EAAM7G,WAC5C7E,aA6FNjJ,EACAa,OAAAA,EACA2P,cA1DaC,EAAc5Q,EAAwB6Q,KAC1C,cAAiBF,KAAMC,EAAM5Q,EAAO6Q,MA0D7CmF,mBAzBkBnB,UACXA,EAAgB1B,EAAgBwB,GAATlV,QAyB9BwW,2BAhBOtB,EAAOlV,OAAS+L,EAAQ+J,2BCzPXvK,EAAgB0G,EAAwBlG,OAS1D0K,EAKAC,IAbiBjI,GAAgBlD,GAA7BX,IAAAA,GAAIO,IAAAA,KACJwE,EAAqBsC,EAArBtC,SAAUgH,EAAW1E,EAAX0E,OACV5H,EAAYkD,EAAWjD,UAAvBD,QACF6H,EAAwB,iBAyBrBH,EAAaI,eAoCHlK,OACX+G,EAASiD,EAAOnB,MAAMpU,QACpBpB,EAAW0T,EAAX1T,UAEHA,EAAS,OACC,EAEL0T,EAAO1T,OAAS2M,KAChB+G,EAAQA,KAGTtS,OAAQuL,GAAQmK,OAAQpD,EAAOtS,MAAO,EAAGuL,IAAUjM,QAAS,SAAEyU,EAAO7G,OACpEyI,EAASzI,EAAQ3B,EACjBqK,WAeQxV,GACZwV,EAAQxV,EAAIyV,WAAW,YACnBD,EAAOjL,EAAQtK,QAAQuV,SAC3B1K,GAASf,EAAOqD,KAAKtC,YAAa2D,KAAOyG,GACxCM,EAnBYE,CAAW/B,EAAM3G,SACvB0H,EAAQc,EAAOtD,EAAQ,GAAIlF,OAAU2H,EAAQxG,EAAS6D,KAAMwD,KAC/DJ,EAAQI,KACPzB,SAAUyB,EAAO1I,EAAQ3B,KAAmB,EAAI3M,GAAUmV,EAAM7G,WAnD/DmI,kBAQJG,KACDA,qCAgBFH,IAAeI,OACZlN,qBAwDAwN,IALSpL,EAAX6K,cAECrL,EAAO+B,GAAIc,IAEJgJ,QACND,EAAapL,EAASgD,EAAS,iBACLzI,GAAMoJ,EAAMC,EAASC,OAASb,EAAS,UAAcoI,IAClDpL,EAASgD,EAAS,eAAmBxD,EAAOvL,QAAY+L,EAAQ+J,YAEpE3B,QAAiBkD,eAAiB,GAAM,EAAI,MANlE,EASJD,QAGF,CACLpH,uBAtGIrG,GAAe0K,KACf,CAAEzK,GAAeC,IAAgByI,IAsGrChH,QAAAA,oBCvHoBC,EAAgB0G,EAAwBlG,OACtDnB,KAAmB6D,GAAgBlD,IAAnCX,GAAI5I,IAAAA,KAAMmJ,IAAAA,KACVwL,EAAW1E,EAAX0E,OACA3B,EAAW/C,EAAWZ,MAAtB2D,OACAjG,EAAYkD,EAAWjD,UAAvBD,QACAH,KAAsBqD,EAAWtC,UAAjCf,KAAMgB,IAAAA,MAAO4D,IAAAA,KACbwC,EAAUW,EAAVX,MACFsB,EL1BW,QK0BAvL,EAAQsH,yBAkBfzE,EAAM,WAAY2I,EAAMxL,EAAQ/E,UAChC4I,EAAOb,EAAS,eAAiByI,GAAY,MAC7C5H,EAAOb,EAAS,gBAAkByI,GAAY,MAE/CtG,KAAMnC,EAAS,eAAiBwI,EAAMxL,EAAQ0L,QAC9CvG,KAAM,QA2ENnF,EAAQ9E,UAAY,GAAKsQ,EAAMxL,EAAQ7E,gBAA6B,GAAKwQ,6BAjExE9H,EAAO,wBAgCX+H,EAAS,QAMLC,EACAC,EALHP,QACMQ,IACO,yCAEVF,EAAgBJ,GAAY,GAC5BK,EAAgBL,GAAY,IAE7BI,GAAcC,eACCF,MACJC,QAAoBA,EAAgB,KAAOC,QAAuBA,EAAmB,iBAIhGF,EA/CkBI,MACjBC,aAAeC,MACjBnO,mBAOCoH,KAAM,SAiENqG,EAAMxL,EAAQmM,iBACDnM,EAAQoM,WAAa,GAAKT,IAAmBI,MAlExB,cAWtBxQ,OACX8Q,EAAYrM,EAAZqM,QACFjH,EAAOpC,EAASzH,EAAQ,QAAU,QAAQ,UACzC8Q,EAAUb,EAAMa,EAASjH,OAAsBiH,GAAY,IAAMA,IAAc,uBAiC/Eb,EAAMxL,EAAQ4L,QAAUjI,EAAM8D,GAAOxM,MAAQ+E,EAAQiM,8BA4BtDP,EAAMF,EAAMxL,EAAQ0L,yBACLA,SAAcA,SAAa1L,EAAQ+J,SAAW,IAAM2B,SAAcA,wBAsEjFtC,EAAQa,EAAO,UACdb,GAAQkD,WAAYrU,EAAOmR,EAAM3G,MAAOO,EAAS,kBAA2B,QAc9E,CACLiB,uBApMMsI,OAAQ,cAAeC,GAAUpN,EAAKnJ,KAAMoN,KAAMvF,QACpD,CAAED,GAAeD,IAAiB6O,KAClC3O,GAAc4O,IAmMlBC,2BA9EOhJ,EAAM8D,GAAQzE,EAAS,WA+E9B4J,mBApEkBrK,EAAgBsK,UAC5BzD,EAAQa,EAAO1H,GAAS,IAE1BoB,EAAMyF,EAAM3G,OAASO,EAAS,aAA6B,EAAI8J,KAC/D,GAiEJC,0BApCMC,EAAa/C,EAAO,GACpBgD,EAAahD,EAAOW,EAAOJ,WAAW,GAAS,UAEhDwC,GAAcC,EACVtJ,EAAMsJ,EAAUxK,OAASO,EAAS,UAAcW,EAAMqJ,EAAWvK,OAASO,EAAS,SAGrF,GA8BPkK,mBAvDkB3K,EAAesK,OAC3BzD,EAAQa,EAAO1H,MAEhB6G,EAAQ,CACL7N,EAAQoI,EAAMyF,EAAM3G,OAASO,EAAS,UACtC1H,EAAQqI,EAAM8D,GAAQzE,EAAS,gBAC9BxI,GAAKe,EAAQD,MAAwB,EAAIwR,YAG3C,GA+CPK,oBAVmB5R,UACZ+Q,WAAYrU,EAAO4L,EAAOb,aAAoBzH,EAAQ,QAAU,SAAW,MAAc,mBCvM9EiE,EAAgB0G,EAAwBlG,OASxDoN,EAKAC,IAbiB3K,GAAgBlD,GAA7BX,IAAAA,GAAIO,IAAAA,KACJwN,KAA2D1G,EAAWoH,QAAtEV,UAAWO,IAAAA,WAAYD,IAAAA,UAAWP,IAAAA,SAAUI,IAAAA,WAC5C/J,KAAoBkD,EAAWjD,WAA/BD,QAASuE,IAAAA,OACTE,KAAgBvB,EAAWtC,UAA3B6D,KAAM5D,IAAAA,MAeV0J,EAAe,EAKfC,EAAe,eAaK,SAAjBxN,EAAQoI,OACL5I,EAAO+C,QAENvC,EAASgD,EAAS,gBAAsBhD,EAASgD,EAAS,iBACpD2J,IAAaa,GAGrBC,EAAeF,MACPG,GAAU,gBAwDZnL,KACFoL,EAAYpL,GAAO,eAQZqL,cAWLA,QAELlI,EACAmI,EACAC,GAHDV,GAAW5N,EAAO+B,GAAIc,MACrBqD,EAAckI,EAAWL,EACzBM,EAAcE,EAAeH,GAC7BE,EAAcL,EAAeG,IAE5BC,GAAsB,EAAPnI,GAAgBoI,GAAepI,EAAO,QAC9C6B,EAAQwF,OAA+B,gBAIhDa,EArBQI,CAAMJ,KACNL,EAAeZ,MACnBrH,MAAM2D,OAAQxB,EAAM,wBAA0BzE,EAAS,SAAWuK,oBAkE1DhL,EAAe0L,OAmCnB1L,EAlCTqL,EAAWrG,EAAQ2F,EAAW3K,EAAQ,IAkC7BA,EAlC2CA,EAqC3C,YAFP2L,EAAUlO,EAAVkO,SAMEA,GAAS,GAAMtB,EAAWrK,QAHZqK,EAAWrK,GAAO,IAAW,WArC9C0L,WAoBML,GACR5N,EAAQmO,WAAa3O,EAAO+B,GAAIa,QACxBgM,EAAOR,EAAU,EAAGrG,EAAQwF,IAAeJ,cAGjDiB,EAzBWS,CAAMT,GAAaA,mBAS/BtS,EAAO0H,EAAS,eACfW,EAAM8D,GAAQnM,GAASqI,EAAME,GAASvI,GAASiM,EAAQ4F,GAAY,eAwCzDlT,OACXgU,IAAcjO,EAAQmO,iBACfR,EAAN1T,EAAkBiM,EAAW0D,WAAW0E,SAAmC,EAAzBL,6BASlCZ,cAWDO,EAAkBW,UACjChH,EAAQqG,OAAyB,GAAMrG,EAAQmG,GAAU,eAW1CE,EAAkBW,UACjChH,EAAQqG,OAAyB,GAAMrG,EAAQmG,GAAU,UAY3D,CACLzJ,mBAzPI,CAAEnG,GAAcD,GAAeD,IAAiB4Q,IA0PpDC,cA/NavJ,EAAc3C,EAAe0C,OAElC2I,EADDc,MACCd,EAAWe,MAEPzJ,IAAS3C,IACTvC,EAAQ4O,oBAEXvN,MAAMC,IpEtFG,KoEuFVlE,GAAYmF,EAAO0C,EAAMC,KAEpB2J,WAAW1Z,MAAO+P,EAAM,qBAcrBA,EAAc3C,EAAe0C,EAAc6J,GACtD1B,MACG7K,MACI,MAGF,IACHlB,MAAMC,IpEnHG,KoEoHVjE,GAAakF,EAAO0C,EAAMC,GAEL,SAAtBlF,EAAQmO,WAAwBjJ,IAASD,GAAQ6J,IAAgBH,OACzD/E,WAAWmF,GAAW9J,EAAPC,EAAc,IAAM,MAxBnCA,EAAM3C,EAAO0C,EAAM2I,OAqNhCoB,KAAAA,EACAC,UAAAA,EACAjO,oBAhJW2N,OACAE,WAAW7N,UAgJtB6I,iBAtIgB+D,WACVhD,EAAS1E,EAAW0E,OAAOnB,MAE7BlH,EAAc,EACd2M,EAAcC,EAAAA,EAERrY,EAAI,EAAGA,EAAI8T,EAAO3W,OAAQ6C,IAAM,KAClC0L,EAAaoI,EAAQ9T,GAAIyL,MACzBkD,EAAajL,GAAKmT,EAAYnL,GAAeoL,QAE9CnI,EAAWyJ,WACAzJ,IACAjD,SAMXD,GAqHPoL,WAAAA,EACAgB,YAAAA,EACAjB,SAAAA,EACAgB,OAAAA,EACAX,cAAAA,EACAN,cAAAA,EACA2B,6BAhBOrB,EAAeR,IAAkBE,EAAeF,0BCvR/B/N,EAAgB0G,EAAwBlG,OAmB9DqP,EAKAC,EAKAvF,EA5BIlL,EAAO6D,GAAgBlD,GAAvBX,GACA0Q,EAASrJ,EAATqJ,KACA9E,KAAwBvE,EAAW0E,QAAnCH,SAAUD,IAAAA,UACZgF,EAAShQ,EAAO+B,GAAIc,IAKtBe,EAAYpD,EAAQ7K,OAAS,EAK7Bsa,EAAYrM,iBAmCDoH,GAAW,KACXxK,EAAQsP,UACRtP,EAAQ+J,mBAyDL2F,UACTC,GAAa,EAAOD,cAUXA,UACTC,GAAa,EAAMD,cAWNzK,EAAeyK,GAC7BxK,EAAO0K,EAAkBxM,GA8GxBkM,GAAWxF,IAAa,EAAIC,SA9GqC,GAAK3G,UACtEsM,EAAcxK,EAAO8I,EAAM9I,cAeTA,EAAcM,EAAcqK,OAE7Cza,SADHqV,KACGrV,EAAMkZ,IAGPpJ,EAAO,GAAY9P,EAAP8P,IACV8E,EAAS,EAAG9E,EAAMM,GAAM,IAAUwE,EAAS5U,EAAKoQ,EAAMN,GAAM,GACxD2E,EAASiG,EAAQ5K,IAEnBsK,EACIF,EACHpK,EACAA,EAAO,MAAqB6E,GAAWA,GAAYsF,EAC7CrP,EAAQiB,OACXiE,EAAO,EAAI9P,EAAM,KAMrBoa,GAAYK,GAAe3K,IAASM,MAClCqE,EAASiG,EAAQtK,MAAkBA,KAAY,WAOrDN,mBAWH9P,EAAMia,EAAatF,SAElBD,KAAgB0F,GAAUF,OACvBD,EAAa,GAGdpV,GAAK7E,EAAK,cAUJmN,UACRiN,EACI/E,IAAalI,EAAQ8M,KAAuB,EAAIA,EAAa,MAG/D9M,aAUSoH,UACTyE,EAAOtE,IAAaH,EAAOI,EAAUJ,EAAM,EAAG2E,gBAQtC/L,UACRuH,QACGE,EAASzH,EAAO8M,EAAatF,EAASsF,EAAa,GAAMA,EAAa,EAAI9M,IAC1EjI,GAAOiI,EAAQwH,IAGlBxH,aAiBUA,GACZA,IAAUa,MACDA,IACAb,6BnE7OU,ImEgQFvC,EAAQkO,QAAWlO,EAAQ8C,mBAG5C,CACLmB,uBAhPK+K,KAAM5L,KAEP,CAAEvF,GAAeD,IAAiB6O,KAElCzO,GAAgB,aACRuR,EAAK1F,QAAS0F,EAAKZ,iBAC5B,IA2OHI,YAvNWgB,EAA0BC,OAC/B9K,WAcQ6K,OACVxN,EAAQa,OAGA6M,EAAWtW,EADlB9B,EAAUkY,MACmBA,EAAQG,MAAO,oBAAuB,GAA5DD,OAAWtW,OAEF,MAAdsW,GAAmC,MAAdA,IAChBL,EAAkBxM,QAAiB6M,IAAetW,GAAU,IAAMyJ,GAAW,GAC7D,MAAd6M,IACFtW,EAASkQ,GAAUlQ,GAAWwW,GAAS,GACvB,MAAdF,MACFG,GAAS,OAGdZ,EACKpB,EAAO2B,GAAUhG,EAASsF,EAAatF,EAAU,GAEjDqE,EAAO2B,EAAS,EAAGzB,YAIxB/L,EAnCOyE,CAAO+I,OACfxN,EAAQyL,EAAM9I,MAECqK,EAAKb,cAAgCnM,IAAUa,OACxDb,KACLkM,KAAMvJ,EAAM3C,EAAOkN,KAkN1BU,QAAAA,EACAC,QAAAA,EACA9B,OAAAA,EACA+B,SAAAA,EACAC,kBApBiBrL,UACVA,EAAOwK,EAAYrM,GAoB1ByG,QAAAA,EACAiG,OAAAA,EACAhG,SAAAA,oBC5RoBtK,EAAgB0G,EAAwBlG,OAwB1DuQ,EAvBI1R,KAAmB6D,GAAgBlD,IAAnCX,GAAI5I,IAAAA,KAAMmJ,IAAAA,KACV1J,EAAkBsK,EAAlBtK,QAAS8O,EAASxE,EAATwE,KACTZ,EAAyBsC,EAAzBtC,SAAUgG,EAAe1D,EAAf0D,WACVpC,EAAkB5D,EAAlB4D,OAAQ3D,EAAUD,EAAVC,MAKZ2M,EAAU5M,EAASkE,OAKnB7C,EAAOrB,EAASqB,KAKhBD,EAAOpB,EAASoB,KAUd8C,EAAsC,oBA+DpChS,EAVEiZ,EAvCH/O,EAAQ8H,SACJ7C,GAAUD,IAgDblP,EAA4B,WAAnBkK,EAAQ8H,QAAuBN,EAASA,EAAShI,EAAOqD,OAE7DkG,EAAQ,MAAOrT,EAAQoS,UACvB2I,GAAa,KACbA,GAAa,MACb,IAEFD,EAAS,CAAEvL,EAAMD,MACjBwL,EAAS/Z,EAAOX,MAnDnBmP,GAAQD,IACJ8C,EAAO7C,OAWHuL,GAA4B,IAAnBxQ,EAAQ8H,OAAmB,OAAS,OAVxC7C,EAAMnD,GAAe+B,EAAMtD,MAC3ByE,EAAMlD,GAAe+B,EAAMtD,MAElC0E,KAAOA,IACPD,KAAOA,EA2BV+J,EAAOnF,EAAPmF,KACJ,CAAE5R,GAAeC,GAAYS,GAAeD,GAAeI,IAAkB8C,KAC3EkE,EAAM,QAAS,aAAY,SAC3BC,EAAM,QAAS,aAAY,StBtGG,iBsB4EJA,EAAMD,gBAmDlB0L,UAKbxG,oBAJ0BxU,EAAQib,WAAWD,EAAOhb,EAAQuP,KAAOvP,EAAQsP,sHAEjEhF,EAAQ4Q,WC9IT,kHDuJVrO,EAAY/C,EAAO+C,MACnBkN,EAAY7F,EAAWwG,UACvBS,EAAYjH,EAAWuG,UACvBW,KAAYrB,GAAkBlN,EAAQkN,EAAYjL,EAAKuM,KAAOvM,EAAKS,KACnE+L,KAAYH,GAA0BA,EAARtO,EAAoBiC,EAAKyM,MAAQzM,EAAKQ,OAErEkM,SAAWzB,EAAY,IACvByB,SAAWL,EAAY,IAEd5L,EAAMjD,GAAY8O,KAClB9L,EAAMhD,GAAYgP,KtBpJI,iBsBsJR/L,EAAMD,EAAMyK,EAAWoB,SAG9C,CACL/I,OAAAA,EACA7D,uBAvGIpG,GAAe4O,IAwGnBlN,mBAtEKgR,IACKC,MAESvL,EAAM9C,MACN6C,EAAM7C,0BE1EH3C,EAAgB0G,EAAwBlG,OAS5DmR,EAKAC,EAKAzQ,IAlBuB+B,GAAgBlD,GAAnCX,IAAAA,GAAI5I,IAAAA,KAAMmJ,IAAAA,KACVyD,KAA2DqD,EAAWtC,UAAtEf,KAAMgB,IAAAA,MAAOkE,IAAAA,IAAWsJ,IAANrJ,KAAyBsJ,IAAPpQ,MACtChB,EAAWyB,GAAiB3B,EAAQE,SAAUV,EAAOuP,GAAG9Y,KAAMuJ,EAAQ,cAyH3DkB,KxBtIqB,mBwBuINA,GAEzBqH,KACIA,EAAK,CAAE9M,MAAkB,IAAPyF,UA5HrBS,EAAajB,EAAbiB,oBAuCaoQ,OACbC,EAASD,EAAWD,EAAcD,EAEnCG,IACInN,EAAqBmN,MACZA,EAAQ3P,GAAM,YAGhB2P,EAAQ1P,GAAe+B,EAAMtD,MAC7BiR,EAAQxP,GAAYhC,EAAQwE,KAAM+M,EAAW,QAAU,WAE/DC,EAAQ,QAASD,EAAWrQ,EAAQ8G,iBA6BvC7G,KAAc+E,EAAW0E,OAAOH,eAC1BtV,OAAS6K,EAAQyR,oBAChB,IxBhGwB,6BwB2GtBC,YAAAA,IAAAA,GAAS,GAChBvQ,QACID,UxB3GyB,qBwB+G3BwQ,eAQF/Q,IACEwQ,GAAaC,KAGX,cAkBN,CACLnN,qBA5GQ4D,EAAa7H,EAAb6H,SAEHA,OACS,MACA,cAiCT7H,EAAQ2R,gBACL9O,EAAM,wBAAyB,cACd,eAAXlK,EAAEkM,WAKX7E,EAAQ4R,gBACL/O,EAAM,mBAAoB,cACT,YAAXlK,EAAEkM,aAKZ,CAAEzH,GxBhG8B,SwBgGJQ,IAAiBsC,EAASe,WA5CtC,UAAb4G,SAsGPtI,QAASW,EAASc,OAClBgH,KAAAA,EACA9G,MAAAA,EACAC,SAAAA,mBCnJmB3B,EAAgB0G,EAAwBlG,OACrDnB,EAAO6D,GAAgBlD,GAAvBX,cAwBQgT,KACHjH,OAAOjW,QAAS,gBACnB4V,EAAM9T,EAAyB2S,EAAMjG,WAAaiG,EAAM3G,MAAO,OAEhE8H,GAAOA,EAAIuH,OACND,EAAOtH,EAAKnB,gBAYTyI,EAAgBtH,EAAuBnB,KAChDjE,KAAM,aAAc0M,iCAAwCtH,EAAIuH,SAAW,IAAI,KAC5EvH,EAAKsH,EAAQ,OAAS,UAG1B,CACL5N,iBAzCKjE,EAAQ6R,UACP3T,GAAuB,SAAEqM,EAAKnB,MAAqB,EAAMmB,EAAKnB,OAC9D,CAAEjM,GAAeU,GAAeD,IAAiByB,EAAMpJ,KAAM,MAAM,MAwCzEsJ,sBAhCO,sBCdaC,EAAgB0G,EAAwBlG,OAQ1DE,IAPiBwC,GAAgBlD,GAA7BX,IAAAA,GAAIO,IAAAA,KACJmQ,EAASrJ,EAATqJ,KACAZ,EAA0BY,EAA1BZ,YAAajB,EAAa6B,EAAb7B,oBAsBJgC,EAAqBhO,EAAmBqQ,OAkD/BtM,EAjDlBtQ,EAAQwZ,IACVqD,EAAW,IAEJtQ,IA8Ca+D,EA9CgBjL,GAAKkV,EAAcva,GA+CpD8E,GAAKwL,EChFa,IAOD,YD6Bb9D,GAAiBD,EAAUuQ,EAAY,gBAC1CrE,EAAWe,IAEXjJ,GADWvQ,KAAwBA,IAuE5B+c,EAvE8CxR,GAwErDyR,EAAenS,EAAfmS,YACYA,EAAYD,GAAM,EAAIlY,KAAKoY,IAAK,EAAIF,EAAG,IAxE7BvD,KAAkBqD,IAEzC/C,UAAWrB,EAAWlI,GAEtBlG,EAAO+B,GAAIa,MAAa2P,GAAsBxC,EAAKH,kBClD/B,GDqDlB5U,GAAKkL,GCnEmB,KDkFlB2M,EAdD9C,EAAKxB,cAAeY,OAe1BjB,GAAY2E,GC5EO,KD4EuB,MAZ/C,K1B3DiC,Y0B8D3Bld,uBAgBH6I,iBAkBDkC,KACMc,sBAQNd,IAAcA,EAASiB,4BAkBvB,CACL8C,mBAjGI7G,GAAYkV,KACZ,CAAEzU,GAAeD,IAAiBoD,IAiGtCzB,QAAS+S,EACTC,OAAAA,EACAvR,OAAAA,kBEpHkBxB,EAAgB0G,EAAwBlG,OAexDwS,EAKAC,EAKAC,EAKAC,EAKAC,EAKAC,EAKAC,EAKAC,EAKA9Z,EAKA+Z,IA3D2BtQ,GAAgBlD,GAAvCJ,IAAAA,KAAMnJ,IAAAA,KAAM6J,IAAAA,OACZ+D,EAAUqC,EAAWtC,SAArBC,MACAb,KAAoBkD,EAAWjD,WAA/BD,QAASuE,IAAAA,OACToF,EAAazG,EAAWoH,OAAxBX,SACAoC,KAAe7I,EAAW0D,YAA1BmF,GAAIT,IAAAA,OACJiB,EAAiBrJ,EAAjBqJ,KAAM0D,EAAW/M,EAAX+M,OACNhE,EAAgDM,EAAhDN,UAAWpF,EAAqC0F,EAArC1F,QAAS8E,EAA4BY,EAA5BZ,YAAaS,EAAeG,EAAfH,WACnC8D,EAAU1T,EAAO+B,GAAIa,IACrB+Q,EAAU3T,EAAO+B,GAAIe,IACrB8Q,EAA2B,SAAjBpT,EAAQoI,gBAkEAzP,KACD,cAAXA,EAAEkM,OACFkO,EAAUxG,OAAS1I,KAEPlL,EAAkB6Y,SAC/BjC,EAAKb,WAOD/V,MANHM,EAAQ0M,GAAqB0N,KAC7Bpa,EAAQ2M,GAAmB0N,KAC5BtS,WACEA,WACMuS,EAAU5a,gBAYLA,OAEd6a,EADH7a,EAAE8a,YACCD,EAAMxT,EAAQ0T,kBAAoB,IAEnCX,GAAWvY,GAAK+Y,EAAU5a,GAAM6Z,GAAegB,OAmChD3P,EAAO,QAAS,cACZA,EAAO,WACNlL,GAAG,IACX,CAAEgb,WArCQ,M5BrHuB,S4ByH7Bb,aA2CYna,OACXib,EAAcjb,EAAdib,YACUnB,GnBxKW,GmBwKGmB,EAAYnB,GAE5BrD,MAAiB4D,OAChBrE,MACA4E,EAAU5a,KACVib,KAGLxE,MACAwE,IACAjb,EAELwa,KACMT,WAmGKhN,UACXA,MAAqBwN,EnBhSR,EmBgS6B,GApGrBW,CAAWN,EAAU5a,GAAMga,M5BlLnB,a4ByHpBha,KACHA,GAAG,OAGDA,cAWKA,KACZM,EAAY0M,OAAyBC,OACpC,EAEJiN,aAiDala,GACZmb,WAyBkBnb,MACnB6G,EAAO+B,GAAIc,MAAY+M,IAAe,KACnC2E,EAAYR,EAAUV,GAAcF,EACpCqB,EAAYnB,EAAUe,UAAYhB,EAClCqB,EAAYtb,EAAEib,UAAYnB,EnB9NL,MmBgOtBuB,GAAYC,SACRF,EAAYC,SAIhB,EApCUE,CAAiBvb,GAE7Bwa,IACC3T,EAAO+C,MAAQgF,EAAQ4M,EAAML,MAE3BpE,WAyCmBoE,OACrBM,EAAapU,EAAQoU,YAAc,WAElCzF,IAAgBwF,EAAML,GAAa/Z,EACxCS,GAAKsZ,GAAaM,EAClBhB,EAASjE,EAAAA,EAAWxC,OAAuBrB,eAAiB,IA9CxC+I,CAAoBP,GAEnCV,IACIb,OAAQ7C,cAsDEA,GACfxK,EAAO2E,EAAS6F,UACfwD,EAAU9E,EAAOlJ,EAAM,EAAGoJ,KAAapJ,EAtDtCoP,CAAc5E,IAAe,MAI1B,I5B3MyB,Y4B4IvB/W,KACC,iBA2HGA,YACEA,EAAMA,EAAkB4b,QAAS,IAAOvR,EAAS,gBAe/D,CACLiB,iBA3MKjE,EAAQoI,QACLvE,EnB5EuB,uBmB4EK2Q,wBlB7DdhV,EAAgB0G,EAAwBlG,OAQ5D/G,IAPyByJ,GAAgBlD,GAArCX,IAAAA,GAAI5I,IAAAA,KAAM6J,IAAAA,OACV+C,EAASqD,EAAWtC,SAApBf,KACAG,EAAYkD,EAAWjD,UAAvBD,2BAuB0BhD,EAAxByU,SAAAA,aAAW,WAEdA,IACe,YAAbA,MACM5R,EACWX,GAAW,KAEtBqK,SAGLtT,EAAQ,UAAW,YAgCTlC,EA/BS4B,EAAE5B,IAAnBA,EAgCH0K,EAAUoE,GAAe9O,WAAiBA,EAASA,EA9BjDA,IAAQiM,EAAS,eACb+L,GAAI,KACDhY,IAAQiM,EAAS,iBACpB+L,GAAI,qBAUZ9V,MACKA,EAAQ,WAEXiQ,EAAejQ,MACDA,EAAQiJ,WAgBxB,CACL+B,uBAzDIpG,GAAe,sBA0DnB0B,QAAAA,sBmBjDsBC,EAAgB0G,EAAwBlG,SAChC0C,GAAgBlD,GAAxCX,IAAAA,GAAIK,IAAAA,IAAKjJ,IAAAA,KAAMmJ,IAAAA,KACjBsV,EAAoC,eAArB1U,EAAQ2U,SAKzBtK,EAA+B,GAK/B9H,EAAQ,iBA6CD,IACA,mBAQA8H,EAAO7U,OAAQ,mBACjBuK,EAAKqJ,MAAM7D,SAAU/F,EAAO+C,MAAOvC,EAAQ+J,YAAsB6K,cAAgB,GAAM,KACnFC,EAAM9U,MAMH9L,UACPoJ,eASM0C,OACLwK,EAAQxK,EAARwK,MAEExK,EAAKqJ,MAAM3G,MAAOxF,MACtBsN,EAAK,aAAc,sBAgBVxK,EAA0B+U,OACjC1L,EAAUrJ,EAAVqJ,QAEKA,EAAM3G,MAAOxF,IAEnB6X,MACG/U,EAAKgV,WACJhV,EAAKwK,IAAK,MACbrM,GAAuB6B,EAAKwK,IAAKnB,KACjCtL,KAGH4W,QA5BmC3U,EAAiB,UAAXpH,EAAEkM,SAE9C,MAAO,UAAWlQ,QAAS,YACtBoL,EAAMnK,OACK2U,EAAK3U,EAAMmK,EAAMnK,MACd2U,EAAc,QAAT3U,EAAiBkQ,GAAqBC,oBAgC3DxD,EAAQ8H,EAAOpW,UACZoW,EAAQ9H,YAIX,CACL0B,iBAjHKjE,EAAQ2U,aACP,CAAExX,GAAeS,IAAiB,iBAe7BgN,OAAOjW,QAAS,cACGyU,EAAM3G,MAAOuD,IAAiBrR,QAAS,gBAKzDogB,EAJFjD,EAAS1Z,EAAcmS,EAAKzE,IAC5BkP,EAAS5c,EAAcmS,EAAKxE,IAE7B+L,IAAQvH,EAAIuH,KAAOkD,IAAWzK,EAAIyK,WAC/BD,EAAUhM,EAAQ,OAAQ/I,EAAQtK,QAAQqf,QAASxK,EAAI0K,eACtCpT,GAAM,kBACtB/M,KAAM,CAAEyV,IAAAA,EAAKnB,MAAAA,EAAO0I,IAAAA,EAAKkD,OAAAA,EAAQD,QAAAA,MAC/BxK,EAAK,aAKfmK,SAxBIA,KACD,CAAEvX,GAAeS,GAAeP,IAAekJ,KA2GvDhH,QAAAA,wBC/IwBC,EAAgB0G,EAAwBlG,OAa9DyH,IAZ+B/E,GAAgBlD,GAA3CX,IAAAA,GAAIO,IAAAA,KAAMnJ,IAAAA,KAAM6J,IAAAA,OAChB8K,EAAW1E,EAAX0E,OACAmE,KAAmC7I,EAAW0D,YAA9CmF,GAAIe,IAAAA,OAAQhG,IAAAA,SAAUwG,IAAAA,SAKxBzb,EAA0B,oBAsBzBmL,EAAQkV,YAActK,EAAOH,4BAuB1BxW,EAAWuL,EAAXvL,OACAyB,EAA2BsK,EAA3BtK,QAAS8O,EAAkBxE,EAAlBwE,KAAMuF,EAAY/J,EAAZ+J,UACE7D,EAAWtC,SAA5B4D,IAAAA,OAAQ3E,IAAAA,KACV/M,EAAgC,WAAvBkK,EAAQkV,YAA2B1N,EAASA,EAAS3E,EAC9D5I,EAAS6P,IAAa7V,EAASsG,GAAMtG,EAAS8V,KAE7ChB,EAAQ,KAAMrT,EAAQwf,WAAYpf,WAE/BgB,EAAI,EAAGA,EAAImD,EAAKnD,cAAhBA,OACFqe,EAAWpM,EAAQ,KAAM,KAAMtB,GAC/B+J,EAAWzI,EAAQ,SAAU,CAAEqM,MAAO1f,EAAQiU,KAAM9E,KAAM,UAAYsQ,GACtE/Q,EAAWwG,EAAOlB,MAAO5S,GAAI6N,IAAK,mBAASyE,EAAM3G,MAAMlC,KACvD8U,GAAavL,KAAwB,EAAVC,EAAcvF,EAAK/I,MAAQ+I,EAAKC,SAE3D+M,EAAQ,QAAS,iBAAiB1a,OAE1B0a,EAAQ1P,GAAesC,EAAS3F,KAAM,QACtC+S,EAAQxP,GAAYuC,GAAQ8Q,EAAMve,EAAI,M9B/FlB,kB8BiGL2Q,EAAM0N,EAAI3D,EAAQ1a,KAEzChC,KAAM,CAAEqgB,GAAAA,EAAI3D,OAAAA,EAAQ7H,KAAM7S,KAbxBA,Q9BvF0B,qB8B0DF,CAAE2Q,KAAAA,EAAM5S,MAAAA,GAASoV,EAAOzK,EAAO+C,0BAS5DkF,MACKA,KACF9S,QAAS,cAAkB6R,EAAKgL,OAAQ,aACvC3c,KACA,iBAwCK0N,UACP1N,EAAOib,EAAQvN,qBAOhB0C,EAAOgF,EAAOqG,GAAU,IACxBgF,EAAOrL,EAAOqG,KAEfrL,MACUA,EAAKuM,OAAQ3U,MACToI,EAAKuM,OAAQzP,KAG3BuT,MACOA,EAAK9D,OAAQ3U,MACTyY,EAAK9D,OAAQzP,IAAc,M9B/HP,qB8BkIJ,CAAE0F,KAAAA,EAAM5S,MAAAA,GAASoQ,EAAMqQ,SAGlD,CACLzgB,MAAAA,EACAoP,uBA3FI,CAAEpG,GAAeD,IAAiB6O,KAClC,CAAErP,GAAYY,IAAkB8C,IA2FpCvB,QAAAA,EACA0K,MAAAA,kBlB/HkBzK,EAAgB0G,EAAwBlG,OACpD0E,EAAYlF,EAAZkF,cAsDD,CACLT,qBArCMsR,IAoBE1W,EAhCHmB,EAAQ8C,gBAgCQJ,GAAgBlD,GAA7BX,IAAAA,GAAIO,IAAAA,OAER9B,GAAa,cACRyR,GAAI3F,EAAM7G,WAGf5E,GAAqB,SAAEyL,EAAOzQ,GAC3B8I,EAAUwE,GAActN,EAAE5B,SACtBgY,GAAI3F,EAAM7G,SACR5J,QZzDuB,qBY6DJ6G,EAAOkF,WAjCjC6Q,EAAsB,KAEpBxK,OAAQvL,GAAS7K,QAAS,SAAEiQ,EAAQrC,EAAOiT,MACjC5Q,GAAS/F,GAAIzB,GAAY,SAAEqY,EAAOxQ,EAAMC,KAC5CvQ,QAAS,YACZ+gB,IAAa9Q,GAAYnD,EAAU8T,EAAW3Q,OACvC9P,KAAM4gB,KACP3G,GAAI2G,EAASnU,GAAIc,IAAS6C,EAAOuQ,QAIvCF,0BmBvCQ/V,EAAgB0G,EAAwBlG,OACrD/J,EAASyM,GAAgBlD,GAAzBvJ,gBAgBU0C,OACRgd,EAAWhd,EAAXgd,OAEHA,MACI5G,GAAI4G,EAAS,EAAI,IAAM,OACrBhd,UAIN,CACLsL,iBApBKjE,EAAQ4V,SACL1P,EAAWtC,SAASC,MAAO,QAASgS,QCtBnCC,GAAoB,CAC/BjR,KAAmB,QACnBkR,MAAmB,IACnBnH,mBAAmB,EACnB7E,QAAmB,EACnBjC,QAAmB,EACnBoN,YAAmB,EACnBhV,SAAmB,IACnByR,cAAmB,EACnBC,cAAmB,EACnBH,eAAmB,EACnBuE,OAAmB,gCACnB5N,MAAmB,EACnBd,UAAmB,MACnB9D,YAAmB,EACnB2K,WAAmB,EACnBzY,QjCcqB,CACrB+M,MAAYzG,GACZiP,MAAYhP,GACZ6L,OAAY3L,GACZwU,MAAYvU,EACZ6I,KAAY5I,GACZ2I,KAAY1I,GACZ4Y,WAAY3Y,GACZoN,KAlCuCpN,YAmCvCwY,QA7BuCvhB,eiCOvCgR,KCvBkB,CAClBS,KAAQ,iBACRD,KAAQ,aACRiM,MAAQ,oBACRF,KAAQ,mBACRtM,OAAQ,iBACRhJ,MAAQ,gBACRuM,KAAQ,iBACR9G,MAAQ,+BCKY1B,EAAgB0G,EAAwBlG,OACpDnB,EAAO6D,GAAgBlD,GAAvBX,GACAoK,EAAW/C,EAAWZ,MAAtB2D,aAiCD,CACLhF,mBA3BI,CAAE9G,GAAeS,IAAiB,aAC1B,aACGgN,OAAOjW,QAAS,cACjByU,EAAM3G,MAAO,wBAA0BzC,EAAQ+V,YAAa/V,EAAQgW,eAyBlF7gB,eAZcoN,EAAe0T,OACrBpS,EAAUqC,EAAWtC,SAArBC,QACAA,EAAO,SAAU2H,EAAM7H,EAAME,GAAQ+H,WAEnC,iBAEA/H,EAAO,SAAU,OAO3B7C,OAAQxH,eCtCWgG,EAAgB0G,EAAwBlG,OAQzDkW,EAPIjgB,EAASyM,GAAgBlD,GAAzBvJ,KACAsZ,EAAqBrJ,EAArBqJ,KAAM3F,EAAe1D,EAAf0D,WACNnC,EAASvB,EAAWtC,SAApB6D,oBA6CC,eA4BO0O,KACH7Q,MAAM2D,OAAQxB,EAAM,aAAc0O,SAGxC,CACLlS,mBAnEMwD,EAAM,gBAAiB,YACtB9O,EAAEM,SAAWwO,GAAQyO,gBAmE5B/gB,eArDcoN,EAAe0T,OACvBvG,EAAcH,EAAK5B,WAAYpL,GAAO,GACtCqL,EAAc2B,EAAKZ,cACnBoH,WAwBWxT,OACT6T,EAAgBpW,EAAhBoW,eAEH5W,EAAO+B,GAAIa,KAAWgU,EAAc,KACjCnR,EAAO2E,EAAW0G,UAAU,GAC5Blb,EAAOwU,EAAW0E,YAER,IAATrJ,GAAuB7P,GAATmN,GAA4BnN,GAAR6P,GAAyB,IAAV1C,SAC/C6T,SAIJpW,EAAQ+V,MApCKM,CAAU9T,GAEQ,GAAjC/H,GAAKkV,EAAc9B,IAA4B,GAATmI,kBACpBA,QAAa/V,EAAQgW,UACrC/G,UAAWS,KACFuG,MAETjH,KAAMzM,SA4CbvB,OAAAA,4BC1BW/H,EAA8B+G,cAtCVsW,qBAKC,cAKJC,GpF5CT,gBoFiDQ,aAKI,mBAK0B,GAcnD1T,EAAOhL,EAAUoB,GAAW2O,EAAoBjQ,SAAUsB,GAAWA,IACnE4J,EAAUA,uBAEbA,KAAOA,IAELiT,GAAUU,EAAOC,YACjBtf,EAAOkM,KAAKqT,KAAMZ,IAAY9V,GAAW,+BAWlDiE,MAAA,SAAO0S,EAAmD9H,mBACnDxN,MAAMC,IpF3FQ,QoF6FduN,WAAaA,GAAcxL,KAAKwL,kBAAqBtN,GAAIe,IAASsU,GAAOxN,SACzEuN,WAAaA,GAActT,KAAKsT,eAE/BE,EAAexO,EAAQ,GAAIyO,GAAuBzT,KAAKsT,WAAY,CAAE9H,WAAYxL,KAAKwL,eACrExL,KAAfT,oBAEAiU,EAAc,SAAEE,EAAWhgB,GAC3BigB,EAAYD,EAAWE,EAAMA,EAAKrU,WAAYqU,EAAKP,SAC7C3f,GAAQigB,GACVlQ,OAASkQ,EAAUlQ,YAGvBZ,EAAY,cACRjC,OAAS+S,EAAU/S,YAGvBiC,EAAY,cACRgR,SAAWF,EAAUE,iBAG5B9X,KAAMjC,MAEDkG,KAAKR,KrChGkB,uBqCkG5BxB,MAAMC,IpF3GK,QoF4GXlC,KpCxH+B,SoC0H7BiE,QAqBT8T,KAAA,SAAMvS,eACCF,QAAQ5P,KAAM8P,KACZF,QAAQ5P,KAAMuO,MACdA,QAqCT0L,GAAA,SAAIgB,QACGnN,WAAWgH,WAAWmF,GAAIgB,MAyBjClR,GAAA,SAAIT,EAAgB7E,eACb8E,MAAMQ,GAAIT,EAAQ7E,GAChB8J,QAsBTnE,IAAA,SAAKd,eACEC,MAAMa,IAAKd,GACTiF,QAWTjE,KAAA,SAAMf,gCAAkB+Y,mCAAAA,kCACjB/Y,OAAMe,cAAMf,UAAU+Y,IACpB/T,QAuBT1N,IAAA,SAAKgS,EAA4DpF,eAC1DK,WAAWgI,OAAOjV,IAAKgS,EAAQpF,GAC7Bc,QASTgU,OAAA,SAAQ9N,eACD3G,WAAWgI,OAAOyM,OAAQ9N,GACxBlG,QAUT9B,GAAA,SAAIsD,UACKxB,KAAKqT,KAAK7R,OAASA,KAQ5ByD,QAAA,uBACOlJ,KAAMxB,IACJyF,QAUT9D,QAAA,SAAS+G,OACCjI,EAAiBgF,KAAjBhF,MAAOgD,EAAUgC,KAAVhC,aAEVA,EAAME,GpF5TQ,KoF8TX1C,GpChU4B,QoCgUXwE,KAAK9D,QAAQtJ,KAAMoN,KAAMiD,GAAcjD,SAEtDA,KAAKT,WAAY,cACbrD,SAAWyX,EAAUzX,QAAS+G,OAGpClH,KAAMnB,MACNsB,YACC8D,KAAKqB,WACNpD,IpFnTa,IoFsTd+B,2DASAA,KAAKqT,mBAQD1W,OACH0W,EAASrT,KAATqT,OACDA,EAAM1W,GAENqD,KAAKhC,MAAME,GpF/VC,SoFgWZnC,KAAMvB,GAAe6Y,yCAUrBrT,KAAKT,WAAWgI,OAAOJ,WAAW,wCASlCnH,KAAKT,WAAWgH,WAAW0G,2BApW/BgH,GAIEb,SAAoB,GAJtBa,GASW5jB,OAASA"}