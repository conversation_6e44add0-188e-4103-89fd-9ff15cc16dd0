function DeleteConfirmFront(msgadd) {
  return window.confirm(msgadd);
}

function DeleteConfirm(msgadd) {
  return window.confirm("Opravdu chcete smazat " + msgadd + " ?");
}

function zobrazSkryj(idecko){
  el=document.getElementById(idecko).style;
  el.display=(el.display == 'block')?'none':'block';
}

function format_Number(num) {  
    nStr = num + '';  
    var x = nStr.split('.');  
    var x1 = x[0];  
    var x2;  
    x2 = x.length > 1 ? ',' + x[1] : '';
      
    var rgx = /(\d+)(\d{3})/;  
    while (rgx.test(x1)) {  
        x1 = x1.replace(rgx, '$1' + ' ' + '$2');
    }  
    return x1 + x2;  
}

$(document).ready(function() {
  //vymaze obsah
  $('.clear_me').one("focus", function() {
    $(this).val("");
  });

	// prepinani vyrobcu
	$('.producers').hide();
	$('h2.katalog').click( function() { $('.producers').hide(); $('.productmenu').show(); } );
	$('h2.vyrobci').click( function() { $('.producers').show(); $('.productmenu').hide(); } );

});