body {
  font: 13px Arial, sans-serif;
  color: #000;
  background: #f0f0f0; 
  padding: 50px 0
}

h1 {
  font-size: 150%;
  
}

h2 {
  font-size: 120%;
}

img {
  border: 0px;
}

form label {
  text-align: left;
}

#content {
  width: 90%;
  margin: 0 auto;
  background: #FFFFFF;
  padding: 2%
}
#home #content {
		
}
#footer {
	background: #42a135;
	color:#fff;
	margin: 20px 0;
	padding: 10px 0;
	width:100%;
	overflow:hidden;
	border-top: 35px solid #fff
}
#footer p {padding: 0 10px;color:#fff;text-align:right}
#footer a:link, #footer a:visited {color:#fff;}
#footer a:hover {color:#000}
form {
	background: #fff;
	border: 1px solid #d2d2d2;
	margin-bottom: 10px;
	padding: 10px 10px 0 10px
}
#homef form {
	border: 1px solid black
	
}
h3 {
	font-size: 130%;
	padding: 10px 0;
	color:#42a135
}
#home h3 {
	padding: 10px 20px;	
}
legend {
	font-size: 110%;
	padding-bottom: 0px;
	
}
table {
	background:#fff;
	color:#000;
  margin-top: 15px
}
#home form table {
	border: 5px solid #f0ed00		
}
th, td {
	text-align:left;	
	padding: 5px 10px;
}
#home table td, #home table th {
	padding: 10px;	
}

p {
	margin: 0;
	padding: 10px 0;	
	color:#000
}
#footer p img{float:right;border:10px solid #fff;margin: 10px;}
input, select {margin: 0 10px 10px 10px}
form input.default {
  
}

fieldset {
  border:none;
  padding: 10px;  
}

#formbutton {
  margin-left: 100px;
}
input.button {
	padding: 5px 10px;
	cursor:pointer;
	cursor:hand	
}
a:link, a:visited {
  color: #000;
  font-weight:bold;
}
a:hover {
	color:#840000	
}
#message {
  margin: 10px 0;
  color: #f00;
}

#logged-in {
  margin-top: 3em;
}

.flash {
  color: black;
  background: #CAFFCA;
  padding: 1em;
  margin: 1em auto;
  border: 5px solid #a4ffa3;
  font-weight: bolder
}

.err {
  color: black;
  background: #FFC0C0;
  border: 1px solid darkred;
  padding: 1em;
  margin: 1em 0;
  font-weight: bolder
}

.error {
  color: darkred;
  padding: 1em;
  margin-left: 10px;
  font-weight: bolder
}

.required {
  color: darkred;
}

.warning {
  color: black;
  background: #FFC0C0;
  padding: 1em;
  margin: 1em auto;
  border: 5px solid #ff999c
}

table.grid {
  margin: 3px auto 3px 0;
  border-collapse:collapse;
  border: 1px solid #b2b2b2
}

table.grid td, table.grid th {
  border: 1px solid #b2b2b2;
  padding: 3px 5px;
}

table.grid th {
  background: #000 ;
  text-align: left;
  color:#fff;
  border-right: 1px solid #fff;
}
table.delivery th {
  background: #d0d0d0 ;
  text-align: left;
  color:#000;
  border-right: 1px solid #b2b2b2;
}
table.grid .alt td {
  background: #f8f8f0;
}


#menu-top, #submenu {
	zoom: 1;
	overflow:hidden;
	background: #42a135
		
}
#menu-top {padding: 10px;}
#submenu {padding: 0 10px 10px 10px}
#menu-top p, #submenu p {
	padding: 0;
	margin: 0;
	padding: 10px 0;
	overflow:hidden;
	width:100%	
}
#menu-top span, #submenu span {
 display: inline;
 float:left;
 background: #317426;
 margin: 0 5px 5px 0 ;
}
#submenu span, #submenu a:link, #submenu a:visited {background:none}
#menu-top a, #submenu a {
	padding: 5px 10px;
	display: inline;
	float:left;
	zoom: 1
}
#menu-top a:link, #menu-top a:visited {
	color: #fff;
	text-decoration:none	
}
#menu-top a:hover {
	background: #000	
}
 
#submenu a:link, #submenu a:visited {
	color:#fff;	
}
#submenu a:hover {
	color:#000	
}

#menu-login {
  padding: 0px; 
  font-size: 85%;
  text-align: right;
}

#menu-top span.current,  #submenu span.current {
  font-weight: bolder;
  color: black;
  background: #FFFFFF;
}

.current {
  font-weight: bolder;
  color: black;
}

#ajax-spinner {
  margin: 15px 0 0 15px;
  padding: 13px;
  background: white url('../ico/spinner.gif') no-repeat 50% 50%;
  font-size: 0;
  z-index: 123456;
  display: none;
}
textarea.mceEditor {height:400px !important;font-size:14px !important}