{"version": 3, "sources": ["libs/normalize.scss", "styles.css", "libs/simplelightbox.scss", "base/variables.scss", "../js/libs/splide/css/splide-core.min.css", "../js/libs/nouislider/nouislider.css", "base/base.scss", "base/layout.scss", "base/fonts.scss", "base/typography.scss", "base/forms.scss", "icons/icon.scss", "icons/icons.scss", "components/header.scss", "components/nav.scss", "components/section.scss", "components/footer.scss", "components/alert.scss", "components/article.scss", "components/article-detail.scss", "components/avatar.scss", "components/badge.scss", "components/basket.scss", "components/breadcrumb.scss", "components/btn.scss", "components/category.scss", "components/contact-box.scss", "components/content-header.scss", "components/count.scss", "components/delivery.scss", "components/discount.scss", "components/filter-switch.scss", "components/goal.scss", "components/header-top.scss", "components/help.scss", "components/hey.scss", "components/instagram.scss", "components/link.scss", "components/login.scss", "components/manufacturer.scss", "components/modal.scss", "components/nav-footer.scss", "components/notice.scss", "components/nouislider-overwrite.scss", "components/pagination.scss", "components/phone.scss", "components/progress.scss", "components/question.scss", "components/register.scss", "components/reveal.scss", "components/shop.scss", "components/shop-detail.scss", "components/search.scss", "components/separator.scss", "components/sidebar.scss", "components/slider.scss", "components/sort.scss", "components/splide.scss", "components/star.scss", "components/store.scss", "components/table.scss", "components/tag.scss", "components/topic.scss", "components/warn.scss", "components/why.scss", "components/product.scss", "components/product-detail.scss", "components/product-helper.scss", "components/product-photo.scss", "components/order-benefits.scss", "components/order-progress.scss", "components/order-steps.scss", "components/order-sum.scss", "components/order-summary.scss", "components/form.scss", "components/form-checkbox.scss", "components/form-discount.scss", "components/form-radio.scss", "components/form-range.scss", "components/form-search.scss", "base/helpers.scss", "base/print.scss"], "names": [], "mappings": "AAAA,2EAAA,CAUA,KAEE,6BAAA,CADA,gBCDF,CDoBA,KACE,aCLF,CDaA,GACE,aAAA,CACA,cCNF,CDiBA,GACE,8BAAA,CAAA,sBAAA,CACA,QAAA,CACA,gBCRF,CDgBA,IACE,+BAAA,CACA,aCTF,CDmBA,EACE,4BCXF,CDmBA,YACE,kBAAA,CACA,yBAAA,CACA,wCAAA,CAAA,gCCZF,CDmBA,SAEE,kBCbF,CDqBA,cAGE,+BAAA,CACA,aCdF,CDqBA,MACE,aCfF,CDuBA,QAEE,aAAA,CACA,aAAA,CACA,iBAAA,CACA,uBChBF,CDmBA,IACE,aChBF,CDmBA,IACE,SChBF,CD0BA,IACE,iBClBF,CD6BA,sCAKE,mBAAA,CACA,cAAA,CACA,gBAAA,CACA,QCpBF,CD4BA,aAEE,gBCrBF,CD6BA,cAEE,mBCtBF,CD6BA,gDAIE,yBCvBF,CD8BA,wHAIE,iBAAA,CACA,SCxBF,CD+BA,4GAIE,6BCzBF,CDgCA,SACE,0BC1BF,CDoCA,OACE,6BAAA,CAAA,qBAAA,CACA,aAAA,CACA,aAAA,CACA,cAAA,CACA,SAAA,CACA,kBC3BF,CDkCA,SACE,uBC5BF,CDmCA,SACE,aC7BF,CDqCA,6BAEE,6BAAA,CAAA,qBAAA,CACA,SC9BF,CDqCA,kFAEE,WC/BF,CDuCA,cACE,4BAAA,CACA,mBChCF,CDuCA,yCACE,uBCjCF,CDyCA,6BACE,yBAAA,CACA,YClCF,CD4CA,QACE,aCpCF,CD2CA,QACE,iBCrCF,CDuDA,kBACE,YCxCF,CCvRA,mBACC,eD0RD,CCxRA,YAMC,eCjCe,CDgCf,QAAA,CAGA,YAAA,CANA,MAAA,CAKA,UCbsB,CDOtB,cAAA,CAEA,OAAA,CACA,KAAA,CAKA,YD2RD,CCzRA,YACC,YD4RD,CC3RC,mBAEC,sBAAA,CADA,QAAA,CAIA,cAAA,CAFA,iBAAA,CACA,SD8RF,CC5RE,yBACC,UD8RH,CC1RC,sBAYC,UChEc,CDqDd,YAAA,CAUA,uCAhEe,CAkEf,cAzDkB,CAoDlB,WAAA,CAEA,mBAAA,CAHA,kBAAA,CADA,gBAAA,CAJA,cAAA,CACA,UAAA,CACA,QAAA,CAKA,UAAA,CAJA,aDoSF,CC1RE,4BACC,YD4RH,CCxRC,wBAMC,UC9Ec,CDyEd,YAAA,CAMA,cAzEoB,CAsEpB,SAAA,CAFA,cAAA,CACA,QAAA,CAEA,YD4RF,CCvRC,2BAEC,YAAA,CADA,UD0RF,CCxRE,kCAWC,UChGa,CD6Fb,aAAA,CAEA,uCAhGc,CA0Fd,WAAA,CAEA,mBAAA,CAHA,gBAAA,CAFA,cAAA,CAMA,iBAAA,CALA,OAAA,CAGA,UAAA,CAIA,aD4RH,CCzRG,0CAEC,cAtFsB,CAqFtB,SD4RJ,CCxRG,0CAEC,cA3FsB,CA0FtB,QD2RJ,CCvRG,wCACC,YDyRJ,CCtRG,0BA1BD,kCA2BE,UDyRF,CCvRE,0CAEC,cAtGsB,CAqGtB,UD0RH,CCtRE,0CAEC,cA3GsB,CA0GtB,SDyRH,CACF,CCtRG,wBAvCD,kCAwCE,UDyRF,CCvRE,0CAEC,cAlHqB,CAiHrB,UD0RH,CCtRE,0CAEC,cAvHqB,CAsHrB,SDyRH,CACF,CCnRC,sBACC,cAAA,CAEC,qBAAA,CAED,iBAAA,CACA,aDmRF,CClRE,0BAIC,QAvImB,CAsInB,aAAA,CAGA,WAAA,CALA,QAAA,CACA,SAAA,CAGA,UDqRH,CCnRG,0BAPD,0BAQE,QDsRF,CACF,CCrRG,wBAVD,0BAWE,QDwRF,CACF,CCrRE,6BACC,eAAA,CACA,QDuRH,CCtRG,0BAHD,6BAIE,QDyRF,CACF,CCxRG,wBAND,6BAOE,QD2RF,CACF,CCzRE,kCAIC,yBA5KqB,CA+KrB,QAAA,CAJA,UA5KgB,CA0KhB,YAAA,CAIA,cA1KmB,CA6KnB,MAAA,CANA,YAAA,CAIA,iBAAA,CAGA,OD2RH,CCzRG,0CACC,WAAA,CACA,KD2RJ,CCxRG,8CACC,WD0RJ,CCtRE,mCAGC,UAAA,CAEA,UCtMa,CDkMb,YAAA,CACA,iBAAA,CAEA,SAAA,CAEA,YDwRH,CCnRA,YAaG,8CAAA,CAKD,sCAAA,CAhBA,qBAAA,CACA,kBAAA,CAFA,YAAA,CAGA,WAAA,CACA,QAAA,CACA,sBAAA,CACA,SAAA,CACA,cAAA,CACA,OAAA,CACA,UAAA,CACA,YD2RF,CCjRA,sBAIC,WAAA,CACA,eAAA,CAJA,iBAAA,CACA,WAAA,CACA,UDsRD,CCjRA,eAGE,iCAAA,CAEA,qCAAA,CAED,6CAAA,CAAA,gCAAA,CAAA,6BAAA,CAAA,wDDkRD,CC/QA,2BACE,GAEC,SAAA,CADA,2BAAA,CAAA,mBDmRD,CChRA,IACC,SDkRD,CChRA,GAEC,SAAA,CADA,4BAAA,CAAA,oBDmRD,CACF,CChRA,mBACE,GAEC,SAAA,CADA,2BAAA,CAAA,mBDmRD,CChRA,IACC,SDkRD,CChRA,GAEC,SAAA,CADA,4BAAA,CAAA,oBDmRD,CACF,CGhiBA,kCAA0B,GAAG,2BAAA,CAAA,mBH2kB3B,CG3kB+C,GAAG,+BAAA,CAAA,uBH8kBlD,CACF,CG/kBA,0BAA0B,GAAG,2BAAA,CAAA,mBH2kB3B,CG3kB+C,GAAG,+BAAA,CAAA,uBH8kBlD,CACF,CG/kB6E,oFAAoF,wBAAA,CAAyB,oBAAA,CAAqB,qBAAA,CAAA,gBHolB/M,CGplBgO,sGAAsG,aHwlBtU,CGxlBoV,oIAAoI,MAAA,CAAO,SAAA,CAAU,iBAAA,CAAkB,KAAA,CAAM,SHgmBjgB,CGhmB2gB,wJAAwJ,SAAA,CAAU,iBAAA,CAAkB,SHsmB/rB,CGtmBysB,aAAa,aH0mBttB,CG1mBouB,wHAAwH,aH8mB51B,CG9mB02B,mBAAmB,6BAAA,CAAA,qBAAA,CAAsB,iBHmnBn5B,CGnnBq6B,cAAc,kCAAA,CAAmC,0BAAA,CAA2B,mBAAA,CAAoB,mBAAA,CAAA,oBAAA,CAAA,YAAA,CAAa,WAAA,CAAY,kBAAA,CAAmB,mBAAA,CAAoB,mCAAA,CAAA,2BH8nBrkC,CG9nBimC,qDAAqD,aHkoBtpC,CGloBoqC,oBAAoB,qBAAA,CAAsB,wBAAA,CAAsF,oBAAA,CAAqB,uBAAA,CAA3G,0BAAA,CAAA,kBAAA,CAAmB,mBAAA,CAAoB,mBAAA,CAAA,oBAAA,CAAA,YAAA,CAAa,kBAAA,CAAmB,sBAAA,CAAA,cAAA,CAAoC,8BAAA,CAAA,sBAAA,CAAgC,mBH+oBz1C,CG/oB62C,uBAAuB,oBAAA,CAAqB,aAAA,CAAc,oBAAA,CAAqB,QAAA,CAAS,mBHupBr8C,CGvpBy9C,uBAAuB,OH2pBh/C,CG3pBw/C,QAAQ,YAAA,CAA+B,iBHiqB/hD,CGjqBijD,2CAA2C,kBHqqB5lD,CGrqB+mD,eAAmG,mBAAA,CAApF,kCAAA,CAAmC,0BAAA,CAA2B,6BAAA,CAAA,qBAAA,CAA0C,qBAAA,CAAA,aAAA,CAAc,8BAAA,CAA+B,QAAA,CAAS,YAAA,CAAa,iBHirBzyD,CGjrB2zD,mBAAmB,qBHqrB90D,CGrrBo2D,gBAAgB,iBHyrBp3D,CGzrBs4D,iBAAiB,mDAAA,CAAA,2CAAA,CAAkE,qBAAA,CAAA,6BAAA,CAA8B,iBAAA,CAAkB,QAAA,CAAS,oBAAA,CAAqB,WAAA,CAAY,MAAA,CAAO,WAAA,CAAY,iBAAA,CAAkB,OAAA,CAAQ,KAAA,CAAM,UHysBtmE,CGzsBinE,eAAe,eAAA,CAAgB,iBAAA,CAAkB,SH+sBlqE,CI3sBA,4BAEE,0BAAA,CACA,yCAAA,CAQA,6BAAA,CAAA,qBAAA,CANA,qBAAA,CACA,iBAAA,CAFA,wBAAA,CAGA,oBAAA,CACA,qBAAA,CACA,gBJktBF,CI9sBA,aACE,iBJitBF,CI/sBA,0BAGE,WAAA,CACA,iBAAA,CAFA,UAAA,CAGA,SJktBF,CI9sBA,eACE,eAAA,CACA,SJmtBF,CIjtBA,2BAOE,WAAA,CAJA,iBAAA,CAGA,OAAA,CADA,KAAA,CAIA,wBAAA,CACA,4BAAA,CAEA,oBAAA,CADA,mCAAA,CAEA,4BAAA,CAAA,oBAAA,CALA,UAAA,CANA,qBAAA,CAEA,SJ6tBF,CIhtBA,+CACE,MAAA,CACA,UJqtBF,CIhtBA,4BACE,SAAA,CACA,OJstBF,CIptBA,8BACE,QJutBF,CIrtBA,aACE,kCAAA,CACA,0BAAA,CACA,iBJwtBF,CIttBA,iBACE,WAAA,CACA,UJytBF,CIvtBA,2DAEE,gCAAA,CACA,wCAAA,CAAA,gCAAA,CAAA,2BAAA,CAAA,wBAAA,CAAA,8CJ0tBF,CIxtBA,mBACE,wBJ2tBF,CIvtBA,iBACE,WJ4tBF,CI1tBA,8BAEE,WAAA,CACA,WAAA,CAFA,UJguBF,CI3tBA,eACE,UJ8tBF,CI5tBA,4BAIE,YAAA,CAFA,WAAA,CACA,UAAA,CAFA,UJkuBF,CI7tBA,+CACE,UAAA,CACA,UJguBF,CI3tBA,aACE,kBAAA,CAEA,wBAAA,CADA,iBAAA,CAEA,8DAAA,CAAA,sDJiuBF,CI/tBA,eACE,iBJkuBF,CIhuBA,cACE,kBJmuBF,CI/tBA,gBACE,gBJouBF,CIluBA,+BACE,gBJquBF,CInuBA,aAGE,eAAA,CAFA,wBAAA,CACA,iBAAA,CAGA,iFAAA,CAAA,yEAAA,CADA,cJuuBF,CIpuBA,aACE,8EAAA,CAAA,sEJuuBF,CInuBA,uCAOE,kBAAA,CALA,UAAA,CACA,aAAA,CAEA,WAAA,CAGA,SAAA,CAJA,iBAAA,CAKA,OAAA,CAHA,SJ2uBF,CItuBA,mBACE,SJyuBF,CIvuBA,qEAGE,UAAA,CACA,QAAA,CACA,QAAA,CAHA,UJ6uBF,CIxuBA,kCACE,QJ2uBF,CIvuBA,yBACE,kBJ4uBF,CI1uBA,sEAGE,kBJ6uBF,CIxuBA,wBAGE,6BAAA,CAAA,qBJ8uBF,CI5uBA,WAEE,UAAA,CADA,iBJgvBF,CI1uBA,YACE,iBAAA,CAEA,iBAAA,CADA,kBJivBF,CI9uBA,gBACE,UAAA,CACA,iBJivBF,CI5uBA,aAEE,eAAA,CADA,iBJmvBF,CI7uBA,oCACE,eJovBF,CI/uBA,sBAEE,WAAA,CAEA,MAAA,CAHA,cAAA,CAEA,QAAA,CAEA,UJqvBF,CInvBA,uBACE,qCAAA,CACA,iCAAA,CAAA,6BJsvBF,CIpvBA,iCACE,oCAAA,CACA,gCAAA,CAAA,4BJuvBF,CIrvBA,oCAGE,UAAA,CAFA,gBAAA,CACA,SJyvBF,CItvBA,wCACE,WJyvBF,CIvvBA,0CACE,WJ0vBF,CIrvBA,oBAEE,WAAA,CAEA,SAAA,CAHA,cAAA,CAEA,KJ4vBF,CIzvBA,qBAGE,iBAAA,CAFA,kCAAA,CACA,8BAAA,CAAA,0BJ6vBF,CI1vBA,+BACE,iCAAA,CACA,6BAAA,CAAA,yBJ6vBF,CI3vBA,kCAEE,UAAA,CACA,eAAA,CAFA,SJgwBF,CI5vBA,sCACE,UJ+vBF,CI7vBA,wCACE,UJgwBF,CI9vBA,cAKE,eAAA,CAFA,wBAAA,CACA,iBAAA,CAEA,UAAA,CALA,aAAA,CAMA,WAAA,CALA,iBAAA,CAMA,iBAAA,CACA,kBJiwBF,CI/vBA,+BAIE,WAAA,CADA,QAAA,CAFA,iCAAA,CACA,6BAAA,CAAA,yBJowBF,CIhwBA,6BAIE,UAAA,CADA,OAAA,CAFA,kCAAA,CACA,8BAAA,CAAA,0BJqwBF,CIjwBA,4CAIE,WAAA,CADA,SAAA,CAFA,gCAAA,CACA,4BAAA,CAAA,wBJswBF,CIlwBA,0CAIE,UAAA,CADA,QAAA,CAFA,mCAAA,CACA,+BAAA,CAAA,2BJuwBF,CKljCA,KACE,6BAAA,CAAA,qBLqjCF,CKljCA,iBAGE,0BAAA,CAAA,kBLqjCF,CKljCA,IACE,8BAAA,CAAA,sBLqjCF,CKjjCA,MACE,cLojCF,CKhjCA,KAWE,wBHrBkB,CGgBlB,UHvBc,CGwBd,6BAAA,CACA,cAAA,CACA,aAAA,CANA,QAAA,CACA,gBAAA,CAHA,iBL0jCF,CK9iCE,yBAbF,KAcI,iBLijCF,CACF,CK/iCE,0BAjBF,KAkBI,iBLkjCF,CACF,CK9iCA,cACE,kBLijCF,CK9iCA,QAOE,WAAA,CALA,oBAAA,CAGA,WLgjCF,CK3iCA,eANE,cLqjCF,CMxmCA,WAEE,aAAA,CADA,gBJmDQ,CIjDR,cN2mCF,CMvmCA,kBACE,eN0mCF,CMtmCA,oBAGE,wBAAA,CAAA,qBAAA,CAFA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CACA,qCAAA,CAAA,6BNymCF,CMvmCE,0BALF,oBAMI,wBAAA,CAAA,oBAAA,CAAA,gBN0mCF,CACF,CMtmCA,KAGE,uBAAA,CAAA,oBAAA,CAFA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CACA,8BAAA,CAAA,sBAAA,CAEA,cNwmCF,CMrmCE,UACE,YNumCJ,CMjmCE,yBADF,YAEI,aNqmCF,CACF,CMhmCE,iBACE,6BAAA,CAAA,4BAAA,CAAA,0BAAA,CAAA,sBAAA,CAAA,kBNmmCJ,CM9lCA,YACE,wBAAA,CAAA,qBAAA,CAAA,qCAAA,CAAA,6BNimCF,CM7lCA,aACE,wBAAA,CAAA,qBAAA,CAAA,0BAAA,CAAA,kBNgmCF,CM5lCA,YACE,sBAAA,CAAA,mBAAA,CAAA,kCAAA,CAAA,0BN+lCF,CM3lCA,YACE,QN8lCF,CM5lCE,iBACE,SN8lCJ,CMzlCA,KAEE,kBAAA,CACA,2BAAA,CAAA,4BAAA,CAFA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,qBAAA,CAAA,iBAAA,CAAA,aAAA,CACA,6BAAA,CAAA,yBAAA,CAAA,qBN4lCF,CMvlCE,yBAQF,gBAEI,4BAAA,CAAA,uBAAA,CAAA,eAAA,CAEA,SNwlCF,CANF,CM/kCE,yBAPF,QAQI,kBNylCF,CACF,CMplCE,yBADF,QAEI,4BAAA,CAAA,uBAAA,CAAA,eAAA,CAEA,SNulCF,CACF,CMrlCE,yBAPF,QAQI,SNwlCF,CACF,CMnlCE,yBADF,QAEI,4BAAA,CAAA,uBAAA,CAAA,eAAA,CAEA,SNslCF,CACF,CMplCE,yBAPF,QAQI,SNulCF,CACF,CMllCE,yBADF,QAEI,4BAAA,CAAA,uBAAA,CAAA,eAAA,CAEA,SNqlCF,CACF,CMnlCE,yBAPF,QAQI,oBNslCF,CACF,CMllCA,WACE,kBAAA,CAAA,mBAAA,CAAA,mBAAA,CAAA,WNqlCF,CMjlCA,UACE,qBAAA,CAAA,kBAAA,CAAA,4BAAA,CAAA,oBNolCF,CMhlCA,YACE,uBAAA,CAAA,oBAAA,CAAA,8BAAA,CAAA,sBNmlCF,CM/kCA,aACE,wBAAA,CAAA,qBAAA,CAAA,0BAAA,CAAA,kBNklCF,CM9kCA,UACE,6BAAA,CAAA,4BAAA,CAAA,0BAAA,CAAA,sBAAA,CAAA,kBNilCF,COnvCA,WAIE,iBAAA,CAHA,kBAAA,CACA,iBAAA,CACA,eAAA,CAEA,uDAAA,CACA,8XPuvCF,CO/uCA,WAIE,iBAAA,CAHA,kBAAA,CACA,iBAAA,CACA,eAAA,CAEA,mDAAA,CACA,0WPkvCF,CO1uCA,WAIE,iBAAA,CAHA,kBAAA,CACA,iBAAA,CACA,eAAA,CAEA,mDAAA,CACA,0WP6uCF,COruCA,WAIE,iBAAA,CAHA,kBAAA,CACA,iBAAA,CACA,eAAA,CAEA,mDAAA,CACA,0WPwuCF,CQxxCA,YAOE,eAAA,CACA,eAAA,CAHA,kBAAA,CADA,YR6xCF,CQvxCE,yBAVF,YAWI,eR6xCF,CACF,CQ1xCA,GACE,kBR6xCF,CQ3xCE,yBAHF,GAII,gBR8xCF,CACF,CQ3xCA,GACE,iBR8xCF,CQ5xCE,yBAHF,GAII,kBR+xCF,CACF,CQ5xCA,GACE,kBR+xCF,CQ7xCE,yBAHF,GAII,kBRgyCF,CQ5xCF,GAEI,kBRgyCF,CALF,CQtxCA,EACE,UR+xCF,CQ7xCE,gBAEE,aR8xCJ,CQ1xCI,yBADF,gBAEI,oBR6xCJ,CACF,CQxxCA,EAGE,eAAA,CAFA,eR4xCF,CQxxCE,yBALF,EAMI,eR2xCF,CACF,CQvxCA,MAKE,eAAA,CAHA,eAAA,CACA,kBR2xCF,CQvxCE,wBAEE,QAAA,CACA,sBR2xCJ,CQxxCM,gFACE,gBR6xCR,CQtxCA,GACE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAIA,eAAA,CAFA,aRyxCF,CQrxCE,MACE,kBAAA,CAAA,oBAAA,CAAA,gBAAA,CAAA,YAAA,CAIA,eAAA,CAFA,kBRuxCJ,CQlxCE,MACE,kBAAA,CAAA,oBAAA,CAAA,gBAAA,CAAA,YAAA,CAGA,eRmxCJ,CSv4CA,sBAWE,qBPTc,COOd,wBAAA,CACA,iBP4BO,COjCP,UPFc,COGd,mBAAA,CACA,aAAA,CAJA,iBTg5CF,CSt4CE,wCACE,oBPNiB,COOjB,ST04CJ,CSv4CE,uDACE,oBT24CJ,CSx4CE,oDACE,oBT44CJ,CSv4CA,SAGE,eAAA,CAFA,UT24CF,CSr4CA,MACE,kBTw4CF,CSt4CE,SACE,aAAA,CACA,iBTw4CJ,CSn4CA,0BAIE,cAAA,CAFA,UTu4CF,CSj4CA,uCAEE,UTo4CF,CU37CA,MAIE,URDc,CQFd,oBAAA,CAEA,iBAAA,CADA,qBVg8CF,CU57CE,aACE,UAAA,CACA,aV87CJ,CUz7CA,WAME,iBAAA,CADA,WAAA,CAFA,MAAA,CAIA,mBAAA,CANA,iBAAA,CACA,KAAA,CAMA,+BAAA,CAAA,uBAAA,CAJA,UVg8CF,CWj9CA,kBACE,UXo9CF,CWl9CE,yBACE,gBXo9CJ,CWh9CA,cACE,UXm9CF,CWj9CE,qBACE,gBXm9CJ,CW/8CA,WACE,UXk9CF,CWh9CE,kBACE,gBXk9CJ,CW98CA,aACE,UXi9CF,CW/8CE,oBACE,gBXi9CJ,CW78CA,aACE,UXg9CF,CW98CE,oBACE,gBXg9CJ,CW58CA,sBACE,UX+8CF,CW78CE,6BACE,gBX+8CJ,CW38CA,uBACE,UX88CF,CW58CE,8BACE,gBX88CJ,CW18CA,0BACE,UX68CF,CW38CE,iCACE,gBX68CJ,CWz8CA,eACE,UX48CF,CW18CE,sBACE,gBX48CJ,CWx8CA,cACE,UX28CF,CWz8CE,qBACE,gBX28CJ,CWv8CA,cACE,UX08CF,CWx8CE,qBACE,gBX08CJ,CWt8CA,aACE,UXy8CF,CWv8CE,oBACE,gBXy8CJ,CWr8CA,kBACE,UXw8CF,CWt8CE,yBACE,gBXw8CJ,CWp8CA,aACE,UXu8CF,CWr8CE,oBACE,gBXu8CJ,CWn8CA,YACE,UXs8CF,CWp8CE,mBACE,gBXs8CJ,CWl8CA,YACE,UXq8CF,CWn8CE,mBACE,gBXq8CJ,CWj8CA,mBACE,UXo8CF,CWl8CE,0BACE,gBXo8CJ,CWh8CA,sBACE,UXm8CF,CWj8CE,6BACE,gBXm8CJ,CW/7CA,oBACE,UXk8CF,CWh8CE,2BACE,gBXk8CJ,CW97CA,iBACE,UXi8CF,CW/7CE,wBACE,gBXi8CJ,CW77CA,aACE,UXg8CF,CW97CE,oBACE,gBXg8CJ,CW57CA,YACE,UX+7CF,CW77CE,mBACE,gBX+7CJ,CW37CA,kBACE,UX87CF,CW57CE,yBACE,gBX87CJ,CW17CA,aACE,UX67CF,CW37CE,oBACE,gBX67CJ,CWz7CA,WACE,UX47CF,CW17CE,kBACE,gBX47CJ,CWx7CA,YACE,UX27CF,CWz7CE,mBACE,gBX27CJ,CWv7CA,aACE,UX07CF,CWx7CE,oBACE,gBX07CJ,CWt7CA,eACE,UXy7CF,CWv7CE,sBACE,gBXy7CJ,CWr7CA,YACE,UXw7CF,CWt7CE,mBACE,gBXw7CJ,CY5pDA,QAOE,qBVLc,CUDd,cAAA,CACA,KAAA,CAGA,UAAA,CAFA,WZiqDF,CYzpDA,gBAGE,cAAA,CAFA,cZ6pDF,CYzpDE,mBACE,QZ2pDJ,CYzpDI,qBACE,aZ2pDN,CYxpDI,uBACE,aAAA,CAEA,UZypDN,CYvpDM,yBALF,uBAMI,UZ0pDN,CACF,CYnpDI,4EACE,mBZwpDN,CYrpDI,2CACE,iBZupDN,CYlpDI,8BACE,QZopDN,CY9oDA,cAEE,wBAAA,CAAA,qBAAA,CACA,wBAAA,CAAA,qBAAA,CADA,0BAAA,CAAA,kBAAA,CAGA,qBVzDc,CUqDd,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,qCAAA,CAAA,6BZkpDF,CY9oDE,yBAPF,cASI,mBAAA,CADA,gBZkpDF,CACF,CY7oDA,cACE,kBAAA,CAAA,qBAAA,CAAA,iBAAA,CAAA,aZgpDF,CY9oDE,0BAHF,cAII,kBAAA,CAAA,oBAAA,CAAA,gBAAA,CAAA,YZipDF,CACF,CY/oDE,gBAIE,oBAAA,CAEA,mBAAA,CACA,mBAAA,CANA,iBAAA,CACA,WZopDJ,CY7oDI,yBATF,gBAUI,mBAAA,CACA,mBZgpDJ,CACF,CY9oDI,0BAdF,gBAeI,mBAAA,CACA,mBZipDJ,CACF,CY9oDE,kBACE,mBZgpDJ,CY9oDI,yBAHF,kBAII,mBZipDJ,CACF,CY/oDI,0BAPF,kBASI,mBAAA,CADA,eZmpDJ,CACF,CY7oDA,aAEE,wBAAA,CAAA,qBAAA,CACA,oBAAA,CAAA,iBAAA,CADA,0BAAA,CAAA,kBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,gCAAA,CAAA,wBZgpDF,CY9oDE,0BALF,aAMI,kBAAA,CAAA,oBAAA,CAAA,gBAAA,CAAA,YZipDF,CACF,CY/oDE,eACE,oBZipDJ,CY9oDE,mBACE,UZgpDJ,CY3oDA,aAGE,YAAA,CAEA,iBAAA,CAJA,iBZgpDF,CY1oDE,yBAPF,aAQI,aZ6oDF,CACF,CY3oDE,yBAXF,aAYI,iBZ8oDF,CACF,CY5oDE,oBAGE,SAAA,CAFA,iBAAA,CACA,QZ+oDJ,CYzoDA,kBASE,qDAAA,CAEA,cAAA,CAPA,WAAA,CACA,kBAAA,CAJA,eAAA,CAMA,kBAAA,CAJA,UZgpDF,CYtoDE,yBAbF,kBAcI,YZyoDF,CACF,CYvoDE,0BACE,2CZyoDJ,CYpoDA,gBACE,iBZuoDF,CYroDE,yBAHF,gBAII,YZwoDF,CACF,Ca5zDA,KAME,wBXOa,CWZb,YAAA,CAEA,kBAAA,CACA,eb+zDF,Ca3zDE,yBARF,KASI,ab8zDF,CACF,Ca5zDE,yBAZF,KAeI,6BAAA,CAFA,abg0DF,CACF,Ca5zDE,aAIE,aAAA,CAGA,gBAAA,CANA,iBAAA,CAKA,UAAA,CAJA,Wbi0DJ,Ca1zDI,wBAEE,cAAA,CADA,eb6zDN,CaxzDE,gBAKE,oBAAA,CAHA,QAAA,CACA,Sb2zDJ,CarzDI,yBADF,QAEI,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YbwzDJ,CalzDI,0BAEI,abqzDR,CAJF,Ca3yDE,gBAGE,kBAAA,CAFA,iBbmzDJ,Ca/yDI,0BALF,gBAMI,kBbkzDJ,CACF,Ca5yDE,aAUE,qCAAA,CADA,2BAAA,CAJA,UXxEY,CWoEZ,aAAA,CAEA,iBAAA,CAGA,oBAAA,CACA,wBb+yDJ,Ca1yDI,yBAZF,aAeI,qCAAA,CAFA,Yb8yDJ,CACF,Ca1yDI,0BAlBF,aAmBI,iBb6yDJ,CACF,Ca3yDI,0BAtBF,aAuBI,iBb8yDJ,CACF,Ca5yDI,0BA1BF,aA2BI,eb+yDJ,CACF,Ca3yDI,yBACE,mBAGE,wBXpGO,CWkGP,Ub8yDN,CazyDI,+BACE,4Cb2yDN,CACF,CavyDE,yBACE,oCbyyDJ,CavyDI,yBAHF,yBAII,kBb0yDJ,CACF,CaxyDI,0BAPF,yBAQI,kBb2yDJ,CACF,CavyDI,yBACE,iCAGE,wBX9HO,CW+HP,4CAAA,CAHA,Ub2yDN,CaryDI,2CACE,abuyDN,CACF,CajyDA,iBACE,wBboyDF,CalyDE,yBAHF,iBAII,YbqyDF,CACF,CajyDA,YAKE,wBXxJa,CWoJb,YAAA,CAEA,qBboyDF,CahyDE,yBAPF,YAUI,MAAA,CAGA,iBAAA,CALA,iBAAA,CACA,OAAA,CAEA,WboyDF,CACF,CahyDE,sBAME,wBAAA,CAFA,eAAA,CAHA,YAAA,CACA,YboyDJ,Ca9xDI,yBARF,sBASI,UbiyDJ,CACF,Ca/xDI,wDAEE,wBbgyDN,Ca1xDA,aAEE,wBAAA,CAAA,qBAAA,CACA,wBAAA,CAAA,qBAAA,CADA,0BAAA,CAAA,kBAAA,CASA,qBXtMc,CW4Ld,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAOA,kBAAA,CACA,eAAA,CANA,qCAAA,CAAA,6BAAA,CAEA,qBAAA,CACA,0Bb+xDF,CaxxDE,yBAbF,aAcI,Yb2xDF,CACF,CazxDE,eACE,Qb2xDJ,CaxxDM,yBADF,0BAEI,Ub2xDN,CACF,CavxDE,eACE,kBAAA,CACA,eAAA,CACA,oBbyxDJ,CatxDM,yBADF,yBAEI,YbyxDN,CACF,CarxDE,mBACE,gBAAA,CACA,kBbuxDJ,Cc7/DA,SACE,cdggEF,Cc9/DE,yBAHF,SAII,cdigEF,CACF,Cc7/DA,gBACE,cdggEF,Cc9/DE,yBAHF,gBAII,cdigEF,CACF,Cc7/DA,eACE,qBdggEF,Cc5/DA,eACE,wBd+/DF,Cc3/DA,kBAGE,wBZxBkB,CYsBlB,Ud+/DF,Ccz/DA,iBACE,ad4/DF,Ccx/DA,cACE,Sd2/DF,Ccv/DA,iBACE,gBd0/DF,Ccv/DI,yBADF,mCAEI,ed0/DJ,CACF,Ccx/DI,yCAKE,aZpDS,CYgDT,oBAAA,CAKA,kBAAA,CACA,iBAAA,CAJA,gBd4/DN,Ccr/DI,iHAGE,edu/DN,Ccr/DM,yBALF,iHAMI,ed0/DN,Ccn/DF,eAEI,iBdu/DF,Ccr/DE,2GAME,gBdu/DJ,CAbF,Ccp+DI,yBAFF,oCAGI,eds/DJ,CACF,Ccj/DA,kBACE,kBAAA,CAAA,qBAAA,CAAA,iBAAA,CAAA,aAAA,CAEA,cdm/DF,Cc/+DA,gBAEE,wBAAA,CAAA,qBAAA,CAEA,uBAAA,CAAA,oBAAA,CAFA,0BAAA,CAAA,kBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CACA,8BAAA,CAAA,sBdk/DF,Cch/DE,yBANF,gBAOI,wBAAA,CAAA,qBAAA,CAAA,qCAAA,CAAA,6BAAA,CAEA,kBdk/DF,CACF,Cch/DE,yBACE,sBACE,Ydk/DJ,CACF,Cc/+DE,kBACE,Qdi/DJ,Cc5+DA,gBAGE,oBAAA,CAAA,iBAAA,CAFA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CACA,gCAAA,CAAA,wBAAA,CAEA,ed8+DF,Cc5+DE,sCAGE,eAAA,CAEA,iBAAA,CAHA,Udg/DJ,Cc3+DI,yBAPF,sCASI,QAAA,CADA,Sdg/DJ,CACF,Cc7+DI,yBAZF,sCAaI,kBdi/DJ,CACF,Cc7+DI,yBADF,oBAEI,gBdg/DJ,CACF,Cc1+DE,wBACE,Ud6+DJ,Ccx+DA,kBACE,iBAAA,CACA,kBd2+DF,Ccz+DE,yBAJF,kBAKI,oBd4+DF,CACF,Cc1+DE,qBAGE,iBAAA,CAFA,kBd6+DJ,Ccx+DE,oBAGE,gBAAA,CAFA,iBd2+DJ,Ccr+DI,yBADF,oCAEI,iBdw+DJ,CACF,Cct+DI,yBALF,oCAMI,ady+DJ,CACF,Ccv+DI,0BATF,oCAUI,iBd0+DJ,CACF,Ccr+DA,kBAKE,wBAAA,CAHA,UAAA,CACA,aAAA,CAFA,Ud2+DF,Ccr+DE,qBACE,Ydu+DJ,Ccl+DA,iBACE,adq+DF,Ccn+DE,yBAHF,iBAII,ads+DF,CACF,Ccp+DE,oBACE,QAAA,CACA,Sds+DJ,Ccn+DE,2BACE,kBdq+DJ,Ccl+DE,0BAEE,aAAA,CADA,cdq+DJ,Cc/9DA,iBAKE,aAAA,CAJA,iBAAA,CACA,SAAA,CAIA,iBAAA,CAHA,Udo+DF,Cc/9DE,yBARF,iBASI,Udk+DF,CACF,Cc99DA,YAGE,eAAA,CAFA,edk+DF,Cc99DE,yBALF,YAMI,edi+DF,CACF,CevuEA,QACE,cf0uEF,CexuEE,yBAHF,QAII,cf2uEF,CACF,CezuEE,yBAPF,QAQI,iBf4uEF,CACF,Ce1uEE,WACE,mBAAA,CACA,ef4uEJ,CezuEE,qBACE,ef2uEJ,CezuEI,yBAHF,qBAKI,gBAAA,CADA,iBf6uEJ,CACF,CevuEA,iBAIE,oBAAA,CAHA,aAAA,CACA,Sf2uEF,CevuEE,yBANF,iBAOI,ef0uEF,CACF,CexuEE,oBAEE,wBAAA,CAAA,qBAAA,CACA,uBAAA,CAAA,oBAAA,CADA,0BAAA,CAAA,kBAAA,CAUA,qBb/CY,Ca8CZ,iBbVK,CaAL,0BAAA,CAAA,2BAAA,CAAA,0BAAA,CAAA,mBAAA,CAKA,WAAA,CAHA,8BAAA,CAAA,sBAAA,CAIA,kBAAA,CAEA,kBAAA,CAJA,Uf8uEJ,CeruEI,yBAdF,oBAeI,kBfwuEJ,CACF,CenuEA,gBACE,uBAAA,CAAA,oBAAA,CAAA,8BAAA,CAAA,sBAAA,CAEA,iBfquEF,CenuEE,yBALF,gBAMI,UfsuEF,CACF,CepuEE,kBACE,QfsuEJ,CejuEA,mBAGE,kBAAA,CAFA,QAAA,CAGA,iBfmuEF,CejuEE,qBACE,efmuEJ,Ce9tEA,cAIE,gBAAA,CACA,eAAA,CAHA,aAAA,CADA,eAAA,CAKA,iBfguEF,CgB3zEA,OAOE,wBdMa,CcVb,UdDc,CcEd,kBAAA,CACA,eAAA,CAJA,mBhBk0EF,CgB1zEE,yBATF,OAUI,iBAAA,CACA,ahB6zEF,CACF,CgBxzEI,uCAEE,UhB4zEN,CgBxzEE,cACE,ehB0zEJ,CgBvzEE,aAME,Ud/BY,Cc0BZ,YAAA,CAGA,iBAAA,CADA,UhB0zEJ,CgBrzEI,yBARF,aASI,oBhBwzEJ,CACF,CgBtzEI,0BASE,cAAA,CAJA,oBAAA,CAJA,iBAAA,CAEA,OAAA,CADA,QAAA,CAKA,UhBuzEN,CgBnzEM,gEAEE,UhBozER,CgB/yEE,kBAGE,kBAAA,CAFA,iBhBkzEJ,CgB5yEE,kBACE,wBhB8yEJ,CgB1yEE,iBACE,wBhB4yEJ,CgBxyEE,eAGE,kBdhEW,CciEX,mFd3Cc,Cc2Cd,6Dd3Cc,Cc2Cd,sDd3Cc,CcwCd,UhB4yEJ,CgBvyEI,sCAEE,UhByyEN,CiB33EA,SACE,oBjB83EF,CiB53EE,YACE,kBjB83EJ,CiBz3EA,eACE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAGA,kBAAA,CADA,ejB43EF,CiBz3EE,yBAPF,eAQI,wBAAA,CAAA,oBAAA,CAAA,gBAAA,CAEA,kBjB23EF,CiBx3EA,iCAEI,2BjB23EJ,CAJF,CiBp3EI,yBALF,iCAMI,iBjB43EJ,CACF,CiBv3EA,gBAEE,wBAAA,CAAA,qBAAA,CAAA,0BAAA,CAAA,kBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YjB23EF,CiBx3EE,gCACE,ejB03EJ,CiBv3EE,mBAGE,cAAA,CAFA,QjB03EJ,CiBt3EI,0BAGE,afrCS,CemCT,aAAA,CAGA,gBjBu3EN,CiBn3EE,gCACE,kBAAA,CAAA,sBAAA,CAAA,kBAAA,CAAA,cjBq3EJ,CiBl3EE,kCACE,kBjBo3EJ,CiB/2EA,gBAOE,iBflCO,CegCP,QAAA,CAFA,eAAA,CAFA,iBjBq3EF,CiB72EE,yBATF,gBAUI,kBAAA,CAAA,qBAAA,CAAA,iBAAA,CAAA,ajBg3EF,CACF,CiB92EE,oBACE,aAAA,CAGA,WAAA,CAEA,mBAAA,CAAA,gBAAA,CAHA,UjBi3EJ,CiBz2EA,eAQE,qBAAA,CAAA,kBAAA,CAAA,4BAAA,CAAA,oBAAA,CAQA,sGAAA,CAAA,6EAAA,CAAA,sEAAA,CAZA,QAAA,CASA,UfpGc,Ce8Fd,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAOA,iBAAA,CATA,MAAA,CAKA,QAAA,CACA,iBAAA,CAVA,iBAAA,CAEA,OAAA,CADA,KjBs3EF,CiBt2EE,sBAKE,afnGW,Ce+FX,oBAAA,CAEA,iBAAA,CAGA,wBjBs2EJ,CiBj2EA,kBAEE,kBAAA,CACA,2BAAA,CAAA,4BAAA,CAFA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,qBAAA,CAAA,iBAAA,CAAA,aAAA,CACA,6BAAA,CAAA,yBAAA,CAAA,qBAAA,CAEA,gBjBm2EF,CiB/1EA,sBAGE,af5Ha,Ce6Hb,kBAAA,CACA,gBAAA,CAJA,kBjBq2EF,CiB71EA,eAEE,eAAA,CADA,ejBi2EF,CkB1+EA,gBACE,gBlB6+EF,CkB3+EE,yBAHF,gBAII,iBlB8+EF,CACF,CkB1+EA,sBAME,ehBZc,CgBWd,iBhBwBO,CgB1BP,QAAA,CAFA,iBlBg/EF,CkBz+EE,yBARF,sBASI,elB4+EF,CACF,CkB1+EE,0BACE,alB4+EJ,CkBv+EA,sBAQE,sGAAA,CAAA,6EAAA,CAAA,sEAAA,CADA,iBhBIO,CgBPP,UhB7Bc,CgB8Bd,kBAAA,CAJA,QAAA,CACA,YlB8+EF,CkBt+EE,yBAVF,sBAkBI,qBAAA,CAAA,kBAAA,CACA,wBAAA,CAAA,qBAAA,CADA,4BAAA,CAAA,oBAAA,CAJA,QAAA,CAGA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,qCAAA,CAAA,6BAAA,CAJA,MAAA,CAMA,iBAAA,CAVA,iBAAA,CAEA,OAAA,CADA,KlBg/EF,CACF,CkBr+EE,yBAGE,mBAAA,CAFA,elBw+EJ,CkBp+EI,yBALF,yBAQI,gBAAA,CAFA,iBlBw+EJ,CACF,CkBp+EI,+BAGE,ahBpDS,CgBkDT,aAAA,CAGA,iBAAA,CACA,eAAA,CACA,wBlBq+EN,CkBj+EE,wBACE,kBlBm+EJ,CkBj+EI,yBAHF,wBAII,iBlBo+EJ,CACF,CkBl+EI,+BACE,mBAAA,CACA,eAAA,CACA,alBo+EN,CkBj+EI,gCACE,kBlBm+EN,CkBj+EM,yBAHF,gCAII,YlBo+EN,CACF,CkB99EA,yBACE,iBlBi+EF,CkB/9EE,yBAHF,yBAII,YlBk+EF,CACF,CkBh+EE,uCACE,kBlBk+EJ,CkBh+EI,yDACE,gBlBk+EN,CkB/9EQ,yBADF,2DAEI,elBk+ER,CACF,CkB39EA,wBAQE,qBhB7Hc,CgByHd,ahBrHa,CgBsHb,kBAAA,CAJA,eAAA,CACA,YAAA,CAIA,iBlB89EF,CkB19EE,yBAVF,wBAWI,YlB69EF,CACF,CkB39EE,sDAKE,oBAAA,CAHA,QAAA,CACA,SlB89EJ,CkBz9EE,2BACE,oBAAA,CAGA,mBAAA,CADA,SlB29EJ,CkBx9EI,yBANF,2BAQI,gBAAA,CADA,SlB49EJ,CACF,CkBz9EI,kCACE,aAAA,CAEA,mBAAA,CACA,alB09EN,CkBp9EA,uBAGE,kBAAA,CAFA,QlBw9EF,CkBp9EE,yBALF,uBAMI,elBu9EF,CACF,CkBr9EE,8BACE,mBAAA,CACA,alBu9EJ,CkBp9EE,6BACE,gBlBs9EJ,CkBj9EA,wBAOE,qBAAA,CAHA,cAAA,CACA,eAAA,CAJA,aAAA,CACA,YlBu9EF,CkBh9EE,yBATF,wBAUI,YlBm9EF,CACF,CkBj9EE,0BAGE,eAAA,CAFA,elBo9EJ,CkB/8EE,qCACE,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAEA,kBlBg9EJ,CkB98EI,uCACE,clBg9EN,CkB78EI,4CACE,mBAAA,CACA,elB+8EN,CmBnqFA,QAQE,iBAAA,CAPA,aAAA,CAIA,YAAA,CAHA,eAAA,CAIA,SAAA,CAFA,WnBwqFF,CmBlqFE,YAEE,WAAA,CADA,UnBqqFJ,CmB/pFA,eAEE,WAAA,CADA,UnBmqFF,CoBrrFA,OAUE,wBAAA,CACA,iBlB2BO,CkBjCP,aAAA,CAJA,oBAAA,CAKA,kBAAA,CACA,eAAA,CAJA,gBAAA,CAKA,oBpBwrFF,CoB1qFE,6CAIE,oBlBbW,CkBWX,UpBirFJ,CoB1qFA,gBAGE,wBAAA,CAFA,UpB8qFF,CqB9sFA,QAGE,kBAAA,CACA,eAAA,CAHA,iBrBmtFF,CqB9sFE,cAaE,wBnBZgB,CmBGhB,YAAA,CAHA,UAAA,CAMA,aAAA,CACA,YAAA,CAGA,WAAA,CANA,SAAA,CAFA,iBAAA,CAYA,+BAAA,CAAA,2BAAA,CAAA,uBAAA,CALA,UrBgtFJ,CqBxsFE,yBAGI,oGAEE,arBusFN,CqBnsFI,wEACE,YrBqsFN,CACF,CqBjsFE,UACE,UnBrCY,CmBsCZ,oBrBmsFJ,CqBhsFE,cACE,erBksFJ,CqB/rFE,cAEE,iBAAA,CADA,UrBksFJ,CqB9rFE,eAGE,SAAA,CAFA,iBAAA,CACA,QrBisFJ,CqB5rFE,4BACE,UrB8rFJ,CqB3rFE,kCAEE,qBnB/DY,CmB8DZ,iBrB8rFJ,CqB1rFE,kCAGE,kBnB3DW,CmByDX,qBAAA,CACA,iBrB6rFJ,CqBzrFE,wCACE,wBrB2rFJ,CqBtrFA,eACE,YrByrFF,CqBvrFE,yBAHF,eAII,oBrB0rFF,CACF,CqBtrFA,gBAUE,UnBlGc,CmB6Fd,YAAA,CAGA,gBAAA,CAPA,iBAAA,CACA,OAAA,CAKA,WAAA,CAJA,WrB6rFF,CqBlrFA,iBASE,wBnB1GkB,CmBwGlB,0BnBzEO,CmB0EP,2BnB1EO,CmBsEP,8BAAA,CAHA,aAAA,CAIA,2BAAA,CAFA,UrByrFF,CqBjrFE,yBAXF,iBAYI,8BrBorFF,CACF,CqBhrFA,gBAME,wBAAA,CADA,6BnBxFO,CmBuFP,8BnBvFO,CmBqFP,sBAAA,CADA,UrBurFF,CqBhrFE,kBACE,erBkrFJ,CqB/qFE,sEAEE,QAAA,CACA,SrBirFJ,CqB5qFA,aAGE,iBAAA,CACA,eAAA,CAHA,kBAAA,CAIA,gBrB8qFF,CqB5qFE,oBACE,oBAAA,CAIA,mBAAA,CAFA,qBrB8qFJ,CsBr0FA,YACE,apBOmB,CoBNnB,iBAAA,CACA,etBw0FF,CsBt0FE,yBALF,YAMI,atBy0FF,CACF,CsBv0FE,cAGE,UpBVY,CoBQZ,gBAAA,CAGA,oBtBw0FJ,CsBt0FI,oBASE,8CAAA,CARA,UAAA,CAEA,oBAAA,CAGA,UAAA,CACA,eAAA,CAFA,StBy0FN,CsBl0FI,wCAEE,yBtBm0FN,CsB/zFE,kBAGE,UpBlCY,CoBgCZ,UtBk0FJ,CuBp2FA,WAiBE,wBrBJa,CqBEb,WAAA,CACA,iBrBsBO,CqBhCP,UrBHc,CqBgBd,cAAA,CAjBA,oBAAA,CAKA,YAAA,CACA,kBAAA,CACA,eAAA,CACA,aAAA,CANA,iBAAA,CAOA,iBAAA,CACA,oBAAA,CACA,wBvBy2FF,CuBj2FE,8CAEE,mFrBYc,CqBZd,6DrBYc,CqBZd,sDvBo2FJ,CuBj2FE,uBAEE,oBAAA,CADA,UvBq2FJ,CuBj2FE,mCACE,YvBo2FJ,CuBl2FI,yBAHF,mCAII,oBvBs2FJ,CACF,CuBj2FA,qBAEE,kBvBo2FF,CuBl2FE,yBAJF,qBAKI,iBvBs2FF,CACF,CuBl2FA,6BAIE,wBrBrCgB,CqBmChB,UvBs2FF,CuBl2FE,kFAEE,wBAAA,CACA,qBvBq2FJ,CuBl2FE,yCACE,UvBq2FJ,CuBh2FA,2BAIE,wBrBrDe,CqBmDf,UvBo2FF,CuBh2FE,8EAEE,wBAAA,CACA,qBvBm2FJ,CuBh2FE,uCACE,UvBm2FJ,CuB91FA,2BAIE,wBrBrFa,CqBmFb,UvBk2FF,CuB91FE,8EAIE,qBrB/FY,CqBgGZ,qBAAA,CAHA,UvBm2FJ,CuB31FA,uBAEE,wBvB81FF,CuB51FE,sEAEE,wBrBhGW,CqBiGX,qBvB+1FJ,CuB11FA,yBAKE,kBAAA,CAFA,eAAA,CADA,UvB+1FF,CuB11FE,yBAPF,yBAUI,kBAAA,CAFA,iBvB+1FF,CACF,CuB31FE,0EAEE,qBrB/HY,CqBgIZ,qBvB81FJ,CuB31FE,qCAIE,UrBxIY,CqBsIZ,gBAAA,CADA,UvBg2FJ,CuB11FE,oGACE,evB+1FJ,CuB11FA,2BAIE,qBAAA,CAFA,UvB81FF,CuBx1FA,+BAIE,wBAAA,CAFA,UvB41FF,CuBt1FA,2BAEE,wBvBy1FF,CuBv1FE,8EAEE,wBAAA,CACA,qBvB01FJ,CuBr1FA,yBAIE,wBAAA,CAFA,UvBy1FF,CwB1gGA,UAEE,wBAAA,CAAA,qBAAA,CAAA,0BAAA,CAAA,kBAAA,CAUA,wBAAA,CADA,iBtB2BO,CsB/BP,UtBLc,CsBDd,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAOA,eAAA,CAJA,aAAA,CACA,iBAAA,CAIA,oBxB6gGF,CwBxgGE,yBAdF,UAeI,QxB2gGF,CACF,CwBzgGE,gCAIE,wBtBhBW,CsBcX,UxB2gGJ,CwBpgGA,iBAQE,qBtBjCc,CsBgCd,iBtBIO,CsBPP,WAAA,CACA,iBAAA,CAJA,eAAA,CAEA,UxB0gGF,CyBxiGA,aAEE,wBAAA,CAAA,qBAAA,CAAA,0BAAA,CAAA,kBAAA,CAEA,avBEa,CuBLb,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAIA,kBzB0iGF,CyBxiGE,eAIE,eAAA,CAFA,QAAA,CADA,ezB4iGJ,CyBtiGE,eACE,kBzBwiGJ,CyBtiGI,yBAHF,eAII,kBzByiGJ,CACF,CyBtiGE,oBAKE,UvBxBY,CuBoBZ,oBAAA,CAKA,mBAAA,CAHA,iBzByiGJ,CyBniGE,mBAKE,aAAA,CAJA,oBAAA,CAKA,kBAAA,CAHA,iBzBsiGJ,CyBhiGE,qBACE,gBzBkiGJ,CyBhiGI,yBAHF,qBAII,iBzBmiGJ,CACF,CyBjiGI,yBAPF,qBAQI,iBzBoiGJ,CACF,CyBhiGI,2BACE,OzBkiGN,CyB3hGE,qBACE,kBzB8hGJ,CyBzhGA,0BACE,ezB4hGF,C0BhmGA,gBAKE,wBxBEkB,CwBJlB,UxBDc,CwBDd,c1BqmGF,C0B/lGE,yBAPF,gBAQI,mB1BkmGF,CACF,C0BhmGE,mBAGE,eAAA,CAFA,e1BmmGJ,C0B9lGE,mBAIE,oBAAA,CAHA,eAAA,CACA,S1BimGJ,C0B5lGE,mBACE,Q1B8lGJ,C0B3lGE,2BACE,iB1B6lGJ,C0B1lGE,sBACE,Y1B4lGJ,C0B1lGI,yBAHF,sBAII,aAAA,CACA,WAAA,CAIA,eAAA,CADA,eAAA,CADA,U1B8lGJ,CACF,C0B1lGI,+BACE,Y1B4lGN,C0BrlGE,yBADF,wBAEI,gB1BylGF,CACF,C0BplGE,0BADF,yBAEI,gBAAA,CACA,mB1BwlGF,C0BtlGE,4BAEE,iBAAA,CADA,e1BylGJ,C0BrlGE,sDACE,iB1BulGJ,CACF,C0BllGA,6BAGE,axBtEmB,CwBuEnB,mBAAA,CACA,gBAAA,CAJA,e1BwlGF,C0BllGE,+BACE,a1BolGJ,C0BllGI,0EAEE,U1BmlGN,C0B/kGE,qCAEE,gBAAA,CADA,iB1BklGJ,C0B5kGA,yBAGE,axB7FmB,CwB8FnB,mBAAA,CACA,eAAA,CAJA,e1BklGF,C0B5kGE,yBAPF,yBASI,kBAAA,CADA,gB1BglGF,CACF,C0B7kGE,kDACE,Y1B+kGJ,C0B5kGE,2BAGE,UxBnHY,CwBiHZ,aAAA,CAGA,kBAAA,CACA,e1B6kGJ,C0B1kGE,+BAGE,iBAAA,CAFA,iB1B6kGJ,C0BtkGA,wBACE,Q1BykGF,C0BvkGE,yBAHF,wBAKI,wBAAA,CAAA,qBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,qCAAA,CAAA,6BAAA,CAEA,mB1BykGF,CACF,C0BvkGE,qCAEE,QAAA,CACA,gBAAA,CAFA,U1B2kGJ,C0BvkGI,yBALF,qCAMI,gB1B0kGJ,CACF,C0BxkGI,2CAKE,iBxBxJU,CwBoJV,oBAAA,CAEA,e1B0kGN,C0BtkGM,yBAPF,2CAQI,gB1BykGN,CACF,C0BtkGI,0CAIE,kBAAA,CAHA,kBAAA,CACA,iB1BykGN,C2B5uGA,OAcE,wBzBDa,CyBAb,iBAAA,CANA,UzBJc,CyBFd,oBAAA,CAOA,kBAAA,CACA,eAAA,CALA,WAAA,CAMA,aAAA,CAPA,cAAA,CAEA,iBAAA,CAMA,iB3B+uGF,C2BzuGE,kBAGE,wBzBFc,CyBAd,U3B4uGJ,C2BtuGE,iBAGE,wBzBPa,CyBKb,U3ByuGJ,C4BlwGA,UAOE,qB1BLc,C0BDd,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAEA,eAAA,CACA,Y5BqwGF,C4BjwGE,yBATF,UAUI,wBAAA,CAAA,oBAAA,CAAA,gB5BowGF,CACF,C4BlwGE,YACE,Q5BowGJ,C4BjwGE,cACE,e5BmwGJ,C4BhwGE,aACE,e5BkwGJ,C4B7vGA,gBACE,kBAAA,CAAA,qBAAA,CAAA,iBAAA,CAAA,a5BgwGF,C4B9vGE,oBACE,aAAA,CAEA,a5B+vGJ,C4B1vGA,mBAEE,cAAA,CADA,U5B8vGF,C4B3vGE,yBAJF,mBAKI,iB5B8vGF,CACF,C4B5vGE,sBAGE,iBAAA,CACA,eAAA,CAHA,Q5BgwGJ,C4B3vGI,0BACE,iBAAA,CAEA,qB5B4vGN,C4BxvGE,wBACE,e5B0vGJ,C4BvvGE,qBAGE,a1B5DW,C0B0DX,e5B0vGJ,C4BtvGI,yBALF,qBAMI,gB5ByvGJ,CACF,C4BpvGA,iBACE,eAAA,CACA,e5BuvGF,C4BrvGE,yBAJF,iBAKI,gB5BwvGF,CACF,C4BtvGE,0BARF,iBASI,eAAA,CACA,kB5ByvGF,CACF,C4BvvGE,wBACE,mBAAA,CACA,kB5ByvGJ,C6Bn1GA,UAEE,kBAAA,CAaA,wB3BFa,C2BCb,iBAAA,CANA,U3BLc,C2BFd,oBAAA,CACA,qBAAA,CAAA,iBAAA,CAAA,aAAA,CAOA,cAAA,CACA,eAAA,CALA,WAAA,CAMA,aAAA,CALA,gBAAA,CAMA,iBAAA,CARA,U7B81GF,C6Bh1GE,qBAGE,wB3BHc,C2BCd,U7Bm1GJ,C6B70GE,oBAGE,wB3BRa,C2BMb,U7Bg1GJ,C8Bz2GE,0BADF,eAEI,Y9B62GF,CACF,C8B32GE,kBACE,iB9B62GJ,C8B12GE,qBACE,U9B42GJ,C+Br3GE,SAGE,iBAAA,CAFA,iBAAA,CAGA,wB/Bu3GJ,C+Bp3GE,kBAKE,oBAAA,CAHA,QAAA,CACA,S/Bu3GJ,C+Bl3GE,QACE,aAAA,CACA,kBAAA,CACA,oBAAA,CACA,wB/Bo3GJ,C+Bl3GI,4BAEE,a/Bm3GN,C+B72GA,aAOE,iB7BCO,C6BHP,QAAA,CAFA,eAAA,CAFA,iB/Bm3GF,C+Bz2GI,8CACE,6BAAA,CAAA,yBAAA,CAAA,qB/B22GN,C+Bv2GE,iBACE,aAAA,CAEA,0BAAA,CAAA,qBAAA,CAAA,kB/Bw2GJ,C+Bn2GA,eASE,2BAAA,CAAA,4BAAA,CACA,oBAAA,CAAA,iBAAA,CAKA,qIAAA,CAAA,gGAAA,CAAA,yFAAA,CAXA,QAAA,CAIA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,6BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,gCAAA,CAAA,wBAAA,CALA,MAAA,CAOA,QAAA,CACA,iBAAA,CAZA,iBAAA,CAEA,OAAA,CADA,KAAA,CAIA,U/B42GF,CgCx6GA,YAOE,wBAAA,CANA,YAAA,CAIA,iBAAA,CAFA,ahC46GF,CgCt6GE,yBATF,YAUI,ahCy6GF,CACF,CgCv6GE,cAGE,aAAA,CAFA,QhC06GJ,CgCr6GE,cACE,oBhCu6GJ,CgCr6GI,wCAEE,UhCs6GN,CgCl6GE,uBAEE,wBAAA,CAAA,qBAAA,CACA,wBAAA,CAAA,qBAAA,CADA,0BAAA,CAAA,kBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,qCAAA,CAAA,6BhCo6GJ,CgCh6GE,mBACE,a9B9BW,C8B+BX,gBAAA,CACA,kBhCk6GJ,CgCh6GI,0BACE,OhCk6GN,CgC35GE,0BADF,iBAEI,kBAAA,CAAA,oBAAA,CAAA,gBAAA,CAAA,YhC+5GF,CACF,CgC75GE,wCAOE,aAAA,CAFA,oBAAA,CAHA,QAAA,CACA,ShCi6GJ,CgC15GE,oBACE,oBhC45GJ,CgCx5GM,0DACE,ahC05GR,CgCt5GI,gCAGE,6CAAA,CACA,wBAAA,CAHA,iBhC05GN,CgCr5GM,4EAEE,yChCs5GR,CgCj5GE,uBAIE,YAAA,CAEA,eAAA,CALA,iBAAA,CACA,YhCq5GJ,CgC/4GI,0BAKE,wBAAA,CAJA,aAAA,CAEA,gBhCi5GN,CgC74GM,sCACE,gBhC+4GR,CgC54GM,qCACE,mBhC84GR,CgCz4GE,mBAGE,a9B3GW,C8ByGX,iBhC44GJ,CgCr4GA,qBAEE,wBAAA,CAAA,qBAAA,CACA,uBAAA,CAAA,oBAAA,CADA,0BAAA,CAAA,kBAAA,CADA,YAAA,CAMA,kBAAA,CAJA,8BAAA,CAAA,sBAAA,CAEA,QhCw4GF,CgCp4GE,yBATF,qBAUI,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YhCu4GF,CACF,CgCr4GE,0BAbF,qBAcI,kBAAA,CAAA,oBAAA,CAAA,gBAAA,CAAA,YhCw4GF,CACF,CgCt4GE,uBAGE,kBAAA,CACA,eAAA,CAHA,mBhC04GJ,CgCl4GA,kBACE,gBhCq4GF,CgCn4GE,0BAHF,kBAII,kBAAA,CAAA,oBAAA,CAAA,gBAAA,CAAA,YhCs4GF,CACF,CgCp4GE,oBACE,UhCs4GJ,CgCp4GI,kFAGE,ShCo4GN,CiCjiHA,MASE,6CAAA,CARA,oBAAA,CAGA,WAAA,CACA,UAAA,CAEA,qBAAA,CAJA,UjCuiHF,CkC1iHA,KAEE,wBAAA,CAAA,qBAAA,CAEA,uBAAA,CAAA,oBAAA,CAFA,0BAAA,CAAA,kBAAA,CASA,wBhCImB,CgCdnB,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAMA,mBAAA,CALA,8BAAA,CAAA,sBAAA,CAEA,aAAA,CACA,YlC8iHF,CkCxiHE,yBAbF,KAcI,wBAAA,CAAA,qBAAA,CAAA,qCAAA,CAAA,6BlC2iHF,CACF,CkCziHE,OAGE,eAAA,CAFA,WlC4iHJ,CkCxiHI,yBALF,OAMI,UlC2iHJ,CACF,CkCxiHE,OACE,elC0iHJ,CkCviHE,WACE,UlCyiHJ,CkCpiHA,aACE,uBAAA,CAAA,oBAAA,CAAA,8BAAA,CAAA,sBlCuiHF,CkCriHE,wBACE,kBAAA,CAAA,qBAAA,CAAA,iBAAA,CAAA,alCuiHJ,CkCliHA,eACE,uBAAA,CAAA,oBAAA,CAAA,8BAAA,CAAA,sBAAA,CAEA,iBlCoiHF,CkCliHE,yBALF,eAMI,2BAAA,CAAA,4BAAA,CAAA,6BAAA,CAAA,yBAAA,CAAA,qBlCqiHF,CACF,CkCniHE,iBACE,alCqiHJ,CkChiHA,WACE,kBAAA,CAAA,qBAAA,CAAA,iBAAA,CAAA,alCmiHF,CkC9hHE,yBADF,WAEI,YlCkiHF,CACF,CmCvmHA,WAOE,wBAAA,CAJA,UjCDc,CiCEd,kBAAA,CACA,eAAA,CAJA,cnC8mHF,CmCtmHE,yBATF,WAUI,mBnCymHF,CACF,CmCvmHE,aACE,aAAA,CACA,enCymHJ,CmCvmHI,sCAEE,anCwmHN,CmCtmHM,8CACE,4BAAA,CAAA,wBAAA,CAAA,oBnCwmHR,CmCnmHE,eAGE,iBjCQK,CiCVL,aAAA,CAIA,0BAAA,CAAA,qBAAA,CAAA,kBnCmmHJ,CmC/lHI,yBADF,mBAEI,kBAAA,CAAA,oBAAA,CAAA,gBAAA,CAAA,YAAA,CAEA,WnCimHJ,CACF,CmC5lHA,mBAEE,wBAAA,CAAA,qBAAA,CAEA,uBAAA,CAAA,oBAAA,CAFA,0BAAA,CAAA,kBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CACA,8BAAA,CAAA,sBAAA,CAEA,kBnC8lHF,CmC5lHE,yBARF,mBASI,wBAAA,CAAA,qBAAA,CAAA,qCAAA,CAAA,6BnC+lHF,CmC5lHA,sBAII,kBAAA,CAFA,QnCgmHJ,CALF,CmCrlHE,qBACE,QnC6lHJ,CmC1lHE,qBAEE,wBAAA,CAAA,qBAAA,CACA,2BAAA,CAAA,4BAAA,CADA,0BAAA,CAAA,kBAAA,CAKA,UjC1EY,CiCoEZ,0BAAA,CAAA,2BAAA,CAAA,0BAAA,CAAA,mBAAA,CAEA,6BAAA,CAAA,yBAAA,CAAA,qBAAA,CAKA,kBAAA,CACA,iBAAA,CAJA,SnC8lHJ,CmCxlHI,yBAXF,qBAYI,oBAAA,CAKA,kBAAA,CAFA,gBAAA,CADA,UnC4lHJ,CACF,CmCtlHE,yBAEE,iBAAA,CADA,UnCylHJ,CoCprHA,MACE,eAAA,CACA,yBpCurHF,CoCrrHE,yBAJF,MAKI,kBpCwrHF,CACF,CoCtrHE,YAYE,6CAAA,CAXA,UAAA,CAEA,oBAAA,CAGA,WAAA,CAEA,eAAA,CADA,eAAA,CAGA,qBAAA,CALA,UpC2rHJ,CoC/qHA,eACE,UpCkrHF,CoChrHE,sDAEE,6CpCirHJ,CoC9qHE,0CAEE,apC+qHJ,CoCzqHE,gBACE,gCAAA,CAAA,4BAAA,CAAA,wBpC4qHJ,CoCtqHE,kBACE,+BAAA,CAAA,2BAAA,CAAA,uBpCyqHJ,CoCnqHE,kBACE,YpCsqHJ,CoCnqHE,mBAYE,6CAAA,CAXA,UAAA,CAEA,oBAAA,CAGA,WAAA,CAEA,iBAAA,CADA,eAAA,CAOA,gCAAA,CAAA,4BAAA,CAAA,wBAAA,CAJA,qBAAA,CALA,UpCyqHJ,CqCzuHA,OAGE,iBAAA,CAFA,iBrC6uHF,CqCzuHE,yBALF,OAMI,aAAA,CAEA,iBrC2uHF,CACF,CqCzuHE,aAYE,wBnChBgB,CmCQhB,YAAA,CAHA,UAAA,CAMA,YAAA,CAGA,WAAA,CALA,QAAA,CAFA,iBAAA,CAWA,+BAAA,CAAA,2BAAA,CAAA,uBAAA,CALA,UrC2uHJ,CqCnuHE,cAGE,SAAA,CAEA,eAAA,CAJA,iBAAA,CACA,QrCuuHJ,CqCluHI,oBAGE,UnCpCU,CmCkCV,UrCquHN,CqC9tHI,yBACE,mDAEE,arC+tHN,CACF,CqC1tHI,oBACE,arC4tHN,CqCztHI,6BACE,arC2tHN,CqCxtHI,gCACE,WrC0tHN,CqCptHA,eAUE,UnC3Ec,CmCsEd,YAAA,CAGA,gBAAA,CAPA,iBAAA,CACA,WAAA,CAKA,WAAA,CAJA,WrC2tHF,CqChtHA,gBAME,wBnChFkB,CmC+ElB,iBnChDO,CmC4CP,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,UrCotHF,CqC7sHA,aACE,kBAAA,CAMA,wBAAA,CADA,6BnC3DO,CmC0DP,0BnC1DO,CmCsDP,oBAAA,CAAA,gBAAA,CAAA,YAAA,CAEA,2BrCktHF,CqC5sHE,eACE,QrC8sHJ,CqC3sHE,iBACE,erC6sHJ,CqC1sHE,mBACE,UrC4sHJ,CqCzsHE,2BACE,UrC2sHJ,CqCxsHE,kBAEE,eAAA,CAEA,iBAAA,CAHA,UrC4sHJ,CqCpsHA,iBACE,kBAAA,CAAA,oBAAA,CAAA,gBAAA,CAAA,YAAA,CAEA,2BrCssHF,CqCpsHE,oBACE,mBAAA,CACA,eAAA,CACA,erCssHJ,CqCnsHE,oBAIE,oBAAA,CAHA,aAAA,CACA,SrCssHJ,CqCjsHE,oBAME,2CAAA,CAFA,kBAAA,CAHA,QAAA,CACA,sBrCqsHJ,CqC/rHI,sBACE,UnCtJU,CmCuJV,cAAA,CACA,yBrCisHN,CqC7rHE,mBACE,QrC+rHJ,CqC7rHI,uBACE,gBrC+rHN,CqCzrHA,cAWE,cAAA,CAFA,WAAA,CAHA,eAAA,CALA,iBAAA,CAEA,UAAA,CADA,QAAA,CAMA,UAAA,CAJA,UrCgsHF,CqCvrHE,wCAEE,UrCwrHJ,CqCrrHE,oBAGE,UnC3LY,CmCyLZ,UrCwrHJ,CsCn3HA,cAOE,qBpCLc,CoCDd,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAEA,eAAA,CACA,YtCs3HF,CsCl3HE,yBATF,cAUI,wBAAA,CAAA,oBAAA,CAAA,gBtCq3HF,CACF,CsCn3HE,+BACE,etCq3HJ,CsCh3HA,oBACE,kBAAA,CAAA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAEA,YtCk3HF,CsCh3HE,wBACE,aAAA,CAGA,aAAA,CADA,gBtCk3HJ,CsC52HA,uBAEE,cAAA,CADA,UtCg3HF,CsC72HE,yBAJF,uBAKI,iBtCg3HF,CACF,CsC92HE,0BACE,etCg3HJ,CsC72HE,yBACE,atC+2HJ,CsC72HI,yBAHF,yBAII,gBtCg3HJ,CACF,CsC92HI,oCACE,etCg3HN,CuCr6HA,OAaE,+BAAA,CATA,QAAA,CAIA,YAAA,CAHA,MAAA,CAIA,aAAA,CARA,cAAA,CAEA,OAAA,CADA,KAAA,CASA,UAAA,CALA,YvC46HF,CuCn6HE,eACE,avCq6HJ,CuC95HE,yDACE,evCk6HJ,CuCh6HI,yBAHF,yDAII,iBvCo6HJ,CACF,CuCj6HE,2DAEE,UAAA,CADA,QvCq6HJ,CuCl6HI,yBAJF,2DAMI,UAAA,CADA,QvCu6HJ,CACF,CuCh6HE,0BACE,SvCm6HJ,CuC75HE,oBAGE,mBAAA,CAFA,kBAAA,CAGA,iBvC+5HJ,CuC35HI,kCACE,mBvC65HN,CuCz5HM,mDACE,QvC25HR,CuCv5HQ,yBADF,wDAEI,YvC05HR,CACF,CuCt5HI,sCACE,kBvCw5HN,CuCr5HI,iDAEE,WAAA,CAGA,WAAA,CACA,WAAA,CALA,SAAA,CAGA,UvCw5HN,CuCp5HM,yBARF,iDAYI,WAAA,CACA,WAAA,CAJA,UAAA,CAEA,UvCw5HN,CuC/4HF,qBAII,aAAA,CAFA,cvCo5HF,CANF,CuCz4HE,wBAGE,mBAAA,CAFA,kBAAA,CAGA,iBvCi5HJ,CuC94HE,kCACE,evCg5HJ,CuC94HI,yBAHF,kCAKI,QAAA,CADA,OAAA,CAGA,sCAAA,CAAA,kCAAA,CAAA,8BvCg5HJ,CACF,CuC14HE,gBAGE,UrChIY,CqC8HZ,kBAAA,CAGA,iBvC44HJ,CuC14HI,sBAIE,UrCvIU,CqCqIV,gBAAA,CADA,UvC84HN,CuCv4HE,0BACE,wBvCy4HJ,CuCr4HI,iCACE,UvCu4HN,CuCn4HE,8BACE,uBvCq4HJ,CuCl4HE,oBACE,kBvCo4HJ,CuC/3HA,aAOE,wBrC9JkB,CqC2JlB,eAAA,CACA,YAAA,CAJA,iBAAA,CAEA,UvCo4HF,CuC93HE,yBATF,aAWI,QAAA,CAEA,YAAA,CAHA,QAAA,CAKA,kCAAA,CAAA,8BAAA,CAAA,0BvC+3HF,CACF,CuC33HA,cAUE,cAAA,CAFA,WAAA,CAHA,eAAA,CAJA,iBAAA,CAEA,UAAA,CADA,QAAA,CAKA,UvC83HF,CuCz3HE,yBAZF,cAcI,WAAA,CADA,UvC63HF,CACF,CuC13HE,wCAEE,UvC23HJ,CuCx3HE,oBACE,UvC03HJ,CuCx3HI,yBAHF,oBAII,UvC23HJ,CACF,CuCt3HA,eAEE,wBAAA,CAAA,qBAAA,CAAA,0BAAA,CAAA,kBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,sBAAA,CAAA,kBAAA,CAAA,cvCy3HF,CuCv3HE,yBALF,eAOI,wBAAA,CAAA,qBAAA,CADA,wBAAA,CAAA,oBAAA,CAAA,gBAAA,CACA,qCAAA,CAAA,6BAAA,CAEA,gBvCy3HF,CACF,CuCv3HE,iBACE,WvCy3HJ,CuCv3HI,yBAHF,iBAII,QvC03HJ,CACF,CuCv3HE,qBACE,cvCy3HJ,CuCp3HA,iBAGE,wBrCxOkB,CqCsOlB,sBvCw3HF,CuCp3HE,oBAGE,gBAAA,CAFA,iBvCu3HJ,CuCh3HA,qBAME,iBAAA,CAFA,oBAAA,CAHA,QAAA,CACA,cvCq3HF,CuC/2HE,2BAGE,arC1PW,CqCwPX,UvCk3HJ,CuC72HE,4BACE,kBvC+2HJ,CwC1nIA,YAME,wBtCCkB,CsCJlB,UtCDc,CsCEd,kBAAA,CAHA,iBxCgoIF,CwCznIE,yBARF,YASI,cxC4nIF,CACF,CwC1nIE,eAGE,atCPiB,CsCQjB,kBAAA,CACA,eAAA,CAJA,eAAA,CAKA,wBxC2nIJ,CwCznII,yBARF,eAWI,UtCrBU,CsCmBV,kBxC6nIJ,CACF,CwCxnIM,yBADF,sBAYI,qDAAA,CACA,yBAAA,CAXA,UAAA,CAEA,oBAAA,CAGA,WAAA,CACA,mBAAA,CAOA,UAAA,CACA,+BAAA,CAAA,2BAAA,CAAA,uBAAA,CANA,qBAAA,CAJA,UxCgoIN,CACF,CwCnnII,uBAGE,UtC/CU,CsC6CV,iBxCsnIN,CwClnIM,8BACE,SAAA,CACA,gCAAA,CAAA,4BAAA,CAAA,wBxConIR,CwC/mIE,8BAKE,oBAAA,CAHA,QAAA,CACA,SxCknIJ,CwC7mIE,eACE,YxC+mIJ,CwC7mII,yBAHF,eAII,axCgnIJ,CACF,CwC9mII,uBACE,axCgnIN,CwC5mIE,eACE,gBxC8mIJ,CwC5mII,yBAHF,eAII,gBxC+mIJ,CACF,CwC5mIE,cACE,UAAA,CACA,oBxC8mIJ,CwC5mII,wCAEE,axC6mIN,CwC1mII,+BACE,yBxC4mIN,CyC5sIA,QACE,avCmBe,CuClBf,ezC+sIF,C0CjtIA,iBACE,UAAA,CACA,Y1CotIF,C0CjtIA,cACE,wB1CotIF,C0CjtIA,aAEE,wBxCJkB,CwCGlB,QAAA,CAEA,uBAAA,CAAA,e1CotIF,C0CjtIA,8BASE,qBxCtBc,CwCoBd,wBAAA,CACA,iBAAA,CAEA,uBAAA,CAAA,eAAA,CALA,WAAA,CAHA,UAAA,CADA,QAAA,CAGA,U1CwtIF,C0ChtIE,yEAEE,Y1CitIJ,C2C5uIE,+BAiBE,wBAAA,CADA,iBAAA,CARA,UzCPY,CyCCZ,oBAAA,CAOA,kBAAA,CACA,eAAA,CAJA,WAAA,CAKA,gBAAA,CARA,eAAA,CASA,iBAAA,CAEA,oBAAA,CADA,qBAAA,CARA,U3CwvIJ,C2CvuIA,wBACE,qB3C0uIF,C2CtuIA,sBAGE,sBAAA,CAFA,U3C0uIF,C2CpuIA,sCAOE,sBAAA,CACA,gDAAA,CAEA,uBAAA,CADA,2BAAA,CANA,WAAA,CAEA,kBAAA,CAHA,U3C6uIF,C2CjuIA,kBACE,gCAAA,CAAA,4BAAA,CAAA,wB3CouIF,C4CvxIA,OAGE,iBAAA,CAFA,iB5C2xIF,C4CvxIE,cAaE,wB1CViB,C0CSjB,iBAAA,CAXA,UAAA,CAMA,aAAA,CAGA,UAAA,CALA,QAAA,CAFA,iBAAA,CACA,OAAA,CAKA,S5CyxIJ,C4CjxII,yBACE,wB5CmxIN,C4C9wII,wBACE,wB5CgxIN,C6C7yIA,UASE,wB3CFkB,C2CClB,kBAAA,CALA,oBAAA,CAGA,UAAA,CALA,iBAAA,CAIA,U7CizIF,C6CxyIE,gCACE,wB7C2yIJ,C6CtyIA,eAOE,wB3Cda,C2Cab,kBAAA,CAFA,UAAA,CAHA,iBAAA,CAEA,U7C2yIF,C8Cl0IA,UASE,qB5CPc,C4CMd,iB5C8BO,C4CjCP,a5CCa,C4CAb,kBAAA,CAJA,kBAAA,CADA,eAAA,CAEA,Y9Cy0IF,C8Cj0IE,yBAXF,UAYI,Y9Co0IF,CACF,C8Cl0IE,aAOE,+CAAA,CAHA,U5ChBY,C4CiBZ,kBAAA,CAJA,iBAAA,CACA,iB9Cu0IJ,C8C/zIE,YAGE,gBAAA,CAFA,Q9Ck0IJ,C8C7zIE,YACE,U9C+zIJ,C8C1zIA,iBAGE,aAAA,CADA,cAAA,CADA,e9C+zIF,C8CzzIA,gBAGE,U5C5Cc,C4C6Cd,mBAAA,CAHA,mB9C8zIF,C8CvzIA,kBASE,wB5CtDkB,C4CqDlB,iB5CtBO,C4CoBP,aAAA,CAHA,eAAA,CACA,YAAA,CAHA,iB9C+zIF,C8CrzIE,yBAXF,kBAYI,Y9CwzIF,CACF,C8CtzIE,yBAYE,wB5CxEgB,C4C6DhB,UAAA,CAMA,aAAA,CAGA,WAAA,CALA,SAAA,CAFA,iBAAA,CACA,QAAA,CAUA,+BAAA,CAAA,2BAAA,CAAA,uBAAA,CALA,U9CwzIJ,C8ChzIE,qBAOE,eAAA,CAFA,U5CvFY,C4CmFZ,oBAAA,CAEA,S9CmzIJ,C8C5yIE,oBACE,U9C8yIJ,C8C3yIE,kCAKE,U5CrGY,C4CiGZ,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAKA,aAAA,CAHA,gB9C8yIJ,C8CzyII,qCACE,gBAAA,CACA,iB9C2yIN,C8CxyII,0CACE,mBAAA,CAAA,qBAAA,CAAA,aAAA,CAEA,iB9CyyIN,C8CnyIA,oBACE,kB9CsyIF,C8CpyIE,uBAGE,kBAAA,CACA,eAAA,CAHA,Q9CwyIJ,C8ClyIE,sBACE,kBAAA,CAAA,qBAAA,CAAA,iBAAA,CAAA,aAAA,CAEA,Q9CmyIJ,C8ChyIE,sBAUE,wBAAA,CADA,iB5C3GK,C4CmGL,0BAAA,CAAA,2BAAA,CAAA,0BAAA,CAAA,mBAAA,CAGA,kBAAA,CADA,eAAA,CAEA,iBAAA,CAEA,oB9CkyIJ,C8C7xII,yBAZF,sBAaI,wBAAA,CAAA,qBAAA,CAAA,0BAAA,CAAA,kBAAA,CAEA,S9C+xIJ,CACF,C8C5xIE,2BACE,aAAA,CAIA,mBAAA,CAFA,e9C8xIJ,C8CzxIE,wBACE,aAAA,CACA,gBAAA,CACA,eAAA,CACA,qB9C2xIJ,C8CxxIE,wBASE,e5CjLY,C4CgLZ,iB5C5IK,C4CqIL,aAAA,CAGA,WAAA,CAEA,kBAAA,CADA,iBAAA,CAFA,U9C8xIJ,C8CtxII,yBAXF,wBAaI,WAAA,CACA,eAAA,CAFA,U9C2xIJ,CACF,C+Cl9IA,UACE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,c/Cq9IF,C+Cn9IE,yBAJF,UAKI,wBAAA,CAAA,oBAAA,CAAA,gB/Cs9IF,CACF,C+Cp9IE,gBACE,U/Cs9IJ,C+Cj9IA,oBACE,kBAAA,CAMA,wB7CdkB,C6CYlB,U7CjBc,C6Cad,qBAAA,CAAA,iBAAA,CAAA,aAAA,CAEA,2B/Cq9IF,C+C/8IE,yBATF,oBAUI,kBAAA,CAAA,sBAAA,CAAA,kBAAA,CAAA,c/Ck9IF,CACF,C+Ch9IE,yBAbF,oBAcI,kBAAA,CAAA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAEA,iB/Ck9IF,CACF,C+Ch9IE,uBACE,eAAA,CACA,gB/Ck9IJ,C+Ch9II,yBAJF,uBAKI,kB/Cm9IJ,CACF,C+Ch9IE,8CAKE,oBAAA,CAHA,QAAA,CACA,S/Cm9IJ,C+C98IE,uBAKE,mBAAA,CACA,gBAAA,CAHA,wBAAA,CAFA,iB/Cm9IJ,C+C58II,8BACE,e/C88IN,C+C18IE,wBAGE,QAAA,CAFA,iBAAA,CACA,Q/C68IJ,CgD9gJA,QACE,YhDihJF,CgD/gJE,mBACE,ahDihJJ,CgD3gJE,2BACE,oBhD8gJJ,CgDxgJE,yBACE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YhD2gJJ,CiD7hJA,MAQE,wB/CDkB,C+CFlB,U/CHc,C+CId,cAAA,CAHA,cAAA,CAFA,iBjDoiJF,CiD3hJE,yBAVF,MAaI,kBAAA,CAFA,ejD+hJF,CiD3hJE,aAYE,wBAAA,CAPA,QAAA,CAJA,UAAA,CAOA,aAAA,CAFA,QAAA,CAHA,iBAAA,CACA,KAAA,CAMA,SjD2hJJ,CACF,CiDthJE,SAGE,kBAAA,CACA,eAAA,CAHA,kBjD0hJJ,CiDrhJI,yBANF,SAOI,kBjDwhJJ,CACF,CiDrhJE,QAGE,kBAAA,CACA,iBAAA,CAHA,iBjDyhJJ,CiDphJI,cAEE,MAAA,CADA,iBjDuhJN,CiDphJM,yBAJF,cAKI,OjDuhJN,CACF,CiDnhJE,UAGE,iB/CvBK,C+CqBL,oBjDshJJ,CiDjhJE,iBAGE,wBAAA,CAAA,qBAAA,CAFA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CACA,qCAAA,CAAA,6BjDmhJJ,CiDhhJE,YAIE,a/C7DW,C+C2DX,gBAAA,CADA,UjDohJJ,CiD5gJA,YAIE,eAAA,CAHA,iBAAA,CAEA,UjD+gJF,CiD3gJA,wBACE,ejD8gJF,CiD5gJE,yBAHF,wBAII,YjD+gJF,CACF,CiD3gJA,aAGE,wBAAA,CAAA,qBAAA,CAFA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CACA,qCAAA,CAAA,6BAAA,CAEA,SjD6gJF,CiD3gJE,qBACE,ajD6gJJ,CiDpgJA,2BACE,SjD2gJF,CiDzgJE,yBAHF,aAMI,QAAA,CAFA,iBAAA,CACA,OjD6gJF,CACF,CkDnoJA,gCD0HI,iBjDihJJ,CkD3oJA,aAGE,alDwoJF,CkDroJI,yBADF,0BAEI,elDwoJJ,CACF,CkDloJE,yBADF,oBAGI,wBAAA,CAAA,qBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,qCAAA,CAAA,6BlDsoJF,CACF,CkDpoJE,sBACE,QlDsoJJ,CkDnoJE,wBAGE,iBhDYK,CgDdL,alDsoJJ,CkD/nJA,sBACE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YlDkoJF,CkDhoJE,yBAHF,sBAII,kBAAA,CACA,2BAAA,CAAA,4BAAA,CACA,wBAAA,CAAA,qBAAA,CAFA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CACA,6BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,qCAAA,CAAA,6BAAA,CAEA,iBlDkoJF,CACF,CkDhoJE,8BACE,gBlDkoJJ,CkDhoJI,yBAHF,8BAII,gBlDmoJJ,CACF,CkDjoJI,0BAPF,8BAQI,gBlDooJJ,CACF,CkDhoJA,uBACE,alDmoJF,CkDjoJE,yBAHF,uBAII,QlDooJF,CACF,CkDhoJA,sBAOE,qBhDrEc,CgDmEd,kBAAA,CAFA,YAAA,CAFA,iBlDsoJF,CkD9nJE,0BATF,sBAcI,iBAAA,CAHA,uBAAA,CADA,eAAA,CAEA,iBlDkoJF,CACF,CkD9nJE,yBACE,kBlDgoJJ,CkD7nJE,wBAIE,cAAA,CADA,eAAA,CAEA,iBAAA,CAJA,iBlDkoJJ,CkD3nJE,4BAOE,ahDvFW,CgDkFX,MAAA,CAGA,gBAAA,CAJA,iBAAA,CAGA,UlD8nJJ,CkDtnJA,mBACE,elDynJF,CkDvnJE,yBAHF,mBAMI,WAAA,CAEA,QAAA,CAJA,iBAAA,CACA,UlD4nJF,CACF,CkDvnJE,0BAXF,mBAYI,UlD0nJF,CACF,CmDhvJA,QAIE,kBAAA,CADA,YAAA,CACA,oBAAA,CAAA,gBAAA,CAAA,YAAA,CAEA,aAAA,CALA,iBnDsvJF,CmD/uJE,yBARF,QASI,aAAA,CAEA,enDivJF,CACF,CmD/uJE,0BAdF,QAeI,QnDkvJF,CACF,CmDhvJE,gBAYE,kBjDvBgB,CiDiBhB,aAAA,CAHA,MAAA,CAMA,QAAA,CACA,YAAA,CATA,iBAAA,CACA,QAAA,CAMA,UAAA,CAJA,WnDuvJJ,CmD5uJE,qBACE,iBAAA,CACA,WnD8uJJ,CmDzuJA,gBAgBE,wBjDjDkB,CiDgDlB,iBjDjBO,CiDeP,UjDnDc,CiD6Cd,YAAA,CAHA,MAAA,CAMA,6BAAA,CAFA,aAAA,CAGA,mBAAA,CAVA,iBAAA,CAEA,OAAA,CADA,KAAA,CAGA,WnDmvJF,CmDtuJE,yBAlBF,gBAqBI,UAAA,CAEA,8BAAA,CAHA,WAAA,CADA,SnD4uJF,CACF,CmDtuJE,wBACE,anDwuJJ,CmDruJE,mBACE,kBAAA,CACA,enDuuJJ,CmDnuJI,6BACE,enDquJN,CmDhuJE,mCACE,UnDkuJJ,CmD/tJE,yCAEE,qBjDrFY,CiDoFZ,iBnDkuJJ,CmD9tJE,yCAGE,kBjDjFW,CiD+EX,qBAAA,CACA,iBnDiuJJ,CmD7tJE,+CACE,wBnD+tJJ,CmD1tJA,iBAGE,kBAAA,CAFA,iBnD8tJF,CmD1tJE,oBACE,iBnD4tJJ,CmDztJE,oBAIE,oBAAA,CAHA,QAAA,CACA,SnD4tJJ,CmDvtJE,oBACE,QAAA,CACA,anDytJJ,CmDttJE,mBACE,UnDwtJJ,CmDttJI,kDAEE,anDutJN,CmDjtJA,cAKE,wBAAA,CAFA,iBAAA,CAFA,iBnDstJF,CmD/sJI,yBADF,8BAEI,cnDktJJ,CACF,CmDhtJI,kCAGE,aAAA,CAFA,anDmtJN,CmD7sJE,4CACE,kBnD+sJJ,CmD5sJE,uBACE,UnD8sJJ,CmD3sJE,mBACE,kBnD6sJJ,CmDxsJA,gBAcE,wBAAA,CACA,iBAAA,CAdA,oBAAA,CAOA,iBAAA,CACA,eAAA,CALA,WAAA,CAMA,aAAA,CALA,YAAA,CACA,eAAA,CAKA,iBAAA,CACA,qBAAA,CATA,UnDotJF,CmDpsJA,eAWE,cAAA,CAFA,WAAA,CAHA,eAAA,CALA,iBAAA,CAEA,UAAA,CADA,QAAA,CAMA,UAAA,CAJA,UnD2sJF,CmDlsJE,0CAEE,UnDmsJJ,CmDhsJE,qBAGE,UjD9MY,CiD4MZ,UnDmsJJ,CoDj5JA,WAGE,aAAA,CAFA,iBAAA,CAIA,iBpDk5JF,CoDh5JE,kBAaE,qBlDjBY,CkDKZ,UAAA,CAMA,aAAA,CAIA,UAAA,CADA,eAAA,CAPA,iBAAA,CACA,QAAA,CAKA,UAAA,CAJA,UpDs5JJ,CoD34JE,gBAgBE,qBlDrCY,CkDmCZ,qBAAA,CACA,iBAAA,CAPA,UlD5BY,CkDwBZ,oBAAA,CAKA,gBAAA,CACA,eAAA,CACA,aAAA,CALA,gBAAA,CALA,iBAAA,CAWA,wBAAA,CAVA,UpDu5JJ,CoDt4JE,wBACE,kBpDw4JJ,CoDn4JI,yBADF,0BAEI,iBpDs4JJ,CACF,CoDh4JE,2BACE,qBpDm4JJ,CoDh4JE,yBAIE,wBAAA,CADA,iBlD7DY,CkD2DZ,UpDo4JJ,CqDj8JA,SACE,erDo8JF,CqDl8JE,0BAHF,SAII,kBAAA,CAAA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAEA,YAAA,CACA,iBrDo8JF,CACF,CqDl8JE,YAGE,iBAAA,CAFA,erDq8JJ,CqDh8JE,oBAEE,WAAA,CADA,2BrDm8JJ,CqD/7JE,sBACE,kBrDi8JJ,CqD57JA,iBACE,YAAA,CAGA,cAAA,CADA,UrD+7JF,CqD57JE,0BANF,iBAQI,kBAAA,CADA,aAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAEA,kBrD87JF,CACF,CqD57JE,yBACE,arD87JJ,CqD37JE,mBACE,kBrD67JJ,CqD17JE,8BACE,erD47JJ,CqDv7JA,gBACE,kBAAA,CAAA,qBAAA,CAAA,iBAAA,CAAA,arD07JF,CqDx7JE,0BAHF,gBAII,kBAAA,CAAA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAEA,iBrD07JF,CACF,CqDx7JE,0BATF,gBAUI,kBrD27JF,CACF,CqDv7JI,yBAFF,gDAGI,YrD27JJ,CACF,CqDx7JE,gCACE,erD07JJ,CqDr7JA,eACE,gBrDw7JF,CsDzgKA,QAOE,qBpDJc,CoDEd,UpDHc,CoDCd,YAAA,CAFA,iBtD+gKF,CsDvgKE,yBATF,QAUI,atD0gKF,CACF,CsDxgKE,aAGE,kBAAA,CAFA,iBtD2gKJ,CsDrgKI,sBAGE,wBAAA,CAFA,UtDwgKN,CsDngKI,uBACE,atDqgKN,CsDhgKI,yBAGE,wBpDjBY,CoDeZ,UtDmgKN,CsD9/JI,0BACE,atDggKN,CsD3/JI,wBAGE,wBpD3BW,CoDyBX,UtD8/JN,CsDz/JI,yBACE,atD2/JN,CsDr/JA,eAIE,QAAA,CACA,MAAA,CAKA,QAAA,CAFA,eAAA,CAPA,iBAAA,CAEA,OAAA,CADA,KAAA,CAIA,StD0/JF,CsDp/JE,iBACE,atDs/JJ,CsDn/JE,mBACE,aAAA,CAEA,WAAA,CACA,gBAAA,CAEA,mBAAA,CAAA,gBtDm/JJ,CsD9+JA,iBAKE,wBAAA,CAAA,qBAAA,CACA,2BAAA,CAAA,4BAAA,CACA,uBAAA,CAAA,oBAAA,CAFA,0BAAA,CAAA,kBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,6BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,8BAAA,CAAA,sBAAA,CAGA,gBAAA,CATA,iBAAA,CAWA,iBAAA,CAHA,SAAA,CAPA,UtDw/JF,CsD1+JA,eACE,mBAAA,CACA,eAAA,CACA,atD6+JF,CsD3+JE,qBAKE,apDjGW,CoD6FX,aAAA,CAKA,kBAAA,CACA,eAAA,CAJA,kBtD++JJ,CuD3lKA,oBDsHE,QtDi/JF,CuDvmKA,MACE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAKA,oBAAA,CAFA,cAAA,CAIA,wBvD+lKF,CuD7lKE,yBAVF,MAcI,+BAAA,CAHA,aAAA,CACA,SvDimKF,CACF,CuD7lKE,yBAjBF,MAkBI,evDgmKF,CACF,CuD9lKE,SAIE,mCAAA,CAHA,oBAAA,CACA,WvDimKJ,CuD7lKI,yBANF,SAOI,iBAAA,CACA,cvDgmKJ,CACF,CuD7lKE,QACE,arD5BW,CqD8BX,oBAAA,CADA,kBvDgmKJ,CuD7lKI,4BAEE,UvD8lKN,CuD1lKE,iBACE,evD4lKJ,CuD1lKI,yBAHF,iBAII,oBvD6lKJ,CACF,CuD3lKI,mBACE,UvD6lKN,CuDtlKE,yBADF,cAEI,uBAAA,CAAA,oBAAA,CAAA,8BAAA,CAAA,sBvD0lKF,CuDxlKE,iBACE,kBvD0lKJ,CACF,CwD1pKA,QACE,iBxD6pKF,CwD3pKE,iBAEE,WAAA,CACA,aAAA,CAFA,UxD+pKJ,CwDxpKA,cACE,QxD2pKF,CwDzpKE,6BACE,wDxD2pKJ,CwDxpKE,mCACE,SxD0pKJ,CwDxpKI,0BAHF,mCAII,SxD2pKJ,CACF,CwDxpKE,mCACE,UxD0pKJ,CwDxpKI,0BAHF,mCAII,UxD2pKJ,CACF,CwDtpKA,kBACE,axDypKF,CwDvpKE,0BAHF,kBAII,cxD0pKF,CACF,CwDtpKA,eAaE,oEAAA,CACA,yBAAA,CAFA,WAAA,CAJA,WAAA,CAHA,eAAA,CAJA,iBAAA,CASA,kBAAA,CARA,OAAA,CAcA,kCAAA,CAAA,8BAAA,CAAA,0BAAA,CATA,UAAA,CAJA,UxDiqKF,CwDlpKE,0BAlBF,eAsBI,yBAAA,CAFA,WAAA,CADA,UxDupKF,CACF,CwDlpKE,0CAEE,UxDmpKJ,CwDhpKE,wBACE,UxDkpKJ,CwD7oKA,qBACE,UAAA,CAEA,gBAAA,CAEA,iCAAA,CAAA,6BAAA,CAAA,yBxD8oKF,CwD5oKE,0BAPF,qBAQI,UxD+oKF,CACF,CwD3oKA,qBACE,WxD8oKF,CwD5oKE,0BAHF,qBAII,WxD+oKF,CACF,CwD3oKA,oBAWE,wBAAA,CAFA,0BtD1EO,CsD2EP,2BtD3EO,CsDmEP,QAAA,CACA,QAAA,CAGA,QAAA,CACA,gBAAA,CANA,iBAAA,CAYA,kCAAA,CAAA,8BAAA,CAAA,0BAAA,CATA,UxDopKF,CwDzoKE,2BAQE,sBAAA,CADA,WAAA,CAJA,UtDvHY,CsDwHZ,kBAAA,CACA,eAAA,CAKA,UAAA,CATA,cxDipKJ,CwDtoKI,qCACE,SxDwoKN,CyD3wKA,MAQE,qCAAA,CACA,2BAAA,CARA,oBAAA,CAGA,WAAA,CAEA,oBAAA,CAMA,0BAAA,CAAA,qBAAA,CAAA,kBAAA,CATA,UzDmxKF,CyDrwKE,gCAPA,uBzDgxKF,CyDlwKA,UACE,8BzDqwKF,CyDjwKA,UACE,8BzDowKF,CyDhwKA,UACE,8BzDmwKF,CyD/vKA,SACE,8BzDkwKF,C0D1yKA,OACE,axDKa,CwDJb,kBAAA,CACA,oB1D6yKF,C0D3yKE,cACE,e1D6yKJ,C0D1yKE,aAIE,axDPW,CwDKX,iBAAA,CADA,U1D8yKJ,C0DpyKI,0CACE,a1DyyKN,C0DjyKI,wCACE,a1DsyKN,C0D9xKI,oCACE,a1DmyKN,C2D10KA,6BAIE,QAAA,CAFA,U3D80KF,C2D10KE,sEAWE,QAAA,CAAA,+BAAA,CAPA,eAAA,CAFA,gBAAA,CAGA,eAAA,CACA,kB3Di1KJ,C2Dv0KM,8HAEE,e3D20KR,C2Dr0KE,mCACE,kBAAA,CACA,e3Dw0KJ,C2Dn0KA,gBAOE,qBzD1Cc,CyDuCd,eAAA,CADA,cAAA,CAFA,eAAA,CAIA,sB3Ds0KF,C4Dh3KA,KAaE,wBAAA,CADA,iB1D0BO,C0DhCP,U1DHc,C0DFd,oBAAA,CAMA,iBAAA,CACA,eAAA,CACA,aAAA,CANA,kBAAA,CACA,gBAAA,CAMA,wB5Dm3KF,C4D92KE,QACE,aAAA,CAIA,iBAAA,CAFA,e5Dg3KJ,C4D32KE,WAEE,sBAAA,CADA,U5D82KJ,C4Dx2KA,UAGE,kBAAA,CAFA,gB5D42KF,C4Dx2KE,gBAEE,sBAAA,CADA,U5D22KJ,C4Dr2KA,WAGE,mF1DVgB,C0DUhB,6D1DVgB,C0DUhB,sD1DVgB,C0DQhB,U5Dy2KF,C4Dr2KE,iBACE,U5Du2KJ,C4Dl2KA,eACE,wB5Dq2KF,C4Dj2KA,aAGE,wB1DzCe,C0DuCf,U5Dq2KF,C4Dj2KE,mBACE,U5Dm2KJ,C4D91KA,YACE,wB5Di2KF,C4D/1KE,kBACE,a5Di2KJ,C4D51KA,UACE,wB5D+1KF,C6D96KA,OAKE,wBAAA,CAFA,U3DDc,C2DDd,c7Dm7KF,C6D76KE,yBAPF,OAQI,c7Dg7KF,C6D76KA,kBAEI,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,Y7Dg7KJ,CAJF,C6Dt6KA,aAEE,wBAAA,CAAA,qBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,qCAAA,CAAA,6BAAA,CAEA,mB7D66KF,C6D36KE,yBANF,aAOI,kBAAA,CACA,2BAAA,CAAA,4BAAA,CADA,qBAAA,CAAA,iBAAA,CAAA,aAAA,CACA,6BAAA,CAAA,yBAAA,CAAA,qBAAA,CAGA,gBAAA,CADA,kB7D86KF,CACF,C6D36KE,gBAGE,kBAAA,CACA,eAAA,CAHA,Q7D+6KJ,C6Dz6KE,eACE,Q7D26KJ,C6Dx6KE,eACE,U3D3CY,C2D4CZ,kB7D06KJ,C6Dr6KA,gBACE,kBAAA,CAAA,qBAAA,CAAA,iBAAA,CAAA,a7Dw6KF,C6Dt6KE,uBACE,Y7Dw6KJ,C8D/9KA,MAOE,wB5DMa,C4DVb,iBAAA,CACA,eAAA,CACA,eAAA,CAJA,oB9Ds+KF,C8D99KE,aACE,e9Dg+KJ,C8D79KE,YAOE,U5DlBY,C4DYZ,YAAA,CAIA,gBAAA,CADA,eAAA,CADA,U9Di+KJ,C8D39KI,yBATF,YAUI,oB9D89KJ,CACF,C8D19KE,iBAGE,wB5Dbc,C4DWd,U9D69KJ,C8Dv9KE,gBAGE,wB5DlBa,C4DgBb,U9D09KJ,C+D9/KA,KAGE,uBAAA,CAAA,oBAAA,CAFA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CACA,8BAAA,CAAA,sB/DigLF,C+D//KE,yBALF,KAMI,wBAAA,CAAA,qBAAA,CACA,wBAAA,CAAA,qBAAA,CADA,0BAAA,CAAA,kBAAA,CACA,qCAAA,CAAA,6B/DkgLF,CACF,C+DhgLE,QAGE,iBAAA,CAFA,U/DmgLJ,C+D//KI,yBALF,QASI,eAAA,CAFA,iBAAA,CADA,U/DogLJ,CACF,C+D9/KE,OAKE,iBAAA,CACA,gBAAA,CAJA,aAAA,CACA,WAAA,CAIA,iBAAA,CANA,S/DqgLJ,C+D7/KI,yBATF,OAaI,kBAAA,CAFA,QAAA,CADA,U/DkgLJ,CACF,C+D5/KE,SACE,kB/D8/KJ,C+D3/KE,YACE,aAAA,CAEA,e/D4/KJ,CgE3iLA,SAEE,2BAAA,CAAA,4BAAA,CAMA,qB9DNc,C8DDd,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,6BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAGA,YAAA,CADA,UhE+iLF,CgE1iLE,yBAVF,SAWI,wBAAA,CAAA,oBAAA,CAAA,gBhE6iLF,CACF,CgE3iLE,YACE,kBAAA,CAAA,qBAAA,CAAA,iBAAA,CAAA,aAAA,CAIA,cAAA,CAFA,kBhE6iLJ,CgEziLI,cACE,oBhE2iLN,CgEviLE,WACE,QhEyiLJ,CgEpiLA,cACE,wBAAA,CAAA,qBAAA,CACA,6BAAA,CAAA,4BAAA,CADA,0BAAA,CAAA,kBAAA,CACA,0BAAA,CAAA,sBAAA,CAAA,kBAAA,CAEA,cAAA,CACA,YhEsiLF,CgEpiLE,yBAPF,cAQI,kBhEuiLF,CACF,CgEriLE,iBACE,ehEuiLJ,CgEpiLE,8BACE,kBAAA,CAAA,qBAAA,CAAA,iBAAA,CAAA,aAAA,CAEA,ShEqiLJ,CgEniLI,kCACE,ahEqiLN,CgEjiLE,gCACE,kBAAA,CAAA,mBAAA,CAAA,mBAAA,CAAA,yCAAA,CAAA,oCAAA,CAAA,4BAAA,CAAA,mBAAA,CAAA,WAAA,CAAA,qBAAA,CAAA,aAAA,CAEA,gBhEkiLJ,CgEhiLI,yBALF,gCAMI,iBhEmiLJ,CACF,CgEjiLI,yBATF,gCAUI,qBhEoiLJ,CACF,CgEliLI,yBAbF,gCAcI,kBAAA,CAAA,qBAAA,CAAA,iBAAA,CAAA,ahEqiLJ,CACF,CgEniLI,kCACE,ehEqiLN,CgEjiLE,6BACE,QAAA,CACA,ShEmiLJ,CgEjiLI,yBAJF,6BAKI,gBhEoiLJ,CACF,CgEliLI,kCACE,gBhEoiLN,CgE9hLA,eAGE,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAEA,gBAAA,CAJA,iBhEmiLF,CgE7hLE,yBAPF,eAQI,kBhEgiLF,CgE7hLA,+BAGI,SAAA,CADA,iBhEiiLJ,CALF,CgEvhLE,8BAGE,eAAA,CAFA,UhEgiLJ,CgEzhLA,cACE,wBAAA,CAAA,oBAAA,CAAA,gBAAA,CAEA,eAAA,CACA,0BhE2hLF,CgEzhLE,gCACE,chE2hLJ,CgEzhLI,mCAGE,kBAAA,CACA,eAAA,CAHA,QhE6hLN,CgEthLE,8BACE,kBhEwhLJ,CgErhLE,6BACE,UhEuhLJ,CgElhLA,iBAGE,sBAAA,CAFA,UhEshLF,CgElhLE,8BACE,eAAA,CACA,ShEohLJ,CgElhLI,8CAEE,kBAAA,CAEA,iB9D1HG,C8DwHH,qBAAA,CAAA,iBAAA,CAAA,aAAA,CADA,ehEshLN,CgEhhLI,gDACE,kBhEkhLN,CgE9gLE,oBACE,kBhEghLJ,CgEpgLI,8FACE,UhE4gLN,CgEzgLI,uCAGE,U9D1LU,C8DwLV,chE4gLN,CgEngLE,gCACE,gBhEsgLJ,CgEjgLA,cAIE,a9DhMa,C8DiMb,kBAAA,CACA,eAAA,CALA,QAAA,CACA,oBhEugLF,CgEjgLE,iBACE,ahEmgLJ,CgEhgLE,iBACE,ahEkgLJ,CgE7/KA,gBAGE,mBAAA,CAFA,iBAAA,CAIA,iBhE8/KF,CgE5/KE,kBACE,ahE8/KJ,CgE1/KM,yBACE,wDACE,4BAAA,CAAA,wBAAA,CAAA,oBhE4/KR,CgEz/KM,oFACE,4BAAA,CAAA,wBAAA,CAAA,oBhE2/KR,CACF,CgEt/KE,oBACE,oBAAA,CAEA,aAAA,CAEA,0BAAA,CAAA,qBAAA,CAAA,kBhEs/KJ,CgEh/KE,2BACE,WhEm/KJ,CgE9+KA,kBAcE,qB9DhRc,C8D8Qd,wBAAA,CACA,iBAAA,CAVA,WAAA,CAEA,aAAA,CAIA,WAAA,CAHA,eAAA,CAIA,WAAA,CATA,iBAAA,CACA,SAAA,CAcA,0BAAA,CAAA,qBAAA,CAAA,kBAAA,CARA,UhEq/KF,CgE3+KE,yBAlBF,kBAmBI,UhE8+KF,CACF,CgE5+KE,sBAEE,WAAA,CADA,UhE++KJ,CgEz+KA,eAGE,MAAA,CAIA,aAAA,CANA,iBAAA,CAOA,eAAA,CANA,KAAA,CAGA,WhE6+KF,CgEt+KA,mBAEE,WAAA,CACA,MAAA,CAFA,iBhE2+KF,CgEr+KA,cACE,iBAAA,CAEA,OAAA,CADA,KhEy+KF,CgEt+KE,oBAGE,aAAA,CAFA,UhEy+KJ,CgE/9KE,oFACE,ahEo+KJ,CgE/9KA,sBACE,a9DrUa,C8DsUb,iBAAA,CACA,gBhEk+KF,CgE/9KI,0BADF,8BAEI,ehEk+KJ,CACF,CgE79KA,eAEE,wBAAA,CAAA,qBAAA,CACA,wBAAA,CAAA,qBAAA,CADA,0BAAA,CAAA,kBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,qCAAA,CAAA,6BAAA,CAEA,eAAA,CACA,gBhE+9KF,CgE79KE,yBARF,eASI,UhEg+KF,CACF,CgE99KE,yBACE,iBhEg+KJ,CgE79KE,oBACE,kBhE+9KJ,CgE19KA,gBACE,U9D3Wc,C8D4Wd,mBAAA,CACA,ahE69KF,CgE39KE,yBALF,gBAMI,kBhE89KF,CACF,CgE59KE,2CAEE,kBhE89KJ,CgE39KE,oBAKE,a9D1XW,C8DsXX,oBAAA,CAKA,gBAAA,CAHA,gBhE89KJ,CgEx9KE,gCACE,iBhE09KJ,CgEr9KA,gBACE,cAAA,CAEA,kBhEu9KF,CgEr9KE,sBAOE,sBAAA,CADA,WAAA,CAJA,aAAA,CAEA,iBAAA,CAHA,UhE29KJ,CgEl9KE,sBAIE,cAAA,CAFA,eAAA,CAGA,wBAAA,CAAA,qBAAA,CAAA,oBAAA,CAAA,gBAAA,CAJA,UhEu9KJ,CgEj9KI,yBAPF,sBAQI,UhEo9KJ,CACF,CgEl9KI,wDAEE,UhEm9KN,CgE78KA,iBACE,gBAAA,CAEA,oBhE+8KF,CgE38KI,0DACE,ahE68KN,CgEz8KE,uBAGE,aAAA,CAFA,UhE48KJ,CgEr8KA,gBACE,ahEw8KF,CgEt8KE,qBACE,YhEw8KJ,CiE54LA,gBACE,gBAAA,CACA,gBjE+4LF,CiE54LI,0BADF,2BAEI,kBjE+4LJ,CACF,CiE34LI,yBADF,yBAEI,kBjE84LJ,CACF,CiE34LE,gCACE,4BAAA,CAAA,uBAAA,CAAA,ejE64LJ,CiEz4LI,oCACE,QjE24LN,CiEv4LM,yBADF,yCAEI,YjE04LN,CACF,CiEt4LE,uBACE,kBjEw4LJ,CiEn4LA,uBACE,QjEs4LF,CiEp4LE,yBAUE,qGAAA,CAAA,uEAAA,CAAA,6DAAA,CAPA,aAAA,CAEA,gBAAA,CACA,mBAAA,CALA,iBAAA,CAOA,iBjEo4LJ,CkEr7LA,gBAGE,wBAAA,CAAA,qBAAA,CAFA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CACA,qCAAA,CAAA,6BAAA,CAQA,aAAA,CAFA,oBAAA,CAHA,gBAAA,CADA,eAAA,CAEA,SlEy7LF,CkEn7LE,mBACE,QAAA,CACA,WlEq7LJ,CkEl7LE,kBACE,oBlEo7LJ,CkEj7LE,sBAIE,aAAA,CAFA,gBAAA,CADA,UlEq7LJ,CmE58LA,eAKE,eAAA,CADA,eAAA,CAHA,iBAAA,CAEA,UnEg9LF,CmE58LE,0BAPF,eAUI,SAAA,CAGA,YAAA,CALA,iBAAA,CACA,QAAA,CAEA,UnEg9LF,CACF,CmE58LE,mBACE,aAAA,CAEA,0BAAA,CAAA,qBAAA,CAAA,kBnE68LJ,CmEx8LA,qBAEE,wBAAA,CAAA,qBAAA,CAEA,uBAAA,CAAA,oBAAA,CAFA,0BAAA,CAAA,kBAAA,CAOA,qBjE/Bc,CiEuBd,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CACA,8BAAA,CAAA,sBAAA,CAGA,QAAA,CADA,UnE48LF,CmEv8LE,0BAXF,qBAYI,YnE08LF,CACF,CmEp8LQ,yBADF,kEAEI,6BAAA,CAAA,yBAAA,CAAA,qBnEu8LR,CACF,CmEh8LA,+DAME,QAAA,CAHA,iBAAA,CACA,UnEo8LF,CmE97LA,qBAEE,SAAA,CADA,QAAA,CAGA,WnEg8LF,CmE57LA,oBAEE,UAAA,CADA,QnEg8LF,CmE77LE,0BACE,UnE+7LJ,CmE57LE,sCACE,anE87LJ,CmEz7LA,sBAEE,UAAA,CADA,QAAA,CAGA,UnE27LF,CmEv7LA,wBAGE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAEA,eAAA,CALA,iBnE67LF,CmEt7LE,yBARF,wBASI,wBAAA,CAAA,oBAAA,CAAA,gBnEy7LF,CACF,CmEv7LE,0BAEE,wBAAA,CAAA,qBAAA,CACA,2BAAA,CAAA,4BAAA,CACA,uBAAA,CAAA,oBAAA,CAFA,0BAAA,CAAA,kBAAA,CAQA,qBjEhHY,CiEuGZ,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,6BAAA,CAAA,yBAAA,CAAA,qBAAA,CAIA,YAAA,CAHA,8BAAA,CAAA,sBAAA,CAIA,UAAA,CAFA,2BnE27LJ,CmEr7LI,yBAZF,0BAaI,qBnEw7LJ,CmEn7LI,wEAEI,4BAAA,CAAA,wBAAA,CAAA,oBnEs7LR,CAJF,CmE16LA,qBAGE,iBAAA,CACA,enEi7LF,CoEzjMA,qCDqIE,sBAAA,CAAA,kBAAA,CAAA,cnE07LF,CoE/jMA,gBACE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAGA,epE2jMF,CoEzjME,yBANF,gBAOI,wBAAA,CAAA,oBAAA,CAAA,gBpE4jMF,CACF,CoE1jME,qBAEE,kBAAA,CADA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAEA,QpE2jMJ,CoEzjMI,yBANF,qBAOI,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YpE4jMJ,CACF,CoEvjMA,uBACE,epE0jMF,CoExjME,6CAEE,wBAAA,CAAA,qBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,qCAAA,CAAA,6BAAA,CAEA,QAAA,CACA,SpEyjMJ,CoEtjME,4CACE,SpEwjMJ,CoEnjMA,sBAQE,wBAAA,CAJA,UlE1Cc,CkE2Cd,kBAAA,CACA,eAAA,CAJA,sBAAA,CADA,UpE2jMF,CoEljME,yBAVF,sBAWI,iBpEqjMF,CACF,CoEnjME,4BAIE,kBAAA,CACA,eAAA,CAJA,oBAAA,CACA,gBpEujMJ,CoEjjME,wBACE,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAEA,cpEkjMJ,CoE/iME,0BACE,epEijMJ,CoE9iME,4BAIE,alEhEW,CkE8DX,gBAAA,CADA,UpEkjMJ,CoE5iME,iCACE,apE8iMJ,CoE3iME,gCACE,iBpE6iMJ,CqEloMA,gBAGE,wBnEIkB,CmENlB,crEsoMF,CqEloME,yBALF,gBAMI,crEqoMF,CACF,CqEnoME,yBATF,gBAUI,gBrEsoMF,CACF,CqEpoME,0BAbF,gBAcI,gBrEuoMF,CACF,CqEroME,mBAEE,wBAAA,CAAA,qBAAA,CAEA,uBAAA,CAAA,oBAAA,CAFA,0BAAA,CAAA,kBAAA,CASA,aAAA,CAVA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CASA,iBAAA,CARA,8BAAA,CAAA,sBAAA,CAKA,oBAAA,CAHA,QAAA,CACA,SrEyoMJ,CqEloMI,sBAGE,QAAA,CACA,kBAAA,CAHA,iBrEsoMN,CqEjoMM,yBANF,sBAOI,crEooMN,CACF,CqEjoMI,yBAEI,gCAWE,8CAAA,CAVA,UAAA,CAOA,UAAA,CAHA,MAAA,CAIA,eAAA,CANA,iBAAA,CACA,OAAA,CAGA,SrEmoMR,CACF,CqE3nMI,qBACE,UnE3DU,CmE4DV,oBrE6nMN,CqE3nMM,sDAEE,arE4nMR,CqEznMM,6CACE,iBrE2nMR,CqEvnMI,8BACE,UnEzEU,CmE0EV,erEynMN,CqEvnMM,gCACE,UrEynMR,CqEtnMM,sDAGE,iBnEnFQ,CmEiFR,arEynMR,CqEhnMA,wBAEE,wBAAA,CAAA,qBAAA,CACA,uBAAA,CAAA,oBAAA,CADA,0BAAA,CAAA,kBAAA,CAYA,wBAAA,CACA,iBAAA,CAdA,0BAAA,CAAA,2BAAA,CAAA,0BAAA,CAAA,mBAAA,CASA,cAAA,CACA,eAAA,CALA,WAAA,CAHA,8BAAA,CAAA,sBAAA,CASA,aAAA,CALA,gBAAA,CACA,gBAAA,CAHA,UrE0nMF,CqE9mME,yBAjBF,wBAmBI,WAAA,CACA,iBAAA,CAFA,UrEmnMF,CACF,CsEluMA,aAEE,wBAAA,CAAA,qBAAA,CAEA,wBAAA,CAAA,qBAAA,CAFA,0BAAA,CAAA,kBAAA,CAOA,epEPc,CoEDd,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CACA,qCAAA,CAAA,6BAAA,CAEA,eAAA,CACA,YtEquMF,CsEjuME,yBAXF,aAYI,wBAAA,CAAA,oBAAA,CAAA,gBAAA,CAEA,eAAA,CACA,iBtEmuMF,CACF,CsEjuME,eACE,QtEmuMJ,CsEhuME,mBACE,oBAAA,CAEA,kBtEiuMJ,CsE/tMI,yBALF,mBAMI,etEkuMJ,CACF,CsE/tME,kBACE,etEiuMJ,CsE/tMI,yBAHF,kBAII,YAAA,CACA,2BAAA,CAEA,kBtEiuMJ,CACF,CsE5tMA,uBACE,UtE+tMF,CsE7tME,yBAHF,uBAII,kBAAA,CAAA,oBAAA,CAAA,gBAAA,CAAA,YAAA,CAEA,cAAA,CAEA,iBtE8tMF,CACF,CsE5tME,kDACE,aAAA,CAEA,ctE6tMJ,CsE3tMI,0BALF,kDAMI,oBAAA,CAGA,iBAAA,CADA,etE8tMJ,CACF,CsE1tME,sCACE,etE4tMJ,CsEvtMA,mBAME,aAAA,CALA,oBAAA,CAMA,iBAAA,CACA,eAAA,CAJA,eAAA,CADA,etE6tMF,CuE3yMA,WAKE,gBAAA,CAFA,oBAAA,CADA,eAAA,CADA,UvEizMF,CuE3yME,yBAPF,WAQI,iBvE8yMF,CACF,CuE5yME,aAEE,wBAAA,CAAA,qBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,qCAAA,CAAA,6BAAA,CAIA,aAAA,CAFA,evE8yMJ,CuEzyME,kBACE,kBvE2yMJ,CuEryME,yBADF,mBAEI,iBvEyyMF,CACF,CuEryMA,iBACE,aAAA,CACA,iBvEwyMF,CwE10ME,yBADF,eAEI,kBxE80MF,CACF,CwE50ME,kBAGE,iBAAA,CAFA,kBxE+0MJ,CwEz0MI,yBADF,iBAEI,exE40MJ,CACF,CwEx0MI,yBADF,yBAEI,exE20MJ,CACF,CwEx0ME,0BAGE,kBAAA,CADA,eAAA,CADA,exE40MJ,CyEp2MA,MAKE,qBvEHc,CuECd,UAAA,CAFA,YzEy2MF,CyEn2ME,yBAPF,MAQI,YzEs2MF,CACF,CyEp2ME,iBAIE,gBAAA,CADA,iBAAA,CADA,ezEw2MJ,CyEp2MI,uCACE,ezEu2MN,CyEn2ME,QACE,ezEq2MJ,CyEl2ME,wCAKE,iBAAA,CADA,eAAA,CADA,UzEs2MJ,CyEj2ME,SACE,ezEm2MJ,CyE71ME,6BAEE,czEg2MJ,CyE11MI,yBAHF,0DAMI,iBAAA,CADA,cAAA,CADA,UzEi2MJ,CACF,CyE11MA,aAGE,sBAAA,CAFA,SzE81MF,CyEx1MA,gBACE,avE9Ce,CuE+Cf,ezE21MF,CyE/0MA,iEALI,czE+1MJ,CyE11MA,YAGE,aAAA,CAFA,gBzEy1MF,CyEn1MA,aACE,ezEs1MF,CyEl1MA,cACE,ezEq1MF,CyEj1MA,cACE,oBAAA,CACA,WAAA,CAKA,kBAAA,CAHA,gBAAA,CACA,ezEo1MF,CyEh1ME,wCAEE,azEi1MJ,CyE50MA,eACE,yBzE+0MF,C0Eh8MA,eAWE,cAAA,CARA,oBAAA,CAMA,mBAAA,CAHA,iBAAA,CADA,eAAA,CAEA,iBAAA,CANA,iB1Ey8MF,C0E77ME,qBAKE,SAAA,CAJA,iBAAA,CAEA,U1E+7MJ,C0E17ME,qDACE,yD1E47MJ,C0Ez7ME,mDACE,e1E27MJ,C0Ex7ME,sDACE,oB1E07MJ,C0Ev7ME,oDACE,a1Ey7MJ,C0Er7ME,2BAGE,cAAA,CACA,mBAAA,CAHA,oB1Ey7MJ,C0Ep7MI,kEAEE,a1Eq7MN,C0E/6MA,wBAWE,4BAAA,CADA,qBAAA,CALA,oBAAA,CAGA,WAAA,CALA,QAAA,CAFA,iBAAA,CACA,OAAA,CAKA,U1Em7MF,C2E9+MA,eAGE,oBAAA,CAGA,eAAA,CALA,iBAAA,CAIA,U3Eg/MF,C2E7+ME,qBAIE,kBAAA,CAFA,kBAAA,CADA,U3Ei/MJ,C2E5+MI,gDACE,UzEZU,CyEcV,S3E6+MN,C2Eh/MI,uCACE,UzEZU,CyEcV,S3E6+MN,C2Eh/MI,2CAGE,S3E6+MN,C2Eh/MI,4CAGE,S3E6+MN,C2Eh/MI,kCACE,UzEZU,CyEcV,S3E6+MN,C2E1+MI,2CACE,U3E4+MN,C2Ez+MI,4CACE,U3E2+MN,C2Ev+ME,wDAiBE,qBzE3CY,CyEyCZ,WAAA,CACA,iBzEPK,CyECL,UzErCY,CyEsCZ,kBAAA,CACA,eAAA,CAJA,WAAA,CADA,eAAA,CALA,iBAAA,CAEA,SAAA,CASA,wBAAA,CAVA,OAAA,CAGA,U3Ei/MJ,C2Ep+MI,wIAEE,wB3Eu+MN,C4EzhNA,YAUE,cAAA,CAPA,oBAAA,CAKA,mBAAA,CAHA,eAAA,CACA,iBAAA,CALA,iB5EiiNF,C4EthNE,uBACE,oB5EwhNJ,C4ErhNE,kBAGE,SAAA,CAFA,iB5EwhNJ,C4EnhNE,sDACE,wB5EqhNJ,C4ElhNE,oDACE,iB5EohNJ,C4EjhNE,6CACE,e5EmhNJ,C4EjhNI,mDACE,e5EmhNN,C4E/gNE,gDACE,oB5EihNJ,C4E9gNE,8CACE,a5EghNJ,C4E7gNE,kBAKE,U1EhDY,C0E4CZ,aAAA,CAKA,gBAAA,CAHA,e5EghNJ,C4ExgNA,sBAEE,wBAAA,CAAA,qBAAA,CAAA,0BAAA,CAAA,kBAAA,CAMA,qB1E/Dc,C0EwDd,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,sBAAA,CAAA,kBAAA,CAAA,cAAA,CAEA,cAAA,CACA,2B5E2gNF,C4EvgNE,yBAVF,sBAWI,wBAAA,CAAA,oBAAA,CAAA,gBAAA,CAEA,2B5EygNF,CACF,C4EvgNE,yCAIE,eAAA,CAFA,cAAA,CADA,U5E2gNJ,C4EtgNI,yBANF,yCAOI,c5EygNJ,CACF,C4EtgNE,2CAEE,SAAA,CADA,Q5EygNJ,C4EtgNI,yBAJF,2CAMI,SAAA,CAEA,gBAAA,CAHA,O5E2gNJ,CACF,C4EngNA,qBAYE,4BAAA,CAFA,qBAAA,CACA,iBAAA,CANA,oBAAA,CAGA,WAAA,CALA,QAAA,CAFA,iBAAA,CACA,OAAA,CAKA,U5EwgNF,C4EjgNE,4BAUE,4BAAA,CADA,iBAAA,CARA,UAAA,CAKA,UAAA,CACA,kBAAA,CAJA,iBAAA,CAEA,S5EqgNJ,C4E3/MA,kBACE,kBAAA,CAAA,qBAAA,CAAA,iBAAA,CAAA,a5E8/MF,C4E5/ME,sBACE,a5E8/MJ,C4E5/MI,yBAHF,sBAII,a5E+/MJ,CACF,C4E1/MA,mBACE,mBAAA,CACA,kB5E6/MF,C4E3/ME,0BAWE,4BAAA,CALA,QAAA,CALA,UAAA,CAQA,aAAA,CAFA,MAAA,CAMA,mBAAA,CAVA,iBAAA,CAEA,OAAA,CADA,K5EkgNJ,C4Ep/MA,oBAOE,e1EvKc,C0EsKd,qBAAA,CALA,YAAA,CAGA,kBAAA,CADA,e5Ey/MF,C4En/ME,2BAME,WAAA,CALA,aAAA,CAGA,YAAA,CADA,U5Es/MJ,C4Eh/ME,+BACE,a5Ek/MJ,C6EvqNA,YACE,e7E0qNF,C6ExqNE,cAEE,wBAAA,CAAA,qBAAA,CACA,wBAAA,CAAA,qBAAA,CADA,0BAAA,CAAA,kBAAA,CADA,mBAAA,CAAA,oBAAA,CAAA,mBAAA,CAAA,YAAA,CAEA,qCAAA,CAAA,6BAAA,CAEA,e7EyqNJ,C6EtqNE,kBAKE,kBAAA,CAJA,cAAA,CAEA,gBAAA,CADA,iBAAA,CAIA,iB7EuqNJ,C8ExrNA,aACE,iB9E2rNF,C8EzrNE,mBAEE,kBAAA,CADA,U9E4rNJ,C8EzrNI,8CACE,U5ELU,C4EMV,kBAAA,CAEA,S9E0rNN,C8E9rNI,qCACE,U5ELU,C4EMV,kBAAA,CAEA,S9E0rNN,C8E9rNI,yCAIE,S9E0rNN,C8E9rNI,0CAIE,S9E0rNN,C8E9rNI,gCACE,U5ELU,C4EMV,kBAAA,CAEA,S9E0rNN,C8EvrNI,yCACE,U5EZU,C4EaV,kB9EyrNN,C8EtrNI,0CACE,U5EjBU,C4EkBV,kB9EwrNN,C8EprNE,oBAQE,sBAAA,CADA,WAAA,CAHA,WAAA,CACA,kBAAA,CAJA,iBAAA,CAEA,U9EyrNJ,C8EjrNE,mBACE,U9EmrNJ,C+ExtNA,QACE,iB/E2tNF,C+ExtNA,MACE,e/E2tNF,C+ExtNA,OACE,gB/E2tNF,C+EvtNA,QACE,kB/E0tNF,C+EttNA,OACE,U/EytNF,C+EttNA,QACE,W/EytNF,C+EttNA,KACE,U/EytNF,C+ErtNA,qBAEE,WAAA,CAEA,a/EutNF,C+EptNA,UACE,U/EutNF,C+EptNA,KACE,M/EutNF,CgFhwNA,aAEE,EAGE,yBAAA,CAGA,qBAAA,CASA,wBAAA,CAVA,iCAAA,CAAA,yBAAA,CAGA,qBAAA,CAQA,sBAAA,CANA,wBAAA,CACA,wBAAA,CAEA,wBAAA,CAJA,yBAAA,CAGA,2BAAA,CAKA,uBAAA,CAbA,0BAAA,CAUA,qBAAA,CAPA,oBhF4wNF,CgFhwNA,OAjBE,oBAAA,CADA,gChF8xNF,CgF5wNA,KASE,yBAAA,CACA,wBAAA,CAJA,6BAAA,CAFA,yBAAA,CAFA,kBAAA,CACA,mBAAA,CAFA,oBAAA,CAIA,4BhFuwNF,CgFhwNA,YAAiB,oBAAA,CAAwB,uBhFowNzC,CgFnwNA,GAAK,wBhFswNL,CgFrwNA,GAAK,wBhFwwNL,CgFvwNA,GAAK,wBhF0wNL,CgFzwNA,GAAK,wBhF4wNL,CgF3wNA,IAAM,kChF8wNN,CgF7wNA,MAAwC,iCAAA,CAA/B,2BhFixNT,CgFhxNA,GAAK,yBAAA,CAA6B,2BhFoxNlC,CgFnxNA,MAAQ,qBhFsxNR,CgFrxNA,4CAAqF,oBAAA,CAAlC,8BhFyxNnD,CgFxxNA,MAAQ,oBAAA,CAAwB,yBhF4xNhC,CgF3xNA,GAAmD,yBhF+xNnD,CgF9xNA,MADK,sChFkyNL,CgFhyNA,MAAS,gChFmyNT,CgFlyNA,GAAK,iChFqyNL,CgFlyNA,iEAA6E,sBhFqyN7E,CgFlyNA,KAAO,uBAAA,CAA2B,kBhFsyNlC,CACF", "file": "styles.css", "sourcesContent": ["/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */\n\n/* Document\n   ========================================================================== */\n\n/**\n * 1. Correct the line height in all browsers.\n * 2. Prevent adjustments of font size after orientation changes in iOS.\n */\n\nhtml {\n  line-height: 1.15; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n}\n\n/* Sections\n   ========================================================================== */\n\n/**\n * Remove the margin in all browsers.\n */\n\nbody {\n  margin: 0;\n}\n\n/**\n * Render the `main` element consistently in IE.\n */\n\nmain {\n  display: block;\n}\n\n/**\n * Correct the font size and margin on `h1` elements within `section` and\n * `article` contexts in Chrome, Firefox, and Safari.\n */\n\nh1 {\n  font-size: 2em;\n  margin: 0.67em 0;\n}\n\n/* Grouping content\n   ========================================================================== */\n\n/**\n * 1. Add the correct box sizing in Firefox.\n * 2. Show the overflow in Edge and IE.\n */\n\nhr {\n  box-sizing: content-box; /* 1 */\n  height: 0; /* 1 */\n  overflow: visible; /* 2 */\n}\n\n/**\n * 1. Correct the inheritance and scaling of font size in all browsers.\n * 2. Correct the odd `em` font sizing in all browsers.\n */\n\npre {\n  font-family: monospace, monospace; /* 1 */\n  font-size: 1em; /* 2 */\n}\n\n/* Text-level semantics\n   ========================================================================== */\n\n/**\n * Remove the gray background on active links in IE 10.\n */\n\na {\n  background-color: transparent;\n}\n\n/**\n * 1. Remove the bottom border in Chrome 57-\n * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.\n */\n\nabbr[title] {\n  border-bottom: none; /* 1 */\n  text-decoration: underline; /* 2 */\n  text-decoration: underline dotted; /* 2 */\n}\n\n/**\n * Add the correct font weight in Chrome, Edge, and Safari.\n */\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/**\n * 1. Correct the inheritance and scaling of font size in all browsers.\n * 2. Correct the odd `em` font sizing in all browsers.\n */\n\ncode,\nkbd,\nsamp {\n  font-family: monospace, monospace; /* 1 */\n  font-size: 1em; /* 2 */\n}\n\n/**\n * Add the correct font size in all browsers.\n */\n\nsmall {\n  font-size: 80%;\n}\n\n/**\n * Prevent `sub` and `sup` elements from affecting the line height in\n * all browsers.\n */\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/* Embedded content\n   ========================================================================== */\n\n/**\n * Remove the border on images inside links in IE 10.\n */\n\nimg {\n  border-style: none;\n}\n\n/* Forms\n   ========================================================================== */\n\n/**\n * 1. Change the font styles in all browsers.\n * 2. Remove the margin in Firefox and Safari.\n */\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  line-height: 1.15; /* 1 */\n  margin: 0; /* 2 */\n}\n\n/**\n * Show the overflow in IE.\n * 1. Show the overflow in Edge.\n */\n\nbutton,\ninput { /* 1 */\n  overflow: visible;\n}\n\n/**\n * Remove the inheritance of text transform in Edge, Firefox, and IE.\n * 1. Remove the inheritance of text transform in Firefox.\n */\n\nbutton,\nselect { /* 1 */\n  text-transform: none;\n}\n\n/**\n * Correct the inability to style clickable types in iOS and Safari.\n */\n\nbutton,\n[type=\"button\"],\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button;\n}\n\n/**\n * Remove the inner border and padding in Firefox.\n */\n\nbutton::-moz-focus-inner,\n[type=\"button\"]::-moz-focus-inner,\n[type=\"reset\"]::-moz-focus-inner,\n[type=\"submit\"]::-moz-focus-inner {\n  border-style: none;\n  padding: 0;\n}\n\n/**\n * Restore the focus styles unset by the previous rule.\n */\n\nbutton:-moz-focusring,\n[type=\"button\"]:-moz-focusring,\n[type=\"reset\"]:-moz-focusring,\n[type=\"submit\"]:-moz-focusring {\n  outline: 1px dotted ButtonText;\n}\n\n/**\n * Correct the padding in Firefox.\n */\n\nfieldset {\n  padding: 0.35em 0.75em 0.625em;\n}\n\n/**\n * 1. Correct the text wrapping in Edge and IE.\n * 2. Correct the color inheritance from `fieldset` elements in IE.\n * 3. Remove the padding so developers are not caught out when they zero out\n *    `fieldset` elements in all browsers.\n */\n\nlegend {\n  box-sizing: border-box; /* 1 */\n  color: inherit; /* 2 */\n  display: table; /* 1 */\n  max-width: 100%; /* 1 */\n  padding: 0; /* 3 */\n  white-space: normal; /* 1 */\n}\n\n/**\n * Add the correct vertical alignment in Chrome, Firefox, and Opera.\n */\n\nprogress {\n  vertical-align: baseline;\n}\n\n/**\n * Remove the default vertical scrollbar in IE 10+.\n */\n\ntextarea {\n  overflow: auto;\n}\n\n/**\n * 1. Add the correct box sizing in IE 10.\n * 2. Remove the padding in IE 10.\n */\n\n[type=\"checkbox\"],\n[type=\"radio\"] {\n  box-sizing: border-box; /* 1 */\n  padding: 0; /* 2 */\n}\n\n/**\n * Correct the cursor style of increment and decrement buttons in Chrome.\n */\n\n[type=\"number\"]::-webkit-inner-spin-button,\n[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/**\n * 1. Correct the odd appearance in Chrome and Safari.\n * 2. Correct the outline style in Safari.\n */\n\n[type=\"search\"] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/**\n * Remove the inner padding in Chrome and Safari on macOS.\n */\n\n[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/**\n * 1. Correct the inability to style clickable types in iOS and Safari.\n * 2. Change font properties to `inherit` in Safari.\n */\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/* Interactive\n   ========================================================================== */\n\n/*\n * Add the correct display in Edge, IE 10+, and Firefox.\n */\n\ndetails {\n  display: block;\n}\n\n/*\n * Add the correct display in all browsers.\n */\n\nsummary {\n  display: list-item;\n}\n\n/* Misc\n   ========================================================================== */\n\n/**\n * Add the correct display in IE 10+.\n */\n\ntemplate {\n  display: none;\n}\n\n/**\n * Add the correct display in IE 10.\n */\n\n[hidden] {\n  display: none;\n}\n", "/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */\n/* Document\n   ========================================================================== */\n/**\n * 1. Correct the line height in all browsers.\n * 2. Prevent adjustments of font size after orientation changes in iOS.\n */\nhtml {\n  line-height: 1.15; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n}\n\n/* Sections\n   ========================================================================== */\n/**\n * Remove the margin in all browsers.\n */\nbody {\n  margin: 0;\n}\n\n/**\n * Render the `main` element consistently in IE.\n */\nmain {\n  display: block;\n}\n\n/**\n * Correct the font size and margin on `h1` elements within `section` and\n * `article` contexts in Chrome, Firefox, and Safari.\n */\nh1 {\n  font-size: 2em;\n  margin: 0.67em 0;\n}\n\n/* Grouping content\n   ========================================================================== */\n/**\n * 1. Add the correct box sizing in Firefox.\n * 2. Show the overflow in Edge and IE.\n */\nhr {\n  box-sizing: content-box; /* 1 */\n  height: 0; /* 1 */\n  overflow: visible; /* 2 */\n}\n\n/**\n * 1. Correct the inheritance and scaling of font size in all browsers.\n * 2. Correct the odd `em` font sizing in all browsers.\n */\npre {\n  font-family: monospace, monospace; /* 1 */\n  font-size: 1em; /* 2 */\n}\n\n/* Text-level semantics\n   ========================================================================== */\n/**\n * Remove the gray background on active links in IE 10.\n */\na {\n  background-color: transparent;\n}\n\n/**\n * 1. Remove the bottom border in Chrome 57-\n * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.\n */\nabbr[title] {\n  border-bottom: none; /* 1 */\n  text-decoration: underline; /* 2 */\n  text-decoration: underline dotted; /* 2 */\n}\n\n/**\n * Add the correct font weight in Chrome, Edge, and Safari.\n */\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/**\n * 1. Correct the inheritance and scaling of font size in all browsers.\n * 2. Correct the odd `em` font sizing in all browsers.\n */\ncode,\nkbd,\nsamp {\n  font-family: monospace, monospace; /* 1 */\n  font-size: 1em; /* 2 */\n}\n\n/**\n * Add the correct font size in all browsers.\n */\nsmall {\n  font-size: 80%;\n}\n\n/**\n * Prevent `sub` and `sup` elements from affecting the line height in\n * all browsers.\n */\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/* Embedded content\n   ========================================================================== */\n/**\n * Remove the border on images inside links in IE 10.\n */\nimg {\n  border-style: none;\n}\n\n/* Forms\n   ========================================================================== */\n/**\n * 1. Change the font styles in all browsers.\n * 2. Remove the margin in Firefox and Safari.\n */\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  line-height: 1.15; /* 1 */\n  margin: 0; /* 2 */\n}\n\n/**\n * Show the overflow in IE.\n * 1. Show the overflow in Edge.\n */\nbutton,\ninput { /* 1 */\n  overflow: visible;\n}\n\n/**\n * Remove the inheritance of text transform in Edge, Firefox, and IE.\n * 1. Remove the inheritance of text transform in Firefox.\n */\nbutton,\nselect { /* 1 */\n  text-transform: none;\n}\n\n/**\n * Correct the inability to style clickable types in iOS and Safari.\n */\nbutton,\n[type=button],\n[type=reset],\n[type=submit] {\n  -webkit-appearance: button;\n}\n\n/**\n * Remove the inner border and padding in Firefox.\n */\nbutton::-moz-focus-inner,\n[type=button]::-moz-focus-inner,\n[type=reset]::-moz-focus-inner,\n[type=submit]::-moz-focus-inner {\n  border-style: none;\n  padding: 0;\n}\n\n/**\n * Restore the focus styles unset by the previous rule.\n */\nbutton:-moz-focusring,\n[type=button]:-moz-focusring,\n[type=reset]:-moz-focusring,\n[type=submit]:-moz-focusring {\n  outline: 1px dotted ButtonText;\n}\n\n/**\n * Correct the padding in Firefox.\n */\nfieldset {\n  padding: 0.35em 0.75em 0.625em;\n}\n\n/**\n * 1. Correct the text wrapping in Edge and IE.\n * 2. Correct the color inheritance from `fieldset` elements in IE.\n * 3. Remove the padding so developers are not caught out when they zero out\n *    `fieldset` elements in all browsers.\n */\nlegend {\n  box-sizing: border-box; /* 1 */\n  color: inherit; /* 2 */\n  display: table; /* 1 */\n  max-width: 100%; /* 1 */\n  padding: 0; /* 3 */\n  white-space: normal; /* 1 */\n}\n\n/**\n * Add the correct vertical alignment in Chrome, Firefox, and Opera.\n */\nprogress {\n  vertical-align: baseline;\n}\n\n/**\n * Remove the default vertical scrollbar in IE 10+.\n */\ntextarea {\n  overflow: auto;\n}\n\n/**\n * 1. Add the correct box sizing in IE 10.\n * 2. Remove the padding in IE 10.\n */\n[type=checkbox],\n[type=radio] {\n  box-sizing: border-box; /* 1 */\n  padding: 0; /* 2 */\n}\n\n/**\n * Correct the cursor style of increment and decrement buttons in Chrome.\n */\n[type=number]::-webkit-inner-spin-button,\n[type=number]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/**\n * 1. Correct the odd appearance in Chrome and Safari.\n * 2. Correct the outline style in Safari.\n */\n[type=search] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/**\n * Remove the inner padding in Chrome and Safari on macOS.\n */\n[type=search]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/**\n * 1. Correct the inability to style clickable types in iOS and Safari.\n * 2. Change font properties to `inherit` in Safari.\n */\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/* Interactive\n   ========================================================================== */\n/*\n * Add the correct display in Edge, IE 10+, and Firefox.\n */\ndetails {\n  display: block;\n}\n\n/*\n * Add the correct display in all browsers.\n */\nsummary {\n  display: list-item;\n}\n\n/* Misc\n   ========================================================================== */\n/**\n * Add the correct display in IE 10+.\n */\ntemplate {\n  display: none;\n}\n\n/**\n * Add the correct display in IE 10.\n */\n[hidden] {\n  display: none;\n}\n\nbody.hidden-scroll {\n  overflow: hidden;\n}\n\n.sl-overlay {\n  position: fixed;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  background: #000;\n  opacity: 0.9;\n  display: none;\n  z-index: 1035;\n}\n\n.sl-wrapper {\n  z-index: 1040;\n}\n.sl-wrapper button {\n  border: 0 none;\n  background: transparent;\n  font-size: 28px;\n  padding: 0;\n  cursor: pointer;\n}\n.sl-wrapper button:hover {\n  opacity: 0.7;\n}\n.sl-wrapper .sl-close {\n  display: none;\n  position: fixed;\n  right: 30px;\n  top: 30px;\n  z-index: 10060;\n  margin-top: -14px;\n  margin-right: -14px;\n  height: 44px;\n  width: 44px;\n  line-height: 44px;\n  font-family: Arial, Baskerville, monospace;\n  color: #fff;\n  font-size: 3rem;\n}\n.sl-wrapper .sl-close:focus {\n  outline: none;\n}\n.sl-wrapper .sl-counter {\n  display: none;\n  position: fixed;\n  top: 30px;\n  left: 30px;\n  z-index: 1060;\n  color: #fff;\n  font-size: 1rem;\n}\n.sl-wrapper .sl-navigation {\n  width: 100%;\n  display: none;\n}\n.sl-wrapper .sl-navigation button {\n  position: fixed;\n  top: 50%;\n  margin-top: -22px;\n  height: 44px;\n  width: 22px;\n  line-height: 44px;\n  text-align: center;\n  display: block;\n  z-index: 10060;\n  font-family: Arial, Baskerville, monospace;\n  color: #fff;\n}\n.sl-wrapper .sl-navigation button.sl-next {\n  right: 5px;\n  font-size: 2rem;\n}\n.sl-wrapper .sl-navigation button.sl-prev {\n  left: 5px;\n  font-size: 2rem;\n}\n.sl-wrapper .sl-navigation button:focus {\n  outline: none;\n}\n@media (min-width: 35.5em) {\n  .sl-wrapper .sl-navigation button {\n    width: 44px;\n  }\n  .sl-wrapper .sl-navigation button.sl-next {\n    right: 10px;\n    font-size: 3rem;\n  }\n  .sl-wrapper .sl-navigation button.sl-prev {\n    left: 10px;\n    font-size: 3rem;\n  }\n}\n@media (min-width: 50em) {\n  .sl-wrapper .sl-navigation button {\n    width: 44px;\n  }\n  .sl-wrapper .sl-navigation button.sl-next {\n    right: 20px;\n    font-size: 3rem;\n  }\n  .sl-wrapper .sl-navigation button.sl-prev {\n    left: 20px;\n    font-size: 3rem;\n  }\n}\n.sl-wrapper .sl-image {\n  position: fixed;\n  -ms-touch-action: none;\n  touch-action: none;\n  z-index: 10000;\n}\n.sl-wrapper .sl-image img {\n  margin: 0;\n  padding: 0;\n  display: block;\n  border: 0 none;\n  width: 100%;\n  height: auto;\n}\n@media (min-width: 35.5em) {\n  .sl-wrapper .sl-image img {\n    border: 0 none;\n  }\n}\n@media (min-width: 50em) {\n  .sl-wrapper .sl-image img {\n    border: 0 none;\n  }\n}\n.sl-wrapper .sl-image iframe {\n  background: #000;\n  border: 0 none;\n}\n@media (min-width: 35.5em) {\n  .sl-wrapper .sl-image iframe {\n    border: 0 none;\n  }\n}\n@media (min-width: 50em) {\n  .sl-wrapper .sl-image iframe {\n    border: 0 none;\n  }\n}\n.sl-wrapper .sl-image .sl-caption {\n  display: none;\n  padding: 10px;\n  color: #fff;\n  background: rgba(0, 0, 0, 0.8);\n  font-size: 1rem;\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n}\n.sl-wrapper .sl-image .sl-caption.pos-top {\n  bottom: auto;\n  top: 0;\n}\n.sl-wrapper .sl-image .sl-caption.pos-outside {\n  bottom: auto;\n}\n.sl-wrapper .sl-image .sl-download {\n  display: none;\n  position: absolute;\n  bottom: 5px;\n  right: 5px;\n  color: #fff;\n  z-index: 1060;\n}\n\n.sl-spinner {\n  display: none;\n  border: 5px solid #333;\n  border-radius: 40px;\n  height: 40px;\n  left: 50%;\n  margin: -20px 0 0 -20px;\n  opacity: 0;\n  position: fixed;\n  top: 50%;\n  width: 40px;\n  z-index: 1007;\n  -webkit-animation: pulsate 1s ease-out infinite;\n  -moz-animation: pulsate 1s ease-out infinite;\n  -ms-animation: pulsate 1s ease-out infinite;\n  -o-animation: pulsate 1s ease-out infinite;\n  animation: pulsate 1s ease-out infinite;\n}\n\n.sl-scrollbar-measure {\n  position: absolute;\n  top: -9999px;\n  width: 50px;\n  height: 50px;\n  overflow: scroll;\n}\n\n.sl-transition {\n  transition: -moz-transform ease 200ms;\n  transition: -ms-transform ease 200ms;\n  transition: -o-transform ease 200ms;\n  transition: -webkit-transform ease 200ms;\n  transition: transform ease 200ms;\n}\n\n@-webkit-keyframes pulsate {\n  0% {\n    transform: scale(0.1);\n    opacity: 0;\n  }\n  50% {\n    opacity: 1;\n  }\n  100% {\n    transform: scale(1.2);\n    opacity: 0;\n  }\n}\n@keyframes pulsate {\n  0% {\n    transform: scale(0.1);\n    opacity: 0;\n  }\n  50% {\n    opacity: 1;\n  }\n  100% {\n    transform: scale(1.2);\n    opacity: 0;\n  }\n}\n@-moz-keyframes pulsate {\n  0% {\n    transform: scale(0.1);\n    opacity: 0;\n  }\n  50% {\n    opacity: 1;\n  }\n  100% {\n    transform: scale(1.2);\n    opacity: 0;\n  }\n}\n@-o-keyframes pulsate {\n  0% {\n    transform: scale(0.1);\n    opacity: 0;\n  }\n  50% {\n    opacity: 1;\n  }\n  100% {\n    transform: scale(1.2);\n    opacity: 0;\n  }\n}\n@-ms-keyframes pulsate {\n  0% {\n    transform: scale(0.1);\n    opacity: 0;\n  }\n  50% {\n    opacity: 1;\n  }\n  100% {\n    transform: scale(1.2);\n    opacity: 0;\n  }\n}\n@keyframes splide-loading {\n  0% {\n    transform: rotate(0);\n  }\n  to {\n    transform: rotate(1turn);\n  }\n}\n.splide--draggable > .splide__slider > .splide__track, .splide--draggable > .splide__track {\n  -webkit-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n.splide--fade > .splide__slider > .splide__track > .splide__list, .splide--fade > .splide__track > .splide__list {\n  display: block;\n}\n\n.splide--fade > .splide__slider > .splide__track > .splide__list > .splide__slide, .splide--fade > .splide__track > .splide__list > .splide__slide {\n  left: 0;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  z-index: 0;\n}\n\n.splide--fade > .splide__slider > .splide__track > .splide__list > .splide__slide.is-active, .splide--fade > .splide__track > .splide__list > .splide__slide.is-active {\n  opacity: 1;\n  position: relative;\n  z-index: 1;\n}\n\n.splide--rtl {\n  direction: rtl;\n}\n\n.splide--ttb.is-active > .splide__slider > .splide__track > .splide__list, .splide--ttb.is-active > .splide__track > .splide__list {\n  display: block;\n}\n\n.splide__container {\n  box-sizing: border-box;\n  position: relative;\n}\n\n.splide__list {\n  -webkit-backface-visibility: hidden;\n  backface-visibility: hidden;\n  display: -ms-flexbox;\n  display: flex;\n  height: 100%;\n  margin: 0 !important;\n  padding: 0 !important;\n  transform-style: preserve-3d;\n}\n\n.splide.is-initialized:not(.is-active) .splide__list {\n  display: block;\n}\n\n.splide__pagination {\n  -ms-flex-align: center;\n  align-items: center;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n  -ms-flex-pack: center;\n  justify-content: center;\n  margin: 0;\n  pointer-events: none;\n}\n\n.splide__pagination li {\n  display: inline-block;\n  line-height: 1;\n  list-style-type: none;\n  margin: 0;\n  pointer-events: auto;\n}\n\n.splide__progress__bar {\n  width: 0;\n}\n\n.splide {\n  outline: none;\n  position: relative;\n  visibility: hidden;\n}\n\n.splide.is-initialized, .splide.is-rendered {\n  visibility: visible;\n}\n\n.splide__slide {\n  -webkit-backface-visibility: hidden;\n  backface-visibility: hidden;\n  box-sizing: border-box;\n  -ms-flex-negative: 0;\n  flex-shrink: 0;\n  list-style-type: none !important;\n  margin: 0;\n  outline: none;\n  position: relative;\n}\n\n.splide__slide img {\n  vertical-align: bottom;\n}\n\n.splide__slider {\n  position: relative;\n}\n\n.splide__spinner {\n  animation: splide-loading 1s linear infinite;\n  border: 2px solid #999;\n  border-left-color: transparent;\n  border-radius: 50%;\n  bottom: 0;\n  display: inline-block;\n  height: 20px;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n  width: 20px;\n}\n\n.splide__track {\n  overflow: hidden;\n  position: relative;\n  z-index: 0;\n} /* Functional styling;\n * These styles are required for noUiSlider to function.\n * You don't need to change these rules to apply your design.\n */\n.noUi-target,\n.noUi-target * {\n  -webkit-touch-callout: none;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  -webkit-user-select: none;\n  -ms-touch-action: none;\n  touch-action: none;\n  -ms-user-select: none;\n  -moz-user-select: none;\n  user-select: none;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n\n.noUi-target {\n  position: relative;\n}\n\n.noUi-base,\n.noUi-connects {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  z-index: 1;\n}\n\n/* Wrapper for all connect elements.\n */\n.noUi-connects {\n  overflow: hidden;\n  z-index: 0;\n}\n\n.noUi-connect,\n.noUi-origin {\n  will-change: transform;\n  position: absolute;\n  z-index: 1;\n  top: 0;\n  right: 0;\n  height: 100%;\n  width: 100%;\n  -ms-transform-origin: 0 0;\n  -webkit-transform-origin: 0 0;\n  -webkit-transform-style: preserve-3d;\n  transform-origin: 0 0;\n  transform-style: flat;\n}\n\n/* Offset direction\n */\n.noUi-txt-dir-rtl.noUi-horizontal .noUi-origin {\n  left: 0;\n  right: auto;\n}\n\n/* Give origins 0 height/width so they don't interfere with clicking the\n * connect elements.\n */\n.noUi-vertical .noUi-origin {\n  top: -100%;\n  width: 0;\n}\n\n.noUi-horizontal .noUi-origin {\n  height: 0;\n}\n\n.noUi-handle {\n  -webkit-backface-visibility: hidden;\n  backface-visibility: hidden;\n  position: absolute;\n}\n\n.noUi-touch-area {\n  height: 100%;\n  width: 100%;\n}\n\n.noUi-state-tap .noUi-connect,\n.noUi-state-tap .noUi-origin {\n  -webkit-transition: transform 0.3s;\n  transition: transform 0.3s;\n}\n\n.noUi-state-drag * {\n  cursor: inherit !important;\n}\n\n/* Slider size and handle placement;\n */\n.noUi-horizontal {\n  height: 18px;\n}\n\n.noUi-horizontal .noUi-handle {\n  width: 34px;\n  height: 28px;\n  right: -17px;\n  top: -6px;\n}\n\n.noUi-vertical {\n  width: 18px;\n}\n\n.noUi-vertical .noUi-handle {\n  width: 28px;\n  height: 34px;\n  right: -6px;\n  bottom: -17px;\n}\n\n.noUi-txt-dir-rtl.noUi-horizontal .noUi-handle {\n  left: -17px;\n  right: auto;\n}\n\n/* Styling;\n * Giving the connect element a border radius causes issues with using transform: scale\n */\n.noUi-target {\n  background: #FAFAFA;\n  border-radius: 4px;\n  border: 1px solid #D3D3D3;\n  box-shadow: inset 0 1px 1px #F0F0F0, 0 3px 6px -5px #BBB;\n}\n\n.noUi-connects {\n  border-radius: 3px;\n}\n\n.noUi-connect {\n  background: #3FB8AF;\n}\n\n/* Handles and cursors;\n */\n.noUi-draggable {\n  cursor: ew-resize;\n}\n\n.noUi-vertical .noUi-draggable {\n  cursor: ns-resize;\n}\n\n.noUi-handle {\n  border: 1px solid #D9D9D9;\n  border-radius: 3px;\n  background: #FFF;\n  cursor: default;\n  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #EBEBEB, 0 3px 6px -3px #BBB;\n}\n\n.noUi-active {\n  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #DDD, 0 3px 6px -3px #BBB;\n}\n\n/* Handle stripes;\n */\n.noUi-handle:before,\n.noUi-handle:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  height: 14px;\n  width: 1px;\n  background: #E8E7E6;\n  left: 14px;\n  top: 6px;\n}\n\n.noUi-handle:after {\n  left: 17px;\n}\n\n.noUi-vertical .noUi-handle:before,\n.noUi-vertical .noUi-handle:after {\n  width: 14px;\n  height: 1px;\n  left: 6px;\n  top: 14px;\n}\n\n.noUi-vertical .noUi-handle:after {\n  top: 17px;\n}\n\n/* Disabled state;\n */\n[disabled] .noUi-connect {\n  background: #B8B8B8;\n}\n\n[disabled].noUi-target,\n[disabled].noUi-handle,\n[disabled] .noUi-handle {\n  cursor: not-allowed;\n}\n\n/* Base;\n *\n */\n.noUi-pips,\n.noUi-pips * {\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n\n.noUi-pips {\n  position: absolute;\n  color: #999;\n}\n\n/* Values;\n *\n */\n.noUi-value {\n  position: absolute;\n  white-space: nowrap;\n  text-align: center;\n}\n\n.noUi-value-sub {\n  color: #ccc;\n  font-size: 10px;\n}\n\n/* Markings;\n *\n */\n.noUi-marker {\n  position: absolute;\n  background: #CCC;\n}\n\n.noUi-marker-sub {\n  background: #AAA;\n}\n\n.noUi-marker-large {\n  background: #AAA;\n}\n\n/* Horizontal layout;\n *\n */\n.noUi-pips-horizontal {\n  padding: 10px 0;\n  height: 80px;\n  top: 100%;\n  left: 0;\n  width: 100%;\n}\n\n.noUi-value-horizontal {\n  -webkit-transform: translate(-50%, 50%);\n  transform: translate(-50%, 50%);\n}\n\n.noUi-rtl .noUi-value-horizontal {\n  -webkit-transform: translate(50%, 50%);\n  transform: translate(50%, 50%);\n}\n\n.noUi-marker-horizontal.noUi-marker {\n  margin-left: -1px;\n  width: 2px;\n  height: 5px;\n}\n\n.noUi-marker-horizontal.noUi-marker-sub {\n  height: 10px;\n}\n\n.noUi-marker-horizontal.noUi-marker-large {\n  height: 15px;\n}\n\n/* Vertical layout;\n *\n */\n.noUi-pips-vertical {\n  padding: 0 10px;\n  height: 100%;\n  top: 0;\n  left: 100%;\n}\n\n.noUi-value-vertical {\n  -webkit-transform: translate(0, -50%);\n  transform: translate(0, -50%);\n  padding-left: 25px;\n}\n\n.noUi-rtl .noUi-value-vertical {\n  -webkit-transform: translate(0, 50%);\n  transform: translate(0, 50%);\n}\n\n.noUi-marker-vertical.noUi-marker {\n  width: 5px;\n  height: 2px;\n  margin-top: -1px;\n}\n\n.noUi-marker-vertical.noUi-marker-sub {\n  width: 10px;\n}\n\n.noUi-marker-vertical.noUi-marker-large {\n  width: 15px;\n}\n\n.noUi-tooltip {\n  display: block;\n  position: absolute;\n  border: 1px solid #D9D9D9;\n  border-radius: 3px;\n  background: #fff;\n  color: #000;\n  padding: 5px;\n  text-align: center;\n  white-space: nowrap;\n}\n\n.noUi-horizontal .noUi-tooltip {\n  -webkit-transform: translate(-50%, 0);\n  transform: translate(-50%, 0);\n  left: 50%;\n  bottom: 120%;\n}\n\n.noUi-vertical .noUi-tooltip {\n  -webkit-transform: translate(0, -50%);\n  transform: translate(0, -50%);\n  top: 50%;\n  right: 120%;\n}\n\n.noUi-horizontal .noUi-origin > .noUi-tooltip {\n  -webkit-transform: translate(50%, 0);\n  transform: translate(50%, 0);\n  left: auto;\n  bottom: 10px;\n}\n\n.noUi-vertical .noUi-origin > .noUi-tooltip {\n  -webkit-transform: translate(0, -18px);\n  transform: translate(0, -18px);\n  top: auto;\n  right: 28px;\n}\n\nhtml {\n  box-sizing: border-box;\n}\n\n*,\n*:before,\n*:after {\n  box-sizing: inherit;\n}\n\nimg {\n  box-sizing: content-box;\n}\n\n:root {\n  font-size: 100%;\n}\n\nbody {\n  position: relative;\n  margin: 0;\n  padding-top: 70px;\n  color: #000;\n  font-family: \"Barlow\", sans-serif;\n  font-size: 16px;\n  line-height: 1;\n  background-color: #f5f5f5;\n}\n@media (min-width: 992px) {\n  body {\n    padding-top: 151px;\n  }\n}\n@media (min-width: 1200px) {\n  body {\n    padding-top: 194px;\n  }\n}\n\n@-ms-viewport {\n  width: device-width;\n}\nimg,\nsvg {\n  display: inline-block;\n  max-width: 100%;\n  height: auto;\n  border: none;\n}\n\niframe {\n  max-width: 100%;\n}\n\n.container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 12px;\n}\n\n.container--short {\n  max-width: 920px;\n}\n\n.container--sidebar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n@media (min-width: 1200px) {\n  .container--sidebar {\n    flex-wrap: nowrap;\n  }\n}\n\n.row {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n  margin: 0 -12px;\n}\n.row > .col {\n  padding: 12px;\n}\n\n@media (max-width: 768px) {\n  .row--block {\n    display: block;\n  }\n}\n\n.row--align > .col {\n  flex-direction: row;\n}\n\n.row--space {\n  justify-content: space-between;\n}\n\n.row--center {\n  align-items: center;\n}\n\n.row--start {\n  justify-content: flex-start;\n}\n\n.row--nogap {\n  margin: 0;\n}\n.row--nogap > .col {\n  padding: 0;\n}\n\n.col {\n  display: flex;\n  flex: 0 0 100%;\n  flex-direction: column;\n}\n\n@media (min-width: 576px) {\n  .col--2 {\n    flex-basis: auto;\n    width: 50%;\n  }\n}\n\n@media (min-width: 576px) {\n  .col--3 {\n    flex-basis: auto;\n    width: 50%;\n  }\n}\n@media (min-width: 992px) {\n  .col--3 {\n    width: 33.33333333%;\n  }\n}\n\n@media (min-width: 576px) {\n  .col--4 {\n    flex-basis: auto;\n    width: 50%;\n  }\n}\n@media (min-width: 992px) {\n  .col--4 {\n    width: 25%;\n  }\n}\n\n@media (min-width: 576px) {\n  .col--5 {\n    flex-basis: auto;\n    width: 50%;\n  }\n}\n@media (min-width: 992px) {\n  .col--5 {\n    width: 20%;\n  }\n}\n\n@media (min-width: 576px) {\n  .col--6 {\n    flex-basis: auto;\n    width: 50%;\n  }\n}\n@media (min-width: 992px) {\n  .col--6 {\n    width: 16.6666666667%;\n  }\n}\n\n.col--grow {\n  flex-grow: 1;\n}\n\n.col--end {\n  align-items: flex-end;\n}\n\n.col--start {\n  align-items: flex-start;\n}\n\n.col--center {\n  align-items: center;\n}\n\n.col--row {\n  flex-direction: row;\n}\n\n/* barlow-regular - latin-ext_latin */\n@font-face {\n  font-family: \"Barlow\";\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(\"../fonts/barlow-v5-latin-ext_latin-regular.eot\"); /* IE9 Compat Modes */\n  src: local(\"\"), url(\"../fonts/barlow-v5-latin-ext_latin-regular.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/barlow-v5-latin-ext_latin-regular.woff2\") format(\"woff2\"), url(\"../fonts/barlow-v5-latin-ext_latin-regular.woff\") format(\"woff\"), url(\"../fonts/barlow-v5-latin-ext_latin-regular.ttf\") format(\"truetype\"), url(\"../fonts/barlow-v5-latin-ext_latin-regular.svg#Barlow\") format(\"svg\"); /* Legacy iOS */\n}\n/* barlow-500 - latin-ext_latin */\n@font-face {\n  font-family: \"Barlow\";\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(\"../fonts/barlow-v5-latin-ext_latin-500.eot\"); /* IE9 Compat Modes */\n  src: local(\"\"), url(\"../fonts/barlow-v5-latin-ext_latin-500.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/barlow-v5-latin-ext_latin-500.woff2\") format(\"woff2\"), url(\"../fonts/barlow-v5-latin-ext_latin-500.woff\") format(\"woff\"), url(\"../fonts/barlow-v5-latin-ext_latin-500.ttf\") format(\"truetype\"), url(\"../fonts/barlow-v5-latin-ext_latin-500.svg#Barlow\") format(\"svg\"); /* Legacy iOS */\n}\n/* barlow-600 - latin-ext_latin */\n@font-face {\n  font-family: \"Barlow\";\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(\"../fonts/barlow-v5-latin-ext_latin-600.eot\"); /* IE9 Compat Modes */\n  src: local(\"\"), url(\"../fonts/barlow-v5-latin-ext_latin-600.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/barlow-v5-latin-ext_latin-600.woff2\") format(\"woff2\"), url(\"../fonts/barlow-v5-latin-ext_latin-600.woff\") format(\"woff\"), url(\"../fonts/barlow-v5-latin-ext_latin-600.ttf\") format(\"truetype\"), url(\"../fonts/barlow-v5-latin-ext_latin-600.svg#Barlow\") format(\"svg\"); /* Legacy iOS */\n}\n/* barlow-700 - latin-ext_latin */\n@font-face {\n  font-family: \"Barlow\";\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(\"../fonts/barlow-v5-latin-ext_latin-700.eot\"); /* IE9 Compat Modes */\n  src: local(\"\"), url(\"../fonts/barlow-v5-latin-ext_latin-700.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/barlow-v5-latin-ext_latin-700.woff2\") format(\"woff2\"), url(\"../fonts/barlow-v5-latin-ext_latin-700.woff\") format(\"woff\"), url(\"../fonts/barlow-v5-latin-ext_latin-700.ttf\") format(\"truetype\"), url(\"../fonts/barlow-v5-latin-ext_latin-700.svg#Barlow\") format(\"svg\"); /* Legacy iOS */\n}\nh1,\nh2,\nh3,\nh4 {\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-weight: 700;\n  line-height: 1.4;\n}\n@media (min-width: 768px) {\n  h1,\n  h2,\n  h3,\n  h4 {\n    line-height: 1.5;\n  }\n}\n\nh1 {\n  font-size: 30px;\n}\n@media (min-width: 992px) {\n  h1 {\n    font-size: 40px;\n  }\n}\n\nh2 {\n  font-size: 20px;\n}\n@media (min-width: 992px) {\n  h2 {\n    font-size: 30px;\n  }\n}\n\nh3 {\n  font-size: 18px;\n}\n@media (min-width: 992px) {\n  h3 {\n    font-size: 22px;\n  }\n}\n\n@media (min-width: 992px) {\n  h4 {\n    font-size: 18px;\n  }\n}\n\na {\n  color: #000;\n}\na:hover, a:focus {\n  color: #585858;\n}\n@media (min-width: 768px) {\n  a[href^=\"tel:\"] {\n    text-decoration: none;\n  }\n}\n\np {\n  margin: 0 0 30px 0;\n  line-height: 1.4;\n}\n@media (min-width: 768px) {\n  p {\n    line-height: 1.8;\n  }\n}\n\nul,\nol {\n  margin: 0 0 30px 0;\n  padding: 0 0 0 20px;\n  line-height: 1.8;\n}\nul ul,\nul ol,\nol ul,\nol ol {\n  margin: 0;\n  padding: 3px 3px 0 10px;\n}\nul ul li:last-child,\nul ol li:last-child,\nol ul li:last-child,\nol ol li:last-child {\n  padding-bottom: 0;\n}\n\ndl {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 20px 0;\n  line-height: 1.5;\n}\ndl dt {\n  flex: 0 0 20%;\n  padding-right: 20px;\n  font-weight: 600;\n}\ndl dd {\n  flex: 1 1 80%;\n  margin: 0;\n  margin-bottom: 15px;\n}\n\ninput,\ntextarea,\nselect {\n  padding: 13px 20px;\n  color: #000;\n  font-size: 17px;\n  line-height: 1;\n  border: 2px solid #1c1c1c;\n  border-radius: 5px;\n  background-color: #fff;\n}\ninput:focus,\ntextarea:focus,\nselect:focus {\n  border-color: #bcbcbc;\n  outline: 0;\n}\ninput.is-success,\ntextarea.is-success,\nselect.is-success {\n  border-color: #008f05;\n}\ninput.is-danger,\ntextarea.is-danger,\nselect.is-danger {\n  border-color: #e84747;\n}\n\ntextarea {\n  width: 100%;\n  line-height: 1.4;\n}\n\nlabel {\n  font-size: 15px;\n}\nlabel em {\n  color: #9e9e9e;\n  font-style: normal;\n}\n\nbutton,\ninput[type=submit] {\n  width: auto;\n  cursor: pointer;\n}\n\ninput[type=checkbox],\ninput[type=radio] {\n  width: auto;\n}\n\n.icon {\n  display: inline-block;\n  vertical-align: middle;\n  position: relative;\n  color: #000;\n}\n.icon:before {\n  content: \"\";\n  display: block;\n}\n\n.icon__svg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  fill: currentColor;\n  pointer-events: none;\n  transform: translateZ(0);\n}\n\n.icon--arrow-next {\n  width: 16px;\n}\n.icon--arrow-next:before {\n  padding-top: 100%;\n}\n\n.icon--basket {\n  width: 16px;\n}\n.icon--basket:before {\n  padding-top: 100%;\n}\n\n.icon--car {\n  width: 16px;\n}\n.icon--car:before {\n  padding-top: 100%;\n}\n\n.icon--clock {\n  width: 16px;\n}\n.icon--clock:before {\n  padding-top: 100%;\n}\n\n.icon--close {\n  width: 16px;\n}\n.icon--close:before {\n  padding-top: 100%;\n}\n\n.icon--color-facebook {\n  width: 16px;\n}\n.icon--color-facebook:before {\n  padding-top: 100%;\n}\n\n.icon--color-instagram {\n  width: 16px;\n}\n.icon--color-instagram:before {\n  padding-top: 100%;\n}\n\n.icon--color-login-google {\n  width: 16px;\n}\n.icon--color-login-google:before {\n  padding-top: 100%;\n}\n\n.icon--compare {\n  width: 16px;\n}\n.icon--compare:before {\n  padding-top: 100%;\n}\n\n.icon--danger {\n  width: 16px;\n}\n.icon--danger:before {\n  padding-top: 100%;\n}\n\n.icon--delete {\n  width: 16px;\n}\n.icon--delete:before {\n  padding-top: 100%;\n}\n\n.icon--email {\n  width: 16px;\n}\n.icon--email:before {\n  padding-top: 100%;\n}\n\n.icon--heart-full {\n  width: 16px;\n}\n.icon--heart-full:before {\n  padding-top: 100%;\n}\n\n.icon--heart {\n  width: 16px;\n}\n.icon--heart:before {\n  padding-top: 100%;\n}\n\n.icon--home {\n  width: 16px;\n}\n.icon--home:before {\n  padding-top: 100%;\n}\n\n.icon--leaf {\n  width: 16px;\n}\n.icon--leaf:before {\n  padding-top: 100%;\n}\n\n.icon--login-apple {\n  width: 16px;\n}\n.icon--login-apple:before {\n  padding-top: 100%;\n}\n\n.icon--login-facebook {\n  width: 16px;\n}\n.icon--login-facebook:before {\n  padding-top: 100%;\n}\n\n.icon--login-seznam {\n  width: 16px;\n}\n.icon--login-seznam:before {\n  padding-top: 100%;\n}\n\n.icon--magnifier {\n  width: 16px;\n}\n.icon--magnifier:before {\n  padding-top: 100%;\n}\n\n.icon--minus {\n  width: 16px;\n}\n.icon--minus:before {\n  padding-top: 100%;\n}\n\n.icon--next {\n  width: 16px;\n}\n.icon--next:before {\n  padding-top: 100%;\n}\n\n.icon--percentage {\n  width: 16px;\n}\n.icon--percentage:before {\n  padding-top: 100%;\n}\n\n.icon--phone {\n  width: 16px;\n}\n.icon--phone:before {\n  padding-top: 100%;\n}\n\n.icon--pin {\n  width: 16px;\n}\n.icon--pin:before {\n  padding-top: 100%;\n}\n\n.icon--plus {\n  width: 16px;\n}\n.icon--plus:before {\n  padding-top: 100%;\n}\n\n.icon--print {\n  width: 16px;\n}\n.icon--print:before {\n  padding-top: 100%;\n}\n\n.icon--success {\n  width: 16px;\n}\n.icon--success:before {\n  padding-top: 100%;\n}\n\n.icon--user {\n  width: 16px;\n}\n.icon--user:before {\n  padding-top: 100%;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  z-index: 999;\n  width: 100%;\n  background-color: #fff;\n}\n\n.header--simple {\n  position: fixed;\n  padding: 10px 0;\n}\n.header--simple h1 {\n  margin: 0;\n}\n.header--simple h1 a {\n  display: block;\n}\n.header--simple h1 img {\n  display: block;\n  width: 50px;\n}\n@media (min-width: 992px) {\n  .header--simple h1 img {\n    width: 90px;\n  }\n}\n.header--simple .header-top__contact {\n  font-size: 17px;\n}\n.header--simple .header-top__contact a {\n  font-size: 17px;\n}\n.header--simple .header-top__contact small {\n  font-size: 14px;\n}\n.header--simple .phone:before {\n  top: 10px;\n}\n\n.header__body {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  background-color: #fff;\n}\n@media (min-width: 992px) {\n  .header__body {\n    padding-top: 10px;\n    padding-bottom: 10px;\n  }\n}\n\n.header__logo {\n  flex: 0 0 76px;\n}\n@media (min-width: 1200px) {\n  .header__logo {\n    flex: 1 1 33%;\n  }\n}\n.header__logo a {\n  position: relative;\n  z-index: 101;\n  display: inline-block;\n  margin-bottom: -25px;\n  padding-bottom: 25px;\n}\n@media (min-width: 992px) {\n  .header__logo a {\n    margin-bottom: -50px;\n    padding-bottom: 50px;\n  }\n}\n@media (min-width: 1200px) {\n  .header__logo a {\n    margin-bottom: -75px;\n    padding-bottom: 75px;\n  }\n}\n.header__logo img {\n  margin-bottom: -25px;\n}\n@media (min-width: 992px) {\n  .header__logo img {\n    margin-bottom: -50px;\n  }\n}\n@media (min-width: 1200px) {\n  .header__logo img {\n    margin-top: 10px;\n    margin-bottom: -75px;\n  }\n}\n\n.header__nav {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n}\n@media (min-width: 1200px) {\n  .header__nav {\n    flex: 1 1 33%;\n  }\n}\n.header__nav a {\n  text-decoration: none;\n}\n.header__nav .icon {\n  width: 30px;\n}\n\n.header__fav {\n  position: relative;\n  display: none;\n  margin-right: 20px;\n}\n@media (min-width: 360px) {\n  .header__fav {\n    display: block;\n  }\n}\n@media (min-width: 992px) {\n  .header__fav {\n    margin-right: 50px;\n  }\n}\n.header__fav .count {\n  position: absolute;\n  top: -8px;\n  left: 20px;\n}\n\n.header__switcher {\n  overflow: hidden;\n  width: 70px;\n  height: 70px;\n  margin-right: -12px;\n  text-indent: 9999px;\n  background: #ffc300 url(../img/menu.svg) center center no-repeat;\n  cursor: pointer;\n}\n@media (min-width: 992px) {\n  .header__switcher {\n    display: none;\n  }\n}\n.header__switcher.is-open {\n  background-image: url(../img/menu-close.svg);\n}\n\n.header__search {\n  margin-right: 12px;\n}\n@media (min-width: 992px) {\n  .header__search {\n    display: none;\n  }\n}\n\n.nav {\n  display: none;\n  font-size: 18px;\n  font-weight: 600;\n  background-color: #ffc300;\n}\n@media (min-width: 992px) {\n  .nav {\n    display: block;\n  }\n}\n@media (max-width: 991px) {\n  .nav {\n    overflow: auto;\n    max-height: calc(100vh - 70px);\n  }\n}\n.nav.is-open {\n  position: absolute;\n  z-index: 100;\n  display: block;\n  width: 100%;\n  padding-bottom: 0;\n}\n.nav.is-open .container {\n  padding-right: 0;\n  padding-left: 0;\n}\n.nav ul,\n.nav li {\n  margin: 0;\n  padding: 0;\n  list-style-type: none;\n}\n@media (min-width: 992px) {\n  .nav ul {\n    display: flex;\n  }\n}\n@media (min-width: 992px) {\n  .nav li:hover .nav__inner {\n    display: block;\n  }\n}\n.nav .container {\n  position: relative;\n  padding-left: 100px;\n}\n@media (min-width: 1200px) {\n  .nav .container {\n    padding-left: 170px;\n  }\n}\n\n.nav__main > a {\n  display: block;\n  padding: 15px 20px;\n  color: #000;\n  text-decoration: none;\n  text-transform: uppercase;\n  background-repeat: no-repeat;\n  background-position: right 20px center;\n}\n@media (min-width: 992px) {\n  .nav__main > a {\n    padding: 10px;\n    background-position: right 10px center;\n  }\n}\n@media (min-width: 1200px) {\n  .nav__main > a {\n    padding: 20px 15px;\n  }\n}\n@media (min-width: 1400px) {\n  .nav__main > a {\n    padding: 20px 30px;\n  }\n}\n@media (max-width: 1199px) {\n  .nav__main > a {\n    line-height: 1.3;\n  }\n}\n@media (min-width: 992px) {\n  .nav__main:hover > a {\n    color: #fff;\n    background-color: #585858;\n  }\n  .nav__main:hover.has-submenu > a {\n    background-image: url(../img/nav-inverse.svg);\n  }\n}\n.nav__main.has-submenu > a {\n  background-image: url(../img/nav.svg);\n}\n@media (min-width: 992px) {\n  .nav__main.has-submenu > a {\n    padding-right: 25px;\n  }\n}\n@media (min-width: 1400px) {\n  .nav__main.has-submenu > a {\n    padding-right: 30px;\n  }\n}\n@media (max-width: 991px) {\n  .nav__main.has-submenu.is-open > a {\n    color: #fff;\n    background-color: #585858;\n    background-image: url(../img/nav-inverse.svg);\n  }\n  .nav__main.has-submenu.is-open .nav__inner {\n    display: block;\n  }\n}\n\n.nav__main--info {\n  background-color: #bcbcbc;\n}\n@media (min-width: 992px) {\n  .nav__main--info {\n    display: none;\n  }\n}\n\n.nav__inner {\n  display: none;\n  padding: 5px 10px 20px 10px;\n  background-color: #585858;\n}\n@media (min-width: 992px) {\n  .nav__inner {\n    position: absolute;\n    right: 0;\n    left: 0;\n    z-index: 101;\n    padding: 40px 50px;\n  }\n}\n.nav__inner .category {\n  margin: 2px 0;\n  padding: 10px;\n  font-weight: 400;\n  background-color: #3f3f3f;\n}\n@media (min-width: 992px) {\n  .nav__inner .category {\n    margin: 5px;\n  }\n}\n.nav__inner .category:hover, .nav__inner .category:focus {\n  background-color: #1c1c1c;\n}\n\n.nav__helper {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin: 30px 18px 10px 18px;\n  padding: 12px 8px 12px 12px;\n  font-size: 13px;\n  font-weight: 400;\n  background-color: #fff;\n}\n@media (min-width: 992px) {\n  .nav__helper {\n    display: none;\n  }\n}\n.nav__helper p {\n  margin: 0;\n}\n@media (max-width: 575px) {\n  .nav__helper p:last-child {\n    width: 25px;\n  }\n}\n.nav__helper a {\n  font-size: 15px;\n  font-weight: 500;\n  text-decoration: none;\n}\n@media (max-width: 575px) {\n  .nav__helper a.is-active {\n    display: none;\n  }\n}\n.nav__helper small {\n  font-size: 12px;\n  white-space: nowrap;\n}\n\n.section {\n  padding: 30px 0;\n}\n@media (min-width: 992px) {\n  .section {\n    padding: 80px 0;\n  }\n}\n\n.section--short {\n  padding: 15px 0;\n}\n@media (min-width: 992px) {\n  .section--short {\n    padding: 50px 0;\n  }\n}\n\n.section--base {\n  background-color: #fff;\n}\n\n.section--dark {\n  background-color: #f2f2f2;\n}\n\n.section--inverse {\n  color: #fff;\n  background-color: #1c1c1c;\n}\n\n.section--follow {\n  padding-top: 0;\n}\n\n.section--nav {\n  padding: 0;\n}\n\n.section--detail {\n  padding-top: 30px;\n}\n@media (min-width: 992px) {\n  .section--detail .section__content {\n    max-width: 800px;\n  }\n}\n.section--detail .section__content h2 em {\n  display: inline-block;\n  padding-left: 5px;\n  color: #585858;\n  font-size: 15px;\n  font-style: normal;\n}\n.section--detail .section__content p,\n.section--detail .section__content ul,\n.section--detail .section__content ol {\n  margin: 0 0 20px 0;\n}\n@media (min-width: 768px) {\n  .section--detail .section__content p,\n  .section--detail .section__content ul,\n  .section--detail .section__content ol {\n    line-height: 1.4;\n  }\n}\n\n@media (min-width: 768px) {\n  .section--text {\n    font-size: 20px;\n  }\n  .section--text h2,\n  .section--text h3,\n  .section--text h4,\n  .section--text p,\n  .section--text ul,\n  .section--text ol {\n    max-width: 1110px;\n  }\n}\n@media (max-width: 767px) {\n  .section--text ul,\n  .section--text li {\n    line-height: 1.4;\n  }\n}\n\n.section__content {\n  flex: 1 1 auto;\n  max-width: 100%;\n}\n\n.section__title {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n@media (min-width: 768px) {\n  .section__title {\n    justify-content: space-between;\n    margin-bottom: 40px;\n  }\n}\n@media (max-width: 767px) {\n  .section__title .link {\n    display: none;\n  }\n}\n.section__title * {\n  margin: 0;\n}\n\n.section__pages {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-end;\n  margin-top: 40px;\n}\n.section__pages p,\n.section__pages nav {\n  width: 100%;\n  margin: 0 0 15px 0;\n  text-align: center;\n}\n@media (min-width: 768px) {\n  .section__pages p,\n  .section__pages nav {\n    width: 50%;\n    margin: 0;\n  }\n}\n@media (min-width: 992px) {\n  .section__pages p,\n  .section__pages nav {\n    width: 33.33333333%;\n  }\n}\n@media (min-width: 768px) {\n  .section__pages nav {\n    text-align: right;\n  }\n}\n\n.section__pages--full p {\n  width: 100%;\n}\n\n.section__contact {\n  font-size: 14px;\n  vertical-align: top;\n}\n@media (min-width: 576px) {\n  .section__contact {\n    display: inline-block;\n  }\n}\n.section__contact h2 {\n  margin-bottom: 20px;\n  font-size: 20px;\n}\n.section__contact p {\n  margin-right: 15px;\n  line-height: 1.57;\n}\n@media (min-width: 576px) {\n  .section__contact + .section__contact {\n    margin-left: 100px;\n  }\n}\n@media (min-width: 992px) {\n  .section__contact + .section__contact {\n    margin-left: 0;\n  }\n}\n@media (min-width: 1200px) {\n  .section__contact + .section__contact {\n    margin-left: 100px;\n  }\n}\n\n.section__divider {\n  width: 100%;\n  height: 1px;\n  margin: 35px 0;\n  background-color: #dedede;\n}\n.section__divider hr {\n  display: none;\n}\n\n.section__search {\n  margin: 25px 0;\n}\n@media (min-width: 992px) {\n  .section__search {\n    margin: 50px 0;\n  }\n}\n.section__search ul {\n  margin: 0;\n  padding: 0;\n}\n.section__search .category {\n  margin: 0 5px 5px 0;\n}\n.section__search .article {\n  margin-right: 0;\n  margin-left: 0;\n}\n\n.section__anchor {\n  position: relative;\n  top: -80px;\n  z-index: -1;\n  display: block;\n  visibility: hidden;\n}\n@media (min-width: 992px) {\n  .section__anchor {\n    top: -200px;\n  }\n}\n\n.section__p {\n  margin: 0 0 30px 0;\n  line-height: 1.4;\n}\n@media (min-width: 768px) {\n  .section__p {\n    line-height: 1.8;\n  }\n}\n\n.footer {\n  padding: 30px 0;\n}\n@media (min-width: 768px) {\n  .footer {\n    padding: 50px 0;\n  }\n}\n@media (max-width: 991px) {\n  .footer {\n    text-align: center;\n  }\n}\n.footer h2 {\n  font-size: 17px;\n  font-weight: 600;\n}\n.footer .contact-box {\n  text-align: left;\n}\n@media (max-width: 991px) {\n  .footer .contact-box {\n    margin-right: auto;\n    margin-left: auto;\n  }\n}\n\n.footer__payment {\n  margin: 15px 0;\n  padding: 0;\n  list-style-type: none;\n}\n@media (min-width: 992px) {\n  .footer__payment {\n    margin-top: 30px;\n  }\n}\n.footer__payment li {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 63px;\n  height: 39px;\n  margin: 8px 4px 0 0;\n  vertical-align: top;\n  border-radius: 5px;\n  background-color: #fff;\n}\n@media (min-width: 768px) {\n  .footer__payment li {\n    margin: 8px 0 0 8px;\n  }\n}\n\n.footer__rating {\n  justify-content: center;\n  text-align: center;\n}\n@media (max-width: 991px) {\n  .footer__rating {\n    width: 100%;\n  }\n}\n.footer__rating p {\n  margin: 0;\n}\n\n.footer__copyright {\n  margin: 0;\n  font-size: 15px;\n  text-align: center;\n}\n.footer__copyright p {\n  margin: 0 0 10px 0;\n}\n\n.footer__note {\n  max-width: 950px;\n  margin: 0 auto;\n  font-size: 12px;\n  line-height: 1.3;\n  text-align: center;\n}\n\n.alert {\n  padding: 16px 0 15px 0;\n  color: #fff;\n  font-size: 18px;\n  line-height: 1.2;\n  background-color: #ffc300;\n}\n@media (min-width: 992px) {\n  .alert {\n    font-size: 20px;\n    line-height: 1;\n  }\n}\n.alert a {\n  color: #fff;\n}\n.alert a:hover, .alert a:focus {\n  color: #fff;\n}\n.alert strong {\n  font-weight: 600;\n}\n.alert .icon {\n  display: none;\n  width: 35px;\n  margin-right: 15px;\n  color: #fff;\n}\n@media (min-width: 768px) {\n  .alert .icon {\n    display: inline-block;\n  }\n}\n.alert .icon.icon--delete {\n  position: absolute;\n  top: 12px;\n  right: 0;\n  display: inline-block;\n  width: 12px;\n  cursor: pointer;\n}\n.alert .icon.icon--delete:hover, .alert .icon.icon--delete:focus {\n  opacity: 0.8;\n}\n.alert .container {\n  position: relative;\n  padding-right: 40px;\n}\n.alert.is-success {\n  background-color: #008f05;\n}\n.alert.is-danger {\n  background-color: #e84747;\n}\n.alert.is-info {\n  color: #000;\n  background: #ffc300;\n  background-image: linear-gradient(to right, #ffc300 0%, #ff9900 100%);\n}\n.alert.is-info a,\n.alert.is-info .icon {\n  color: #000;\n}\n\n.article {\n  text-decoration: none;\n}\n.article h3 {\n  margin-bottom: 15px;\n}\n\n.article--wide {\n  display: flex;\n  flex-wrap: wrap;\n  max-width: 930px;\n  margin: 0 auto 25px auto;\n}\n@media (min-width: 768px) {\n  .article--wide {\n    flex-wrap: nowrap;\n    margin-bottom: 45px;\n  }\n}\n@media (min-width: 768px) {\n  .article--wide .article__content {\n    padding: 10px 10px 10px 20px;\n  }\n}\n@media (min-width: 992px) {\n  .article--wide .article__content {\n    padding-left: 45px;\n  }\n}\n\n.article--small {\n  display: flex;\n  align-items: center;\n}\n.article--small + .article--small {\n  margin-top: 12px;\n}\n.article--small h3 {\n  margin: 0;\n  font-size: 16px;\n}\n.article--small h3 strong {\n  display: block;\n  color: #ffc300;\n  font-size: 12px;\n}\n.article--small .article__image {\n  flex: 0 0 100px;\n}\n.article--small .article__content {\n  padding: 0 0 0 17px;\n}\n\n.article__image {\n  position: relative;\n  overflow: hidden;\n  margin: 0;\n  border-radius: 5px;\n}\n@media (min-width: 992px) {\n  .article__image {\n    flex: 1 0 auto;\n  }\n}\n.article__image img {\n  display: block;\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.article__meta {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  display: flex;\n  align-items: flex-end;\n  margin: 0;\n  padding: 10px 23px;\n  color: #fff;\n  font-size: 14px;\n  background-image: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.47) 100%);\n}\n.article__meta strong {\n  display: inline-block;\n  margin-right: 20px;\n  color: #ffc300;\n  text-transform: uppercase;\n}\n\n.article__content {\n  display: flex;\n  flex: 0 1 auto;\n  flex-direction: column;\n  padding-top: 20px;\n}\n\n.article__description {\n  margin-bottom: 20px;\n  color: #585858;\n  font-size: 15px;\n  line-height: 1.53;\n}\n\n.article__link {\n  margin-top: auto;\n  margin-bottom: 0;\n}\n\n.article-detail {\n  margin-top: -10px;\n}\n@media (min-width: 768px) {\n  .article-detail {\n    margin-top: -220px;\n  }\n}\n\n.article-detail__head {\n  position: relative;\n  margin: 0;\n  border-radius: 5px;\n  background: #000;\n}\n@media (min-width: 768px) {\n  .article-detail__head {\n    overflow: hidden;\n  }\n}\n.article-detail__head img {\n  display: block;\n}\n\n.article-detail__meta {\n  margin: 0;\n  padding: 20px;\n  color: #fff;\n  font-size: 15px;\n  border-radius: 5px;\n  background-image: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.47) 100%);\n}\n@media (min-width: 768px) {\n  .article-detail__meta {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    display: flex;\n    align-items: flex-end;\n    justify-content: space-between;\n    padding: 30px 40px;\n  }\n}\n.article-detail__meta h1 {\n  margin: 0 0 15px 0;\n  font-size: 25px;\n}\n@media (min-width: 768px) {\n  .article-detail__meta h1 {\n    margin: 0 10px 0 0;\n    font-size: 40px;\n  }\n}\n.article-detail__meta h1 small {\n  display: block;\n  color: #ffc300;\n  font-size: 14px;\n  font-weight: 700;\n  text-transform: uppercase;\n}\n.article-detail__meta p {\n  margin-bottom: 10px;\n}\n@media (min-width: 768px) {\n  .article-detail__meta p {\n    text-align: center;\n  }\n}\n.article-detail__meta p strong {\n  font-size: 21px;\n  font-weight: 600;\n  line-height: 1;\n}\n.article-detail__meta p .avatar {\n  margin: 0 auto 10px auto;\n}\n@media (max-width: 767px) {\n  .article-detail__meta p .avatar {\n    display: none;\n  }\n}\n\n.article-detail__content {\n  padding: 20px 10px;\n}\n@media (min-width: 768px) {\n  .article-detail__content {\n    padding: 40px;\n  }\n}\n.article-detail__content .product--top {\n  margin-bottom: 40px;\n}\n.article-detail__content .product--top .product__content {\n  padding: 0 0 10px 0;\n}\n@media (min-width: 768px) {\n  .article-detail__content .product--top .product__content p {\n    max-width: 350px;\n  }\n}\n\n.article-detail__rating {\n  margin-top: 20px;\n  padding: 15px;\n  color: #585858;\n  font-size: 15px;\n  text-align: center;\n  background-color: #fff;\n}\n@media (min-width: 768px) {\n  .article-detail__rating {\n    padding: 40px;\n  }\n}\n.article-detail__rating ul,\n.article-detail__rating li {\n  margin: 0;\n  padding: 0;\n  list-style-type: none;\n}\n.article-detail__rating li {\n  display: inline-block;\n  width: 49%;\n  padding-bottom: 15px;\n}\n@media (min-width: 768px) {\n  .article-detail__rating li {\n    width: 20%;\n    padding-bottom: 0;\n  }\n}\n.article-detail__rating li strong {\n  display: block;\n  font-size: 25px;\n  line-height: 1;\n}\n\n.article-detail__stars {\n  margin: 0;\n  font-size: 18px;\n}\n@media (min-width: 768px) {\n  .article-detail__stars {\n    margin-top: 30px;\n  }\n}\n.article-detail__stars strong {\n  font-size: 33px;\n  line-height: 1;\n}\n.article-detail__stars .star {\n  margin: 8px 2px 0 2px;\n}\n\n.article-detail__author {\n  margin: 40px 0;\n  padding: 20px;\n  font-size: 16px;\n  line-height: 1.5;\n  background-color: #eee;\n}\n@media (min-width: 768px) {\n  .article-detail__author {\n    padding: 40px;\n  }\n}\n.article-detail__author > p {\n  margin-bottom: 0;\n  line-height: 1.5;\n}\n.article-detail__author .contact-box {\n  flex-wrap: wrap;\n  margin-bottom: 10px;\n}\n.article-detail__author .contact-box p {\n  max-width: 100%;\n}\n.article-detail__author .contact-box strong {\n  font-size: 25px;\n  font-weight: 600;\n}\n\n.avatar {\n  display: block;\n  overflow: hidden;\n  width: 110px;\n  height: 110px;\n  padding: 0;\n  border-radius: 50%;\n}\n.avatar img {\n  width: 100%;\n  height: auto;\n}\n\n.avatar--small {\n  width: 35px;\n  height: 35px;\n}\n\n.badge {\n  display: inline-block;\n  padding: 5px 12px;\n  color: #9e9e9e;\n  font-size: 13px;\n  font-weight: 600;\n  text-decoration: none;\n  border: 2px solid #9e9e9e;\n  border-radius: 5px;\n}\n.badge.is-active {\n  color: #fff;\n  border-color: #ffc300;\n}\n\na.badge:hover, a.badge:focus {\n  color: #fff;\n  border-color: #ffc300;\n}\n\n.badge--inverse {\n  color: #fff;\n  background-color: #9e9e9e;\n}\n\n.basket {\n  position: relative;\n  font-size: 18px;\n  font-weight: 600;\n}\n.basket:after {\n  content: \"\";\n  position: absolute;\n  bottom: -30px;\n  left: 13px;\n  display: block;\n  display: none;\n  width: 20px;\n  height: 20px;\n  background-color: #1c1c1c;\n  transform: rotate(45deg);\n}\n@media (min-width: 768px) {\n  .basket:hover .basket__detail, .basket:hover:after, .basket:focus .basket__detail, .basket:focus:after {\n    display: block;\n  }\n  .header--simple .basket:hover:after, .header--simple .basket:focus:after {\n    display: none;\n  }\n}\n.basket a {\n  color: #000;\n  text-decoration: none;\n}\n.basket small {\n  font-weight: 400;\n}\n.basket .icon {\n  width: 38px;\n  margin-right: 15px;\n}\n.basket .count {\n  position: absolute;\n  top: -5px;\n  left: 25px;\n}\n.basket ::-webkit-scrollbar {\n  width: 10px;\n}\n.basket ::-webkit-scrollbar-track {\n  border-radius: 5px;\n  background-color: #000;\n}\n.basket ::-webkit-scrollbar-thumb {\n  border: 3px solid #000;\n  border-radius: 5px;\n  background: #ffc300;\n}\n.basket ::-webkit-scrollbar-thumb:hover {\n  background-color: #ffc300;\n}\n\n.basket__price {\n  display: none;\n}\n@media (min-width: 992px) {\n  .basket__price {\n    display: inline-block;\n  }\n}\n\n.basket__detail {\n  position: absolute;\n  right: 0;\n  z-index: 999;\n  display: none;\n  width: 560px;\n  padding-top: 15px;\n  color: #fff;\n}\n\n.basket__content {\n  overflow: auto;\n  width: 100%;\n  max-height: calc(100vh - 70px - 220px);\n  padding: 20px 10px 20px 25px;\n  border-top-left-radius: 5px;\n  border-top-right-radius: 5px;\n  background-color: #1c1c1c;\n}\n@media (min-width: 992px) {\n  .basket__content {\n    max-height: calc(100vh - 125px - 220px);\n  }\n}\n\n.basket__footer {\n  width: 100%;\n  padding: 20px 25px 30px 25px;\n  border-bottom-right-radius: 5px;\n  border-bottom-left-radius: 5px;\n  background-color: #272727;\n}\n.basket__footer p {\n  margin: 15px 0 0 0;\n}\n.basket__footer .order-benefits,\n.basket__footer .order-benefits__info {\n  margin: 0;\n  padding: 0;\n}\n\n.basket__sum {\n  padding: 5px 0 10px 0;\n  font-size: 14px;\n  font-weight: 400;\n  text-align: right;\n}\n.basket__sum strong {\n  display: inline-block;\n  padding: 0 44px 0 20px;\n  font-size: 19px;\n}\n\n.breadcrumb {\n  color: #bcbcbc;\n  font-size: 14px;\n  line-height: 1.5;\n}\n@media (min-width: 992px) {\n  .breadcrumb {\n    line-height: 1;\n  }\n}\n.breadcrumb a {\n  margin-right: 5px;\n  color: #fff;\n  text-decoration: none;\n}\n.breadcrumb a:after {\n  content: \"\";\n  display: inline-block;\n  width: 5px;\n  height: 8px;\n  margin-left: 5px;\n  background: url(../img/arrow.svg) center center no-repeat;\n}\n.breadcrumb a:hover, .breadcrumb a:focus {\n  text-decoration: underline;\n}\n.breadcrumb .icon {\n  width: 17px;\n  color: #fff;\n}\n\n.btn,\na.btn {\n  display: inline-block;\n  padding: 13px 22px;\n  color: #000;\n  font: inherit;\n  font-size: 13px;\n  font-weight: 600;\n  line-height: 1;\n  text-align: center;\n  text-decoration: none;\n  text-transform: uppercase;\n  border: none;\n  border-radius: 5px;\n  background-color: #ffc300;\n  cursor: pointer;\n}\n.btn:hover, .btn:focus,\na.btn:hover,\na.btn:focus {\n  background-image: linear-gradient(to right, #ffc300 0%, #ff9900 100%);\n}\n.btn .icon,\na.btn .icon {\n  width: 11px;\n  margin: -4px 0 0 10px;\n}\n.btn .icon--next,\na.btn .icon--next {\n  display: none;\n}\n@media (min-width: 576px) {\n  .btn .icon--next,\n  a.btn .icon--next {\n    display: inline-block;\n  }\n}\n\n.btn--big,\na.btn--big {\n  font-size: 18px;\n}\n@media (min-width: 576px) {\n  .btn--big,\n  a.btn--big {\n    padding: 15px 25px;\n  }\n}\n\n.btn--success,\na.btn--success {\n  color: #fff;\n  background-color: #008f05;\n}\n.btn--success:hover, .btn--success:focus,\na.btn--success:hover,\na.btn--success:focus {\n  background-color: #005c03;\n  background-image: none;\n}\n.btn--success .icon,\na.btn--success .icon {\n  color: #fff;\n}\n\n.btn--danger,\na.btn--danger {\n  color: #fff;\n  background-color: #e84747;\n}\n.btn--danger:hover, .btn--danger:focus,\na.btn--danger:hover,\na.btn--danger:focus {\n  background-color: #e01c1c;\n  background-image: none;\n}\n.btn--danger .icon,\na.btn--danger .icon {\n  color: #fff;\n}\n\n.btn--helper,\na.btn--helper {\n  color: #fff;\n  background-color: #585858;\n}\n.btn--helper:hover, .btn--helper:focus,\na.btn--helper:hover,\na.btn--helper:focus {\n  color: #000;\n  background-color: #fff;\n  background-image: none;\n}\n\n.btn--soft,\na.btn--soft {\n  background-color: #fff3cc;\n}\n.btn--soft:hover, .btn--soft:focus,\na.btn--soft:hover,\na.btn--soft:focus {\n  background-color: #ffc300;\n  background-image: none;\n}\n\n.btn--login,\na.btn--login {\n  width: 100%;\n  max-width: 410px;\n  font-size: 15px;\n}\n@media (min-width: 576px) {\n  .btn--login,\n  a.btn--login {\n    padding: 15px 25px;\n    font-size: 18px;\n  }\n}\n.btn--login:hover, .btn--login:focus,\na.btn--login:hover,\na.btn--login:focus {\n  background-color: #000;\n  background-image: none;\n}\n.btn--login .icon,\na.btn--login .icon {\n  width: 22px;\n  margin-right: 5px;\n  color: #fff;\n}\n.btn--login + .btn--login,\n.btn--login + a.btn--login,\na.btn--login + .btn--login,\na.btn--login + a.btn--login {\n  margin-top: 10px;\n}\n\n.btn--seznam,\na.btn--seznam {\n  color: #fff;\n  background-color: #c00;\n}\n\n.btn--facebook,\na.btn--facebook {\n  color: #fff;\n  background-color: #0866ff;\n}\n\n.btn--google,\na.btn--google {\n  background-color: #f7f7f7;\n}\n.btn--google:hover, .btn--google:focus,\na.btn--google:hover,\na.btn--google:focus {\n  background-color: #dfdfdf;\n  background-image: none;\n}\n\n.btn--apple,\na.btn--apple {\n  color: #fff;\n  background-color: #6e6e73;\n}\n\n.category {\n  display: flex;\n  align-items: center;\n  margin: -5px 0;\n  padding: 12px 15px;\n  color: #fff;\n  line-height: 1.2;\n  text-decoration: none;\n  border-radius: 5px;\n  background-color: #272727;\n}\n@media (min-width: 768px) {\n  .category {\n    margin: 0;\n  }\n}\n.category:hover, .category:focus {\n  color: #fff;\n  background-color: #585858;\n}\n\n.category__image {\n  overflow: hidden;\n  width: 40px;\n  height: 40px;\n  margin-right: 15px;\n  border-radius: 5px;\n  background-color: #fff;\n}\n\n.contact-box {\n  display: flex;\n  align-items: center;\n  color: #585858;\n  font-size: 15px;\n}\n.contact-box p {\n  max-width: 390px;\n  margin: 0;\n  line-height: 1.3;\n}\n.contact-box a {\n  font-size: 18px;\n}\n@media (min-width: 768px) {\n  .contact-box a {\n    font-size: 22px;\n  }\n}\n.contact-box strong {\n  display: inline-block;\n  margin-bottom: 5px;\n  color: #000;\n  font-size: 19px;\n}\n.contact-box small {\n  display: inline-block;\n  padding-left: 16px;\n  color: #acacac;\n  font-size: 11px;\n}\n.contact-box .avatar {\n  margin-right: 5px;\n}\n@media (min-width: 360px) {\n  .contact-box .avatar {\n    margin-right: 15px;\n  }\n}\n@media (min-width: 768px) {\n  .contact-box .avatar {\n    margin-right: 32px;\n  }\n}\n.contact-box .phone:before {\n  top: 4px;\n}\n\n.contact-box--list a {\n  font-size: 15px;\n}\n\n.contact-box + .contact-box {\n  margin-top: 25px;\n}\n\n.content-header {\n  padding: 30px 0;\n  color: #fff;\n  background-color: #1c1c1c;\n}\n@media (min-width: 992px) {\n  .content-header {\n    padding-bottom: 60px;\n  }\n}\n.content-header h1 {\n  margin: 25px 0 0 0;\n  line-height: 1.3;\n}\n.content-header ul {\n  margin-top: 15px;\n  padding: 0;\n  list-style-type: none;\n}\n.content-header li {\n  margin: 0;\n}\n.content-header .container {\n  position: relative;\n}\n.content-header .form {\n  display: none;\n}\n@media (min-width: 992px) {\n  .content-header .form {\n    display: block;\n    float: right;\n    width: 100%;\n    max-width: 640px;\n    margin-top: 30px;\n  }\n}\n.content-header .form textarea {\n  height: 140px;\n}\n\n@media (min-width: 768px) {\n  .content-header--detail {\n    min-height: 300px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .content-header--product {\n    min-height: 320px;\n    padding-bottom: 40px;\n  }\n  .content-header--product h1 {\n    margin-top: 10px;\n    margin-left: 630px;\n  }\n  .content-header--product .content-header__description {\n    margin-left: 630px;\n  }\n}\n\n.content-header__description {\n  margin: 15px 0 0 0;\n  color: #bcbcbc;\n  font-size: 17px;\n  line-height: 1.35;\n}\n.content-header__description a {\n  color: #bcbcbc;\n}\n.content-header__description a:hover, .content-header__description a:focus {\n  color: #fff;\n}\n.center .content-header__description {\n  margin-right: auto;\n  margin-left: auto;\n}\n\n.content-header__contact {\n  margin-top: 20px;\n  color: #bcbcbc;\n  font-size: 17px;\n  line-height: 1.3;\n}\n@media (min-width: 992px) {\n  .content-header__contact {\n    margin-top: 105px;\n    margin-bottom: 40px;\n  }\n}\n.content-header__contact + .content-header__contact {\n  margin-top: 0;\n}\n.content-header__contact a {\n  display: block;\n  color: #fff;\n  font-size: 30px;\n  font-weight: 700;\n}\n.content-header__contact small {\n  position: relative;\n  font-size: 14px;\n}\n\n.content-header__search {\n  margin: 0;\n}\n@media (min-width: 768px) {\n  .content-header__search {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: -30px;\n  }\n}\n.content-header__search .form-search {\n  width: 100%;\n  margin: 0;\n  padding-top: 25px;\n}\n@media (min-width: 768px) {\n  .content-header__search .form-search {\n    text-align: right;\n  }\n}\n.content-header__search .form-search input {\n  display: inline-block;\n  max-width: 440px;\n  border-color: #fff;\n}\n@media (min-width: 768px) {\n  .content-header__search .form-search input {\n    margin-left: 20px;\n  }\n}\n.content-header__search .form-search .btn {\n  margin-bottom: 10px;\n  padding: 16px 25px;\n  font-size: 18px;\n}\n\n.count {\n  display: inline-block;\n  min-width: 22px;\n  height: 22px;\n  padding: 4px 3px 0 3px;\n  color: #000;\n  font-size: 13px;\n  font-weight: 600;\n  line-height: 1;\n  text-align: center;\n  border-radius: 50%;\n  background-color: #ffc300;\n}\n.count.is-success {\n  color: #fff;\n  background-color: #008f05;\n}\n.count.is-danger {\n  color: #fff;\n  background-color: #e84747;\n}\n\n.delivery {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #fff;\n}\n@media (min-width: 768px) {\n  .delivery {\n    flex-wrap: nowrap;\n  }\n}\n.delivery p {\n  margin: 0;\n}\n.delivery p + p {\n  margin-top: 10px;\n}\n.delivery + h2 {\n  margin-top: 40px;\n}\n\n.delivery__icon {\n  flex: 0 0 80px;\n}\n.delivery__icon img {\n  display: block;\n  margin: 0 auto;\n}\n\n.delivery__content {\n  width: 100%;\n  padding: 10px 0;\n}\n@media (min-width: 768px) {\n  .delivery__content {\n    padding: 10px 20px;\n  }\n}\n.delivery__content h3 {\n  margin: 0;\n  font-size: 20px;\n  font-weight: 500;\n}\n.delivery__content h3 img {\n  margin: 0 0 0 10px;\n  vertical-align: middle;\n}\n.delivery__content h3 + p {\n  margin-top: 10px;\n}\n.delivery__content p {\n  max-width: 800px;\n  color: #585858;\n}\n@media (min-width: 768px) {\n  .delivery__content p {\n    line-height: 1.63;\n  }\n}\n\n.delivery__price {\n  font-weight: 500;\n  line-height: 1.3;\n}\n@media (min-width: 768px) {\n  .delivery__price {\n    text-align: right;\n  }\n}\n@media (min-width: 1200px) {\n  .delivery__price {\n    line-height: 1.8;\n    white-space: nowrap;\n  }\n}\n.delivery__price strong {\n  font-size: 19px;\n  white-space: nowrap;\n}\n\n.discount {\n  display: inline-block;\n  flex: 0 0 48px;\n  width: 48px;\n  height: 48px;\n  padding-top: 15px;\n  color: #000;\n  font-size: 16px;\n  font-weight: 600;\n  line-height: 1;\n  text-align: center;\n  border-radius: 50%;\n  background-color: #ffc300;\n}\n.discount.is-success {\n  color: #fff;\n  background-color: #008f05;\n}\n.discount.is-danger {\n  color: #fff;\n  background-color: #e84747;\n}\n\n@media (min-width: 1200px) {\n  .filter-switch {\n    display: none;\n  }\n}\n.filter-switch em {\n  font-style: normal;\n}\n.filter-switch .icon {\n  width: 20px;\n}\n\n.goal h3 {\n  margin-bottom: 5px;\n  font-size: 20px;\n  text-transform: uppercase;\n}\n.goal ul,\n.goal li {\n  margin: 0;\n  padding: 0;\n  list-style-type: none;\n}\n.goal a {\n  color: #d8d8d8;\n  font-size: 15px;\n  text-decoration: none;\n  text-transform: uppercase;\n}\n.goal a:hover, .goal a:focus {\n  color: #ffc300;\n}\n\n.goal__image {\n  position: relative;\n  overflow: hidden;\n  margin: 0;\n  border-radius: 5px;\n}\n.goal__image:hover img, .goal__image:focus img {\n  transform: scale(1.07);\n}\n.goal__image img {\n  display: block;\n  transition: all 0.2s;\n}\n\n.goal__content {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 11;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-end;\n  margin: 0;\n  padding: 20px 30px;\n  background-image: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 57%, rgba(0, 0, 0, 0.58) 100%);\n}\n\n.header-top {\n  display: none;\n  padding: 8px 0;\n  font-size: 14px;\n  background-color: #f6f6f6;\n}\n@media (min-width: 992px) {\n  .header-top {\n    display: block;\n  }\n}\n.header-top p {\n  margin: 0;\n  line-height: 1;\n}\n.header-top a {\n  text-decoration: none;\n}\n.header-top a:hover, .header-top a:focus {\n  color: #000;\n}\n.header-top .container {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n.header-top .phone {\n  color: #585858;\n  font-size: 12px;\n  line-height: 12px;\n}\n.header-top .phone:before {\n  top: 4px;\n}\n\n@media (min-width: 1200px) {\n  .header-top__nav {\n    flex: 1 1 33%;\n  }\n}\n.header-top__nav ul,\n.header-top__nav li {\n  margin: 0;\n  padding: 0;\n  list-style-type: none;\n  line-height: 1;\n}\n.header-top__nav li {\n  display: inline-block;\n}\n.header-top__nav li:hover ul, .header-top__nav li:focus ul {\n  display: block;\n}\n.header-top__nav li.has-submenu {\n  margin-right: 12px;\n  background: url(../img/nav.svg) center right no-repeat;\n  background-size: 8px auto;\n}\n.header-top__nav li.has-submenu:hover, .header-top__nav li.has-submenu:focus {\n  background-image: url(../img/nav-open.svg);\n}\n.header-top__nav li ul {\n  position: absolute;\n  z-index: 1002;\n  display: none;\n  padding-top: 5px;\n}\n.header-top__nav li ul li {\n  display: block;\n  padding: 5px 15px;\n  background-color: #f6f6f6;\n}\n.header-top__nav li ul li:first-child {\n  padding-top: 10px;\n}\n.header-top__nav li ul li:last-child {\n  padding-bottom: 10px;\n}\n.header-top__nav a {\n  margin-right: 17px;\n  color: #585858;\n}\n\n.header-top__contact {\n  display: none;\n  align-items: center;\n  justify-content: center;\n  margin: 0;\n  font-size: 13px;\n}\n@media (min-width: 768px) {\n  .header-top__contact {\n    display: flex;\n  }\n}\n@media (min-width: 1200px) {\n  .header-top__contact {\n    flex: 1 1 33%;\n  }\n}\n.header-top__contact a {\n  margin: 0 6px 0 15px;\n  font-size: 15px;\n  font-weight: 500;\n}\n\n.header-top__lang {\n  text-align: right;\n}\n@media (min-width: 1200px) {\n  .header-top__lang {\n    flex: 1 1 33%;\n  }\n}\n.header-top__lang a {\n  opacity: 0.5;\n}\n.header-top__lang a:hover, .header-top__lang a:focus, .header-top__lang a.is-active {\n  opacity: 1;\n}\n\n.help {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n  margin: 2px;\n  vertical-align: middle;\n  background: url(../img/help.svg) center center no-repeat;\n}\n\n.hey {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  justify-content: center;\n  margin: 20px 0;\n  padding: 15px;\n  font-size: 19px;\n  background-color: #fff3cc;\n}\n@media (min-width: 992px) {\n  .hey {\n    justify-content: space-between;\n  }\n}\n.hey p {\n  margin: 10px;\n  line-height: 1.3;\n}\n@media (max-width: 767px) {\n  .hey p {\n    width: 100%;\n  }\n}\n.hey a {\n  font-weight: 600;\n}\n.hey .icon {\n  width: 25px;\n}\n\n.hey--center {\n  justify-content: center;\n}\n.hey--center .hey__info {\n  flex: 0 0 auto;\n}\n\n.hey--vertical {\n  justify-content: center;\n  text-align: center;\n}\n@media (min-width: 992px) {\n  .hey--vertical {\n    flex-direction: column;\n  }\n}\n.hey--vertical a {\n  display: block;\n}\n\n.hey__info {\n  flex: 1 1 auto;\n}\n\n@media (max-width: 767px) {\n  .hey__icon {\n    display: none;\n  }\n}\n\n.instagram {\n  padding: 30px 0;\n  color: #fff;\n  font-size: 18px;\n  font-weight: 700;\n  background-color: #272727;\n}\n@media (min-width: 992px) {\n  .instagram {\n    padding: 60px 0 75px 0;\n  }\n}\n.instagram a {\n  display: block;\n  overflow: hidden;\n}\n.instagram a:hover, .instagram a:focus {\n  color: #f5f5f5;\n}\n.instagram a:hover img, .instagram a:focus img {\n  transform: scale(1.1);\n}\n.instagram img {\n  display: block;\n  border-radius: 5px;\n  transition: all 0.2s;\n}\n@media (max-width: 991px) {\n  .instagram .col--6 {\n    flex: 1 1 33%;\n    padding: 6px;\n  }\n}\n\n.instagram__header {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  justify-content: center;\n  margin-bottom: 15px;\n}\n@media (min-width: 768px) {\n  .instagram__header {\n    justify-content: space-between;\n  }\n}\n@media (min-width: 768px) {\n  .instagram__header h2 {\n    margin: 0;\n    font-size: 30px;\n  }\n}\n.instagram__header p {\n  margin: 0;\n}\n.instagram__header a {\n  display: inline-flex;\n  align-items: center;\n  flex-direction: column;\n  width: 48%;\n  color: #fff;\n  font-size: 15px;\n  text-align: center;\n}\n@media (min-width: 992px) {\n  .instagram__header a {\n    display: inline-block;\n    width: auto;\n    margin-left: 50px;\n    font-size: 18px;\n  }\n}\n.instagram__header .icon {\n  width: 22px;\n  margin-right: 10px;\n}\n\n.link {\n  font-weight: 700;\n  text-decoration: underline;\n}\n@media (min-width: 768px) {\n  .link {\n    font-size: 18px;\n  }\n}\n.link:after {\n  content: \"\";\n  display: inline-block;\n  width: 22px;\n  height: 22px;\n  margin-top: -3px;\n  margin-left: 6px;\n  vertical-align: middle;\n  background: url(../img/link.svg) center center no-repeat;\n}\n\n.link--inverse {\n  color: #fff;\n}\n.link--inverse:after, .link--inverse.link--back:before {\n  background-image: url(../img/link-inverse.svg);\n}\n.link--inverse:hover, .link--inverse:focus {\n  color: #f5f5f5;\n}\n\n.link--up:after {\n  transform: rotate(-90deg);\n}\n\n.link--down:after {\n  transform: rotate(90deg);\n}\n\n.link--back:after {\n  display: none;\n}\n.link--back:before {\n  content: \"\";\n  display: inline-block;\n  width: 22px;\n  height: 22px;\n  margin-top: -3px;\n  margin-right: 10px;\n  vertical-align: middle;\n  background: url(../img/link.svg) center center no-repeat;\n  transform: rotate(180deg);\n}\n\n.login {\n  position: relative;\n  margin-right: 10px;\n}\n@media (min-width: 992px) {\n  .login {\n    display: block;\n    margin-right: 50px;\n  }\n}\n.login:after {\n  content: \"\";\n  position: absolute;\n  bottom: -30px;\n  left: 5px;\n  display: none;\n  width: 20px;\n  height: 20px;\n  background-color: #1c1c1c;\n  transform: rotate(45deg);\n}\n.login .count {\n  position: absolute;\n  top: -8px;\n  left: 18px;\n  padding-top: 3px;\n}\n.login .count .icon {\n  width: 11px;\n  color: #fff;\n}\n@media (min-width: 992px) {\n  .login.is-open .login__detail, .login.is-open:after {\n    display: block;\n  }\n}\n.login.is-logged h2 {\n  color: #ffc300;\n}\n.login.is-logged .icon--user {\n  color: #008f05;\n}\n.login.is-logged .login__detail {\n  width: 500px;\n}\n\n.login__detail {\n  position: absolute;\n  right: -80px;\n  z-index: 999;\n  display: none;\n  width: 790px;\n  padding-top: 15px;\n  color: #fff;\n}\n\n.login__content {\n  display: flex;\n  width: 100%;\n  border-radius: 5px;\n  background-color: #1c1c1c;\n}\n\n.login__form {\n  flex: 1 1 45%;\n  padding: 40px 60px 40px 50px;\n  border-top-left-radius: 5px;\n  border-bottom-left-radius: 5px;\n  background-color: #272727;\n}\n.login__form p {\n  margin: 0;\n}\n.login__form p + p {\n  margin-top: 10px;\n}\n.login__form input {\n  width: 100%;\n}\n.login__form .form__helper {\n  color: #fff;\n}\n.login__form .btn {\n  width: 100%;\n  margin-top: 10px;\n  text-align: center;\n}\n\n.login__benefits {\n  flex: 1 1 55%;\n  padding: 40px 30px 40px 50px;\n}\n.login__benefits h2 {\n  font-size: 23px;\n  font-weight: 600;\n  line-height: 1.3;\n}\n.login__benefits ul {\n  margin: 20px 0 20px 0;\n  padding: 0;\n  list-style-type: none;\n}\n.login__benefits li {\n  margin: 0;\n  padding: 3px 0 3px 25px;\n  font-size: 15px;\n  background: url(../img/list.svg) left center no-repeat;\n}\n.login__benefits li a {\n  color: #fff;\n  font-size: 16px;\n  text-decoration: underline;\n}\n.login__benefits p {\n  margin: 0;\n}\n.login__benefits p a + a {\n  margin-left: 12px;\n}\n\n.login__close {\n  position: absolute;\n  top: 30px;\n  right: 15px;\n  z-index: 11;\n  overflow: hidden;\n  width: 25px;\n  height: 25px;\n  cursor: pointer;\n}\n.login__close:hover, .login__close:focus {\n  opacity: 0.7;\n}\n.login__close .icon {\n  width: 25px;\n  color: #fff;\n}\n\n.manufacturer {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #fff;\n}\n@media (min-width: 768px) {\n  .manufacturer {\n    flex-wrap: nowrap;\n  }\n}\n.manufacturer + .section__anchor {\n  margin-top: 30px;\n}\n\n.manufacturer__logo {\n  flex: 0 0 200px;\n  padding: 25px;\n}\n.manufacturer__logo img {\n  display: block;\n  max-height: 150px;\n  margin: 0 auto;\n}\n\n.manufacturer__content {\n  width: 100%;\n  padding: 10px 0;\n}\n@media (min-width: 768px) {\n  .manufacturer__content {\n    padding: 10px 30px;\n  }\n}\n.manufacturer__content h3 {\n  font-weight: 500;\n}\n.manufacturer__content p {\n  color: #585858;\n}\n@media (min-width: 768px) {\n  .manufacturer__content p {\n    line-height: 1.63;\n  }\n}\n.manufacturer__content p:last-child {\n  margin-bottom: 0;\n}\n\n.modal {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1000;\n  display: none;\n  overflow: auto;\n  width: 100%;\n  background-color: rgba(0, 0, 0, 0.8);\n}\n.modal.is-open {\n  display: block;\n}\n\n.modal--question .modal__body,\n.modal--watch .modal__body {\n  max-width: 530px;\n}\n@media (min-width: 768px) {\n  .modal--question .modal__body,\n  .modal--watch .modal__body {\n    padding: 30px 40px;\n  }\n}\n.modal--question .modal__close,\n.modal--watch .modal__close {\n  top: 15px;\n  right: 15px;\n}\n@media (min-width: 768px) {\n  .modal--question .modal__close,\n  .modal--watch .modal__close {\n    top: 30px;\n    right: 40px;\n  }\n}\n\n.modal--watch .form input {\n  width: 60%;\n}\n\n.modal--variants h2 {\n  margin: 10px 0 30px 0;\n  font-size: 23px;\n  text-align: center;\n}\n.modal--variants .product--top h3 {\n  font-size: 17px;\n}\n.modal--variants .product--top .product__meta .btn {\n  margin: 0;\n}\n@media (max-width: 767px) {\n  .modal--variants .product--top .product__meta .discount {\n    display: none;\n  }\n}\n.modal--variants .product--top .store {\n  font-size: 13px;\n}\n.modal--variants .product--top .product__flavour {\n  right: 5px;\n  bottom: -2px;\n  width: 30px;\n  height: 30px;\n  padding: 3px;\n}\n@media (min-width: 768px) {\n  .modal--variants .product--top .product__flavour {\n    right: -2px;\n    width: 40px;\n    height: 40px;\n    padding: 3px;\n  }\n}\n\n@media (min-width: 768px) {\n  .modal--availability {\n    position: fixed;\n    overflow: auto;\n  }\n}\n.modal--availability h2 {\n  margin: 10px 0 30px 0;\n  font-size: 23px;\n  text-align: center;\n}\n.modal--availability .modal__body {\n  max-width: 530px;\n}\n@media (min-width: 768px) {\n  .modal--availability .modal__body {\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n  }\n}\n\n.modal--dark h2 {\n  margin: 10px 0 30px 0;\n  color: #fff;\n  text-align: center;\n}\n.modal--dark h2 .icon {\n  width: 30px;\n  margin-right: 5px;\n  color: #fff;\n}\n.modal--dark .modal__body {\n  background-color: #272727;\n}\n.modal--dark .modal__close .icon {\n  color: #fff;\n}\n.modal--dark .modal__products {\n  margin: 40px -20px -20px -20px;\n}\n.modal--dark .store {\n  font-size: 13px;\n}\n\n.modal__body {\n  position: relative;\n  width: 100%;\n  max-width: 920px;\n  padding: 15px;\n  background-color: #f5f5f5;\n}\n@media (min-width: 768px) {\n  .modal__body {\n    top: 50px;\n    left: 50%;\n    padding: 20px;\n    transform: translateX(-50%);\n  }\n}\n\n.modal__close {\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  overflow: hidden;\n  width: 26px;\n  height: 26px;\n  cursor: pointer;\n}\n@media (min-width: 768px) {\n  .modal__close {\n    width: 42px;\n    height: 42px;\n  }\n}\n.modal__close:hover, .modal__close:focus {\n  opacity: 0.7;\n}\n.modal__close .icon {\n  width: 26px;\n}\n@media (min-width: 768px) {\n  .modal__close .icon {\n    width: 42px;\n  }\n}\n\n.modal__submit {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n}\n@media (min-width: 768px) {\n  .modal__submit {\n    flex-wrap: nowrap;\n    justify-content: space-between;\n    margin: 25px 10px;\n  }\n}\n.modal__submit p {\n  margin: 15px;\n}\n@media (min-width: 768px) {\n  .modal__submit p {\n    margin: 0;\n  }\n}\n.modal__submit .link {\n  font-size: 16px;\n}\n\n.modal__products {\n  padding: 15px 25px 10px 25px;\n  background-color: #f5f5f5;\n}\n.modal__products h3 {\n  margin-bottom: 5px;\n  font-size: 24px;\n}\n\n.modal__availability {\n  margin: 0;\n  padding: 0 10px;\n  list-style-type: none;\n  font-size: 20px;\n}\n.modal__availability .icon {\n  width: 12px;\n  color: #ffc300;\n}\n.modal__availability .store {\n  font-size: 18px;\n}\n\n.nav-footer {\n  padding: 30px 10px;\n  color: #fff;\n  font-size: 15px;\n  background-color: #1c1c1c;\n}\n@media (min-width: 992px) {\n  .nav-footer {\n    padding: 70px 0;\n  }\n}\n.nav-footer h2 {\n  margin-bottom: 0;\n  color: #bcbcbc;\n  font-size: 18px;\n  font-weight: 600;\n  text-transform: uppercase;\n}\n@media (min-width: 768px) {\n  .nav-footer h2 {\n    margin-bottom: 25px;\n    color: #fff;\n  }\n}\n@media (max-width: 767px) {\n  .nav-footer h2:before {\n    content: \"\";\n    display: inline-block;\n    width: 23px;\n    height: 23px;\n    margin: 0 10px 3px 0;\n    vertical-align: middle;\n    background: url(../img/link-inverse.svg) center center no-repeat;\n    background-size: 23px 23px;\n    opacity: 0.5;\n    transform: rotate(90deg);\n  }\n}\n.nav-footer h2.is-open {\n  margin-bottom: 5px;\n  color: #fff;\n}\n.nav-footer h2.is-open:before {\n  opacity: 1;\n  transform: rotate(-90deg);\n}\n.nav-footer ul,\n.nav-footer li {\n  margin: 0;\n  padding: 0;\n  list-style-type: none;\n}\n.nav-footer ul {\n  display: none;\n}\n@media (min-width: 768px) {\n  .nav-footer ul {\n    display: block;\n  }\n}\n.nav-footer ul.is-open {\n  display: block;\n}\n.nav-footer li {\n  line-height: 2.27;\n}\n@media (min-width: 768px) {\n  .nav-footer li {\n    line-height: 2.67;\n  }\n}\n.nav-footer a {\n  color: #ccc;\n  text-decoration: none;\n}\n.nav-footer a:hover, .nav-footer a:focus {\n  color: #ffc300;\n}\n.nav-footer a.nav-footer__link {\n  text-decoration: underline;\n}\n\n.notice {\n  color: #e84747;\n  font-weight: bold;\n}\n\n.noUi-horizontal {\n  height: 6px;\n  margin: 0 5px;\n}\n\n.noUi-connect {\n  background-color: #ffc300;\n}\n\n.noUi-target {\n  border: 0;\n  background-color: #1c1c1c;\n  box-shadow: none;\n}\n\n.noUi-horizontal .noUi-handle {\n  top: -6px;\n  right: -9px;\n  width: 18px;\n  height: 18px;\n  border: 2px solid #1c1c1c;\n  border-radius: 50%;\n  background-color: #fff;\n  box-shadow: none;\n}\n.noUi-horizontal .noUi-handle:before, .noUi-horizontal .noUi-handle:after {\n  display: none;\n}\n\n.pagination a,\n.pagination span {\n  display: inline-block;\n  overflow: hidden;\n  width: 32px;\n  height: 32px;\n  color: #000;\n  font-size: 15px;\n  font-weight: 600;\n  line-height: 32px;\n  text-align: center;\n  vertical-align: middle;\n  text-decoration: none;\n  border-radius: 50%;\n  background-color: #ececec;\n}\n\nspan.paginator__current {\n  background-color: #fff;\n}\n\nspan.paginator__space {\n  width: 15px;\n  background: transparent;\n}\n\na.pagination__prev,\na.pagination__next {\n  width: 42px;\n  height: 42px;\n  text-indent: 9999px;\n  background: transparent;\n  background-image: url(../img/pagination-next.svg);\n  background-repeat: no-repeat;\n  background-position: center center;\n}\n\n.pagination__prev {\n  transform: rotate(180deg);\n}\n\n.phone {\n  position: relative;\n  padding-left: 15px;\n}\n.phone:before {\n  content: \"\";\n  position: absolute;\n  top: 6px;\n  left: 4px;\n  display: block;\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background-color: #bcbcbc;\n}\n.phone.is-offline:before {\n  background-color: #e84747;\n}\n.phone.is-online:before {\n  background-color: #27c900;\n}\n\n.progress {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  height: 6px;\n  border-radius: 50px;\n  background-color: #1c1c1c;\n}\n\n.progress--light .progress__bar {\n  background-color: #ffe89c;\n}\n\n.progress__bar {\n  position: absolute;\n  width: 100%;\n  height: 6px;\n  border-radius: 50px;\n  background-color: #ffc300;\n}\n\n.question {\n  max-width: 920px;\n  margin: 0 auto 25px auto;\n  padding: 15px;\n  color: #585858;\n  font-size: 15px;\n  border-radius: 5px;\n  background-color: #fff;\n}\n@media (min-width: 768px) {\n  .question {\n    padding: 25px;\n  }\n}\n.question h2 {\n  margin-bottom: 5px;\n  padding-left: 30px;\n  color: #000;\n  font-size: 18px;\n  background: url(../img/question.svg) left center no-repeat;\n}\n.question p {\n  margin: 0;\n  line-height: 1.33;\n}\n.question a {\n  color: #000;\n}\n\n.question--short {\n  max-width: 800px;\n  margin-right: 0;\n  margin-left: 0;\n}\n\n.question__meta {\n  padding-bottom: 10px;\n  color: #000;\n  font-size: 17px;\n}\n\n.question__answer {\n  position: relative;\n  margin-top: 35px;\n  padding: 15px;\n  color: #d1d1d1;\n  border-radius: 5px;\n  background-color: #1c1c1c;\n}\n@media (min-width: 768px) {\n  .question__answer {\n    padding: 25px;\n  }\n}\n.question__answer:before {\n  content: \"\";\n  position: absolute;\n  top: -5px;\n  left: 30px;\n  display: block;\n  width: 14px;\n  height: 14px;\n  background-color: #1c1c1c;\n  transform: rotate(45deg);\n}\n.question__answer h2 {\n  display: inline-block;\n  padding: 0;\n  color: #fff;\n  background: none;\n}\n.question__answer a {\n  color: #fff;\n}\n.question__answer .question__meta {\n  display: flex;\n  padding: 0 0 15px 0;\n  color: #fff;\n  line-height: 1;\n}\n.question__answer .question__meta em {\n  font-size: 12px;\n  font-style: normal;\n}\n.question__answer .question__meta .avatar {\n  flex-shrink: 0;\n  margin-right: 10px;\n}\n\n.question__products {\n  margin: 30px 0 10px 0;\n}\n.question__products h3 {\n  margin: 0;\n  font-size: 15px;\n  line-height: 1.2;\n}\n.question__products p {\n  flex: 0 0 auto;\n  margin: 0;\n}\n.question__products a {\n  display: inline-flex;\n  max-width: 350px;\n  margin: 5px 2px 0 0;\n  padding: 14px 16px;\n  text-decoration: none;\n  border-radius: 5px;\n  background-color: #272727;\n}\n@media (min-width: 768px) {\n  .question__products a {\n    align-items: center;\n    width: 49%;\n  }\n}\n.question__products strong {\n  display: block;\n  padding-top: 5px;\n  font-size: 19px;\n}\n.question__products del {\n  color: #acacac;\n  font-size: 12px;\n  font-weight: 400;\n  vertical-align: middle;\n}\n.question__products img {\n  display: block;\n  width: 40px;\n  height: 40px;\n  margin-right: 14px;\n  margin-bottom: 10px;\n  border-radius: 5px;\n  background: #fff;\n}\n@media (min-width: 768px) {\n  .question__products img {\n    width: 70px;\n    height: 70px;\n    margin-bottom: 0;\n  }\n}\n\n.register {\n  display: flex;\n  flex-wrap: wrap;\n}\n@media (min-width: 768px) {\n  .register {\n    flex-wrap: nowrap;\n  }\n}\n.register .form {\n  width: 100%;\n}\n\n.register__benefits {\n  flex: 1 1 auto;\n  padding: 20px 10px 20px 30px;\n  color: #fff;\n  background-color: #1c1c1c;\n}\n@media (min-width: 768px) {\n  .register__benefits {\n    flex: 0 0 350px;\n  }\n}\n@media (min-width: 992px) {\n  .register__benefits {\n    flex: 0 0 500px;\n    padding: 70px 90px;\n  }\n}\n.register__benefits h2 {\n  font-weight: 600;\n  line-height: 1.17;\n}\n@media (min-width: 768px) {\n  .register__benefits h2 {\n    font-size: 30px;\n  }\n}\n.register__benefits ul,\n.register__benefits li {\n  margin: 0;\n  padding: 0;\n  list-style-type: none;\n}\n.register__benefits li {\n  position: relative;\n  padding: 28px 0 28px 90px;\n  font-size: 19px;\n  line-height: 1.21;\n}\n.register__benefits li strong {\n  font-weight: 600;\n}\n.register__benefits img {\n  position: absolute;\n  top: 20px;\n  left: 5px;\n}\n\n.reveal {\n  display: none;\n}\n.reveal.is-visible {\n  display: block;\n}\n\n.reveal--inline.is-visible {\n  display: inline-block;\n}\n\n.reveal--flex.is-visible {\n  display: flex;\n}\n\n.shop {\n  position: relative;\n  padding: 30px 0;\n  color: #fff;\n  font-size: 16px;\n  background-color: #1c1c1c;\n}\n@media (min-width: 992px) {\n  .shop {\n    padding: 100px 0;\n    font-size: 18px;\n  }\n  .shop:before {\n    content: \"\";\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 50%;\n    display: block;\n    width: 1px;\n    background-color: #272727;\n  }\n}\n.shop h2 {\n  margin: 20px 0 12px 0;\n  font-size: 18px;\n  font-weight: 700;\n}\n@media (min-width: 768px) {\n  .shop h2 {\n    font-size: 30px;\n  }\n}\n.shop p {\n  position: relative;\n  margin-bottom: 12px;\n  padding-left: 34px;\n}\n.shop p .icon {\n  position: absolute;\n  left: 0;\n}\n@media (min-width: 992px) {\n  .shop p .icon {\n    top: 5px;\n  }\n}\n.shop img {\n  display: inline-block;\n  border-radius: 5px;\n}\n.shop .container {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n.shop .icon {\n  width: 24px;\n  margin-right: 5px;\n  color: #ffc300;\n}\n\n.shop__item {\n  position: relative;\n  width: 100%;\n  max-width: 610px;\n}\n\n.shop__item + .shop__item {\n  margin-top: 20px;\n}\n@media (min-width: 992px) {\n  .shop__item + .shop__item {\n    margin-top: 0;\n  }\n}\n\n.shop__photo {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  padding: 0;\n}\n.shop__photo picture {\n  max-width: 48%;\n}\n\np.shop__photo {\n  padding: 0;\n}\n\np.shop__link {\n  padding: 0;\n}\n@media (min-width: 992px) {\n  p.shop__link {\n    position: absolute;\n    right: 0;\n    bottom: 0;\n  }\n}\np.shop__link .icon {\n  position: relative;\n}\n\n.shop-detail {\n  position: relative;\n  margin: 20px 0;\n}\n@media (min-width: 768px) {\n  .shop-detail + .shop-detail {\n    margin-top: 80px;\n  }\n}\n\n@media (min-width: 992px) {\n  .shop-detail__photo {\n    display: flex;\n    justify-content: space-between;\n  }\n}\n.shop-detail__photo p {\n  margin: 0;\n}\n.shop-detail__photo img {\n  display: block;\n  border-radius: 5px;\n}\n\n.shop-detail__gallery {\n  display: flex;\n}\n@media (min-width: 992px) {\n  .shop-detail__gallery {\n    flex: 0 0 327px;\n    flex-direction: column;\n    justify-content: space-between;\n    padding-left: 20px;\n  }\n}\n.shop-detail__gallery picture {\n  padding: 10px 5px;\n}\n@media (min-width: 992px) {\n  .shop-detail__gallery picture {\n    padding: 0 0 10px 0;\n  }\n}\n@media (min-width: 1300px) {\n  .shop-detail__gallery picture {\n    padding-bottom: 0;\n  }\n}\n\np.shop-detail__gallery {\n  margin: 0 -5px;\n}\n@media (min-width: 992px) {\n  p.shop-detail__gallery {\n    margin: 0;\n  }\n}\n\n.shop-detail__content {\n  position: relative;\n  padding: 20px;\n  font-size: 18px;\n  background-color: #fff;\n}\n@media (min-width: 1200px) {\n  .shop-detail__content {\n    max-width: 680px;\n    margin: -140px 0 0 120px;\n    padding: 45px 65px;\n    font-size: 20px;\n  }\n}\n.shop-detail__content h2 {\n  margin-bottom: 20px;\n}\n.shop-detail__content p {\n  position: relative;\n  min-height: 35px;\n  margin: 0 0 5px 0;\n  padding-left: 35px;\n}\n.shop-detail__content .icon {\n  position: absolute;\n  left: 0;\n  width: 25px;\n  margin-right: 8px;\n  color: #ffc300;\n}\n\n.shop-detail__link {\n  margin-top: 20px;\n}\n@media (min-width: 992px) {\n  .shop-detail__link {\n    position: absolute;\n    right: 20px;\n    bottom: 40px;\n    margin: 0;\n  }\n}\n@media (min-width: 1200px) {\n  .shop-detail__link {\n    right: 90px;\n  }\n}\n\n.search {\n  position: relative;\n  display: none;\n  flex: 1 1 55%;\n  margin: 0 25px;\n}\n@media (min-width: 992px) {\n  .search {\n    display: block;\n    max-width: 630px;\n  }\n}\n@media (min-width: 1200px) {\n  .search {\n    margin: 0;\n  }\n}\n.search.is-open {\n  position: absolute;\n  top: 70px;\n  left: 0;\n  z-index: 999;\n  display: block;\n  width: 100%;\n  margin: 0;\n  padding: 10px;\n  background: #1c1c1c;\n}\n.search .form-search {\n  position: relative;\n  z-index: 103;\n}\n\n.search__detail {\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 102;\n  display: none;\n  overflow: auto;\n  max-height: calc(100vh - 70px);\n  padding: 60px 0 20px 0;\n  color: #fff;\n  border-radius: 5px;\n  background-color: #1c1c1c;\n}\n@media (min-width: 992px) {\n  .search__detail {\n    top: -10px;\n    right: -10px;\n    left: -10px;\n    max-height: calc(100vh - 108px);\n  }\n}\n.search__detail.is-open {\n  display: block;\n}\n.search__detail h2 {\n  font-size: 18px;\n  font-weight: 600;\n}\n.search__detail > p:last-child {\n  margin: 20px 0 0 0;\n}\n.search__detail::-webkit-scrollbar {\n  width: 10px;\n}\n.search__detail::-webkit-scrollbar-track {\n  border-radius: 5px;\n  background-color: #000;\n}\n.search__detail::-webkit-scrollbar-thumb {\n  border: 3px solid #000;\n  border-radius: 5px;\n  background: #ffc300;\n}\n.search__detail::-webkit-scrollbar-thumb:hover {\n  background-color: #ffc300;\n}\n\n.search__content {\n  padding: 15px 18px;\n  font-size: 13px;\n}\n.search__content h2 {\n  margin-bottom: 5px;\n}\n.search__content ul {\n  margin: 0;\n  padding: 0;\n  list-style-type: none;\n}\n.search__content li {\n  margin: 0;\n  padding: 2px 0;\n}\n.search__content a {\n  color: #fff;\n}\n.search__content a:hover, .search__content a:focus {\n  color: #ffc300;\n}\n\n.search__list {\n  position: relative;\n  padding: 15px 18px;\n  background-color: #272727;\n}\n@media (max-width: 767px) {\n  .search__list .product__price {\n    padding: 0 20px;\n  }\n}\n.search__list .product__price del {\n  display: block;\n  color: #acacac;\n}\n.search__list .product--basket.product--top {\n  margin-bottom: 10px;\n}\n.search__list .article {\n  color: #fff;\n}\n.search__list .btn {\n  white-space: nowrap;\n}\n\n.search__number {\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  margin: 0 3px;\n  padding-top: 5px;\n  font-size: 10px;\n  font-weight: 400;\n  line-height: 1;\n  text-align: center;\n  vertical-align: middle;\n  border: 1px solid #3b3b3b;\n  border-radius: 50%;\n}\n\n.search__close {\n  position: absolute;\n  top: 70px;\n  right: 15px;\n  z-index: 11;\n  overflow: hidden;\n  width: 25px;\n  height: 25px;\n  cursor: pointer;\n}\n.search__close:hover, .search__close:focus {\n  opacity: 0.7;\n}\n.search__close .icon {\n  width: 25px;\n  color: #fff;\n}\n\n.separator {\n  position: relative;\n  margin: 25px 0;\n  text-align: center;\n}\n.separator:before {\n  content: \"\";\n  position: absolute;\n  top: 15px;\n  z-index: 10;\n  display: block;\n  width: 100%;\n  max-width: 410px;\n  height: 1px;\n  background-color: #000;\n}\n.separator span {\n  position: relative;\n  z-index: 11;\n  display: inline-block;\n  padding: 6px 10px;\n  color: #000;\n  font-size: 12px;\n  font-weight: 600;\n  line-height: 1;\n  text-transform: uppercase;\n  border: 1px solid #000;\n  border-radius: 5px;\n  background-color: #fff;\n}\n.login__form .separator {\n  margin: 25px 0 10px 0;\n}\n@media (min-width: 420px) {\n  .register .separator span {\n    margin-left: -50px;\n  }\n}\n\n.separator--inverse:before {\n  background-color: #fff;\n}\n.separator--inverse span {\n  color: #fff;\n  border-color: #fff;\n  background-color: #272727;\n}\n\n.sidebar {\n  margin-top: 20px;\n}\n@media (min-width: 1200px) {\n  .sidebar {\n    flex: 0 0 450px;\n    margin-top: 0;\n    padding-left: 20px;\n  }\n}\n.sidebar > h3 {\n  margin-top: 10px;\n  font-size: 20px;\n}\n.sidebar .order-sum {\n  max-width: calc(100% - 40px);\n  margin: 20px;\n}\n.sidebar .btn--danger {\n  margin-bottom: 10px;\n}\n\n.sidebar--filter {\n  display: none;\n  width: 100%;\n  padding-left: 0;\n}\n@media (min-width: 1200px) {\n  .sidebar--filter {\n    display: block;\n    flex: 0 0 350px;\n    padding-right: 20px;\n  }\n}\n.sidebar--filter.is-open {\n  display: block;\n}\n.sidebar--filter p {\n  margin-bottom: 20px;\n}\n.sidebar--filter .form-search {\n  max-width: 260px;\n}\n\n.sidebar--order {\n  flex: 1 1 100%;\n}\n@media (min-width: 1200px) {\n  .sidebar--order {\n    flex: 0 0 480px;\n    padding-left: 20px;\n  }\n}\n@media (min-width: 1400px) {\n  .sidebar--order {\n    padding-left: 100px;\n  }\n}\n@media (max-width: 991px) {\n  .sidebar--order > h3,\n  .sidebar--order .contact-box {\n    display: none;\n  }\n}\n.sidebar--order .product__price {\n  line-height: 1.2;\n}\n\n.sidebar__show {\n  margin-top: -15px;\n}\n\n.slider {\n  position: relative;\n  display: none;\n  color: #fff;\n  background-color: #000;\n}\n@media (min-width: 992px) {\n  .slider {\n    display: block;\n  }\n}\n.slider .btn {\n  padding: 15px 33px;\n  font-size: 15px;\n}\n.slider .is-info .btn {\n  color: #fff;\n  background-color: #39d8fd;\n}\n.slider .is-info small {\n  color: #39d8fd;\n}\n.slider .is-success .btn {\n  color: #fff;\n  background-color: #008f05;\n}\n.slider .is-success small {\n  color: #008f05;\n}\n.slider .is-danger .btn {\n  color: #fff;\n  background-color: #e84747;\n}\n.slider .is-danger small {\n  color: #e84747;\n}\n\n.slider__image {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 9;\n  overflow: hidden;\n  margin: 0;\n}\n.slider__image a {\n  display: block;\n}\n.slider__image img {\n  display: block;\n  height: 100%;\n  margin-left: auto;\n  object-fit: cover;\n}\n\n.slider__content {\n  position: relative;\n  z-index: 10;\n  display: flex;\n  align-items: center;\n  flex-direction: column;\n  justify-content: center;\n  width: 50%;\n  min-height: 465px;\n  text-align: center;\n}\n\n.slider__title {\n  font-size: 55px;\n  font-weight: 700;\n  line-height: 1;\n}\n.slider__title small {\n  display: block;\n  padding-bottom: 5px;\n  color: #ffc300;\n  font-size: 22px;\n  font-weight: 600;\n}\n\n.slider__link {\n  margin: 0;\n}\n\n.sort {\n  display: flex;\n  margin: 0;\n  padding: 10px 0;\n  list-style-type: none;\n  text-transform: uppercase;\n}\n@media (min-width: 768px) {\n  .sort {\n    margin: 25px 0;\n    padding: 0;\n    border-bottom: 1px solid #dedede;\n  }\n}\n@media (max-width: 767px) {\n  .sort {\n    overflow-x: auto;\n  }\n}\n.sort li {\n  margin: 0 10px -1px 0;\n  padding: 5px;\n  border-bottom: 1px solid transparent;\n}\n@media (min-width: 768px) {\n  .sort li {\n    margin-right: 40px;\n    padding: 15px 0;\n  }\n}\n.sort a {\n  color: #585858;\n  white-space: nowrap;\n  text-decoration: none;\n}\n.sort a:hover, .sort a:focus {\n  color: #000;\n}\n.sort .is-active {\n  font-weight: 700;\n}\n@media (min-width: 768px) {\n  .sort .is-active {\n    border-color: #ffc300;\n  }\n}\n.sort .is-active a {\n  color: #000;\n}\n\n@media (min-width: 768px) {\n  .sort--center {\n    justify-content: center;\n  }\n  .sort--center li {\n    margin: 0 20px -1px 20px;\n  }\n}\n\n.splide {\n  position: relative;\n}\n.splide .product {\n  width: auto;\n  height: 100%;\n  margin: 0 12px;\n}\n\n.splide--main {\n  margin: 0;\n}\n.splide--main .splide__arrow {\n  background-image: url(../img/pagination-next-inverse.svg);\n}\n.splide--main .splide__arrow--prev {\n  left: 20px;\n}\n@media (min-width: 1600px) {\n  .splide--main .splide__arrow--prev {\n    left: 80px;\n  }\n}\n.splide--main .splide__arrow--next {\n  right: 20px;\n}\n@media (min-width: 1600px) {\n  .splide--main .splide__arrow--next {\n    right: 80px;\n  }\n}\n\n.splide--products {\n  margin: 0 20px;\n}\n@media (min-width: 1600px) {\n  .splide--products {\n    margin: 0 -12px;\n  }\n}\n\n.splide__arrow {\n  position: absolute;\n  top: 50%;\n  z-index: 11;\n  overflow: hidden;\n  width: 34px;\n  height: 34px;\n  text-indent: 9999px;\n  border: none;\n  background: transparent url(../img/pagination-next.svg) center center no-repeat;\n  background-size: 34px 34px;\n  transform: translateY(-50%);\n}\n@media (min-width: 1600px) {\n  .splide__arrow {\n    width: 64px;\n    height: 64px;\n    background-size: 64px 64px;\n  }\n}\n.splide__arrow:hover, .splide__arrow:focus {\n  opacity: 0.7;\n}\n.splide__arrow:disabled {\n  opacity: 0.2;\n}\n\n.splide__arrow--prev {\n  left: -27px;\n  margin-top: -15px;\n  transform: rotate(-180deg);\n}\n@media (min-width: 1600px) {\n  .splide__arrow--prev {\n    left: -80px;\n  }\n}\n\n.splide__arrow--next {\n  right: -27px;\n}\n@media (min-width: 1600px) {\n  .splide__arrow--next {\n    right: -80px;\n  }\n}\n\n.splide__pagination {\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  z-index: 11;\n  margin: 0;\n  padding: 7px 10px;\n  border-top-left-radius: 5px;\n  border-top-right-radius: 5px;\n  background-color: #131313;\n  transform: translateX(-50%);\n}\n.splide__pagination button {\n  padding: 0 10px;\n  color: #fff;\n  font-size: 15px;\n  font-weight: 600;\n  border: none;\n  background: transparent;\n  opacity: 0.3;\n}\n.splide__pagination button.is-active {\n  opacity: 1;\n}\n\n.star {\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  text-decoration: none;\n  background-image: url(../img/star.svg);\n  background-repeat: no-repeat;\n  background-position: left top;\n  transition: all 0.3s;\n}\n\na.star:hover, a.star:focus {\n  background-position: left top;\n}\n\n.star--75 {\n  background-position: left -48px;\n}\n\n.star--50 {\n  background-position: left -72px;\n}\n\n.star--25 {\n  background-position: left -96px;\n}\n\n.star--0 {\n  background-position: left -24px;\n}\n\n.store {\n  color: #585858;\n  font-size: 11px;\n  text-decoration: none;\n}\n.store strong {\n  font-weight: 600;\n}\n.store .icon {\n  width: 15px;\n  margin: -3px 3px 0 3px;\n  color: #585858;\n}\n.store.is-success {\n  color: #008f05;\n}\n.store.is-success .icon {\n  color: #008f05;\n}\n.store.is-danger {\n  color: #e84747;\n}\n.store.is-danger .icon {\n  color: #e84747;\n}\n.store.is-info {\n  color: #e6b000;\n}\n.store.is-info .icon {\n  color: #e6b000;\n}\n\n.table,\n.table__wrapper table {\n  width: 100%;\n  border: 0;\n}\n.table th,\n.table td,\n.table__wrapper table th,\n.table__wrapper table td {\n  padding: 8px 15px;\n  line-height: 1.3;\n  text-align: left;\n  vertical-align: top;\n  border-top: 0;\n  border-right: 0;\n  border-bottom: 1px solid #dfdfdf;\n  border-left: 0;\n}\n.table tr:last-child th,\n.table tr:last-child td,\n.table__wrapper table tr:last-child th,\n.table__wrapper table tr:last-child td {\n  border-bottom: 0;\n}\n.table th,\n.table__wrapper table th {\n  font-size: 18px;\n  font-weight: 700;\n}\n\n.table__wrapper {\n  overflow-x: auto;\n  max-width: 100%;\n  margin: 0 0 40px 0;\n  padding: 10px 20px 30px 20px;\n  background-color: #fff;\n}\n\n.tag {\n  display: inline-block;\n  margin: 0 4px 4px 0;\n  padding: 6px 10px;\n  color: #000;\n  font-size: 10px;\n  font-weight: 600;\n  line-height: 1;\n  text-transform: uppercase;\n  border-radius: 5px;\n  background-color: #ffc300;\n}\n.tag em {\n  display: block;\n  padding-top: 3px;\n  font-style: normal;\n}\n.tag .icon {\n  width: 10px;\n  margin: -2px 2px 0 -2px;\n}\n\n.tag--big {\n  padding: 8px 16px;\n  font-size: 13px;\n}\n.tag--big .icon {\n  width: 14px;\n  margin: -2px 2px 0 -4px;\n}\n\n.tag--gold {\n  color: #fff;\n  background-image: linear-gradient(to right, #ffc300 0%, #ff9900 100%);\n}\n.tag--gold .icon {\n  color: #fff;\n}\n\n.tag--delivery {\n  background-color: #ffe89c;\n}\n\n.tag--action {\n  color: #fff;\n  background-color: #e84747;\n}\n.tag--action .icon {\n  color: #fff;\n}\n\n.tag--vegan {\n  background-color: #cdf1ce;\n}\n.tag--vegan .icon {\n  color: #008f05;\n}\n\n.tag--new {\n  background-color: #c7e6fb;\n}\n\n.topic {\n  padding: 25px 0;\n  color: #fff;\n  background-color: #272727;\n}\n@media (min-width: 768px) {\n  .topic {\n    padding: 50px 0;\n  }\n}\n@media (min-width: 768px) {\n  .topic .container {\n    display: flex;\n  }\n}\n\n.topic__info {\n  display: flex;\n  justify-content: space-between;\n  padding-bottom: 15px;\n}\n@media (min-width: 768px) {\n  .topic__info {\n    flex: 0 0 auto;\n    flex-direction: column;\n    padding-right: 25px;\n    padding-bottom: 0;\n  }\n}\n.topic__info h2 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n}\n.topic__info p {\n  margin: 0;\n}\n.topic__info a {\n  color: #fff;\n  font-size: 15px;\n}\n\n.topic__content {\n  flex: 1 1 auto;\n}\n.topic__content .badge {\n  margin: 2px 0;\n}\n\n.warn {\n  padding: 7px 10px 5px 10px;\n  font-size: 14px;\n  font-weight: 500;\n  line-height: 1.2;\n  background-color: #ffc300;\n}\n.warn strong {\n  font-weight: 600;\n}\n.warn .icon {\n  display: none;\n  width: 22px;\n  margin-top: -2px;\n  margin-right: 7px;\n  color: #fff;\n}\n@media (min-width: 768px) {\n  .warn .icon {\n    display: inline-block;\n  }\n}\n.warn.is-success {\n  color: #fff;\n  background-color: #008f05;\n}\n.warn.is-danger {\n  color: #fff;\n  background-color: #e84747;\n}\n\n.why {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n@media (min-width: 992px) {\n  .why {\n    align-items: center;\n    justify-content: space-between;\n  }\n}\n.why h2 {\n  width: 100%;\n  text-align: center;\n}\n@media (min-width: 992px) {\n  .why h2 {\n    width: auto;\n    margin: 0 30px 0 0;\n    line-height: 1.2;\n  }\n}\n.why p {\n  width: 50%;\n  margin: 10px 0;\n  padding: 5px;\n  font-size: 14px;\n  line-height: 1.22;\n  text-align: center;\n}\n@media (min-width: 992px) {\n  .why p {\n    width: auto;\n    margin: 0;\n    font-size: 18px;\n  }\n}\n.why img {\n  margin-bottom: 10px;\n}\n.why strong {\n  display: block;\n  font-weight: 600;\n}\n\n.product {\n  display: flex;\n  flex-direction: column;\n  flex-wrap: wrap;\n  width: 100%;\n  padding: 17px;\n  background-color: #fff;\n}\n@media (min-width: 768px) {\n  .product {\n    flex-wrap: nowrap;\n  }\n}\n.product h3 {\n  flex: 1 1 auto;\n  margin-bottom: 10px;\n  font-size: 16px;\n}\n.product h3 a {\n  text-decoration: none;\n}\n.product p {\n  margin: 0;\n}\n\n.product--top {\n  align-items: center;\n  flex-direction: row;\n  margin-top: 8px;\n  padding: 10px;\n}\n@media (min-width: 576px) {\n  .product--top {\n    padding-right: 25px;\n  }\n}\n.product--top h3 {\n  margin-bottom: 0;\n}\n.product--top .product__image {\n  flex: 0 0 80px;\n  padding: 0;\n}\n.product--top .product__image img {\n  display: block;\n}\n.product--top .product__content {\n  flex: 1 1 calc(100% - 48px);\n  padding: 0 0 15px 0;\n}\n@media (min-width: 450px) {\n  .product--top .product__content {\n    padding-left: 10px;\n  }\n}\n@media (min-width: 576px) {\n  .product--top .product__content {\n    padding: 0 20px 0 10px;\n  }\n}\n@media (min-width: 768px) {\n  .product--top .product__content {\n    flex: 1 1 auto;\n  }\n}\n.product--top .product__content p {\n  max-width: 400px;\n}\n.product--top .product__meta {\n  margin: 0;\n  padding: 0;\n}\n@media (min-width: 768px) {\n  .product--top .product__meta {\n    text-align: right;\n  }\n}\n.product--top .product__meta .btn {\n  margin-left: 26px;\n}\n\n.product--side {\n  position: relative;\n  flex-wrap: wrap;\n  min-height: 150px;\n}\n@media (min-width: 992px) {\n  .product--side {\n    padding-left: 140px;\n  }\n}\n@media (min-width: 992px) {\n  .product--side .product__image {\n    position: absolute;\n    left: 15px;\n  }\n}\n.product--side .product__meta {\n  width: 100%;\n  text-align: left;\n}\n\n.product--sum {\n  flex-wrap: nowrap;\n  min-height: 60px;\n  padding: 10px 10px 10px 5px;\n}\n.product--sum .product__content {\n  padding-left: 0;\n}\n.product--sum .product__content h3 {\n  margin: 0;\n  font-size: 15px;\n  font-weight: 500;\n}\n.product--sum .product__price {\n  font-size: 15px;\n}\n.product--sum .product__meta {\n  width: auto;\n}\n\n.product--basket {\n  color: #fff;\n  background: transparent;\n}\n.product--basket.product--top {\n  margin: 0 0 15px 0;\n  padding: 0;\n}\n.product--basket.product--top .product__image {\n  overflow: hidden;\n  flex: 0 0 50px;\n  border-radius: 5px;\n}\n.product--basket.product--top .product__content {\n  padding: 0 0 0 20px;\n}\n.product--basket h3 {\n  font-size: 15px;\n}\n.product--basket h3 a {\n  color: #fff;\n}\n.product--basket .product__price {\n  color: #fff;\n}\n.product--basket .product__count input {\n  color: #fff;\n}\n.product--basket .product__count .icon {\n  margin-right: 0;\n  color: #fff;\n}\n\n.product--order .product__price {\n  margin-left: auto;\n}\n\n.product__top {\n  margin: 0;\n  padding: 0 10px 0 5px;\n  color: #ffc300;\n  font-size: 30px;\n  font-weight: 700;\n}\n.product__top--2 {\n  color: #ffda61;\n}\n.product__top--3 {\n  color: #ffe9a1;\n}\n\n.product__image {\n  position: relative;\n  padding: 30px 0 20px 0;\n  text-align: center;\n}\n.product__image a {\n  display: block;\n}\n@media (min-width: 768px) {\n  .product__image a:hover img, .product__image a:focus img {\n    transform: scale(1.1);\n  }\n  .product__image a:hover .product__flavour, .product__image a:focus .product__flavour {\n    transform: scale(1.2);\n  }\n}\n.product__image img {\n  display: inline-block;\n  margin: 0 auto;\n  transition: all 0.2s;\n}\n\n.product__image--small img {\n  height: 40px;\n}\n\n.product__flavour {\n  position: absolute;\n  right: 4px;\n  bottom: -3px;\n  display: block;\n  overflow: hidden;\n  width: 26px;\n  height: 26px;\n  padding: 2px;\n  border: 1px solid #ffc300;\n  border-radius: 50%;\n  background-color: #fff;\n  transition: all 0.2s;\n}\n@media (min-width: 576px) {\n  .product__flavour {\n    right: -3px;\n  }\n}\n.product__flavour img {\n  width: 100%;\n  height: auto;\n}\n\n.product__tags {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 120px;\n  line-height: 1;\n  text-align: left;\n}\n\n.product__discount {\n  position: absolute;\n  bottom: 15px;\n  left: 0;\n}\n\n.product__fav {\n  position: absolute;\n  top: 0;\n  right: 0;\n}\n.product__fav .icon {\n  width: 18px;\n  color: #cecece;\n}\n.product__fav .icon:hover, .product__fav .icon:focus {\n  color: #e84747;\n}\n.product__fav .icon--heart-full {\n  color: #e84747;\n}\n\n.product__description {\n  color: #585858;\n  font-size: 14px;\n  line-height: 1.58;\n}\n@media (min-width: 1200px) {\n  .col--4 .product__description {\n    min-height: 67px;\n  }\n}\n\n.product__meta {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-top: auto;\n  padding-top: 15px;\n}\n@media (max-width: 767px) {\n  .product__meta {\n    width: 100%;\n  }\n}\n.product__meta .discount {\n  margin-right: 20px;\n}\n.product__meta .btn {\n  white-space: nowrap;\n}\n\n.product__price {\n  color: #000;\n  font-size: 19px;\n  line-height: 1;\n}\n@media (min-width: 992px) {\n  .product__price {\n    white-space: nowrap;\n  }\n}\n.product__price strong,\n.product__price del {\n  white-space: nowrap;\n}\n.product__price del {\n  display: inline-block;\n  padding-left: 3px;\n  color: #585858;\n  font-size: 12px;\n}\n.product__price + .product__price {\n  padding-left: 15px;\n}\n\n.product__count {\n  padding: 0 15px;\n  white-space: nowrap;\n}\n.product__count input {\n  width: 28px;\n  padding: 5px 0;\n  text-align: center;\n  border: none;\n  background: transparent;\n}\n.product__count .icon {\n  width: 24px;\n  margin-top: -3px;\n  cursor: pointer;\n  user-select: none;\n}\n@media (min-width: 576px) {\n  .product__count .icon {\n    width: 20px;\n  }\n}\n.product__count .icon:hover, .product__count .icon:focus {\n  opacity: 0.6;\n}\n\n.product__remove {\n  margin-left: 15px;\n  text-decoration: none;\n}\n.product__remove:hover .icon, .product__remove:focus .icon {\n  color: #e84747;\n}\n.product__remove .icon {\n  width: 14px;\n  color: #9e9e9e;\n}\n\n.product__links {\n  margin: 15px 0;\n}\n.product__links .btn {\n  margin: 3px 0;\n}\n\n.product-detail {\n  min-height: 410px;\n  padding: 35px 0 0 0;\n}\n@media (min-width: 1200px) {\n  .product-detail .container {\n    padding-left: 642px;\n  }\n}\n@media (min-width: 576px) {\n  .product-detail .product {\n    padding-right: 15px;\n  }\n}\n.product-detail .product__image {\n  flex-basis: 48px;\n}\n.product-detail .product__meta .btn {\n  margin: 0;\n}\n@media (max-width: 767px) {\n  .product-detail .product__meta .discount {\n    display: none;\n  }\n}\n.product-detail .store {\n  font-size: 13px;\n}\n\n.product-detail__links {\n  margin: 0;\n}\n.product-detail__links a {\n  position: relative;\n  display: block;\n  margin: -60px 0 0 0;\n  padding: 45px 0 15px 0;\n  text-align: center;\n  background: linear-gradient(0deg, rgb(245, 245, 245) 40%, rgba(245, 245, 245, 0) 100%);\n}\n\n.product-helper {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  max-width: 660px;\n  margin: 20px auto;\n  padding: 0;\n  list-style-type: none;\n  line-height: 1;\n}\n.product-helper li {\n  margin: 0;\n  padding: 5px;\n}\n.product-helper a {\n  text-decoration: none;\n}\n.product-helper .icon {\n  width: 25px;\n  margin-right: 6px;\n  color: #cbcbcb;\n}\n\n.product-photo {\n  position: relative;\n  width: 100%;\n  max-width: 560px;\n  margin-top: 25px;\n}\n@media (min-width: 1200px) {\n  .product-photo {\n    position: absolute;\n    top: 45px;\n    left: 12px;\n    z-index: 11;\n    margin-top: 0;\n  }\n}\n.product-photo img {\n  display: block;\n  transition: all 0.2s;\n}\n\n.product-photo__main {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  justify-content: center;\n  width: 100%;\n  margin: 0;\n  background-color: #fff;\n}\n@media (min-width: 1200px) {\n  .product-photo__main {\n    height: 560px;\n  }\n}\n@media (min-width: 768px) {\n  .product-photo__main a:hover img, .product-photo__main a:focus img {\n    transform: scale(1.05);\n  }\n}\n\n.product-photo__tags,\n.product-photo__fav,\n.product-photo__stars {\n  position: absolute;\n  z-index: 11;\n  margin: 0;\n}\n\n.product-photo__tags {\n  top: 15px;\n  left: 20px;\n  width: 120px;\n}\n\n.product-photo__fav {\n  top: 15px;\n  right: 18px;\n}\n.product-photo__fav .icon {\n  width: 20px;\n}\n.product-photo__fav .icon--heart-full {\n  color: #e84747;\n}\n\n.product-photo__stars {\n  top: 50px;\n  right: 16px;\n  width: 24px;\n}\n\n.product-photo__gallery {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  margin: 8px -4px;\n}\n@media (min-width: 768px) {\n  .product-photo__gallery {\n    flex-wrap: nowrap;\n  }\n}\n.product-photo__gallery a {\n  display: flex;\n  align-items: center;\n  flex-direction: column;\n  justify-content: center;\n  width: calc(33.33333% - 8px);\n  height: 105px;\n  margin: 4px;\n  background-color: #fff;\n}\n@media (min-width: 768px) {\n  .product-photo__gallery a {\n    width: calc(25% - 8px);\n  }\n}\n@media (min-width: 768px) {\n  .product-photo__gallery a:hover img, .product-photo__gallery a:focus img {\n    transform: scale(1.1);\n  }\n}\n\n.product-photo__more {\n  flex-wrap: wrap;\n  font-size: 14px;\n  font-weight: 700;\n}\n\n.order-benefits {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 40px;\n}\n@media (min-width: 768px) {\n  .order-benefits {\n    flex-wrap: nowrap;\n  }\n}\n.order-benefits .hey {\n  display: none;\n  flex: 0 0 290px;\n  margin: 0;\n}\n@media (min-width: 768px) {\n  .order-benefits .hey {\n    display: flex;\n  }\n}\n\n.order-benefits--modal {\n  margin-top: 30px;\n}\n.order-benefits--modal .order-benefits__info {\n  display: flex;\n  justify-content: space-between;\n  margin: 0;\n  padding: 0;\n}\n.order-benefits--modal .order-benefits__col {\n  width: 48%;\n}\n\n.order-benefits__info {\n  width: 100%;\n  padding: 20px 25px 30px 25px;\n  color: #fff;\n  font-size: 18px;\n  font-weight: 600;\n  background-color: #272727;\n}\n@media (min-width: 768px) {\n  .order-benefits__info {\n    margin-right: 25px;\n  }\n}\n.order-benefits__info small {\n  margin: auto 0 0 auto;\n  padding-left: 5px;\n  font-size: 13px;\n  font-weight: 400;\n}\n.order-benefits__info p {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 0 2px 0;\n}\n.order-benefits__info p + p {\n  margin-top: 10px;\n}\n.order-benefits__info .icon {\n  width: 18px;\n  margin-right: 4px;\n  color: #ffc300;\n}\n.order-benefits__info .icon--car {\n  color: #ffe89c;\n}\n.order-benefits__info .progress {\n  margin: 2px 0 10px 0;\n}\n\n.order-progress {\n  padding: 20px 0;\n  background-color: #1c1c1c;\n}\n@media (min-width: 768px) {\n  .order-progress {\n    padding: 40px 0;\n  }\n}\n@media (min-width: 992px) {\n  .order-progress {\n    margin-top: -21px;\n  }\n}\n@media (min-width: 1200px) {\n  .order-progress {\n    margin-top: -64px;\n  }\n}\n.order-progress ol {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  justify-content: center;\n  margin: 0;\n  padding: 0;\n  list-style-type: none;\n  color: #9e9e9e;\n  font-size: 20px;\n}\n.order-progress ol li {\n  position: relative;\n  margin: 0;\n  padding: 0 15px 0 0;\n}\n@media (min-width: 768px) {\n  .order-progress ol li {\n    padding: 0 35px;\n  }\n}\n@media (min-width: 768px) {\n  .order-progress ol li + li:before {\n    content: \"\";\n    position: absolute;\n    top: 50%;\n    left: 0;\n    width: 5px;\n    height: 8px;\n    margin-top: -5px;\n    background: url(../img/arrow.svg) center center no-repeat;\n  }\n}\n.order-progress ol a {\n  color: #fff;\n  text-decoration: none;\n}\n.order-progress ol a:hover, .order-progress ol a:focus {\n  color: #9e9e9e;\n}\n.order-progress ol a .order-progress__number {\n  border-color: #fff;\n}\n.order-progress ol .is-active {\n  color: #fff;\n  font-weight: 700;\n}\n.order-progress ol .is-active a {\n  color: #fff;\n}\n.order-progress ol .is-active .order-progress__number {\n  color: #ffc300;\n  border-color: #fff;\n}\n\n.order-progress__number {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 30px;\n  height: 30px;\n  margin-right: 5px;\n  padding-left: 2px;\n  font-size: 16px;\n  font-weight: 700;\n  line-height: 1;\n  border: 2px solid #9e9e9e;\n  border-radius: 50%;\n}\n@media (min-width: 768px) {\n  .order-progress__number {\n    width: 46px;\n    height: 46px;\n    margin-right: 15px;\n  }\n}\n\n.order-steps {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  margin-top: 20px;\n  padding: 20px;\n  background: #fff;\n}\n@media (min-width: 992px) {\n  .order-steps {\n    flex-wrap: nowrap;\n    margin-top: 40px;\n    padding: 45px 55px;\n  }\n}\n.order-steps p {\n  margin: 0;\n}\n.order-steps .link {\n  display: inline-block;\n  margin-bottom: 15px;\n}\n@media (min-width: 768px) {\n  .order-steps .link {\n    margin-bottom: 0;\n  }\n}\n.order-steps .btn {\n  margin-top: 10px;\n}\n@media (min-width: 992px) {\n  .order-steps .btn {\n    margin-top: 0;\n    padding: 22px 25px 22px 28px;\n    white-space: nowrap;\n  }\n}\n\n.order-steps__discount {\n  width: 100%;\n}\n@media (min-width: 768px) {\n  .order-steps__discount {\n    flex: 1 1 1px;\n    padding: 0 25px;\n    text-align: center;\n  }\n}\n.order-steps__discount .reveal--inline.is-visible {\n  display: block;\n  margin: 5px 0 0 0;\n}\n@media (min-width: 1200px) {\n  .order-steps__discount .reveal--inline.is-visible {\n    display: inline-block;\n    min-width: 300px;\n    margin: 0 0 0 20px;\n  }\n}\n.order-steps__discount .form-checkbox {\n  margin-bottom: 0;\n}\n\n.order-steps__note {\n  display: inline-block;\n  max-width: 310px;\n  margin-top: 10px;\n  color: #8b8b8b;\n  font-size: 14px;\n  line-height: 1.3;\n}\n\n.order-sum {\n  width: 100%;\n  max-width: 290px;\n  margin: 20px 0 0 auto;\n  font-size: 24px;\n}\n@media (min-width: 768px) {\n  .order-sum {\n    margin-right: 25px;\n  }\n}\n.order-sum p {\n  display: flex;\n  justify-content: space-between;\n  margin: 10px 0 0 0;\n  line-height: 1;\n}\n.order-sum strong {\n  white-space: nowrap;\n}\n\n@media (min-width: 768px) {\n  .order-sum--pushed {\n    margin-right: 56px;\n  }\n}\n\n.order-sum__note {\n  color: #8b8b8b;\n  font-size: 14px;\n}\n\n@media (min-width: 768px) {\n  .order-summary {\n    margin: 15px 0 70px 0;\n  }\n}\n.order-summary h3 {\n  margin-bottom: 10px;\n  font-size: 20px;\n}\n@media (max-width: 767px) {\n  .order-summary p {\n    margin-bottom: 0;\n  }\n}\n@media (min-width: 768px) {\n  .order-summary .row + .row {\n    margin-top: 30px;\n  }\n}\n.order-summary .order-sum {\n  max-width: 340px;\n  margin-top: auto;\n  margin-bottom: auto;\n}\n\n.form {\n  padding: 20px;\n  color: #000;\n  background-color: #fff;\n}\n@media (min-width: 768px) {\n  .form {\n    padding: 40px;\n  }\n}\n.form h2,\n.form p {\n  max-width: 460px;\n  margin-right: auto;\n  margin-left: auto;\n}\n.form h2:last-child,\n.form p:last-child {\n  margin-bottom: 0;\n}\n.form p {\n  line-height: 1.8;\n}\n.form input,\n.form select,\n.form textarea {\n  width: 100%;\n  max-width: 410px;\n  margin-bottom: 3px;\n}\n.form + h2 {\n  margin-top: 40px;\n}\n\n.form--full h2,\n.form--full p {\n  max-width: 100%;\n}\n@media (min-width: 576px) {\n  .form--full input,\n  .form--full select,\n  .form--full textarea {\n    width: auto;\n    max-width: 100%;\n    margin-right: 15px;\n  }\n}\n\n.form--modal {\n  padding: 0;\n  background: transparent;\n}\n\n.form__required {\n  color: #e84747;\n  font-weight: 600;\n}\n\n.form__agree .form-checkbox,\n.form__agree .form-radio {\n  font-size: 16px;\n}\n\n.form__note {\n  margin-top: -10px;\n  color: #9e9e9e;\n  font-size: 16px;\n}\n\np.form__note {\n  line-height: 1.5;\n}\n\n.form__submit {\n  margin-bottom: 0;\n}\n\n.form__helper {\n  display: inline-block;\n  float: right;\n  margin-left: auto;\n  padding-top: 5px;\n  font-size: 13px;\n}\n.form__helper:hover, .form__helper:focus {\n  color: #bcbcbc;\n}\n\na.form__helper {\n  text-decoration: underline;\n}\n\n.form-checkbox {\n  position: relative;\n  display: inline-block;\n  min-height: 30px;\n  margin-bottom: 3px;\n  padding-left: 40px;\n  font-size: 17px;\n  cursor: pointer;\n}\n.form-checkbox input {\n  position: absolute;\n  width: auto;\n  opacity: 0;\n}\n.form-checkbox input:checked ~ .form-checkbox__checker {\n  background: #1c1c1c url(../img/checkbox.svg) center center no-repeat;\n}\n.form-checkbox input:checked ~ .form-checkbox__label {\n  font-weight: 600;\n}\n.form-checkbox input:disabled ~ .form-checkbox__checker {\n  border-color: #989898;\n}\n.form-checkbox input:disabled ~ .form-checkbox__label {\n  color: #989898;\n}\n.form-checkbox a.js-nopass {\n  text-decoration: none;\n  cursor: default;\n  pointer-events: none;\n}\n.form-checkbox a.js-nopass:hover, .form-checkbox a.js-nopass:focus {\n  color: inherit;\n}\n\n.form-checkbox__checker {\n  position: absolute;\n  top: 3px;\n  left: 3px;\n  display: inline-block;\n  width: 23px;\n  height: 23px;\n  border: 2px solid #000;\n  background-color: transparent;\n}\n\n.form-discount {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  max-width: 330px;\n}\n.form-discount input {\n  width: 100%;\n  padding-right: 40px;\n  font-size: 15px;\n}\n.form-discount input::placeholder {\n  color: #000;\n  opacity: 1;\n}\n.form-discount input:-ms-input-placeholder {\n  color: #000;\n}\n.form-discount input::-ms-input-placeholder {\n  color: #000;\n}\n.form-discount button,\n.form-discount input[type=submit] {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  width: 100%;\n  max-width: 100px;\n  height: 40px;\n  color: #fff;\n  font-size: 13px;\n  font-weight: 600;\n  text-transform: uppercase;\n  border: none;\n  border-radius: 5px;\n  background-color: #000;\n}\n.form-discount button:hover, .form-discount button:focus,\n.form-discount input[type=submit]:hover,\n.form-discount input[type=submit]:focus {\n  background-color: #585858;\n}\n\n.form-radio {\n  position: relative;\n  display: inline-block;\n  min-height: 24px;\n  padding-left: 30px;\n  font-size: 17px;\n  cursor: pointer;\n}\n.form-radio.has-iframe {\n  padding-bottom: 300px;\n}\n.form-radio input {\n  position: absolute;\n  opacity: 0;\n}\n.form-radio input:checked ~ .form-radio__checker:before {\n  background-color: #ffc300;\n}\n.form-radio input:checked ~ .form-radio__price:before {\n  border-color: #000;\n}\n.form-radio input:checked ~ .form-radio__label {\n  font-weight: 600;\n}\n.form-radio input:checked ~ .form-radio__label small {\n  font-weight: 400;\n}\n.form-radio input:disabled ~ .form-radio__checker {\n  border-color: #989898;\n}\n.form-radio input:disabled ~ .form-radio__label {\n  color: #989898;\n}\n.form-radio small {\n  display: block;\n  padding-top: 2px;\n  color: #000;\n  font-size: 12px;\n}\n\n.form-radio--delivery {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  margin-top: 8px;\n  padding: 10px 10px 10px 40px;\n  background-color: #fff;\n}\n@media (min-width: 576px) {\n  .form-radio--delivery {\n    flex-wrap: nowrap;\n    padding: 14px 20px 14px 60px;\n  }\n}\n.form-radio--delivery .form-radio__label {\n  width: 100%;\n  padding: 10px 0;\n  line-height: 1.3;\n}\n@media (min-width: 576px) {\n  .form-radio--delivery .form-radio__label {\n    padding: 0 20px;\n  }\n}\n.form-radio--delivery .form-radio__checker {\n  top: 28px;\n  left: 10px;\n}\n@media (min-width: 576px) {\n  .form-radio--delivery .form-radio__checker {\n    top: 50%;\n    left: 20px;\n    margin-top: -10px;\n  }\n}\n\n.form-radio__checker {\n  position: absolute;\n  top: 5px;\n  left: 5px;\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  border: 2px solid #000;\n  border-radius: 50%;\n  background-color: transparent;\n}\n.form-radio__checker:before {\n  content: \"\";\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  margin: 4px 0 0 4px;\n  border-radius: 50%;\n  background-color: transparent;\n}\n\n.form-radio__icon {\n  flex: 0 0 80px;\n}\n.form-radio__icon img {\n  display: block;\n}\n@media (min-width: 576px) {\n  .form-radio__icon img {\n    margin: 0 auto;\n  }\n}\n\n.form-radio__price {\n  font-size: 19px;\n  white-space: nowrap;\n}\n.form-radio__price:before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  display: block;\n  border: 2px solid transparent;\n  pointer-events: none;\n}\n\n.form-radio__iframe {\n  display: none;\n  max-width: 580px;\n  margin: -2px auto 0 auto;\n  border: 2px solid #000;\n  background: #fff;\n}\n.form-radio__iframe iframe {\n  display: block;\n  width: 100%;\n  height: 600px;\n  border: none;\n}\n.form-radio__iframe.is-visible {\n  display: block;\n}\n\n.form-range {\n  max-width: 260px;\n}\n.form-range p {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-top: 20px;\n}\n.form-range input {\n  max-width: 90px;\n  padding-right: 5px;\n  padding-left: 5px;\n  font-size: 15px;\n  text-align: center;\n}\n\n.form-search {\n  position: relative;\n}\n.form-search input {\n  width: 100%;\n  padding-right: 40px;\n}\n.form-search input::placeholder {\n  color: #000;\n  font-size: 15px;\n  opacity: 1;\n}\n.form-search input:-ms-input-placeholder {\n  color: #000;\n  font-size: 15px;\n}\n.form-search input::-ms-input-placeholder {\n  color: #000;\n  font-size: 15px;\n}\n.form-search button {\n  position: absolute;\n  width: 50px;\n  height: 50px;\n  margin: 0 0 0 -50px;\n  border: none;\n  background: transparent;\n}\n.form-search .icon {\n  width: 20px;\n}\n\n.center {\n  text-align: center;\n}\n\n.left {\n  text-align: left;\n}\n\n.right {\n  text-align: right;\n}\n\n.nowrap {\n  white-space: nowrap;\n}\n\n.fleft {\n  float: left;\n}\n\n.fright {\n  float: right;\n}\n\n.cls {\n  clear: both;\n}\n\n.cf:before,\n.cf:after {\n  content: \" \";\n  display: table;\n}\n\n.cf:after {\n  clear: both;\n}\n\n.cf {\n  *zoom: 1;\n}\n\n@media print {\n  * {\n    font-family: sans-serif !important;\n    color: #000000 !important;\n    background: #ffffff !important;\n    text-shadow: none !important;\n    box-shadow: none !important;\n    border: none !important;\n    width: auto !important;\n    height: auto !important;\n    padding: inherit !important;\n    margin: inherit !important;\n    max-width: none !important;\n    position: relative !important;\n    min-height: 1px !important;\n    top: inherit !important;\n    bottom: inherit !important;\n    left: inherit !important;\n    right: inherit !important;\n  }\n  body {\n    width: 100% !important;\n    margin: 0px !important;\n    padding: 0px !important;\n    line-height: 1.4 !important;\n    word-spacing: 1.1pt !important;\n    letter-spacing: 0.2pt !important;\n    font-family: sans-serif !important;\n    color: #000000 !important;\n    background: none !important;\n    font-size: 12pt !important;\n  }\n  h1, h2, h3, h4 {\n    clear: both !important;\n    margin: 10px 0 !important;\n  }\n  h1 {\n    font-size: 19pt !important;\n  }\n  h2 {\n    font-size: 17pt !important;\n  }\n  h3 {\n    font-size: 15pt !important;\n  }\n  h4 {\n    font-size: 12pt !important;\n  }\n  img {\n    margin: 1em 1.5em 1.5em 0em !important;\n  }\n  ul, ol {\n    padding-left: 20px !important;\n    list-style-type: inherit !important;\n  }\n  li {\n    padding: inherit !important;\n    padding-left: 10px !important;\n  }\n  a img {\n    border: none !important;\n  }\n  a, a:link, a:visited, a:hover, a:active, a:focus {\n    text-decoration: none !important;\n    color: #000000 !important;\n  }\n  table {\n    margin: 1px !important;\n    text-align: left !important;\n  }\n  th {\n    border-bottom: 1px solid #000000 !important;\n    font-weight: bold !important;\n  }\n  td {\n    border-bottom: 1px solid #000000 !important;\n  }\n  th, td {\n    padding: 4px 10px 4px 0px !important;\n  }\n  tr {\n    page-break-inside: avoid !important;\n  }\n  .no, object, .noprint, nav, .nav, iframe, form, .form, button, .btn, .icon {\n    display: none !important;\n  }\n  .row {\n    display: block !important;\n    margin: 0 !important;\n  }\n}", "// You can customize Simplelightbox with the following variables:\r\n\r\n$sl-font-family: Arial, Baskerville, monospace !default;\r\n$sl-overlay-background: #fff !default;\r\n$sl-overlay-opacity: 0.7 !default;\r\n$sl-navigation-color: #000 !default;\r\n$sl-caption-color: #fff !default;\r\n$sl-caption-background: rgba(0, 0, 0, 0.8) !default;\r\n\r\n$sl-counter-fontsize: 1rem !default;\r\n$sl-caption-fontsize: 1rem !default;\r\n$sl-close-fontsize: 3rem !default;\r\n\r\n$sl-breakpoint-medium: 35.5em !default; // 568px, when 1em == 16px\r\n$sl-breakpoint-large:\t50em !default;\t // 800px, when 1em == 16px\r\n\r\n$sl-arrow-fontsize-small:\t2rem !default;\r\n$sl-arrow-fontsize-medium: 3rem !default;\r\n$sl-arrow-fontsize-large:\t3rem !default;\r\n$sl-img-border-small:\t0 none !default;\r\n$sl-img-border-medium: 0 none !default;\r\n$sl-img-border-large:\t0 none !default;\r\n$sl-iframe-border-small:\t0 none !default;\r\n$sl-iframe-border-medium: 0 none !default;\r\n$sl-iframe-border-large:\t0 none !default;\r\n\r\n$add-vendor-prefixes: true !default;\r\n\r\nbody.hidden-scroll {\r\n\toverflow: hidden;\r\n}\r\n.sl-overlay {\r\n\tposition: fixed;\r\n\tleft: 0;\r\n\tright: 0;\r\n\ttop: 0;\r\n\tbottom: 0;\r\n\tbackground: $sl-overlay-background;\r\n\topacity: $sl-overlay-opacity;\r\n\tdisplay: none;\r\n\tz-index: 1035;\r\n}\r\n.sl-wrapper {\r\n\tz-index: 1040;\r\n\tbutton {\r\n\t\tborder: 0 none;\r\n\t\tbackground: transparent;\r\n\t\tfont-size: 28px;\r\n\t\tpadding: 0;\r\n\t\tcursor: pointer;\r\n\t\t&:hover {\r\n\t\t\topacity: 0.7;\r\n\t\t}\r\n\t}\r\n\r\n\t.sl-close {\r\n\t\tdisplay: none;\r\n\t\tposition: fixed;\r\n\t\tright: 30px;\r\n\t\ttop: 30px;\r\n\t\tz-index: 10060;\r\n\t\tmargin-top: -14px;\r\n\t\tmargin-right: -14px;\r\n\t\theight: 44px;\r\n\t\twidth: 44px;\r\n\t\tline-height: 44px;\r\n\t\tfont-family: $sl-font-family;\r\n\t\tcolor: $sl-navigation-color;\r\n\t\tfont-size: $sl-close-fontsize;\r\n\r\n\t\t&:focus {\r\n\t\t\toutline: none;\r\n\t\t}\r\n\t}\r\n\r\n\t.sl-counter {\r\n\t\tdisplay: none;\r\n\t\tposition: fixed;\r\n\t\ttop: 30px;\r\n\t\tleft: 30px;\r\n\t\tz-index: 1060;\r\n\t\tcolor: $sl-navigation-color;\r\n\t\tfont-size: $sl-counter-fontsize;\r\n\t}\r\n\r\n\t.sl-navigation {\r\n\t\twidth: 100%;\r\n\t\tdisplay: none;\r\n\t\tbutton {\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 50%;\r\n\t\t\tmargin-top: -22px;\r\n\t\t\theight: 44px;\r\n\t\t\twidth: 22px;\r\n\t\t\tline-height: 44px;\r\n\t\t\ttext-align: center;\r\n\t\t\tdisplay: block;\r\n\t\t\tz-index: 10060;\r\n\t\t\tfont-family: $sl-font-family;\r\n\t\t\tcolor: $sl-navigation-color;\r\n\t\t\t&.sl-next {\r\n\t\t\t\tright: 5px;\r\n\t\t\t\tfont-size: $sl-arrow-fontsize-small;\r\n\t\t\t}\r\n\r\n\t\t\t&.sl-prev {\r\n\t\t\t\tleft: 5px;\r\n\t\t\t\tfont-size: $sl-arrow-fontsize-small;\r\n\t\t\t}\r\n\r\n\t\t\t&:focus {\r\n\t\t\t\toutline: none;\r\n\t\t\t}\r\n\r\n\t\t\t@media (min-width: $sl-breakpoint-medium) {\r\n\t\t\t\twidth: 44px;\r\n\r\n\t\t\t\t&.sl-next {\r\n\t\t\t\t\tright: 10px;\r\n\t\t\t\t\tfont-size: $sl-arrow-fontsize-medium;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.sl-prev {\r\n\t\t\t\t\tleft: 10px;\r\n\t\t\t\t\tfont-size: $sl-arrow-fontsize-medium;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t@media (min-width: $sl-breakpoint-large) {\r\n\t\t\t\twidth: 44px;\r\n\r\n\t\t\t\t&.sl-next {\r\n\t\t\t\t\tright: 20px;\r\n\t\t\t\t\tfont-size: $sl-arrow-fontsize-large;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.sl-prev {\r\n\t\t\t\t\tleft: 20px;\r\n\t\t\t\t\tfont-size: $sl-arrow-fontsize-large;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.sl-image {\r\n\t\tposition: fixed;\r\n\t\t@if $add-vendor-prefixes {\r\n\t\t\t-ms-touch-action: none;\r\n\t\t}\r\n\t\ttouch-action: none;\r\n\t\tz-index: 10000;\r\n\t\timg {\r\n\t\t\tmargin: 0;\r\n\t\t\tpadding: 0;\r\n\t\t\tdisplay: block;\r\n\t\t\tborder: $sl-img-border-small;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: auto;\r\n\t\t\t@media (min-width: $sl-breakpoint-medium) {\r\n\t\t\t\tborder: $sl-img-border-medium;\r\n\t\t\t}\r\n\t\t\t@media (min-width: $sl-breakpoint-large) {\r\n\t\t\t\tborder: $sl-img-border-large;\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t\tiframe {\r\n\t\t\tbackground: #000;\r\n\t\t\tborder: $sl-iframe-border-small;\r\n\t\t\t@media (min-width: $sl-breakpoint-medium) {\r\n\t\t\t\tborder: $sl-iframe-border-medium;\r\n\t\t\t}\r\n\t\t\t@media (min-width: $sl-breakpoint-large) {\r\n\t\t\t\tborder: $sl-iframe-border-large;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.sl-caption {\r\n\t\t\tdisplay: none;\r\n\t\t\tpadding: 10px;\r\n\t\t\tcolor: $sl-caption-color;\r\n\t\t\tbackground: $sl-caption-background;\r\n\t\t\tfont-size: $sl-caption-fontsize;\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\r\n\t\t\t&.pos-top {\r\n\t\t\t\tbottom: auto;\r\n\t\t\t\ttop: 0;\r\n\t\t\t}\r\n\r\n\t\t\t&.pos-outside {\r\n\t\t\t\tbottom: auto;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.sl-download {\r\n\t\t\tdisplay: none;\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 5px;\r\n\t\t\tright: 5px;\r\n\t\t\tcolor: $sl-navigation-color;\r\n\t\t\tz-index: 1060;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.sl-spinner {\r\n\t\tdisplay: none;\r\n\t\tborder: 5px solid #333;\r\n\t\tborder-radius: 40px;\r\n\t\theight: 40px;\r\n\t\tleft: 50%;\r\n\t\tmargin: -20px 0 0 -20px;\r\n\t\topacity: 0;\r\n\t\tposition: fixed;\r\n\t\ttop: 50%;\r\n\t\twidth: 40px;\r\n\t\tz-index: 1007;\r\n\t\t@if $add-vendor-prefixes {\r\n\t\t\t-webkit-animation: pulsate 1s ease-out infinite;\r\n\t\t\t-moz-animation: pulsate 1s ease-out infinite;\r\n\t\t\t-ms-animation: pulsate 1s ease-out infinite;\r\n\t\t\t-o-animation: pulsate 1s ease-out infinite;\r\n\t\t}\r\n\t\tanimation: pulsate 1s ease-out infinite;\r\n}\r\n\r\n.sl-scrollbar-measure {\r\n\tposition: absolute;\r\n\ttop: -9999px;\r\n\twidth: 50px;\r\n\theight: 50px;\r\n\toverflow: scroll;\r\n}\r\n\r\n.sl-transition {\r\n\t@if $add-vendor-prefixes {\r\n\t\ttransition: -moz-transform ease 200ms;\r\n\t\ttransition: -ms-transform ease 200ms;\r\n\t\ttransition: -o-transform ease 200ms;\r\n\t\ttransition: -webkit-transform ease 200ms;\r\n\t}\r\n\ttransition: transform ease 200ms;\r\n}\r\n\r\n@-webkit-keyframes pulsate{\r\n\t\t0% {\r\n\t\t\ttransform: scale(.1);\r\n\t\t\topacity: 0.0;\r\n\t\t}\r\n\t\t50% {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t\t100% {\r\n\t\t\ttransform: scale(1.2);\r\n\t\t\topacity: 0;\r\n\t\t}\r\n}\r\n@keyframes pulsate {\r\n\t\t0% {\r\n\t\t\ttransform: scale(.1);\r\n\t\t\topacity: 0.0;\r\n\t\t}\r\n\t\t50% {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t\t100% {\r\n\t\t\ttransform: scale(1.2);\r\n\t\t\topacity: 0;\r\n\t\t}\r\n}\r\n@if $add-vendor-prefixes {\r\n\t@-moz-keyframes pulsate{\r\n\t\t\t0% {\r\n\t\t\t\ttransform: scale(.1);\r\n\t\t\t\topacity: 0.0;\r\n\t\t\t}\r\n\t\t\t50% {\r\n\t\t\t\topacity: 1;\r\n\t\t\t}\r\n\t\t\t100% {\r\n\t\t\t\ttransform: scale(1.2);\r\n\t\t\t\topacity: 0;\r\n\t\t\t}\r\n\t}\r\n\r\n\t@-o-keyframes pulsate{\r\n\t\t\t0% {\r\n\t\t\t\ttransform: scale(.1);\r\n\t\t\t\topacity: 0.0;\r\n\t\t\t}\r\n\t\t\t50% {\r\n\t\t\t\topacity: 1;\r\n\t\t\t}\r\n\t\t\t100% {\r\n\t\t\t\ttransform: scale(1.2);\r\n\t\t\t\topacity: 0;\r\n\t\t\t}\r\n\t}\r\n\r\n\t@-ms-keyframes pulsate{\r\n\t\t\t0% {\r\n\t\t\t\ttransform: scale(.1);\r\n\t\t\t\topacity: 0.0;\r\n\t\t\t}\r\n\t\t\t50% {\r\n\t\t\t\topacity: 1;\r\n\t\t\t}\r\n\t\t\t100% {\r\n\t\t\t\ttransform: scale(1.2);\r\n\t\t\t\topacity: 0;\r\n\t\t\t}\r\n\t}\r\n}\r\n", "// barvy\r\n\r\n  // základní barvy\r\n  $color_white: #fff;\r\n  $color_black: #000;\r\n\r\n  // šedá barva\r\n  $color_gray: #585858;\r\n  $color_gray_dark: #1c1c1c;\r\n  $color_gray_light: #bcbcbc;\r\n  $color_gray_medium: lighten( $color_gray, 25% );\r\n  $color_gray_back: #f5f5f5;\r\n\r\n  // hlavní barvy projektu\r\n  $color_main: #ffc300;\r\n  $color_main_dark: darken( $color_main, 5% );\r\n  $color_main_light: #fff3cc;\r\n\r\n  // barvy pro chybové hlášky\r\n  $color_success: #008f05;\r\n  $color_info: $color_main;\r\n  $color_danger: #e84747;\r\n\r\n  // lightbox\r\n  $sl-overlay-background: $color_black;\r\n  $sl-overlay-opacity: 0.9;\r\n  $sl-navigation-color: $color_white;\r\n\r\n  // štítky\r\n  $color_gold: $color_main;\r\n  $color_delivery: #ffe89c;\r\n  $color_action: $color_danger;\r\n  $color_vegan: #cdf1ce;\r\n  $color_new: #c7e6fb;\r\n\r\n  // přechody\r\n  $gradient_gold: linear-gradient( to right, $color_main 0%, #ff9900 100% );\r\n\r\n// zakulacené rohy\r\n$radius: 5px;\r\n\r\n// layout\r\n$layout_gap: 12px;\r\n\r\n// breakpointy\r\n\r\n  // základní převzaty z Bootstrap Gridu\r\n  $mqxs: 576px;     // small\r\n  $mqsm: 768px;     // medium\r\n  $mqmd: 992px;     // large\r\n  $mqlg: 1200px;    // extra large\r\n  // rozšířené\r\n  $mqxxs: 419px;    // extra small\r\n  $mqxlg: 1400px;   // max large\r\n  $mqxxlg: 1600px;  // extra large\r\n  $mq_menu: 992px;  // respo menu\r\n  $mq_modal: 992px; // modal okno\r\n\r\n// z-index\r\n$index_base: 1;\r\n$index_page: 10;\r\n$index_menu: 100;\r\n$index_modal: 1000;\r\n", "@keyframes splide-loading{0%{transform:rotate(0)}to{transform:rotate(1turn)}}.splide--draggable>.splide__slider>.splide__track,.splide--draggable>.splide__track{-webkit-user-select:none;-ms-user-select:none;user-select:none}.splide--fade>.splide__slider>.splide__track>.splide__list,.splide--fade>.splide__track>.splide__list{display:block}.splide--fade>.splide__slider>.splide__track>.splide__list>.splide__slide,.splide--fade>.splide__track>.splide__list>.splide__slide{left:0;opacity:0;position:absolute;top:0;z-index:0}.splide--fade>.splide__slider>.splide__track>.splide__list>.splide__slide.is-active,.splide--fade>.splide__track>.splide__list>.splide__slide.is-active{opacity:1;position:relative;z-index:1}.splide--rtl{direction:rtl}.splide--ttb.is-active>.splide__slider>.splide__track>.splide__list,.splide--ttb.is-active>.splide__track>.splide__list{display:block}.splide__container{box-sizing:border-box;position:relative}.splide__list{-webkit-backface-visibility:hidden;backface-visibility:hidden;display:-ms-flexbox;display:flex;height:100%;margin:0!important;padding:0!important;transform-style:preserve-3d}.splide.is-initialized:not(.is-active) .splide__list{display:block}.splide__pagination{-ms-flex-align:center;align-items:center;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:center;justify-content:center;margin:0;pointer-events:none}.splide__pagination li{display:inline-block;line-height:1;list-style-type:none;margin:0;pointer-events:auto}.splide__progress__bar{width:0}.splide{outline:none;position:relative;visibility:hidden}.splide.is-initialized,.splide.is-rendered{visibility:visible}.splide__slide{-webkit-backface-visibility:hidden;backface-visibility:hidden;box-sizing:border-box;-ms-flex-negative:0;flex-shrink:0;list-style-type:none!important;margin:0;outline:none;position:relative}.splide__slide img{vertical-align:bottom}.splide__slider{position:relative}.splide__spinner{animation:splide-loading 1s linear infinite;border:2px solid #999;border-left-color:transparent;border-radius:50%;bottom:0;display:inline-block;height:20px;left:0;margin:auto;position:absolute;right:0;top:0;width:20px}.splide__track{overflow:hidden;position:relative;z-index:0}", "/* Functional styling;\n * These styles are required for noUiSlider to function.\n * You don't need to change these rules to apply your design.\n */\n.noUi-target,\n.noUi-target * {\n  -webkit-touch-callout: none;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  -webkit-user-select: none;\n  -ms-touch-action: none;\n  touch-action: none;\n  -ms-user-select: none;\n  -moz-user-select: none;\n  user-select: none;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.noUi-target {\n  position: relative;\n}\n.noUi-base,\n.noUi-connects {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  z-index: 1;\n}\n/* Wrapper for all connect elements.\n */\n.noUi-connects {\n  overflow: hidden;\n  z-index: 0;\n}\n.noUi-connect,\n.noUi-origin {\n  will-change: transform;\n  position: absolute;\n  z-index: 1;\n  top: 0;\n  right: 0;\n  height: 100%;\n  width: 100%;\n  -ms-transform-origin: 0 0;\n  -webkit-transform-origin: 0 0;\n  -webkit-transform-style: preserve-3d;\n  transform-origin: 0 0;\n  transform-style: flat;\n}\n/* Offset direction\n */\n.noUi-txt-dir-rtl.noUi-horizontal .noUi-origin {\n  left: 0;\n  right: auto;\n}\n/* Give origins 0 height/width so they don't interfere with clicking the\n * connect elements.\n */\n.noUi-vertical .noUi-origin {\n  top: -100%;\n  width: 0;\n}\n.noUi-horizontal .noUi-origin {\n  height: 0;\n}\n.noUi-handle {\n  -webkit-backface-visibility: hidden;\n  backface-visibility: hidden;\n  position: absolute;\n}\n.noUi-touch-area {\n  height: 100%;\n  width: 100%;\n}\n.noUi-state-tap .noUi-connect,\n.noUi-state-tap .noUi-origin {\n  -webkit-transition: transform 0.3s;\n  transition: transform 0.3s;\n}\n.noUi-state-drag * {\n  cursor: inherit !important;\n}\n/* Slider size and handle placement;\n */\n.noUi-horizontal {\n  height: 18px;\n}\n.noUi-horizontal .noUi-handle {\n  width: 34px;\n  height: 28px;\n  right: -17px;\n  top: -6px;\n}\n.noUi-vertical {\n  width: 18px;\n}\n.noUi-vertical .noUi-handle {\n  width: 28px;\n  height: 34px;\n  right: -6px;\n  bottom: -17px;\n}\n.noUi-txt-dir-rtl.noUi-horizontal .noUi-handle {\n  left: -17px;\n  right: auto;\n}\n/* Styling;\n * Giving the connect element a border radius causes issues with using transform: scale\n */\n.noUi-target {\n  background: #FAFAFA;\n  border-radius: 4px;\n  border: 1px solid #D3D3D3;\n  box-shadow: inset 0 1px 1px #F0F0F0, 0 3px 6px -5px #BBB;\n}\n.noUi-connects {\n  border-radius: 3px;\n}\n.noUi-connect {\n  background: #3FB8AF;\n}\n/* Handles and cursors;\n */\n.noUi-draggable {\n  cursor: ew-resize;\n}\n.noUi-vertical .noUi-draggable {\n  cursor: ns-resize;\n}\n.noUi-handle {\n  border: 1px solid #D9D9D9;\n  border-radius: 3px;\n  background: #FFF;\n  cursor: default;\n  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #EBEBEB, 0 3px 6px -3px #BBB;\n}\n.noUi-active {\n  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #DDD, 0 3px 6px -3px #BBB;\n}\n/* Handle stripes;\n */\n.noUi-handle:before,\n.noUi-handle:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  height: 14px;\n  width: 1px;\n  background: #E8E7E6;\n  left: 14px;\n  top: 6px;\n}\n.noUi-handle:after {\n  left: 17px;\n}\n.noUi-vertical .noUi-handle:before,\n.noUi-vertical .noUi-handle:after {\n  width: 14px;\n  height: 1px;\n  left: 6px;\n  top: 14px;\n}\n.noUi-vertical .noUi-handle:after {\n  top: 17px;\n}\n/* Disabled state;\n */\n[disabled] .noUi-connect {\n  background: #B8B8B8;\n}\n[disabled].noUi-target,\n[disabled].noUi-handle,\n[disabled] .noUi-handle {\n  cursor: not-allowed;\n}\n/* Base;\n *\n */\n.noUi-pips,\n.noUi-pips * {\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.noUi-pips {\n  position: absolute;\n  color: #999;\n}\n/* Values;\n *\n */\n.noUi-value {\n  position: absolute;\n  white-space: nowrap;\n  text-align: center;\n}\n.noUi-value-sub {\n  color: #ccc;\n  font-size: 10px;\n}\n/* Markings;\n *\n */\n.noUi-marker {\n  position: absolute;\n  background: #CCC;\n}\n.noUi-marker-sub {\n  background: #AAA;\n}\n.noUi-marker-large {\n  background: #AAA;\n}\n/* Horizontal layout;\n *\n */\n.noUi-pips-horizontal {\n  padding: 10px 0;\n  height: 80px;\n  top: 100%;\n  left: 0;\n  width: 100%;\n}\n.noUi-value-horizontal {\n  -webkit-transform: translate(-50%, 50%);\n  transform: translate(-50%, 50%);\n}\n.noUi-rtl .noUi-value-horizontal {\n  -webkit-transform: translate(50%, 50%);\n  transform: translate(50%, 50%);\n}\n.noUi-marker-horizontal.noUi-marker {\n  margin-left: -1px;\n  width: 2px;\n  height: 5px;\n}\n.noUi-marker-horizontal.noUi-marker-sub {\n  height: 10px;\n}\n.noUi-marker-horizontal.noUi-marker-large {\n  height: 15px;\n}\n/* Vertical layout;\n *\n */\n.noUi-pips-vertical {\n  padding: 0 10px;\n  height: 100%;\n  top: 0;\n  left: 100%;\n}\n.noUi-value-vertical {\n  -webkit-transform: translate(0, -50%);\n  transform: translate(0, -50%);\n  padding-left: 25px;\n}\n.noUi-rtl .noUi-value-vertical {\n  -webkit-transform: translate(0, 50%);\n  transform: translate(0, 50%);\n}\n.noUi-marker-vertical.noUi-marker {\n  width: 5px;\n  height: 2px;\n  margin-top: -1px;\n}\n.noUi-marker-vertical.noUi-marker-sub {\n  width: 10px;\n}\n.noUi-marker-vertical.noUi-marker-large {\n  width: 15px;\n}\n.noUi-tooltip {\n  display: block;\n  position: absolute;\n  border: 1px solid #D9D9D9;\n  border-radius: 3px;\n  background: #fff;\n  color: #000;\n  padding: 5px;\n  text-align: center;\n  white-space: nowrap;\n}\n.noUi-horizontal .noUi-tooltip {\n  -webkit-transform: translate(-50%, 0);\n  transform: translate(-50%, 0);\n  left: 50%;\n  bottom: 120%;\n}\n.noUi-vertical .noUi-tooltip {\n  -webkit-transform: translate(0, -50%);\n  transform: translate(0, -50%);\n  top: 50%;\n  right: 120%;\n}\n.noUi-horizontal .noUi-origin > .noUi-tooltip {\n  -webkit-transform: translate(50%, 0);\n  transform: translate(50%, 0);\n  left: auto;\n  bottom: 10px;\n}\n.noUi-vertical .noUi-origin > .noUi-tooltip {\n  -webkit-transform: translate(0, -18px);\n  transform: translate(0, -18px);\n  top: auto;\n  right: 28px;\n}\n", "// box sizing\nhtml {\n  box-sizing: border-box;\n}\n\n*,\n*:before,\n*:after {\n  box-sizing: inherit;\n}\n\nimg {\n  box-sizing: content-box;\n}\n\n// nastavení velikosti písma\n:root {\n  font-size: 100%;\n}\n\n// základní definice\nbody {\n  position: relative;\n\n  margin: 0;\n  padding-top: 70px;\n\n  color: $color_black;\n  font-family: '<PERSON>', sans-serif;\n  font-size: 16px;\n  line-height: 1;\n\n  background-color: $color_gray_back;\n\n  @media (min-width: $mq_menu) {\n    padding-top: 151px;\n  }\n\n  @media (min-width: $mqlg) {\n    padding-top: 194px;\n  }\n}\n\n// základní nastavení respo\n@-ms-viewport {\n  width: device-width;\n}\n\nimg,\nsvg {\n  display: inline-block;\n\n  max-width: 100%;\n  height: auto;\n\n  border: none;\n}\n\niframe {\n  max-width: 100%;\n}\n", "// h<PERSON><PERSON><PERSON>\r\n.container {\n  max-width: $mqxlg;\r\n  margin: 0 auto;\r\n  padding: 0 $layout_gap;\r\n}\r\n\r\n// krat<PERSON><PERSON> verze\r\n.container--short {\n  max-width: 920px;\r\n}\r\n\r\n// verze se sidebarem\r\n.container--sidebar {\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n\r\n  @media (min-width: $mqlg) {\n    flex-wrap: nowrap;\r\n  }\n}\r\n\r\n// řádek m<PERSON>\r\n.row {\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n\n  margin: 0 (-$layout_gap);\r\n\r\n  // mezera mezi boxy\r\n  & > .col {\n    padding: $layout_gap;\r\n  }\n}\r\n\r\n// fix pro max-width tabulku\r\n.row--block {\n  @media (max-width: $mqsm) {\n    display: block;\r\n  }\n}\r\n\r\n// řádek se stejně vysokými bloky\r\n.row--align {\n  & > .col {\n    flex-direction: row;\r\n  }\n}\r\n\r\n// mezery mezi bloky\r\n.row--space {\n  justify-content: space-between;\r\n}\r\n\r\n// centrování\r\n.row--center {\n  align-items: center;\r\n}\r\n\r\n// zarovná<PERSON><PERSON> zlev<PERSON>\r\n.row--start {\n  justify-content: flex-start;\r\n}\r\n\r\n// ř<PERSON><PERSON> bez mezer\r\n.row--nogap {\n  margin: 0;\r\n\r\n  & > .col {\n    padding: 0;\r\n  }\n}\r\n\r\n// blok mří<PERSON>ky\r\n.col {\n  display: flex;\r\n  flex: 0 0 100%;\r\n  flex-direction: column;\r\n}\r\n\r\n// dva vedle sebe\r\n.col--2 {\n  @media (min-width: $mqxs) {\n    flex-basis: auto;\r\n\n    width: 50%;\r\n  }\n}\r\n\r\n// tři vedle sebe\r\n.col--3 {\n  @media (min-width: $mqxs) {\n    flex-basis: auto;\r\n\n    width: 50%;\r\n  }\r\n\r\n  @media (min-width: $mqmd) {\n    width: 33.33333333%;\r\n  }\n}\r\n\r\n// čtyři vedle sebe\r\n.col--4 {\n  @media (min-width: $mqxs) {\n    flex-basis: auto;\r\n\n    width: 50%;\r\n  }\r\n\r\n  @media (min-width: $mqmd) {\n    width: 25%;\r\n  }\n}\r\n\r\n// pět vedle sebe\r\n.col--5 {\n  @media (min-width: $mqxs) {\n    flex-basis: auto;\r\n\n    width: 50%;\r\n  }\r\n\r\n  @media (min-width: $mqmd) {\n    width: 20%;\r\n  }\n}\r\n\r\n// šest vedle sebe\r\n.col--6 {\n  @media (min-width: $mqxs) {\n    flex-basis: auto;\r\n\n    width: 50%;\r\n  }\r\n\r\n  @media (min-width: $mqmd) {\n    width: 16.66666666666667%;\r\n  }\n}\r\n\r\n// buňka se roztahuje\r\n.col--grow {\n  flex-grow: 1;\r\n}\r\n\r\n// zarovnání na pravou stranu\r\n.col--end {\n  align-items: flex-end;\r\n}\r\n\r\n// zarovnání na levou stranu\r\n.col--start {\n  align-items: flex-start;\r\n}\r\n\r\n// zarovnání na střed\r\n.col--center {\n  align-items: center;\r\n}\r\n\r\n// změna osy\r\n.col--row {\n  flex-direction: row;\r\n}\r\n", "// webové fonty\n\n/* barlow-regular - latin-ext_latin */\n@font-face {\n  font-family: 'Barlow';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url('../fonts/barlow-v5-latin-ext_latin-regular.eot'); /* IE9 Compat Modes */\n  src: local(''),\n       url('../fonts/barlow-v5-latin-ext_latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */\n       url('../fonts/barlow-v5-latin-ext_latin-regular.woff2') format('woff2'), /* Super Modern Browsers */\n       url('../fonts/barlow-v5-latin-ext_latin-regular.woff') format('woff'), /* Modern Browsers */\n       url('../fonts/barlow-v5-latin-ext_latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */\n       url('../fonts/barlow-v5-latin-ext_latin-regular.svg#Barlow') format('svg'); /* Legacy iOS */\n}\n/* barlow-500 - latin-ext_latin */\n@font-face {\n  font-family: 'Barlow';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url('../fonts/barlow-v5-latin-ext_latin-500.eot'); /* IE9 Compat Modes */\n  src: local(''),\n       url('../fonts/barlow-v5-latin-ext_latin-500.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */\n       url('../fonts/barlow-v5-latin-ext_latin-500.woff2') format('woff2'), /* Super Modern Browsers */\n       url('../fonts/barlow-v5-latin-ext_latin-500.woff') format('woff'), /* Modern Browsers */\n       url('../fonts/barlow-v5-latin-ext_latin-500.ttf') format('truetype'), /* Safari, Android, iOS */\n       url('../fonts/barlow-v5-latin-ext_latin-500.svg#Barlow') format('svg'); /* Legacy iOS */\n}\n/* barlow-600 - latin-ext_latin */\n@font-face {\n  font-family: 'Barlow';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url('../fonts/barlow-v5-latin-ext_latin-600.eot'); /* IE9 Compat Modes */\n  src: local(''),\n       url('../fonts/barlow-v5-latin-ext_latin-600.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */\n       url('../fonts/barlow-v5-latin-ext_latin-600.woff2') format('woff2'), /* Super Modern Browsers */\n       url('../fonts/barlow-v5-latin-ext_latin-600.woff') format('woff'), /* Modern Browsers */\n       url('../fonts/barlow-v5-latin-ext_latin-600.ttf') format('truetype'), /* Safari, Android, iOS */\n       url('../fonts/barlow-v5-latin-ext_latin-600.svg#Barlow') format('svg'); /* Legacy iOS */\n}\n/* barlow-700 - latin-ext_latin */\n@font-face {\n  font-family: 'Barlow';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url('../fonts/barlow-v5-latin-ext_latin-700.eot'); /* IE9 Compat Modes */\n  src: local(''),\n       url('../fonts/barlow-v5-latin-ext_latin-700.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */\n       url('../fonts/barlow-v5-latin-ext_latin-700.woff2') format('woff2'), /* Super Modern Browsers */\n       url('../fonts/barlow-v5-latin-ext_latin-700.woff') format('woff'), /* Modern Browsers */\n       url('../fonts/barlow-v5-latin-ext_latin-700.ttf') format('truetype'), /* Safari, Android, iOS */\n       url('../fonts/barlow-v5-latin-ext_latin-700.svg#Barlow') format('svg'); /* Legacy iOS */\n}\n", "// základn<PERSON> nastavení typografie\r\n\r\n// nadpisy\r\nh1,\nh2,\nh3,\nh4 {\n  margin-top: 0;\r\n  margin-bottom: 15px;\r\n\n  font-weight: 700;\r\n  line-height: 1.4;\r\n\r\n  @media (min-width: $mqsm) {\n    line-height: 1.5;\r\n  }\n}\r\n\r\nh1 {\n  font-size: 30px;\r\n\r\n  @media (min-width: $mqmd) {\n    font-size: 40px;\r\n  }\n}\r\n\r\nh2 {\n  font-size: 20px;\r\n\r\n  @media (min-width: $mqmd) {\n    font-size: 30px;\r\n  }\n}\r\n\r\nh3 {\n  font-size: 18px;\r\n\r\n  @media (min-width: $mqmd) {\n    font-size: 22px;\r\n  }\n}\r\n\r\nh4 {\n  @media (min-width: $mqmd) {\n    font-size: 18px;\r\n  }\n}\r\n\r\n// odkazy\r\na {\n  color: $color_black;\r\n\r\n  &:hover,\n  &:focus {\n    color: $color_gray;\r\n  }\r\n\r\n  &[href^='tel:'] {\n    @media (min-width: $mqsm) {\n      text-decoration: none;\r\n    }\n  }\n}\r\n\r\n// odstavce\r\np {\n  margin: 0 0 30px 0;\r\n\n  line-height: 1.4;\r\n\r\n  @media (min-width: $mqsm) {\n    line-height: 1.8;\r\n  }\n}\r\n\r\n// seznamy\r\nul,\nol {\n  margin: 0 0 30px 0;\r\n  padding: 0 0 0 20px;\r\n\n  line-height: 1.8;\r\n\r\n  ul,\n  ol {\n    margin: 0;\r\n    padding: 3px 3px 0 10px;\r\n\r\n    li {\n      &:last-child {\n        padding-bottom: 0;\r\n      }\n    }\n  }\n}\r\n\r\n// definiční seznam\r\ndl {\n  display: flex;\r\n  flex-wrap: wrap;\r\n\n  margin: 20px 0;\r\n\n  line-height: 1.5;\r\n\r\n  dt {\n    flex: 0 0 20%;\r\n\n    padding-right: 20px;\r\n\n    font-weight: 600;\r\n  }\r\n\r\n  dd {\n    flex: 1 1 80%;\r\n\n    margin: 0;\r\n    margin-bottom: 15px;\r\n  }\n}\r\n", "// základní vzhled input polí\ninput,\ntextarea,\nselect {\n  padding: 13px 20px;\n\n  color: $color_black;\n  font-size: 17px;\n  line-height: 1;\n\n  border: 2px solid $color_gray_dark;\n  border-radius: $radius;\n  background-color: $color_white;\n\n  &:focus {\n    border-color: $color_gray_light;\n    outline: 0;\n  }\n\n  &.is-success {\n    border-color: $color_success;\n  }\n\n  &.is-danger {\n    border-color: $color_danger;\n  }\n}\n\n// úpravy pro textarea\ntextarea {\n  width: 100%;\n\n  line-height: 1.4; // čitelnější rozestupy mezi řádky\n}\n\n// popisek pole\nlabel {\n  font-size: 15px;\n\n  em {\n    color: #9e9e9e;\n    font-style: normal;\n  }\n}\n\n// ošetření submit inputu, pokud je použit\nbutton,\ninput[type='submit'] {\n  width: auto;\n\n  cursor: pointer;\n}\n\n// ošetření chekboxu a radio buttonu\ninput[type='checkbox'],\ninput[type='radio'] {\n  width: auto;\n}\n", "// SVG ikony\r\n.icon {\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n  position: relative;\r\n  color: $color_black;\r\n\r\n  &:before {\r\n    content: '';\r\n    display: block;\r\n  }\r\n}\r\n\r\n// vnitřní box s ikonou\r\n.icon__svg {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  fill: currentColor;\r\n  pointer-events: none;\r\n  transform: translateZ(0);\r\n}\r\n\r\n// propoje<PERSON><PERSON> jednotliv<PERSON>ch ikon\r\n@import \"icons/icons.scss\";\r\n", "\n.icon--arrow-next {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--basket {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--car {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--clock {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--close {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--color-facebook {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--color-instagram {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--color-login-google {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--compare {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--danger {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--delete {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--email {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--heart-full {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--heart {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--home {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--leaf {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--login-apple {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--login-facebook {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--login-seznam {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--magnifier {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--minus {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--next {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--percentage {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--phone {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--pin {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--plus {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--print {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--success {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n\n.icon--user {\n  width: 16px;\n\n  &:before {\n    padding-top: calc(16 / 16) * 100%;\n  }\n}\n", "// hlavička\r\n.header {\n  position: fixed;\r\n  top: 0;\r\n  z-index: $index_modal - 1;\r\n\n  width: 100%;\r\n\n  background-color: $color_white;\r\n}\r\n\r\n// verze do o<PERSON> (zjednodušená)\r\n.header--simple {\n  position: fixed;\r\n\n  padding: 10px 0;\r\n\r\n  h1 {\n    margin: 0;\r\n\r\n    a {\n      display: block;\r\n    }\r\n\r\n    img {\n      display: block;\r\n\n      width: 50px;\r\n\r\n      @media (min-width: $mq_menu) {\n        width: 90px;\r\n      }\n    }\n  }\r\n\r\n  .header-top__contact {\n    font-size: 17px;\r\n\r\n    a {\n      font-size: 17px;\r\n    }\r\n\r\n    small {\n      font-size: 14px;\r\n    }\n  }\r\n\r\n  .phone {\n    &:before {\n      top: 10px;\r\n    }\n  }\n}\r\n\r\n// obsahová část\r\n.header__body {\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n\n  background-color: $color_white;\r\n\r\n  @media (min-width: $mq_menu) {\n    padding-top: 10px;\r\n    padding-bottom: 10px;\r\n  }\n}\r\n\r\n// logo\r\n.header__logo {\n  flex: 0 0 76px;\r\n\r\n  @media (min-width: $mqlg) {\n    flex: 1 1 33%;\r\n  }\r\n\r\n  a {\n    position: relative;\r\n    z-index: $index_menu + 1;\r\n\n    display: inline-block;\r\n\n    margin-bottom: -25px;\r\n    padding-bottom: 25px;\r\n\r\n    @media (min-width: $mq_menu) {\n      margin-bottom: -50px;\r\n      padding-bottom: 50px;\r\n    }\r\n\r\n    @media (min-width: $mqlg) {\n      margin-bottom: -75px;\r\n      padding-bottom: 75px;\r\n    }\n  }\r\n\r\n  img {\n    margin-bottom: -25px;\r\n\r\n    @media (min-width: $mq_menu) {\n      margin-bottom: -50px;\r\n    }\r\n\r\n    @media (min-width: $mqlg) {\n      margin-top: 10px;\r\n      margin-bottom: -75px;\r\n    }\n  }\n}\r\n\r\n// pomocná navigace (přihlášení, košík...)\r\n.header__nav {\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n\r\n  @media (min-width: $mqlg) {\n    flex: 1 1 33%;\r\n  }\r\n\r\n  a {\n    text-decoration: none;\r\n  }\r\n\r\n  .icon {\n    width: 30px;\r\n  }\n}\r\n\r\n// oblíbené\r\n.header__fav {\n  position: relative;\r\n\n  display: none;\r\n\n  margin-right: 20px;\r\n\r\n  @media (min-width: 360px) {\n    display: block;\r\n  }\r\n\r\n  @media (min-width: $mq_menu) {\n    margin-right: 50px;\r\n  }\r\n\r\n  .count {\n    position: absolute;\r\n    top: -8px;\r\n    left: 20px;\r\n  }\n}\r\n\r\n// přepínač menu\r\n.header__switcher {\n  overflow: hidden;\r\n\n  width: 70px;\r\n  height: 70px;\r\n  margin-right: -12px;\r\n\n  text-indent: 9999px;\r\n\n  background: $color_main url( ../img/menu.svg ) center center no-repeat;\r\n\n  cursor: pointer;\r\n\r\n  @media (min-width: $mq_menu) {\n    display: none;\r\n  }\r\n\r\n  &.is-open {\n    background-image: url( ../img/menu-close.svg );\r\n  }\n}\r\n\r\n// vyhledávací pole - ikona\r\n.header__search {\n  margin-right: 12px;\r\n\r\n  @media (min-width: $mq_menu) {\n    display: none;\r\n  }\n}\r\n", "// navigace\r\n.nav {\n  display: none;\r\n\n  font-size: 18px;\r\n  font-weight: 600;\r\n\n  background-color: $color_main;\r\n\r\n  @media (min-width: $mq_menu) {\n    display: block;\r\n  }\r\n\r\n  @media (max-width: $mq_menu - 1px) {\n    overflow: auto;\r\n\n    max-height: calc( 100vh - 70px );\r\n  }\r\n\r\n  &.is-open {\n    position: absolute;\r\n    z-index: $index_menu;\r\n\n    display: block;\r\n\n    width: 100%;\r\n    padding-bottom: 0;\r\n\r\n    .container {\n      padding-right: 0;\r\n      padding-left: 0;\r\n    }\n  }\r\n\r\n  ul,\n  li {\n    margin: 0;\r\n    padding: 0;\r\n\n    list-style-type: none;\r\n  }\r\n\r\n  ul {\n    @media (min-width: $mq_menu) {\n      display: flex;\r\n    }\n  }\r\n\r\n  li {\n    &:hover {\n      .nav__inner {\n        @media (min-width: $mq_menu) {\n          display: block;\r\n        }\n      }\n    }\n  }\r\n\r\n  .container {\n    position: relative;\r\n\n    padding-left: 100px;\r\n\r\n    @media (min-width: $mqlg) {\n      padding-left: 170px;\r\n    }\n  }\n}\r\n\r\n// hlavní část menu\r\n.nav__main {\n  & > a {\n    display: block;\r\n\n    padding: 15px 20px;\r\n\n    color: $color_black;\r\n    text-decoration: none;\r\n    text-transform: uppercase;\r\n\n    background-repeat: no-repeat;\r\n    background-position: right 20px center;\r\n\r\n    @media (min-width: $mq_menu) {\n      padding: 10px;\r\n\n      background-position: right 10px center;\r\n    }\r\n\r\n    @media (min-width: $mqlg) {\n      padding: 20px 15px;\r\n    }\r\n\r\n    @media (min-width: $mqxlg) {\n      padding: 20px 30px;\r\n    }\r\n\r\n    @media (max-width: $mqlg - 1px) {\n      line-height: 1.3;\r\n    }\n  }\r\n\r\n  &:hover {\n    @media (min-width: $mq_menu) {\n      & > a {\n        color: $color_white;\r\n\n        background-color: $color_gray;\r\n      }\r\n\r\n      &.has-submenu > a {\n        background-image: url( ../img/nav-inverse.svg );\r\n      }\n    }\n  }\r\n\r\n  &.has-submenu > a {\n    background-image: url( ../img/nav.svg );\r\n\r\n    @media (min-width: $mq_menu) {\n      padding-right: 25px;\r\n    }\r\n\r\n    @media (min-width: $mqxlg) {\n      padding-right: 30px;\r\n    }\n  }\r\n\r\n  &.has-submenu.is-open {\n    @media (max-width: $mq_menu - 1px) {\n      & > a {\n        color: $color_white;\r\n\n        background-color: $color_gray;\r\n        background-image: url( ../img/nav-inverse.svg );\r\n      }\r\n\r\n      .nav__inner {\n        display: block;\r\n      }\n    }\n  }\n}\r\n\r\n// info menu, zobrazíme jen na mobilu\r\n.nav__main--info {\n  background-color: $color_gray_light;\r\n\r\n  @media (min-width: $mq_menu) {\n    display: none;\r\n  }\n}\r\n\r\n// submenu\r\n.nav__inner {\n  display: none;\r\n\n  padding: 5px 10px 20px 10px;\r\n\n  background-color: $color_gray;\r\n\r\n  @media (min-width: $mq_menu) {\n    position: absolute;\r\n    right: 0;\r\n    left: 0;\r\n    z-index: $index_menu + 1;\r\n\n    padding: 40px 50px;\r\n  }\r\n\r\n  .category {\n    margin: 2px 0;\r\n    padding: 10px;\r\n\n    font-weight: 400;\r\n\n    background-color: darken( $color_gray, 10% );\r\n\r\n    @media (min-width: $mq_menu) {\n      margin: 5px;\r\n    }\r\n\r\n    &:hover,\n    &:focus {\n      background-color: $color_gray_dark;\r\n    }\n  }\n}\r\n\r\n// respo - kontakt, přepínač jazyků\r\n.nav__helper {\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n\n  margin: 30px 18px 10px 18px;\r\n  padding: 12px 8px 12px 12px;\r\n\n  font-size: 13px;\r\n  font-weight: 400;\r\n\n  background-color: $color_white;\r\n\r\n  @media (min-width: $mq_menu) {\n    display: none;\r\n  }\r\n\r\n  p {\n    margin: 0;\r\n\r\n    &:last-child {\n      @media (max-width: $mqxs - 1px) {\n        width: 25px;\r\n      }\n    }\n  }\r\n\r\n  a {\n    font-size: 15px;\r\n    font-weight: 500;\r\n    text-decoration: none;\r\n\r\n    &.is-active {\n      @media (max-width: $mqxs - 1px) {\n        display: none;\r\n      }\n    }\n  }\r\n\r\n  small {\n    font-size: 12px;\r\n    white-space: nowrap;\r\n  }\n}\r\n", "// základ<PERSON><PERSON> blok obsahu\n.section {\n  padding: 30px 0;\n\n  @media (min-width: $mqmd) {\n    padding: 80px 0;\n  }\n}\n\n// krat<PERSON><PERSON> verze\n.section--short {\n  padding: 15px 0;\n\n  @media (min-width: $mqmd) {\n    padding: 50px 0;\n  }\n}\n\n// základní verze\n.section--base {\n  background-color: $color_white;\n}\n\n// tmavější pozadí\n.section--dark {\n  background-color: #f2f2f2;\n}\n\n// inverzní verze\n.section--inverse {\n  color: $color_white;\n\n  background-color: $color_gray_dark;\n}\n\n// obsah nalepený za sebe\n.section--follow {\n  padding-top: 0;\n}\n\n// vnitřní navigace\n.section--nav {\n  padding: 0;\n}\n\n// detail produktu\n.section--detail {\n  padding-top: 30px;\n\n  .section__content {\n    @media (min-width: $mqmd) {\n      max-width: 800px;\n    }\n\n    h2 em {\n      display: inline-block;\n\n      padding-left: 5px;\n\n      color: $color_gray;\n      font-size: 15px;\n      font-style: normal;\n    }\n\n    p,\n    ul,\n    ol {\n      margin: 0 0 20px 0;\n\n      @media (min-width: $mqsm) {\n        line-height: 1.4;\n      }\n    }\n  }\n}\n\n// část s obsahem (pro dlouhé texty)\n.section--text {\n  @media (min-width: $mqsm) {\n    font-size: 20px;\n\n    h2,\n    h3,\n    h4,\n    p,\n    ul,\n    ol {\n      max-width: 1110px;\n    }\n  }\n\n  ul,\n  li {\n    @media (max-width: $mqsm - 1px) {\n      line-height: 1.4;\n    }\n  }\n}\n\n// obsahová část (roztahování pro sidebar)\n.section__content {\n  flex: 1 1 auto;\n\n  max-width: 100%;\n}\n\n// nadpis\n.section__title {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  justify-content: center;\n\n  @media (min-width: $mqsm) {\n    justify-content: space-between;\n\n    margin-bottom: 40px;\n  }\n\n  @media (max-width: $mqsm - 1px) {\n    .link {\n      display: none;\n    }\n  }\n\n  * {\n    margin: 0;\n  }\n}\n\n// stránkování\n.section__pages {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-end;\n\n  margin-top: 40px;\n\n  p,\n  nav {\n    width: 100%;\n    margin: 0 0 15px 0;\n\n    text-align: center;\n\n    @media (min-width: $mqsm) {\n      width: 50%;\n      margin: 0;\n    }\n\n    @media (min-width: $mqmd) {\n      width: 33.33333333%;\n    }\n  }\n\n  nav {\n    @media (min-width: $mqsm) {\n      text-align: right;\n    }\n  }\n}\n\n// stránkování - bez pageru\n.section__pages--full {\n  p {\n    width: 100%;\n  }\n}\n\n// výpis kontaktů\n.section__contact {\n  font-size: 14px;\n  vertical-align: top;\n\n  @media (min-width: $mqxs) {\n    display: inline-block;\n  }\n\n  h2 {\n    margin-bottom: 20px;\n\n    font-size: 20px;\n  }\n\n  p {\n    margin-right: 15px;\n\n    line-height: 1.57;\n  }\n\n  & + & {\n    @media (min-width: $mqxs) {\n      margin-left: 100px;\n    }\n\n    @media (min-width: $mqmd) {\n      margin-left: 0;\n    }\n\n    @media (min-width: $mqlg) {\n      margin-left: 100px;\n    }\n  }\n}\n\n// oddělovač\n.section__divider {\n  width: 100%;\n  height: 1px;\n  margin: 35px 0;\n\n  background-color: #dedede;\n\n  hr {\n    display: none;\n  }\n}\n\n// úprava pro vyheldávání\n.section__search {\n  margin: 25px 0;\n\n  @media (min-width: $mqmd) {\n    margin: 50px 0;\n  }\n\n  ul {\n    margin: 0;\n    padding: 0;\n  }\n\n  .category {\n    margin: 0 5px 5px 0;\n  }\n\n  .article {\n    margin-right: 0;\n    margin-left: 0;\n  }\n}\n\n// kotva\n.section__anchor {\n  position: relative;\n  top: -80px;\n  z-index: -1;\n\n  display: block;\n  visibility: hidden;\n\n  @media (min-width: $mq_menu) {\n    top: -200px;\n  }\n}\n\n// falešný odstavec (problémy s vloženým iframe/divem u doprav)\n.section__p {\n  margin: 0 0 30px 0;\n\n  line-height: 1.4;\n\n  @media (min-width: $mqsm) {\n    line-height: 1.8;\n  }\n}\n", "// patička\r\n.footer {\n  padding: 30px 0;\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 50px 0;\r\n  }\r\n\r\n  @media (max-width: $mqmd - 1px) {\n    text-align: center;\r\n  }\r\n\r\n  h2 {\n    font-size: 17px;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .contact-box {\n    text-align: left;\r\n\r\n    @media (max-width: $mqmd - 1px) {\n      margin-right: auto;\r\n      margin-left: auto;\r\n    }\n  }\n}\r\n\r\n// platby\r\n.footer__payment {\n  margin: 15px 0;\r\n  padding: 0;\r\n\n  list-style-type: none;\r\n\r\n  @media (min-width: $mqmd) {\n    margin-top: 30px;\r\n  }\r\n\r\n  li {\n    display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\n    width: 63px;\r\n    height: 39px;\r\n    margin: 8px 4px 0 0;\r\n\n    vertical-align: top;\r\n\n    border-radius: $radius;\r\n    background-color: $color_white;\r\n\r\n    @media (min-width: $mqsm) {\n      margin: 8px 0 0 8px;\r\n    }\n  }\n}\r\n\r\n// hodnocení\r\n.footer__rating {\n  justify-content: center;\r\n\n  text-align: center;\r\n\r\n  @media (max-width: $mqmd - 1px) {\n    width: 100%;\r\n  }\r\n\r\n  p {\n    margin: 0;\r\n  }\n}\r\n\r\n// copyright\r\n.footer__copyright {\n  margin: 0;\r\n\n  font-size: 15px;\r\n  text-align: center;\r\n\r\n  p {\n    margin: 0 0 10px 0;\r\n  }\n}\r\n\r\n// poznámka\r\n.footer__note {\n  max-width: 950px;\r\n  margin: 0 auto;\r\n\n  font-size: 12px;\r\n  line-height: 1.3;\r\n  text-align: center;\r\n}\r\n", "// chybo<PERSON><PERSON> hl<PERSON><PERSON>\r\n.alert {\n  padding: 16px 0 15px 0;\r\n\n  color: $color_white;\r\n  font-size: 18px;\r\n  line-height: 1.2;\r\n\n  background-color: $color_main;\r\n\r\n  @media (min-width: $mqmd) {\n    font-size: 20px;\r\n    line-height: 1;\r\n  }\r\n\r\n  a {\n    color: $color_white;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_white;\r\n    }\n  }\r\n\r\n  strong {\n    font-weight: 600;\r\n  }\r\n\r\n  .icon {\n    display: none;\r\n\n    width: 35px;\r\n    margin-right: 15px;\r\n\n    color: $color_white;\r\n\r\n    @media (min-width: $mqsm) {\n      display: inline-block;\r\n    }\r\n\r\n    &.icon--delete {\n      position: absolute;\r\n      top: 12px;\r\n      right: 0;\r\n\n      display: inline-block;\r\n\n      width: 12px;\r\n\n      cursor: pointer;\r\n\r\n      &:hover,\n      &:focus {\n        opacity: 0.8;\r\n      }\n    }\n  }\r\n\r\n  .container {\n    position: relative;\r\n\n    padding-right: 40px;\r\n  }\r\n\r\n  // potvrzující hláš<PERSON>\r\n  &.is-success {\n    background-color: $color_success;\r\n  }\r\n\r\n  // výstražná hláška\r\n  &.is-danger {\n    background-color: $color_danger;\r\n  }\r\n\r\n  // informační hlá<PERSON>\r\n  &.is-info {\n    color: $color_black;\r\n\n    background: $color_main;\r\n    background-image: $gradient_gold;\r\n\r\n    a,\n    .icon {\n      color: $color_black;\r\n    }\n  }\n}\r\n", "// v<PERSON><PERSON>\r\n.article {\n  text-decoration: none;\r\n\r\n  h3 {\n    margin-bottom: 15px;\r\n  }\n}\r\n\r\n// v<PERSON><PERSON>ř<PERSON>\r\n.article--wide {\n  display: flex;\r\n  flex-wrap: wrap;\r\n\n  max-width: 930px;\r\n  margin: 0 auto 25px auto;\r\n\r\n  @media (min-width: $mqsm) {\n    flex-wrap: nowrap;\r\n\n    margin-bottom: 45px;\r\n  }\r\n\r\n  .article__content {\n    @media (min-width: $mqsm) {\n      padding: 10px 10px 10px 20px;\r\n    }\r\n\r\n    @media (min-width: $mqmd) {\n      padding-left: 45px;\r\n    }\n  }\n}\r\n\r\n// malá verze (do našeptávače)\r\n.article--small {\n  display: flex;\r\n  align-items: center;\r\n\r\n  & + & {\n    margin-top: 12px;\r\n  }\r\n\r\n  h3 {\n    margin: 0;\r\n\n    font-size: 16px;\r\n\r\n    strong {\n      display: block;\r\n\n      color: $color_main;\r\n      font-size: 12px;\r\n    }\n  }\r\n\r\n  .article__image {\n    flex: 0 0 100px;\r\n  }\r\n\r\n  .article__content {\n    padding: 0 0 0 17px;\r\n  }\n}\r\n\r\n// obr<PERSON>zek\r\n.article__image {\n  position: relative;\r\n\n  overflow: hidden;\r\n\n  margin: 0;\r\n\n  border-radius: $radius;\r\n\r\n  @media (min-width: $mqmd) {\n    flex: 1 0 auto;\r\n  }\r\n\r\n  img {\n    display: block;\r\n\n    width: 100%;\r\n    height: 100%;\r\n\n    object-fit: cover;\r\n  }\n}\r\n\r\n// informace (rubrika, autor)\r\n.article__meta {\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n\n  display: flex;\r\n  align-items: flex-end;\r\n\n  margin: 0;\r\n  padding: 10px 23px;\r\n\n  color: $color_white;\r\n  font-size: 14px;\r\n\n  background-image: linear-gradient( 180deg, rgba( 0, 0, 0, 0 ) 0%, rgba( 0, 0, 0, 0.47 ) 100% );\r\n\r\n  strong {\n    display: inline-block;\r\n\n    margin-right: 20px;\r\n\n    color: $color_main;\r\n    text-transform: uppercase;\r\n  }\n}\r\n\r\n// obsahová část\r\n.article__content {\n  display: flex;\r\n  flex: 0 1 auto;\r\n  flex-direction: column;\r\n\n  padding-top: 20px;\r\n}\r\n\r\n// perex\r\n.article__description {\n  margin-bottom: 20px;\r\n\n  color: $color_gray;\r\n  font-size: 15px;\r\n  line-height: 1.53;\r\n}\r\n\r\n// odkaz\r\n.article__link {\n  margin-top: auto;\r\n  margin-bottom: 0;\r\n}\r\n", "// detail článku\r\n.article-detail {\n  margin-top: -10px;\r\n\r\n  @media (min-width: $mqsm) {\n    margin-top: -220px;\r\n  }\n}\r\n\r\n// hlav<PERSON><PERSON><PERSON> + obr<PERSON><PERSON><PERSON>\r\n.article-detail__head {\n  position: relative;\r\n\n  margin: 0;\r\n\n  border-radius: $radius;\r\n  background: $color_black;\r\n\r\n  @media (min-width: $mqsm) {\n    overflow: hidden;\r\n  }\r\n\r\n  img {\n    display: block;\r\n  }\n}\r\n\r\n// informace (rubrika, autor)\r\n.article-detail__meta {\n  margin: 0;\r\n  padding: 20px;\r\n\n  color: $color_white;\r\n  font-size: 15px;\r\n\n  border-radius: $radius;\r\n  background-image: linear-gradient( 180deg, rgba( 0, 0, 0, 0 ) 0%, rgba( 0, 0, 0, 0.47 ) 100% );\r\n\r\n  @media (min-width: $mqsm) {\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n\n    display: flex;\r\n    align-items: flex-end;\r\n    justify-content: space-between;\r\n\n    padding: 30px 40px;\r\n  }\r\n\r\n  h1 {\n    margin: 0 0 15px 0;\r\n\n    font-size: 25px;\r\n\r\n    @media (min-width: $mqsm) {\n      margin: 0 10px 0 0;\r\n\n      font-size: 40px;\r\n    }\r\n\r\n    small {\n      display: block;\r\n\n      color: $color_main;\r\n      font-size: 14px;\r\n      font-weight: 700;\r\n      text-transform: uppercase;\r\n    }\n  }\r\n\r\n  p {\n    margin-bottom: 10px;\r\n\r\n    @media (min-width: $mqsm) {\n      text-align: center;\r\n    }\r\n\r\n    strong {\n      font-size: 21px;\r\n      font-weight: 600;\r\n      line-height: 1;\r\n    }\r\n\r\n    .avatar {\n      margin: 0 auto 10px auto;\r\n\r\n      @media (max-width: $mqsm - 1px) {\n        display: none;\r\n      }\n    }\n  }\n}\r\n\r\n// obsahová část\r\n.article-detail__content {\n  padding: 20px 10px;\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 40px;\r\n  }\r\n\r\n  .product--top {\n    margin-bottom: 40px;\r\n\r\n    .product__content {\n      padding: 0 0 10px 0;\r\n\r\n      p {\n        @media (min-width: $mqsm) {\n          max-width: 350px;\r\n        }\n      }\n    }\n  }\n}\r\n\r\n// hodnocení\r\n.article-detail__rating {\n  margin-top: 20px;\r\n  padding: 15px;\r\n\n  color: $color_gray;\r\n  font-size: 15px;\r\n  text-align: center;\r\n\n  background-color: $color_white;\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 40px;\r\n  }\r\n\r\n  ul,\n  li {\n    margin: 0;\r\n    padding: 0;\r\n\n    list-style-type: none;\r\n  }\r\n\r\n  li {\n    display: inline-block;\r\n\n    width: 49%;\r\n    padding-bottom: 15px;\r\n\r\n    @media (min-width: $mqsm) {\n      width: 20%;\r\n      padding-bottom: 0;\r\n    }\r\n\r\n    strong {\n      display: block;\r\n\n      font-size: 25px;\r\n      line-height: 1;\r\n    }\n  }\n}\r\n\r\n// hvězdičky\r\n.article-detail__stars {\n  margin: 0;\r\n\n  font-size: 18px;\r\n\r\n  @media (min-width: $mqsm) {\n    margin-top: 30px;\r\n  }\r\n\r\n  strong {\n    font-size: 33px;\r\n    line-height: 1;\r\n  }\r\n\r\n  .star {\n    margin: 8px 2px 0 2px;\r\n  }\n}\r\n\r\n// autor - detailnější info\r\n.article-detail__author {\n  margin: 40px 0;\r\n  padding: 20px;\r\n\n  font-size: 16px;\r\n  line-height: 1.5;\r\n\n  background-color: #eee;\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 40px;\r\n  }\r\n\r\n  & > p {\n    margin-bottom: 0;\r\n\n    line-height: 1.5;\r\n  }\r\n\r\n  .contact-box {\n    flex-wrap: wrap;\r\n\n    margin-bottom: 10px;\r\n\r\n    p {\n      max-width: 100%;\r\n    }\r\n\r\n    strong {\n      font-size: 25px;\r\n      font-weight: 600;\r\n    }\n  }\n}\r\n", "// fotografie lidí\r\n.avatar {\n  display: block;\r\n  overflow: hidden;\r\n\n  width: 110px;\r\n  height: 110px;\r\n  padding: 0;\r\n\n  border-radius: 50%;\r\n\r\n  img {\n    width: 100%;\r\n    height: auto;\r\n  }\n}\r\n\r\n// mal<PERSON> verze (např. do poradny)\r\n.avatar--small {\n  width: 35px;\r\n  height: 35px;\r\n}\r\n", "// téma/štítek/výrobce\r\n.badge {\n  display: inline-block;\r\n\n  padding: 5px 12px;\r\n\n  color: #9e9e9e;\r\n  font-size: 13px;\r\n  font-weight: 600;\r\n  text-decoration: none;\r\n\n  border: 2px solid #9e9e9e;\r\n  border-radius: $radius;\r\n\r\n  &.is-active {\n    color: $color_white;\r\n\n    border-color: $color_main;\r\n  }\n}\r\n\r\n// hover pouze pro odkazy\r\na.badge {\n  &:hover,\n  &:focus {\n    color: $color_white;\r\n\n    border-color: $color_main;\r\n  }\n}\r\n\r\n// inverzní varianta\r\n.badge--inverse {\n  color: $color_white;\r\n\n  background-color: #9e9e9e;\r\n}\r\n", "// košík\n.basket {\n  position: relative;\n\n  font-size: 18px;\n  font-weight: 600;\n\n  &:after {\n    content: '';\n\n    position: absolute;\n    bottom: -30px;\n    left: 13px;\n\n    display: block;\n    display: none;\n\n    width: 20px;\n    height: 20px;\n\n    background-color: $color_gray_dark;\n\n    transform: rotate( 45deg );\n  }\n\n  @media (min-width: $mqsm) {\n    &:hover,\n    &:focus {\n      .basket__detail,\n      &:after {\n        display: block;\n      }\n\n      // ošetření košíku v objednávce\n      .header--simple &:after {\n        display: none;\n      }\n    }\n  }\n\n  a {\n    color: $color_black;\n    text-decoration: none;\n  }\n\n  small {\n    font-weight: 400;\n  }\n\n  .icon {\n    width: 38px;\n    margin-right: 15px;\n  }\n\n  .count {\n    position: absolute;\n    top: -5px;\n    left: 25px;\n  }\n\n  // scroolbar\n  ::-webkit-scrollbar {\n    width: 10px;\n  }\n\n  ::-webkit-scrollbar-track {\n    border-radius: $radius;\n    background-color: $color_black;\n  }\n\n  ::-webkit-scrollbar-thumb {\n    border: 3px solid $color_black;\n    border-radius: $radius;\n    background: $color_main;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background-color: $color_main;\n  }\n}\n\n// cena\n.basket__price {\n  display: none;\n\n  @media (min-width: $mq_menu) {\n    display: inline-block;\n  }\n}\n\n// obsah košíku - wrapper\n.basket__detail {\n  position: absolute;\n  right: 0;\n  z-index: $index_modal - 1;\n\n  display: none;\n\n  width: 560px;\n  padding-top: 15px;\n\n  color: $color_white;\n}\n\n// obsah košíku - obsah\n.basket__content {\n  overflow: auto;\n\n  width: 100%;\n  max-height: calc( 100vh - 70px - 220px );\n  padding: 20px 10px 20px 25px;\n\n  border-top-left-radius: $radius;\n  border-top-right-radius: $radius;\n  background-color: $color_gray_dark;\n\n  @media (min-width: $mq_menu) {\n    max-height: calc( 100vh - 125px - 220px );\n  }\n}\n\n// obsah košíku - patička\n.basket__footer {\n  width: 100%;\n  padding: 20px 25px 30px 25px;\n\n  border-bottom-right-radius: $radius;\n  border-bottom-left-radius: $radius;\n  background-color: #272727;\n\n  p {\n    margin: 15px 0 0 0;\n  }\n\n  .order-benefits,\n  .order-benefits__info {\n    margin: 0;\n    padding: 0;\n  }\n}\n\n// celková cena\n.basket__sum {\n  padding: 5px 0 10px 0;\n\n  font-size: 14px;\n  font-weight: 400;\n  text-align: right;\n\n  strong {\n    display: inline-block;\n\n    padding: 0 44px 0 20px;\n\n    font-size: 19px;\n  }\n}\n", "// drobečková navigace\r\n.breadcrumb {\n  color: $color_gray_light;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n\r\n  @media (min-width: $mqmd) {\n    line-height: 1;\r\n  }\r\n\r\n  a {\n    margin-right: 5px;\r\n\n    color: $color_white;\r\n    text-decoration: none;\r\n\r\n    &:after {\n      content: '';\r\n\n      display: inline-block;\r\n\n      width: 5px;\r\n      height: 8px;\r\n      margin-left: 5px;\r\n\n      background: url( ../img/arrow.svg ) center center no-repeat;\r\n    }\r\n\r\n    &:hover,\n    &:focus {\n      text-decoration: underline;\r\n    }\n  }\r\n\r\n  .icon {\n    width: 17px;\r\n\n    color: $color_white;\r\n  }\n}\r\n", "// tlačítko\n.btn,\na.btn {\n  display: inline-block;\n\n  padding: 13px 22px;\n\n  color: $color_black;\n  font: inherit;\n  font-size: 13px;\n  font-weight: 600;\n  line-height: 1;\n  text-align: center;\n  text-decoration: none;\n  text-transform: uppercase;\n\n  border: none;\n  border-radius: $radius;\n  background-color: $color_main;\n\n  cursor: pointer;\n\n  &:hover,\n  &:focus {\n    background-image: $gradient_gold;\n  }\n\n  .icon {\n    width: 11px;\n    margin: -4px 0 0 10px;\n  }\n\n  .icon--next {\n    display: none;\n\n    @media (min-width: $mqxs) {\n      display: inline-block;\n    }\n  }\n}\n\n// velké tlačítko\n.btn--big,\na.btn--big {\n  font-size: 18px;\n\n  @media (min-width: $mqxs) {\n    padding: 15px 25px;\n  }\n}\n\n// potvrzující tlačítko\n.btn--success,\na.btn--success {\n  color: $color_white;\n\n  background-color: $color_success;\n\n  &:hover,\n  &:focus {\n    background-color: darken( $color_success, 10% );\n    background-image: none;\n  }\n\n  .icon {\n    color: $color_white;\n  }\n}\n\n// chybové tla<PERSON>ko\n.btn--danger,\na.btn--danger {\n  color: $color_white;\n\n  background-color: $color_danger;\n\n  &:hover,\n  &:focus {\n    background-color: darken( $color_danger, 10% );\n    background-image: none;\n  }\n\n  .icon {\n    color: $color_white;\n  }\n}\n\n// pomocné tlačítko\n.btn--helper,\na.btn--helper {\n  color: $color_white;\n\n  background-color: $color_gray;\n\n  &:hover,\n  &:focus {\n    color: $color_black;\n\n    background-color: $color_white;\n    background-image: none;\n  }\n}\n\n// jemnější verze tlačítka\n.btn--soft,\na.btn--soft {\n  background-color: $color_main_light;\n\n  &:hover,\n  &:focus {\n    background-color: $color_main;\n    background-image: none;\n  }\n}\n\n// login tlačítko (externí služby)\n.btn--login,\na.btn--login {\n  width: 100%;\n  max-width: 410px;\n\n  font-size: 15px;\n\n  @media (min-width: $mqxs) {\n    padding: 15px 25px;\n\n    font-size: 18px;\n  }\n\n  &:hover,\n  &:focus {\n    background-color: $color_black;\n    background-image: none;\n  }\n\n  .icon {\n    width: 22px;\n    margin-right: 5px;\n\n    color: $color_white;\n  }\n\n  & + & {\n    margin-top: 10px;\n  }\n}\n\n// login - seznam\n.btn--seznam,\na.btn--seznam {\n  color: $color_white;\n\n  background-color: #c00;\n}\n\n// login - facebook\n.btn--facebook,\na.btn--facebook {\n  color: $color_white;\n\n  background-color: #0866ff;\n}\n\n// login - google\n.btn--google,\na.btn--google {\n  background-color: #f7f7f7;\n\n  &:hover,\n  &:focus {\n    background-color: #dfdfdf;\n    background-image: none;\n  }\n}\n\n// login - apple\n.btn--apple,\na.btn--apple {\n  color: $color_white;\n\n  background-color: #6e6e73;\n}\n", "// výpis kategor<PERSON>\n.category {\n  display: flex;\n  align-items: center;\n\n  margin: -5px 0;\n  padding: 12px 15px;\n\n  color: $color_white;\n  line-height: 1.2;\n  text-decoration: none;\n\n  border-radius: $radius;\n  background-color: #272727;\n\n  @media (min-width: $mqsm) {\n    margin: 0;\n  }\n\n  &:hover,\n  &:focus {\n    color: $color_white;\n\n    background-color: $color_gray;\n  }\n}\n\n// obrázek\n.category__image {\n  overflow: hidden;\n\n  width: 40px;\n  height: 40px;\n  margin-right: 15px;\n\n  border-radius: $radius;\n  background-color: $color_white;\n}\n", "// box člověk + kontakty\n.contact-box {\n  display: flex;\n  align-items: center;\n\n  color: $color_gray;\n  font-size: 15px;\n\n  p {\n    max-width: 390px;\n    margin: 0;\n\n    line-height: 1.3;\n  }\n\n  a {\n    font-size: 18px;\n\n    @media (min-width: $mqsm) {\n      font-size: 22px;\n    }\n  }\n\n  strong {\n    display: inline-block;\n\n    margin-bottom: 5px;\n\n    color: $color_black;\n    font-size: 19px;\n  }\n\n  small {\n    display: inline-block;\n\n    padding-left: 16px;\n\n    color: #acacac;\n    font-size: 11px;\n  }\n\n  .avatar {\n    margin-right: 5px;\n\n    @media (min-width: 360px) {\n      margin-right: 15px;\n    }\n\n    @media (min-width: $mqsm) {\n      margin-right: 32px;\n    }\n  }\n\n  .phone {\n    &:before {\n      top: 4px;\n    }\n  }\n}\n\n// verze pro výpis (např. kontakty)\n.contact-box--list {\n  a {\n    font-size: 15px;\n  }\n}\n\n// osazení pokud jsou za sebou\n.contact-box + .contact-box {\n  margin-top: 25px;\n}\n", "// obsah<PERSON> hlavička\n.content-header {\n  padding: 30px 0;\n\n  color: $color_white;\n\n  background-color: $color_gray_dark;\n\n  @media (min-width: $mqmd) {\n    padding-bottom: 60px;\n  }\n\n  h1 {\n    margin: 25px 0 0 0;\n\n    line-height: 1.3;\n  }\n\n  ul {\n    margin-top: 15px;\n    padding: 0;\n\n    list-style-type: none;\n  }\n\n  li {\n    margin: 0;\n  }\n\n  .container {\n    position: relative;\n  }\n\n  .form {\n    display: none;\n\n    @media (min-width: $mqmd) {\n      display: block;\n      float: right;\n\n      width: 100%;\n      max-width: 640px;\n      margin-top: 30px;\n    }\n\n    textarea {\n      height: 140px;\n    }\n  }\n}\n\n// detail článku\n.content-header--detail {\n  @media (min-width: $mqsm) {\n    min-height: 300px;\n  }\n}\n\n// detail produktu\n.content-header--product {\n  @media (min-width: $mqlg) {\n    min-height: 320px;\n    padding-bottom: 40px;\n\n    h1 {\n      margin-top: 10px;\n      margin-left: 630px;\n    }\n\n    .content-header__description {\n      margin-left: 630px;\n    }\n  }\n}\n\n// popis\n.content-header__description {\n  margin: 15px 0 0 0;\n\n  color: $color_gray_light;\n  font-size: 17px;\n  line-height: 1.35;\n\n  a {\n    color: $color_gray_light;\n\n    &:hover,\n    &:focus {\n      color: $color_white;\n    }\n  }\n\n  .center & {\n    margin-right: auto;\n    margin-left: auto;\n  }\n}\n\n// kontakty\n.content-header__contact {\n  margin-top: 20px;\n\n  color: $color_gray_light;\n  font-size: 17px;\n  line-height: 1.3;\n\n  @media (min-width: $mqmd) {\n    margin-top: 105px;\n    margin-bottom: 40px;\n  }\n\n  & + & {\n    margin-top: 0;\n  }\n\n  a {\n    display: block;\n\n    color: $color_white;\n    font-size: 30px;\n    font-weight: 700;\n  }\n\n  small {\n    position: relative;\n\n    font-size: 14px;\n  }\n}\n\n// vyhledávání (v poradně)\n.content-header__search {\n  margin: 0;\n\n  @media (min-width: $mqsm) {\n    display: flex;\n    justify-content: space-between;\n\n    margin-bottom: -30px;\n  }\n\n  .form-search {\n    width: 100%;\n    margin: 0;\n    padding-top: 25px;\n\n    @media (min-width: $mqsm) {\n      text-align: right;\n    }\n\n    input {\n      display: inline-block;\n\n      max-width: 440px;\n\n      border-color: $color_white;\n\n      @media (min-width: $mqsm) {\n        margin-left: 20px;\n      }\n    }\n\n    .btn {\n      margin-bottom: 10px;\n      padding: 16px 25px;\n\n      font-size: 18px;\n    }\n  }\n}\n", "// počet\r\n.count {\n  display: inline-block;\r\n\n  min-width: 22px;\r\n  height: 22px;\r\n  padding: 4px 3px 0 3px;\r\n\n  color: $color_black;\r\n  font-size: 13px;\r\n  font-weight: 600;\r\n  line-height: 1;\r\n  text-align: center;\r\n\n  border-radius: 50%;\r\n  background-color: $color_main;\r\n\r\n  // potvrzující\r\n  &.is-success {\n    color: $color_white;\r\n\n    background-color: $color_success;\r\n  }\r\n\r\n  // výstražný\r\n  &.is-danger {\n    color: $color_white;\r\n\n    background-color: $color_danger;\r\n  }\n}\r\n", "// doprava a platba (samostatná strana)\n.delivery {\n  display: flex;\n  flex-wrap: wrap;\n\n  margin-top: 20px;\n  padding: 15px;\n\n  background-color: $color_white;\n\n  @media (min-width: $mqsm) {\n    flex-wrap: nowrap;\n  }\n\n  p {\n    margin: 0;\n  }\n\n  p + p {\n    margin-top: 10px;\n  }\n\n  & + h2 {\n    margin-top: 40px;\n  }\n}\n\n// ikona\n.delivery__icon {\n  flex: 0 0 80px;\n\n  img {\n    display: block;\n\n    margin: 0 auto;\n  }\n}\n\n// obsahová část\n.delivery__content {\n  width: 100%;\n  padding: 10px 0;\n\n  @media (min-width: $mqsm) {\n    padding: 10px 20px;\n  }\n\n  h3 {\n    margin: 0;\n\n    font-size: 20px;\n    font-weight: 500;\n\n    img {\n      margin: 0 0 0 10px;\n\n      vertical-align: middle;\n    }\n  }\n\n  h3 + p {\n    margin-top: 10px;\n  }\n\n  p {\n    max-width: 800px;\n\n    color: $color_gray;\n\n    @media (min-width: $mqsm) {\n      line-height: 1.63;\n    }\n  }\n}\n\n// cena\n.delivery__price {\n  font-weight: 500;\n  line-height: 1.3;\n\n  @media (min-width: $mqsm) {\n    text-align: right;\n  }\n\n  @media (min-width: $mqlg) {\n    line-height: 1.8;\n    white-space: nowrap;\n  }\n\n  strong {\n    font-size: 19px;\n    white-space: nowrap;\n  }\n}\n", "// sleva\r\n.discount {\n  display: inline-block;\r\n  flex: 0 0 48px;\r\n\n  width: 48px;\r\n  height: 48px;\r\n  padding-top: 15px;\r\n\n  color: $color_black;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  line-height: 1;\r\n  text-align: center;\r\n\n  border-radius: 50%;\r\n  background-color: $color_main;\r\n\r\n  // potvrzující\r\n  &.is-success {\n    color: $color_white;\r\n\n    background-color: $color_success;\r\n  }\r\n\r\n  // výstražný\r\n  &.is-danger {\n    color: $color_white;\r\n\n    background-color: $color_danger;\r\n  }\n}\r\n", "// p<PERSON><PERSON><PERSON><PERSON>č filtrů\n.filter-switch {\n  @media (min-width: $mqlg) {\n    display: none;\n  }\n\n  em {\n    font-style: normal;\n  }\n\n  .icon {\n    width: 20px;\n  }\n}\n", "// v<PERSON><PERSON> c<PERSON>l<PERSON>\r\n.goal {\n  h3 {\n    margin-bottom: 5px;\r\n\n    font-size: 20px;\r\n    text-transform: uppercase;\r\n  }\r\n\r\n  ul,\n  li {\n    margin: 0;\r\n    padding: 0;\r\n\n    list-style-type: none;\r\n  }\r\n\r\n  a {\n    color: #d8d8d8;\r\n    font-size: 15px;\r\n    text-decoration: none;\r\n    text-transform: uppercase;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_main;\r\n    }\n  }\n}\r\n\r\n// obrázek\r\n.goal__image {\n  position: relative;\r\n\n  overflow: hidden;\r\n\n  margin: 0;\r\n\n  border-radius: $radius;\r\n\r\n  &:hover,\n  &:focus {\n    img {\n      transform: scale( 1.07 );\r\n    }\n  }\r\n\r\n  img {\n    display: block;\r\n\n    transition: all 0.2s;\r\n  }\n}\r\n\r\n// odkaz\r\n.goal__content {\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  z-index: $index_page + 1;\r\n\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-end;\r\n\n  margin: 0;\r\n  padding: 20px 30px;\r\n\n  background-image: linear-gradient( 180deg, rgba( 0, 0, 0, 0 ) 0%, rgba( 0, 0, 0, 0.6 ) 57%, rgba( 0, 0, 0, 0.58 ) 100% );\r\n}\r\n", "// hlavi<PERSON><PERSON> - horní část\r\n.header-top {\n  display: none;\r\n\n  padding: 8px 0;\r\n\n  font-size: 14px;\r\n\n  background-color: #f6f6f6;\r\n\r\n  @media (min-width: $mq_menu) {\n    display: block;\r\n  }\r\n\r\n  p {\n    margin: 0;\r\n\n    line-height: 1;\r\n  }\r\n\r\n  a {\n    text-decoration: none;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_black;\r\n    }\n  }\r\n\r\n  .container {\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  // provozní doba\r\n  .phone {\n    color: $color_gray;\r\n    font-size: 12px;\r\n    line-height: 12px;\r\n\r\n    &:before {\n      top: 4px;\r\n    }\n  }\n}\r\n\r\n// navigace\r\n.header-top__nav {\n  @media (min-width: $mqlg) {\n    flex: 1 1 33%;\r\n  }\r\n\r\n  ul,\n  li {\n    margin: 0;\r\n    padding: 0;\r\n\n    list-style-type: none;\r\n\n    line-height: 1;\r\n  }\r\n\r\n  li {\n    display: inline-block;\r\n\r\n    &:hover,\n    &:focus {\n      ul {\n        display: block;\r\n      }\n    }\r\n\r\n    &.has-submenu {\n      margin-right: 12px;\r\n\n      background: url( ../img/nav.svg ) center right no-repeat;\r\n      background-size: 8px auto;\r\n\r\n      &:hover,\n      &:focus {\n        background-image: url( ../img/nav-open.svg );\r\n      }\n    }\n  }\r\n\r\n  li ul {\n    position: absolute;\r\n    z-index: $index_modal + 2;\r\n\n    display: none;\r\n\n    padding-top: 5px;\r\n\r\n    li {\n      display: block;\r\n\n      padding: 5px 15px;\r\n\n      background-color: #f6f6f6;\r\n\r\n      &:first-child {\n        padding-top: 10px;\r\n      }\r\n\r\n      &:last-child {\n        padding-bottom: 10px;\r\n      }\n    }\n  }\r\n\r\n  a {\n    margin-right: 17px;\r\n\n    color: $color_gray;\r\n  }\n}\r\n\r\n// kontakt\r\n.header-top__contact {\n  display: none;\r\n  align-items: center;\r\n  justify-content: center;\r\n\n  margin: 0;\r\n\n  font-size: 13px;\r\n\r\n  @media (min-width: $mqsm) {\n    display: flex;\r\n  }\r\n\r\n  @media (min-width: $mqlg) {\n    flex: 1 1 33%;\r\n  }\r\n\r\n  a {\n    margin: 0 6px 0 15px;\r\n\n    font-size: 15px;\r\n    font-weight: 500;\r\n  }\n}\r\n\r\n// přepínač jazyků\r\n.header-top__lang {\n  text-align: right;\r\n\r\n  @media (min-width: $mqlg) {\n    flex: 1 1 33%;\r\n  }\r\n\r\n  a {\n    opacity: 0.5;\r\n\r\n    &:hover,\n    &:focus,\n    &.is-active {\n      opacity: 1;\r\n    }\n  }\n}\r\n", "// nápověda/poznámka\r\n.help {\n  display: inline-block;\r\n\n  width: 16px;\r\n  height: 16px;\r\n  margin: 2px;\r\n\n  vertical-align: middle;\r\n\n  background: url( ../img/help.svg ) center center no-repeat;\r\n}\r\n", "// upoutávka/upozornění\r\n.hey {\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n\n  margin: 20px 0;\r\n  padding: 15px;\r\n\n  font-size: 19px;\r\n\n  background-color: $color_main_light;\r\n\r\n  @media (min-width: $mqmd) {\n    justify-content: space-between;\r\n  }\r\n\r\n  p {\n    margin: 10px;\r\n\n    line-height: 1.3;\r\n\r\n    @media (max-width: $mqsm - 1px) {\n      width: 100%;\r\n    }\n  }\r\n\r\n  a {\n    font-weight: 600;\r\n  }\r\n\r\n  .icon {\n    width: 25px;\r\n  }\n}\r\n\r\n// centrováno\r\n.hey--center {\n  justify-content: center;\r\n\r\n  .hey__info {\n    flex: 0 0 auto;\r\n  }\n}\r\n\r\n// vertikální verze\r\n.hey--vertical {\n  justify-content: center;\r\n\n  text-align: center;\r\n\r\n  @media (min-width: $mqmd) {\n    flex-direction: column;\r\n  }\r\n\r\n  a {\n    display: block;\r\n  }\n}\r\n\r\n// text\r\n.hey__info {\n  flex: 1 1 auto;\r\n}\r\n\r\n// ikona\r\n.hey__icon {\n  @media (max-width: $mqsm - 1px) {\n    display: none;\r\n  }\n}\r\n", "// instagram\r\n.instagram {\n  padding: 30px 0;\r\n\n  color: $color_white;\r\n  font-size: 18px;\r\n  font-weight: 700;\r\n\n  background-color: #272727;\r\n\r\n  @media (min-width: $mqmd) {\n    padding: 60px 0 75px 0;\r\n  }\r\n\r\n  a {\n    display: block;\r\n    overflow: hidden;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_gray_back;\r\n\r\n      img {\n        transform: scale( 1.1 );\r\n      }\n    }\n  }\r\n\r\n  img {\n    display: block;\r\n\n    border-radius: $radius;\r\n\n    transition: all 0.2s;\r\n  }\r\n\r\n  .col--6 {\n    @media (max-width: $mqmd - 1px) {\n      flex: 1 1 33%;\r\n\n      padding: calc( $layout-gap / 2 );\r\n    }\n  }\n}\r\n\r\n// horní část\r\n.instagram__header {\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n\n  margin-bottom: 15px;\r\n\r\n  @media (min-width: $mqsm) {\n    justify-content: space-between;\r\n  }\r\n\r\n  h2 {\n    @media (min-width: $mqsm) {\n      margin: 0;\r\n\n      font-size: 30px;\r\n    }\n  }\r\n\r\n  p {\n    margin: 0;\r\n  }\r\n\r\n  a {\n    display: inline-flex;\r\n    align-items: center;\r\n    flex-direction: column;\r\n\n    width: 48%;\r\n\n    color: $color_white;\r\n    font-size: 15px;\r\n    text-align: center;\r\n\r\n    @media (min-width: $mqmd) {\n      display: inline-block;\r\n\n      width: auto;\r\n      margin-left: 50px;\r\n\n      font-size: 18px;\r\n    }\n  }\r\n\r\n  .icon {\n    width: 22px;\r\n    margin-right: 10px;\r\n  }\n}\r\n", "// odkaz (<PERSON><PERSON><PERSON>, <PERSON>ky...)\n.link {\n  font-weight: 700;\n  text-decoration: underline;\n\n  @media (min-width: $mqsm) {\n    font-size: 18px;\n  }\n\n  &:after {\n    content: '';\n\n    display: inline-block;\n\n    width: 22px;\n    height: 22px;\n    margin-top: -3px;\n    margin-left: 6px;\n\n    vertical-align: middle;\n\n    background: url( ../img/link.svg ) center center no-repeat;\n  }\n}\n\n// inverzní varianta\n.link--inverse {\n  color: $color_white;\n\n  &:after,\n  &.link--back:before {\n    background-image: url( ../img/link-inverse.svg );\n  }\n\n  &:hover,\n  &:focus {\n    color: $color_gray_back;\n  }\n}\n\n// nahoru\n.link--up {\n  &:after {\n    transform: rotate( -90deg );\n  }\n}\n\n// dolů\n.link--down {\n  &:after {\n    transform: rotate( 90deg );\n  }\n}\n\n// zpět\n.link--back {\n  &:after {\n    display: none;\n  }\n\n  &:before {\n    content: '';\n\n    display: inline-block;\n\n    width: 22px;\n    height: 22px;\n    margin-top: -3px;\n    margin-right: 10px;\n\n    vertical-align: middle;\n\n    background: url( ../img/link.svg ) center center no-repeat;\n\n    transform: rotate( 180deg );\n  }\n}\n", "// přihlášení\r\n.login {\n  position: relative;\r\n\n  margin-right: 10px;\r\n\r\n  @media (min-width: $mq_menu) {\n    display: block;\r\n\n    margin-right: 50px;\r\n  }\r\n\r\n  &:after {\n    content: '';\r\n\n    position: absolute;\r\n    bottom: -30px;\r\n    left: 5px;\r\n\n    display: none;\r\n\n    width: 20px;\r\n    height: 20px;\r\n\n    background-color: $color_gray_dark;\r\n\n    transform: rotate( 45deg );\r\n  }\r\n\r\n  .count {\n    position: absolute;\r\n    top: -8px;\r\n    left: 18px;\r\n\n    padding-top: 3px;\r\n\r\n    .icon {\n      width: 11px;\r\n\n      color: $color_white;\r\n    }\n  }\r\n\r\n  &.is-open {\n    @media (min-width: $mq_menu) {\n      .login__detail,\n      &:after {\n        display: block;\r\n      }\n    }\n  }\r\n\r\n  &.is-logged {\n    h2 {\n      color: $color_main;\r\n    }\r\n\r\n    .icon--user {\n      color: $color_success;\r\n    }\r\n\r\n    .login__detail {\n      width: 500px;\r\n    }\n  }\n}\r\n\r\n// login - wrapper\r\n.login__detail {\n  position: absolute;\r\n  right: -80px;\r\n  z-index: $index_modal - 1;\r\n\n  display: none;\r\n\n  width: 790px;\r\n  padding-top: 15px;\r\n\n  color: $color_white;\r\n}\r\n\r\n// login - obsah\r\n.login__content {\n  display: flex;\r\n\n  width: 100%;\r\n\n  border-radius: $radius;\r\n  background-color: $color_gray_dark;\r\n}\r\n\r\n// formulář\r\n.login__form {\n  flex: 1 1 45%;\r\n\n  padding: 40px 60px 40px 50px;\r\n\n  border-top-left-radius: $radius;\r\n  border-bottom-left-radius: $radius;\r\n  background-color: #272727;\r\n\r\n  p {\n    margin: 0;\r\n  }\r\n\r\n  p + p {\n    margin-top: 10px;\r\n  }\r\n\r\n  input {\n    width: 100%;\r\n  }\r\n\r\n  .form__helper {\n    color: $color_white;\r\n  }\r\n\r\n  .btn {\n    width: 100%;\r\n    margin-top: 10px;\r\n\n    text-align: center;\r\n  }\n}\r\n\r\n// výhody\r\n.login__benefits {\n  flex: 1 1 55%;\r\n\n  padding: 40px 30px 40px 50px;\r\n\r\n  h2 {\n    font-size: 23px;\r\n    font-weight: 600;\r\n    line-height: 1.3;\r\n  }\r\n\r\n  ul {\n    margin: 20px 0 20px 0;\r\n    padding: 0;\r\n\n    list-style-type: none;\r\n  }\r\n\r\n  li {\n    margin: 0;\r\n    padding: 3px 0 3px 25px;\r\n\n    font-size: 15px;\r\n\n    background: url( ../img/list.svg ) left center no-repeat;\r\n\r\n    a {\n      color: $color_white;\r\n      font-size: 16px;\r\n      text-decoration: underline;\r\n    }\n  }\r\n\r\n  p {\n    margin: 0;\r\n\r\n    a + a {\n      margin-left: 12px;\r\n    }\n  }\n}\r\n\r\n// zavření\r\n.login__close {\n  position: absolute;\r\n  top: 30px;\r\n  right: 15px;\r\n  z-index: $index_page + 1;\r\n\n  overflow: hidden;\r\n\n  width: 25px;\r\n  height: 25px;\r\n\n  cursor: pointer;\r\n\r\n  &:hover,\n  &:focus {\n    opacity: 0.7;\r\n  }\r\n\r\n  .icon {\n    width: 25px;\r\n\n    color: $color_white;\r\n  }\n}\r\n", "// výrobci (výpis)\n.manufacturer {\n  display: flex;\n  flex-wrap: wrap;\n\n  margin-top: 20px;\n  padding: 15px;\n\n  background-color: $color_white;\n\n  @media (min-width: $mqsm) {\n    flex-wrap: nowrap;\n  }\n\n  & + .section__anchor {\n    margin-top: 30px;\n  }\n}\n\n// logo\n.manufacturer__logo {\n  flex: 0 0 200px;\n\n  padding: 25px;\n\n  img {\n    display: block;\n\n    max-height: 150px;\n    margin: 0 auto;\n  }\n}\n\n// obsahová část\n.manufacturer__content {\n  width: 100%;\n  padding: 10px 0;\n\n  @media (min-width: $mqsm) {\n    padding: 10px 30px;\n  }\n\n  h3 {\n    font-weight: 500;\n  }\n\n  p {\n    color: $color_gray;\n\n    @media (min-width: $mqsm) {\n      line-height: 1.63;\n    }\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n}\n", "// modal okno\r\n.modal {\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  z-index: $index_modal;\r\n\n  display: none;\r\n  overflow: auto;\r\n\n  width: 100%;\r\n\n  background-color: rgba( 0, 0, 0, 0.8 );\r\n\r\n  &.is-open {\n    display: block;\r\n  }\n}\r\n\r\n// dotaz + hlídání\r\n.modal--question,\n.modal--watch {\n  .modal__body {\n    max-width: 530px;\r\n\r\n    @media (min-width: $mqsm) {\n      padding: 30px 40px;\r\n    }\n  }\r\n\r\n  .modal__close {\n    top: 15px;\r\n    right: 15px;\r\n\r\n    @media (min-width: $mqsm) {\n      top: 30px;\r\n      right: 40px;\r\n    }\n  }\n}\r\n\r\n// hlídání\r\n.modal--watch {\n  .form input {\n    width: 60%;\r\n  }\n}\r\n\r\n// varianty\r\n.modal--variants {\n  h2 {\n    margin: 10px 0 30px 0;\r\n\n    font-size: 23px;\r\n    text-align: center;\r\n  }\r\n\r\n  .product--top {\n    h3 {\n      font-size: 17px;\r\n    }\r\n\r\n    .product__meta {\n      .btn {\n        margin: 0;\r\n      }\r\n\r\n      .discount {\n        @media (max-width: $mqsm - 1px) {\n          display: none;\r\n        }\n      }\n    }\r\n\r\n    .store {\n      font-size: 13px;\r\n    }\r\n\r\n    .product__flavour {\n      right: 5px;\r\n      bottom: -2px;\r\n\n      width: 30px;\r\n      height: 30px;\r\n      padding: 3px;\r\n\r\n      @media (min-width: $mqsm) {\n        right: -2px;\r\n\n        width: 40px;\r\n        height: 40px;\r\n        padding: 3px;\r\n      }\n    }\n  }\n}\r\n\r\n// dostupnost\r\n.modal--availability {\n  @media (min-width: $mqsm) {\n    position: fixed;\r\n\n    overflow: auto;\r\n  }\r\n\r\n  h2 {\n    margin: 10px 0 30px 0;\r\n\n    font-size: 23px;\r\n    text-align: center;\r\n  }\r\n\r\n  .modal__body {\n    max-width: 530px;\r\n\r\n    @media (min-width: $mqsm) {\n      top: 50%;\r\n      left: 50%;\r\n\n      transform: translate( -50%, -50% );\r\n    }\n  }\n}\r\n\r\n// tmavá verze\r\n.modal--dark {\n  h2 {\n    margin: 10px 0 30px 0;\r\n\n    color: $color_white;\r\n    text-align: center;\r\n\r\n    .icon {\n      width: 30px;\r\n      margin-right: 5px;\r\n\n      color: $color_white;\r\n    }\n  }\r\n\r\n  .modal__body {\n    background-color: #272727;\r\n  }\r\n\r\n  .modal__close {\n    .icon {\n      color: $color_white;\r\n    }\n  }\r\n\r\n  .modal__products {\n    margin: 40px -20px -20px -20px;\r\n  }\r\n\r\n  .store {\n    font-size: 13px;\r\n  }\n}\r\n\r\n// tělo\r\n.modal__body {\n  position: relative;\r\n\n  width: 100%;\r\n  max-width: 920px;\r\n  padding: 15px;\r\n\n  background-color: $color_gray_back;\r\n\r\n  @media (min-width: $mqsm) {\n    top: 50px;\r\n    left: 50%;\r\n\n    padding: 20px;\r\n\n    transform: translateX( -50% );\r\n  }\n}\r\n\r\n// zavřít okno\r\n.modal__close {\n  position: absolute;\r\n  top: 20px;\r\n  right: 20px;\r\n\n  overflow: hidden;\r\n\n  width: 26px;\r\n  height: 26px;\r\n\n  cursor: pointer;\r\n\r\n  @media (min-width: $mqsm) {\n    width: 42px;\r\n    height: 42px;\r\n  }\r\n\r\n  &:hover,\n  &:focus {\n    opacity: 0.7;\r\n  }\r\n\r\n  .icon {\n    width: 26px;\r\n\r\n    @media (min-width: $mqsm) {\n      width: 42px;\r\n    }\n  }\n}\r\n\r\n// odeslání\r\n.modal__submit {\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n\r\n  @media (min-width: $mqsm) {\n    flex-wrap: nowrap;\r\n    justify-content: space-between;\r\n\n    margin: 25px 10px;\r\n  }\r\n\r\n  p {\n    margin: 15px;\r\n\r\n    @media (min-width: $mqsm) {\n      margin: 0;\r\n    }\n  }\r\n\r\n  .link {\n    font-size: 16px;\r\n  }\n}\r\n\r\n// podobné produkty\r\n.modal__products {\n  padding: 15px 25px 10px 25px;\r\n\n  background-color: $color_gray_back;\r\n\r\n  h3 {\n    margin-bottom: 5px;\r\n\n    font-size: 24px;\r\n  }\n}\r\n\r\n// dostupnost\r\n.modal__availability {\n  margin: 0;\r\n  padding: 0 10px;\r\n\n  list-style-type: none;\r\n\n  font-size: 20px;\r\n\r\n  .icon {\n    width: 12px;\r\n\n    color: $color_main;\r\n  }\r\n\r\n  .store {\n    font-size: 18px;\r\n  }\n}\r\n", "// nenu v patičce\r\n.nav-footer {\n  padding: 30px 10px;\r\n\n  color: $color_white;\r\n  font-size: 15px;\r\n\n  background-color: $color_gray_dark;\r\n\r\n  @media (min-width: $mqmd) {\n    padding: 70px 0;\r\n  }\r\n\r\n  h2 {\n    margin-bottom: 0;\r\n\n    color: $color_gray_light;\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    text-transform: uppercase;\r\n\r\n    @media (min-width: $mqsm) {\n      margin-bottom: 25px;\r\n\n      color: $color_white;\r\n    }\r\n\r\n    &:before {\n      @media (max-width: $mqsm - 1px) {\n        content: '';\r\n\n        display: inline-block;\r\n\n        width: 23px;\r\n        height: 23px;\r\n        margin: 0 10px 3px 0;\r\n\n        vertical-align: middle;\r\n\n        background: url( ../img/link-inverse.svg ) center center no-repeat;\r\n        background-size: 23px 23px;\r\n\n        opacity: 0.5;\r\n        transform: rotate( 90deg );\r\n      }\n    }\r\n\r\n    &.is-open {\n      margin-bottom: 5px;\r\n\n      color: $color_white;\r\n\r\n      &:before {\n        opacity: 1;\r\n        transform: rotate( -90deg );\r\n      }\n    }\n  }\r\n\r\n  ul,\n  li {\n    margin: 0;\r\n    padding: 0;\r\n\n    list-style-type: none;\r\n  }\r\n\r\n  ul {\n    display: none;\r\n\r\n    @media (min-width: $mqsm) {\n      display: block;\r\n    }\r\n\r\n    &.is-open {\n      display: block;\r\n    }\n  }\r\n\r\n  li {\n    line-height: 2.27;\r\n\r\n    @media (min-width: $mqsm) {\n      line-height: 2.67;\r\n    }\n  }\r\n\r\n  a {\n    color: #ccc;\r\n    text-decoration: none;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_main;\r\n    }\r\n\r\n    &.nav-footer__link {\n      text-decoration: underline;\r\n    }\n  }\n}\r\n", "// upozornění (červená hvězdička)\r\n.notice {\n  color: $color_danger;\r\n  font-weight: bold;\r\n}\r\n", "// styly pro vlastní slider\r\n.noUi-horizontal {\n  height: 6px;\r\n  margin: 0 5px;\r\n}\r\n\r\n.noUi-connect {\n  background-color: $color_main;\r\n}\r\n\r\n.noUi-target {\n  border: 0;\r\n  background-color: $color_gray_dark;\r\n  box-shadow: none;\r\n}\r\n\r\n.noUi-horizontal .noUi-handle {\n  top: -6px;\r\n  right: -9px;\r\n\n  width: 18px;\r\n  height: 18px;\r\n\n  border: 2px solid $color_gray_dark;\r\n  border-radius: 50%;\r\n  background-color: $color_white;\r\n  box-shadow: none;\r\n\r\n  &:before,\n  &:after {\n    display: none;\r\n  }\n}\r\n", "// stránkování\r\n.pagination {\n  // základní definice boxů\r\n  a,\n  span {\n    display: inline-block;\r\n    overflow: hidden;\r\n\n    width: 32px;\r\n    height: 32px;\r\n\n    color: $color_black;\r\n    font-size: 15px;\r\n    font-weight: 600;\r\n    line-height: 32px;\r\n    text-align: center;\r\n    vertical-align: middle;\r\n    text-decoration: none;\r\n\n    border-radius: 50%;\r\n    background-color: #ececec;\r\n  }\n}\r\n\r\n// aktivní stránka\r\nspan.paginator__current {\n  background-color: $color_white;\r\n}\r\n\r\n// trojtečka\r\nspan.paginator__space {\n  width: 15px;\r\n\n  background: transparent;\r\n}\r\n\r\n// šipky\r\na.pagination__prev,\na.pagination__next {\n  width: 42px;\r\n  height: 42px;\r\n\n  text-indent: 9999px;\r\n\n  background: transparent;\r\n  background-image: url( ../img/pagination-next.svg );\r\n  background-repeat: no-repeat;\r\n  background-position: center center;\r\n}\r\n\r\n// předchozí\r\n.pagination__prev {\n  transform: rotate( 180deg );\r\n}\r\n", "// telefon\n.phone {\n  position: relative;\n\n  padding-left: 15px;\n\n  &:before {\n    content: '';\n\n    position: absolute;\n    top: 6px;\n    left: 4px;\n\n    display: block;\n\n    width: 6px;\n    height: 6px;\n\n    border-radius: 50%;\n    background-color: $color_gray_light;\n  }\n\n  &.is-offline {\n    &:before {\n      background-color: $color_danger;\n    }\n  }\n\n  &.is-online {\n    &:before {\n      background-color: #27c900;\n    }\n  }\n}\n", "// ukazatel (do<PERSON><PERSON>, sleva)\n.progress {\n  position: relative;\n\n  display: inline-block;\n\n  width: 100%;\n  height: 6px;\n\n  border-radius: 50px;\n  background-color: $color_gray_dark;\n}\n\n// světlejší verze\n.progress--light {\n  .progress__bar {\n    background-color: $color_delivery;\n  }\n}\n\n// teploměr\n.progress__bar {\n  position: absolute;\n\n  width: 100%;\n  height: 6px;\n\n  border-radius: 50px;\n  background-color: $color_main;\n}\n", "// poradna - <PERSON><PERSON><PERSON><PERSON><PERSON> a odpovědi\r\n.question {\n  max-width: 920px;\r\n  margin: 0 auto 25px auto;\r\n  padding: 15px;\r\n\n  color: $color_gray;\r\n  font-size: 15px;\r\n\n  border-radius: $radius;\r\n  background-color: $color_white;\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 25px;\r\n  }\r\n\r\n  h2 {\n    margin-bottom: 5px;\r\n    padding-left: 30px;\r\n\n    color: $color_black;\r\n    font-size: 18px;\r\n\n    background: url( ../img/question.svg ) left center no-repeat;\r\n  }\r\n\r\n  p {\n    margin: 0;\r\n\n    line-height: 1.33;\r\n  }\r\n\r\n  a {\n    color: $color_black;\r\n  }\n}\r\n\r\n// kratší verze\r\n.question--short {\n  max-width: 800px;\r\n  margin-right: 0;\r\n  margin-left: 0;\r\n}\r\n\r\n// jméno, datum\r\n.question__meta {\n  padding-bottom: 10px;\r\n\n  color: $color_black;\r\n  font-size: 17px;\r\n}\r\n\r\n// odpověď\r\n.question__answer {\n  position: relative;\r\n\n  margin-top: 35px;\r\n  padding: 15px;\r\n\n  color: #d1d1d1;\r\n\n  border-radius: $radius;\r\n  background-color: $color_gray_dark;\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 25px;\r\n  }\r\n\r\n  &:before {\n    content: '';\r\n\n    position: absolute;\r\n    top: -5px;\r\n    left: 30px;\r\n\n    display: block;\r\n\n    width: 14px;\r\n    height: 14px;\r\n\n    background-color: $color_gray_dark;\r\n\n    transform: rotate( 45deg );\r\n  }\r\n\r\n  h2 {\n    display: inline-block;\r\n\n    padding: 0;\r\n\n    color: $color_white;\r\n\n    background: none;\r\n  }\r\n\r\n  a {\n    color: $color_white;\r\n  }\r\n\r\n  .question__meta {\n    display: flex;\r\n\n    padding: 0 0 15px 0;\r\n\n    color: $color_white;\r\n    line-height: 1;\r\n\r\n    em {\n      font-size: 12px;\r\n      font-style: normal;\r\n    }\r\n\r\n    .avatar {\n      flex-shrink: 0;\r\n\n      margin-right: 10px;\r\n    }\n  }\n}\r\n\r\n// související produkty\r\n.question__products {\n  margin: 30px 0 10px 0;\r\n\r\n  h3 {\n    margin: 0;\r\n\n    font-size: 15px;\r\n    line-height: 1.2;\r\n  }\r\n\r\n  p {\n    flex: 0 0 auto;\r\n\n    margin: 0;\r\n  }\r\n\r\n  a {\n    display: inline-flex;\r\n\n    max-width: 350px;\r\n    margin: 5px 2px 0 0;\r\n    padding: 14px 16px;\r\n\n    text-decoration: none;\r\n\n    border-radius: $radius;\r\n    background-color: #272727;\r\n\r\n    @media (min-width: $mqsm) {\n      align-items: center;\r\n\n      width: 49%;\r\n    }\n  }\r\n\r\n  strong {\n    display: block;\r\n\n    padding-top: 5px;\r\n\n    font-size: 19px;\r\n  }\r\n\r\n  del {\n    color: #acacac;\r\n    font-size: 12px;\r\n    font-weight: 400;\r\n    vertical-align: middle;\r\n  }\r\n\r\n  img {\n    display: block;\r\n\n    width: 40px;\r\n    height: 40px;\r\n    margin-right: 14px;\r\n    margin-bottom: 10px;\r\n\n    border-radius: $radius;\r\n    background: $color_white;\r\n\r\n    @media (min-width: $mqsm) {\n      width: 70px;\r\n      height: 70px;\r\n      margin-bottom: 0;\r\n    }\n  }\n}\r\n", "// registrace - formulář a výhody\r\n.register {\n  display: flex;\r\n  flex-wrap: wrap;\r\n\r\n  @media (min-width: $mqsm) {\n    flex-wrap: nowrap;\r\n  }\r\n\r\n  .form {\n    width: 100%;\r\n  }\n}\r\n\r\n// výhody\r\n.register__benefits {\n  flex: 1 1 auto;\r\n\n  padding: 20px 10px 20px 30px;\r\n\n  color: $color_white;\r\n\n  background-color: $color_gray_dark;\r\n\r\n  @media (min-width: $mqsm) {\n    flex: 0 0 350px;\r\n  }\r\n\r\n  @media (min-width: $mqmd) {\n    flex: 0 0 500px;\r\n\n    padding: 70px 90px;\r\n  }\r\n\r\n  h2 {\n    font-weight: 600;\r\n    line-height: 1.17;\r\n\r\n    @media (min-width: $mqsm) {\n      font-size: 30px;\r\n    }\n  }\r\n\r\n  ul,\n  li {\n    margin: 0;\r\n    padding: 0;\r\n\n    list-style-type: none;\r\n  }\r\n\r\n  li {\n    position: relative;\r\n\n    padding: 28px 0 28px 90px;\r\n\n    font-size: 19px;\r\n    line-height: 1.21;\r\n\r\n    strong {\n      font-weight: 600;\r\n    }\n  }\r\n\r\n  img {\n    position: absolute;\r\n    top: 20px;\r\n    left: 5px;\r\n  }\n}\r\n", "// skrytá část\r\n.reveal {\n  display: none;\r\n\r\n  &.is-visible {\n    display: block;\r\n  }\n}\r\n\r\n// inline verze\r\n.reveal--inline {\n  &.is-visible {\n    display: inline-block;\r\n  }\n}\r\n\r\n// flexbox verze\r\n.reveal--flex {\n  &.is-visible {\n    display: flex;\r\n  }\n}\r\n", "// prodejny\r\n.shop {\n  position: relative;\r\n\n  padding: 30px 0;\r\n\n  color: $color_white;\r\n  font-size: 16px;\r\n\n  background-color: $color_gray_dark;\r\n\r\n  @media (min-width: $mqmd) {\n    padding: 100px 0;\r\n\n    font-size: 18px;\r\n\r\n    &:before {\n      content: '';\r\n\n      position: absolute;\r\n      top: 0;\r\n      bottom: 0;\r\n      left: 50%;\r\n\n      display: block;\r\n\n      width: 1px;\r\n\n      background-color: #272727;\r\n    }\n  }\r\n\r\n  h2 {\n    margin: 20px 0 12px 0;\r\n\n    font-size: 18px;\r\n    font-weight: 700;\r\n\r\n    @media (min-width: $mqsm) {\n      font-size: 30px;\r\n    }\n  }\r\n\r\n  p {\n    position: relative;\r\n\n    margin-bottom: 12px;\r\n    padding-left: 34px;\r\n\r\n    .icon {\n      position: absolute;\r\n      left: 0;\r\n\r\n      @media (min-width: $mqmd) {\n        top: 5px;\r\n      }\n    }\n  }\r\n\r\n  img {\n    display: inline-block;\r\n\n    border-radius: $radius;\r\n  }\r\n\r\n  .container {\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .icon {\n    width: 24px;\r\n    margin-right: 5px;\r\n\n    color: $color_main;\r\n  }\n}\r\n\r\n// obchod\r\n.shop__item {\n  position: relative;\r\n\n  width: 100%;\r\n  max-width: 610px;\r\n}\r\n\r\n.shop__item + .shop__item {\n  margin-top: 20px;\r\n\r\n  @media (min-width: $mqmd) {\n    margin-top: 0;\r\n  }\n}\r\n\r\n// fotografie\r\n.shop__photo {\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n\n  padding: 0;\r\n\r\n  picture {\n    max-width: 48%;\r\n  }\n}\r\n\r\np.shop__photo {\n  padding: 0;\r\n}\r\n\r\n// odkaz\r\np.shop__link {\n  padding: 0;\r\n\r\n  @media (min-width: $mqmd) {\n    position: absolute;\r\n    right: 0;\r\n    bottom: 0;\r\n  }\r\n\r\n  .icon {\n    position: relative;\r\n  }\n}\r\n", "// prodejna (detail)\n.shop-detail {\n  position: relative;\n\n  margin: 20px 0;\n\n  & + & {\n    @media (min-width: $mqsm) {\n      margin-top: 80px;\n    }\n  }\n}\n\n// fotografie\n.shop-detail__photo {\n  @media (min-width: $mqmd) {\n    display: flex;\n    justify-content: space-between;\n  }\n\n  p {\n    margin: 0;\n  }\n\n  img {\n    display: block;\n\n    border-radius: $radius;\n  }\n}\n\n// menší fotografie\n.shop-detail__gallery {\n  display: flex;\n\n  @media (min-width: $mqmd) {\n    flex: 0 0 327px;\n    flex-direction: column;\n    justify-content: space-between;\n\n    padding-left: 20px;\n  }\n\n  picture {\n    padding: 10px 5px;\n\n    @media (min-width: $mqmd) {\n      padding: 0 0 10px 0;\n    }\n\n    @media (min-width: 1300px) {\n      padding-bottom: 0;\n    }\n  }\n}\n\np.shop-detail__gallery {\n  margin: 0 -5px;\n\n  @media (min-width: $mqmd) {\n    margin: 0;\n  }\n}\n\n// obsahová část\n.shop-detail__content {\n  position: relative;\n\n  padding: 20px;\n\n  font-size: 18px;\n\n  background-color: $color_white;\n\n  @media (min-width: $mqlg) {\n    max-width: 680px;\n    margin: -140px 0 0 120px;\n    padding: 45px 65px;\n\n    font-size: 20px;\n  }\n\n  h2 {\n    margin-bottom: 20px;\n  }\n\n  p {\n    position: relative;\n\n    min-height: 35px;\n    margin: 0 0 5px 0;\n    padding-left: 35px;\n  }\n\n  .icon {\n    position: absolute;\n    left: 0;\n\n    width: 25px;\n    margin-right: 8px;\n\n    color: $color_main;\n  }\n}\n\n// odkaz\n.shop-detail__link {\n  margin-top: 20px;\n\n  @media (min-width: $mqmd) {\n    position: absolute;\n    right: 20px;\n    bottom: 40px;\n\n    margin: 0;\n  }\n\n  @media (min-width: $mqlg) {\n    right: 90px;\n  }\n}\n", "// v<PERSON><PERSON><PERSON><PERSON>, našeptávač\n.search {\n  position: relative;\n\n  display: none;\n  flex: 1 1 55%;\n\n  margin: 0 25px;\n\n  @media (min-width: $mq_menu) {\n    display: block;\n\n    max-width: 630px;\n  }\n\n  @media (min-width: $mqlg) {\n    margin: 0;\n  }\n\n  &.is-open {\n    position: absolute;\n    top: 70px;\n    left: 0;\n    z-index: $index_modal - 1;\n\n    display: block;\n\n    width: 100%;\n    margin: 0;\n    padding: 10px;\n\n    background: $color_gray_dark;\n  }\n\n  .form-search {\n    position: relative;\n    z-index: $index_menu + 3;\n  }\n}\n\n// našeptávač\n.search__detail {\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $index_menu + 2;\n\n  display: none;\n  overflow: auto;\n\n  max-height: calc( 100vh - 70px );\n  padding: 60px 0 20px 0;\n\n  color: $color_white;\n\n  border-radius: $radius;\n  background-color: $color_gray_dark;\n\n  @media (min-width: $mq_menu) {\n    top: -10px;\n    right: -10px;\n    left: -10px;\n\n    max-height: calc( 100vh - 108px );\n  }\n\n  &.is-open {\n    display: block;\n  }\n\n  h2 {\n    font-size: 18px;\n    font-weight: 600;\n  }\n\n  & > p {\n    &:last-child {\n      margin: 20px 0 0 0;\n    }\n  }\n\n  // scroolbar\n  &::-webkit-scrollbar {\n    width: 10px;\n  }\n\n  &::-webkit-scrollbar-track {\n    border-radius: $radius;\n    background-color: $color_black;\n  }\n\n  &::-webkit-scrollbar-thumb {\n    border: 3px solid $color_black;\n    border-radius: $radius;\n    background: $color_main;\n  }\n\n  &::-webkit-scrollbar-thumb:hover {\n    background-color: $color_main;\n  }\n}\n\n// odkazy\n.search__content {\n  padding: 15px 18px;\n\n  font-size: 13px;\n\n  h2 {\n    margin-bottom: 5px;\n  }\n\n  ul {\n    margin: 0;\n    padding: 0;\n\n    list-style-type: none;\n  }\n\n  li {\n    margin: 0;\n    padding: 2px 0;\n  }\n\n  a {\n    color: $color_white;\n\n    &:hover,\n    &:focus {\n      color: $color_main;\n    }\n  }\n}\n\n// výpisy (produkty, články...)\n.search__list {\n  position: relative;\n\n  padding: 15px 18px;\n\n  background-color: #272727;\n\n  .product__price {\n    @media (max-width: $mqsm - 1px) {\n      padding: 0 20px;\n    }\n\n    del {\n      display: block;\n\n      color: #acacac;\n    }\n  }\n\n  .product--basket.product--top {\n    margin-bottom: 10px;\n  }\n\n  .article {\n    color: $color_white;\n  }\n\n  .btn {\n    white-space: nowrap;\n  }\n}\n\n// počet\n.search__number {\n  display: inline-block;\n\n  width: 24px;\n  height: 24px;\n  margin: 0 3px;\n  padding-top: 5px;\n\n  font-size: 10px;\n  font-weight: 400;\n  line-height: 1;\n  text-align: center;\n  vertical-align: middle;\n\n  border: 1px solid #3b3b3b;\n  border-radius: 50%;\n}\n\n// zavřít okno\n.search__close {\n  position: absolute;\n  top: 70px;\n  right: 15px;\n  z-index: $index_page + 1;\n\n  overflow: hidden;\n\n  width: 25px;\n  height: 25px;\n\n  cursor: pointer;\n\n  &:hover,\n  &:focus {\n    opacity: 0.7;\n  }\n\n  .icon {\n    width: 25px;\n\n    color: $color_white;\n  }\n}\n", "// oddělovač\r\n.separator {\n  position: relative;\r\n\n  margin: 25px 0;\r\n\n  text-align: center;\r\n\r\n  &:before {\n    content: '';\r\n\n    position: absolute;\r\n    top: 15px;\r\n    z-index: $index_page;\r\n\n    display: block;\r\n\n    width: 100%;\r\n    max-width: 410px;\r\n    height: 1px;\r\n\n    background-color: $color_black;\r\n  }\r\n\r\n  span {\n    position: relative;\r\n    z-index: $index_page + 1;\r\n\n    display: inline-block;\r\n\n    padding: 6px 10px;\r\n\n    color: $color_black;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    line-height: 1;\r\n    text-transform: uppercase;\r\n\n    border: 1px solid $color_black;\r\n    border-radius: $radius;\r\n    background-color: $color_white;\r\n  }\r\n\r\n  .login__form & {\n    margin: 25px 0 10px 0;\r\n  }\r\n\r\n  // kompentace na straně přihlášení\r\n  .register & span {\n    @media (min-width: 420px) {\n      margin-left: -50px;\r\n    }\n  }\n}\r\n\r\n// verze na tmavém pozadí\r\n.separator--inverse {\n  &:before {\n    background-color: $color_white;\r\n  }\r\n\r\n  span {\n    color: $color_white;\r\n\n    border-color: $color_white;\r\n    background-color: #272727;\r\n  }\n}\r\n", "// sidebar\r\n.sidebar {\n  margin-top: 20px;\r\n\r\n  @media (min-width: $mqlg) {\n    flex: 0 0 450px;\r\n\n    margin-top: 0;\r\n    padding-left: 20px;\r\n  }\r\n\r\n  & > h3 {\n    margin-top: 10px;\r\n\n    font-size: 20px;\r\n  }\r\n\r\n  .order-sum {\n    max-width: calc( 100% - 40px );\r\n    margin: 20px;\r\n  }\r\n\r\n  .btn--danger {\n    margin-bottom: 10px;\r\n  }\n}\r\n\r\n// filtry\r\n.sidebar--filter {\n  display: none;\r\n\n  width: 100%;\r\n  padding-left: 0;\r\n\r\n  @media (min-width: $mqlg) {\n    display: block;\r\n    flex: 0 0 350px;\r\n\n    padding-right: 20px;\r\n  }\r\n\r\n  &.is-open {\n    display: block;\r\n  }\r\n\r\n  p {\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .form-search {\n    max-width: 260px;\r\n  }\n}\r\n\r\n// verze v objednávce\r\n.sidebar--order {\n  flex: 1 1 100%;\r\n\r\n  @media (min-width: $mqlg) {\n    flex: 0 0 480px;\r\n\n    padding-left: 20px;\r\n  }\r\n\r\n  @media (min-width: $mqxlg) {\n    padding-left: 100px;\r\n  }\r\n\r\n  & > h3,\n  .contact-box {\n    @media (max-width: $mqmd - 1px) {\n      display: none;\r\n    }\n  }\r\n\r\n  .product__price {\n    line-height: 1.2;\r\n  }\n}\r\n\r\n// rozbalovací odkaz\r\n.sidebar__show {\n  margin-top: -15px;\r\n}\r\n", "// hlavní slider\n.slider {\n  position: relative;\n\n  display: none;\n\n  color: $color_white;\n\n  background-color: $color_black;\n\n  @media (min-width: $mqmd) {\n    display: block;\n  }\n\n  .btn {\n    padding: 15px 33px;\n\n    font-size: 15px;\n  }\n\n  .is-info {\n    .btn {\n      color: $color_white;\n\n      background-color: #39d8fd;\n    }\n\n    small {\n      color: #39d8fd;\n    }\n  }\n\n  .is-success {\n    .btn {\n      color: $color_white;\n\n      background-color: $color_success;\n    }\n\n    small {\n      color: $color_success;\n    }\n  }\n\n  .is-danger {\n    .btn {\n      color: $color_white;\n\n      background-color: $color_danger;\n    }\n\n    small {\n      color: $color_danger;\n    }\n  }\n}\n\n// obrázek\n.slider__image {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $index_page - 1;\n\n  overflow: hidden;\n\n  margin: 0;\n\n  a {\n    display: block;\n  }\n\n  img {\n    display: block;\n\n    height: 100%;\n    margin-left: auto;\n\n    object-fit: cover;\n  }\n}\n\n// obsahová část\n.slider__content {\n  position: relative;\n  z-index: $index_page;\n\n  display: flex;\n  align-items: center;\n  flex-direction: column;\n  justify-content: center;\n\n  width: 50%;\n  min-height: 465px;\n\n  text-align: center;\n}\n\n// nadpis\n.slider__title {\n  font-size: 55px;\n  font-weight: 700;\n  line-height: 1;\n\n  small {\n    display: block;\n\n    padding-bottom: 5px;\n\n    color: $color_main;\n    font-size: 22px;\n    font-weight: 600;\n  }\n}\n\n// odkaz\n.slider__link {\n  margin: 0;\n}\n", "// řazení\r\n.sort {\n  display: flex;\r\n\n  margin: 0;\r\n  padding: 10px 0;\r\n\n  list-style-type: none;\r\n\n  text-transform: uppercase;\r\n\r\n  @media (min-width: $mqsm) {\n    margin: 25px 0;\r\n    padding: 0;\r\n\n    border-bottom: 1px solid #dedede;\r\n  }\r\n\r\n  @media (max-width: $mqsm - 1px) {\n    overflow-x: auto;\r\n  }\r\n\r\n  li {\n    margin: 0 10px -1px 0;\r\n    padding: 5px;\r\n\n    border-bottom: 1px solid transparent;\r\n\r\n    @media (min-width: $mqsm) {\n      margin-right: 40px;\r\n      padding: 15px 0;\r\n    }\n  }\r\n\r\n  a {\n    color: $color_gray;\r\n    white-space: nowrap;\r\n    text-decoration: none;\r\n\r\n    &:hover,\n    &:focus {\n      color: $color_black;\r\n    }\n  }\r\n\r\n  .is-active {\n    font-weight: 700;\r\n\r\n    @media (min-width: $mqsm) {\n      border-color: $color_main;\r\n    }\r\n\r\n    a {\n      color: $color_black;\r\n    }\n  }\n}\r\n\r\n// centrované\r\n.sort--center {\n  @media (min-width: $mqsm) {\n    justify-content: center;\r\n\r\n    li {\n      margin: 0 20px -1px 20px;\r\n    }\n  }\n}\r\n", "// slider - <PERSON><PERSON><PERSON><PERSON><PERSON> styly\n.splide {\n  position: relative;\n\n  .product {\n    width: auto;\n    height: 100%;\n    margin: 0 12px;\n  }\n}\n\n// hlavní slider\n.splide--main {\n  margin: 0;\n\n  .splide__arrow {\n    background-image: url( ../img/pagination-next-inverse.svg );\n  }\n\n  .splide__arrow--prev {\n    left: 20px;\n\n    @media (min-width: $mqxxlg) {\n      left: 80px;\n    }\n  }\n\n  .splide__arrow--next {\n    right: 20px;\n\n    @media (min-width: $mqxxlg) {\n      right: 80px;\n    }\n  }\n}\n\n// úprava pro produkty\n.splide--products {\n  margin: 0 20px;\n\n  @media (min-width: $mqxxlg) {\n    margin: 0 -12px;\n  }\n}\n\n// šipky\n.splide__arrow {\n  position: absolute;\n  top: 50%;\n  z-index: $index_page + 1;\n\n  overflow: hidden;\n\n  width: 34px;\n  height: 34px;\n\n  text-indent: 9999px;\n\n  border: none;\n  background: transparent url( ../img/pagination-next.svg ) center center no-repeat;\n  background-size: 34px 34px;\n\n  transform: translateY( -50% );\n\n  @media (min-width: $mqxxlg) {\n    width: 64px;\n    height: 64px;\n\n    background-size: 64px 64px;\n  }\n\n  &:hover,\n  &:focus {\n    opacity: 0.7;\n  }\n\n  &:disabled {\n    opacity: 0.2;\n  }\n}\n\n// přechozí\n.splide__arrow--prev {\n  left: -27px;\n\n  margin-top: -15px;\n\n  transform: rotate( -180deg );\n\n  @media (min-width: $mqxxlg) {\n    left: -80px;\n  }\n}\n\n// následující\n.splide__arrow--next {\n  right: -27px;\n\n  @media (min-width: $mqxxlg) {\n    right: -80px;\n  }\n}\n\n// stránkování\n.splide__pagination {\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  z-index: $index_page + 1;\n\n  margin: 0;\n  padding: 7px 10px;\n\n  border-top-left-radius: $radius;\n  border-top-right-radius: $radius;\n  background-color: #131313;\n\n  transform: translateX( -50% );\n\n  button {\n    padding: 0 10px;\n\n    color: $color_white;\n    font-size: 15px;\n    font-weight: 600;\n\n    border: none;\n    background: transparent;\n\n    opacity: 0.3;\n\n    &.is-active {\n      opacity: 1;\n    }\n  }\n}\n", "// hodnocení - hv<PERSON><PERSON><PERSON><PERSON>\r\n.star {\n  display: inline-block;\r\n\n  width: 24px;\r\n  height: 24px;\r\n\n  text-decoration: none;\r\n\n  background-image: url( ../img/star.svg );\r\n  background-repeat: no-repeat;\r\n  background-position: left top;\r\n\n  transition: all 0.3s;\r\n}\r\n\r\n// hover - pouze pro odkazy\r\na.star {\n  &:hover,\n  &:focus {\n    background-position: left top;\r\n  }\n}\r\n\r\n// 75%\r\n.star--75 {\n  background-position: left -48px;\r\n}\r\n\r\n// 50%\r\n.star--50 {\n  background-position: left -72px;\r\n}\r\n\r\n// 25%\r\n.star--25 {\n  background-position: left -96px;\r\n}\r\n\r\n// 0%\r\n.star--0 {\n  background-position: left -24px;\r\n}\r\n", "// stav skladu\r\n.store {\n  color: $color_gray;\r\n  font-size: 11px;\r\n  text-decoration: none;\r\n\r\n  strong {\n    font-weight: 600;\r\n  }\r\n\r\n  .icon {\n    width: 15px;\r\n    margin: -3px 3px 0 3px;\r\n\n    color: $color_gray;\r\n  }\r\n\r\n  // skladem\r\n  &.is-success {\n    color: $color_success;\r\n\r\n    .icon {\n      color: $color_success;\r\n    }\n  }\r\n\r\n  // není skladem\r\n  &.is-danger {\n    color: $color_danger;\r\n\r\n    .icon {\n      color: $color_danger;\r\n    }\n  }\r\n\r\n  // blížší informace\r\n  &.is-info {\n    color: $color_main_dark;\r\n\r\n    .icon {\n      color: $color_main_dark;\r\n    }\n  }\n}\r\n", "// tabulka\r\n.table,\n.table__wrapper table {\n  width: 100%;\r\n\n  border: 0;\r\n\r\n  th,\n  td {\n    padding: 8px 15px;\r\n\n    line-height: 1.3;\r\n    text-align: left;\r\n    vertical-align: top;\r\n\n    border-top: 0;\r\n    border-right: 0;\r\n    border-bottom: 1px solid #dfdfdf;\r\n    border-left: 0;\r\n  }\r\n\r\n  tr {\n    &:last-child {\n      th,\n      td {\n        border-bottom: 0;\r\n      }\n    }\n  }\r\n\r\n  // hlavička tabulky\r\n  th {\n    font-size: 18px;\r\n    font-weight: 700;\r\n  }\n}\r\n\r\n// obalový element (pro respo)\r\n.table__wrapper {\n  overflow-x: auto;\r\n\n  max-width: 100%;\r\n  margin: 0 0 40px 0;\r\n  padding: 10px 20px 30px 20px;\r\n\n  background-color: $color_white;\r\n}\r\n", "// štítek\r\n.tag {\n  display: inline-block;\r\n\n  margin: 0 4px 4px 0;\r\n  padding: 6px 10px;\r\n\n  color: $color_black;\r\n  font-size: 10px;\r\n  font-weight: 600;\r\n  line-height: 1;\r\n  text-transform: uppercase;\r\n\n  border-radius: $radius;\r\n  background-color: $color_main;\r\n\r\n  em {\n    display: block;\r\n\n    padding-top: 3px;\r\n\n    font-style: normal;\r\n  }\r\n\r\n  .icon {\n    width: 10px;\r\n    margin: -2px 2px 0 -2px;\r\n  }\n}\r\n\r\n// větší verze\r\n.tag--big {\n  padding: 8px 16px;\r\n\n  font-size: 13px;\r\n\r\n  .icon {\n    width: 14px;\r\n    margin: -2px 2px 0 -4px;\r\n  }\n}\r\n\r\n// zlaté dny\r\n.tag--gold {\n  color: $color_white;\r\n\n  background-image: $gradient_gold;\r\n\r\n  .icon {\n    color: $color_white;\r\n  }\n}\r\n\r\n// doprava zdarma\r\n.tag--delivery {\n  background-color: $color_delivery;\r\n}\r\n\r\n// akce\r\n.tag--action {\n  color: $color_white;\r\n\n  background-color: $color_action;\r\n\r\n  .icon {\n    color: $color_white;\r\n  }\n}\r\n\r\n// vegan\r\n.tag--vegan {\n  background-color: $color_vegan;\r\n\r\n  .icon {\n    color: $color_success;\r\n  }\n}\r\n\r\n// novinka\r\n.tag--new {\n  background-color: $color_new;\r\n}\r\n", "// témata v poradně\r\n.topic {\n  padding: 25px 0;\r\n\n  color: $color_white;\r\n\n  background-color: #272727;\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 50px 0;\r\n  }\r\n\r\n  .container {\n    @media (min-width: $mqsm) {\n      display: flex;\r\n    }\n  }\n}\r\n\r\n// popisná část\r\n.topic__info {\n  display: flex;\r\n  justify-content: space-between;\r\n\n  padding-bottom: 15px;\r\n\r\n  @media (min-width: $mqsm) {\n    flex: 0 0 auto;\r\n    flex-direction: column;\r\n\n    padding-right: 25px;\r\n    padding-bottom: 0;\r\n  }\r\n\r\n  h2 {\n    margin: 0;\r\n\n    font-size: 18px;\r\n    font-weight: 600;\r\n  }\r\n\r\n  p {\n    margin: 0;\r\n  }\r\n\r\n  a {\n    color: $color_white;\r\n    font-size: 15px;\r\n  }\n}\r\n\r\n// výpis témat\r\n.topic__content {\n  flex: 1 1 auto;\r\n\r\n  .badge {\n    margin: 2px 0;\r\n  }\n}\r\n", "// varování\r\n.warn {\n  padding: 7px 10px 5px 10px;\r\n\n  font-size: 14px;\r\n  font-weight: 500;\r\n  line-height: 1.2;\r\n\n  background-color: $color_main;\r\n\r\n  strong {\n    font-weight: 600;\r\n  }\r\n\r\n  .icon {\n    display: none;\r\n\n    width: 22px;\r\n    margin-top: -2px;\r\n    margin-right: 7px;\r\n\n    color: $color_white;\r\n\r\n    @media (min-width: $mqsm) {\n      display: inline-block;\r\n    }\n  }\r\n\r\n  // potvrzující hláška\r\n  &.is-success {\n    color: $color_white;\r\n\n    background-color: $color_success;\r\n  }\r\n\r\n  // výstražná hláška\r\n  &.is-danger {\n    color: $color_white;\r\n\n    background-color: $color_danger;\r\n  }\n}\r\n", "// důvody\n.why {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n\n  @media (min-width: $mqmd) {\n    align-items: center;\n    justify-content: space-between;\n  }\n\n  h2 {\n    width: 100%;\n\n    text-align: center;\n\n    @media (min-width: $mqmd) {\n      width: auto;\n      margin: 0 30px 0 0;\n\n      line-height: 1.2;\n    }\n  }\n\n  p {\n    width: 50%;\n    margin: 10px 0;\n    padding: 5px;\n\n    font-size: 14px;\n    line-height: 1.22;\n    text-align: center;\n\n    @media (min-width: $mqmd) {\n      width: auto;\n      margin: 0;\n\n      font-size: 18px;\n    }\n  }\n\n  img {\n    margin-bottom: 10px;\n  }\n\n  strong {\n    display: block;\n\n    font-weight: 600;\n  }\n}\n", "// výpis produktů\n.product {\n  display: flex;\n  flex-direction: column;\n  flex-wrap: wrap;\n\n  width: 100%;\n  padding: 17px;\n\n  background-color: $color_white;\n\n  @media (min-width: $mqsm) {\n    flex-wrap: nowrap;\n  }\n\n  h3 {\n    flex: 1 1 auto;\n\n    margin-bottom: 10px;\n\n    font-size: 16px;\n\n    a {\n      text-decoration: none;\n    }\n  }\n\n  p {\n    margin: 0;\n  }\n}\n\n// nejprodávanější\n.product--top {\n  align-items: center;\n  flex-direction: row;\n\n  margin-top: 8px;\n  padding: 10px;\n\n  @media (min-width: $mqxs) {\n    padding-right: 25px;\n  }\n\n  h3 {\n    margin-bottom: 0;\n  }\n\n  .product__image {\n    flex: 0 0 80px;\n\n    padding: 0;\n\n    img {\n      display: block;\n    }\n  }\n\n  .product__content {\n    flex: 1 1 calc( 100% - 48px );\n\n    padding: 0 0 15px 0;\n\n    @media (min-width: 450px) {\n      padding-left: 10px;\n    }\n\n    @media (min-width: $mqxs) {\n      padding: 0 20px 0 10px;\n    }\n\n    @media (min-width: $mqsm) {\n      flex: 1 1 auto;\n    }\n\n    p {\n      max-width: 400px;\n    }\n  }\n\n  .product__meta {\n    margin: 0;\n    padding: 0;\n\n    @media (min-width: $mqsm) {\n      text-align: right;\n    }\n\n    .btn {\n      margin-left: 26px;\n    }\n  }\n}\n\n// produkt v sidebaru (vychází z .product--top)\n.product--side {\n  position: relative;\n\n  flex-wrap: wrap;\n\n  min-height: 150px;\n\n  @media (min-width: $mqmd) {\n    padding-left: 140px;\n  }\n\n  .product__image {\n    @media (min-width: $mqmd) {\n      position: absolute;\n      left: 15px;\n    }\n  }\n\n  .product__meta {\n    width: 100%;\n\n    text-align: left;\n  }\n}\n\n// produkt v sidebaru v objednávce (vychází z .product--top)\n.product--sum {\n  flex-wrap: nowrap;\n\n  min-height: 60px;\n  padding: 10px 10px 10px 5px;\n\n  .product__content {\n    padding-left: 0;\n\n    h3 {\n      margin: 0;\n\n      font-size: 15px;\n      font-weight: 500;\n    }\n  }\n\n  .product__price {\n    font-size: 15px;\n  }\n\n  .product__meta {\n    width: auto;\n  }\n}\n\n// produkt ve vyskakovacím košíku\n.product--basket {\n  color: $color_white;\n\n  background: transparent;\n\n  &.product--top {\n    margin: 0 0 15px 0;\n    padding: 0;\n\n    .product__image {\n      overflow: hidden;\n      flex: 0 0 50px;\n\n      border-radius: $radius;\n    }\n\n    .product__content {\n      padding: 0 0 0 20px;\n    }\n  }\n\n  h3 {\n    font-size: 15px;\n\n    a {\n      color: $color_white;\n    }\n  }\n\n  .product__price {\n    color: $color_white;\n  }\n\n  .product__count {\n    input {\n      color: $color_white;\n    }\n\n    .icon {\n      margin-right: 0;\n\n      color: $color_white;\n    }\n  }\n}\n\n// objednávka\n.product--order {\n  .product__price {\n    margin-left: auto;\n  }\n}\n\n// nejprodávanější - číslo\n.product__top {\n  margin: 0;\n  padding: 0 10px 0 5px;\n\n  color: $color_main;\n  font-size: 30px;\n  font-weight: 700;\n\n  &--2 {\n    color: #ffda61;\n  }\n\n  &--3 {\n    color: #ffe9a1;\n  }\n}\n\n// obrázek\n.product__image {\n  position: relative;\n\n  padding: 30px 0 20px 0;\n\n  text-align: center;\n\n  a {\n    display: block;\n\n    &:hover,\n    &:focus {\n      @media (min-width: $mqsm) {\n        img {\n          transform: scale( 1.1 );\n        }\n\n        .product__flavour {\n          transform: scale( 1.2 );\n        }\n      }\n    }\n  }\n\n  img {\n    display: inline-block;\n\n    margin: 0 auto;\n\n    transition: all 0.2s;\n  }\n}\n\n// obrázek - menší verze pro dopravy a platby\n.product__image--small {\n  img {\n    height: 40px;\n  }\n}\n\n// příchuť\n.product__flavour {\n  position: absolute;\n  right: 4px;\n  bottom: -3px;\n\n  display: block;\n  overflow: hidden;\n\n  width: 26px;\n  height: 26px;\n  padding: 2px;\n\n  border: 1px solid $color_main;\n  border-radius: 50%;\n  background-color: $color_white;\n\n  transition: all 0.2s;\n\n  @media (min-width: $mqxs) {\n    right: -3px;\n  }\n\n  img {\n    width: 100%;\n    height: auto;\n  }\n}\n\n// štítky\n.product__tags {\n  position: absolute;\n  top: 0;\n  left: 0;\n\n  width: 120px;\n\n  line-height: 1;\n  text-align: left;\n}\n\n// sleva\n.product__discount {\n  position: absolute;\n  bottom: 15px;\n  left: 0;\n}\n\n// oblíbené\n.product__fav {\n  position: absolute;\n  top: 0;\n  right: 0;\n\n  .icon {\n    width: 18px;\n\n    color: #cecece;\n\n    &:hover,\n    &:focus {\n      color: $color_danger;\n    }\n  }\n\n  .icon--heart-full {\n    color: $color_danger;\n  }\n}\n\n// popis\n.product__description {\n  color: $color_gray;\n  font-size: 14px;\n  line-height: 1.58;\n\n  .col--4 & {\n    @media (min-width: $mqlg) {\n      min-height: 67px;\n    }\n  }\n}\n\n// cena, zakoupení, skladem\n.product__meta {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  margin-top: auto;\n  padding-top: 15px;\n\n  @media (max-width: $mqsm - 1px) {\n    width: 100%;\n  }\n\n  .discount {\n    margin-right: 20px;\n  }\n\n  .btn {\n    white-space: nowrap;\n  }\n}\n\n// cena\n.product__price {\n  color: $color_black;\n  font-size: 19px;\n  line-height: 1;\n\n  @media (min-width: $mqmd) {\n    white-space: nowrap;\n  }\n\n  strong,\n  del {\n    white-space: nowrap;\n  }\n\n  del {\n    display: inline-block;\n\n    padding-left: 3px;\n\n    color: $color_gray;\n    font-size: 12px;\n  }\n\n  & + & {\n    padding-left: 15px;\n  }\n}\n\n// přičítání/odečítání\n.product__count {\n  padding: 0 15px;\n\n  white-space: nowrap;\n\n  input {\n    width: 28px;\n    padding: 5px 0;\n\n    text-align: center;\n\n    border: none;\n    background: transparent;\n  }\n\n  .icon {\n    width: 24px;\n    margin-top: -3px;\n\n    cursor: pointer;\n    user-select: none;\n\n    @media (min-width: $mqxs) {\n      width: 20px;\n    }\n\n    &:hover,\n    &:focus {\n      opacity: 0.6;\n    }\n  }\n}\n\n// odstranění\n.product__remove {\n  margin-left: 15px;\n\n  text-decoration: none;\n\n  &:hover,\n  &:focus {\n    .icon {\n      color: $color_danger;\n    }\n  }\n\n  .icon {\n    width: 14px;\n\n    color: #9e9e9e;\n  }\n}\n\n// odkazy na více produktů\n.product__links {\n  margin: 15px 0;\n\n  .btn {\n    margin: 3px 0;\n  }\n}\n", "// detail produktu\r\n.product-detail {\n  min-height: 410px;\r\n  padding: 35px 0 0 0;\r\n\r\n  .container {\n    @media (min-width: $mqlg) {\n      padding-left: 642px;\r\n    }\n  }\r\n\r\n  .product {\n    @media (min-width: $mqxs) {\n      padding-right: 15px;\r\n    }\n  }\r\n\r\n  .product__image {\n    flex-basis: 48px;\r\n  }\r\n\r\n  .product__meta {\n    .btn {\n      margin: 0;\r\n    }\r\n\r\n    .discount {\n      @media (max-width: $mqsm - 1px) {\n        display: none;\r\n      }\n    }\n  }\r\n\r\n  .store {\n    font-size: 13px;\r\n  }\n}\r\n\r\n// odkaz - varianty\r\n.product-detail__links {\n  margin: 0;\r\n\r\n  a {\n    position: relative;\r\n\n    display: block;\r\n\n    margin: -60px 0 0 0;\r\n    padding: 45px 0 15px 0;\r\n\n    text-align: center;\r\n\n    background: linear-gradient( 0deg, rgba( 245, 245, 245, 1 ) 40%, rgba( 245, 245, 245, 0 ) 100% );\r\n  }\n}\r\n", "// produkt - pomocné menu\r\n.product-helper {\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n\n  max-width: 660px;\r\n  margin: 20px auto;\r\n  padding: 0;\r\n\n  list-style-type: none;\r\n\n  line-height: 1;\r\n\r\n  li {\n    margin: 0;\r\n    padding: 5px;\r\n  }\r\n\r\n  a {\n    text-decoration: none;\r\n  }\r\n\r\n  .icon {\n    width: 25px;\r\n    margin-right: 6px;\r\n\n    color: #cbcbcb;\r\n  }\n}\r\n", "// fotografie detailu + galerie\r\n.product-photo {\n  position: relative;\r\n\n  width: 100%;\r\n  max-width: 560px;\r\n  margin-top: 25px;\r\n\r\n  @media (min-width: $mqlg) {\n    position: absolute;\r\n    top: 45px;\r\n    left: 12px;\r\n    z-index: $index_page + 1;\r\n\n    margin-top: 0;\r\n  }\r\n\r\n  img {\n    display: block;\r\n\n    transition: all 0.2s;\r\n  }\n}\r\n\r\n// hlavní fotografie\r\n.product-photo__main {\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n\n  width: 100%;\r\n  margin: 0;\r\n\n  background-color: $color_white;\r\n\r\n  @media (min-width: $mqlg) {\n    height: 560px;\r\n  }\r\n\r\n  a {\n    &:hover,\n    &:focus {\n      img {\n        @media (min-width: $mqsm) {\n          transform: scale( 1.05 );\r\n        }\n      }\n    }\n  }\n}\r\n\r\n// štítky, oblíbené, hodnocení\r\n.product-photo__tags,\n.product-photo__fav,\n.product-photo__stars {\n  position: absolute;\r\n  z-index: $index_page + 1;\r\n\n  margin: 0;\r\n}\r\n\r\n// štítky\r\n.product-photo__tags {\n  top: 15px;\r\n  left: 20px;\r\n\n  width: 120px;\r\n}\r\n\r\n// oblíbené\r\n.product-photo__fav {\n  top: 15px;\r\n  right: 18px;\r\n\r\n  .icon {\n    width: 20px;\r\n  }\r\n\r\n  .icon--heart-full {\n    color: $color_danger;\r\n  }\n}\r\n\r\n// hodnocení\r\n.product-photo__stars {\n  top: 50px;\r\n  right: 16px;\r\n\n  width: 24px;\r\n}\r\n\r\n// další forografie\r\n.product-photo__gallery {\n  position: relative;\r\n\n  display: flex;\r\n  flex-wrap: wrap;\r\n\n  margin: 8px -4px;\r\n\r\n  @media (min-width: $mqsm) {\n    flex-wrap: nowrap;\r\n  }\r\n\r\n  a {\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n\n    width: calc( 33.33333% - 8px );\r\n    height: 105px;\r\n    margin: 4px;\r\n\n    background-color: $color_white;\r\n\r\n    @media (min-width: $mqsm) {\n      width: calc( 25% - 8px );\r\n    }\r\n\r\n    &:hover,\n    &:focus {\n      img {\n        @media (min-width: $mqsm) {\n          transform: scale( 1.1 );\r\n        }\n      }\n    }\n  }\n}\r\n\r\n// více fotografií\r\n.product-photo__more {\n  flex-wrap: wrap;\r\n\n  font-size: 14px;\r\n  font-weight: 700;\r\n}\r\n", "// objedn<PERSON><PERSON><PERSON> - do<PERSON><PERSON>, slevy, registrace\n.order-benefits {\n  display: flex;\n  flex-wrap: wrap;\n\n  margin-top: 40px;\n\n  @media (min-width: $mqsm) {\n    flex-wrap: nowrap;\n  }\n\n  .hey {\n    display: none;\n    flex: 0 0 290px;\n\n    margin: 0;\n\n    @media (min-width: $mqsm) {\n      display: flex;\n    }\n  }\n}\n\n// verze do modal okna (vedle sebe)\n.order-benefits--modal {\n  margin-top: 30px;\n\n  .order-benefits__info {\n    display: flex;\n    justify-content: space-between;\n\n    margin: 0;\n    padding: 0;\n  }\n\n  .order-benefits__col {\n    width: 48%;\n  }\n}\n\n// hlavní část\n.order-benefits__info {\n  width: 100%;\n  padding: 20px 25px 30px 25px;\n\n  color: $color_white;\n  font-size: 18px;\n  font-weight: 600;\n\n  background-color: #272727;\n\n  @media (min-width: $mqsm) {\n    margin-right: 25px;\n  }\n\n  small {\n    margin: auto 0 0 auto;\n    padding-left: 5px;\n\n    font-size: 13px;\n    font-weight: 400;\n  }\n\n  p {\n    display: flex;\n    flex-wrap: wrap;\n\n    margin: 0 0 2px 0;\n  }\n\n  p + p {\n    margin-top: 10px;\n  }\n\n  .icon {\n    width: 18px;\n    margin-right: 4px;\n\n    color: $color_main;\n  }\n\n  .icon--car {\n    color: $color_delivery;\n  }\n\n  .progress {\n    margin: 2px 0 10px 0;\n  }\n}\n", "// objednávka - ukazatel\n.order-progress {\n  padding: 20px 0;\n\n  background-color: $color_gray_dark;\n\n  @media (min-width: $mqsm) {\n    padding: 40px 0;\n  }\n\n  @media (min-width: $mq_menu) {\n    margin-top: -21px;\n  }\n\n  @media (min-width: $mqlg) {\n    margin-top: -64px;\n  }\n\n  ol {\n    display: flex;\n    align-items: center;\n    flex-wrap: wrap;\n    justify-content: center;\n\n    margin: 0;\n    padding: 0;\n\n    list-style-type: none;\n\n    color: #9e9e9e;\n    font-size: 20px;\n\n    li {\n      position: relative;\n\n      margin: 0;\n      padding: 0 15px 0 0;\n\n      @media (min-width: $mqsm) {\n        padding: 0 35px;\n      }\n    }\n\n    @media (min-width: $mqsm) {\n      li + li {\n        &:before {\n          content: '';\n\n          position: absolute;\n          top: 50%;\n          left: 0;\n\n          width: 5px;\n          height: 8px;\n          margin-top: -5px;\n\n          background: url( ../img/arrow.svg ) center center no-repeat;\n        }\n      }\n    }\n\n    a {\n      color: $color_white;\n      text-decoration: none;\n\n      &:hover,\n      &:focus {\n        color: #9e9e9e;\n      }\n\n      .order-progress__number {\n        border-color: $color_white;\n      }\n    }\n\n    .is-active {\n      color: $color_white;\n      font-weight: 700;\n\n      a {\n        color: $color_white;\n      }\n\n      .order-progress__number {\n        color: $color_main;\n\n        border-color: $color_white;\n      }\n    }\n  }\n}\n\n// číslo\n.order-progress__number {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n\n  width: 30px;\n  height: 30px;\n  margin-right: 5px;\n  padding-left: 2px; // vizuální centrování\n\n  font-size: 16px;\n  font-weight: 700;\n  line-height: 1;\n\n  border: 2px solid #9e9e9e;\n  border-radius: 50%;\n\n  @media (min-width: $mqsm) {\n    width: 46px;\n    height: 46px;\n    margin-right: 15px;\n  }\n}\n", "// pr<PERSON><PERSON><PERSON> objedn<PERSON>v<PERSON><PERSON>\n.order-steps {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  justify-content: space-between;\n\n  margin-top: 20px;\n  padding: 20px;\n\n  background: $color_white;\n\n  @media (min-width: $mqmd) {\n    flex-wrap: nowrap;\n\n    margin-top: 40px;\n    padding: 45px 55px;\n  }\n\n  p {\n    margin: 0;\n  }\n\n  .link {\n    display: inline-block;\n\n    margin-bottom: 15px;\n\n    @media (min-width: $mqsm) {\n      margin-bottom: 0;\n    }\n  }\n\n  .btn {\n    margin-top: 10px;\n\n    @media (min-width: $mqmd) {\n      margin-top: 0;\n      padding: 22px 25px 22px 28px;\n\n      white-space: nowrap;\n    }\n  }\n}\n\n// pole s kódem\n.order-steps__discount {\n  width: 100%;\n\n  @media (min-width: $mqsm) {\n    flex: 1 1 1px;\n\n    padding: 0 25px;\n\n    text-align: center;\n  }\n\n  .reveal--inline.is-visible {\n    display: block;\n\n    margin: 5px 0 0 0;\n\n    @media (min-width: $mqlg) {\n      display: inline-block;\n\n      min-width: 300px;\n      margin: 0 0 0 20px;\n    }\n  }\n\n  .form-checkbox {\n    margin-bottom: 0;\n  }\n}\n\n// poznámka\n.order-steps__note {\n  display: inline-block;\n\n  max-width: 310px;\n  margin-top: 10px;\n\n  color: #8b8b8b;\n  font-size: 14px;\n  line-height: 1.3;\n}\n", "// objednávka - celková cena\n.order-sum {\n  width: 100%;\n  max-width: 290px;\n  margin: 20px 0 0 auto;\n\n  font-size: 24px;\n\n  @media (min-width: $mqsm) {\n    margin-right: 25px;\n  }\n\n  p {\n    display: flex;\n    justify-content: space-between;\n\n    margin: 10px 0 0 0;\n\n    line-height: 1;\n  }\n\n  strong {\n    white-space: nowrap;\n  }\n}\n\n// odsazení\n.order-sum--pushed {\n  @media (min-width: $mqsm) {\n    margin-right: 56px;\n  }\n}\n\n// bez DPH\n.order-sum__note {\n  color: #8b8b8b;\n  font-size: 14px;\n}\n", "// objednávka - potvrzení\n.order-summary {\n  @media (min-width: $mqsm) {\n    margin: 15px 0 70px 0;\n  }\n\n  h3 {\n    margin-bottom: 10px;\n\n    font-size: 20px;\n  }\n\n  p {\n    @media (max-width: $mqsm - 1px) {\n      margin-bottom: 0;\n    }\n  }\n\n  .row + .row {\n    @media (min-width: $mqsm) {\n      margin-top: 30px;\n    }\n  }\n\n  .order-sum {\n    max-width: 340px;\n    margin-top: auto;\n    margin-bottom: auto;\n  }\n}\n", "// formul<PERSON><PERSON> - h<PERSON>ní styly\r\n.form {\n  padding: 20px;\r\n\n  color: $color_black;\r\n\n  background-color: $color_white;\r\n\r\n  @media (min-width: $mqsm) {\n    padding: 40px;\r\n  }\r\n\r\n  h2,\n  p {\n    max-width: 460px;\r\n    margin-right: auto;\r\n    margin-left: auto;\r\n\r\n    &:last-child {\n      margin-bottom: 0;\r\n    }\n  }\r\n\r\n  p {\n    line-height: 1.8;\r\n  }\r\n\r\n  input,\n  select,\n  textarea {\n    width: 100%;\r\n    max-width: 410px;\r\n    margin-bottom: 3px;\r\n  }\r\n\r\n  & + h2 {\n    margin-top: 40px;\r\n  }\n}\r\n\r\n// nezkrácená verze\r\n.form--full {\n  h2,\n  p {\n    max-width: 100%;\r\n  }\r\n\r\n  input,\n  select,\n  textarea {\n    @media (min-width: $mqxs) {\n      width: auto;\r\n      max-width: 100%;\r\n      margin-right: 15px;\r\n    }\n  }\n}\r\n\r\n// verze bez pozadí\r\n.form--modal {\n  padding: 0;\r\n\n  background: transparent;\r\n}\r\n\r\n// povinné pole\r\n.form__required {\n  color: $color_danger;\r\n  font-weight: 600;\r\n}\r\n\r\n// souhlas s podmínkami\r\n.form__agree {\n  .form-checkbox,\n  .form-radio {\n    font-size: 16px;\r\n  }\n}\r\n\r\n// poznámka\r\n.form__note {\n  margin-top: -10px;\r\n\n  color: #9e9e9e;\r\n  font-size: 16px;\r\n}\r\n\r\np.form__note {\n  line-height: 1.5;\r\n}\r\n\r\n// odeslání\r\n.form__submit {\n  margin-bottom: 0;\r\n}\r\n\r\n// pomocný odkaz\r\n.form__helper {\n  display: inline-block;\r\n  float: right;\r\n\n  margin-left: auto;\r\n  padding-top: 5px;\r\n\n  font-size: 13px;\r\n\r\n  &:hover,\n  &:focus {\n    color: $color_gray_light;\r\n  }\n}\r\n\r\n// přeražení hlavičky\r\na.form__helper {\n  text-decoration: underline;\r\n}\r\n", "// checkbox\r\n.form-checkbox {\n  position: relative;\r\n\n  display: inline-block;\r\n\n  min-height: 30px;\r\n  margin-bottom: 3px;\r\n  padding-left: 40px;\r\n\n  font-size: 17px;\r\n\n  cursor: pointer;\r\n\r\n  input {\n    position: absolute;\r\n\n    width: auto;\r\n\n    opacity: 0;\r\n  }\r\n\r\n  input:checked ~ .form-checkbox__checker {\n    background: $color_gray_dark url( ../img/checkbox.svg ) center center no-repeat;\r\n  }\r\n\r\n  input:checked ~ .form-checkbox__label {\n    font-weight: 600;\r\n  }\r\n\r\n  input:disabled ~ .form-checkbox__checker {\n    border-color: $color_gray_medium;\r\n  }\r\n\r\n  input:disabled ~ .form-checkbox__label {\n    color: $color_gray_medium;\r\n  }\r\n\r\n  // slepé odkazy\r\n  a.js-nopass {\n    text-decoration: none;\r\n\n    cursor: default;\r\n    pointer-events: none;\r\n\r\n    &:hover,\n    &:focus {\n      color: inherit;\r\n    }\n  }\n}\r\n\r\n// ukazatel\r\n.form-checkbox__checker {\n  position: absolute;\r\n  top: 3px;\r\n  left: 3px;\r\n\n  display: inline-block;\r\n\n  width: 23px;\r\n  height: 23px;\r\n\n  border: 2px solid $color_black;\r\n  background-color: transparent;\r\n}\r\n", "// sleva\n.form-discount {\n  position: relative;\n\n  display: inline-block;\n\n  width: 100%;\n  max-width: 330px;\n\n  input {\n    width: 100%;\n    padding-right: 40px;\n\n    font-size: 15px;\n\n    &::placeholder {\n      color: $color_black;\n\n      opacity: 1;\n    }\n\n    &:-ms-input-placeholder {\n      color: $color_black;\n    }\n\n    &::-ms-input-placeholder {\n      color: $color_black;\n    }\n  }\n\n  button,\n  input[type='submit'] {\n    position: absolute;\n    top: 4px;\n    right: 4px;\n\n    width: 100%;\n    max-width: 100px;\n    height: 40px;\n\n    color: $color_white;\n    font-size: 13px;\n    font-weight: 600;\n    text-transform: uppercase;\n\n    border: none;\n    border-radius: $radius;\n    background-color: $color_black;\n\n    &:hover,\n    &:focus {\n      background-color: $color_gray;\n    }\n  }\n}\n", "// radio\r\n.form-radio {\n  position: relative;\r\n\n  display: inline-block;\r\n\n  min-height: 24px;\r\n  padding-left: 30px;\r\n\n  font-size: 17px;\r\n\n  cursor: pointer;\r\n\r\n  &.has-iframe {\n    padding-bottom: 300px;\r\n  }\r\n\r\n  input {\n    position: absolute;\r\n\n    opacity: 0;\r\n  }\r\n\r\n  input:checked ~ .form-radio__checker:before {\n    background-color: $color_main;\r\n  }\r\n\r\n  input:checked ~ .form-radio__price:before {\n    border-color: $color_black;\r\n  }\r\n\r\n  input:checked ~ .form-radio__label {\n    font-weight: 600;\r\n\r\n    small {\n      font-weight: 400;\r\n    }\n  }\r\n\r\n  input:disabled ~ .form-radio__checker {\n    border-color: $color_gray_medium;\r\n  }\r\n\r\n  input:disabled ~ .form-radio__label {\n    color: $color_gray_medium;\r\n  }\r\n\r\n  small {\n    display: block;\r\n\n    padding-top: 2px;\r\n\n    color: $color_black;\r\n    font-size: 12px;\r\n  }\n}\r\n\r\n// doprava/platba v objednávce\r\n.form-radio--delivery {\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n\n  margin-top: 8px;\r\n  padding: 10px 10px 10px 40px;\r\n\n  background-color: $color_white;\r\n\r\n  @media (min-width: $mqxs) {\n    flex-wrap: nowrap;\r\n\n    padding: 14px 20px 14px 60px;\r\n  }\r\n\r\n  .form-radio__label {\n    width: 100%;\r\n    padding: 10px 0;\r\n\n    line-height: 1.3;\r\n\r\n    @media (min-width: $mqxs) {\n      padding: 0 20px;\r\n    }\n  }\r\n\r\n  .form-radio__checker {\n    top: 28px;\r\n    left: 10px;\r\n\r\n    @media (min-width: $mqxs) {\n      top: 50%;\r\n      left: 20px;\r\n\n      margin-top: -10px;\r\n    }\n  }\n}\r\n\r\n// ukazatel\r\n.form-radio__checker {\n  position: absolute;\r\n  top: 5px;\r\n  left: 5px;\r\n\n  display: inline-block;\r\n\n  width: 20px;\r\n  height: 20px;\r\n\n  border: 2px solid $color_black;\r\n  border-radius: 50%;\r\n  background-color: transparent;\r\n\r\n  &:before {\n    content: '';\r\n\n    position: absolute;\r\n\n    width: 8px;\r\n    height: 8px;\r\n    margin: 4px 0 0 4px;\r\n\n    border-radius: 50%;\r\n    background-color: transparent;\r\n  }\n}\r\n\r\n// ikona\r\n.form-radio__icon {\n  flex: 0 0 80px;\r\n\r\n  img {\n    display: block;\r\n\r\n    @media (min-width: $mqxs) {\n      margin: 0 auto;\r\n    }\n  }\n}\r\n\r\n// cena\r\n.form-radio__price {\n  font-size: 19px;\r\n  white-space: nowrap;\r\n\r\n  &:before {\n    content: '';\r\n\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n\n    display: block;\r\n\n    border: 2px solid transparent;\r\n\n    pointer-events: none;\r\n  }\n}\r\n\r\n// iframe\r\n.form-radio__iframe {\n  display: none;\r\n\n  max-width: 580px;\r\n  margin: -2px auto 0 auto;\r\n\n  border: 2px solid $color_black;\r\n  background: $color_white;\r\n\r\n  iframe {\n    display: block;\r\n\n    width: 100%;\r\n    height: 600px;\r\n\n    border: none;\r\n  }\r\n\r\n  &.is-visible {\n    display: block;\r\n  }\n}\r\n", "// cena od-do\n.form-range {\n  max-width: 260px;\n\n  p {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n\n    margin-top: 20px;\n  }\n\n  input {\n    max-width: 90px;\n    padding-right: 5px;\n    padding-left: 5px;\n\n    font-size: 15px;\n    text-align: center;\n  }\n}\n", "// vyhledávání\n.form-search {\n  position: relative;\n\n  input {\n    width: 100%;\n    padding-right: 40px;\n\n    &::placeholder {\n      color: $color_black;\n      font-size: 15px;\n\n      opacity: 1;\n    }\n\n    &:-ms-input-placeholder {\n      color: $color_black;\n      font-size: 15px;\n    }\n\n    &::-ms-input-placeholder {\n      color: $color_black;\n      font-size: 15px;\n    }\n  }\n\n  button {\n    position: absolute;\n\n    width: 50px;\n    height: 50px;\n    margin: 0 0 0 -50px;\n\n    border: none;\n    background: transparent;\n  }\n\n  .icon {\n    width: 20px;\n  }\n}\n", "// zarovnání textu\r\n.center {\n  text-align: center;\r\n}\r\n\r\n.left {\n  text-align: left;\r\n}\r\n\r\n.right {\n  text-align: right;\r\n}\r\n\r\n// nezalamování\r\n.nowrap {\n  white-space: nowrap;\r\n}\r\n\r\n// floatování\r\n.fleft {\n  float: left;\r\n}\r\n\r\n.fright {\n  float: right;\r\n}\r\n\r\n.cls {\n  clear: both;\r\n}\r\n\r\n// micro clearfix ( http://nicolasgallagher.com/micro-clearfix-hack/ )\r\n.cf:before,\n.cf:after {\n  content: ' ';\r\n\n  display: table;\r\n}\r\n\r\n.cf:after {\n  clear: both;\r\n}\r\n\r\n.cf {\n  *zoom: 1;\r\n}\r\n", "// tiskový styl\r\n// resetujeme základní bloky a objekty, upravujeme podle potřeby\r\n\r\n@media print {\r\n\r\n  * {\r\n    font-family: sans-serif !important;\r\n    color: #000000 !important;\r\n    background: #ffffff !important;\r\n    text-shadow: none !important;\r\n    box-shadow: none !important;\r\n    border: none !important;\r\n    width: auto !important;\r\n    height: auto !important;\r\n    padding: inherit !important;\r\n    margin: inherit !important;\r\n    max-width: none !important;\r\n    position: relative !important;\r\n    min-height: 1px !important;\r\n    top: inherit !important;\r\n    bottom: inherit !important;\r\n    left: inherit !important;\r\n    right: inherit !important;\r\n  }\r\n  body {\r\n    width: 100% !important;\r\n    margin: 0px !important;\r\n    padding: 0px !important;\r\n    line-height: 1.4 !important;\r\n    word-spacing: 1.1pt !important;\r\n    letter-spacing: 0.2pt !important;\r\n    font-family: sans-serif !important;\r\n    color: #000000 !important;\r\n    background: none !important;\r\n    font-size: 12pt !important;\r\n  }\r\n  h1, h2, h3, h4 { clear: both !important; margin: 10px 0 !important; }\r\n  h1 { font-size: 19pt !important; }\r\n  h2 { font-size: 17pt !important; }\r\n  h3 { font-size: 15pt !important; }\r\n  h4 { font-size: 12pt !important; }\r\n  img { margin: 1em 1.5em 1.5em 0em !important; }\r\n  ul, ol { padding-left: 20px !important; list-style-type: inherit !important; }\r\n  li { padding: inherit !important; padding-left: 10px !important; }\r\n  a img { border: none !important; }\r\n  a, a:link, a:visited, a:hover, a:active, a:focus { text-decoration: none !important; color:#000000 !important; }\r\n  table { margin: 1px !important; text-align:left !important; }\r\n  th { border-bottom: 1px solid #000000 !important;  font-weight: bold !important; }\r\n  td { border-bottom: 1px solid #000000 !important; }\r\n  th, td { padding: 4px 10px 4px 0px !important; }\r\n  tr { page-break-inside: avoid !important; }\r\n\r\n  // skrytí nepotřebných částí\r\n  .no, object, .noprint, nav, .nav, iframe, form, .form, button, .btn, .icon { display: none !important; }\r\n\r\n  // layout\r\n  .row { display: block !important; margin: 0 !important; }\r\n\r\n}\r\n"]}