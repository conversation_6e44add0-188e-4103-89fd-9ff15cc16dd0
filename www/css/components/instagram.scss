// instagram
.instagram {
  padding: 30px 0;

  color: $color_white;
  font-size: 18px;
  font-weight: 700;

  background-color: #272727;

  @media (min-width: $mqmd) {
    padding: 60px 0 75px 0;
  }

  a {
    display: block;
    overflow: hidden;

    &:hover,
    &:focus {
      color: $color_gray_back;

      img {
        transform: scale( 1.1 );
      }
    }
  }

  img {
    display: block;

    border-radius: $radius;

    transition: all 0.2s;
  }

  .col--6 {
    @media (max-width: $mqmd - 1px) {
      flex: 1 1 33%;

      padding: calc( $layout-gap / 2 );
    }
  }
}

// horní část
.instagram__header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;

  margin-bottom: 15px;

  @media (min-width: $mqsm) {
    justify-content: space-between;
  }

  h2 {
    @media (min-width: $mqsm) {
      margin: 0;

      font-size: 30px;
    }
  }

  p {
    margin: 0;
  }

  a {
    display: inline-flex;
    align-items: center;
    flex-direction: column;

    width: 48%;

    color: $color_white;
    font-size: 15px;
    text-align: center;

    @media (min-width: $mqmd) {
      display: inline-block;

      width: auto;
      margin-left: 50px;

      font-size: 18px;
    }
  }

  .icon {
    width: 22px;
    margin-right: 10px;
  }
}
