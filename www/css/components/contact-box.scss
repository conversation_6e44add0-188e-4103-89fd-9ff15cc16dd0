// box člověk + kontakty
.contact-box {
  display: flex;
  align-items: center;

  color: $color_gray;
  font-size: 15px;

  p {
    max-width: 390px;
    margin: 0;

    line-height: 1.3;
  }

  a {
    font-size: 18px;

    @media (min-width: $mqsm) {
      font-size: 22px;
    }
  }

  strong {
    display: inline-block;

    margin-bottom: 5px;

    color: $color_black;
    font-size: 19px;
  }

  small {
    display: inline-block;

    padding-left: 16px;

    color: #acacac;
    font-size: 11px;
  }

  .avatar {
    margin-right: 5px;

    @media (min-width: 360px) {
      margin-right: 15px;
    }

    @media (min-width: $mqsm) {
      margin-right: 32px;
    }
  }

  .phone {
    &:before {
      top: 4px;
    }
  }
}

// verze pro výpis (např. kontakty)
.contact-box--list {
  a {
    font-size: 15px;
  }
}

// osazení pokud jsou za sebou
.contact-box + .contact-box {
  margin-top: 25px;
}
