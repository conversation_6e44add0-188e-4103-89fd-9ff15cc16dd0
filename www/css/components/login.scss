// přihlášení
.login {
  position: relative;

  margin-right: 10px;

  @media (min-width: $mq_menu) {
    display: block;

    margin-right: 50px;
  }

  &:after {
    content: '';

    position: absolute;
    bottom: -30px;
    left: 5px;

    display: none;

    width: 20px;
    height: 20px;

    background-color: $color_gray_dark;

    transform: rotate( 45deg );
  }

  .count {
    position: absolute;
    top: -8px;
    left: 18px;

    padding-top: 3px;

    .icon {
      width: 11px;

      color: $color_white;
    }
  }

  &.is-open {
    @media (min-width: $mq_menu) {
      .login__detail,
      &:after {
        display: block;
      }
    }
  }

  &.is-logged {
    h2 {
      color: $color_main;
    }

    .icon--user {
      color: $color_success;
    }

    .login__detail {
      width: 500px;
    }
  }
}

// login - wrapper
.login__detail {
  position: absolute;
  right: -80px;
  z-index: $index_modal - 1;

  display: none;

  width: 790px;
  padding-top: 15px;

  color: $color_white;
}

// login - obsah
.login__content {
  display: flex;

  width: 100%;

  border-radius: $radius;
  background-color: $color_gray_dark;
}

// formulář
.login__form {
  flex: 1 1 45%;

  padding: 40px 60px 40px 50px;

  border-top-left-radius: $radius;
  border-bottom-left-radius: $radius;
  background-color: #272727;

  p {
    margin: 0;
  }

  p + p {
    margin-top: 10px;
  }

  input {
    width: 100%;
  }

  .form__helper {
    color: $color_white;
  }

  .btn {
    width: 100%;
    margin-top: 10px;

    text-align: center;
  }
}

// výhody
.login__benefits {
  flex: 1 1 55%;

  padding: 40px 30px 40px 50px;

  h2 {
    font-size: 23px;
    font-weight: 600;
    line-height: 1.3;
  }

  ul {
    margin: 20px 0 20px 0;
    padding: 0;

    list-style-type: none;
  }

  li {
    margin: 0;
    padding: 3px 0 3px 25px;

    font-size: 15px;

    background: url( ../img/list.svg ) left center no-repeat;

    a {
      color: $color_white;
      font-size: 16px;
      text-decoration: underline;
    }
  }

  p {
    margin: 0;

    a + a {
      margin-left: 12px;
    }
  }
}

// zavření
.login__close {
  position: absolute;
  top: 30px;
  right: 15px;
  z-index: $index_page + 1;

  overflow: hidden;

  width: 25px;
  height: 25px;

  cursor: pointer;

  &:hover,
  &:focus {
    opacity: 0.7;
  }

  .icon {
    width: 25px;

    color: $color_white;
  }
}
