// navigace
.nav {
  display: none;

  font-size: 18px;
  font-weight: 600;

  background-color: $color_main;

  @media (min-width: $mq_menu) {
    display: block;
  }

  @media (max-width: $mq_menu - 1px) {
    overflow: auto;

    max-height: calc( 100vh - 70px );
  }

  &.is-open {
    position: absolute;
    z-index: $index_menu;

    display: block;

    width: 100%;
    padding-bottom: 0;

    .container {
      padding-right: 0;
      padding-left: 0;
    }
  }

  ul,
  li {
    margin: 0;
    padding: 0;

    list-style-type: none;
  }

  ul {
    @media (min-width: $mq_menu) {
      display: flex;
    }
  }

  li {
    &:hover {
      .nav__inner {
        @media (min-width: $mq_menu) {
          display: block;
        }
      }
    }
  }

  .container {
    position: relative;

    padding-left: 100px;

    @media (min-width: $mqlg) {
      padding-left: 170px;
    }
  }
}

// hlavní část menu
.nav__main {
  & > a {
    display: block;

    padding: 15px 20px;

    color: $color_black;
    text-decoration: none;
    text-transform: uppercase;

    background-repeat: no-repeat;
    background-position: right 20px center;

    @media (min-width: $mq_menu) {
      padding: 10px;

      background-position: right 10px center;
    }

    @media (min-width: $mqlg) {
      padding: 20px 15px;
    }

    @media (min-width: $mqxlg) {
      padding: 20px 30px;
    }

    @media (max-width: $mqlg - 1px) {
      line-height: 1.3;
    }
  }

  &:hover {
    @media (min-width: $mq_menu) {
      & > a {
        color: $color_white;

        background-color: $color_gray;
      }

      &.has-submenu > a {
        background-image: url( ../img/nav-inverse.svg );
      }
    }
  }

  &.has-submenu > a {
    background-image: url( ../img/nav.svg );

    @media (min-width: $mq_menu) {
      padding-right: 25px;
    }

    @media (min-width: $mqxlg) {
      padding-right: 30px;
    }
  }

  &.has-submenu.is-open {
    @media (max-width: $mq_menu - 1px) {
      & > a {
        color: $color_white;

        background-color: $color_gray;
        background-image: url( ../img/nav-inverse.svg );
      }

      .nav__inner {
        display: block;
      }
    }
  }
}

// info menu, zobrazíme jen na mobilu
.nav__main--info {
  background-color: $color_gray_light;

  @media (min-width: $mq_menu) {
    display: none;
  }
}

// submenu
.nav__inner {
  display: none;

  padding: 5px 10px 20px 10px;

  background-color: $color_gray;

  @media (min-width: $mq_menu) {
    position: absolute;
    right: 0;
    left: 0;
    z-index: $index_menu + 1;

    padding: 40px 50px;
  }

  .category {
    margin: 2px 0;
    padding: 10px;

    font-weight: 400;

    background-color: darken( $color_gray, 10% );

    @media (min-width: $mq_menu) {
      margin: 5px;
    }

    &:hover,
    &:focus {
      background-color: $color_gray_dark;
    }
  }
}

// respo - kontakt, přepínač jazyků
.nav__helper {
  display: flex;
  align-items: center;
  justify-content: space-between;

  margin: 30px 18px 10px 18px;
  padding: 12px 8px 12px 12px;

  font-size: 13px;
  font-weight: 400;

  background-color: $color_white;

  @media (min-width: $mq_menu) {
    display: none;
  }

  p {
    margin: 0;

    &:last-child {
      @media (max-width: $mqxs - 1px) {
        width: 25px;
      }
    }
  }

  a {
    font-size: 15px;
    font-weight: 500;
    text-decoration: none;

    &.is-active {
      @media (max-width: $mqxs - 1px) {
        display: none;
      }
    }
  }

  small {
    font-size: 12px;
    white-space: nowrap;
  }
}
