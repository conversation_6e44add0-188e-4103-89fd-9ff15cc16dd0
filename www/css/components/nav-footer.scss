// nenu v patičce
.nav-footer {
  padding: 30px 10px;

  color: $color_white;
  font-size: 15px;

  background-color: $color_gray_dark;

  @media (min-width: $mqmd) {
    padding: 70px 0;
  }

  h2 {
    margin-bottom: 0;

    color: $color_gray_light;
    font-size: 18px;
    font-weight: 600;
    text-transform: uppercase;

    @media (min-width: $mqsm) {
      margin-bottom: 25px;

      color: $color_white;
    }

    &:before {
      @media (max-width: $mqsm - 1px) {
        content: '';

        display: inline-block;

        width: 23px;
        height: 23px;
        margin: 0 10px 3px 0;

        vertical-align: middle;

        background: url( ../img/link-inverse.svg ) center center no-repeat;
        background-size: 23px 23px;

        opacity: 0.5;
        transform: rotate( 90deg );
      }
    }

    &.is-open {
      margin-bottom: 5px;

      color: $color_white;

      &:before {
        opacity: 1;
        transform: rotate( -90deg );
      }
    }
  }

  ul,
  li {
    margin: 0;
    padding: 0;

    list-style-type: none;
  }

  ul {
    display: none;

    @media (min-width: $mqsm) {
      display: block;
    }

    &.is-open {
      display: block;
    }
  }

  li {
    line-height: 2.27;

    @media (min-width: $mqsm) {
      line-height: 2.67;
    }
  }

  a {
    color: #ccc;
    text-decoration: none;

    &:hover,
    &:focus {
      color: $color_main;
    }

    &.nav-footer__link {
      text-decoration: underline;
    }
  }
}
