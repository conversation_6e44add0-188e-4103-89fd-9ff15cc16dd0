// hlavička
.header {
  position: fixed;
  top: 0;
  z-index: $index_modal - 1;

  width: 100%;

  background-color: $color_white;
}

// verze do o<PERSON> (zjednodušená)
.header--simple {
  position: fixed;

  padding: 10px 0;

  h1 {
    margin: 0;

    a {
      display: block;
    }

    img {
      display: block;

      width: 50px;

      @media (min-width: $mq_menu) {
        width: 90px;
      }
    }
  }

  .header-top__contact {
    font-size: 17px;

    a {
      font-size: 17px;
    }

    small {
      font-size: 14px;
    }
  }

  .phone {
    &:before {
      top: 10px;
    }
  }
}

// obsahová část
.header__body {
  display: flex;
  align-items: center;
  justify-content: space-between;

  background-color: $color_white;

  @media (min-width: $mq_menu) {
    padding-top: 10px;
    padding-bottom: 10px;
  }
}

// logo
.header__logo {
  flex: 0 0 76px;

  @media (min-width: $mqlg) {
    flex: 1 1 33%;
  }

  a {
    position: relative;
    z-index: $index_menu + 1;

    display: inline-block;

    margin-bottom: -25px;
    padding-bottom: 25px;

    @media (min-width: $mq_menu) {
      margin-bottom: -50px;
      padding-bottom: 50px;
    }

    @media (min-width: $mqlg) {
      margin-bottom: -75px;
      padding-bottom: 75px;
    }
  }

  img {
    margin-bottom: -25px;

    @media (min-width: $mq_menu) {
      margin-bottom: -50px;
    }

    @media (min-width: $mqlg) {
      margin-top: 10px;
      margin-bottom: -75px;
    }
  }
}

// pomocná navigace (přihlášení, košík...)
.header__nav {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  @media (min-width: $mqlg) {
    flex: 1 1 33%;
  }

  a {
    text-decoration: none;
  }

  .icon {
    width: 30px;
  }
}

// oblíbené
.header__fav {
  position: relative;

  display: none;

  margin-right: 20px;

  @media (min-width: 360px) {
    display: block;
  }

  @media (min-width: $mq_menu) {
    margin-right: 50px;
  }

  .count {
    position: absolute;
    top: -8px;
    left: 20px;
  }
}

// přepínač menu
.header__switcher {
  overflow: hidden;

  width: 70px;
  height: 70px;
  margin-right: -12px;

  text-indent: 9999px;

  background: $color_main url( ../img/menu.svg ) center center no-repeat;

  cursor: pointer;

  @media (min-width: $mq_menu) {
    display: none;
  }

  &.is-open {
    background-image: url( ../img/menu-close.svg );
  }
}

// vyhledávací pole - ikona
.header__search {
  margin-right: 12px;

  @media (min-width: $mq_menu) {
    display: none;
  }
}
