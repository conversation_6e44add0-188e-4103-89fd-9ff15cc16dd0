// prodejna (detail)
.shop-detail {
  position: relative;

  margin: 20px 0;

  & + & {
    @media (min-width: $mqsm) {
      margin-top: 80px;
    }
  }
}

// fotografie
.shop-detail__photo {
  @media (min-width: $mqmd) {
    display: flex;
    justify-content: space-between;
  }

  p {
    margin: 0;
  }

  img {
    display: block;

    border-radius: $radius;
  }
}

// menší fotografie
.shop-detail__gallery {
  display: flex;

  @media (min-width: $mqmd) {
    flex: 0 0 327px;
    flex-direction: column;
    justify-content: space-between;

    padding-left: 20px;
  }

  picture {
    padding: 10px 5px;

    @media (min-width: $mqmd) {
      padding: 0 0 10px 0;
    }

    @media (min-width: 1300px) {
      padding-bottom: 0;
    }
  }
}

p.shop-detail__gallery {
  margin: 0 -5px;

  @media (min-width: $mqmd) {
    margin: 0;
  }
}

// obsahová část
.shop-detail__content {
  position: relative;

  padding: 20px;

  font-size: 18px;

  background-color: $color_white;

  @media (min-width: $mqlg) {
    max-width: 680px;
    margin: -140px 0 0 120px;
    padding: 45px 65px;

    font-size: 20px;
  }

  h2 {
    margin-bottom: 20px;
  }

  p {
    position: relative;

    min-height: 35px;
    margin: 0 0 5px 0;
    padding-left: 35px;
  }

  .icon {
    position: absolute;
    left: 0;

    width: 25px;
    margin-right: 8px;

    color: $color_main;
  }
}

// odkaz
.shop-detail__link {
  margin-top: 20px;

  @media (min-width: $mqmd) {
    position: absolute;
    right: 20px;
    bottom: 40px;

    margin: 0;
  }

  @media (min-width: $mqlg) {
    right: 90px;
  }
}
