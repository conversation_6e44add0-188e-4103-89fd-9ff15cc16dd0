// obsah<PERSON> hlavička
.content-header {
  padding: 30px 0;

  color: $color_white;

  background-color: $color_gray_dark;

  @media (min-width: $mqmd) {
    padding-bottom: 60px;
  }

  h1 {
    margin: 25px 0 0 0;

    line-height: 1.3;
  }

  ul {
    margin-top: 15px;
    padding: 0;

    list-style-type: none;
  }

  li {
    margin: 0;
  }

  .container {
    position: relative;
  }

  .form {
    display: none;

    @media (min-width: $mqmd) {
      display: block;
      float: right;

      width: 100%;
      max-width: 640px;
      margin-top: 30px;
    }

    textarea {
      height: 140px;
    }
  }
}

// detail článku
.content-header--detail {
  @media (min-width: $mqsm) {
    min-height: 300px;
  }
}

// detail produktu
.content-header--product {
  @media (min-width: $mqlg) {
    min-height: 320px;
    padding-bottom: 40px;

    h1 {
      margin-top: 10px;
      margin-left: 630px;
    }

    .content-header__description {
      margin-left: 630px;
    }
  }
}

// popis
.content-header__description {
  margin: 15px 0 0 0;

  color: $color_gray_light;
  font-size: 17px;
  line-height: 1.35;

  a {
    color: $color_gray_light;

    &:hover,
    &:focus {
      color: $color_white;
    }
  }

  .center & {
    margin-right: auto;
    margin-left: auto;
  }
}

// kontakty
.content-header__contact {
  margin-top: 20px;

  color: $color_gray_light;
  font-size: 17px;
  line-height: 1.3;

  @media (min-width: $mqmd) {
    margin-top: 105px;
    margin-bottom: 40px;
  }

  & + & {
    margin-top: 0;
  }

  a {
    display: block;

    color: $color_white;
    font-size: 30px;
    font-weight: 700;
  }

  small {
    position: relative;

    font-size: 14px;
  }
}

// vyhledávání (v poradně)
.content-header__search {
  margin: 0;

  @media (min-width: $mqsm) {
    display: flex;
    justify-content: space-between;

    margin-bottom: -30px;
  }

  .form-search {
    width: 100%;
    margin: 0;
    padding-top: 25px;

    @media (min-width: $mqsm) {
      text-align: right;
    }

    input {
      display: inline-block;

      max-width: 440px;

      border-color: $color_white;

      @media (min-width: $mqsm) {
        margin-left: 20px;
      }
    }

    .btn {
      margin-bottom: 10px;
      padding: 16px 25px;

      font-size: 18px;
    }
  }
}
