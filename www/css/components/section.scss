// základ<PERSON><PERSON> blok obsahu
.section {
  padding: 30px 0;

  @media (min-width: $mqmd) {
    padding: 80px 0;
  }
}

// krat<PERSON><PERSON> verze
.section--short {
  padding: 15px 0;

  @media (min-width: $mqmd) {
    padding: 50px 0;
  }
}

// základní verze
.section--base {
  background-color: $color_white;
}

// tmavější pozadí
.section--dark {
  background-color: #f2f2f2;
}

// inverzní verze
.section--inverse {
  color: $color_white;

  background-color: $color_gray_dark;
}

// obsah nalepený za sebe
.section--follow {
  padding-top: 0;
}

// vnitřní navigace
.section--nav {
  padding: 0;
}

// detail produktu
.section--detail {
  padding-top: 30px;

  .section__content {
    @media (min-width: $mqmd) {
      max-width: 800px;
    }

    h2 em {
      display: inline-block;

      padding-left: 5px;

      color: $color_gray;
      font-size: 15px;
      font-style: normal;
    }

    p,
    ul,
    ol {
      margin: 0 0 20px 0;

      @media (min-width: $mqsm) {
        line-height: 1.4;
      }
    }
  }
}

// část s obsahem (pro dlouhé texty)
.section--text {
  @media (min-width: $mqsm) {
    font-size: 20px;

    h2,
    h3,
    h4,
    p,
    ul,
    ol {
      max-width: 1110px;
    }
  }

  ul,
  li {
    @media (max-width: $mqsm - 1px) {
      line-height: 1.4;
    }
  }
}

// obsahová část (roztahování pro sidebar)
.section__content {
  flex: 1 1 auto;

  max-width: 100%;
}

// nadpis
.section__title {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;

  @media (min-width: $mqsm) {
    justify-content: space-between;

    margin-bottom: 40px;
  }

  @media (max-width: $mqsm - 1px) {
    .link {
      display: none;
    }
  }

  * {
    margin: 0;
  }
}

// stránkování
.section__pages {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;

  margin-top: 40px;

  p,
  nav {
    width: 100%;
    margin: 0 0 15px 0;

    text-align: center;

    @media (min-width: $mqsm) {
      width: 50%;
      margin: 0;
    }

    @media (min-width: $mqmd) {
      width: 33.33333333%;
    }
  }

  nav {
    @media (min-width: $mqsm) {
      text-align: right;
    }
  }
}

// stránkování - bez pageru
.section__pages--full {
  p {
    width: 100%;
  }
}

// výpis kontaktů
.section__contact {
  font-size: 14px;
  vertical-align: top;

  @media (min-width: $mqxs) {
    display: inline-block;
  }

  h2 {
    margin-bottom: 20px;

    font-size: 20px;
  }

  p {
    margin-right: 15px;

    line-height: 1.57;
  }

  & + & {
    @media (min-width: $mqxs) {
      margin-left: 100px;
    }

    @media (min-width: $mqmd) {
      margin-left: 0;
    }

    @media (min-width: $mqlg) {
      margin-left: 100px;
    }
  }
}

// oddělovač
.section__divider {
  width: 100%;
  height: 1px;
  margin: 35px 0;

  background-color: #dedede;

  hr {
    display: none;
  }
}

// úprava pro vyheldávání
.section__search {
  margin: 25px 0;

  @media (min-width: $mqmd) {
    margin: 50px 0;
  }

  ul {
    margin: 0;
    padding: 0;
  }

  .category {
    margin: 0 5px 5px 0;
  }

  .article {
    margin-right: 0;
    margin-left: 0;
  }
}

// kotva
.section__anchor {
  position: relative;
  top: -80px;
  z-index: -1;

  display: block;
  visibility: hidden;

  @media (min-width: $mq_menu) {
    top: -200px;
  }
}

// falešný odstavec (problémy s vloženým iframe/divem u doprav)
.section__p {
  margin: 0 0 30px 0;

  line-height: 1.4;

  @media (min-width: $mqsm) {
    line-height: 1.8;
  }
}
