// upoutávka/upozornění
.hey {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;

  margin: 20px 0;
  padding: 15px;

  font-size: 19px;

  background-color: $color_main_light;

  @media (min-width: $mqmd) {
    justify-content: space-between;
  }

  p {
    margin: 10px;

    line-height: 1.3;

    @media (max-width: $mqsm - 1px) {
      width: 100%;
    }
  }

  a {
    font-weight: 600;
  }

  .icon {
    width: 25px;
  }
}

// centrováno
.hey--center {
  justify-content: center;

  .hey__info {
    flex: 0 0 auto;
  }
}

// vertikální verze
.hey--vertical {
  justify-content: center;

  text-align: center;

  @media (min-width: $mqmd) {
    flex-direction: column;
  }

  a {
    display: block;
  }
}

// text
.hey__info {
  flex: 1 1 auto;
}

// ikona
.hey__icon {
  @media (max-width: $mqsm - 1px) {
    display: none;
  }
}
