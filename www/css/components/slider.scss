// hlavní slider
.slider {
  position: relative;

  display: none;

  color: $color_white;

  background-color: $color_black;

  @media (min-width: $mqmd) {
    display: block;
  }

  .btn {
    padding: 15px 33px;

    font-size: 15px;
  }

  .is-info {
    .btn {
      color: $color_white;

      background-color: #39d8fd;
    }

    small {
      color: #39d8fd;
    }
  }

  .is-success {
    .btn {
      color: $color_white;

      background-color: $color_success;
    }

    small {
      color: $color_success;
    }
  }

  .is-danger {
    .btn {
      color: $color_white;

      background-color: $color_danger;
    }

    small {
      color: $color_danger;
    }
  }
}

// obrázek
.slider__image {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: $index_page - 1;

  overflow: hidden;

  margin: 0;

  a {
    display: block;
  }

  img {
    display: block;

    height: 100%;
    margin-left: auto;

    object-fit: cover;
  }
}

// obsahová část
.slider__content {
  position: relative;
  z-index: $index_page;

  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;

  width: 50%;
  min-height: 465px;

  text-align: center;
}

// nadpis
.slider__title {
  font-size: 55px;
  font-weight: 700;
  line-height: 1;

  small {
    display: block;

    padding-bottom: 5px;

    color: $color_main;
    font-size: 22px;
    font-weight: 600;
  }
}

// odkaz
.slider__link {
  margin: 0;
}
