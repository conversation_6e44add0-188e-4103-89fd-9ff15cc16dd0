// prodejny
.shop {
  position: relative;

  padding: 30px 0;

  color: $color_white;
  font-size: 16px;

  background-color: $color_gray_dark;

  @media (min-width: $mqmd) {
    padding: 100px 0;

    font-size: 18px;

    &:before {
      content: '';

      position: absolute;
      top: 0;
      bottom: 0;
      left: 50%;

      display: block;

      width: 1px;

      background-color: #272727;
    }
  }

  h2 {
    margin: 20px 0 12px 0;

    font-size: 18px;
    font-weight: 700;

    @media (min-width: $mqsm) {
      font-size: 30px;
    }
  }

  p {
    position: relative;

    margin-bottom: 12px;
    padding-left: 34px;

    .icon {
      position: absolute;
      left: 0;

      @media (min-width: $mqmd) {
        top: 5px;
      }
    }
  }

  img {
    display: inline-block;

    border-radius: $radius;
  }

  .container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .icon {
    width: 24px;
    margin-right: 5px;

    color: $color_main;
  }
}

// obchod
.shop__item {
  position: relative;

  width: 100%;
  max-width: 610px;
}

.shop__item + .shop__item {
  margin-top: 20px;

  @media (min-width: $mqmd) {
    margin-top: 0;
  }
}

// fotografie
.shop__photo {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  padding: 0;

  picture {
    max-width: 48%;
  }
}

p.shop__photo {
  padding: 0;
}

// odkaz
p.shop__link {
  padding: 0;

  @media (min-width: $mqmd) {
    position: absolute;
    right: 0;
    bottom: 0;
  }

  .icon {
    position: relative;
  }
}
