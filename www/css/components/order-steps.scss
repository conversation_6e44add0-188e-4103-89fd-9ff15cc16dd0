// pr<PERSON><PERSON><PERSON> objedn<PERSON>v<PERSON><PERSON>
.order-steps {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;

  margin-top: 20px;
  padding: 20px;

  background: $color_white;

  @media (min-width: $mqmd) {
    flex-wrap: nowrap;

    margin-top: 40px;
    padding: 45px 55px;
  }

  p {
    margin: 0;
  }

  .link {
    display: inline-block;

    margin-bottom: 15px;

    @media (min-width: $mqsm) {
      margin-bottom: 0;
    }
  }

  .btn {
    margin-top: 10px;

    @media (min-width: $mqmd) {
      margin-top: 0;
      padding: 22px 25px 22px 28px;

      white-space: nowrap;
    }
  }
}

// pole s kódem
.order-steps__discount {
  width: 100%;

  @media (min-width: $mqsm) {
    flex: 1 1 1px;

    padding: 0 25px;

    text-align: center;
  }

  .reveal--inline.is-visible {
    display: block;

    margin: 5px 0 0 0;

    @media (min-width: $mqlg) {
      display: inline-block;

      min-width: 300px;
      margin: 0 0 0 20px;
    }
  }

  .form-checkbox {
    margin-bottom: 0;
  }
}

// poznámka
.order-steps__note {
  display: inline-block;

  max-width: 310px;
  margin-top: 10px;

  color: #8b8b8b;
  font-size: 14px;
  line-height: 1.3;
}
