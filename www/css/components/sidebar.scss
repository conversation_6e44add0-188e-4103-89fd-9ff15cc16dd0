// sidebar
.sidebar {
  margin-top: 20px;

  @media (min-width: $mqlg) {
    flex: 0 0 450px;

    margin-top: 0;
    padding-left: 20px;
  }

  & > h3 {
    margin-top: 10px;

    font-size: 20px;
  }

  .order-sum {
    max-width: calc( 100% - 40px );
    margin: 20px;
  }

  .btn--danger {
    margin-bottom: 10px;
  }
}

// filtry
.sidebar--filter {
  display: none;

  width: 100%;
  padding-left: 0;

  @media (min-width: $mqlg) {
    display: block;
    flex: 0 0 350px;

    padding-right: 20px;
  }

  &.is-open {
    display: block;
  }

  p {
    margin-bottom: 20px;
  }

  .form-search {
    max-width: 260px;
  }
}

// verze v objednávce
.sidebar--order {
  flex: 1 1 100%;

  @media (min-width: $mqlg) {
    flex: 0 0 480px;

    padding-left: 20px;
  }

  @media (min-width: $mqxlg) {
    padding-left: 100px;
  }

  & > h3,
  .contact-box {
    @media (max-width: $mqmd - 1px) {
      display: none;
    }
  }

  .product__price {
    line-height: 1.2;
  }
}

// rozbalovací odkaz
.sidebar__show {
  margin-top: -15px;
}
