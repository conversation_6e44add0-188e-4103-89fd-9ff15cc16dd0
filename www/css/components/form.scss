// formul<PERSON><PERSON> - h<PERSON>ní styly
.form {
  padding: 20px;

  color: $color_black;

  background-color: $color_white;

  @media (min-width: $mqsm) {
    padding: 40px;
  }

  h2,
  p {
    max-width: 460px;
    margin-right: auto;
    margin-left: auto;

    &:last-child {
      margin-bottom: 0;
    }
  }

  p {
    line-height: 1.8;
  }

  input,
  select,
  textarea {
    width: 100%;
    max-width: 410px;
    margin-bottom: 3px;
  }

  & + h2 {
    margin-top: 40px;
  }
}

// nezkrácená verze
.form--full {
  h2,
  p {
    max-width: 100%;
  }

  input,
  select,
  textarea {
    @media (min-width: $mqxs) {
      width: auto;
      max-width: 100%;
      margin-right: 15px;
    }
  }
}

// verze bez pozadí
.form--modal {
  padding: 0;

  background: transparent;
}

// povinné pole
.form__required {
  color: $color_danger;
  font-weight: 600;
}

// souhlas s podmínkami
.form__agree {
  .form-checkbox,
  .form-radio {
    font-size: 16px;
  }
}

// poznámka
.form__note {
  margin-top: -10px;

  color: #9e9e9e;
  font-size: 16px;
}

p.form__note {
  line-height: 1.5;
}

// odeslání
.form__submit {
  margin-bottom: 0;
}

// pomocný odkaz
.form__helper {
  display: inline-block;
  float: right;

  margin-left: auto;
  padding-top: 5px;

  font-size: 13px;

  &:hover,
  &:focus {
    color: $color_gray_light;
  }
}

// přeražení hlavičky
a.form__helper {
  text-decoration: underline;
}
