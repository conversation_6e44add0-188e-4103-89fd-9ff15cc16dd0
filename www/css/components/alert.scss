// chybo<PERSON><PERSON> hl<PERSON><PERSON>
.alert {
  padding: 16px 0 15px 0;

  color: $color_white;
  font-size: 18px;
  line-height: 1.2;

  background-color: $color_main;

  @media (min-width: $mqmd) {
    font-size: 20px;
    line-height: 1;
  }

  a {
    color: $color_white;

    &:hover,
    &:focus {
      color: $color_white;
    }
  }

  strong {
    font-weight: 600;
  }

  .icon {
    display: none;

    width: 35px;
    margin-right: 15px;

    color: $color_white;

    @media (min-width: $mqsm) {
      display: inline-block;
    }

    &.icon--delete {
      position: absolute;
      top: 12px;
      right: 0;

      display: inline-block;

      width: 12px;

      cursor: pointer;

      &:hover,
      &:focus {
        opacity: 0.8;
      }
    }
  }

  .container {
    position: relative;

    padding-right: 40px;
  }

  // potvrzující hláš<PERSON>
  &.is-success {
    background-color: $color_success;
  }

  // výstražná hláška
  &.is-danger {
    background-color: $color_danger;
  }

  // informační hlá<PERSON>
  &.is-info {
    color: $color_black;

    background: $color_main;
    background-image: $gradient_gold;

    a,
    .icon {
      color: $color_black;
    }
  }
}
