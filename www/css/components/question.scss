// poradna - <PERSON><PERSON><PERSON><PERSON><PERSON> a odpovědi
.question {
  max-width: 920px;
  margin: 0 auto 25px auto;
  padding: 15px;

  color: $color_gray;
  font-size: 15px;

  border-radius: $radius;
  background-color: $color_white;

  @media (min-width: $mqsm) {
    padding: 25px;
  }

  h2 {
    margin-bottom: 5px;
    padding-left: 30px;

    color: $color_black;
    font-size: 18px;

    background: url( ../img/question.svg ) left center no-repeat;
  }

  p {
    margin: 0;

    line-height: 1.33;
  }

  a {
    color: $color_black;
  }
}

// kratší verze
.question--short {
  max-width: 800px;
  margin-right: 0;
  margin-left: 0;
}

// jméno, datum
.question__meta {
  padding-bottom: 10px;

  color: $color_black;
  font-size: 17px;
}

// odpověď
.question__answer {
  position: relative;

  margin-top: 35px;
  padding: 15px;

  color: #d1d1d1;

  border-radius: $radius;
  background-color: $color_gray_dark;

  @media (min-width: $mqsm) {
    padding: 25px;
  }

  &:before {
    content: '';

    position: absolute;
    top: -5px;
    left: 30px;

    display: block;

    width: 14px;
    height: 14px;

    background-color: $color_gray_dark;

    transform: rotate( 45deg );
  }

  h2 {
    display: inline-block;

    padding: 0;

    color: $color_white;

    background: none;
  }

  a {
    color: $color_white;
  }

  .question__meta {
    display: flex;

    padding: 0 0 15px 0;

    color: $color_white;
    line-height: 1;

    em {
      font-size: 12px;
      font-style: normal;
    }

    .avatar {
      flex-shrink: 0;

      margin-right: 10px;
    }
  }
}

// související produkty
.question__products {
  margin: 30px 0 10px 0;

  h3 {
    margin: 0;

    font-size: 15px;
    line-height: 1.2;
  }

  p {
    flex: 0 0 auto;

    margin: 0;
  }

  a {
    display: inline-flex;

    max-width: 350px;
    margin: 5px 2px 0 0;
    padding: 14px 16px;

    text-decoration: none;

    border-radius: $radius;
    background-color: #272727;

    @media (min-width: $mqsm) {
      align-items: center;

      width: 49%;
    }
  }

  strong {
    display: block;

    padding-top: 5px;

    font-size: 19px;
  }

  del {
    color: #acacac;
    font-size: 12px;
    font-weight: 400;
    vertical-align: middle;
  }

  img {
    display: block;

    width: 40px;
    height: 40px;
    margin-right: 14px;
    margin-bottom: 10px;

    border-radius: $radius;
    background: $color_white;

    @media (min-width: $mqsm) {
      width: 70px;
      height: 70px;
      margin-bottom: 0;
    }
  }
}
