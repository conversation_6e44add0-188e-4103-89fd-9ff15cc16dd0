// odkaz (<PERSON><PERSON><PERSON>, <PERSON>ky...)
.link {
  font-weight: 700;
  text-decoration: underline;

  @media (min-width: $mqsm) {
    font-size: 18px;
  }

  &:after {
    content: '';

    display: inline-block;

    width: 22px;
    height: 22px;
    margin-top: -3px;
    margin-left: 6px;

    vertical-align: middle;

    background: url( ../img/link.svg ) center center no-repeat;
  }
}

// inverzní varianta
.link--inverse {
  color: $color_white;

  &:after,
  &.link--back:before {
    background-image: url( ../img/link-inverse.svg );
  }

  &:hover,
  &:focus {
    color: $color_gray_back;
  }
}

// nahoru
.link--up {
  &:after {
    transform: rotate( -90deg );
  }
}

// dolů
.link--down {
  &:after {
    transform: rotate( 90deg );
  }
}

// zpět
.link--back {
  &:after {
    display: none;
  }

  &:before {
    content: '';

    display: inline-block;

    width: 22px;
    height: 22px;
    margin-top: -3px;
    margin-right: 10px;

    vertical-align: middle;

    background: url( ../img/link.svg ) center center no-repeat;

    transform: rotate( 180deg );
  }
}
