// modal okno
.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: $index_modal;

  display: none;
  overflow: auto;

  width: 100%;

  background-color: rgba( 0, 0, 0, 0.8 );

  &.is-open {
    display: block;
  }
}

// dotaz + hlídání
.modal--question,
.modal--watch {
  .modal__body {
    max-width: 530px;

    @media (min-width: $mqsm) {
      padding: 30px 40px;
    }
  }

  .modal__close {
    top: 15px;
    right: 15px;

    @media (min-width: $mqsm) {
      top: 30px;
      right: 40px;
    }
  }
}

// hlídání
.modal--watch {
  .form input {
    width: 60%;
  }
}

// varianty
.modal--variants {
  h2 {
    margin: 10px 0 30px 0;

    font-size: 23px;
    text-align: center;
  }

  .product--top {
    h3 {
      font-size: 17px;
    }

    .product__meta {
      .btn {
        margin: 0;
      }

      .discount {
        @media (max-width: $mqsm - 1px) {
          display: none;
        }
      }
    }

    .store {
      font-size: 13px;
    }

    .product__flavour {
      right: 5px;
      bottom: -2px;

      width: 30px;
      height: 30px;
      padding: 3px;

      @media (min-width: $mqsm) {
        right: -2px;

        width: 40px;
        height: 40px;
        padding: 3px;
      }
    }
  }
}

// dostupnost
.modal--availability {
  @media (min-width: $mqsm) {
    position: fixed;

    overflow: auto;
  }

  h2 {
    margin: 10px 0 30px 0;

    font-size: 23px;
    text-align: center;
  }

  .modal__body {
    max-width: 530px;

    @media (min-width: $mqsm) {
      top: 50%;
      left: 50%;

      transform: translate( -50%, -50% );
    }
  }
}

// tmavá verze
.modal--dark {
  h2 {
    margin: 10px 0 30px 0;

    color: $color_white;
    text-align: center;

    .icon {
      width: 30px;
      margin-right: 5px;

      color: $color_white;
    }
  }

  .modal__body {
    background-color: #272727;
  }

  .modal__close {
    .icon {
      color: $color_white;
    }
  }

  .modal__products {
    margin: 40px -20px -20px -20px;
  }

  .store {
    font-size: 13px;
  }
}

// tělo
.modal__body {
  position: relative;

  width: 100%;
  max-width: 920px;
  padding: 15px;

  background-color: $color_gray_back;

  @media (min-width: $mqsm) {
    top: 50px;
    left: 50%;

    padding: 20px;

    transform: translateX( -50% );
  }
}

// zavřít okno
.modal__close {
  position: absolute;
  top: 20px;
  right: 20px;

  overflow: hidden;

  width: 26px;
  height: 26px;

  cursor: pointer;

  @media (min-width: $mqsm) {
    width: 42px;
    height: 42px;
  }

  &:hover,
  &:focus {
    opacity: 0.7;
  }

  .icon {
    width: 26px;

    @media (min-width: $mqsm) {
      width: 42px;
    }
  }
}

// odeslání
.modal__submit {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  @media (min-width: $mqsm) {
    flex-wrap: nowrap;
    justify-content: space-between;

    margin: 25px 10px;
  }

  p {
    margin: 15px;

    @media (min-width: $mqsm) {
      margin: 0;
    }
  }

  .link {
    font-size: 16px;
  }
}

// podobné produkty
.modal__products {
  padding: 15px 25px 10px 25px;

  background-color: $color_gray_back;

  h3 {
    margin-bottom: 5px;

    font-size: 24px;
  }
}

// dostupnost
.modal__availability {
  margin: 0;
  padding: 0 10px;

  list-style-type: none;

  font-size: 20px;

  .icon {
    width: 12px;

    color: $color_main;
  }

  .store {
    font-size: 18px;
  }
}
