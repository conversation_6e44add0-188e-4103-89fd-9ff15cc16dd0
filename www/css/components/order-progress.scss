// objednávka - ukazatel
.order-progress {
  padding: 20px 0;

  background-color: $color_gray_dark;

  @media (min-width: $mqsm) {
    padding: 40px 0;
  }

  @media (min-width: $mq_menu) {
    margin-top: -21px;
  }

  @media (min-width: $mqlg) {
    margin-top: -64px;
  }

  ol {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;

    margin: 0;
    padding: 0;

    list-style-type: none;

    color: #9e9e9e;
    font-size: 20px;

    li {
      position: relative;

      margin: 0;
      padding: 0 15px 0 0;

      @media (min-width: $mqsm) {
        padding: 0 35px;
      }
    }

    @media (min-width: $mqsm) {
      li + li {
        &:before {
          content: '';

          position: absolute;
          top: 50%;
          left: 0;

          width: 5px;
          height: 8px;
          margin-top: -5px;

          background: url( ../img/arrow.svg ) center center no-repeat;
        }
      }
    }

    a {
      color: $color_white;
      text-decoration: none;

      &:hover,
      &:focus {
        color: #9e9e9e;
      }

      .order-progress__number {
        border-color: $color_white;
      }
    }

    .is-active {
      color: $color_white;
      font-weight: 700;

      a {
        color: $color_white;
      }

      .order-progress__number {
        color: $color_main;

        border-color: $color_white;
      }
    }
  }
}

// číslo
.order-progress__number {
  display: inline-flex;
  align-items: center;
  justify-content: center;

  width: 30px;
  height: 30px;
  margin-right: 5px;
  padding-left: 2px; // vizuální centrování

  font-size: 16px;
  font-weight: 700;
  line-height: 1;

  border: 2px solid #9e9e9e;
  border-radius: 50%;

  @media (min-width: $mqsm) {
    width: 46px;
    height: 46px;
    margin-right: 15px;
  }
}
