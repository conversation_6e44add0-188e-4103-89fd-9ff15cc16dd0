// tlačítko
.btn,
a.btn {
  display: inline-block;

  padding: 13px 22px;

  color: $color_black;
  font: inherit;
  font-size: 13px;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;

  border: none;
  border-radius: $radius;
  background-color: $color_main;

  cursor: pointer;

  &:hover,
  &:focus {
    background-image: $gradient_gold;
  }

  .icon {
    width: 11px;
    margin: -4px 0 0 10px;
  }

  .icon--next {
    display: none;

    @media (min-width: $mqxs) {
      display: inline-block;
    }
  }
}

// velké tlačítko
.btn--big,
a.btn--big {
  font-size: 18px;

  @media (min-width: $mqxs) {
    padding: 15px 25px;
  }
}

// potvrzující tlačítko
.btn--success,
a.btn--success {
  color: $color_white;

  background-color: $color_success;

  &:hover,
  &:focus {
    background-color: darken( $color_success, 10% );
    background-image: none;
  }

  .icon {
    color: $color_white;
  }
}

// chybové tla<PERSON>ko
.btn--danger,
a.btn--danger {
  color: $color_white;

  background-color: $color_danger;

  &:hover,
  &:focus {
    background-color: darken( $color_danger, 10% );
    background-image: none;
  }

  .icon {
    color: $color_white;
  }
}

// pomocné tlačítko
.btn--helper,
a.btn--helper {
  color: $color_white;

  background-color: $color_gray;

  &:hover,
  &:focus {
    color: $color_black;

    background-color: $color_white;
    background-image: none;
  }
}

// jemnější verze tlačítka
.btn--soft,
a.btn--soft {
  background-color: $color_main_light;

  &:hover,
  &:focus {
    background-color: $color_main;
    background-image: none;
  }
}

// login tlačítko (externí služby)
.btn--login,
a.btn--login {
  width: 100%;
  max-width: 410px;

  font-size: 15px;

  @media (min-width: $mqxs) {
    padding: 15px 25px;

    font-size: 18px;
  }

  &:hover,
  &:focus {
    background-color: $color_black;
    background-image: none;
  }

  .icon {
    width: 22px;
    margin-right: 5px;

    color: $color_white;
  }

  & + & {
    margin-top: 10px;
  }
}

// login - seznam
.btn--seznam,
a.btn--seznam {
  color: $color_white;

  background-color: #c00;
}

// login - facebook
.btn--facebook,
a.btn--facebook {
  color: $color_white;

  background-color: #0866ff;
}

// login - google
.btn--google,
a.btn--google {
  background-color: #f7f7f7;

  &:hover,
  &:focus {
    background-color: #dfdfdf;
    background-image: none;
  }
}

// login - apple
.btn--apple,
a.btn--apple {
  color: $color_white;

  background-color: #6e6e73;
}
