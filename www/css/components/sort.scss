// řazení
.sort {
  display: flex;

  margin: 0;
  padding: 10px 0;

  list-style-type: none;

  text-transform: uppercase;

  @media (min-width: $mqsm) {
    margin: 25px 0;
    padding: 0;

    border-bottom: 1px solid #dedede;
  }

  @media (max-width: $mqsm - 1px) {
    overflow-x: auto;
  }

  li {
    margin: 0 10px -1px 0;
    padding: 5px;

    border-bottom: 1px solid transparent;

    @media (min-width: $mqsm) {
      margin-right: 40px;
      padding: 15px 0;
    }
  }

  a {
    color: $color_gray;
    white-space: nowrap;
    text-decoration: none;

    &:hover,
    &:focus {
      color: $color_black;
    }
  }

  .is-active {
    font-weight: 700;

    @media (min-width: $mqsm) {
      border-color: $color_main;
    }

    a {
      color: $color_black;
    }
  }
}

// centrované
.sort--center {
  @media (min-width: $mqsm) {
    justify-content: center;

    li {
      margin: 0 20px -1px 20px;
    }
  }
}
