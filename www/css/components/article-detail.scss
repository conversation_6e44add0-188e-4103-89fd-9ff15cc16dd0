// detail článku
.article-detail {
  margin-top: -10px;

  @media (min-width: $mqsm) {
    margin-top: -220px;
  }
}

// hlav<PERSON><PERSON><PERSON> + obr<PERSON><PERSON><PERSON>
.article-detail__head {
  position: relative;

  margin: 0;

  border-radius: $radius;
  background: $color_black;

  @media (min-width: $mqsm) {
    overflow: hidden;
  }

  img {
    display: block;
  }
}

// informace (rubrika, autor)
.article-detail__meta {
  margin: 0;
  padding: 20px;

  color: $color_white;
  font-size: 15px;

  border-radius: $radius;
  background-image: linear-gradient( 180deg, rgba( 0, 0, 0, 0 ) 0%, rgba( 0, 0, 0, 0.47 ) 100% );

  @media (min-width: $mqsm) {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;

    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    padding: 30px 40px;
  }

  h1 {
    margin: 0 0 15px 0;

    font-size: 25px;

    @media (min-width: $mqsm) {
      margin: 0 10px 0 0;

      font-size: 40px;
    }

    small {
      display: block;

      color: $color_main;
      font-size: 14px;
      font-weight: 700;
      text-transform: uppercase;
    }
  }

  p {
    margin-bottom: 10px;

    @media (min-width: $mqsm) {
      text-align: center;
    }

    strong {
      font-size: 21px;
      font-weight: 600;
      line-height: 1;
    }

    .avatar {
      margin: 0 auto 10px auto;

      @media (max-width: $mqsm - 1px) {
        display: none;
      }
    }
  }
}

// obsahová část
.article-detail__content {
  padding: 20px 10px;

  @media (min-width: $mqsm) {
    padding: 40px;
  }

  .product--top {
    margin-bottom: 40px;

    .product__content {
      padding: 0 0 10px 0;

      p {
        @media (min-width: $mqsm) {
          max-width: 350px;
        }
      }
    }
  }
}

// hodnocení
.article-detail__rating {
  margin-top: 20px;
  padding: 15px;

  color: $color_gray;
  font-size: 15px;
  text-align: center;

  background-color: $color_white;

  @media (min-width: $mqsm) {
    padding: 40px;
  }

  ul,
  li {
    margin: 0;
    padding: 0;

    list-style-type: none;
  }

  li {
    display: inline-block;

    width: 49%;
    padding-bottom: 15px;

    @media (min-width: $mqsm) {
      width: 20%;
      padding-bottom: 0;
    }

    strong {
      display: block;

      font-size: 25px;
      line-height: 1;
    }
  }
}

// hvězdičky
.article-detail__stars {
  margin: 0;

  font-size: 18px;

  @media (min-width: $mqsm) {
    margin-top: 30px;
  }

  strong {
    font-size: 33px;
    line-height: 1;
  }

  .star {
    margin: 8px 2px 0 2px;
  }
}

// autor - detailnější info
.article-detail__author {
  margin: 40px 0;
  padding: 20px;

  font-size: 16px;
  line-height: 1.5;

  background-color: #eee;

  @media (min-width: $mqsm) {
    padding: 40px;
  }

  & > p {
    margin-bottom: 0;

    line-height: 1.5;
  }

  .contact-box {
    flex-wrap: wrap;

    margin-bottom: 10px;

    p {
      max-width: 100%;
    }

    strong {
      font-size: 25px;
      font-weight: 600;
    }
  }
}
