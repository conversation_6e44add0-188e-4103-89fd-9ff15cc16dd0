// slider - <PERSON><PERSON><PERSON><PERSON><PERSON> styly
.splide {
  position: relative;

  .product {
    width: auto;
    height: 100%;
    margin: 0 12px;
  }
}

// hlavní slider
.splide--main {
  margin: 0;

  .splide__arrow {
    background-image: url( ../img/pagination-next-inverse.svg );
  }

  .splide__arrow--prev {
    left: 20px;

    @media (min-width: $mqxxlg) {
      left: 80px;
    }
  }

  .splide__arrow--next {
    right: 20px;

    @media (min-width: $mqxxlg) {
      right: 80px;
    }
  }
}

// úprava pro produkty
.splide--products {
  margin: 0 20px;

  @media (min-width: $mqxxlg) {
    margin: 0 -12px;
  }
}

// šipky
.splide__arrow {
  position: absolute;
  top: 50%;
  z-index: $index_page + 1;

  overflow: hidden;

  width: 34px;
  height: 34px;

  text-indent: 9999px;

  border: none;
  background: transparent url( ../img/pagination-next.svg ) center center no-repeat;
  background-size: 34px 34px;

  transform: translateY( -50% );

  @media (min-width: $mqxxlg) {
    width: 64px;
    height: 64px;

    background-size: 64px 64px;
  }

  &:hover,
  &:focus {
    opacity: 0.7;
  }

  &:disabled {
    opacity: 0.2;
  }
}

// přechozí
.splide__arrow--prev {
  left: -27px;

  margin-top: -15px;

  transform: rotate( -180deg );

  @media (min-width: $mqxxlg) {
    left: -80px;
  }
}

// následující
.splide__arrow--next {
  right: -27px;

  @media (min-width: $mqxxlg) {
    right: -80px;
  }
}

// stránkování
.splide__pagination {
  position: absolute;
  bottom: 0;
  left: 50%;
  z-index: $index_page + 1;

  margin: 0;
  padding: 7px 10px;

  border-top-left-radius: $radius;
  border-top-right-radius: $radius;
  background-color: #131313;

  transform: translateX( -50% );

  button {
    padding: 0 10px;

    color: $color_white;
    font-size: 15px;
    font-weight: 600;

    border: none;
    background: transparent;

    opacity: 0.3;

    &.is-active {
      opacity: 1;
    }
  }
}
