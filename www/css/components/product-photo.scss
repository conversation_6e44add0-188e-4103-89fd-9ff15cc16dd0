// fotografie detailu + galerie
.product-photo {
  position: relative;

  width: 100%;
  max-width: 560px;
  margin-top: 25px;

  @media (min-width: $mqlg) {
    position: absolute;
    top: 45px;
    left: 12px;
    z-index: $index_page + 1;

    margin-top: 0;
  }

  img {
    display: block;

    transition: all 0.2s;
  }
}

// hlavní fotografie
.product-photo__main {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;

  width: 100%;
  margin: 0;

  background-color: $color_white;

  @media (min-width: $mqlg) {
    height: 560px;
  }

  a {
    &:hover,
    &:focus {
      img {
        @media (min-width: $mqsm) {
          transform: scale( 1.05 );
        }
      }
    }
  }
}

// štítky, oblíbené, hodnocení
.product-photo__tags,
.product-photo__fav,
.product-photo__stars {
  position: absolute;
  z-index: $index_page + 1;

  margin: 0;
}

// štítky
.product-photo__tags {
  top: 15px;
  left: 20px;

  width: 120px;
}

// oblíbené
.product-photo__fav {
  top: 15px;
  right: 18px;

  .icon {
    width: 20px;
  }

  .icon--heart-full {
    color: $color_danger;
  }
}

// hodnocení
.product-photo__stars {
  top: 50px;
  right: 16px;

  width: 24px;
}

// další forografie
.product-photo__gallery {
  position: relative;

  display: flex;
  flex-wrap: wrap;

  margin: 8px -4px;

  @media (min-width: $mqsm) {
    flex-wrap: nowrap;
  }

  a {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;

    width: calc( 33.33333% - 8px );
    height: 105px;
    margin: 4px;

    background-color: $color_white;

    @media (min-width: $mqsm) {
      width: calc( 25% - 8px );
    }

    &:hover,
    &:focus {
      img {
        @media (min-width: $mqsm) {
          transform: scale( 1.1 );
        }
      }
    }
  }
}

// více fotografií
.product-photo__more {
  flex-wrap: wrap;

  font-size: 14px;
  font-weight: 700;
}
