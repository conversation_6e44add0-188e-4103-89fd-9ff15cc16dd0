// v<PERSON><PERSON><PERSON><PERSON>, našeptávač
.search {
  position: relative;

  display: none;
  flex: 1 1 55%;

  margin: 0 25px;

  @media (min-width: $mq_menu) {
    display: block;

    max-width: 630px;
  }

  @media (min-width: $mqlg) {
    margin: 0;
  }

  &.is-open {
    position: absolute;
    top: 70px;
    left: 0;
    z-index: $index_modal - 1;

    display: block;

    width: 100%;
    margin: 0;
    padding: 10px;

    background: $color_gray_dark;
  }

  .form-search {
    position: relative;
    z-index: $index_menu + 3;
  }
}

// našeptávač
.search__detail {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: $index_menu + 2;

  display: none;
  overflow: auto;

  max-height: calc( 100vh - 70px );
  padding: 60px 0 20px 0;

  color: $color_white;

  border-radius: $radius;
  background-color: $color_gray_dark;

  @media (min-width: $mq_menu) {
    top: -10px;
    right: -10px;
    left: -10px;

    max-height: calc( 100vh - 108px );
  }

  &.is-open {
    display: block;
  }

  h2 {
    font-size: 18px;
    font-weight: 600;
  }

  & > p {
    &:last-child {
      margin: 20px 0 0 0;
    }
  }

  // scroolbar
  &::-webkit-scrollbar {
    width: 10px;
  }

  &::-webkit-scrollbar-track {
    border-radius: $radius;
    background-color: $color_black;
  }

  &::-webkit-scrollbar-thumb {
    border: 3px solid $color_black;
    border-radius: $radius;
    background: $color_main;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: $color_main;
  }
}

// odkazy
.search__content {
  padding: 15px 18px;

  font-size: 13px;

  h2 {
    margin-bottom: 5px;
  }

  ul {
    margin: 0;
    padding: 0;

    list-style-type: none;
  }

  li {
    margin: 0;
    padding: 2px 0;
  }

  a {
    color: $color_white;

    &:hover,
    &:focus {
      color: $color_main;
    }
  }
}

// výpisy (produkty, články...)
.search__list {
  position: relative;

  padding: 15px 18px;

  background-color: #272727;

  .product__price {
    @media (max-width: $mqsm - 1px) {
      padding: 0 20px;
    }

    del {
      display: block;

      color: #acacac;
    }
  }

  .product--basket.product--top {
    margin-bottom: 10px;
  }

  .article {
    color: $color_white;
  }

  .btn {
    white-space: nowrap;
  }
}

// počet
.search__number {
  display: inline-block;

  width: 24px;
  height: 24px;
  margin: 0 3px;
  padding-top: 5px;

  font-size: 10px;
  font-weight: 400;
  line-height: 1;
  text-align: center;
  vertical-align: middle;

  border: 1px solid #3b3b3b;
  border-radius: 50%;
}

// zavřít okno
.search__close {
  position: absolute;
  top: 70px;
  right: 15px;
  z-index: $index_page + 1;

  overflow: hidden;

  width: 25px;
  height: 25px;

  cursor: pointer;

  &:hover,
  &:focus {
    opacity: 0.7;
  }

  .icon {
    width: 25px;

    color: $color_white;
  }
}
