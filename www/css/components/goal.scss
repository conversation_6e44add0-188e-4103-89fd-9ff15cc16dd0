// v<PERSON><PERSON> c<PERSON>l<PERSON>
.goal {
  h3 {
    margin-bottom: 5px;

    font-size: 20px;
    text-transform: uppercase;
  }

  ul,
  li {
    margin: 0;
    padding: 0;

    list-style-type: none;
  }

  a {
    color: #d8d8d8;
    font-size: 15px;
    text-decoration: none;
    text-transform: uppercase;

    &:hover,
    &:focus {
      color: $color_main;
    }
  }
}

// obrázek
.goal__image {
  position: relative;

  overflow: hidden;

  margin: 0;

  border-radius: $radius;

  &:hover,
  &:focus {
    img {
      transform: scale( 1.07 );
    }
  }

  img {
    display: block;

    transition: all 0.2s;
  }
}

// odkaz
.goal__content {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: $index_page + 1;

  display: flex;
  flex-direction: column;
  justify-content: flex-end;

  margin: 0;
  padding: 20px 30px;

  background-image: linear-gradient( 180deg, rgba( 0, 0, 0, 0 ) 0%, rgba( 0, 0, 0, 0.6 ) 57%, rgba( 0, 0, 0, 0.58 ) 100% );
}
