// stránkování
.pagination {
  // základní definice boxů
  a,
  span {
    display: inline-block;
    overflow: hidden;

    width: 32px;
    height: 32px;

    color: $color_black;
    font-size: 15px;
    font-weight: 600;
    line-height: 32px;
    text-align: center;
    vertical-align: middle;
    text-decoration: none;

    border-radius: 50%;
    background-color: #ececec;
  }
}

// aktivní stránka
span.paginator__current {
  background-color: $color_white;
}

// trojtečka
span.paginator__space {
  width: 15px;

  background: transparent;
}

// šipky
a.pagination__prev,
a.pagination__next {
  width: 42px;
  height: 42px;

  text-indent: 9999px;

  background: transparent;
  background-image: url( ../img/pagination-next.svg );
  background-repeat: no-repeat;
  background-position: center center;
}

// předchozí
.pagination__prev {
  transform: rotate( 180deg );
}
