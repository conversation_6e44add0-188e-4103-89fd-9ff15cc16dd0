// objedn<PERSON><PERSON><PERSON> - do<PERSON><PERSON>, slevy, registrace
.order-benefits {
  display: flex;
  flex-wrap: wrap;

  margin-top: 40px;

  @media (min-width: $mqsm) {
    flex-wrap: nowrap;
  }

  .hey {
    display: none;
    flex: 0 0 290px;

    margin: 0;

    @media (min-width: $mqsm) {
      display: flex;
    }
  }
}

// verze do modal okna (vedle sebe)
.order-benefits--modal {
  margin-top: 30px;

  .order-benefits__info {
    display: flex;
    justify-content: space-between;

    margin: 0;
    padding: 0;
  }

  .order-benefits__col {
    width: 48%;
  }
}

// hlavní část
.order-benefits__info {
  width: 100%;
  padding: 20px 25px 30px 25px;

  color: $color_white;
  font-size: 18px;
  font-weight: 600;

  background-color: #272727;

  @media (min-width: $mqsm) {
    margin-right: 25px;
  }

  small {
    margin: auto 0 0 auto;
    padding-left: 5px;

    font-size: 13px;
    font-weight: 400;
  }

  p {
    display: flex;
    flex-wrap: wrap;

    margin: 0 0 2px 0;
  }

  p + p {
    margin-top: 10px;
  }

  .icon {
    width: 18px;
    margin-right: 4px;

    color: $color_main;
  }

  .icon--car {
    color: $color_delivery;
  }

  .progress {
    margin: 2px 0 10px 0;
  }
}
