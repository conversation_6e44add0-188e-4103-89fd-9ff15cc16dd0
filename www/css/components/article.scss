// v<PERSON><PERSON>
.article {
  text-decoration: none;

  h3 {
    margin-bottom: 15px;
  }
}

// v<PERSON><PERSON>ř<PERSON>
.article--wide {
  display: flex;
  flex-wrap: wrap;

  max-width: 930px;
  margin: 0 auto 25px auto;

  @media (min-width: $mqsm) {
    flex-wrap: nowrap;

    margin-bottom: 45px;
  }

  .article__content {
    @media (min-width: $mqsm) {
      padding: 10px 10px 10px 20px;
    }

    @media (min-width: $mqmd) {
      padding-left: 45px;
    }
  }
}

// malá verze (do našeptávače)
.article--small {
  display: flex;
  align-items: center;

  & + & {
    margin-top: 12px;
  }

  h3 {
    margin: 0;

    font-size: 16px;

    strong {
      display: block;

      color: $color_main;
      font-size: 12px;
    }
  }

  .article__image {
    flex: 0 0 100px;
  }

  .article__content {
    padding: 0 0 0 17px;
  }
}

// obr<PERSON>zek
.article__image {
  position: relative;

  overflow: hidden;

  margin: 0;

  border-radius: $radius;

  @media (min-width: $mqmd) {
    flex: 1 0 auto;
  }

  img {
    display: block;

    width: 100%;
    height: 100%;

    object-fit: cover;
  }
}

// informace (rubrika, autor)
.article__meta {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;

  display: flex;
  align-items: flex-end;

  margin: 0;
  padding: 10px 23px;

  color: $color_white;
  font-size: 14px;

  background-image: linear-gradient( 180deg, rgba( 0, 0, 0, 0 ) 0%, rgba( 0, 0, 0, 0.47 ) 100% );

  strong {
    display: inline-block;

    margin-right: 20px;

    color: $color_main;
    text-transform: uppercase;
  }
}

// obsahová část
.article__content {
  display: flex;
  flex: 0 1 auto;
  flex-direction: column;

  padding-top: 20px;
}

// perex
.article__description {
  margin-bottom: 20px;

  color: $color_gray;
  font-size: 15px;
  line-height: 1.53;
}

// odkaz
.article__link {
  margin-top: auto;
  margin-bottom: 0;
}
