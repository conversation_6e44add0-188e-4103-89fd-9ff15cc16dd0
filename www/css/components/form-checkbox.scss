// checkbox
.form-checkbox {
  position: relative;

  display: inline-block;

  min-height: 30px;
  margin-bottom: 3px;
  padding-left: 40px;

  font-size: 17px;

  cursor: pointer;

  input {
    position: absolute;

    width: auto;

    opacity: 0;
  }

  input:checked ~ .form-checkbox__checker {
    background: $color_gray_dark url( ../img/checkbox.svg ) center center no-repeat;
  }

  input:checked ~ .form-checkbox__label {
    font-weight: 600;
  }

  input:disabled ~ .form-checkbox__checker {
    border-color: $color_gray_medium;
  }

  input:disabled ~ .form-checkbox__label {
    color: $color_gray_medium;
  }

  // slepé odkazy
  a.js-nopass {
    text-decoration: none;

    cursor: default;
    pointer-events: none;

    &:hover,
    &:focus {
      color: inherit;
    }
  }
}

// ukazatel
.form-checkbox__checker {
  position: absolute;
  top: 3px;
  left: 3px;

  display: inline-block;

  width: 23px;
  height: 23px;

  border: 2px solid $color_black;
  background-color: transparent;
}
