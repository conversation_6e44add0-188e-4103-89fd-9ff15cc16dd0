// hlavi<PERSON><PERSON> - horní část
.header-top {
  display: none;

  padding: 8px 0;

  font-size: 14px;

  background-color: #f6f6f6;

  @media (min-width: $mq_menu) {
    display: block;
  }

  p {
    margin: 0;

    line-height: 1;
  }

  a {
    text-decoration: none;

    &:hover,
    &:focus {
      color: $color_black;
    }
  }

  .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  // provozní doba
  .phone {
    color: $color_gray;
    font-size: 12px;
    line-height: 12px;

    &:before {
      top: 4px;
    }
  }
}

// navigace
.header-top__nav {
  @media (min-width: $mqlg) {
    flex: 1 1 33%;
  }

  ul,
  li {
    margin: 0;
    padding: 0;

    list-style-type: none;

    line-height: 1;
  }

  li {
    display: inline-block;

    &:hover,
    &:focus {
      ul {
        display: block;
      }
    }

    &.has-submenu {
      margin-right: 12px;

      background: url( ../img/nav.svg ) center right no-repeat;
      background-size: 8px auto;

      &:hover,
      &:focus {
        background-image: url( ../img/nav-open.svg );
      }
    }
  }

  li ul {
    position: absolute;
    z-index: $index_modal + 2;

    display: none;

    padding-top: 5px;

    li {
      display: block;

      padding: 5px 15px;

      background-color: #f6f6f6;

      &:first-child {
        padding-top: 10px;
      }

      &:last-child {
        padding-bottom: 10px;
      }
    }
  }

  a {
    margin-right: 17px;

    color: $color_gray;
  }
}

// kontakt
.header-top__contact {
  display: none;
  align-items: center;
  justify-content: center;

  margin: 0;

  font-size: 13px;

  @media (min-width: $mqsm) {
    display: flex;
  }

  @media (min-width: $mqlg) {
    flex: 1 1 33%;
  }

  a {
    margin: 0 6px 0 15px;

    font-size: 15px;
    font-weight: 500;
  }
}

// přepínač jazyků
.header-top__lang {
  text-align: right;

  @media (min-width: $mqlg) {
    flex: 1 1 33%;
  }

  a {
    opacity: 0.5;

    &:hover,
    &:focus,
    &.is-active {
      opacity: 1;
    }
  }
}
