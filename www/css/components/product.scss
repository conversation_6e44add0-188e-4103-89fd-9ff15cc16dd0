// výpis produktů
.product {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;

  width: 100%;
  padding: 17px;

  background-color: $color_white;

  @media (min-width: $mqsm) {
    flex-wrap: nowrap;
  }

  h3 {
    flex: 1 1 auto;

    margin-bottom: 10px;

    font-size: 16px;

    a {
      text-decoration: none;
    }
  }

  p {
    margin: 0;
  }
}

// nejprodávanější
.product--top {
  align-items: center;
  flex-direction: row;

  margin-top: 8px;
  padding: 10px;

  @media (min-width: $mqxs) {
    padding-right: 25px;
  }

  h3 {
    margin-bottom: 0;
  }

  .product__image {
    flex: 0 0 80px;

    padding: 0;

    img {
      display: block;
    }
  }

  .product__content {
    flex: 1 1 calc( 100% - 48px );

    padding: 0 0 15px 0;

    @media (min-width: 450px) {
      padding-left: 10px;
    }

    @media (min-width: $mqxs) {
      padding: 0 20px 0 10px;
    }

    @media (min-width: $mqsm) {
      flex: 1 1 auto;
    }

    p {
      max-width: 400px;
    }
  }

  .product__meta {
    margin: 0;
    padding: 0;

    @media (min-width: $mqsm) {
      text-align: right;
    }

    .btn {
      margin-left: 26px;
    }
  }
}

// produkt v sidebaru (vychází z .product--top)
.product--side {
  position: relative;

  flex-wrap: wrap;

  min-height: 150px;

  @media (min-width: $mqmd) {
    padding-left: 140px;
  }

  .product__image {
    @media (min-width: $mqmd) {
      position: absolute;
      left: 15px;
    }
  }

  .product__meta {
    width: 100%;

    text-align: left;
  }
}

// produkt v sidebaru v objednávce (vychází z .product--top)
.product--sum {
  flex-wrap: nowrap;

  min-height: 60px;
  padding: 10px 10px 10px 5px;

  .product__content {
    padding-left: 0;

    h3 {
      margin: 0;

      font-size: 15px;
      font-weight: 500;
    }
  }

  .product__price {
    font-size: 15px;
  }

  .product__meta {
    width: auto;
  }
}

// produkt ve vyskakovacím košíku
.product--basket {
  color: $color_white;

  background: transparent;

  &.product--top {
    margin: 0 0 15px 0;
    padding: 0;

    .product__image {
      overflow: hidden;
      flex: 0 0 50px;

      border-radius: $radius;
    }

    .product__content {
      padding: 0 0 0 20px;
    }
  }

  h3 {
    font-size: 15px;

    a {
      color: $color_white;
    }
  }

  .product__price {
    color: $color_white;
  }

  .product__count {
    input {
      color: $color_white;
    }

    .icon {
      margin-right: 0;

      color: $color_white;
    }
  }
}

// objednávka
.product--order {
  .product__price {
    margin-left: auto;
  }
}

// nejprodávanější - číslo
.product__top {
  margin: 0;
  padding: 0 10px 0 5px;

  color: $color_main;
  font-size: 30px;
  font-weight: 700;

  &--2 {
    color: #ffda61;
  }

  &--3 {
    color: #ffe9a1;
  }
}

// obrázek
.product__image {
  position: relative;

  padding: 30px 0 20px 0;

  text-align: center;

  a {
    display: block;

    &:hover,
    &:focus {
      @media (min-width: $mqsm) {
        img {
          transform: scale( 1.1 );
        }

        .product__flavour {
          transform: scale( 1.2 );
        }
      }
    }
  }

  img {
    display: inline-block;

    margin: 0 auto;

    transition: all 0.2s;
  }
}

// obrázek - menší verze pro dopravy a platby
.product__image--small {
  img {
    height: 40px;
  }
}

// příchuť
.product__flavour {
  position: absolute;
  right: 4px;
  bottom: -3px;

  display: block;
  overflow: hidden;

  width: 26px;
  height: 26px;
  padding: 2px;

  border: 1px solid $color_main;
  border-radius: 50%;
  background-color: $color_white;

  transition: all 0.2s;

  @media (min-width: $mqxs) {
    right: -3px;
  }

  img {
    width: 100%;
    height: auto;
  }
}

// štítky
.product__tags {
  position: absolute;
  top: 0;
  left: 0;

  width: 120px;

  line-height: 1;
  text-align: left;
}

// sleva
.product__discount {
  position: absolute;
  bottom: 15px;
  left: 0;
}

// oblíbené
.product__fav {
  position: absolute;
  top: 0;
  right: 0;

  .icon {
    width: 18px;

    color: #cecece;

    &:hover,
    &:focus {
      color: $color_danger;
    }
  }

  .icon--heart-full {
    color: $color_danger;
  }
}

// popis
.product__description {
  color: $color_gray;
  font-size: 14px;
  line-height: 1.58;

  .col--4 & {
    @media (min-width: $mqlg) {
      min-height: 67px;
    }
  }
}

// cena, zakoupení, skladem
.product__meta {
  display: flex;
  align-items: center;
  justify-content: space-between;

  margin-top: auto;
  padding-top: 15px;

  @media (max-width: $mqsm - 1px) {
    width: 100%;
  }

  .discount {
    margin-right: 20px;
  }

  .btn {
    white-space: nowrap;
  }
}

// cena
.product__price {
  color: $color_black;
  font-size: 19px;
  line-height: 1;

  @media (min-width: $mqmd) {
    white-space: nowrap;
  }

  strong,
  del {
    white-space: nowrap;
  }

  del {
    display: inline-block;

    padding-left: 3px;

    color: $color_gray;
    font-size: 12px;
  }

  & + & {
    padding-left: 15px;
  }
}

// přičítání/odečítání
.product__count {
  padding: 0 15px;

  white-space: nowrap;

  input {
    width: 28px;
    padding: 5px 0;

    text-align: center;

    border: none;
    background: transparent;
  }

  .icon {
    width: 24px;
    margin-top: -3px;

    cursor: pointer;
    user-select: none;

    @media (min-width: $mqxs) {
      width: 20px;
    }

    &:hover,
    &:focus {
      opacity: 0.6;
    }
  }
}

// odstranění
.product__remove {
  margin-left: 15px;

  text-decoration: none;

  &:hover,
  &:focus {
    .icon {
      color: $color_danger;
    }
  }

  .icon {
    width: 14px;

    color: #9e9e9e;
  }
}

// odkazy na více produktů
.product__links {
  margin: 15px 0;

  .btn {
    margin: 3px 0;
  }
}
