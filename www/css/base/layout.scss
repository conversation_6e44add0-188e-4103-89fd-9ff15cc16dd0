// h<PERSON><PERSON><PERSON>
.container {
  max-width: $mqxlg;
  margin: 0 auto;
  padding: 0 $layout_gap;
}

// krat<PERSON><PERSON> verze
.container--short {
  max-width: 920px;
}

// verze se sidebarem
.container--sidebar {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  @media (min-width: $mqlg) {
    flex-wrap: nowrap;
  }
}

// řádek m<PERSON>
.row {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;

  margin: 0 (-$layout_gap);

  // mezera mezi boxy
  & > .col {
    padding: $layout_gap;
  }
}

// fix pro max-width tabulku
.row--block {
  @media (max-width: $mqsm) {
    display: block;
  }
}

// řádek se stejně vysokými bloky
.row--align {
  & > .col {
    flex-direction: row;
  }
}

// mezery mezi bloky
.row--space {
  justify-content: space-between;
}

// centrování
.row--center {
  align-items: center;
}

// zarovná<PERSON><PERSON> zlev<PERSON>
.row--start {
  justify-content: flex-start;
}

// ř<PERSON><PERSON> bez mezer
.row--nogap {
  margin: 0;

  & > .col {
    padding: 0;
  }
}

// blok mří<PERSON>ky
.col {
  display: flex;
  flex: 0 0 100%;
  flex-direction: column;
}

// dva vedle sebe
.col--2 {
  @media (min-width: $mqxs) {
    flex-basis: auto;

    width: 50%;
  }
}

// tři vedle sebe
.col--3 {
  @media (min-width: $mqxs) {
    flex-basis: auto;

    width: 50%;
  }

  @media (min-width: $mqmd) {
    width: 33.33333333%;
  }
}

// čtyři vedle sebe
.col--4 {
  @media (min-width: $mqxs) {
    flex-basis: auto;

    width: 50%;
  }

  @media (min-width: $mqmd) {
    width: 25%;
  }
}

// pět vedle sebe
.col--5 {
  @media (min-width: $mqxs) {
    flex-basis: auto;

    width: 50%;
  }

  @media (min-width: $mqmd) {
    width: 20%;
  }
}

// šest vedle sebe
.col--6 {
  @media (min-width: $mqxs) {
    flex-basis: auto;

    width: 50%;
  }

  @media (min-width: $mqmd) {
    width: 16.66666666666667%;
  }
}

// buňka se roztahuje
.col--grow {
  flex-grow: 1;
}

// zarovnání na pravou stranu
.col--end {
  align-items: flex-end;
}

// zarovnání na levou stranu
.col--start {
  align-items: flex-start;
}

// zarovnání na střed
.col--center {
  align-items: center;
}

// změna osy
.col--row {
  flex-direction: row;
}
