// box sizing
html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

img {
  box-sizing: content-box;
}

// nastavení velikosti písma
:root {
  font-size: 100%;
}

// základní definice
body {
  position: relative;

  margin: 0;
  padding-top: 70px;

  color: $color_black;
  font-family: '<PERSON>', sans-serif;
  font-size: 16px;
  line-height: 1;

  background-color: $color_gray_back;

  @media (min-width: $mq_menu) {
    padding-top: 151px;
  }

  @media (min-width: $mqlg) {
    padding-top: 194px;
  }
}

// základní nastavení respo
@-ms-viewport {
  width: device-width;
}

img,
svg {
  display: inline-block;

  max-width: 100%;
  height: auto;

  border: none;
}

iframe {
  max-width: 100%;
}
