// základní vzhled input polí
input,
textarea,
select {
  padding: 13px 20px;

  color: $color_black;
  font-size: 17px;
  line-height: 1;

  border: 2px solid $color_gray_dark;
  border-radius: $radius;
  background-color: $color_white;

  &:focus {
    border-color: $color_gray_light;
    outline: 0;
  }

  &.is-success {
    border-color: $color_success;
  }

  &.is-danger {
    border-color: $color_danger;
  }
}

// úpravy pro textarea
textarea {
  width: 100%;

  line-height: 1.4; // čitelnější rozestupy mezi řádky
}

// popisek pole
label {
  font-size: 15px;

  em {
    color: #9e9e9e;
    font-style: normal;
  }
}

// ošetření submit inputu, pokud je použit
button,
input[type='submit'] {
  width: auto;

  cursor: pointer;
}

// ošetření chekboxu a radio buttonu
input[type='checkbox'],
input[type='radio'] {
  width: auto;
}
