// základn<PERSON> nastavení typografie

// nadpisy
h1,
h2,
h3,
h4 {
  margin-top: 0;
  margin-bottom: 15px;

  font-weight: 700;
  line-height: 1.4;

  @media (min-width: $mqsm) {
    line-height: 1.5;
  }
}

h1 {
  font-size: 30px;

  @media (min-width: $mqmd) {
    font-size: 40px;
  }
}

h2 {
  font-size: 20px;

  @media (min-width: $mqmd) {
    font-size: 30px;
  }
}

h3 {
  font-size: 18px;

  @media (min-width: $mqmd) {
    font-size: 22px;
  }
}

h4 {
  @media (min-width: $mqmd) {
    font-size: 18px;
  }
}

// odkazy
a {
  color: $color_black;

  &:hover,
  &:focus {
    color: $color_gray;
  }

  &[href^='tel:'] {
    @media (min-width: $mqsm) {
      text-decoration: none;
    }
  }
}

// odstavce
p {
  margin: 0 0 30px 0;

  line-height: 1.4;

  @media (min-width: $mqsm) {
    line-height: 1.8;
  }
}

// seznamy
ul,
ol {
  margin: 0 0 30px 0;
  padding: 0 0 0 20px;

  line-height: 1.8;

  ul,
  ol {
    margin: 0;
    padding: 3px 3px 0 10px;

    li {
      &:last-child {
        padding-bottom: 0;
      }
    }
  }
}

// definiční seznam
dl {
  display: flex;
  flex-wrap: wrap;

  margin: 20px 0;

  line-height: 1.5;

  dt {
    flex: 0 0 20%;

    padding-right: 20px;

    font-weight: 600;
  }

  dd {
    flex: 1 1 80%;

    margin: 0;
    margin-bottom: 15px;
  }
}
