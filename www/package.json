{"name": "wt-devstack", "version": "3.0.0", "description": "Oficiální devstack Webterans.", "author": "<PERSON>", "main": "gulpfile.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "browserslist": ["last 5 versions", "ie >= 10", "ios >= 7", "android >= 4.4"], "dependencies": {"@ronilaukkarinen/gulp-stylelint": "^14.0.9", "autoprefixer": "^10.4.13", "browser-sync": "^2.27.11", "cheerio": "^1.0.0-rc.12", "cssnano": "^5.1.14", "gulp": "^4.0.2", "gulp-concat": "^2.6.1", "gulp-csscomb": "^3.1.0", "gulp-jshint": "^2.1.0", "gulp-minify": "^3.1.0", "gulp-plumber": "^1.2.1", "gulp-postcss": "^9.0.1", "gulp-prettier": "^4.0.0", "gulp-rename": "^2.0.0", "gulp-sass": "^5.1.0", "gulp-sourcemaps": "^3.0.0", "gulp-svgmin": "^4.1.0", "gulp-svgstore": "^9.0.0", "gulp-uglify": "^3.0.2", "jshint": "^2.13.6", "postcss-flexbugs-fixes": "^5.0.2", "postcss-pxtorem": "^6.0.0", "sass": "^1.57.1", "stylelint": "^14.16.1", "stylelint-config-standard-scss": "^6.1.0", "through2": "^4.0.2", "vinyl": "^3.0.0"}}