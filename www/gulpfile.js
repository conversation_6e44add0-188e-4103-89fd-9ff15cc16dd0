'use strict';

// nastavení
var settings = {
  browsersync: {
    url: 'http://goldfitness/',
    browser: 'chrome',
    watch: ['*.html', '*.htm', '*.php']
  },
  css: {
    source: 'css/styles.scss',
    target: 'css/',
    filename: 'styles.css',
    watch: ['css/**/*.scss', 'css/**/*.css', '!css/styles.css'],
    components: ['css/base/**/*.scss', '!css/base/base.scss', '!css/base/print.scss', '!css/base/variables.scss', '!css/base/fonts.scss', 'css/components/**/*.scss']
  },
  js: {
    source: ['js/libs/simple-lightbox.min.js', 'js/components/**/*.js', 'js/main.js'],
    target: 'js/',
    filename: 'scripts.js',
    watch: ['js/**/*.js', '!js/scripts.js'],
    components: ['js/components/**/*.js', 'js/main.js']
  },
  icons: {
    source: 'img/icons/**/*.svg',
    target: 'img/',
    filename: 'icons.svg',
    style: '../css/icons/icons.scss',
    prettycode: true
  }
};

// gulp
const gulp = require('gulp');
const {src, dest} = require('gulp');
  // spojení souborů
  const concat = require('gulp-concat');
  // Cheerio - manipulace v HTML/XML souborech
  const cheerio = require('cheerio')
  // plumber - odchycení chybových hlášek
  const plumber = require('gulp-plumber');
  // přejmenování souborů
  const rename = require("gulp-rename");
  // sourcemaps - generování map zdrojů
  const sourcemaps = require('gulp-sourcemaps');
  // through2 - Node wrapper
  const through2 = require('through2');
  // Vinyl - konvertor streamu
  const Vinyl = require('vinyl');
// BrowserSync - live realod, server, ovládání prohlížeče
const browserSync = require('browser-sync');
// SASS - generování CSS z preprocesoru
const sass = require('gulp-sass')(require('sass'));
// postCSS - postprocessing CSS (minifikace, autoprefixer...)
const postcss = require('gulp-postcss');
  const autoprefixer = require('autoprefixer');
  const cssnano = require('cssnano');
  const flexbugs = require('postcss-flexbugs-fixes');
  const pxtorem = require('postcss-pxtorem');
// CSScomb - uhlazení SASS souborů (řazení vlastností, odsazení...)
const csscomb = require('gulp-csscomb');
// lintování CSS
const stylelint = require('@ronilaukkarinen/gulp-stylelint');
// minifikace JavaScriptu
const uglify = require('gulp-uglify');
// lintování JavaScriptu
const jshint = require('gulp-jshint');
// Prettier - uhlazení JS souborů
const prettier = require('gulp-prettier');
// generování SVG spritů a ikon
const svgstore = require('gulp-svgstore');
// minimalizace SVG
const svgmin = require('gulp-svgmin');

// postCSS pluginy a nastavení
const postcssPlugins = [
  flexbugs(),
  pxtorem(),
  autoprefixer(),
  cssnano()
];

// výpis chybových hlášek
const onError = function (err) {
  console.log(err);
  this.emit('end');
};

// SASS kompilace
function wtSass() {
  return gulp.src(settings.css.source)
    .pipe(plumber({ errorHandler: onError }))
    .pipe(sourcemaps.init())
    .pipe(sass())
    .pipe(rename(settings.css.filename))
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest(settings.css.target))
    .pipe(browserSync.stream());
};

// CSS kompilace (produkce)
function wtCss() {
  return gulp.src(settings.css.source)
    .pipe(plumber({ errorHandler: onError }))
    .pipe(sourcemaps.init())
    .pipe(sass({ style: 'expanded' }))
    .pipe(postcss(postcssPlugins))
    .pipe(rename(settings.css.filename))
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest(settings.css.target));
};

// CSScomb - úpravy SASS souborů (řazení vlastností, odsazení...)
function wtCssComb() {
  return gulp.src(settings.css.components, { base: './' })
    .pipe(plumber({ errorHandler: onError }))
    .pipe(csscomb())
    .pipe(gulp.dest('./'));
};

// CSS - lintování (Stylelint)
function wtStyleLint() {
  return gulp.src(settings.css.components, { base: './' })
    .pipe(plumber({ errorHandler: onError }))
    .pipe(stylelint({
      reporters: [
        {
          formatter: 'string',
          console: true
        }
      ]
    }));
};

// JavaScript - spojení souborů
function wtConcatJs() {
  return gulp.src(settings.js.source, { base: './' })
    .pipe(plumber({ errorHandler: onError }))
    .pipe(sourcemaps.init())
    .pipe(concat(settings.js.target + settings.js.filename))
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest('./'));
};

// JavaScript - spojení a minifikace (produkce)
function wtJs() {
  return gulp.src(settings.js.target + settings.js.filename, { base: './' })
    .pipe(plumber({ errorHandler: onError }))
    .pipe(sourcemaps.init())
    .pipe(uglify())
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest('./'));
};

// JavaScript - lintování
function wtJsLint() {
  return gulp.src(settings.js.components)
    .pipe(plumber({ errorHandler: onError }))
    .pipe(jshint())
    .pipe(jshint.reporter('default'))
    .pipe(jshint.reporter('fail'));
};

// Prettier - uhlazení JS souborů
function wtPrettier() {
  return gulp.src(settings.js.components, { base: './' })
    .pipe(plumber({ errorHandler: onError }))
    .pipe(prettier({ singleQuote: true }))
    .pipe(gulp.dest('./'));
};

// generování SVG sprite ikon
function wtSvgIcons() {
  return gulp.src(settings.icons.source)
    .pipe(plumber({ errorHandler: onError }))
    .pipe(svgstore())
    .pipe(through2.obj(function (file, encoding, cb) {
      var $ = cheerio.load(file.contents.toString(), { xmlMode: true });

      // odstraní fill atributy u souborů, které nemají v názvu color
      $('symbol').not('[id*="color"]').find('*').removeAttr('fill');
      // odstraní style tagy
      $('[style]').removeAttr('style');

      // vytáhneme si název, výšku a šířku
      var data = $('svg > symbol').map(function() {
        var $this = $(this);
        var name = $this.attr('id');
        var viewBox = $this.attr('viewBox').split(' ');

        return {
          name: name,
          width: viewBox[2],
          height: viewBox[3],
        };
      }).get();

      // převedeme na SASS formát
      var dataToStyles = "";
      for (var i = 0; i < data.length; i++) {
        dataToStyles = dataToStyles + '\n.icon--' + data[i].name + ' {' + '\n';
          dataToStyles = dataToStyles + '  width: ' + data[i].width + 'px;\n\n';
          dataToStyles = dataToStyles + '  &:before {' + '\n';
          dataToStyles = dataToStyles + '    padding-top: calc(' + data[i].height + ' / ' + data[i].width + ') * 100%;' + '\n';
          dataToStyles = dataToStyles + '  }' + '\n';
        dataToStyles = dataToStyles + '}' + '\n';
      }

      // uložíme do soubou
      var fileSASS = new Vinyl({
        path: settings.icons.style,
        contents: new Buffer.from(dataToStyles)
      });

      file.contents = new Buffer.from($.xml());
      this.push(fileSASS);
      this.push(file);
      cb();

    }))
    .pipe(gulp.dest(settings.icons.target));
};

// optimalizace SVG sprite
function wtSvgOptimize() {
  return gulp.src(settings.icons.target + settings.icons.filename, { base: './' })
    .pipe(plumber({ errorHandler: onError }))
    .pipe(svgmin({
        plugins: [
          { removeUselessDefs: false },
          { removeXMLProcInst: false },
          { removeDoctype: false },
          { removeTitle: false },
          { cleanupIDs: false },
          { removeViewBox: false }
        ],
        js2svg: { pretty: settings.icons.prettycode }
    }))
    .pipe(gulp.dest('./'));
};

// sledování změn souborů
function wtWatch(cb) {

  // nastavení BrowserSync:
  browserSync.init({
    proxy: settings.browsersync.url,
    browser: settings.browsersync.browser
  });

  gulp.watch( settings.icons.source, wtSvgIcons ).on('change', browserSync.reload );
  gulp.watch( settings.css.watch, wtSass );
  gulp.watch( settings.js.watch, wtConcatJs ).on('change', browserSync.reload );
  gulp.watch( settings.browsersync.watch ).on('change', browserSync.reload );

  cb();
};

// aliasy tasků
  // úpravy před nahráním do produkce
  exports.deploy = gulp.parallel( gulp.series( wtCssComb, wtCss, wtStyleLint ), gulp.series( wtPrettier, wtConcatJs, wtJs, wtJsLint ), wtSvgOptimize );

  // generování CSS
  exports.makecss = gulp.series( wtCssComb, wtCss );

  // generování JavaScriptu
  exports.makejs = gulp.series( wtPrettier, wtConcatJs, wtJs );

  // generování ikon + optimalizace
  exports.icons = gulp.series( wtSvgIcons, wtSvgOptimize );

  // defaultni task
  exports.default = gulp.parallel( wtWatch );
