# mod_rewrite
<IfModule mod_rewrite.c>
	RewriteEngine On
	# RewriteBase /

  # presmeruje z goldfitness.sk  na goldfitness.cz a nastavím €
  RewriteCond %{HTTP_HOST} ^(www\.)?goldfitness.sk$
  RewriteRule ^(.*)$ https://www.goldfitness.cz/$1?d=sk [R=301,L]

  # presmeruje na adresu s www na zacatku
  RewriteCond %{HTTP_HOST} ^([^\.]+\.[^\.]+)$ [NC]
  RewriteRule ^(.*)$ https://www.%1/$1 [R=301,L]

  #kontakt presmerovani
  RewriteRule ^mailform.php /kontakt-t77 [NC,L,R=301]
  RewriteRule ^page.php?form=prodejna$ /kamenny-obchod-t62 [NC,L,R=301]

	# front controller
	RewriteCond %{REQUEST_FILENAME} !-f
	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteRule !\.(pdf|js|ico|gif|jpg|png|css|rar|zip|tar\.gz)$ index.php [L]
</IfModule>

<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript
    BrowserMatch ^Mozilla/4 gzip-only-text/html
    BrowserMatch ^Mozilla/4\.0[678] no-gzip
    BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
</IfModule>

# kesovaci hlavicky
<IfModule mod_expires.c>
<FilesMatch "\.(?i:gif|jpe?g|png|js|css|swf|ico|woff|woff2|webmanifest|svg|webp)$">
  ExpiresActive on
  ExpiresDefault "access plus 365 days"
</FilesMatch>
</IfModule>

# kesovani souboru
<IfModule mod_headers.c>
<FilesMatch "\.(?i:gif|jpe?g|png|js|css|swf|ico|woff|woff2|webmanifest|svg|webp)$">
  Header set Cache-Control "max-age=31536000, public"
</FilesMatch>
</IfModule>
