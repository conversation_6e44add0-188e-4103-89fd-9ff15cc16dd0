# nastaveni ftp serveru
variables:
  FTP_URL: "ftp.goldfitness.cz"
  FTP_USERNAME: "deploy.goldfitness.cz.32797"
  FTP_PASSWORD: "LpBoWqduJ3HxDUGX"
  # DEV server
  FTP_DEV_URL: "tch02.vas-server.cz"
  FTP_DEV_USERNAME: "goldfitnesscz.karelbednar.eu"
  FTP_DEV_PASSWORD: "psmsA*AUz8@BU#"

# definice akci
stages:
  - deploy_dev
  - deploy
  - clear-cache
  - clear-cache_dev

# natazeni docker image s git-ftp
image: danielstrelec/git-ftp

# deploy
deploy:
  # nazev akce
  stage: deploy
  # spustime pozadovane skripty
  script:
    - git --version
    - git ftp --version
    # pouze pro vstupni deploy
    #- git ftp init --user $FTP_USERNAME --passwd $FTP_PASSWORD $FTP_URL
    # deploy posledniho commitu
    - git ftp push --user $FTP_USERNAME --passwd $FTP_PASSWORD $FTP_URL
  # nastaveni spusteni - pouze na master vetvi
  only:
    - master
  # manualni spusteni, kdyz je zakomentovano spousti se automaticky
  #when: manual

# deploy - DEV
deploy_dev:
  # nazev akce
  stage: deploy_dev
  # spustime pozadovane skripty
  script:
    - git --version
    - git ftp --version
    # pouze pro vstupni deploy
    #- git ftp init --user $FTP_DEV_USERNAME --passwd $FTP_DEV_PASSWORD $FTP_DEV_URL
    # deploy posledniho commitu
    - git ftp push --user $FTP_DEV_USERNAME --passwd $FTP_DEV_PASSWORD $FTP_DEV_URL
  # nastaveni spusteni - pouze na master vetvi
  only:
    - redesign
  # manualni spusteni, kdyz je zakomentovano spousti se automaticky
  #when: manual

# mazani cache
clear-cache:
  # nazev akce
  stage: clear-cache
  # spustime pozadovane skripty
  script:
    curl -O https://www.goldfitness.cz/cc.php?k=lZwJIL
  only:
    - master
  # manualni spusteni, kdyz je zakomentovano spousti se automaticky
  #when: manual

  # mazani cache
clear-cache_dev:
  # nazev akce
  stage: clear-cache_dev
  # spustime pozadovane skripty
  script:
    curl -O https://test:<EMAIL>/www/cc.php?k=lZwJIL
  only:
    - redesign
  # manualni spusteni, kdyz je zakomentovano spousti se automaticky
  #when: manual
