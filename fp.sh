#!/bin/sh

# aktualni vetev se musi jmenovat master

# v config git-ftp musi byt nastaven user a url pro SCOPE master
##  git config git-ftp.goldfitness_onebit.user goldfitness.cz
##  git config git-ftp.goldfitness_onebit.url 178.238.37.205

# prvni init na ftp
##  git ftp init -s goldfitness_onebit -P

branch_name=$(git rev-parse --abbrev-ref HEAD)

if [ $branch_name = "master" ] ; then
echo "Spusti se push aktualni vetve: '$branch_name' ..."
echo "Server: ftp.goldfitness.cz - OneBit"
echo "login: goldfitness.cz"
else
echo "Aktualni vetev neni 'master' ($branch_name) ... "
exit  
fi

/C/Users/<USER>/git-ftp/git-ftp push -s goldfitness_onebit -P

# posunu tag www
git tag -d www
# git push origin :refs/tags/www
git tag www
# git push origin master --tags

exit 0