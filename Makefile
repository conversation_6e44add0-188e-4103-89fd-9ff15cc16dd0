# Makefile for GoldFitness project
# Basic Docker commands for development

.PHONY: help up down build restart logs shell db-shell clean status xdebug-toggle

# Default target
help:
	@echo "Available commands:"
	@echo "  make up              - Start Docker containers"
	@echo "  make down            - Stop Docker containers"
	@echo "  make build           - Build Docker images"
	@echo "  make restart         - Restart Docker containers"
	@echo "  make logs            - Show logs from containers"
	@echo "  make shell           - Open shell in web container"
	@echo "  make db-shell        - Open MySQL shell in database container"
	@echo "  make clean           - Remove all containers and volumes"
	@echo "  make status          - Show status of containers"
	@echo "  make xdebug-toggle   - Toggle Xdebug on/off"

# Start Docker containers
up:
	docker-compose up -d

# Stop Docker containers
down:
	docker-compose down

# Build Docker images
build:
	docker-compose build

# Restart Docker containers
restart:
	docker-compose restart

# Show logs from containers
logs:
	docker-compose logs -f

# Open shell in web container
shell:
	docker-compose exec web bash

# Open MySQL shell in database container
db-shell:
	docker-compose exec database mysql -u goldfitnesscz -pgoldfitnesscz goldfitnesscz

# Remove all containers and volumes
clean:
	docker-compose down -v --remove-orphans

# Show status of containers
status:
	docker-compose ps

# Toggle Xdebug on/off
xdebug-toggle:
	bash .docker/docker-xdebug.sh

# Import database dump
db-import:
	@echo "Importing database dump..."
	@if [ -f .docker/database/dump.sql ]; then \
		docker-compose exec -T database mysql -u goldfitnesscz -pgoldfitnesscz goldfitnesscz < .docker/database/dump.sql; \
		echo "Database imported successfully"; \
	else \
		echo "Error: dump.sql file not found in .docker/database/"; \
	fi

# Export database dump
db-export:
	@echo "Exporting database dump..."
	docker-compose exec database mysqldump -u goldfitnesscz -pgoldfitnesscz goldfitnesscz > .docker/database/dump.sql
	@echo "Database exported to .docker/database/dump.sql"

# Install composer dependencies
composer-install:
	docker-compose exec web php /var/www/html/composer.phar install --ignore-platform-reqs

# Update composer dependencies
composer-update:
	docker-compose exec web php /var/www/html/composer.phar update --ignore-platform-reqs

# Clear application cache
cache-clear:
	docker-compose exec web rm -rf temp/cache/*
	@echo "Cache cleared"
